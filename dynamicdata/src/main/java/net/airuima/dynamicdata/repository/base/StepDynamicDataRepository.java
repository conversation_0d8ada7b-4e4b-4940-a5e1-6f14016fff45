package net.airuima.dynamicdata.repository.base;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.dynamicdata.domain.base.StepDynamicData;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 动态数据定义表Repository
 *
 * <AUTHOR>
 * @date 2022-08-25
 */
@Repository
public interface StepDynamicDataRepository extends LogicDeleteableRepository<StepDynamicData>,
        EntityGraphJpaSpecificationExecutor<StepDynamicData>, EntityGraphJpaRepository<StepDynamicData, Long> {
    /**
     * 通过产品谱系主键id+工艺路线主键id+工序主键id查询动态数据
     * <AUTHOR>
     * @date 2022/11/14 15:54
     * @param pedigreeId 产品谱系主键id
     * @param workFlowId 工艺路线主键id
     * @param stepId 工序主键id
     * @param deleted 逻辑删除
     * @return net.airuima.rbase.domain.base.dynamic.StepDynamicData  动态数据
     */
    @DataFilter(isSkip = true)
    StepDynamicData findByPedigreeIdAndWorkFlowIdAndStepIdAndDeleted(Long pedigreeId, Long workFlowId, Long stepId, Long deleted);

    /**
     * 通过产品谱系主键id集合+工艺路线主键id+工序主键id查询动态数据列表
     * <AUTHOR>
     * @date 2022/11/15 10:27
     * @param pedigreeIdList 产品谱系主键id集合
     * @param workFlowId 工艺路线主键id
     * @param stepId 工序主键id
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.dynamic.StepDynamicData> 动态数据列表
     */
    @DataFilter(isSkip = true)
    @Query("select sdd from StepDynamicData as sdd where sdd.pedigree.id in (?1) and sdd.workFlow.id = ?2 and sdd.step.id = ?3 and sdd.isEnable=?4 and sdd.deleted=?5")
    List<StepDynamicData> findByPedigreeIdInAndWorkFlowIdAndStepIdAndDeleted(List<Long> pedigreeIdList, Long workFlowId, Long stepId,Boolean enable, Long deleted);

    /**
     * 通过动态数据编码+删除标识查询动态数据
     *
     * @param code    动态数据编码
     * @param deleted 删除标识
     * @return net.airuima.rbase.domain.base.dynamic.StepDynamicData  动态数据
     * <AUTHOR>
     * @date 2023/5/9
     **/
    @DataFilter(isSkip = true)
    StepDynamicData findByCodeAndDeleted(String code, Long deleted);


    /**
     * 根据产品谱系主键id查找动态数据
     * @param pedigreeId 产品谱系主键id
     * @param deleted 删除标识
     * @return java.util.List<net.airuima.rbase.domain.base.dynamic.StepDynamicData> 动态数据集合
     */
    @DataFilter(isSkip = true)
    @Query("select s from StepDynamicData s where s.pedigree.id = ?1 and s.deleted = ?2")
    List<StepDynamicData> findByPedigreeIdAndDeleted(Long pedigreeId, Long deleted);



}
