package net.airuima.dynamicdata.service.base;

import jakarta.persistence.criteria.Predicate;
import net.airuima.constant.Constants;
import net.airuima.dynamicdata.domain.base.DynamicDataColumn;
import net.airuima.dynamicdata.repository.base.DynamicDataColumnRepository;
import net.airuima.dynamicdata.web.rest.base.dto.DynamicDataColumnDTO;
import net.airuima.service.CommonJpaService;
import net.airuima.util.MapperUtils;
import net.airuima.util.ResponseException;
import net.airuima.util.ValidateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = Exception.class)
public class DynamicDataColumnService extends CommonJpaService<DynamicDataColumn> {

    private DynamicDataColumnRepository dynamicDataColumnRepository;

    public DynamicDataColumnService(DynamicDataColumnRepository dynamicDataColumnRepository) {
        this.dynamicDataColumnRepository = dynamicDataColumnRepository;
    }

    @Override
    public Page<DynamicDataColumn> find(Specification<DynamicDataColumn> spec, Pageable pageable) {
        return addDynamicDataColumnPage(dynamicDataColumnRepository.findAll(addSpecification(spec), pageable), pageable);
    }

    @Override
    public List<DynamicDataColumn> find(Specification<DynamicDataColumn> spec) {
        return addSubDynamicDataColumns(dynamicDataColumnRepository.findAll(addSpecification(spec)));
    }

    @Override
    public Page<DynamicDataColumn> findAll(Pageable pageable) {
        return addDynamicDataColumnPage(dynamicDataColumnRepository.findAll(addSpecification(null), pageable), pageable);
    }


    /**
     * 添加查询过滤描述条件
     *
     * @param spec 描述条件
     * @return Specification<DynamicDataColumn>
     */
    private Specification<DynamicDataColumn> addSpecification(Specification<DynamicDataColumn> spec) {
        if (Objects.nonNull(spec)) {
            Specification<DynamicDataColumn> parentIsNull = (root, query, build) -> {
                return build.isNull(root.get("parent"));
            };
            return spec.and(parentIsNull);
        } else {
            return (root, query, build) -> {
                List<Predicate> predicateList = new ArrayList<>();
                //逻辑删除
                Predicate deletedPredicate = build.equal(root.get("deleted"), Constants.LONG_ZERO);
                predicateList.add(deletedPredicate);
                //过滤有父类的元数据
                Predicate parentIsNotNull = build.isNull(root.get("parent"));
                predicateList.add(parentIsNotNull);
                return query.where(predicateList.toArray(new Predicate[0])).getRestriction();
            };
        }
    }

    /**
     * 添加当前动态元数据的 子项数据
     *
     * @param dynamicDataColumns 当前元数据列表
     * @return List<DynamicDataColumn>
     */
    public List<DynamicDataColumn> addSubDynamicDataColumns(List<DynamicDataColumn> dynamicDataColumns) {
        if (CollectionUtils.isEmpty(dynamicDataColumns)) {
            return Collections.emptyList();
        }
        List<DynamicDataColumn> subDynamicDataColumns = dynamicDataColumnRepository.findByParentIdInAndDeleted(dynamicDataColumns.stream().map(DynamicDataColumn::getId).collect(Collectors.toList()), Constants.LONG_ZERO);
        Map<DynamicDataColumn, List<DynamicDataColumn>> dataColumnListMap = subDynamicDataColumns.stream().collect(Collectors.groupingBy(DynamicDataColumn::getParent));
        if (dataColumnListMap.isEmpty()) {
            return dynamicDataColumns;
        }

        return dynamicDataColumns.stream().map(dynamicDataColumn -> {

            List<DynamicDataColumn> subDynamicDataColumnList = dataColumnListMap.get(dynamicDataColumn);
            if (ValidateUtils.isValid(subDynamicDataColumnList)) {
                dynamicDataColumn.setSubDynamicDataColumns(subDynamicDataColumnList.stream().map(DynamicDataColumn::getName).collect(Collectors.toList()));
            }
            return dynamicDataColumn;
        }).collect(Collectors.toList());
    }

    /**
     * 添加当前动态元数据的 子项数据
     *
     * @param dynamicDataColumnPage 当前元数据页
     * @param pageable              分页参数
     * @return Page<DynamicDataColumn>
     */
    private Page<DynamicDataColumn> addDynamicDataColumnPage(Page<DynamicDataColumn> dynamicDataColumnPage, Pageable pageable) {

        List<DynamicDataColumn> dynamicDataColumns = Optional.ofNullable(dynamicDataColumnPage).map(Slice::getContent).orElse(Collections.emptyList());
        if (CollectionUtils.isEmpty(dynamicDataColumns)) {
            return dynamicDataColumnPage;
        }
        List<DynamicDataColumn> newDynamicDataColumns = addSubDynamicDataColumns(dynamicDataColumns);
        int totalSize = newDynamicDataColumns.size();
        return PageableExecutionUtils.getPage(newDynamicDataColumns, pageable, () -> totalSize);
    }

    /**
     * 保存动态元数据的变化
     *
     * @param dynamicDataColumnDto 动态元数据信息
     * @return DynamicDataColumn
     */
    public DynamicDataColumn saveInstance(DynamicDataColumnDTO dynamicDataColumnDto) {
        if (Objects.isNull(dynamicDataColumnDto.getId())) {
            Optional<DynamicDataColumn> dynamicDataColumnOptional = dynamicDataColumnRepository.findByCodeAndDeleted(dynamicDataColumnDto.getCode(), Constants.LONG_ZERO);
            if (dynamicDataColumnOptional.isPresent()) {
                throw new ResponseException("error.codeExist", "动态元数据编码已存在");
            }
            //验证子动态元数据是否存在
            if (ValidateUtils.isValid(dynamicDataColumnDto.getSubDynamicDataColumnDtoList())) {
                validSubDynamicDataColumnData(dynamicDataColumnDto.getSubDynamicDataColumnDtoList());
            }

            DynamicDataColumn dynamicDataColumn = new DynamicDataColumn();
            BeanUtils.copyProperties(dynamicDataColumnDto, dynamicDataColumn);

            DynamicDataColumn finalDynamicDataColumn = dynamicDataColumnRepository.save(dynamicDataColumn);
            saveSubDynamicDataColumnData(dynamicDataColumnDto.getSubDynamicDataColumnDtoList(), finalDynamicDataColumn, Boolean.TRUE);
            return finalDynamicDataColumn;
        } else {
            DynamicDataColumn dynamicDataColumn = dynamicDataColumnRepository.findByIdAndDeleted(dynamicDataColumnDto.getId(), Constants.LONG_ZERO)
                    .orElseThrow(() -> new ResponseException("error.idNotExist", "动态元数据不存在"));

            //验证子动态元数据是否存在
            if (ValidateUtils.isValid(dynamicDataColumnDto.getSubDynamicDataColumnDtoList())) {
                validSubDynamicDataColumnData(dynamicDataColumnDto.getSubDynamicDataColumnDtoList());
            }
            dynamicDataColumn.setName(dynamicDataColumnDto.getName()).setGuidance(dynamicDataColumnDto.getGuidance())
                    .setCategory(dynamicDataColumn.getCategory()).setWidget(dynamicDataColumn.getWidget())
                    .setPrompt(dynamicDataColumnDto.getPrompt()).setWidgetData(dynamicDataColumnDto.getWidgetData())
                    .setColumnValidate(dynamicDataColumnDto.getColumnValidate()).setIsEnable(dynamicDataColumnDto.getIsEnable());
            DynamicDataColumn finalDynamicDataColumn = dynamicDataColumnRepository.save(dynamicDataColumn);
            saveSubDynamicDataColumnData(dynamicDataColumnDto.getSubDynamicDataColumnDtoList(), finalDynamicDataColumn, Boolean.FALSE);
            return finalDynamicDataColumn;
        }
    }

    /**
     * 验证子动态数据的合法性
     *
     * @param subDynamicDataColumns 子动态数据列表
     */
    public void validSubDynamicDataColumnData(List<DynamicDataColumnDTO.SubDynamicDataColumnDTO> subDynamicDataColumns) {
        List<String> codeList = subDynamicDataColumns.stream().map(DynamicDataColumnDTO.SubDynamicDataColumnDTO::getCode).distinct().toList();
        if (subDynamicDataColumns.size() != codeList.size()) {
            throw new ResponseException("CodeIsExist", "请求数据中编码存在重复");
        }
        for (DynamicDataColumnDTO.SubDynamicDataColumnDTO subDynamicDataColumnDTO : subDynamicDataColumns) {
            Optional<DynamicDataColumn> dynamicDataColumnOptional = ObjectUtils.isEmpty(subDynamicDataColumnDTO.getId()) ? dynamicDataColumnRepository.findByCodeAndDeleted(subDynamicDataColumnDTO.getCode(), Constants.LONG_ZERO) : dynamicDataColumnRepository.findByIdNotAndCodeAndDeleted(subDynamicDataColumnDTO.getId(), subDynamicDataColumnDTO.getCode(), Constants.LONG_ZERO);
            if (dynamicDataColumnOptional.isPresent()) {
                throw new ResponseException("CodeIsExist", "子项编码已存在");
            }
        }
    }

    /**
     * 保存动态元数据的 子数据（先删除，后新增）
     *
     * @param subDynamicDataColumns 子动态元数据
     * @param dynamicDataColumn     动态元数据
     * @param isLatest              是否为新增
     */
    public void saveSubDynamicDataColumnData(List<DynamicDataColumnDTO.SubDynamicDataColumnDTO> subDynamicDataColumns, DynamicDataColumn dynamicDataColumn, Boolean isLatest) {
        if (!isLatest) {
            dynamicDataColumnRepository.deleteByParentIdAndDeleted(dynamicDataColumn.getId(), Constants.LONG_ZERO);
        }
        if (!ValidateUtils.isValid(subDynamicDataColumns)) {
            return;
        }
        List<DynamicDataColumn> dynamicDataColumns = subDynamicDataColumns.stream().map(subDynamicDataColumnDTO -> {
            DynamicDataColumn subDynamicDataColumn = new DynamicDataColumn();
            BeanUtils.copyProperties(dynamicDataColumn, subDynamicDataColumn, "id");
            subDynamicDataColumn.setId(null);
            return subDynamicDataColumn.setParent(dynamicDataColumn).setCode(subDynamicDataColumnDTO.getCode()).setName(subDynamicDataColumnDTO.getName()).setFormOrder(subDynamicDataColumnDTO.getFormOrder());
        }).toList();
        dynamicDataColumnRepository.saveAll(dynamicDataColumns);
    }

    /**
     * 获取子动态元数据
     *
     * @param parentId 父级ID
     * @return : java.util.List<net.airuima.dto.dynamic.SubStepDynamicDataColumnGetDTO>
     * <AUTHOR>
     * @date 2023/1/28
     **/
    public DynamicDataColumnDTO findBySubDynamicDataColumn(Long parentId) {
        DynamicDataColumn dynamicDataColumn = dynamicDataColumnRepository.findByIdAndDeleted(parentId, Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("dynamicDataColumnIsNotExist", "动态元数据不存在"));
        DynamicDataColumnDTO dynamicDataColumnDto = MapperUtils.map(dynamicDataColumn, DynamicDataColumnDTO.class);

        List<DynamicDataColumn> stepDynamicDataColumnList = dynamicDataColumnRepository.findByParentIdAndDeleted(parentId, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(stepDynamicDataColumnList)) {
            dynamicDataColumnDto.setSubDynamicDataColumnDtoList(MapperUtils.mapAll(stepDynamicDataColumnList, DynamicDataColumnDTO.SubDynamicDataColumnDTO.class));
        }
        return dynamicDataColumnDto;
    }

    /**
     * 根据动态元数据编码或者名称获取启用的动态元数据列表
     *
     * @param text 动态元数据编码或者名称
     */
    public List<DynamicDataColumn> findByNameOrCode(String text, Integer size, Boolean isEnable) {
        return addSubDynamicDataColumns(Optional.ofNullable(dynamicDataColumnRepository.findByNameOrCode(StringUtils.isNotBlank(text) ? text : null, null != isEnable ? isEnable : Boolean.TRUE, PageRequest.of(Constants.INT_ZERO, size))).map(Slice::getContent).orElse(new ArrayList<>()));
    }

    /**
     * 动态元数据删除
     *
     * @param id 元数据
     */
    public void logicDelete(Long id) {
        dynamicDataColumnRepository.logicDelete(id);
        dynamicDataColumnRepository.deleteByParentIdAndDeleted(id, Constants.LONG_ZERO);
    }
}
