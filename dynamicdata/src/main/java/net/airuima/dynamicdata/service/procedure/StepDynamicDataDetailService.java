package net.airuima.dynamicdata.service.procedure;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.dynamicdata.domain.procedure.StepDynamicDataDetail;
import net.airuima.dynamicdata.repository.procedure.StepDynamicDataDetailRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工序动态生产数据明细表Service
 *
 * <AUTHOR>
 * @date 2022-08-25
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class StepDynamicDataDetailService extends CommonJpaService<StepDynamicDataDetail> {

    private final String STEP_DYNAMIC_DATA_DETAIL_ENTITY_GRAPH = "stepDynamicDataDetailEntityGraph";
    private final StepDynamicDataDetailRepository stepDynamicDataDetailRepository;

    public StepDynamicDataDetailService(StepDynamicDataDetailRepository stepDynamicDataDetailRepository) {
        this.stepDynamicDataDetailRepository = stepDynamicDataDetailRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<StepDynamicDataDetail> find(Specification<StepDynamicDataDetail> spec, Pageable pageable) {
        return stepDynamicDataDetailRepository.findAll(spec, pageable,new NamedEntityGraph(STEP_DYNAMIC_DATA_DETAIL_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public List<StepDynamicDataDetail> find(Specification<StepDynamicDataDetail> spec) {
        return stepDynamicDataDetailRepository.findAll(spec,new NamedEntityGraph(STEP_DYNAMIC_DATA_DETAIL_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<StepDynamicDataDetail> findAll(Pageable pageable) {
        return stepDynamicDataDetailRepository.findAll(pageable,new NamedEntityGraph(STEP_DYNAMIC_DATA_DETAIL_ENTITY_GRAPH));
    }

}
