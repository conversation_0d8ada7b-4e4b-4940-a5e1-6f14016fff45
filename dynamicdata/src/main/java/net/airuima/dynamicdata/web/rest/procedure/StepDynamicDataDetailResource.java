package net.airuima.dynamicdata.web.rest.procedure;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.dynamicdata.domain.procedure.StepDynamicDataDetail;
import net.airuima.dynamicdata.service.procedure.StepDynamicDataDetailService;
import net.airuima.web.BaseResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工序动态生产数据明细表Resource
 *
 * <AUTHOR>
 * @date 2022-08-25
 */
@Tag(name = "工序动态生产数据明细表Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/step-dynamic-data-details")
@AuthorityRegion("动态数据")
@FuncInterceptor("StepDynamicData")
public class StepDynamicDataDetailResource extends BaseResource<StepDynamicDataDetail> {

    private final StepDynamicDataDetailService stepDynamicDataDetailService;

    public StepDynamicDataDetailResource(StepDynamicDataDetailService stepDynamicDataDetailService) {
        this.stepDynamicDataDetailService = stepDynamicDataDetailService;
        this.mapUri = "/api/step-dynamic-data-details";
    }

}
