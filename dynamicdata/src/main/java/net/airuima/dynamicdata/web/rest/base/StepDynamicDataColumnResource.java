package net.airuima.dynamicdata.web.rest.base;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.dynamicdata.domain.base.StepDynamicDataColumn;
import net.airuima.dynamicdata.service.base.StepDynamicDataColumnService;
import net.airuima.dynamicdata.web.rest.base.dto.DynamicDataGetDTO;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.BaseResource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 动态元数据定义表Resource
 *
 * <AUTHOR>
 * @date 2022-08-25
 */
@Tag(name = "动态元数据与工序对应关系Resource")
@RestController
@RequestMapping("/api/step-dynamic-data-columns")
@AuthorityRegion("动态数据")
@FuncInterceptor("StepDynamicData")
public class StepDynamicDataColumnResource extends BaseResource<StepDynamicDataColumn> {
    private static final String EXCEPTION = "exception";
    private final Logger log = LoggerFactory.getLogger(StepDynamicDataColumnResource.class);

    private final StepDynamicDataColumnService stepDynamicDataColumnService;

    public StepDynamicDataColumnResource(StepDynamicDataColumnService stepDynamicDataColumnService) {
        this.stepDynamicDataColumnService = stepDynamicDataColumnService;
        this.mapUri = "/api/step-dynamic-data-columns";
    }


    /**
     * 获取动态元数据
     *
     * @param id 动态数据Id
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")

    @Operation(summary = "获取动态元数据", parameters = {
            @Parameter(name = "id", required = true, description = "动态数据Id", schema = @Schema(type = "int64"), in = ParameterIn.PATH)
    })
    @GetMapping("/dynamic-data/{id}")
    public ResponseEntity<ResponseData<DynamicDataGetDTO>> findByDynamicDataColumn(@PathVariable("id") Long id) {
        try {
            return ResponseData.ok(stepDynamicDataColumnService.findByDynamicDataColumn(id));
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 根据产品谱系ID,工艺路线Id、工序ID获取动态数据信息
     * @param pedigreeId 产品谱系ID
     * @param workFlowId 工艺路线ID
     * @param stepId 工序ID
     * @return  StepDynamicData
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "根据产品谱系ID,工艺路线Id、工序ID获取动态数据信息")
    @GetMapping("/byConditions")
    public ResponseEntity<ResponseData<DynamicDataGetDTO>> byCondition(@RequestParam(value = "pedigreeId",required = false) Long pedigreeId,
                                                                       @RequestParam(value = "workFlowId",required = false) Long workFlowId,
                                                                       @RequestParam(value = "stepId",required = false) Long stepId){
        return ResponseData.ok(stepDynamicDataColumnService.findAllByPedigreeIdAndWorkFlowIdAndStepId(pedigreeId,workFlowId, stepId));
    }


}
