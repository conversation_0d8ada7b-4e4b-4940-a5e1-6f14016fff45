package net.airuima.dynamicdata.web.rest.base.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dynamicdata.domain.base.DynamicDataColumn;
import net.airuima.dynamicdata.domain.base.StepDynamicData;

import java.io.Serializable;
import java.util.List;

@Schema(description = "动态数据详情信息")
public class DynamicDataGetDTO implements Serializable {

    @Schema(description = "动态数据")
    private StepDynamicData stepDynamicData;

    @Schema(description = "动态元数据列表")
    private List<DynamicDataColumn> dynamicDataColumnList;


    public StepDynamicData getStepDynamicData() {
        return stepDynamicData;
    }

    public DynamicDataGetDTO setStepDynamicData(StepDynamicData stepDynamicData) {
        this.stepDynamicData = stepDynamicData;
        return this;
    }

    public List<DynamicDataColumn> getDynamicDataColumnList() {
        return dynamicDataColumnList;
    }

    public DynamicDataGetDTO setDynamicDataColumnList(List<DynamicDataColumn> dynamicDataColumnList) {
        this.dynamicDataColumnList = dynamicDataColumnList;
        return this;
    }
}
