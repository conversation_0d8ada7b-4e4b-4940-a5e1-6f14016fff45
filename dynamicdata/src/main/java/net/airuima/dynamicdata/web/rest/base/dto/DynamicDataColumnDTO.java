package net.airuima.dynamicdata.web.rest.base.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

@Schema(description = "动态元数据保存与修改DTO")
public class DynamicDataColumnDTO implements Serializable {

    /**
     * 动态元数据Id，新增时不存在，修改时存在
     */
    @Schema(description = "动态元数据Id，新增时不存在，修改时存在")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 动态元数据名称
     */
    @Schema(description = "动态元数据名称")
    private String name;

    /**
     * 动态元数据编码
     */
    @Schema(description = "动态元数据编码")
    private String code;

    /**
     * 引导信息
     */
    @Schema(description = "引导信息")
    private String guidance;

    /**
     * 提示信息
     */
    @Schema(description = "提示信息")
    private String prompt;

    /**
     * 字段类型
     */
    @Schema(description = "字段类型")
    private String category;

    /**
     * 前端组件
     */
    @Schema(description = "前端组件")
    private String widget;

    /**
     * 前端组件数据
     */
    @Schema(description = "前端组件数据")
    private String widgetData;

    /**
     * 前端字段验证
     */
    @Schema(description = "前端字段验证")
    private String columnValidate;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    private Boolean isEnable;

    /**
     * 动态子元数据列表
     */
    @Schema(description = "动态子元数据列表")
    private List<SubDynamicDataColumnDTO> subDynamicDataColumnDtoList;



    public static class SubDynamicDataColumnDTO {

        /**
         * 动态子元数据Id，新增时不存在，修改时存在
         */
        @Schema(description = "动态子元数据Id，新增时不存在，修改时存在")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 动态子元数据名称
         */
        @Schema(description = "动态子元数据名称")
        private String name;

        /**
         * 动态子元数据编码
         */
        @Schema(description = "动态子元数据编码")
        private String code;

        /**
         * 表单展示顺序
         */
        @Schema(description = "表单展示顺序")
        private Integer formOrder;

        public Long getId() {
            return id;
        }

        public SubDynamicDataColumnDTO setId(Long id) {
            this.id = id;
            return this;
        }

        public String getName() {
            return name;
        }

        public SubDynamicDataColumnDTO setName(String name) {
            this.name = name;
            return this;
        }

        public String getCode() {
            return code;
        }

        public SubDynamicDataColumnDTO setCode(String code) {
            this.code = code;
            return this;
        }

        public Integer getFormOrder() {
            return formOrder;
        }

        public SubDynamicDataColumnDTO setFormOrder(Integer formOrder) {
            this.formOrder = formOrder;
            return this;
        }
    }

    public Long getId() {
        return id;
    }

    public DynamicDataColumnDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getName() {
        return name;
    }

    public DynamicDataColumnDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public DynamicDataColumnDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getGuidance() {
        return guidance;
    }

    public DynamicDataColumnDTO setGuidance(String guidance) {
        this.guidance = guidance;
        return this;
    }

    public String getPrompt() {
        return prompt;
    }

    public DynamicDataColumnDTO setPrompt(String prompt) {
        this.prompt = prompt;
        return this;
    }

    public String getCategory() {
        return category;
    }

    public DynamicDataColumnDTO setCategory(String category) {
        this.category = category;
        return this;
    }

    public String getWidget() {
        return widget;
    }

    public DynamicDataColumnDTO setWidget(String widget) {
        this.widget = widget;
        return this;
    }

    public String getWidgetData() {
        return widgetData;
    }

    public DynamicDataColumnDTO setWidgetData(String widgetData) {
        this.widgetData = widgetData;
        return this;
    }

    public String getColumnValidate() {
        return columnValidate;
    }

    public DynamicDataColumnDTO setColumnValidate(String columnValidate) {
        this.columnValidate = columnValidate;
        return this;
    }

    public Boolean getIsEnable() {
        return isEnable;
    }

    public DynamicDataColumnDTO setIsEnable(Boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    public List<SubDynamicDataColumnDTO> getSubDynamicDataColumnDtoList() {
        return subDynamicDataColumnDtoList;
    }

    public DynamicDataColumnDTO setSubDynamicDataColumnDtoList(List<SubDynamicDataColumnDTO> subDynamicDataColumnDtoList) {
        this.subDynamicDataColumnDtoList = subDynamicDataColumnDtoList;
        return this;
    }
}
