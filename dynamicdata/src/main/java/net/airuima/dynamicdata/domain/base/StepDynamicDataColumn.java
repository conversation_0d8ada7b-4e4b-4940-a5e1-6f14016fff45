package net.airuima.dynamicdata.domain.base;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 动态元数据定义表Domain
 *
 * <AUTHOR>
 * @date 2022-08-25
 */
@Schema(name = "动态元数据与工序对应关系(StepDynamicDataColumn)", description = "动态元数据与工序对应关系")
@Entity
@Table(name = "base_step_dynamic_data_column", uniqueConstraints = {
        @UniqueConstraint(name = "base_step_dynamic_data_column_unique_index", columnNames = {"step_dynamic_data_id", "dynamic_data_column_id","deleted"})
})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "stepDynamicDataColumnEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "stepDynamicData",subgraph = "stepDynamicDataEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "stepDynamicDataEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "pedigree")})})
public class StepDynamicDataColumn extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 动态数据ID
     */
    @NotNull
    @Schema(description = "动态数据ID", requiredMode = Schema.RequiredMode.REQUIRED,implementation = StepDynamicData.class)
    @ManyToOne
    @JoinColumn(name = "step_dynamic_data_id", nullable = false)
    private StepDynamicData stepDynamicData;

    /**
     * 动态数据ID
     */
    @Schema(description = "动态元数据ID", requiredMode = Schema.RequiredMode.REQUIRED,implementation = StepDynamicData.class)
    @ManyToOne
    @JoinColumn(name = "dynamic_data_column_id")
    private DynamicDataColumn dynamicDataColumn;

    /**
     * 表单展示顺序
     */
    @Schema(description = "表单展示顺序",requiredMode = Schema.RequiredMode.NOT_REQUIRED,type = "integer",format = "int32",maxLength = 11,defaultValue = "0")
    @Column(name = "form_order")
    private int formOrder;


    public StepDynamicData getStepDynamicData() {
        return stepDynamicData;
    }

    public StepDynamicDataColumn setStepDynamicData(StepDynamicData stepDynamicData) {
        this.stepDynamicData = stepDynamicData;
        return this;
    }

    public DynamicDataColumn getDynamicDataColumn() {
        return dynamicDataColumn;
    }

    public StepDynamicDataColumn setDynamicDataColumn(DynamicDataColumn dynamicDataColumn) {
        this.dynamicDataColumn = dynamicDataColumn;
        return this;
    }

    public int getFormOrder() {
        return formOrder;
    }

    public StepDynamicDataColumn setFormOrder(int formOrder) {
        this.formOrder = formOrder;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        StepDynamicDataColumn stepDynamicDataColumn = (StepDynamicDataColumn) o;
        if (stepDynamicDataColumn.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), stepDynamicDataColumn.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
