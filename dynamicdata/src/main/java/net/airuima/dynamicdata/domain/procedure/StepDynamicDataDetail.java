package net.airuima.dynamicdata.domain.procedure;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.dynamicdata.domain.base.StepDynamicData;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工序动态生产数据明细表Domain
 *
 * <AUTHOR>
 * @date 2022-08-25
 */
@Schema(name = "工序动态生产数据明细表(StepDynamicDataDetail)", description = "工序动态生产数据明细表")
@Entity
@Table(name = "procedure_step_dynamic_data_detail")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "stepDynamicDataDetailEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "stepDynamicData",subgraph = "stepDynamicDataEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "stepDynamicDataEntityGraph",
                        attributeNodes = {@NamedAttributeNode("pedigree")})})
public class StepDynamicDataDetail extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 动态数据定义ID
     */
    @NotNull
    @Schema(description = "动态数据定义ID", required = true)
    @ManyToOne
    @JoinColumn(name = "step_dynamic_data_id", nullable = false)
    private StepDynamicData stepDynamicData;

    /**
     * 业务数据ID
     */
    @Schema(description = "业务数据ID", required = true)
    @Column(name = "bussiness_id", nullable = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private long bussinessId;

    /**
     * 动态数据明细
     */
    @Schema(description = "动态数据明细")
    @Column(name = "data_info")
    private String dataInfo;


    public StepDynamicData getStepDynamicData() {
        return stepDynamicData;
    }

    public StepDynamicDataDetail setStepDynamicData(StepDynamicData stepDynamicData) {
        this.stepDynamicData = stepDynamicData;
        return this;
    }

    public long getBussinessId() {
        return bussinessId;
    }

    public void setBussinessId(long bussinessId) {
        this.bussinessId = bussinessId;
    }

    public String getDataInfo() {
        return dataInfo;
    }

    public StepDynamicDataDetail setDataInfo(String dataInfo) {
        this.dataInfo = dataInfo;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        StepDynamicDataDetail stepDynamicDataDetail = (StepDynamicDataDetail) o;
        if (stepDynamicDataDetail.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), stepDynamicDataDetail.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
