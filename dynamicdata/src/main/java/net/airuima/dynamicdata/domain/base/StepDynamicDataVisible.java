package net.airuima.dynamicdata.domain.base;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.process.Step;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 动态数据可见工序
 *
 * <AUTHOR>
 * @date 2023/11/14
 */
@Schema(description = "动态数据可见工序")
@Entity
@Table(name = "base_step_dynamic_data_visible")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "stepDynamicDataVisibleEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "stepDynamicData",subgraph = "stepDynamicDataEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "stepDynamicDataEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "pedigree")})})
public class StepDynamicDataVisible extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 动态数据
     */
    @Schema(description = "动态数据" ,requiredMode = Schema.RequiredMode.NOT_REQUIRED,implementation = StepDynamicData.class)
    @ManyToOne
    @JoinColumn(name = "dynamic_id")
    private StepDynamicData stepDynamicData;


    /**
     * 工序
     */
    @Schema(description = "工序" ,requiredMode = Schema.RequiredMode.NOT_REQUIRED,implementation = Step.class)
    @ManyToOne
    @JoinColumn(name = "step_id")
    private Step step;

    public StepDynamicData getStepDynamicData() {
        return stepDynamicData;
    }

    public StepDynamicDataVisible setStepDynamicData(StepDynamicData stepDynamicData) {
        this.stepDynamicData = stepDynamicData;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public StepDynamicDataVisible setStep(Step step) {
        this.step = step;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        StepDynamicDataVisible stepDynamicDataVisible = (StepDynamicDataVisible) o;
        if (stepDynamicDataVisible.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), stepDynamicDataVisible.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
