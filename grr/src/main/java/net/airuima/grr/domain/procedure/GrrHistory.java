package net.airuima.grr.domain.procedure;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 台位GRR测试历史表Domain
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Schema(name = "台位GRR测试历史表(GrrHistory)", description = "台位GRR测试历史表")
@Entity
@Table(name = "procedure_grr_history")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
public class GrrHistory extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 台位
     */
    @NotNull
    @Schema(description = "台位", required = true)
    @Column(name = "work_cell_code", nullable = false)
    private String workCellCode;

    /**
     * 测试时间
     */
    @Schema(description = "测试时间")
    @Column(name = "test_time")
    private LocalDateTime testTime;

    /**
     * 是否为最新记录
     */
    @Schema(description = "是否为最新记录")
    @Column(name = "is_latest")
    private boolean isLatest;

    public String getWorkCellCode() {
        return workCellCode;
    }

    public GrrHistory setWorkCellCode(String workCellCode) {
        this.workCellCode = workCellCode;
        return this;
    }

    public LocalDateTime getTestTime() {
        return testTime;
    }

    public GrrHistory setTestTime(LocalDateTime testTime) {
        this.testTime = testTime;
        return this;
    }

    public boolean getIsLatest() {
        return isLatest;
    }

    public GrrHistory setIsLatest(boolean latest) {
        isLatest = latest;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        GrrHistory grrHistory = (GrrHistory) o;
        if (grrHistory.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), grrHistory.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
