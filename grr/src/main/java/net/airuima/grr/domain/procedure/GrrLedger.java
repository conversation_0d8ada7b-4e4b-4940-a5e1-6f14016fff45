package net.airuima.grr.domain.procedure;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 台位GRR台账Domain
 * <AUTHOR>
 * @date 2022-05-23
 */
@Schema(name = "台位GRR台账(GrrLedger)", description = "台位GRR台账")
@Entity
@Table(name = "procedure_grr_ledger", uniqueConstraints = {
        @UniqueConstraint(name = "procedure_grr_ledger_unique_index", columnNames = {"work_cell_code", "deleted"})
})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
public class GrrLedger extends CustomBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 台位编码
     */
    @Schema(description = "台位编码")
    @Column(name = "work_cell_code")
    private String workCellCode;

    /**
     * 测试时间
     */
    @Schema(description = "测试时间")
    @Column(name = "test_time")
    private LocalDateTime testTime;

    /**
     * 到期时间
     */
    @Schema(description = "到期时间")
    @Column(name = "expire_time")
    private LocalDateTime expireTime;

    /**
     * 距离到期时间天数
     */
    @Schema(description = "距离到期时间天数")
    @Transient
    private Long tipDay;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getWorkCellCode() {
        return workCellCode;
    }

    public GrrLedger setWorkCellCode(String workCellCode) {
        this.workCellCode = workCellCode;
        return this;
    }

    public LocalDateTime getTestTime() {
        return testTime;
    }

    public GrrLedger setTestTime(LocalDateTime testTime) {
        this.testTime = testTime;
        return this;
    }

    public LocalDateTime getExpireTime() {
        return expireTime;
    }

    public GrrLedger setExpireTime(LocalDateTime expireTime) {
        this.expireTime = expireTime;
        return this;
    }

    public Long getTipDay() {
        return this.expireTime != null ? LocalDate.now().until(this.expireTime, ChronoUnit.DAYS):null;
    }

    public GrrLedger setTipDay(Long tipDay) {
        this.tipDay = tipDay;
        return this;
    }
}
