package net.airuima.grr.web.rest.procedure.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import net.airuima.config.DoubleSerializer;
import net.airuima.config.annotation.DoubleSerialize;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/5/24
 */
@Schema(description = "GRR历史明细DTO")
public class GrrHistoryDetailGetDTO {
    @Schema(description = "GRR测试数据明细ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 测试人
     */
    @Schema(description = "测试人", required = true)
    private String tester;

    /**
     * SN
     */
    @Schema(description = "SN", required = true)
    @Column(name = "sn", nullable = false)
    private String sn;

    /**
     * 检测项目编码
     */
    @Schema(description = "检测项目编码")
    private String checkItemCode;

    /**
     * 子模块
     */
    @Schema(description = "子模块")
    private String subModule;

    /**
     * 测试次数
     */
    @Schema(description = "测试次数")
    private Integer times;

    /**
     * 测试值
     */
    @Schema(description = "测试值")
    @JsonSerialize(using = DoubleSerializer.class)
    @DoubleSerialize(scale = 3)
    private Double number;

    /**
     * GRR
     */
    @Schema(description = "GRR")
    @JsonSerialize(using = DoubleSerializer.class)
    @DoubleSerialize(scale = 3)
    private Double grr;

    /**
     * NDC
     */
    @Schema(description = "NDC")
    @JsonSerialize(using = DoubleSerializer.class)
    @DoubleSerialize(scale = 3)
    private Double ndc;

    public Long getId() {
        return id;
    }

    public GrrHistoryDetailGetDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getTester() {
        return tester;
    }

    public GrrHistoryDetailGetDTO setTester(String tester) {
        this.tester = tester;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public GrrHistoryDetailGetDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public String getCheckItemCode() {
        return checkItemCode;
    }

    public GrrHistoryDetailGetDTO setCheckItemCode(String checkItemCode) {
        this.checkItemCode = checkItemCode;
        return this;
    }

    public String getSubModule() {
        return subModule;
    }

    public GrrHistoryDetailGetDTO setSubModule(String subModule) {
        this.subModule = subModule;
        return this;
    }

    public Integer getTimes() {
        return times;
    }

    public GrrHistoryDetailGetDTO setTimes(Integer times) {
        this.times = times;
        return this;
    }

    public Double getNumber() {
        return number;
    }

    public GrrHistoryDetailGetDTO setNumber(Double number) {
        this.number = number;
        return this;
    }

    public Double getGrr() {
        return grr;
    }

    public GrrHistoryDetailGetDTO setGrr(Double grr) {
        this.grr = grr;
        return this;
    }

    public Double getNdc() {
        return ndc;
    }

    public GrrHistoryDetailGetDTO setNdc(Double ndc) {
        this.ndc = ndc;
        return this;
    }
}
