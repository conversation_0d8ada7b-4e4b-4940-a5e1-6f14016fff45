package net.airuima.grr.web.rest.procedure.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.config.DoubleSerializer;
import net.airuima.config.annotation.DoubleSerialize;
import net.airuima.grr.domain.procedure.GrrHistoryDetail;
import net.airuima.rbase.constant.Constants;

import java.util.List;
import java.util.Map;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/5/25
 */
public class GrrLedgerGetDTO {

    /**
     * 检测项目编码
     */
    @Schema(description = "检测项目编码")
    private String checkItemCode;

    /**
     * 子模块
     */
    @Schema(description = "子模块/通道")
    private String subModule;

    /**
     * GRR
     */
    @Schema(description = "GRR")
    @JsonSerialize(using = DoubleSerializer.class)
    @DoubleSerialize(scale = 3)
    private Double grr;

    /**
     * NDC
     */
    @Schema(description = "NDC")
    @JsonSerialize(using = DoubleSerializer.class)
    @DoubleSerialize(scale = 3)
    private Double ndc;

    public GrrLedgerGetDTO() {
    }

    public GrrLedgerGetDTO(Map.Entry<String, List<GrrHistoryDetail>> entry) {
        String splitKey = entry.getKey();
        List<GrrHistoryDetail> groupGrrHistoryDetailList = entry.getValue();
        this.setCheckItemCode(splitKey.split(Constants.STR_PRAGMA)[Constants.INT_ZERO]);
        this.setSubModule(splitKey.split(Constants.STR_PRAGMA)[Constants.INT_ONE]);
        this.setGrr(groupGrrHistoryDetailList.get(Constants.INT_ZERO).getGrr());
        this.setNdc(groupGrrHistoryDetailList.get(Constants.INT_ZERO).getNdc());
    }

    public String getCheckItemCode() {
        return checkItemCode;
    }

    public GrrLedgerGetDTO setCheckItemCode(String checkItemCode) {
        this.checkItemCode = checkItemCode;
        return this;
    }

    public String getSubModule() {
        return subModule;
    }

    public GrrLedgerGetDTO setSubModule(String subModule) {
        this.subModule = subModule;
        return this;
    }

    public Double getGrr() {
        return grr;
    }

    public GrrLedgerGetDTO setGrr(Double grr) {
        this.grr = grr;
        return this;
    }

    public Double getNdc() {
        return ndc;
    }

    public GrrLedgerGetDTO setNdc(Double ndc) {
        this.ndc = ndc;
        return this;
    }
}
