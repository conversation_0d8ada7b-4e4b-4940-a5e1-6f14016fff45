package net.airuima.grr.web.rest.procedure;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.grr.domain.procedure.GrrHistory;
import net.airuima.grr.domain.procedure.GrrHistoryDetail;
import net.airuima.grr.service.procedure.GrrHistoryDetailService;
import net.airuima.grr.service.procedure.GrrHistoryService;
import net.airuima.grr.web.rest.procedure.dto.GrrAnalyseQueryParameterGetDTO;
import net.airuima.grr.web.rest.procedure.dto.GrrAnalyseReportGetDTO;
import net.airuima.grr.web.rest.procedure.dto.GrrHistorySaveDTO;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.client.ClientGrrHistorySaveDTO;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 台位GRR测试历史表Resource
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Tag(name = "台位GRR测试历史Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/grr-histories")
@AuthorityRegion("GRR分析")
@FuncInterceptor("GRR")
public class GrrHistoryResource extends ProtectBaseResource<GrrHistory> {

    private final GrrHistoryService grrHistoryService;
    private final GrrHistoryDetailService grrHistoryDetailService;

    public GrrHistoryResource(GrrHistoryService grrHistoryService, GrrHistoryDetailService grrHistoryDetailService) {
        this.grrHistoryService = grrHistoryService;
        this.grrHistoryDetailService = grrHistoryDetailService;
        this.mapUri = "/api/grr-histories";
    }

    /**
     * 通过ID删除台位校准历史及对应明细数据
     *
     * @param id 检测历史ID
     * @return
     */
    @Operation(summary = "通过GRR历史ID删除台位校准历史及对应明细数据")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Override
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        try {
            BaseDTO baseDto = grrHistoryService.deleteHistoryById(id);
            if (null != baseDto) {
                return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "deleteFailed", baseDto.getMessage())).build();
            }
            return ResponseEntity.ok().headers(HeaderUtil.deletedAlert(this.entityName, id.toString())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "deleteFailed", e.toString())).build();
        }
    }


    /**
     * 通过台位编码获取不重复的检测项编码和子模块列表
     *
     * @param workCellCode 台位编码
     * @return List<GrrAnalyseQueryParameterGetDTO>
     */
    @Operation(summary = "通过台位编码获取不重复的检测项编码和子模块列表")
    @GetMapping("/grr-analyse-query-parameter/{workCellCode}")
    public ResponseEntity<ResponseData<List<GrrAnalyseQueryParameterGetDTO>>> findGrrAnalyseQuery(@PathVariable("workCellCode") String workCellCode) {
        return ResponseData.ok(grrHistoryService.findGrrAnalyseQueryParameter(workCellCode));
    }

    /**
     * 通过台位编码、检测项目编码、子模块及测试时间获取GRR分析报告
     *
     * @param workCellCode  台位编码
     * @param checkItemCode 检测项目编码
     * @param subModule     子模块
     * @param testTime      测试时间
     * @return GrrAnalyseReportGetDTO
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "获取需要打印的GRR分析报告数据")
    @Parameters({
            @Parameter(name = "workCellCode", description = "台位编码", required = true),
            @Parameter(name = "checkItemCode", description = "检测项目编码", required = true),
            @Parameter(name = "subModule", description = "子模块", required = true),
            @Parameter(name = "testTime", description = "测试时间", required = false)
    })
    @GetMapping("/grr-analyse-report")
    public ResponseEntity<ResponseData<GrrAnalyseReportGetDTO>> findGrrAnalyseReport(@RequestParam(value = "workCellCode", required = true) String workCellCode,
                                                                                     @RequestParam(value = "checkItemCode", required = true) String checkItemCode,
                                                                                     @RequestParam(value = "subModule", required = true) String subModule,
                                                                                     @RequestParam(value = "testTime", required = false) LocalDateTime testTime) {
        return ResponseData.ok(grrHistoryService.findGrrAnalyseReport(workCellCode, checkItemCode, subModule, testTime));
    }

    /**
     * 台位GRR测试数据导入
     *
     * @param file 导入的Excel文件
     * @return
     * @throws IOException
     * @throws Exception
     */
    @Operation(summary = "台位GRR测试数据导入")
    @Parameter(name = "fileName", description = "导入的Excel文件", required = true)
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_IMPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Override
    public ResponseEntity<Void> importExcel(@RequestParam("fileName") MultipartFile file) throws IOException, Exception {
        try {
            if (file.isEmpty()) {
                return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(entityName, "FileEmpty", "File invalid.")).build();
            }
            BaseClientDTO baseClientDto;
            ImportParams importParams = new ImportParams();
            importParams.setHeadRows(Constants.INT_ONE);
            List<GrrHistorySaveDTO> grrHistorySaveDtoList = ExcelImportUtil.importExcel(file.getInputStream(), GrrHistorySaveDTO.class, importParams);
            baseClientDto = grrHistoryService.saveGrrHistory(grrHistorySaveDtoList);
            if (baseClientDto.getStatus().equals(Constants.KO)) {
                return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(entityName, "importFailed", baseClientDto.getMessage())).build();
            }
            return ResponseEntity.ok()
                    .headers(HeaderUtil.createdAlert(entityName + ".importSuccess", entityName)).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "importFailed", e.toString())).build();
        }
    }

    /**
     * 通过GRR历史ID导出历史明细数据
     *
     * @param id       历史ID
     * @param response
     * @throws Exception
     */
    @Operation(summary = "通过GRR历史ID导出历史明细数据")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_EXPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping({"/exportExcel/{id}"})
    public ResponseEntity<ResponseData<Void>> export(@PathVariable("id") Long id, HttpServletResponse response) throws Exception {
        List<GrrHistoryDetail> grrHistoryDetailList = grrHistoryDetailService.findByGrrHistoryId(id);
        if (!ValidateUtils.isValid(grrHistoryDetailList)) {
            return ResponseData.error("error.isNotExist", "历史明细数据不存在");
        }
        List<GrrHistorySaveDTO> clientGrrHistorySaveDTOList = grrHistoryDetailList.stream().map(GrrHistorySaveDTO::new).collect(Collectors.toList());
        ExportParams exportParams = new ExportParams();
        exportParams.setType(ExcelType.XSSF);
        exportParams.setFreezeCol(Constants.INT_TWO);
        String fileName = this.getEntityName() + "_" + UUID.randomUUID() + ".xlsx";
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, GrrHistorySaveDTO.class, clientGrrHistorySaveDTOList);
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);
        workbook.write(response.getOutputStream());
        return ResponseData.ok();
    }

    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览GRR测试数据报表";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建GRR测试数据报表";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改GRR测试数据报表";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除GRR测试数据报表";
        }else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_IMPORT)) {
            return "导入GRR测试数据报表";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出GRR测试数据报表";
        }
        return "";
    }

}
