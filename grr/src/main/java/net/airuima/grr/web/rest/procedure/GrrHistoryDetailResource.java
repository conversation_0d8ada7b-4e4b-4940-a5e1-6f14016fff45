package net.airuima.grr.web.rest.procedure;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.grr.domain.procedure.GrrHistoryDetail;
import net.airuima.grr.service.procedure.GrrHistoryDetailService;
import net.airuima.grr.web.rest.procedure.dto.GrrHistoryDetailGetDTO;
import net.airuima.grr.web.rest.procedure.dto.GrrHistoryDetailSaveDTO;
import net.airuima.util.ResponseData;
import net.airuima.web.BaseResource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 台位GRR测试历史明细Resource
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Tag(name = "台位GRR测试历史明细Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/grr-history-details")
@AuthorityRegion("GRR分析")
@FuncInterceptor("GRR")
public class GrrHistoryDetailResource extends BaseResource<GrrHistoryDetail> {

    private final GrrHistoryDetailService grrHistoryDetailService;

    public GrrHistoryDetailResource(GrrHistoryDetailService grrHistoryDetailService) {
        this.grrHistoryDetailService = grrHistoryDetailService;
        this.mapUri = "/api/grr-history-details";
    }

    /**
     * 通过台位GRR测试历史ID获取检测数据明细
     *
     * @param historyId 台位GRR测试历史ID
     * @return GrrHistoryDetailGetDTO
     */
    @PreAuthorize("hasAnyAuthority('GRRHISTORY_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "通过台位GRR测试历史ID获取检测数据明细")
    @GetMapping("/edit/{historyId}")
    public ResponseEntity<ResponseData<List<GrrHistoryDetailGetDTO>>> getDetailByResultId(@PathVariable("historyId") Long historyId) {
        return ResponseData.ok(grrHistoryDetailService.getCheckResultDetailByResultId(historyId));
    }

    /**
     * 批量保存手动修改的GRR历史检测项明细数据
     *
     * @param grrHistoryDetailSaveDtoList 批量待保存的GRR历史明细数据参数DTO
     */
    @PreAuthorize("hasAnyAuthority('GRRHISTORY_UPDATE') or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "批量保存手动修改的GRR历史检测项明细数据")
    @PutMapping("/batch")
    public ResponseEntity<ResponseData<Void>> batchUpdateHistoryDetail(@RequestBody List<GrrHistoryDetailSaveDTO> grrHistoryDetailSaveDtoList) {
        try {
            grrHistoryDetailService.batchUpdateHistoryDetail(grrHistoryDetailSaveDtoList);
            return ResponseData.save();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

}
