package net.airuima.grr.web.rest.procedure.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/5/27
 */
@Schema(description = "GRR分析报告返回测试项目及子模块查询条件DTO")
public class GrrAnalyseQueryParameterGetDTO {
    /**
     * 检测项目编码列表
     */
    @Schema(description = "检测项目编码")
    private String checkItemCode;

    /**
     * 标准件子模块列表
     */
    @Schema(description = "标准件子模块列表")
    private List<String> subModuleList;


    public String getCheckItemCode() {
        return checkItemCode;
    }

    public GrrAnalyseQueryParameterGetDTO setCheckItemCode(String checkItemCode) {
        this.checkItemCode = checkItemCode;
        return this;
    }

    public List<String> getSubModuleList() {
        return subModuleList;
    }

    public GrrAnalyseQueryParameterGetDTO setSubModuleList(List<String> subModuleList) {
        this.subModuleList = subModuleList;
        return this;
    }
}
