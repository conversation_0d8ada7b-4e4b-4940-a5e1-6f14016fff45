package net.airuima.grr.service.procedure;

import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.grr.domain.base.GrrCycleConfig;
import net.airuima.grr.domain.procedure.GrrHistory;
import net.airuima.grr.domain.procedure.GrrHistoryDetail;
import net.airuima.grr.domain.procedure.GrrLedger;
import net.airuima.grr.repository.base.GrrCycleConfigRepository;
import net.airuima.grr.repository.procedure.GrrHistoryDetailRepository;
import net.airuima.grr.repository.procedure.GrrHistoryRepository;
import net.airuima.grr.repository.procedure.GrrLedgerRepository;
import net.airuima.grr.web.rest.procedure.dto.GrrAnalyseQueryParameterGetDTO;
import net.airuima.grr.web.rest.procedure.dto.GrrAnalyseReportGetDTO;
import net.airuima.grr.web.rest.procedure.dto.GrrHistorySaveDTO;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.client.ClientGrrHistorySaveDTO;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.util.MapperUtils;
import net.airuima.rbase.util.ToolUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.DateTimeUtil;
import org.apache.commons.compress.utils.Lists;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 台位GRR测试历史表Service
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class GrrHistoryService extends CommonJpaService<GrrHistory> {

    private final GrrHistoryRepository grrHistoryRepository;
    private final GrrHistoryDetailRepository grrHistoryDetailRepository;
    private final GrrCycleConfigRepository grrCycleConfigRepository;
    private final WorkCellRepository workCellRepository;
    private final GrrLedgerRepository grrLedgerRepository;

    public GrrHistoryService(GrrHistoryRepository grrHistoryRepository, GrrHistoryDetailRepository grrHistoryDetailRepository,
                             GrrCycleConfigRepository grrCycleConfigRepository, WorkCellRepository workCellRepository,
                             GrrLedgerRepository grrLedgerRepository) {
        this.grrHistoryRepository = grrHistoryRepository;
        this.grrHistoryDetailRepository = grrHistoryDetailRepository;
        this.grrCycleConfigRepository = grrCycleConfigRepository;
        this.workCellRepository = workCellRepository;
        this.grrLedgerRepository = grrLedgerRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<GrrHistory> find(Specification<GrrHistory> spec, Pageable pageable) {
        return grrHistoryRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<GrrHistory> find(Specification<GrrHistory> spec) {
        return grrHistoryRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<GrrHistory> findAll(Pageable pageable) {
        return grrHistoryRepository.findAll(pageable);
    }

    /**
     * 保存客户端上传或者直接导入的台位GRR测试数据
     *
     * @param clientGrrHistorySaveDTOList GRR测试数据参数列表DTO
     * @return BaseClientDTO
     */
    public BaseClientDTO saveGrrHistory(List<GrrHistorySaveDTO> clientGrrHistorySaveDTOList) {
        //验证上传的标准件测试数据中是否存在不同的台位编码
        long distinctWorkCellCodeSize = clientGrrHistorySaveDTOList.stream().map(GrrHistorySaveDTO::getWorkCellCode).distinct().count();
        if (distinctWorkCellCodeSize > Constants.INT_ONE) {
            return new BaseClientDTO(Constants.KO, "请保持待保存的数据中台位编码一致");
        }
        //验证上传的标准件测试数据中是否存在不同的测试日期
        long distinctTestTimeSize = clientGrrHistorySaveDTOList.stream().map(GrrHistorySaveDTO::getTestTime).distinct().count();
        if (distinctTestTimeSize > Constants.INT_ONE) {
            return new BaseClientDTO(Constants.KO, "请保持待保存的数据中测试日期一致");
        }
        GrrHistorySaveDTO firstClientGrrHistorySaveDto = clientGrrHistorySaveDTOList.get(Constants.INT_ZERO);
        //验证台位在工位中是否存在
        Optional<WorkCell> workCellOptional = workCellRepository.findByCodeAndDeleted(firstClientGrrHistorySaveDto.getWorkCellCode(), Constants.LONG_ZERO);
        if (!workCellOptional.isPresent()) {
            return new BaseClientDTO(Constants.KO, "台位号在工位中不存在");
        }
        GrrHistory oldGrrHistory = grrHistoryRepository.findByWorkCellCodeAndTestTimeAndDeleted(firstClientGrrHistorySaveDto.getWorkCellCode(),firstClientGrrHistorySaveDto.getTestTime(),Constants.LONG_ZERO);
        if(null != oldGrrHistory){
            grrHistoryDetailRepository.batchDeleteByHistoryId(oldGrrHistory.getId());
            oldGrrHistory.setDeleted(oldGrrHistory.getId());
            grrHistoryRepository.save(oldGrrHistory);
        }
        GrrHistory latestGrrHistory = grrHistoryRepository.findTop1ByWorkCellCodeAndIsLatestAndDeleted(firstClientGrrHistorySaveDto.getWorkCellCode(), Boolean.TRUE, Constants.LONG_ZERO);
        LocalDateTime testDate = firstClientGrrHistorySaveDto.getTestTime();
        GrrHistory grrHistory = new GrrHistory();
        grrHistory.setWorkCellCode(firstClientGrrHistorySaveDto.getWorkCellCode()).setTestTime(testDate).setIsLatest(Boolean.FALSE).setDeleted(Constants.LONG_ZERO);
        boolean isLatest = latestGrrHistory == null || DateTimeUtil.isBeforeOrEqualLocalDateTime(latestGrrHistory.getTestTime(), testDate);
        if (isLatest) {
            grrHistory.setIsLatest(Boolean.TRUE);
            //将历史记录更新为非最新记录
            grrHistoryRepository.batchUpdateGrrHistoryIsLatest(firstClientGrrHistorySaveDto.getWorkCellCode(), Boolean.FALSE);
        }
        grrHistoryRepository.save(grrHistory);
        clientGrrHistorySaveDTOList.forEach(grrHistoryDetailInfo -> {
            GrrHistoryDetail grrHistoryDetail = MapperUtils.map(grrHistoryDetailInfo, GrrHistoryDetail.class);
            grrHistoryDetail.setGrrHistory(grrHistory).setDeleted(Constants.LONG_ZERO);
            grrHistoryDetailRepository.save(grrHistoryDetail);
        });
        //更新台位GRR周期配置表的到期日期及更新GRR的台账数据
        if (isLatest) {
            //更新台位GRR周期配置表的到期日期
            Optional<GrrCycleConfig> grrCycleConfigOptional = grrCycleConfigRepository.findByWorkCellCodeAndDeleted(firstClientGrrHistorySaveDto.getWorkCellCode(), Constants.LONG_ZERO);
            grrCycleConfigOptional.ifPresent(grrCycleConfig -> {
                grrCycleConfig.setExpireTime(grrHistory.getTestTime().plusDays(ToolUtils.getCycleDays(grrCycleConfig.getUnit(), grrCycleConfig.getPeriod())));
                grrCycleConfigRepository.save(grrCycleConfig);
            });
            //更新GRR的台账数据
            GrrLedger grrLedger = grrLedgerRepository.findByWorkCellCodeAndDeleted(firstClientGrrHistorySaveDto.getWorkCellCode(), Constants.LONG_ZERO).orElse(new GrrLedger());
            grrLedger.setWorkCellCode(firstClientGrrHistorySaveDto.getWorkCellCode())
                    .setTestTime(grrHistory.getTestTime())
                    .setExpireTime(grrCycleConfigOptional.map(grrCycleConfig -> grrHistory.getTestTime().plusDays(ToolUtils.getCycleDays(grrCycleConfig.getUnit(), grrCycleConfig.getPeriod())))
                            .orElseGet(() -> grrHistory.getTestTime().plusDays(365))).setDeleted(Constants.LONG_ZERO);
            grrLedgerRepository.save(grrLedger);
        }
        return new BaseClientDTO(Constants.OK);
    }

    /**
     * 通过GRR历史ID删除历史及明细数据
     *
     * @param historyId GRR历史ID
     * @return BaseDTO
     */
    public BaseDTO deleteHistoryById(Long historyId) {
        GrrHistory grrHistory = grrHistoryRepository.getReferenceById(historyId);
        grrHistory.setDeleted(historyId);
        grrHistoryDetailRepository.batchDeleteByHistoryId(historyId);
        Optional<GrrCycleConfig> grrCycleConfigOptional = grrCycleConfigRepository.findByWorkCellCodeAndDeleted(grrHistory.getWorkCellCode(), Constants.LONG_ZERO);
        Optional<GrrLedger> grrLatestStatusOptional = grrLedgerRepository.findByWorkCellCodeAndDeleted(grrHistory.getWorkCellCode(), Constants.LONG_ZERO);
        //根据台位号获取最新历史记录并将其改为最新记录，同时更新台位配置记录的到期时间以及台位GRR台账的到期时间
        GrrHistory latestHistory = grrHistoryRepository.findTop1ByWorkCellCodeAndDeletedOrderByTestTimeDescIdDesc(grrHistory.getWorkCellCode(), Constants.LONG_ZERO);
        if (null != latestHistory) {
            latestHistory.setIsLatest(Boolean.TRUE);
            grrCycleConfigOptional.ifPresent(grrCycleConfig -> {
                grrCycleConfig.setExpireTime(latestHistory.getTestTime().plusDays(ToolUtils.getCycleDays(grrCycleConfig.getUnit(), grrCycleConfig.getPeriod()))).setDeleted(Constants.LONG_ZERO);
                grrCycleConfigRepository.save(grrCycleConfig);
            });
            grrLatestStatusOptional.ifPresent(grrLatestStatus -> {
                grrLatestStatus.setWorkCellCode(latestHistory.getWorkCellCode())
                        .setTestTime(latestHistory.getTestTime())
                        .setExpireTime(grrCycleConfigOptional.map(grrCycleConfig -> grrHistory.getTestTime().plusDays(ToolUtils.getCycleDays(grrCycleConfig.getUnit(), grrCycleConfig.getPeriod())))
                                .orElseGet(() -> grrHistory.getTestTime().plusDays(365))).setDeleted(Constants.LONG_ZERO);
                grrLedgerRepository.save(grrLatestStatus);
            });
            grrHistoryRepository.save(latestHistory);
        } else {
            grrCycleConfigOptional.ifPresent(grrCycleConfig -> {
                grrCycleConfig.setExpireTime(LocalDateTime.now());
                grrCycleConfigRepository.save(grrCycleConfig);
            });
            grrLatestStatusOptional.ifPresent(grrLatestStatus -> {
                grrLatestStatus.setTestTime(null).setExpireTime(LocalDateTime.now());
                grrLedgerRepository.save(grrLatestStatus);
            });
        }
        return null;
    }

    /**
     * 通过台位编码获取不重复的检测项编码和子模块列表
     *
     * @param workCellCode 台位编码
     * @return List<GrrAnalyseQueryParameterGetDTO>
     */
    public List<GrrAnalyseQueryParameterGetDTO> findGrrAnalyseQueryParameter(String workCellCode) {
        List<GrrAnalyseQueryParameterGetDTO> grrAnalyseQueryParameterGetDtoList = Lists.newArrayList();
        List<GrrHistoryDetail> grrHistoryDetailList = grrHistoryDetailRepository.findDistinctCheckItemAndSubModuleByWorkCellCode(workCellCode, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(grrHistoryDetailList)) {
            return grrAnalyseQueryParameterGetDtoList;
        }
        grrHistoryDetailList.stream().collect(Collectors.groupingBy(GrrHistoryDetail::getCheckItemCode)).forEach((checkItemCode, groupDetailList) -> {
            GrrAnalyseQueryParameterGetDTO grrAnalyseQueryParameterGetDto = new GrrAnalyseQueryParameterGetDTO();
            grrAnalyseQueryParameterGetDtoList.add(grrAnalyseQueryParameterGetDto.setCheckItemCode(checkItemCode).setSubModuleList(groupDetailList.stream().map(GrrHistoryDetail::getSubModule).collect(Collectors.toList())));
        });
        return grrAnalyseQueryParameterGetDtoList;
    }

    /**
     * 通过台位编码、检测项目编码、子模块及测试时间获取GRR分析报告
     *
     * @param workCellCode  台位编码
     * @param checkItemCode 检测项目编码
     * @param subModule     子模块
     * @param testTime      测试时间
     * @return GrrAnalyseReportGetDTO
     */
    @Transactional(readOnly = true)
    public GrrAnalyseReportGetDTO findGrrAnalyseReport(String workCellCode, String checkItemCode, String subModule, LocalDateTime testTime) {
        GrrHistory grrHistory = null != testTime ? grrHistoryRepository.findByWorkCellCodeAndTestTimeAndDeleted(workCellCode, testTime, Constants.LONG_ZERO) : grrHistoryRepository.findTop1ByWorkCellCodeAndIsLatestAndDeleted(workCellCode, Boolean.TRUE, Constants.LONG_ZERO);
        if (null == grrHistory) {
            return null;
        }
        List<GrrHistoryDetail> grrHistoryDetailList = grrHistoryDetailRepository.findByGrrHistoryIdAndCheckItemCodeAndSubModuleAndDeleted(grrHistory.getId(), checkItemCode, subModule, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(grrHistoryDetailList)) {
            return null;
        }
        GrrAnalyseReportGetDTO grrAnalyseReportGetDto = new GrrAnalyseReportGetDTO();
        grrAnalyseReportGetDto.setGrr(grrHistoryDetailList.get(Constants.INT_ZERO).getGrr())
                .setNdc(grrHistoryDetailList.get(Constants.INT_ZERO).getNdc())
                .setCheckItemCode(checkItemCode).setTestTime(grrHistory.getTestTime()).setWorkCellCode(workCellCode);
        List<GrrAnalyseReportGetDTO.GrrHistoryDetailInfo> grrHistoryDetailInfoList = Lists.newArrayList();
        Map<String, List<GrrHistoryDetail>> snGroupDetailMap = grrHistoryDetailList.stream().collect(Collectors.groupingBy(GrrHistoryDetail::getSn));
        snGroupDetailMap.forEach((sn, snGroupDetailList) -> {
            GrrAnalyseReportGetDTO.GrrHistoryDetailInfo grrHistoryDetailInfo = new GrrAnalyseReportGetDTO.GrrHistoryDetailInfo();
            grrHistoryDetailInfo.setSn(sn);
            List<GrrAnalyseReportGetDTO.GrrHistoryDetailInfo.TestDataInfo> testDataInfoList = Lists.newArrayList();
            Map<String, List<GrrHistoryDetail>> testerGroupDetailMap = snGroupDetailList.stream().collect(Collectors.groupingBy(GrrHistoryDetail::getTester));
            testerGroupDetailMap.forEach((tester, testerGroupDetailList) -> {
                testerGroupDetailList.stream().sorted(Comparator.comparing(GrrHistoryDetail::getTimes)).collect(Collectors.toList()).forEach(sortDetail -> {
                    GrrAnalyseReportGetDTO.GrrHistoryDetailInfo.TestDataInfo testDataInfo = new GrrAnalyseReportGetDTO.GrrHistoryDetailInfo.TestDataInfo();
                    testDataInfo.setTester(tester).setTestTimes(sortDetail.getTimes()).setNumber(sortDetail.getNumber());
                    testDataInfoList.add(testDataInfo);
                });
            });
            grrHistoryDetailInfo.setTestDataInfoList(testDataInfoList);
            grrHistoryDetailInfoList.add(grrHistoryDetailInfo);
        });
        grrAnalyseReportGetDto.setGrrHistoryDetailInfoList(grrHistoryDetailInfoList);
        return grrAnalyseReportGetDto;
    }

}
