package net.airuima.grr.service.procedure;

import net.airuima.grr.domain.procedure.GrrHistoryDetail;
import net.airuima.grr.repository.procedure.GrrHistoryDetailRepository;
import net.airuima.grr.web.rest.procedure.dto.GrrHistoryDetailGetDTO;
import net.airuima.grr.web.rest.procedure.dto.GrrHistoryDetailSaveDTO;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.util.MapperUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import org.apache.commons.compress.utils.Lists;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 台位GRR测试历史明细Service
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class GrrHistoryDetailService extends CommonJpaService<GrrHistoryDetail> {

    private final GrrHistoryDetailRepository grrHistoryDetailRepository;

    public GrrHistoryDetailService(GrrHistoryDetailRepository grrHistoryDetailRepository) {
        this.grrHistoryDetailRepository = grrHistoryDetailRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<GrrHistoryDetail> find(Specification<GrrHistoryDetail> spec, Pageable pageable) {
        return grrHistoryDetailRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<GrrHistoryDetail> find(Specification<GrrHistoryDetail> spec) {
        return grrHistoryDetailRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<GrrHistoryDetail> findAll(Pageable pageable) {
        return grrHistoryDetailRepository.findAll(pageable);
    }

    /**
     * 通过台位GRR测试历史ID获取检测数据明细
     *
     * @param historyId 台位GRR测试历史ID
     * @return GrrHistoryDetailGetDTO
     */
    @Transactional(readOnly = true)
    public List<GrrHistoryDetailGetDTO> getCheckResultDetailByResultId(Long historyId) {
        List<GrrHistoryDetailGetDTO> grrHistoryDetailGetDTOList = Lists.newArrayList();
        List<GrrHistoryDetail> grrHistoryDetailList = grrHistoryDetailRepository.findByGrrHistoryIdAndDeleted(historyId, Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(grrHistoryDetailList)) {
            return grrHistoryDetailGetDTOList;
        }

        //按照SN、员工及及检测项目进行分组
        Map<String, List<GrrHistoryDetail>> groupHistoryDetailMap = grrHistoryDetailList.stream().collect(Collectors.groupingBy(grrHistoryDetail -> grrHistoryDetail.getSn() + Constants.STR_PRAGMA + grrHistoryDetail.getTester() + Constants.STR_PRAGMA + grrHistoryDetail.getCheckItemCode()));
        groupHistoryDetailMap.forEach((groupKey, groupHistoryDetailList) -> {
            groupHistoryDetailList.forEach(grrHistoryDetail -> {
                grrHistoryDetailGetDTOList.add(MapperUtils.map(grrHistoryDetail, GrrHistoryDetailGetDTO.class));
            });
        });
        return grrHistoryDetailGetDTOList;
    }

    /**
     * 批量保存手动修改的GRR历史检测项明细数据
     *
     * @param grrHistoryDetailSaveDtoList 批量待保存的GRR历史明细数据参数DTO
     */
    public void batchUpdateHistoryDetail(List<GrrHistoryDetailSaveDTO> grrHistoryDetailSaveDtoList) {
        grrHistoryDetailSaveDtoList.forEach(grrHistoryDetailSaveDto -> {
            Optional<GrrHistoryDetail> grrHistoryDetailOptional = grrHistoryDetailRepository.findById(grrHistoryDetailSaveDto.getId());
            grrHistoryDetailOptional.ifPresent(grrHistoryDetail -> {
                grrHistoryDetail.setNumber(grrHistoryDetailSaveDto.getNumber()).setGrr(grrHistoryDetailSaveDto.getGrr()).setNdc(grrHistoryDetailSaveDto.getNdc()).setDeleted(Constants.LONG_ZERO);
                grrHistoryDetailRepository.save(grrHistoryDetail);
            });
        });
    }

    /**
     * 通过GRR历史ID获取检测数据明细
     * @param historyId GRR历史ID
     * @return List<GrrHistoryDetail>
     */
    public List<GrrHistoryDetail> findByGrrHistoryId(Long historyId){
        return grrHistoryDetailRepository.findByGrrHistoryIdAndDeleted(historyId,Constants.LONG_ZERO);
    }

}
