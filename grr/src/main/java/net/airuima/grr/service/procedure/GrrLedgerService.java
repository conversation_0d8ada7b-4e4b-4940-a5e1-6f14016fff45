package net.airuima.grr.service.procedure;

import net.airuima.grr.domain.procedure.GrrHistory;
import net.airuima.grr.domain.procedure.GrrHistoryDetail;
import net.airuima.grr.domain.procedure.GrrLedger;
import net.airuima.grr.repository.procedure.GrrHistoryDetailRepository;
import net.airuima.grr.repository.procedure.GrrHistoryRepository;
import net.airuima.grr.repository.procedure.GrrLedgerRepository;
import net.airuima.grr.web.rest.procedure.dto.GrrLedgerGetDTO;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import org.apache.commons.compress.utils.Lists;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/5/25
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class GrrLedgerService extends CommonJpaService<GrrLedger> {
    private final GrrLedgerRepository grrLedgerRepository;
    private final GrrHistoryRepository grrHistoryRepository;
    private final GrrHistoryDetailRepository grrHistoryDetailRepository;

    public GrrLedgerService(GrrLedgerRepository grrLedgerRepository, GrrHistoryRepository grrHistoryRepository,
                            GrrHistoryDetailRepository grrHistoryDetailRepository) {
        this.grrLedgerRepository = grrLedgerRepository;
        this.grrHistoryRepository = grrHistoryRepository;
        this.grrHistoryDetailRepository = grrHistoryDetailRepository;
    }

    @Override
    public Page<GrrLedger> find(Specification<GrrLedger> spec, Pageable pageable) {
        return grrLedgerRepository.findAll(spec, pageable);
    }

    @Override
    public List<GrrLedger> find(Specification<GrrLedger> spec) {
        return grrLedgerRepository.findAll(spec);
    }

    @Override
    public Page<GrrLedger> findAll(Pageable pageable) {
        return grrLedgerRepository.findAll(pageable);
    }

    /**
     * 通过台账ID获取明细数据
     *
     * @param id GRR台账ID
     * @return List<GrrLedgerGetDTO>
     */
    @Transactional(readOnly = true)
    public List<GrrLedgerGetDTO> getDetailById(Long id) {
        List<GrrLedgerGetDTO> grrLedgerGetDtoList = Lists.newArrayList();
        GrrLedger grrLedger = grrLedgerRepository.getReferenceById(id);
        GrrHistory grrHistory = grrHistoryRepository.findTop1ByWorkCellCodeAndIsLatestAndDeleted(grrLedger.getWorkCellCode(), Boolean.TRUE, Constants.LONG_ZERO);
        if (null == grrHistory) {
            return grrLedgerGetDtoList;
        }
        List<GrrHistoryDetail> grrHistoryDetailList = grrHistoryDetailRepository.findByGrrHistoryIdAndDeleted(grrHistory.getId(), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(grrHistoryDetailList)) {
            return grrLedgerGetDtoList;
        }
        List<GrrLedgerGetDTO> grrLedgerGetDTOList = new ArrayList<>();
        grrHistoryDetailList.parallelStream().collect(Collectors.groupingBy(grrHistoryDetail ->
                grrHistoryDetail.getCheckItemCode() + Constants.STR_PRAGMA + grrHistoryDetail.getSubModule()))
                .entrySet().parallelStream().map(GrrLedgerGetDTO::new)
                .collect(Collectors.groupingBy(GrrLedgerGetDTO::getCheckItemCode))
                .forEach((splitKey, list) -> {
                    list.sort(Comparator.comparing(GrrLedgerGetDTO::getSubModule));
                    grrLedgerGetDTOList.addAll(list);
                });
        return grrLedgerGetDTOList;
    }
}
