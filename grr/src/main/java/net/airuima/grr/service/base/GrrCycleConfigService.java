package net.airuima.grr.service.base;


import net.airuima.grr.domain.base.GrrCycleConfig;
import net.airuima.grr.repository.base.GrrCycleConfigRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 台位校准周期配置表Service
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class GrrCycleConfigService extends CommonJpaService<GrrCycleConfig> {

    private final GrrCycleConfigRepository grrCycleConfigRepository;

    public GrrCycleConfigService(GrrCycleConfigRepository grrCycleConfigRepository) {
        this.grrCycleConfigRepository = grrCycleConfigRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<GrrCycleConfig> find(Specification<GrrCycleConfig> spec, Pageable pageable) {
        return grrCycleConfigRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<GrrCycleConfig> find(Specification<GrrCycleConfig> spec) {
        return grrCycleConfigRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<GrrCycleConfig> findAll(Pageable pageable) {
        return grrCycleConfigRepository.findAll(pageable);
    }

}
