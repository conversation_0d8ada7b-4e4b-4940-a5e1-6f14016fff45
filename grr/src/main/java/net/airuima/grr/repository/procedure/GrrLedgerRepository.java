package net.airuima.grr.repository.procedure;

import net.airuima.grr.domain.procedure.GrrLedger;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 * 台位GRR台账表Repository
 * <AUTHOR>
 * @date 2022/5/25
 */
@Repository
public interface GrrLedgerRepository extends LogicDeleteableRepository<GrrLedger>,
        JpaSpecificationExecutor<GrrLedger>, JpaRepository<GrrLedger, Long> {

    /**
     * 通过台位编号获取台位GRR最新状态记录
     * @param workCellCode 台位编号
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.grr.GrrLedger> 台位GRR台账
     */
    Optional<GrrLedger> findByWorkCellCodeAndDeleted(String workCellCode, Long deleted);
}
