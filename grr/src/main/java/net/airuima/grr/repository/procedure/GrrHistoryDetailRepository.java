package net.airuima.grr.repository.procedure;

import net.airuima.grr.domain.procedure.GrrHistoryDetail;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 台位GRR测试历史明细Repository
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Repository
public interface GrrHistoryDetailRepository extends LogicDeleteableRepository<GrrHistoryDetail>,
        JpaSpecificationExecutor<GrrHistoryDetail>, JpaRepository<GrrHistoryDetail, Long> {

    /**
     * 通过GRR历史主键ID获取检测数据明细
     * @param historyId GRR历史主键ID
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.grr.GrrHistoryDetail> 台位GRR测试历史明细列表
     */
    List<GrrHistoryDetail> findByGrrHistoryIdAndDeleted(Long historyId,Long deleted);

    /**
     * 通过GRR历史主键ID批量逻辑删除明细数据
     * @param id GRR历史ID
     */
    @Modifying
    @Query("update GrrHistoryDetail set deleted=id where grrHistory.id=?1")
    void batchDeleteByHistoryId(Long id);

    /**
     * 通过GRR历史主键ID，检测项目编码，子模块获取指定数据
     * @param historyId GRR历史主键ID
     * @param checkItemCode 检测项目编码
     * @param subModule 子模块
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.grr.GrrHistoryDetail> 台位GRR测试历史明细列表
     */
    List<GrrHistoryDetail> findByGrrHistoryIdAndCheckItemCodeAndSubModuleAndDeleted(Long historyId,String checkItemCode,String subModule,Long deleted );

    /**
     * 通过台位编码获取不重复的检测项编码和子模块列表
     * @param workCellCode 台位编码
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.grr.GrrHistoryDetail> 台位GRR测试历史明细列表
     */
    @Query("select new GrrHistoryDetail(detail.checkItemCode,detail.subModule) from GrrHistoryDetail detail where detail.grrHistory.workCellCode=?1 and detail.deleted=?2 group by detail.checkItemCode,detail.subModule")
    List<GrrHistoryDetail> findDistinctCheckItemAndSubModuleByWorkCellCode(String workCellCode, Long deleted);
}
