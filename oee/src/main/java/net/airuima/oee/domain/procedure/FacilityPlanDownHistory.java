package net.airuima.oee.domain.procedure;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalTime;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 计划停止历史表Domain
 *
 * <AUTHOR>
 * @date 2022-11-24
 */
@Schema(name = "计划停止历史表(FacilityPlanDownHistory)", description = "计划停止历史表")
@Entity
@Table(name = "procedure_facility_plan_down_history")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "facilityPlanDownHistoryEntityGraph", attributeNodes = {
        @NamedAttributeNode(value = "facilityOee", subgraph = "facilityOeeEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "facilityOeeEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workCell", subgraph = "workCellEntityGraph")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class FacilityPlanDownHistory extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备OEE-ID
     */
    @NotNull
    @Schema(description = "设备OEE-ID", required = true)
    @ManyToOne
    @JoinColumn(name = "facility_oee_id", nullable = false)
    private FacilityOee facilityOee;

    /**
     * 计划停止时间开始
     */
    @Schema(description = "计划停止时间开始")
    @Column(name = "start_time")
    private LocalTime startTime;

    /**
     * 计划停止时间结束
     */
    @Schema(description = "计划停止时间结束")
    @Column(name = "end_time")
    private LocalTime endTime;

    /**
     * 停止原因
     */
    @Schema(description = "停止原因")
    @Column(name = "remark")
    private String remark;


    public FacilityOee getFacilityOee() {
        return facilityOee;
    }

    public FacilityPlanDownHistory setFacilityOee(FacilityOee facilityOee) {
        this.facilityOee = facilityOee;
        return this;
    }

    public LocalTime getStartTime() {
        return startTime;
    }

    public FacilityPlanDownHistory setStartTime(LocalTime startTime) {
        this.startTime = startTime;
        return this;
    }

    public LocalTime getEndTime() {
        return endTime;
    }

    public FacilityPlanDownHistory setEndTime(LocalTime endTime) {
        this.endTime = endTime;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public FacilityPlanDownHistory setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        FacilityPlanDownHistory facilityPlanDownHistory = (FacilityPlanDownHistory) o;
        if (facilityPlanDownHistory.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), facilityPlanDownHistory.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
