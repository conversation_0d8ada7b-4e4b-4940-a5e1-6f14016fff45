package net.airuima.oee.proxy;

import net.airuima.config.bean.BeanDefine;
import net.airuima.oee.dto.calendar.CalendarDTO;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class OeeCalendarProxy {

    @BeanDefine(value = "calendarRepository",funcKey = "WorkCalendar")
    public List<CalendarDTO> findByWorkLineIdInAndWorkTimeAndWorkStationIdIsNullAndDeleted(List<Long> workLineIds, LocalDate workTime, Long deleted){
        return null;
    }

    @BeanDefine(value = "calendarRepository",funcKey = "WorkCalendar")
    public List<CalendarDTO> findByWorkStationIdInAndWorkLineIdNullAndWorkTimeAndDeleted(List<Long> workStationIds, LocalDate workTime, Long deleted){
        return null;
    }

    @BeanDefine(value = "calendarRepository",funcKey = "WorkCalendar")
    public CalendarDTO findTop1ByWorkLineIdAndWorkTimeAndWorkStationIdIsNullAndDeleted(Long workLineId, LocalDate workTime, Long deleted){
        return null;
    }

    @BeanDefine(value = "calendarRepository",funcKey = "WorkCalendar")
    public CalendarDTO findTop1ByWorkStationIdAndWorkLineIdNullAndWorkTimeAndDeleted(Long workStationId, LocalDate workTime, Long deleted){
        return null;
    }

    @BeanDefine(value = "calendarService",funcKey = "WorkCalendar")
    public List<CalendarDTO> findByWorkTimeTomorrow(String workTime){
        return null;
    }
}
