package net.airuima.oee.proxy;

import net.airuima.config.bean.BeanDefine;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class OeeFacilityIotProxy {

    @BeanDefine(value = "facilityIotMqttWebSender",funcKey = "FBase && IOT")
    public void sendFacilityStatusChangeMsg(List<String> facilityCodes){

    }
}
