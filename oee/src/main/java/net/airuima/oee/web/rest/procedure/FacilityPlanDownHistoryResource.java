package net.airuima.oee.web.rest.procedure;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.oee.domain.procedure.FacilityPlanDownHistory;
import net.airuima.oee.service.procedure.FacilityPlanDownHistoryService;
import net.airuima.oee.web.rest.procedure.dto.FacilityPlanDownHistoryCreateDTO;
import net.airuima.oee.web.rest.procedure.dto.FacilityPlanDownHistoryReturnDTO;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.BaseResource;
import net.airuima.web.rest.errors.BadRequestAlertException;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.net.URISyntaxException;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 计划停止历史表Resource
 *
 * <AUTHOR>
 * @date 2022-11-24
 */
@Tag(name = "计划停止历史表Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/facility-plan-down-histories")
@AuthorityRegion("设备OEE")
@FuncInterceptor("FacilityOee")
public class FacilityPlanDownHistoryResource extends BaseResource<FacilityPlanDownHistory> {
    private static final String EXCEPTION = "exception";
    private final FacilityPlanDownHistoryService facilityPlanDownHistoryService;

    public FacilityPlanDownHistoryResource(FacilityPlanDownHistoryService facilityPlanDownHistoryService) {
        this.facilityPlanDownHistoryService = facilityPlanDownHistoryService;
        this.mapUri = "/api/facility-plan-down-histories";
    }

    /**
     * 新增计划停止时间
     *
     * @param createDTO 计划停止历史表新增DTO
     * @return : org.springframework.http.ResponseEntity<net.airuima.rbase.domain.procedure.oee.FacilityPlanDownHistory>
     * <AUTHOR>
     * @date 2022/12/2
     **/
    @PreAuthorize(" hasAnyAuthority('FACILITYOEE_CREATE') or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary= "新增计划停止时间")
    @PostMapping("/custom")
    @PreventRepeatSubmit
    public ResponseEntity<ResponseData<FacilityPlanDownHistory>> custom(@Valid @RequestBody FacilityPlanDownHistoryCreateDTO createDTO) throws URISyntaxException {
        try {
            FacilityPlanDownHistory facilityPlanDownHistory = facilityPlanDownHistoryService.create(createDTO);
            return ResponseData.ok(facilityPlanDownHistory);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 删除计划停止时间
     *
     * @param id 主键
     * @return : org.springframework.http.ResponseEntity<java.lang.Void>
     * <AUTHOR>
     * @date 2022/12/2
     **/
    @PreAuthorize("hasAnyAuthority('FACILITYOEE_DELETE') or hasAnyAuthority('ROLE_ADMIN')")
    @DeleteMapping({"/{id}"})
    @Operation(summary= "删除计划停止时间")
    @Override
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        try {
            facilityPlanDownHistoryService.deleteEntity(id);
            return ResponseEntity.ok().headers(HeaderUtil.deletedAlert(this.entityName, id.toString())).build();
        } catch (ResponseException responseException){
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        }catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, EXCEPTION, e.getMessage())).build();
        }
    }

    /**
     * 根据设备运行状态ID查询计划停止历史表集合
     *
     * @param facilityOeeId 设备运行状态ID
     * @return : org.springframework.http.ResponseEntity<java.util.List<net.airuima.rbase.domain.procedure.oee.FacilityPlanDownHistory>>
     * <AUTHOR>
     * @date 2022/12/5
     **/
    @PreAuthorize("hasAnyAuthority('FACILITYOEE_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary= "根据设备运行状态ID查询计划停止历史表集合")
    @GetMapping("/facilityOeeId/{facilityOeeId}")
    public ResponseEntity<ResponseData<List<FacilityPlanDownHistoryReturnDTO>>> findByFacilityOeeId(@PathVariable(value = "facilityOeeId") Long facilityOeeId) {
        try {
            List<FacilityPlanDownHistoryReturnDTO> facilityPlanDownHistoryList = facilityPlanDownHistoryService.findByFacilityOeeId(facilityOeeId);
            return ResponseData.ok(facilityPlanDownHistoryList);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

}
