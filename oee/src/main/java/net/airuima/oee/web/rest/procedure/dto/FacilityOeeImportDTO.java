package net.airuima.oee.web.rest.procedure.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDate;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
public class FacilityOeeImportDTO {

    /**
     * 工位编码
     */
    @Excel(name = "工位编码",orderNum = "1")
    private String workCellCode;

    /**
     * 设备编码
     */
    @Excel(name = "设备编码",orderNum = "2")
    private String facilityCode;

    /**
     * 日实际作业时长(s)
     */
    @Excel(name = "日实际作业时长(s)",orderNum = "3")
    private int actualWorkDuration;

    /**
     * 理论节拍时间(s)
     */
    @Excel(name = "理论节拍时间(s)",orderNum = "4")
    private int duration;

    /**
     * 日计划停线时长(s)
     */
    @Excel(name = "日计划停线时长(s)",orderNum = "5")
    private int planDownDuration;

    /**
     * 日停线时长(s)
     */
    @Excel(name = "日停线时长(s)",orderNum = "6")
    private int downLineDuration;

    /**
     * 实际完成数量
     */
    @Excel(name = "完成数量",orderNum = "7")
    private int finishNumber;

    /**
     * 合格数量
     */
    @Excel(name = "合格数量",orderNum = "8")
    private int qualifiedNumber;


    /**
     * 记录日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @Excel(name = "记录日期",format = "yyyy-MM-dd",orderNum = "9")
    private LocalDate recordDate;

    /**
     * 错误原因
     */
    @Excel(name = "错误原因")
    private String message;

    public String getWorkCellCode() {
        return workCellCode;
    }

    public FacilityOeeImportDTO setWorkCellCode(String workCellCode) {
        this.workCellCode = workCellCode;
        return this;
    }

    public String getFacilityCode() {
        return facilityCode;
    }

    public FacilityOeeImportDTO setFacilityCode(String facilityCode) {
        this.facilityCode = facilityCode;
        return this;
    }

    public int getActualWorkDuration() {
        return actualWorkDuration;
    }

    public FacilityOeeImportDTO setActualWorkDuration(int actualWorkDuration) {
        this.actualWorkDuration = actualWorkDuration;
        return this;
    }

    public int getDuration() {
        return duration;
    }

    public FacilityOeeImportDTO setDuration(int duration) {
        this.duration = duration;
        return this;
    }

    public int getDownLineDuration() {
        return downLineDuration;
    }

    public FacilityOeeImportDTO setDownLineDuration(int downLineDuration) {
        this.downLineDuration = downLineDuration;
        return this;
    }

    public int getFinishNumber() {
        return finishNumber;
    }

    public FacilityOeeImportDTO setFinishNumber(int finishNumber) {
        this.finishNumber = finishNumber;
        return this;
    }

    public int getQualifiedNumber() {
        return qualifiedNumber;
    }

    public FacilityOeeImportDTO setQualifiedNumber(int qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public LocalDate getRecordDate() {
        return recordDate;
    }

    public FacilityOeeImportDTO setRecordDate(LocalDate recordDate) {
        this.recordDate = recordDate;
        return this;
    }

    public String getMessage() {
        return message;
    }

    public FacilityOeeImportDTO setMessage(String message) {
        this.message = message;
        return this;
    }

    public int getPlanDownDuration() {
        return planDownDuration;
    }

    public FacilityOeeImportDTO setPlanDownDuration(int planDownDuration) {
        this.planDownDuration = planDownDuration;
        return this;
    }
}
