package net.airuima.oee.web.rest.procedure.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 设备状态历史表Domain
 *
 * <AUTHOR>
 * @date 2022-11-24
 */
@Schema(name = "设备状态历史表(FacilityStatusChangeHistory)", description = "设备状态历史表")
public class FacilityStatusChangeHistoryReturnDTO implements Serializable {
    /**
     * 记录时间
     */
    @Schema(description = "记录时间")
    private LocalDateTime recordTime;

    /**
     * 原运行状态（0：正常运行，1：正常关机，2：异常停机，3：维护保养，4：调机换型）
     */
    @Schema(description = "原运行状态（0：正常运行，1：正常关机，2：异常停机，3：维护保养，4：调机换型）", required = true)
    private int originStatus;

    /**
     * 目标运行状态（0：正常运行，1：正常关机，2：异常停机，3：维护保养，4：调机换型）
     */
    @Schema(description = "目标运行状态（0：正常运行，1：正常关机，2：异常停机，3：维护保养，4：调机换型）", required = true)
    private int targetStatus;

    /**
     * 提交人
     */
    @Schema(description = "提交人")
    private String createdBy;

    public LocalDateTime getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(LocalDateTime recordTime) {
        this.recordTime = recordTime;
    }

    public int getOriginStatus() {
        return originStatus;
    }

    public void setOriginStatus(int originStatus) {
        this.originStatus = originStatus;
    }

    public int getTargetStatus() {
        return targetStatus;
    }

    public void setTargetStatus(int targetStatus) {
        this.targetStatus = targetStatus;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
}
