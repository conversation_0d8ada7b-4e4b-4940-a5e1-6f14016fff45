package net.airuima.oee.repository.procedure;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.oee.domain.procedure.FacilityOee;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.base.scene.WorkCellStepFacility;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 设备运行状态表Repository
 *
 * <AUTHOR>
 * @date 2022-11-24
 */
@Repository
public interface FacilityOeeRepository extends LogicDeleteableRepository<FacilityOee>,
        EntityGraphJpaSpecificationExecutor<FacilityOee>, EntityGraphJpaRepository<FacilityOee, Long> {
    /**
     * 根据工位主键ID+设备主键ID集合+记录日期+删除标识 查询设备运行状态
     *
     * @param workCellId      工位主键ID
     * @param equipmentIdList 设备主键ID集合
     * @param recordDate      记录日期
     * @param deleted         删除标识
     * @return java.util.List<net.airuima.domain.procedure.oee.FacilityOee> 设备运行状态列表
     * <AUTHOR>
     * @date 2022/11/30
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query(value = "select fo from FacilityOee fo where fo.workCell.id = ?1 and fo.facilityId in (?2) and fo.recordDate = ?3 and fo.deleted = ?4")
    List<FacilityOee> findByWorkCellIdAndFacilityIdInAndRecordDateAndDeleted(Long workCellId, List<Long> equipmentIdList, LocalDate recordDate, Long deleted);

    /**
     * 通过主键Id和删除标识查询设备运行状态
     *
     * @param facilityOeeId
     * @param deleted
     * @return net.airuima.domain.procedure.oee.FacilityOee 设备运行状态
     * <AUTHOR>
     * @date 2022/12/2
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    FacilityOee findByIdAndDeleted(Long facilityOeeId, Long deleted);


    /**
     * 根据工站主键ID+记录日期+删除标识 查询设备运行状态
     *
     * @param workStationId 工站主键ID集合
     * @param recordDate    记录日期
     * @param deleted       删除标识
     * @return java.util.List<net.airuima.domain.procedure.oee.FacilityOee> 设备运行状态列表
     * <AUTHOR>
     * @date 2022/11/30
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query(value = "select fo from FacilityOee fo where fo.workCell.workStation.id = ?1 and fo.recordDate = ?2 and fo.deleted = ?3")
    List<FacilityOee> findByWorkStationIdAndRecordDateAndDeleted(Long workStationId, LocalDate recordDate, Long deleted);


    /**
     * 根据工站主键ID+记录日期+删除标识 查询设备运行状态
     *
     * @param workLineId 产线主键ID集合
     * @param recordDate 记录日期
     * @param deleted    删除标识
     * @return java.util.List<net.airuima.domain.procedure.oee.FacilityOee> 设备运行状态列表
     * <AUTHOR>
     * @date 2022/11/30
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query(value = "select fo from FacilityOee fo where fo.workCell.workLine.id = ?1 and fo.recordDate = ?2 and fo.deleted = ?3")
    List<FacilityOee> findByWorkLineIdAndRecordDateAndDeleted(Long workLineId, LocalDate recordDate, Long deleted);



    @DataFilter(isSkip = true)
    @FetchMethod
    @Query(value = "select fo from FacilityOee fo where (coalesce(?1, null) is null or (fo.workCell.workLine.id IN (?1))) and fo.recordDate = ?2 and (coalesce(?3, null) is null or (fo.facilityId=?3)) and fo.deleted = ?4")
    List<FacilityOee> findByWorkLineIdInAndRecordDateAndFacilityIdAndDeleted(List<Long> workLineIds, LocalDate recordDate,Long facilityId, Long deleted);


    /**
     * 根据工位主键Id + 记录日期 + 删除标识，查询工位OEE
     *
     * @param workCellId 工位主键ID
     * @param recordDate 记录日期国
     * @param deleted    删除标识
     * @return java.util.List<net.airuima.domain.procedure.oee.FacilityOee> 设备运行状态列表
     * <AUTHOR>
     * @date 2022/12/21
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<FacilityOee> findByWorkCellIdAndFacilityIdIsNullAndRecordDateAndDeleted(Long workCellId, LocalDate recordDate, Long deleted);

    /**
     * 根据工位主键ID + 设备主键ID为空 + 异常停机开始时间不为空 + 删除标识，查询工位OEE
     *
     * @param workCellId 工位主键ID
     * @param deleted    删除标识
     * @return net.airuima.domain.procedure.oee.FacilityOee 设备运行状态
     * <AUTHOR>
     * @date 2022/12/22
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    FacilityOee findTop1ByWorkCellIdAndFacilityIdIsNullAndAbnormalStatusStartTimeIsNotNullAndDeleted(Long workCellId, Long deleted);

    /**
     * 根据工位主键ID + 设备主键ID + 异常停机开始时间不为空 + 删除标识，查询设备OEE
     *
     * @param workCellId 工位主键ID
     * @param facilityId 设备主键ID
     * @param deleted    删除标识
     * @return net.airuima.domain.procedure.oee.FacilityOee 设备运行状态
     * <AUTHOR>
     * @date 2022/12/22
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    FacilityOee findTop1ByWorkCellIdAndFacilityIdAndAbnormalStatusStartTimeIsNotNullAndDeleted(Long workCellId, Long facilityId, Long deleted);

    /**
     * 通过工站主键ID集合++记录日期+删除标识查询OEE
     *
     * @param workStationIdList 工站主键ID集合
     * @param recordDate        记录日期
     * @param deleted           删除标识
     * @return java.util.List<net.airuima.domain.procedure.oee.FacilityOee> 设备运行状态列表
     * <AUTHOR>
     * @date 2023/5/17
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<FacilityOee> findByWorkCellWorkStationIdInAndRecordDateAndDeleted(List<Long> workStationIdList,  LocalDate recordDate, Long deleted);

    /**
     * 通过产线主键ID集合+记录日期+删除标识查询OEE
     *
     * @param workLineIdList 产线ID集合
     * @param recordDate        记录日期
     * @param deleted           删除标识
     * @return java.util.List<net.airuima.domain.procedure.oee.FacilityOee> 设备运行状态列表
     * <AUTHOR>
     * @date 2023/5/17
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<FacilityOee> findByWorkCellWorkLineIdInAndRecordDateAndDeleted(List<Long> workLineIdList, LocalDate recordDate, Long deleted);

    /**
     * 通过记录日期+删除标识查询OEE
     *
     * @param recordDate 记录日期
     * @param deleted    删除标识
     * @return java.util.List<net.airuima.domain.procedure.oee.FacilityOee> 设备运行状态列表
     * <AUTHOR>
     * @date 2023/5/17
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<FacilityOee> findByRecordDateAndDeleted(LocalDate recordDate, Long deleted);



    /**
     * 查询指定记录日期中不为指定工站的OEE
     *
     * @param workStationIdList 工站主键ID集合
     * @param recordDate         记录日期
     * @param deleted            删除标识
     * @return java.util.List<net.airuima.domain.procedure.oee.FacilityOee> 设备运行状态列表
     * <AUTHOR>
     * @date 2023/5/17
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<FacilityOee> findByWorkCellWorkStationIdNotInAndRecordDateAndDeleted(List<Long> workStationIdList, LocalDate recordDate, Long deleted);

    /**
     * 查询指定记录日期中不为指定产线的OEE
     *
     * @param workLineIdList 产线ID集合
     * @param recordDate         记录日期
     * @param deleted            删除标识
     * @return java.util.List<net.airuima.domain.procedure.oee.FacilityOee> 设备运行状态列表
     * <AUTHOR>
     * @date 2023/5/17
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<FacilityOee> findByWorkCellWorkLineIdNotInAndRecordDateAndDeleted(List<Long> workLineIdList, LocalDate recordDate, Long deleted);


    /**
     * 通过产线主键ID+记录日期+删除标识查询状态+数量
     *
     * @param workLineId 产线主键ID
     * @param recordDate 记录日期
     * @param deleted    删除标识
     * @return : java.util.List<java.util.Map<java.lang.Integer,java.lang.Integer>> 数量
     * <AUTHOR>
     * @date 2023/5/19
     **/
    @DataFilter(isSkip = true)
    @Query("select f from FacilityOee f where (coalesce(?1, null) is null or (f.workCell.workLine.id IN (?1) or f.workCell.workStation.workLine.id IN (?1)))" +
            " and (coalesce(?3, null) is null or f.facilityId=?3) " +
            " and f.recordDate = ?2 " +
            " and f.deleted = ?4")
    List<FacilityOee> findByWorkCellWorkLineIdInAndRecordDateAndFacilityIdAndDeleted(List<Long> workLineIds, LocalDate recordDate, Long facilityId,Long deleted);



    @DataFilter(isSkip = true)
    @Query("select f from FacilityOee f where (case when ?1 is null then true else f.workCell.workLine.id in (?1) end) " +
            "and (case when ?3 is null then true else f.facilityId=?3 end) " +
            " and f.recordDate = ?2 " +
            " and f.deleted = ?4")
    List<FacilityOee> findByWorkCellWorkLineIdAndRecordDateAndDeletedWhenNoWorkStation(List<Long> workLineIds, LocalDate recordDate, Long facilityId, Long deleted);

    /**
     * OEE过低TOP5(OEE为0的不展示)
     *
     * @param workLineId 产线主键ID
     * @param recordDate 记录日期
     * @param deleted    删除标识
     * @return : java.util.List<java.util.Map<java.lang.String,java.lang.Object>> OEE过低TOP5数据
     * <AUTHOR>
     * @date 2023/5/19
     **/
    @DataFilter(isSkip = true)
    @Query(value = "select bf.code as code,bf.name as name, " +"IFNULL((f.activation_duration/f.load_duration),0) as timeActivation, "+
            "IFNULL((((case when b.unit = 1 then b.duration*60 when b.unit = 2 then b.duration*3600 else b.duration end) * f.finish_number)/f.activation_duration), 0) as performActivation, "+
            "round( " +
            "IFNULL((f.activation_duration/f.load_duration),0) *    " +
            "IFNULL((((case when b.unit = 1 then b.duration*60 when b.unit = 2 then b.duration*3600 else b.duration end) * f.finish_number)/f.activation_duration), 0) *  " +
            "IFNULL(((f.finish_number - f.unqualified_number)/f.finish_number),0) " +
            ", 4) as oee " +
            "from procedure_facility_oee f  " +
            "left join base_work_cell c on f.work_cell_id = c.id " +
            "left join base_work_station s on c.work_station_id = s.id " +
            "left join base_facility bf on f.facility_id = bf.id " +
            "left join base_facility_cadence b on (b.work_cell_id = f.work_cell_id and b.facility_id = f.facility_id) " +
            "where f.actual_work_duration != 0  " +
            "and (coalesce(?1, null) is null or (s.work_line_id in (?1) or c.work_line_id in(?1))) " +
            "and (coalesce(?3, null) is null or (f.facility_id=?3)) " +
            "and f.record_date = ?2 and f.deleted = ?4 " +
            "and IFNULL((f.activation_duration/f.load_duration),0) != 0 " +
            "and IFNULL((((case when b.unit = 1 then b.duration*60 when b.unit = 2 then b.duration*3600 else b.duration end) * f.finish_number)/f.activation_duration), 0) != 0 " +
            "and IFNULL(((f.finish_number - f.unqualified_number)/f.finish_number),0) != 0 " +
            "order by oee ", nativeQuery = true)
    List<Map<String, Object>> facilityOeeLowHasWorkLine(List<Long> workLineIds, LocalDate recordDate, Long facilityId, Long deleted);

    /**
     * OEE过低TOP5(OEE为0的不展示)
     *
     * @param recordDate 记录日期
     * @param deleted    删除标识
     * @return : java.util.List<java.util.Map<java.lang.String,java.lang.Object>>  OEE过低TOP5数据
     * <AUTHOR>
     * @date 2023/5/19
     **/
    @DataFilter(isSkip = true)
    @Query(value = "select bf.name as name," +
            "round( " +
            "IFNULL((f.activation_duration/f.load_duration),0) *    " +
            "IFNULL((((case when b.unit = 1 then b.duration*60 when b.unit = 2 then b.duration*3600 else b.duration end) * f.finish_number)/f.activation_duration), 0) *  " +
            "IFNULL(((f.finish_number - f.unqualified_number)/f.finish_number),0) " +
            ", 4) as number " +
            "from procedure_facility_oee f  " +
            "left join base_work_cell c on f.work_cell_id = c.id " +
            "left join base_work_station s on c.work_station_id = s.id " +
            "left join base_facility bf on f.facility_id = bf.id " +
            "left join base_facility_cadence b on (b.work_cell_id = f.work_cell_id and b.facility_id = f.facility_id) " +
            "where f.actual_work_duration != 0  " +
            "and f.record_date = ?1 and f.deleted = ?2 " +
            "and IFNULL((f.activation_duration/f.load_duration),0) != 0 " +
            "and IFNULL((((case when b.unit = 1 then b.duration*60 when b.unit = 2 then b.duration*3600 else b.duration end) * f.finish_number)/f.activation_duration), 0) != 0 " +
            "and IFNULL(((f.finish_number - f.unqualified_number)/f.finish_number),0) != 0 " +
            "order by number limit 5", nativeQuery = true)
    List<Map<String, Object>> facilityOeeLowTop5(LocalDate recordDate, Long deleted);


    /**
     * 通过产线主键ID+记录日期+状态+删除标识查询OEE集合（带分页）
     *
     * @param recordDate 记录日期
     * @param deleted    删除标识
     * @return org.springframework.data.domain.Page<net.airuima.domain.procedure.oee.FacilityOee> 设备运行状态分页
     * <AUTHOR>
     * @date 2023/5/19
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select f from FacilityOee f where (case when ?3 is null then true else f.facilityId in (?3) end) " +
            "and f.recordDate = ?2 " +
            "and (coalesce(?1, null) is null or (f.workCell.workStation.workLine.id in (?1) or f.workCell.workLine.id in(?1)))  " +
            "and f.actualWorkDuration != ?4 and f.deleted = ?5")
    List<FacilityOee> findByWorkLineIdAndRecordDateAndActualWorkDurationNotAndDeleted(List<Long> workLineIds, LocalDate recordDate, List<Long> facilityIds, int actualWorkDuration, Long deleted);



    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select f from FacilityOee f where  (case when ?3 is null then true else f.facilityId in (?3) end) " +
            "and f.recordDate = ?2 " +
            "and (case when ?1 is null then true else f.workCell.workLine.id in (?1) end) " +
            "and f.actualWorkDuration != ?4 and f.deleted = ?5")
    List<FacilityOee> findByWorkLineIdAndRecordDateAndActualWorkDurationNotAndDeletedWhenNoWorkStation(List<Long> workLineIds, LocalDate recordDate, List<Long> facilityIds,  int actualWorkDuration, Long deleted);


    /**
     * 通过记录日期+状态+删除标识查询OEE集合（带分页）
     *
     * @param recordDate 记录日期
     * @param deleted    删除标识
     * @return org.springframework.data.domain.Page<net.airuima.domain.procedure.oee.FacilityOee> 设备运行状态分页
     * <AUTHOR>
     * @date 2023/5/19
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select f from FacilityOee f where f.recordDate = ?1 and f.actualWorkDuration != ?2 and f.deleted = ?3")
    Page<FacilityOee> findByRecordDateAndActualWorkDurationNotAndDeleted(LocalDate recordDate, Integer actualWorkDuration, Long deleted, Pageable pageable);


    /**
     * 通过工位主键ID+设备主键ID+记录时间范围+删除标识查询设备OEE集合
     *
     * @param workCellId 工位主键ID
     * @param facilityId 设备主键ID
     * @param recordDate 记录日期
     * @param now        记录日期
     * @param deleted    删除标识
     * @return java.util.List<net.airuima.domain.procedure.oee.FacilityOee> 设备运行状态列表
     * <AUTHOR>
     * @date 2023/5/19
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<FacilityOee> findByWorkCellIdAndFacilityIdAndRecordDateGreaterThanEqualAndRecordDateLessThanEqualAndDeletedOrderByRecordDateDesc(Long workCellId, Long facilityId, LocalDate recordDate, LocalDate now, Long deleted);

    /**
     * 通过工位主键ID+设备主键ID+删除标识，查询设备OEE
     *
     * @param workCellId 工位主键ID
     * @param facilityId 设备主键ID
     * @param recordDate 记录时间
     * @param deleted    删除标识
     * @return java.util.List<net.airuima.domain.procedure.oee.FacilityOee> 设备运行状态列表
     * <AUTHOR>
     * @date 2023/5/19
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<FacilityOee> findByWorkCellIdAndFacilityIdAndRecordDateLessThanEqualAndDeleted(Long workCellId, Long facilityId, LocalDate recordDate, Long deleted);


    /**
     *
     * @param workCellId 工位ID
     * @param facilityId 设备ID
     * @param recordDate 记录日期
     * @param deleted 逻辑删除
     * @return  Optional<FacilityOee>
     */
    Optional<FacilityOee> findByWorkCellIdAndFacilityIdAndRecordDateAndDeleted(Long workCellId, Long facilityId, LocalDate recordDate, Long deleted);


    @DataFilter(isSkip = true)
    @Query("select fo from FacilityOee fo where fo.facilityId=?2 and fo.recordDate=?3 and fo.deleted=?4 and (case when ?1 is null then true else fo.workCell.code=?1 end)")
    List<FacilityOee> findByWorkCellCodeAndFacilityIdAndRecordDateAndDeleted(String workCellCode, Long facilityId, LocalDate recordDate, Long deleted);


    /**
     * 通过产线主键ID+记录时间+删除标识查询指定日期中不存在OEE数据的工位信息
     *
     * @param workLineId 产线主键ID
     * @param recordDate 记录时间
     * @param deleted    删除标识
     * @return java.util.List<net.airuima.rbase.domain.base.scene.WorkCell> 工位列表
     * <AUTHOR>
     * @date 2022/12/6
     **/
    @DataFilter(isSkip = true)
    @Query(value = "select w from WorkCell w where w.workLine.id = ?1 " +
            "and not exists (select o from FacilityOee o where o.workCell.id = w.id and o.recordDate = ?2 and o.facilityId is null and o.deleted = 0l) " +
            "and w.deleted = ?3")
    @FetchMethod
    List<WorkCell> findByWorkLineIdAndDeletedGroupByWorkCellId(Long workLineId, LocalDate recordDate, Long deleted);

    /**
     * 通过工站主键ID+记录时间+删除标识查询指定日期中不存在OEE数据的工位信息
     *
     * @param workStationId 工站主键ID
     * @param recordDate    记录时间
     * @param deleted       删除标识
     * @return java.util.List<net.airuima.rbase.domain.base.scene.WorkCell> 工位列表
     * <AUTHOR>
     * @date 2022/12/6
     **/
    @DataFilter(isSkip = true)
    @Query(value = "select w from WorkCell w where w.workStation.id = ?1 " +
            "and not exists (select o from FacilityOee o where o.workCell.id = w.id and o.recordDate = ?2 and o.facilityId is null  and o.deleted = 0l) " +
            "and w.deleted = ?3")
    @FetchMethod
    List<WorkCell> findByWorkStationIdAndDeletedGroupByWorkCellId(Long workStationId, LocalDate recordDate, Long deleted);

    /**
     * 通过产线主键ID+记录时间+删除标识查询指定日期中不存在OEE数据的工位设备信息
     *
     * @param workLineId 产线主键ID
     * @param recordDate 记录时间
     * @param deleted    删除标识
     * @return java.util.List<net.airuima.domain.base.scene.WorkCellStepFacility> 工位工序设备列表
     * <AUTHOR>
     * @date 2022/12/6
     **/
    @DataFilter(isSkip = true)
    @Query(value = "select w from WorkCellStepFacility w where w.workCell.workLine.id = ?1 " +
            "and not exists (select o from FacilityOee o where o.workCell.id = w.workCell.id and o.recordDate = ?2 and o.facilityId = w.facilityId and o.deleted = 0l) " +
            "and w.deleted = ?3 group by w.workCell, w.facilityId")
    @FetchMethod
    List<WorkCellStepFacility> findByWorkLineIdAndDeletedGroupByWorkCellIdAndFacilityId(Long workLineId, LocalDate recordDate, Long deleted);



    /**
     * 通过工站主键ID+记录时间+删除标识查询指定日期中不存在设备OEE数据的工位设备信息
     *
     * @param workStationId 工站主键ID
     * @param recordDate    记录时间
     * @param deleted       删除标识
     * @return java.util.List<net.airuima.domain.base.scene.WorkCellStepFacility> 工位工序设备列表
     * <AUTHOR>
     * @date 2022/12/6
     **/
    @DataFilter(isSkip = true)
    @Query(value = "select w from WorkCellStepFacility w where w.workCell.workStation.id = ?1 " +
            "and not exists (select o from FacilityOee o where o.workCell.id = w.workCell.id and o.recordDate = ?2 and o.facilityId = w.facilityId and o.deleted = 0l) " +
            "and w.deleted = ?3 group by w.workCell, w.facilityId")
    @FetchMethod
    List<WorkCellStepFacility> findByWorkStationIdAndDeletedGroupByWorkCellIdAndFacilityId(Long workStationId, LocalDate recordDate, Long deleted);
}
