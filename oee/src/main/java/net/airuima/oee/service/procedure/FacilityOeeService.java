package net.airuima.oee.service.procedure;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.oee.domain.base.FacilityCadence;
import net.airuima.oee.domain.procedure.FacilityOee;
import net.airuima.oee.domain.procedure.FacilityPlanDownHistory;
import net.airuima.oee.dto.calendar.CalendarDTO;
import net.airuima.oee.dto.calendar.EveryDayShiftDTO;
import net.airuima.oee.proxy.OeeCalendarProxy;
import net.airuima.oee.proxy.OeeFacilityIotProxy;
import net.airuima.oee.repository.base.FacilityCadenceRepository;
import net.airuima.oee.repository.procedure.FacilityOeeRepository;
import net.airuima.oee.repository.procedure.FacilityPlanDownHistoryRepository;
import net.airuima.oee.service.procedure.api.IFacilityOeeService;
import net.airuima.oee.web.rest.procedure.dto.FacilityOeeCreateDTO;
import net.airuima.oee.web.rest.procedure.dto.FacilityOeeImportDTO;
import net.airuima.oee.web.rest.procedure.dto.FacilitySliceDashboardDTO;
import net.airuima.oee.web.rest.procedure.dto.FacilityStatusDashboardDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.FuncKeyConstants;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.base.scene.WorkCellStepFacility;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.dto.rabbitmq.DelayedMessageDTO;
import net.airuima.rbase.dto.rfms.*;
import net.airuima.rbase.dto.rule.DictionaryDTO;
import net.airuima.rbase.proxy.rfms.RbaseFacilityProxy;
import net.airuima.rbase.proxy.rule.RbaseDictionaryProxy;
import net.airuima.rbase.proxy.rule.RbaseSysCodeProxy;
import net.airuima.rbase.rabbitmq.RmesRabbitMqSender;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.repository.base.scene.WorkLineRepository;
import net.airuima.rbase.repository.procedure.scene.WorkCellStepFacilityRepository;
import net.airuima.rbase.util.DateUtils;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.klock.annotation.Klock;
import org.springframework.boot.autoconfigure.klock.model.LockTimeoutStrategy;
import org.springframework.core.annotation.Order;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.*;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 设备运行状态表Service
 *
 * <AUTHOR>
 * @date 2022-11-24
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class FacilityOeeService extends CommonJpaService<FacilityOee> implements IFacilityOeeService {
    private final Logger log = LoggerFactory.getLogger(FacilityOeeService.class);
    private final String facilityOeeEntityGraph = "facilityOeeEntityGraph";
    @Autowired
    private FacilityOeeRepository facilityOeeRepository;
    @Autowired
    private WorkLineRepository workLineRepository;
    @Autowired
    private RbaseDictionaryProxy dictionaryProxy;
    @Autowired
    private WorkCellRepository workCellRepository;
    @Autowired
    private WorkCellStepFacilityRepository workCellStepFacilityRepository;
    @Autowired
    private FacilityPlanDownHistoryRepository facilityPlanDownHistoryRepository;
    @Autowired
    private RmesRabbitMqSender rmesRabbitMqSender;
    @Autowired
    private RbaseSysCodeProxy rbaseSysCodeProxy;
    @Autowired
    private RbaseFacilityProxy rbaseFacilityProxy;
    @Autowired
    private OeeCalendarProxy oeeCalendarProxy;
    @Autowired
    private FacilityCadenceRepository facilityCadenceRepository;
    @Autowired
    private OeeFacilityIotProxy oeeFacilityIotProxy;

    @Autowired
    private RedisUtils redisUtils;

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<FacilityOee> find(Specification<FacilityOee> spec, Pageable pageable) {
        return facilityOeeRepository.findAll(spec, pageable, new NamedEntityGraph(facilityOeeEntityGraph));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<FacilityOee> find(Specification<FacilityOee> spec) {
        return facilityOeeRepository.findAll(spec, new NamedEntityGraph(facilityOeeEntityGraph));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<FacilityOee> findAll(Pageable pageable) {
        return facilityOeeRepository.findAll(pageable, new NamedEntityGraph(facilityOeeEntityGraph));
    }

    /**
     * 新增当天指定生产线下所有工站下的设备或工位数据
     *
     * @param facilityOeeCreateDTO 设备OEE创建DTO
     * @param recordDate           指定记录日期
     * @return : void
     * <AUTHOR>
     * @date 2022/12/6
     **/
    public void createByWorkLine(FacilityOeeCreateDTO facilityOeeCreateDTO, LocalDate recordDate) {
        //需要生成OEE的生产线及对应每日班次信息集合，Map<生产线，List<每日班次信息>>
        Map<Long, List<EveryDayShiftDTO>> workLineIdAndWorkTime = facilityOeeCreateDTO.getWorkIdAndWorkTime();

        //1. OEE类别（0:工位OEE，1:设备OEE）
        Integer oeeCategoryKey = getOeeCategoryKey();
        //2. 遍历生产线信息集合
        workLineIdAndWorkTime.forEach((workLineId, everyDayShiftDTOList) -> {
            //生产线ID
            //每日班次信息集合
            //获取待新增OEE对象集合（生产线）
            List<FacilityOee> facilityOeeList = addFacilityOeeListByWorkLineId(oeeCategoryKey, workLineId, everyDayShiftDTOList, recordDate);
            facilityOeeRepository.saveAll(facilityOeeList);
        });
    }

    /**
     * 获取待新增OEE对象集合（生产线）
     *
     * @param oeeCategoryKey       OEE类别（0:工位OEE，1:设备OEE）
     * @param workLineId           生产线ID
     * @param everyDayShiftDTOList 每日班次信息集合
     * @param recordDate           指定记录日期
     * @return : java.util.List<net.airuima.domain.procedure.oee.FacilityOee>
     * <AUTHOR>
     * @date 2022/12/7
     **/
    public List<FacilityOee> addFacilityOeeListByWorkLineId(Integer oeeCategoryKey, Long workLineId, List<EveryDayShiftDTO> everyDayShiftDTOList, LocalDate recordDate) {
        //2.1 获取实际工作时长
        Integer actualWorkDuration = everyDayShiftDTOList.stream().mapToInt(i -> DateUtils.getWorkTime(i.getActualStartTime(), i.getActualEndTime())).sum();
        //2.2 新增工位OEE
        List<FacilityOee> facilityOeeSaveList = new ArrayList<>();
        if (oeeCategoryKey == Constants.INT_ZERO) {
            //通过产线ID获取需要新增当天OEE记录的工位集合
            List<WorkCell> workCellList = facilityOeeRepository.findByWorkLineIdAndDeletedGroupByWorkCellId(workLineId, recordDate, Constants.LONG_ZERO);
            //组装OEE对象集合（工位）
            List<FacilityOee> facilityOeeListSaveList = getFacilityOeeListByWorkCell(workCellList, everyDayShiftDTOList, actualWorkDuration, recordDate);
            facilityOeeSaveList.addAll(facilityOeeListSaveList);
        } else if (oeeCategoryKey == Constants.INT_ONE) {
            //通过产线ID+记录时间+删除标识查询指定天中不存在设备OEE数据的工位设备信息
            List<WorkCellStepFacility> workCellStepFacilityList = workCellStepFacilityRepository.findByWorkLineIdGroupByWorkCellIdAndFacilityId(workLineId, Constants.LONG_ZERO);
            //组装OEE对象集合（工位_设备）
            List<FacilityOee> facilityOeeListSaveList = getFacilityOeeListByWorkCellAndFacility(workCellStepFacilityList, everyDayShiftDTOList, actualWorkDuration, recordDate);
            facilityOeeSaveList.addAll(facilityOeeListSaveList);
        }
        return facilityOeeSaveList;
    }

    public List<FacilityOee> addFacilityOeeBySingleFacility(Set<WorkCellStepFacility> workCellStepFacilityList, int targetStatus, Integer number, LocalDateTime recordDateTime) {
        Set<FacilityOee> facilityOeeSaveList = new HashSet<>();
        //1. OEE类别（0:工位OEE，1:设备OEE）
        Integer oeeCategoryKey = getOeeCategoryKey();
        //获取工作日历工作时间
        Map<String, LocalTime> workTimeMap = getWorkDayTime();
        LocalTime endTime = workTimeMap.get("end");
        //获取工作日历
        boolean existWorkStation = FuncKeyUtil.checkApi(FuncKeyConstants.WORK_STATION_FUNC_KEY);
        Set<Long> workStationOrWorkLineIds = new HashSet<>();
        workCellStepFacilityList.forEach(workCellStepFacility -> {
            if (existWorkStation && Objects.nonNull(workCellStepFacility.getWorkCell().getWorkStation())) {
                workStationOrWorkLineIds.add(workCellStepFacility.getWorkCell().getWorkStation().getId());
            } else if (!existWorkStation && Objects.nonNull(workCellStepFacility.getWorkCell().getWorkLine())) {
                workStationOrWorkLineIds.add(workCellStepFacility.getWorkCell().getWorkLine().getId());
            }
        });
        List<CalendarDTO> calendarDTOList;
        if (!existWorkStation) {
            calendarDTOList = oeeCalendarProxy.findByWorkLineIdInAndWorkTimeAndWorkStationIdIsNullAndDeleted(workStationOrWorkLineIds.stream().toList(), recordDateTime.toLocalDate(), Constants.LONG_ZERO);
        } else {
            calendarDTOList = oeeCalendarProxy.findByWorkStationIdInAndWorkLineIdNullAndWorkTimeAndDeleted(workStationOrWorkLineIds.stream().toList(), recordDateTime.toLocalDate(), Constants.LONG_ZERO);
        }
        Map<Long, CalendarDTO> calendarDTOGroup = CollectionUtils.isNotEmpty(calendarDTOList) ? existWorkStation ? calendarDTOList.stream().collect(Collectors.toMap(CalendarDTO::getWorkStationId, Function.identity())) : calendarDTOList.stream().collect(Collectors.toMap(CalendarDTO::getWorkLineId, Function.identity())) : new HashMap<>();
        if (!ValidateUtils.isValid(calendarDTOGroup)) {
            return null;
        }
        workCellStepFacilityList.forEach(workCellStepFacility -> {
            WorkCell workCell = workCellStepFacility.getWorkCell();
            List<EveryDayShiftDTO> everyDayShiftDTOList = null;
            if (existWorkStation && Objects.nonNull(workCell.getWorkStation()) && calendarDTOGroup.containsKey(workCell.getWorkStation().getId())) {
                CalendarDTO calendarDTO = calendarDTOGroup.get(workCell.getWorkStation().getId());
                everyDayShiftDTOList = calendarDTO.getShiftJsonList().stream().sorted(Comparator.comparing(i -> DateUtils.getWorkTime(i.getActualEndTime(), endTime))).collect(Collectors.toList());

            } else if (!existWorkStation && Objects.nonNull(workCell.getWorkLine()) && calendarDTOGroup.containsKey(workCell.getWorkLine().getId())) {
                CalendarDTO calendarDTO = calendarDTOGroup.get(workCell.getWorkLine().getId());
                everyDayShiftDTOList = calendarDTO.getShiftJsonList().stream().sorted(Comparator.comparing(i -> DateUtils.getWorkTime(i.getActualEndTime(), endTime))).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(everyDayShiftDTOList)) {
                return;
            }
            Integer actualWorkDuration = everyDayShiftDTOList.stream().mapToInt(i -> DateUtils.getWorkTime(i.getActualStartTime(), i.getActualEndTime())).sum();
            if (oeeCategoryKey == Constants.INT_ZERO) {
                //组装OEE对象集合（工位）
                facilityOeeSaveList.addAll(getFacilityOeeListByWorkCell(Collections.singletonList(workCell), everyDayShiftDTOList, actualWorkDuration, recordDateTime.toLocalDate()));
            } else {
                facilityOeeSaveList.addAll(getFacilityOeeListByWorkCellAndFacility(Collections.singletonList(workCellStepFacility), everyDayShiftDTOList, actualWorkDuration, recordDateTime.toLocalDate()));
            }
        });
        return CollectionUtils.isEmpty(facilityOeeSaveList) ? null : facilityOeeSaveList.stream().toList();
    }

    /**
     * 组装OEE对象集合（工位）
     *
     * @param workCellList         待处理工位数据集合
     * @param everyDayShiftDTOList 每日班次信息集合
     * @param facilityOeeMap       Map<工位ID_[设备ID], 设备运行状态记录>
     * @param actualWorkDuration   实际工作时长
     * @return : java.util.List<net.airuima.domain.procedure.oee.FacilityOee>
     * <AUTHOR>
     * @date 2022/12/7
     **/
    public List<FacilityOee> getFacilityOeeListByWorkCell(List<WorkCell> workCellList, List<EveryDayShiftDTO> everyDayShiftDTOList,
                                                          Integer actualWorkDuration, LocalDate recordDate) {
        //获取工作日历工作时间
        Map<String, LocalTime> workTimeMap = getWorkDayTime();
        LocalTime endTime = workTimeMap.get("end");

        List<FacilityOee> facilityOeeSaveList = new ArrayList<>();
        for (WorkCell workCell : workCellList) {
            //异常停机开始时间
            LocalDateTime abnormalStatusStartTime = null;
            //获取最新异常停机开始时间不为空的OEE
            FacilityOee facilityOeeByAbnormalStatus = this.getFacilityOeeByAbnormalStatus(workCell.getId(), null);
            //如果不为空
            if (!ObjectUtils.isEmpty(facilityOeeByAbnormalStatus)) {
                // 通过实际结束时间和当日工作结束时间之间的距离进行正序排序，第一个元素为最晚时间段，最后一个元素为最早时间段
                everyDayShiftDTOList = everyDayShiftDTOList.stream().sorted(Comparator.comparing(i -> DateUtils.getWorkTime(i.getActualEndTime(), endTime))).collect(Collectors.toList());
                abnormalStatusStartTime = recordDate.atTime(everyDayShiftDTOList.get(everyDayShiftDTOList.size() - 1).getActualStartTime());
                //将旧OEE异常停机开始时间置为空
                facilityOeeByAbnormalStatus.setAbnormalStatusStartTime(null);
                facilityOeeRepository.save(facilityOeeByAbnormalStatus);
            }
            //获取昨日OEE状态
            String key = String.valueOf(workCell.getId());
            //组装OEE对象
            FacilityOee facilityOee = getFacilityOee(workCell, null, actualWorkDuration, recordDate, abnormalStatusStartTime);
            facilityOeeSaveList.add(facilityOee);
        }
        return facilityOeeSaveList;
    }

    /**
     * 组装OEE对象集合（工位_设备）
     *
     * @param workCellStepFacilityList 待处理工位工序设备数据集合
     * @param everyDayShiftDTOList     每日班次信息集合
     * @param facilityOeeMap           Map<工位ID_[设备ID], 设备运行状态记录>
     * @param actualWorkDuration       实际工作时长
     * @return : java.util.List<net.airuima.domain.procedure.oee.FacilityOee>
     * <AUTHOR>
     * @date 2022/12/7
     **/
    public List<FacilityOee> getFacilityOeeListByWorkCellAndFacility(List<WorkCellStepFacility> workCellStepFacilityList, List<EveryDayShiftDTO> everyDayShiftDTOList,
                                                                     Integer actualWorkDuration, LocalDate recordDate) {
        //获取工作日历工作时间
        Map<String, LocalTime> workTimeMap = getWorkDayTime();
        LocalTime endTime = workTimeMap.get("end");

        List<FacilityOee> facilityOeeSaveList = new ArrayList<>();
        for (WorkCellStepFacility workCellStepFacility : workCellStepFacilityList) {
            //异常停机开始时间
            LocalDateTime abnormalStatusStartTime = null;
            //获取最新异常停机开始时间不为空的OEE
            FacilityOee facilityOeeByAbnormalStatus = this.getFacilityOeeByAbnormalStatus(workCellStepFacility.getWorkCell().getId(), workCellStepFacility.getFacilityId());
            //如果不为空
            if (!ObjectUtils.isEmpty(facilityOeeByAbnormalStatus)) {
                // 通过实际结束时间和当日工作结束时间之间的距离进行正序排序，第一个元素为最晚时间段，最后一个元素为最早时间段
                everyDayShiftDTOList = everyDayShiftDTOList.stream().sorted(Comparator.comparing(i -> DateUtils.getWorkTime(i.getActualEndTime(), endTime))).collect(Collectors.toList());
                abnormalStatusStartTime = recordDate.atTime(everyDayShiftDTOList.get(everyDayShiftDTOList.size() - 1).getActualStartTime());
                //将旧OEE异常停机开始时间置为空
                facilityOeeByAbnormalStatus.setAbnormalStatusStartTime(null);
                facilityOeeRepository.save(facilityOeeByAbnormalStatus);
            }
            //获取昨日OEE状态
            // 组装OEE对象
            FacilityOee facilityOee = getFacilityOee(workCellStepFacility.getWorkCell(), workCellStepFacility.getFacilityId(), actualWorkDuration, recordDate, abnormalStatusStartTime);
            facilityOeeSaveList.add(facilityOee);
        }
        return facilityOeeSaveList;
    }

    /**
     * 新增/更新当天指定工站/产线下设备或工位数据
     *
     * @param facilityOeeCreateDTO 设备OEE创建DTO
     * @return : void
     * <AUTHOR>
     * @date 2022/12/6
     **/
    public void updateByWorkStationOrWorkLine(FacilityOeeCreateDTO facilityOeeCreateDTO, LocalDate recordDate) {
        //需要生成OEE的工站/产线ID及对应每日班次信息集合，Map<工站ID/产线ID，List<每日班次信息>>
        Map<Long, List<EveryDayShiftDTO>> workStationIdAndWorkTime = facilityOeeCreateDTO.getWorkIdAndWorkTime();

        //1. OEE类别（0:工位OEE，1:设备OEE）
        Integer oeeCategoryKey = getOeeCategoryKey();
        //1. 获取工作日历工作时间
        Map<String, LocalTime> workTimeMap = getWorkDayTime();
        LocalTime endTime = workTimeMap.get("end");
        //2. 遍历工站/产线信息集合
        workStationIdAndWorkTime.forEach((workId, everyDayShiftDTOList) -> {
            //工站/产线ID
            //每日班次信息集合
            //实际工作时长
            Integer actualWorkDuration = everyDayShiftDTOList.stream().mapToInt(i -> DateUtils.getWorkTime(i.getActualStartTime(), i.getActualEndTime())).sum();
            //2.1 获取新增OEE对象集合（工站/产线）
            List<FacilityOee> facilityOeeList = addFacilityOeeListByWorkStationId(oeeCategoryKey, workId, actualWorkDuration, everyDayShiftDTOList, recordDate);
            List<FacilityOee> facilityOeeSaveList = new ArrayList<>(facilityOeeList);
            //2.2 获取更新OEE对象集合（之前已存在的）
            List<FacilityOee> facilityOeeListExist = updateFacilityOeeListByWorkStationId(oeeCategoryKey, workId, actualWorkDuration, everyDayShiftDTOList, recordDate, endTime);
            facilityOeeSaveList.addAll(facilityOeeListExist);

            facilityOeeRepository.saveAll(facilityOeeSaveList);
        });
    }

    /**
     * 将前一天设备数据直接赋值到当前天，状态继承，其它数据为0
     *
     * @param recordDate 记录日期
     * @return : void
     * <AUTHOR>
     * @date 2022/12/6
     **/
    public void addByNoCalendar(Long workId, LocalDate recordDate) {
        Integer oeeCategoryKey = getOeeCategoryKey();
        List<FacilityOee> facilityOeeList = new ArrayList<>();
        boolean existWorkStation = FuncKeyUtil.checkApi(FuncKeyConstants.WORK_STATION_FUNC_KEY);
        //1. OEE类别（0:工位OEE，1:设备OEE）
        if (oeeCategoryKey == Constants.INT_ZERO) {
            //2.2.1 通过工站ID+记录时间+删除标识查询指定天中不存在设备OEE数据的工位信息
            List<WorkCell> workCellList = existWorkStation ? facilityOeeRepository.findByWorkStationIdAndDeletedGroupByWorkCellId(workId, recordDate, Constants.LONG_ZERO)
                    : facilityOeeRepository.findByWorkLineIdAndDeletedGroupByWorkCellId(workId, recordDate, Constants.LONG_ZERO);
            for (WorkCell workCell : workCellList) {
                //获取前一天设备OEE
                List<FacilityOee> facilityOeeDB = facilityOeeRepository.findByWorkCellIdAndFacilityIdIsNullAndRecordDateAndDeleted(workCell.getId(), recordDate.minusDays(1), Constants.LONG_ZERO);
                FacilityOee facilityOeeSub = CollectionUtils.isEmpty(facilityOeeDB) ? null : facilityOeeDB.get(Constants.INT_ZERO);
                //组装OEE对象集合（工位_设备）
                FacilityOee facilityOee = getZeroFacilityOee(workCell, null, facilityOeeSub, recordDate);
                facilityOeeList.add(facilityOee);
            }
        } else if (oeeCategoryKey == Constants.INT_ONE) {
            //2.2.1 通过工站ID+记录时间+删除标识查询指定天中不存在设备OEE数据的工位设备信息
            List<WorkCellStepFacility> workCellStepFacilityList = existWorkStation ? facilityOeeRepository.findByWorkStationIdAndDeletedGroupByWorkCellIdAndFacilityId(workId, recordDate, Constants.LONG_ZERO)
                    : facilityOeeRepository.findByWorkLineIdAndDeletedGroupByWorkCellIdAndFacilityId(workId, recordDate, Constants.LONG_ZERO);
            for (WorkCellStepFacility workCellStepFacility : workCellStepFacilityList) {
                //获取前一天设备OEE
                List<FacilityOee> facilityOeeDB = facilityOeeRepository.findByWorkCellIdAndFacilityIdInAndRecordDateAndDeleted(workCellStepFacility.getWorkCell().getId(), Arrays.asList(workCellStepFacility.getFacilityId()), recordDate.minusDays(1), Constants.LONG_ZERO);
                FacilityOee facilityOeeSub = CollectionUtils.isEmpty(facilityOeeDB) ? null : facilityOeeDB.get(Constants.INT_ZERO);
                //组装OEE对象集合（工位_设备）
                FacilityOee facilityOee = getZeroFacilityOee(workCellStepFacility.getWorkCell(), workCellStepFacility.getFacilityId(), facilityOeeSub, recordDate);
                facilityOeeList.add(facilityOee);
            }
        }
        facilityOeeRepository.saveAll(facilityOeeList);
    }

    /**
     * 组合数据为0的设备OEE
     *
     * @param workCell       工位
     * @param facilityId     设备ID
     * @param facilityOeeSub 设备OEE
     * @param recordDate     记录日期
     * @return : net.airuima.domain.procedure.oee.FacilityOee
     * <AUTHOR>
     * @date 2023/4/26
     **/
    public FacilityOee getZeroFacilityOee(WorkCell workCell, Long facilityId, FacilityOee facilityOeeSub, LocalDate recordDate) {
        //组装OEE对象集合（工位）
        FacilityOee facilityOee = new FacilityOee().setWorkCell(workCell)
                .setActualWorkDuration(Constants.INT_ZERO)
                .setPlanDownDuration(Constants.INT_ZERO)
                .setDownLineDuration(Constants.INT_ZERO)
                .setRecordDate(recordDate)
                .setAbnormalStatusStartTime(null)
                //理论运行时长(理论节拍时间×实际完成数量)(s)
                .setIdealWorkDuration(Constants.INT_ZERO)
                //负荷时间(实际作业时间-计划停止时间)(s)
                .setLoadDuration(Constants.INT_ZERO)
                //稼动时间(负荷时间-停线时间)(s)
                .setActivationDuration(Constants.INT_ZERO);
        if (!ObjectUtils.isEmpty(facilityId)) {
            facilityOee.setFacilityId(facilityId);
        }
        return facilityOee;
    }

    /**
     * 获取新增OEE对象集合（工站）
     *
     * @param oeeCategoryKey       OEE类别（0:工位OEE，1:设备OEE）
     * @param actualWorkDuration   实际工作时长
     * @param everyDayShiftDTOList 每日班次信息集合
     * @return : java.util.List<net.airuima.domain.procedure.oee.FacilityOee>
     * <AUTHOR>
     * @date 2022/12/7
     **/
    public List<FacilityOee> addFacilityOeeListByWorkStationId(Integer oeeCategoryKey, Long workId, Integer actualWorkDuration, List<EveryDayShiftDTO> everyDayShiftDTOList, LocalDate recordDate) {
        List<FacilityOee> facilityOeeSaveList = new ArrayList<>();
        boolean existWorkStation = FuncKeyUtil.checkApi(FuncKeyConstants.WORK_STATION_FUNC_KEY);
        //根据工站ID或者产线ID+记录日期(前一天)+删除标识 查询设备运行状态
        List<FacilityOee> facilityOeeList = existWorkStation ? facilityOeeRepository.findByWorkStationIdAndRecordDateAndDeleted(workId, recordDate.minusDays(Constants.INT_ONE), Constants.LONG_ZERO) : facilityOeeRepository.findByWorkLineIdAndRecordDateAndDeleted(workId, recordDate.minusDays(Constants.INT_ONE), Constants.LONG_ZERO);
        //Map<工位ID_[设备ID], 设备运行状态记录>
        Map<String, FacilityOee> facilityOeeMap = facilityOeeList.stream().collect(Collectors.toMap(item -> item.getWorkCell().getId() + (ObjectUtils.isEmpty(item.getFacilityId()) ? "" : "_" + item.getFacilityId()), item -> item));
        //2.2 新增/编辑工位OEE
        if (oeeCategoryKey == Constants.INT_ZERO) {
            //2.2.1 通过工站ID+记录时间+删除标识查询指定天中不存在设备OEE数据的工位信息
            List<WorkCell> workCellList = existWorkStation ? facilityOeeRepository.findByWorkStationIdAndDeletedGroupByWorkCellId(workId, recordDate, Constants.LONG_ZERO) : facilityOeeRepository.findByWorkLineIdAndDeletedGroupByWorkCellId(workId, recordDate, Constants.LONG_ZERO);
            //组装OEE对象集合（工位）
            List<FacilityOee> facilityOeeListSaveList = getFacilityOeeListByWorkCell(workCellList, everyDayShiftDTOList, actualWorkDuration, recordDate);
            facilityOeeSaveList.addAll(facilityOeeListSaveList);
        } else if (oeeCategoryKey == Constants.INT_ONE) {
            //2.2.1 通过工站ID/或者产线ID+记录时间+删除标识查询指定天中不存在设备OEE数据的工位设备信息
            List<WorkCellStepFacility> workCellStepFacilityList = existWorkStation ? workCellStepFacilityRepository.findByWorkStationIdGroupByWorkCellIdAndFacilityId(workId, Constants.LONG_ZERO) : workCellStepFacilityRepository.findByWorkLineIdGroupByWorkCellIdAndFacilityId(workId, Constants.LONG_ZERO);
            Map<Long, Integer> facilityStatusGroupMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(facilityOeeList)) {
                List<Long> facilityIdList = facilityOeeList.stream().map(FacilityOee::getFacilityId).filter(Objects::nonNull).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(facilityIdList)) {
                    FacilityStatusChangeRequestDTO facilityStatusChangeRequestDTO = new FacilityStatusChangeRequestDTO();
                    facilityStatusChangeRequestDTO.setRecordDate(recordDate).setFacilityIds(facilityIdList).setLatest(Boolean.TRUE);
                    List<FacilityStatusChangeDTO> facilityStatusChangeDTOS = rbaseFacilityProxy.findByConditions(facilityStatusChangeRequestDTO);
                    if (CollectionUtils.isNotEmpty(facilityStatusChangeDTOS)) {
                        facilityStatusChangeDTOS.forEach(facilityStatusChangeDTO -> {
                            facilityStatusGroupMap.put(facilityStatusChangeDTO.getFacilityId(), facilityStatusChangeDTO.getTargetStatus());
                        });
                    }
                }
            }
            //组装OEE对象集合（工位_设备）
            List<FacilityOee> facilityOeeListSaveList = getFacilityOeeListByWorkCellAndFacility(workCellStepFacilityList, everyDayShiftDTOList, actualWorkDuration, recordDate);
            facilityOeeSaveList.addAll(facilityOeeListSaveList);
        }
        return facilityOeeSaveList;
    }

    /**
     * 获取更新OEE对象集合（之前已存在的）
     *
     * @param workId             工站ID/产线ID
     * @param oeeCategoryKey     OEE类别（0:工位OEE，1:设备OEE）
     * @param actualWorkDuration 实际工作时长
     * @param endTime            当日最终工作时间
     * @return : java.util.List<net.airuima.domain.procedure.oee.FacilityOee>
     * <AUTHOR>
     * @date 2022/12/7
     **/
    public List<FacilityOee> updateFacilityOeeListByWorkStationId(Integer oeeCategoryKey, Long workId, Integer actualWorkDuration, List<EveryDayShiftDTO> everyDayShiftDTOList, LocalDate recordDate, LocalTime endTime) {
        List<FacilityOee> facilityOeeSaveList = new ArrayList<>();
        boolean existWorkStation = FuncKeyUtil.checkApi(FuncKeyConstants.WORK_STATION_FUNC_KEY);
        //根据工站ID+记录日期+删除标识 查询设备运行状态
        List<FacilityOee> facilityOeeList = existWorkStation ? facilityOeeRepository.findByWorkStationIdAndRecordDateAndDeleted(workId, recordDate, Constants.LONG_ZERO) : facilityOeeRepository.findByWorkLineIdAndRecordDateAndDeleted(workId, recordDate, Constants.LONG_ZERO);
        List<Long> facilityOeeIdList = facilityOeeList.stream().map(FacilityOee::getId).collect(Collectors.toList());
        //获取计划停止时间
        List<FacilityPlanDownHistory> facilityPlanDownHistoryList = facilityPlanDownHistoryRepository.findByFacilityOeeIdInAndDeleted(facilityOeeIdList, Constants.LONG_ZERO);

        for (FacilityOee facilityOee : facilityOeeList) {
            List<List<LocalTime>> dayShiftTimeList = everyDayShiftDTOList.stream().map(i -> Arrays.asList(i.getActualStartTime(), i.getActualEndTime())).collect(Collectors.toList());
            //重新计算计划停止时间
            Integer planDownDuration = reloadPlanDownDuration(facilityPlanDownHistoryList, facilityOee.getId(), dayShiftTimeList);
            //重新计算停线时间
            //获取停线时间
            Integer downLineDuration = Constants.INT_ZERO;
            //异常停机开始时间
            LocalDateTime abnormalStatusStartTime = null;
            //获取最新异常停机开始时间不为空的OEE
            FacilityOee facilityOeeByAbnormalStatus = this.getFacilityOeeByAbnormalStatus(facilityOee.getWorkCell().getId(), facilityOee.getFacilityId());
            //如果不为空
            if (!ObjectUtils.isEmpty(facilityOeeByAbnormalStatus)) {
                //重新计算停线时间
                downLineDuration = this.reloadDownLineDuration(facilityOeeByAbnormalStatus, everyDayShiftDTOList, recordDate, endTime, Boolean.FALSE);
                //将当前时间设置为新OEE异常停机开始时间
                abnormalStatusStartTime = LocalDateTime.now();
                //将旧OEE异常停机开始时间置为空
                facilityOeeByAbnormalStatus.setAbnormalStatusStartTime(null);
                facilityOeeRepository.save(facilityOeeByAbnormalStatus);
            }

            //实际工作时长
            facilityOee.setActualWorkDuration(actualWorkDuration)
                    .setPlanDownDuration(planDownDuration)
                    .setDownLineDuration(downLineDuration)
                    .setAbnormalStatusStartTime(abnormalStatusStartTime)
                    //负荷时间(实际作业时间-计划停止时间)(s)
                    .setLoadDuration(actualWorkDuration - facilityOee.getPlanDownDuration())
                    //稼动时间(负荷时间-停线时间)(s)
                    .setActivationDuration(facilityOee.getLoadDuration() - facilityOee.getDownLineDuration());
            facilityOeeSaveList.add(facilityOee);
        }
        return facilityOeeSaveList;
    }

    /**
     * 重新计算计划停止时间
     *
     * @param facilityPlanDownHistoryList 计划停止历史表集合
     * @param facilityOeeId               OEEID
     * @param dayShiftTimeList            每日班次信息集合List<List<开始时间,结束时间>>
     * @return : void
     * <AUTHOR>
     * @date 2022/12/8
     **/
    public Integer reloadPlanDownDuration(List<FacilityPlanDownHistory> facilityPlanDownHistoryList, Long facilityOeeId, List<List<LocalTime>> dayShiftTimeList) {
        Map<Long, List<FacilityPlanDownHistory>> facilityPlanDownHistoryMap = facilityPlanDownHistoryList.stream().collect(Collectors.groupingBy(i -> i.getFacilityOee().getId()));
        //1. 获取当前oee计划停止时间段集合 及 日历排班工作时间段集合
        List<FacilityPlanDownHistory> facilityPlanDownHistoryListByOee = facilityPlanDownHistoryMap.get(facilityOeeId);
        List<List<LocalTime>> planDownTimeList = CollectionUtils.isEmpty(facilityPlanDownHistoryListByOee) ? new ArrayList<>() : facilityPlanDownHistoryListByOee.stream().map(i -> Arrays.asList(i.getStartTime(), i.getEndTime())).collect(Collectors.toList());
        //2. 重新计算计划停止时间
        return DateUtils.timeOfCrossingByWorkTimeList(planDownTimeList, dayShiftTimeList);
    }


    /**
     * 组装OEE对象
     *
     * @param status             状态
     * @param workCell           工位
     * @param facilityId         设备ID
     * @param actualWorkDuration 日实际作业时长(s)
     * @param downLineDuration   日停线时长(s)
     * @return : net.airuima.domain.procedure.oee.FacilityOee
     * <AUTHOR>
     * @date 2022/12/7
     **/
    public FacilityOee getFacilityOee(WorkCell workCell, Long facilityId, Integer actualWorkDuration, LocalDate recordDate, LocalDateTime abnormalStatusStartTime) {
        FacilityOee facilityOee = facilityOeeRepository.findByWorkCellIdAndFacilityIdAndRecordDateAndDeleted(workCell.getId(), facilityId, recordDate, Constants.LONG_ZERO).orElse(new FacilityOee());
        FacilityCadence facilityCadence = facilityCadenceRepository.findByWorkCellIdAndFacilityIdAndDeleted(workCell.getId(), facilityId, Constants.LONG_ZERO);
        if (Objects.nonNull(facilityCadence)) {
            int theoryRunTime = facilityCadence.getUnit() == Constants.INT_ZERO ? facilityCadence.getDuration().intValue() : facilityCadence.getUnit() == Constants.INT_ONE ? (facilityCadence.getDuration()).intValue() * 60 : facilityCadence.getDuration().intValue() * 3600;
            facilityOee.setIdealWorkDuration(facilityOee.getFinishNumber() * theoryRunTime);
        }
        return facilityOee.setWorkCell(workCell).setFacilityId(facilityId)
                .setActualWorkDuration(actualWorkDuration)
                .setRecordDate(recordDate)
                .setAbnormalStatusStartTime(abnormalStatusStartTime)
                //负荷时间(实际作业时间-计划停止时间)(s)
                .setLoadDuration(actualWorkDuration - facilityOee.getPlanDownDuration())
                //稼动时间(负荷时间-停线时间)(s)
                .setActivationDuration(actualWorkDuration - facilityOee.getDownLineDuration());
    }

    /**
     * 获取OEE类别，0:工位OEE(false)，1:设备OEE(true)
     *
     * @return : java.lang.Integer
     * <AUTHOR>
     * @date 2022/12/6
     **/
    public Integer getOeeCategoryKey() {
        DictionaryDTO dictionaryDTO = dictionaryProxy.findByCodeAndDeleted(Constants.KEY_OEE_CATEGORY,Constants.LONG_ZERO).orElse(null);
        String oeeCategoryStr = Objects.isNull(dictionaryDTO) ? null:dictionaryDTO.getData();
        //没有oee类别配置默认就为工位oee
        if (StringUtils.isBlank(oeeCategoryStr)) {
            return Constants.INT_ZERO;
        }
        //存在配置且为false是工位oee，true 则为设备oee
        boolean oeeCategory = Boolean.parseBoolean(oeeCategoryStr);
        if (oeeCategory) {
            return Constants.INT_ONE;
        }
        return Constants.INT_ZERO;
    }

    /**
     * 获取工作日历工作时间配置
     *
     * @return : java.util.Map<java.lang.String,java.time.LocalTime>
     * <AUTHOR>
     * @date 2022/12/23
     **/
    public Map<String, LocalTime> getWorkDayTime() {
        String result = rbaseSysCodeProxy.findByCode(Constants.KEY_WORK_DAY_TIME);
        if (StringUtils.isBlank(result)) {
            throw new ResponseException("error.keyWorkDayTimeDictionaryNotFond", "未配置工作日历工作时间");
        }
        List<Map<String, String>> resultMaps = JSON.parseObject(result, new TypeReference<List<Map<String, String>>>() {
        });
        Map<String, LocalTime> workDayTimeMap = new HashMap<>();
        resultMaps.forEach(resultMap -> {
            workDayTimeMap.put(resultMap.get("key"), LocalTime.parse(resultMap.get("value")));
        });
        return workDayTimeMap;
    }

    /**
     * 每天00:05, 更新昨天设备或工位OEE，新增新一天设备或者工位OEE，如“日停线时长”、“稼动时间”
     * - 对于24:00之前结束的实际作业时间，直接结算当天“日停线时长”、“稼动时间”，【并新增新一天设备或工位OEE】
     * - 对于24:00之后（如02:00）结束的实际作用时间，通过延迟队列，实现结算当天“日停线时长”、“稼动时间”，【并新增新一天设备或工位OEE】
     *
     * <AUTHOR>
     * @date 2023/4/25
     **/
    public void updateAndSave() {
        LocalDate recordDateToday = LocalDate.now();
        LocalDate recordDateYesterday = recordDateToday.minusDays(1);
        List<CalendarDTO> calendarDTOList = oeeCalendarProxy.findByWorkTimeTomorrow(recordDateToday.toString());
        List<CalendarDTO> calendarDTOListYesterday = oeeCalendarProxy.findByWorkTimeTomorrow(recordDateYesterday.toString());
        boolean existWorkStation = FuncKeyUtil.checkApi(FuncKeyConstants.WORK_STATION_FUNC_KEY);
        //1. 判断昨天是否生成OEE，未生成则直接生成（第一次初始化用） || 昨天未设置日历+已生成OEE，今天已设置日历，则直接生成(需要获取最近一个日历)
        List<FacilityOee> facilityOeeListHasYesterday = facilityOeeRepository.findByRecordDateAndDeleted(recordDateYesterday, Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(facilityOeeListHasYesterday) || (CollectionUtils.isEmpty(calendarDTOListYesterday) && !CollectionUtils.isEmpty(facilityOeeListHasYesterday) && !CollectionUtils.isEmpty(calendarDTOList))) {
            //工站功能key ? Map<工站ID，List<每日班次信息>>: Map<产线ID，List<每日班次信息>>
            Map<Long, List<EveryDayShiftDTO>> paramHaveData = calendarDTOList.stream().filter(i -> !i.getShiftJsonList().isEmpty()).collect(Collectors.toMap(existWorkStation ? CalendarDTO::getWorkStationId : CalendarDTO::getWorkLineId, CalendarDTO::getShiftJsonList,(v1,v2)->v1));
            FacilityOeeCreateDTO facilityOeeCreateDTO = new FacilityOeeCreateDTO();
            facilityOeeCreateDTO.setWorkIdAndWorkTime(paramHaveData);
            this.updateByWorkStationOrWorkLine(facilityOeeCreateDTO, recordDateToday);
            return;
        }

        //2. 更新昨天已结束的设备OEE
        //获取工作日历工作时间
        Map<String, LocalTime> workTimeMap = this.getWorkDayTime();
        LocalTime startTime = workTimeMap.get("start");
        LocalTime endTime = workTimeMap.get("end");
        //获取昨天工作日历集合
        List<Long> workIdList = calendarDTOListYesterday.stream().map(existWorkStation ? CalendarDTO::getWorkStationId : CalendarDTO::getWorkLineId).collect(Collectors.toList());
        //获取00:00之前结束的工作日历对应的工站ID集合
        List<Long> workIdListYesterday = calendarDTOListYesterday.stream().filter(i -> {
            LocalTime actualEndTime = getLastTime(i.getShiftJsonList(), endTime).getActualEndTime();
            //如果工作日历跨天，则获取 不在 0:0:0到endTime 之前的数据，如果不跨天，则均满足
            return endTime.isBefore(startTime) ? !(LocalTime.of(Constants.INT_ZERO, Constants.INT_ZERO, Constants.INT_ZERO).isBefore(actualEndTime) && actualEndTime.isBefore(endTime)) : Boolean.TRUE;
        }).map(existWorkStation ? CalendarDTO::getWorkStationId : CalendarDTO::getWorkLineId).distinct().collect(Collectors.toList());
        //获取设备OEE（状态为异常停机）
        List<FacilityOee> facilityOeeListYesterday = existWorkStation ? facilityOeeRepository.findByWorkCellWorkStationIdInAndRecordDateAndDeleted(workIdListYesterday, recordDateYesterday, Constants.LONG_ZERO)
                : facilityOeeRepository.findByWorkCellWorkLineIdInAndRecordDateAndDeleted(workIdListYesterday, recordDateYesterday, Constants.LONG_ZERO);
        Map<Long, Boolean> facilityStopGroup = new HashMap<>();
        if (CollectionUtils.isNotEmpty(facilityOeeListYesterday)) {
            FacilityStatusChangeRequestDTO facilityStatusChangeRequestDTO = new FacilityStatusChangeRequestDTO();
            facilityStatusChangeRequestDTO.setFacilityIds(facilityOeeListYesterday.stream().map(FacilityOee::getFacilityId).toList()).setRecordDate(recordDateYesterday).setTargetStatus(Constants.INT_THREE).setLatest(Boolean.TRUE);
            List<FacilityStatusChangeDTO> facilityStatusChangeDTOS = rbaseFacilityProxy.findByConditions(facilityStatusChangeRequestDTO);
            if (CollectionUtils.isNotEmpty(facilityStatusChangeDTOS)) {
                facilityStatusChangeDTOS.forEach(facilityStatusChangeDTO -> {
                    facilityStopGroup.put(facilityStatusChangeDTO.getFacilityId(), Boolean.TRUE);
                });
            }
            for (FacilityOee facilityOee : facilityOeeListYesterday) {
                if (facilityStopGroup.containsKey(facilityOee.getFacilityId())) {
                    //工站指定日历
                    CalendarDTO calendarDTO = existWorkStation && Objects.nonNull(facilityOee.getWorkCell().getWorkStation()) ? oeeCalendarProxy.findTop1ByWorkStationIdAndWorkLineIdNullAndWorkTimeAndDeleted(facilityOee.getWorkCell().getWorkStation().getId(), recordDateYesterday, Constants.LONG_ZERO)
                            : oeeCalendarProxy.findTop1ByWorkLineIdAndWorkTimeAndWorkStationIdIsNullAndDeleted(facilityOee.getWorkCell().getWorkLine().getId(), recordDateYesterday, Constants.LONG_ZERO);
                    //重新计算停线时间
                    Integer downLineDuration = this.calculationDownLineDuration(facilityOee.getWorkCell().getId(), facilityOee.getFacilityId(), calendarDTO.getShiftJsonList(), facilityOee.getRecordDate(), endTime);
                    facilityOee.setDownLineDuration(facilityOee.getDownLineDuration() + downLineDuration)
                            //稼动时间(负荷时间-停线时间)(s)
                            .setActivationDuration(facilityOee.getLoadDuration() - facilityOee.getDownLineDuration());
                }
                //更新理论运行时长
                FacilityCadence facilityCadence = facilityCadenceRepository.findByWorkCellIdAndFacilityIdAndDeleted(facilityOee.getWorkCell().getId(), facilityOee.getFacilityId(), Constants.LONG_ZERO);
                if (Objects.nonNull(facilityCadence)) {
                    int theoryRunTime = facilityCadence.getUnit() == Constants.INT_ZERO ? facilityCadence.getDuration().intValue() : facilityCadence.getUnit() == Constants.INT_ONE ? (facilityCadence.getDuration()).intValue() * 60 : facilityCadence.getDuration().intValue() * 3600;
                    facilityOee.setIdealWorkDuration(facilityOee.getFinishNumber() * theoryRunTime);
                }
                facilityOeeRepository.saveAll(facilityOeeListYesterday);
            }
        }


        //3. 新增昨天已结束的设备OEE
        List<Long> workStationIdList = calendarDTOList.stream().map(existWorkStation ? CalendarDTO::getWorkStationId : CalendarDTO::getWorkLineId).collect(Collectors.toList());
        //需新增有数据的工站ID
        List<Long> workStationIdListYesterdayRetain = new ArrayList<Long>(workIdListYesterday);
        workStationIdListYesterdayRetain.retainAll(workStationIdList);
        //需新增无数据的工站ID
        List<Long> workStationIdListYesterdayRemove = new ArrayList<Long>(workIdListYesterday);
        workStationIdListYesterdayRemove.removeAll(workStationIdList);
        //新增有数据OEE
        if (!workStationIdListYesterdayRetain.isEmpty()) {
            FacilityOeeCreateDTO facilityOeeCreateDTO = new FacilityOeeCreateDTO();
            //Map<工站ID，List<每日班次信息>>
            Map<Long, List<EveryDayShiftDTO>> param = calendarDTOList.stream().filter(i -> workStationIdListYesterdayRetain.contains(existWorkStation ? i.getWorkStationId() : i.getWorkLineId()) && !i.getShiftJsonList().isEmpty()).collect(Collectors.toMap(item -> existWorkStation ? item.getWorkStationId() : item.getWorkLineId(), item -> item.getShiftJsonList()));
            facilityOeeCreateDTO.setWorkIdAndWorkTime(param);
            this.updateByWorkStationOrWorkLine(facilityOeeCreateDTO, recordDateToday);
        }
        //新增无数据OEE，昨天有日历，今天无日历，则全部为0
        if (!workStationIdListYesterdayRemove.isEmpty()) {
            //没有日历，则将前一天设备数据直接赋值到当前天，状态继承，其它数据为0
            workStationIdListYesterdayRemove.forEach(i -> this.addByNoCalendar(i, recordDateToday));
        }

        //4. 获取00:00之后结束的工作日历（排除00:00之前的即可）
        List<CalendarDTO> calendarDTOListByMQ = calendarDTOListYesterday.stream().filter(i -> !workIdListYesterday.contains(existWorkStation ? i.getWorkStationId() : i.getWorkLineId())).collect(Collectors.toList());
        for (CalendarDTO calendarDTO : calendarDTOListByMQ) {
            //获取当日最终时间
            EveryDayShiftDTO everyDayShiftDTO = getLastTime(calendarDTO.getShiftJsonList(), endTime);
            //计算间隔时间
            int timeMillis = DateUtils.getWorkTime(LocalTime.now(), everyDayShiftDTO.getActualEndTime()) * 1000;
            //发送延迟队列
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("workStationId", existWorkStation ? calendarDTO.getWorkStationId() : calendarDTO.getWorkLineId());
            param.put("recordDate", recordDateToday);
            DelayedMessageDTO delayedMessageDTO = new DelayedMessageDTO();
            delayedMessageDTO.setId(null).setData(JSON.toJSONString(param)).setBusinessKey(FacilityOee.class.getSimpleName());
            rmesRabbitMqSender.send(JSON.toJSONString(delayedMessageDTO), timeMillis);
        }

        //5. 获取昨天所有OEE但是没有日历的数据
        List<FacilityOee> facilityOeeList = null;
        if (CollectionUtils.isEmpty(workIdList)) {
            facilityOeeList = existWorkStation ? facilityOeeRepository.findByWorkCellWorkStationIdNotInAndRecordDateAndDeleted(workIdList, recordDateYesterday, Constants.LONG_ZERO)
                    : facilityOeeRepository.findByWorkCellWorkLineIdNotInAndRecordDateAndDeleted(workIdList, recordDateYesterday, Constants.LONG_ZERO);
        } else {
            facilityOeeList = facilityOeeRepository.findByRecordDateAndDeleted(recordDateYesterday, Constants.LONG_ZERO);
        }
        List<Long> workStationIdNoneCalendarList = facilityOeeList.stream().map(i -> existWorkStation ? i.getWorkCell().getWorkStation().getId() : i.getWorkCell().getWorkLine().getId()).distinct().collect(Collectors.toList());
        if (!workStationIdNoneCalendarList.isEmpty()) {
            workStationIdNoneCalendarList.forEach(i -> this.addByNoCalendar(i, recordDateToday));
        }
    }

    /**
     * 获取OEE设备看板数据
     * @param organizationId
     * @param workLineId
     * @param facilityId
     * @param recordDate
     * @return
     */
    public FacilityStatusDashboardDTO facilityOeeDashboard(Long organizationId, Long workLineId, Long facilityId,List<Long> facilityIds, LocalDate recordDate) {
        if(CollectionUtils.isEmpty(facilityIds) && Objects.nonNull(facilityId)){
            facilityIds = Lists.newArrayList();
            facilityIds.add(facilityId);
        }
        facilityIds = CollectionUtils.isNotEmpty(facilityIds)?facilityIds:null;
        FacilityStatusDashboardDTO facilityStatusDashboardDTO = new FacilityStatusDashboardDTO();
        recordDate = Objects.nonNull(recordDate) ? recordDate : LocalDate.now();
        List<Long> workLineIdList = Lists.newArrayList();
        if (Objects.nonNull(workLineId)) {
            workLineIdList.add(workLineId);
        } else if (Objects.nonNull(organizationId)) {
            List<WorkLine> workLines = workLineRepository.findByOrganizationIdAndIsEnableAndDeleted(organizationId, Boolean.TRUE, Constants.LONG_ZERO);
            if (!CollectionUtils.isEmpty(workLines)) {
                workLineIdList.addAll(workLines.stream().map(WorkLine::getId).toList());
            }
        }
        workLineIdList = CollectionUtils.isNotEmpty(workLineIdList) ? workLineIdList : null;
        boolean existWorkStation = FuncKeyUtil.checkApi(FuncKeyConstants.WORK_STATION_FUNC_KEY);
        //获取符合查询条件的设备OEE
        List<FacilityOee> facilityOeeList = existWorkStation ? facilityOeeRepository.findByWorkLineIdAndRecordDateAndActualWorkDurationNotAndDeleted(workLineIdList, recordDate, facilityIds, Constants.INT_ZERO, Constants.LONG_ZERO)
                : facilityOeeRepository.findByWorkLineIdAndRecordDateAndActualWorkDurationNotAndDeletedWhenNoWorkStation(workLineIdList, recordDate, facilityIds, Constants.INT_ZERO, Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(facilityOeeList)) {
            this.updateAndSave();
            facilityOeeList = existWorkStation ? facilityOeeRepository.findByWorkLineIdAndRecordDateAndActualWorkDurationNotAndDeleted(workLineIdList, recordDate, facilityIds, Constants.INT_ZERO, Constants.LONG_ZERO)
                    : facilityOeeRepository.findByWorkLineIdAndRecordDateAndActualWorkDurationNotAndDeletedWhenNoWorkStation(workLineIdList, recordDate, facilityIds, Constants.INT_ZERO, Constants.LONG_ZERO);
            if (CollectionUtils.isEmpty(facilityOeeList)) {
                return facilityStatusDashboardDTO;
            }
        }
        //获取设备状态的MAP
        Map<Long, Integer> facilityStatusMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(facilityOeeList)) {
            List<FacilityLatestStatusDTO> facilityLatestStatusDTOS = rbaseFacilityProxy.findByFacilityIdInAndDeleted(facilityOeeList.stream().map(FacilityOee::getFacilityId).toList(), Constants.LONG_ZERO);
            if (CollectionUtils.isNotEmpty(facilityLatestStatusDTOS)) {
                facilityLatestStatusDTOS.stream().forEach(facilityLatestStatusDTO -> {
                    facilityStatusMap.put(facilityLatestStatusDTO.getFacility().getId(), facilityLatestStatusDTO.getLatestStatus());
                });
            }
        }
        //获取工作日历工作时间
        Map<String, LocalTime> workTimeMap = this.getWorkDayTime();
        LocalTime workStartTime = workTimeMap.get("start");
        LocalTime workEndTime = workTimeMap.get("end");
        List<CalendarDTO> calendarDTOList;
        if (!existWorkStation) {
            List<Long> workLineIds = facilityOeeList.stream().map(facilityOee -> facilityOee.getWorkCell().getWorkLine().getId()).toList();
            calendarDTOList = oeeCalendarProxy.findByWorkLineIdInAndWorkTimeAndWorkStationIdIsNullAndDeleted(workLineIds, recordDate, Constants.LONG_ZERO);
        } else {
            List<Long> workStationIds = facilityOeeList.stream().filter(facilityOee -> Objects.nonNull(facilityOee.getWorkCell().getWorkStation())).toList().stream().map(facilityOee -> facilityOee.getWorkCell().getWorkStation().getId()).toList();
            calendarDTOList = CollectionUtils.isNotEmpty(workStationIds) ? oeeCalendarProxy.findByWorkStationIdInAndWorkLineIdNullAndWorkTimeAndDeleted(workStationIds, recordDate, Constants.LONG_ZERO) : null;
        }
        Map<Long, CalendarDTO> calendarDTOGroup = CollectionUtils.isNotEmpty(calendarDTOList) ? existWorkStation ? calendarDTOList.stream().collect(Collectors.toMap(CalendarDTO::getWorkStationId, Function.identity())) : calendarDTOList.stream().collect(Collectors.toMap(CalendarDTO::getWorkLineId, Function.identity())) : new HashMap<>();
        //获取设备状态列表信息
        List<FacilityStatusDashboardDTO.FacilityOeeInfo> facilityOeeStatusList = facilityStatusInfo(facilityOeeList, facilityStatusMap, workEndTime, recordDate, calendarDTOGroup);
        //获取设备切片时长信息
        List<FacilitySliceDashboardDTO.FacilitySlice> facilitySliceList = facilitySliceDashboard(facilityOeeList, facilityStatusMap, workEndTime, recordDate, calendarDTOGroup);
        //1. 设备状态占比集合
        setFacilityStatusDistribution(facilityOeeStatusList, facilityStatusDashboardDTO);
        //2. 获取指定条件下的设备OEE计算结果列表
        //List<FacilityStatusDashboardDTO.FacilityOeeInfo> facilityOeeRealTimeInfoList = getRealTimeFacilityOee(workLineIdList, facilityId, recordDate, facilityStatusDashboardDTO);
        facilityOeeStatusList.forEach(facilityOeeInfo -> {
//            if (!CollectionUtils.isEmpty(facilityOeeRealTimeInfoList)) {
//                facilityOeeRealTimeInfoList.stream().filter(facilityOeeRealTimeInfo -> facilityOeeRealTimeInfo.getCode().equals(facilityOeeInfo.getCode())).findFirst().ifPresent(facilityOeeRealTimeInfo -> {
//                    facilityOeeInfo.setOee(facilityOeeRealTimeInfo.getOee()).setTimeActivation(facilityOeeRealTimeInfo.getTimeActivation()).setPerformActivation(facilityOeeRealTimeInfo.getPerformActivation());
//                });
//            }
            //前端状态：0：运行，1：关机，2：故障，3：待机
            if (!CollectionUtils.isEmpty(facilitySliceList)) {
                facilitySliceList.stream().filter(facilitySlice -> facilitySlice.getCode().equals(facilityOeeInfo.getCode())).findFirst().ifPresent(facilitySlice -> {
                    facilitySlice.getFacilityStageList().forEach(facilityStage -> {
                        if (facilityStage.getStatus() == Constants.INT_ONE) {
                            facilityOeeInfo.setCloseTime(facilityOeeInfo.getCloseTime().add(BigDecimal.valueOf(DateUtils.getWorkTime(facilityStage.getStartTime(), facilityStage.getEndTime()))));
                        } else if (facilityStage.getStatus() == Constants.INT_TWO) {
                            facilityOeeInfo.setFaultTime(facilityOeeInfo.getFaultTime().add(BigDecimal.valueOf(DateUtils.getWorkTime(facilityStage.getStartTime(), facilityStage.getEndTime()))));
                        } else if (facilityStage.getStatus() == Constants.INT_THREE) {
                            facilityOeeInfo.setIdleTime(facilityOeeInfo.getIdleTime().add(BigDecimal.valueOf(DateUtils.getWorkTime(facilityStage.getStartTime(), facilityStage.getEndTime()))));
                        } else if (facilityStage.getStatus() == Constants.INT_FIVE) {
                            facilityOeeInfo.setIdleTime(facilityOeeInfo.getIdleTime().add(BigDecimal.valueOf(DateUtils.getWorkTime(facilityStage.getStartTime(), facilityStage.getEndTime()))));
                        }
                    });
                    facilityOeeInfo.setFacilitySlice(facilitySlice);
                });
            }
        });
        facilityStatusDashboardDTO.setFacilityOeeInfoList(facilityOeeStatusList);
        return facilityStatusDashboardDTO;
    }

//    /**
//     * 异常停机时间TOP5
//     *
//     * @param workLineId                 产线ID
//     * @param facilityStatusDashboardDTO 设备状态看板DTO
//     * @return : void
//     * <AUTHOR>
//     * @date 2023/5/19
//     **/
//    public void setDownLineDurationTop5(List<Long> workLineIds, Long facilityId, LocalDate recordDate, FacilityStatusDashboardDTO facilityStatusDashboardDTO) {
//
//        //查询已停机OEE
//        List<FacilityOee> facilityOeeList = facilityOeeRepository.findByWorkLineIdInAndRecordDateAndFacilityIdAndDeleted(workLineIds, recordDate, facilityId, Constants.LONG_ZERO);
//        if (CollectionUtils.isEmpty(facilityOeeList)) {
//            return;
//        }
//        List<FacilityOee> facilityOeeDownLines = facilityOeeList.stream().filter(facilityOee -> facilityOee.getDownLineDuration() > Constants.INT_ZERO || Objects.nonNull(facilityOee.getAbnormalStatusStartTime())).toList();
//        if (CollectionUtils.isEmpty(facilityOeeDownLines)) {
//            return;
//        }
//        //获取工作日历工作时间
//        Map<String, LocalTime> workTimeMap = getWorkDayTime();
//        LocalTime endTime = workTimeMap.get("end");
//        boolean existWorkStation = FuncKeyUtil.checkApi(FuncKeyConstants.WORK_STATION_FUNC_KEY);
//        List<FacilityStatusDashboardDTO.DownLineDurationTop5> downLineDurationTop5List = new ArrayList<>();
//        facilityOeeDownLines.forEach(facilityOee -> {
//            FacilityStatusDashboardDTO.DownLineDurationTop5 downLineDurationTop5 = new FacilityStatusDashboardDTO.DownLineDurationTop5().setCode(facilityOee.getFacilityDto().getCode()).setName(facilityOee.getFacilityDto().getName());
//            CalendarDTO calendarDTO = existWorkStation && Objects.nonNull(facilityOee.getWorkCell().getWorkStation()) ? ResponseDataUtils.get(calendarFeignClient.findByWorkStationIdAndWorkTime(facilityOee.getWorkCell().getWorkStation().getId(), recordDate.toString())) : ResponseDataUtils.get(calendarFeignClient.findByWorkLineIdAndWorkTime(facilityOee.getWorkCell().getWorkLine().getId(), recordDate.toString()));
//            //工作时间
//            List<EveryDayShiftDTO> everyDayShiftDTOList = null;
//            //工作时间集合
//            List<List<LocalTime>> workTimeList = null;
//            if (!ObjectUtils.isEmpty(calendarDTO)) {
//                // 通过实际结束时间和当日工作结束时间之间的距离进行正序排序，第一个为最晚时间
//                everyDayShiftDTOList = calendarDTO.getShiftJsonList().stream().sorted(Comparator.comparing(i -> DateUtils.getWorkTime(i.getActualEndTime(), endTime))).collect(Collectors.toList());
//                //工作时间集合
//                workTimeList = CollectionUtils.isEmpty(calendarDTO.getShiftJsonList()) ? new ArrayList<>() : calendarDTO.getShiftJsonList().stream().map(i -> Arrays.asList(i.getActualStartTime(), i.getActualEndTime())).collect(Collectors.toList());
//            }
//            //获取当前设备正在停线的持续时长
//            int currentAbnormalDuration = Objects.nonNull(facilityOee.getAbnormalStatusStartTime()) && CollectionUtils.isNotEmpty(workTimeList) ? DateUtils.timeOfCrossingByWorkTimeList(workTimeList, Collections.singletonList(Arrays.asList(facilityOee.getAbnormalStatusStartTime().toLocalTime(), LocalTime.now()))) : Constants.INT_ZERO;
//            downLineDurationTop5.setDownLineTime(DateUtils.minutesToHMS(facilityOee.getDownLineDuration() + currentAbnormalDuration));
//            downLineDurationTop5List.add(downLineDurationTop5);
//        });
//        List<FacilityStatusDashboardDTO.DownLineDurationTop5> top5List = downLineDurationTop5List.stream().sorted(Comparator.comparing(FacilityStatusDashboardDTO.DownLineDurationTop5::getDownLineTime).reversed()).limit(Constants.INT_FIVE).collect(Collectors.toList());
//        facilityStatusDashboardDTO.setDownLineDurationTop5List(top5List);
//    }

    /**
     * 获取实时设备OEE
     *
     * @param workLineId                 产线ID
     * @param facilityStatusDashboardDTO 设备状态看板DTO
     * <AUTHOR>
     * @date 2023/5/19
     **/
    public List<FacilityStatusDashboardDTO.FacilityOeeInfo> getRealTimeFacilityOee(List<Long> workLineIds, Long facilityId, LocalDate recordDate, FacilityStatusDashboardDTO facilityStatusDashboardDTO) {
        //OEE为0的不展示
        List<Map<String, Object>> facilityOeeList = facilityOeeRepository.facilityOeeLowHasWorkLine(workLineIds, recordDate, facilityId, Constants.LONG_ZERO);
        //组装数据
        return facilityOeeList.stream().map(map ->
                new FacilityStatusDashboardDTO.FacilityOeeInfo()
                        .setName(String.valueOf(map.get("name")))
                        .setCode(String.valueOf(map.get("code")))
                        .setTimeActivation(BigDecimal.valueOf(Double.valueOf(String.valueOf(map.get("timeActivation")))))
                        .setPerformActivation(BigDecimal.valueOf(Double.valueOf(String.valueOf(map.get("performActivation")))))
                        .setOee(BigDecimal.valueOf(Double.valueOf(String.valueOf(map.get("oee")))))).collect(Collectors.toList());
    }

    /**
     * 设备状态占比集合
     *
     * @param workLineId                 产线ID
     * @param facilityStatusDashboardDTO 设备状态看板DTO
     * <AUTHOR>
     * @date 2023/5/19
     **/
    public void setFacilityStatusDistribution(List<FacilityStatusDashboardDTO.FacilityOeeInfo> facilityOeeStatusList,
                                              FacilityStatusDashboardDTO facilityStatusDashboardDTO) {

        if (CollectionUtils.isEmpty(facilityOeeStatusList)) {
            return;
        }
        //计算总和
        int numberSum = facilityOeeStatusList.size();
        //0：运行，1：关机，2：故障，3：待机
        //运行
        AtomicInteger status0Number = new AtomicInteger();
        //关机
        AtomicInteger status1Number = new AtomicInteger();
        //故障
        AtomicInteger status2Number = new AtomicInteger();
        //待机
        AtomicInteger status3Number = new AtomicInteger();
        facilityOeeStatusList.forEach(facilityOeeInfo -> {
            if (facilityOeeInfo.getStatus() == Constants.INT_ZERO) {
                status0Number.addAndGet(Constants.INT_ONE);
            } else if (facilityOeeInfo.getStatus() == Constants.INT_ONE) {
                status1Number.addAndGet(Constants.INT_ONE);
            } else if (facilityOeeInfo.getStatus() == Constants.INT_TWO) {
                status2Number.addAndGet(Constants.INT_ONE);
            } else if (facilityOeeInfo.getStatus() == Constants.INT_THREE) {
                status3Number.addAndGet(Constants.INT_ONE);
            }
        });
        //组装数据
        FacilityStatusDashboardDTO.FacilityStatusDistribution facilityStatusDistribution0 = new FacilityStatusDashboardDTO.FacilityStatusDistribution().setName("运行").setStatus(Constants.INT_ZERO).setNumber(status0Number.get()).setDistribution(numberSum == 0 ? BigDecimal.ZERO : NumberUtils.divide(status0Number.get(), numberSum, Constants.INT_TWO));
        FacilityStatusDashboardDTO.FacilityStatusDistribution facilityStatusDistribution1 = new FacilityStatusDashboardDTO.FacilityStatusDistribution().setName("关机").setStatus(Constants.INT_ONE).setNumber(status1Number.get()).setDistribution(numberSum == 0 ? BigDecimal.ZERO : NumberUtils.divide(status1Number.get(), numberSum, Constants.INT_TWO));
        FacilityStatusDashboardDTO.FacilityStatusDistribution facilityStatusDistribution2 = new FacilityStatusDashboardDTO.FacilityStatusDistribution().setName("故障").setStatus(Constants.INT_TWO).setNumber(status2Number.get()).setDistribution(numberSum == 0 ? BigDecimal.ZERO : NumberUtils.divide(status2Number.get(), numberSum, Constants.INT_TWO));
        FacilityStatusDashboardDTO.FacilityStatusDistribution facilityStatusDistribution3 = new FacilityStatusDashboardDTO.FacilityStatusDistribution().setName("待机").setStatus(Constants.INT_THREE).setNumber(status3Number.get()).setDistribution(numberSum == 0 ? BigDecimal.ZERO : NumberUtils.divide(status3Number.get(), numberSum, Constants.INT_TWO));
        List<FacilityStatusDashboardDTO.FacilityStatusDistribution> facilityStatusDistributionList = new ArrayList<FacilityStatusDashboardDTO.FacilityStatusDistribution>();
        facilityStatusDistributionList.add(facilityStatusDistribution0);
        facilityStatusDistributionList.add(facilityStatusDistribution1);
        facilityStatusDistributionList.add(facilityStatusDistribution2);
        facilityStatusDistributionList.add(facilityStatusDistribution3);
        facilityStatusDashboardDTO.setFacilityStatusDistributionList(facilityStatusDistributionList);
    }

    /**
     * 设备状态看板（分页）
     *
     * @param workLineId
     * @param pageable
     * @return : net.airuima.web.rest.procedure.oee.dto.FacilityStatusDashboardDTO
     * <AUTHOR>
     * @date 2023/5/19
     **/
    public List<FacilityStatusDashboardDTO.FacilityOeeInfo> facilityStatusInfo(List<FacilityOee> facilityOeeList, Map<Long, Integer> facilityStatusMap,
                                                                               LocalTime workEndTime, LocalDate recordDate, Map<Long, CalendarDTO> calendarDTOGroup) {
        recordDate = Objects.nonNull(recordDate) ? recordDate : LocalDate.now();
        LocalDate finalRecordDate = recordDate;
        boolean existWorkStation = FuncKeyUtil.checkApi(FuncKeyConstants.WORK_STATION_FUNC_KEY);
        List<FacilityStatusDashboardDTO.FacilityOeeInfo> facilityOeeStatusList = new ArrayList<>();
        facilityOeeList.forEach(facilityOee -> {
            //运行时间
            BigDecimal runTime = BigDecimal.ZERO;
            //产量
            int number = Constants.INT_ZERO;
            //前端状态：0：运行，1：关机，2：故障，3：待机
            int status = facilityStatusMap.containsKey(facilityOee.getFacilityId()) ? facilityStatusMap.get(facilityOee.getFacilityId()) : Constants.INT_ONE;
            //将数据字典的状态转换为前端状态
            status = convertStatus(status);
            //工作时间
            List<EveryDayShiftDTO> everyDayShiftDTOList = null;
            //工作时间集合
            List<List<LocalTime>> workTimeList = null;
            CalendarDTO calendarDTO = existWorkStation && Objects.nonNull(facilityOee.getWorkCell().getWorkStation()) ? calendarDTOGroup.containsKey(facilityOee.getWorkCell().getWorkStation().getId()) ? null : calendarDTOGroup.get(facilityOee.getWorkCell().getWorkStation().getId()) : calendarDTOGroup.containsKey(facilityOee.getWorkCell().getWorkLine().getId()) ? calendarDTOGroup.get(facilityOee.getWorkCell().getWorkLine().getId()) : null;
            if (!ObjectUtils.isEmpty(calendarDTO)) {
                // 通过实际结束时间和当日工作结束时间之间的距离进行正序排序，第一个为最晚时间
                everyDayShiftDTOList = calendarDTO.getShiftJsonList().stream().sorted(Comparator.comparing(i -> DateUtils.getWorkTime(i.getActualEndTime(), workEndTime))).collect(Collectors.toList());
                //工作时间集合
                workTimeList = CollectionUtils.isEmpty(calendarDTO.getShiftJsonList()) ? new ArrayList<>() : calendarDTO.getShiftJsonList().stream().map(i -> Arrays.asList(i.getActualStartTime(), i.getActualEndTime())).collect(Collectors.toList());
            }
            //如果当前时间不是工作时间且最新设备状态为运行时则看板设备状态改为待机
            boolean currentIsWorkTime = DateUtils.localTimeBetween(LocalTime.now(), workTimeList);
            if (status == Constants.INT_ZERO && !currentIsWorkTime) {
                status = Constants.INT_THREE;
            }
            //当天运行时间
            LocalTime actualStartTime = getActualStartTime(everyDayShiftDTOList, null);
            Integer time = DateUtils.timeOfCrossingByWorkTimeList(workTimeList, Collections.singletonList(Arrays.asList(actualStartTime, LocalTime.now())));
            //获取当前设备正在停线的持续时长
            int currentAbnormalDuration = Objects.nonNull(facilityOee.getAbnormalStatusStartTime()) ? DateUtils.timeOfCrossingByWorkTimeList(workTimeList, Collections.singletonList(Arrays.asList(facilityOee.getAbnormalStatusStartTime().toLocalTime(), LocalTime.now()))) : Constants.INT_ZERO;
            runTime = BigDecimal.valueOf(Double.valueOf(time - facilityOee.getDownLineDuration() - currentAbnormalDuration));
            FacilityStatusDashboardDTO.FacilityOeeInfo facilityOeeInfo = new FacilityStatusDashboardDTO.FacilityOeeInfo()
                    .setCode(facilityOee.getFacilityDto().getCode())
                    .setName(facilityOee.getFacilityDto().getName())
                    .setStatus(status).setRunTime(runTime)
                    .setQualifiedNumber(facilityOee.getQualifiedNumber()).setUnqualifiedNumber(facilityOee.getUnqualifiedNumber())
                    .setNumber(number + facilityOee.getFinishNumber()).setQualifiedRate(facilityOee.getFinishNumber() > Constants.INT_ZERO
                            ? NumberUtils.divide(facilityOee.getQualifiedNumber(), facilityOee.getFinishNumber(), Constants.INT_FOUR) : BigDecimal.ZERO);
            //计算实时稼动时长
            facilityOeeInfo.setTimeActivation(Objects.nonNull(time) && time!=Constants.INT_ZERO ?runTime.divide(BigDecimal.valueOf(Double.valueOf(time)),Constants.INT_FOUR,BigDecimal.ROUND_HALF_UP):BigDecimal.valueOf(Constants.INT_ZERO));

            if (facilityOee.getFinishNumber()>Constants.INT_ZERO) {
                //获取节拍
                FacilityCadence facilityCadence = facilityCadenceRepository.findByWorkCellIdAndFacilityIdAndDeleted(facilityOee.getWorkCell().getId(),facilityOee.getFacilityId(),Constants.LONG_ZERO);
                if( Objects.nonNull(facilityCadence)) {
                    int theoryRunTime = facilityCadence.getUnit() == Constants.INT_ONE ? (facilityCadence.getDuration()).intValue() * 60 : facilityCadence.getUnit() == Constants.INT_TWO ? facilityCadence.getDuration().intValue() * 3600 : facilityCadence.getDuration().intValue();
                    //计算实时性能稼动率
                    facilityOeeInfo.setPerformActivation(runTime.compareTo(BigDecimal.ZERO) != 0 ? BigDecimal.valueOf(Double.valueOf(facilityOee.getFinishNumber() * theoryRunTime)).divide(runTime, Constants.INT_FOUR, BigDecimal.ROUND_HALF_UP) : new BigDecimal(0));
                    //计算实时OEE
                    facilityOeeInfo.setOee(facilityOeeInfo.getTimeActivation().multiply(facilityOeeInfo.getPerformActivation()).multiply(facilityOeeInfo.getQualifiedRate()).setScale(Constants.INT_FOUR, RoundingMode.HALF_UP));
                }
            }
            facilityOeeStatusList.add(facilityOeeInfo);
        });
        return facilityOeeStatusList;

    }

    /**
     * 获取当天运行时间
     *
     * @param everyDayShiftDTOList 每日班次信息
     * @return java.time.LocalTime运行时间
     */
    private LocalTime getActualStartTime(List<EveryDayShiftDTO> everyDayShiftDTOList, FacilityStatusChangeDTO facilityStatusChangeDTO) {
        if (!ObjectUtils.isEmpty(facilityStatusChangeDTO) && facilityStatusChangeDTO.getRecordTime().toLocalDate().equals(LocalDate.now())) {
            //当天时间是从recodeTime开始
            return facilityStatusChangeDTO.getRecordTime().toLocalTime();
        } else {
            //当天时间是从开始时间开始
            return !CollectionUtils.isEmpty(everyDayShiftDTOList) ? everyDayShiftDTOList.get(everyDayShiftDTOList.size() - Constants.INT_ONE).getActualStartTime() : LocalTime.MIN;
        }
    }

    /**
     * 设备状态看板（分页）
     *
     * @param workLineId
     * @param pageable
     * @return : net.airuima.web.rest.procedure.oee.dto.FacilityStatusDashboardDTO
     * <AUTHOR>
     * @date 2023/5/19
     **/
    public List<FacilitySliceDashboardDTO.FacilitySlice> facilitySliceDashboard(List<FacilityOee> facilityOeeList,
                                                                                Map<Long, Integer> facilityStatusMap,
                                                                                LocalTime workEndTime,
                                                                                LocalDate recordDate, Map<Long, CalendarDTO> calendarDTOGroup) {
        LocalDateTime localDateTime = LocalDateTime.now();
        recordDate = Objects.nonNull(recordDate) ? recordDate : LocalDate.now();
        if (recordDate.isBefore(LocalDate.now())) {
            localDateTime = LocalDateTime.of(recordDate, LocalTime.MAX);
        }
        //获取设备阶段集合
        LocalDate finalRecordDate = recordDate;
        LocalDateTime finalLocalDateTime = localDateTime;
        LocalDateTime finalLocalDateTime1 = localDateTime;
        List<FacilitySliceDashboardDTO.FacilitySlice> facilitySliceList = new ArrayList<>();
        facilityOeeList.stream().map(FacilityOee::getFacilityId).toList();
        FacilityStatusChangeRequestDTO facilityStatusChangeRequestDTO = new FacilityStatusChangeRequestDTO();
        facilityStatusChangeRequestDTO.setFacilityIds(facilityOeeList.stream().map(FacilityOee::getFacilityId).toList()).setLatest(Boolean.FALSE).setRecordDate(finalRecordDate);
        List<FacilityStatusChangeDTO> allFacilityStatusChangeDTOList = rbaseFacilityProxy.findByConditions(facilityStatusChangeRequestDTO);
        Map<Long, List<FacilityStatusChangeDTO>> facilityStatusChangeDTOGroup = CollectionUtils.isNotEmpty(allFacilityStatusChangeDTOList) ? allFacilityStatusChangeDTOList.stream().collect(Collectors.groupingBy(FacilityStatusChangeDTO::getFacilityId)) : new HashMap<>();
        facilityOeeList.forEach(facilityOee -> {
            //获取工作日历工作时间
            boolean existWorkStation = FuncKeyUtil.checkApi(FuncKeyConstants.WORK_STATION_FUNC_KEY) && Objects.nonNull(facilityOee.getWorkCell().getWorkStation());
            CalendarDTO calendarDTO = existWorkStation && Objects.nonNull(facilityOee.getWorkCell().getWorkStation()) ? calendarDTOGroup.containsKey(facilityOee.getWorkCell().getWorkStation().getId()) ? null : calendarDTOGroup.get(facilityOee.getWorkCell().getWorkStation().getId()) : calendarDTOGroup.containsKey(facilityOee.getWorkCell().getWorkLine().getId()) ? calendarDTOGroup.get(facilityOee.getWorkCell().getWorkLine().getId()) : null;
            // 通过实际结束时间和当日工作结束时间之间的距离进行正序排序，第一个为最晚时间
            List<EveryDayShiftDTO> everyDayShiftDTOList = calendarDTO.getShiftJsonList().stream().sorted(Comparator.comparing(i -> DateUtils.getWorkTime(i.getActualEndTime(), workEndTime))).collect(Collectors.toList());
            LocalTime actualStartTime = everyDayShiftDTOList.get(everyDayShiftDTOList.size() - Constants.INT_ONE).getActualStartTime();
            LocalTime actualEndTime = everyDayShiftDTOList.get(Constants.INT_ZERO).getActualEndTime();
            List<List<LocalTime>> workTimeList = calendarDTO.getShiftJsonList().stream().map(i -> Arrays.asList(i.getActualStartTime(), i.getActualEndTime())).collect(Collectors.toList());
            //判断当前时间是否在范围内
            boolean isOK = everyDayShiftDTOList.stream().anyMatch(i -> (i.getActualStartTime().isBefore(i.getActualEndTime()) && i.getActualStartTime().isBefore(finalLocalDateTime.toLocalTime()) && finalLocalDateTime.toLocalTime().isBefore(i.getActualEndTime())) || (i.getActualStartTime().isAfter(i.getActualEndTime()) && !(i.getActualEndTime().isBefore(finalLocalDateTime.toLocalTime()) && finalLocalDateTime.toLocalTime().isBefore(i.getActualStartTime()))));
            LocalTime actualEndNowTime = Boolean.TRUE.equals(isOK) ? finalLocalDateTime1.toLocalTime() : actualEndTime;
            List<FacilityStatusChangeDTO> facilityStatusChangeDTOList = facilityStatusChangeDTOGroup.containsKey(facilityOee.getFacilityId()) ? facilityStatusChangeDTOGroup.get(facilityOee.getFacilityId()) : new ArrayList<>();
            if (CollectionUtils.isEmpty(facilityStatusChangeDTOList)) {
                facilityStatusChangeRequestDTO.setRecordDate(null).setLatest(Boolean.TRUE).setFacilityIds(Collections.singletonList(facilityOee.getFacilityId()));
                facilityStatusChangeDTOList = rbaseFacilityProxy.findByConditions(facilityStatusChangeRequestDTO);
                if (!CollectionUtils.isEmpty(facilityStatusChangeDTOList)) {
                    facilityStatusChangeDTOList.forEach(facilityStatusChangeDTO -> {
                        facilityStatusChangeDTO.setRecordDate(LocalDate.now()).setRecordTime(LocalDateTime.of(LocalDate.now(), actualStartTime));
                    });
                } else {
                    facilityStatusChangeDTOList = new ArrayList<>();
                    FacilityStatusChangeDTO facilityStatusChangeDTO = new FacilityStatusChangeDTO();
                    facilityStatusChangeDTO.setRecordDate(LocalDate.now())
                            .setRecordTime(LocalDateTime.of(LocalDate.now(), actualStartTime))
                            .setFacilityId(facilityOee.getFacilityId()).setOriginStatus(Constants.NEGATIVE_ONE).setTargetStatus(Constants.INT_ONE);
                    facilityStatusChangeDTOList.add(facilityStatusChangeDTO);
                }
            }
            //过滤不为上班期间的状态变更
            facilityStatusChangeDTOList = CollectionUtils.isEmpty(facilityStatusChangeDTOList) ? new ArrayList<>() : facilityStatusChangeDTOList.stream().filter(i -> DateUtils.localTimeBetween(i.getRecordTime().toLocalTime(), workTimeList)).collect(Collectors.toList());

            List<FacilitySliceDashboardDTO.FacilityStage> facilityStageList = new ArrayList<>();
            //获取设备切片集合
            if (!CollectionUtils.isEmpty(facilityStatusChangeDTOList)) {
                handleFacilityStatusChangeHistory(everyDayShiftDTOList, actualStartTime, actualEndNowTime, facilityStatusChangeDTOList, facilityStageList);
            } else {
                //无状态变更记录则为一个状态
                FacilitySliceDashboardDTO.FacilityStage facilityStage = new FacilitySliceDashboardDTO.FacilityStage()
                        .setStatus(convertStatus(facilityStatusMap.getOrDefault(facilityOee.getFacilityId(), Constants.NEGATIVE_ONE))).setStartTime(actualStartTime).setEndTime(actualEndNowTime).setPercent(BigDecimal.ONE);
                //拆分
                List<FacilitySliceDashboardDTO.FacilityStage> facilityStageResult = splitFacilityStageTime(facilityStage, everyDayShiftDTOList);
                facilityStageResult.forEach(result -> {
                    int stageSecondsLast = DateUtils.getWorkTime(result.getStartTime(), result.getEndTime());
                    result.setPercent(NumberUtils.divide(stageSecondsLast, Constants.DAYOFSECOND, 4));
                });
                facilityStageList.addAll(facilityStageResult);
            }
            //补齐上班时间到开始时间+当前时间至第二天上班时间
            List<FacilitySliceDashboardDTO.FacilityStage> facilityStageListAll = complementWorkTime(facilityStageList, actualStartTime, actualEndTime);
            if (CollectionUtils.isNotEmpty(facilityStageListAll)) {
                List<FacilitySliceDashboardDTO.FacilityStage> sameStartAndEndTimeStageList = facilityStageListAll.stream().filter(facilityStage -> facilityStage.getStartTime().equals(facilityStage.getEndTime())).toList();
                if (CollectionUtils.isNotEmpty(sameStartAndEndTimeStageList)) {
                    facilityStageListAll.removeAll(sameStartAndEndTimeStageList);
                }
            }
            facilitySliceList.add(new FacilitySliceDashboardDTO.FacilitySlice().setFacilityStageList(facilityStageListAll)
                    .setCode(facilityOee.getFacilityDto().getCode()).setName(facilityOee.getFacilityDto().getName())
                    .setActualStartTime(actualStartTime).setActualEndTime(actualEndNowTime));
        });
        return facilitySliceList;
    }

    /**
     * 处理设备变更状态历史
     *
     * @param everyDayShiftDTOList 每日班次信息列表
     * @param actualStartTime      开始时间
     * @param actualEndNowTime     结束最新时间
     * @param facilityStageList    设备阶段列表
     */
    private void handleFacilityStatusChangeHistory(List<EveryDayShiftDTO> everyDayShiftDTOList, LocalTime actualStartTime, LocalTime actualEndNowTime, List<FacilityStatusChangeDTO> facilityStatusChangeDTOList, List<FacilitySliceDashboardDTO.FacilityStage> facilityStageList) {
        for (int i = 0; i < facilityStatusChangeDTOList.size(); i++) {
            int status = convertStatus(facilityStatusChangeDTOList.get(i).getOriginStatus());
            FacilitySliceDashboardDTO.FacilityStage facilityStage = new FacilitySliceDashboardDTO.FacilityStage().setStatus(status)
                    .setStartTime(i == 0 ? actualStartTime : facilityStatusChangeDTOList.get(i - Constants.INT_ONE).getRecordTime().toLocalTime()).setEndTime(facilityStatusChangeDTOList.get(i).getRecordTime().toLocalTime());
            //拆分
            List<FacilitySliceDashboardDTO.FacilityStage> facilityStageResult = splitFacilityStageTime(facilityStage, everyDayShiftDTOList);
            facilityStageResult.forEach(result -> {
                int stageSeconds = DateUtils.getWorkTime(result.getStartTime(), result.getEndTime());
                result.setPercent(NumberUtils.divide(stageSeconds, Constants.DAYOFSECOND, 4));
            });
            facilityStageList.addAll(facilityStageResult);

            if (i == facilityStatusChangeDTOList.size() - Constants.INT_ONE) {
                FacilitySliceDashboardDTO.FacilityStage facilityStageLast = new FacilitySliceDashboardDTO.FacilityStage().setStatus(convertStatus(facilityStatusChangeDTOList.get(i).getTargetStatus()))
                        .setStartTime(facilityStatusChangeDTOList.get(i).getRecordTime().toLocalTime()).setEndTime(actualEndNowTime);
                //拆分
                List<FacilitySliceDashboardDTO.FacilityStage> facilityStageLastResult = splitFacilityStageTime(facilityStageLast, everyDayShiftDTOList);
                facilityStageLastResult.forEach(lastResult -> {
                    int stageSecondsLast = DateUtils.getWorkTime(lastResult.getStartTime(), lastResult.getEndTime());
                    lastResult.setPercent(NumberUtils.divide(stageSecondsLast, Constants.DAYOFSECOND, 4));
                });
                facilityStageList.addAll(facilityStageLastResult);
            }
        }
    }

    /**
     * 补齐上班时间到开始时间+当前时间至第二天上班时间
     *
     * @param facilityStageList 原始时间段
     * @param actualStartTime   实际开始时间
     * @param actualEndTime     实际结束时间
     * @return : java.util.List<net.airuima.web.rest.procedure.oee.dto.FacilitySliceDashboardDTO.FacilityStage>
     * <AUTHOR>
     * @date 2023/5/24
     **/
    public List<FacilitySliceDashboardDTO.FacilityStage> complementWorkTime(List<FacilitySliceDashboardDTO.FacilityStage> facilityStageList, LocalTime actualStartTime, LocalTime actualEndTime) {
        List<FacilitySliceDashboardDTO.FacilityStage> facilityStageListResult = new ArrayList<>();
        FacilitySliceDashboardDTO.FacilityStage facilityStage = facilityStageList.get(Constants.INT_ZERO);
        if (facilityStage.getStartTime().isAfter(actualStartTime)) {
            //补齐开始时间段
            FacilitySliceDashboardDTO.FacilityStage facilityStageFirst = new FacilitySliceDashboardDTO.FacilityStage().setStartTime(actualStartTime).setEndTime(facilityStage.getStartTime()).setStatus(Constants.INT_THREE);
            int stageSecondsLast = DateUtils.getWorkTime(facilityStageFirst.getStartTime(), facilityStageFirst.getEndTime());
            facilityStageFirst.setPercent(NumberUtils.divide(stageSecondsLast, Constants.DAYOFSECOND, 4));
            facilityStageListResult.add(facilityStageFirst);
        }
        //中间有效时间段
        facilityStageListResult.addAll(facilityStageList);
        if (!facilityStageList.get(facilityStageList.size() - 1).getEndTime().equals(actualStartTime)) {
            //补齐末尾时间段
            FacilitySliceDashboardDTO.FacilityStage facilityStageLast = new FacilitySliceDashboardDTO.FacilityStage()
                    .setStartTime(facilityStageList.get(facilityStageList.size() - 1).getEndTime()).setEndTime(actualStartTime).setStatus(Constants.INT_THREE);
            int stageSecondsLast = DateUtils.getWorkTime(facilityStageLast.getStartTime(), facilityStageLast.getEndTime());
            facilityStageLast.setPercent(NumberUtils.divide(stageSecondsLast, Constants.DAYOFSECOND, 4));
            facilityStageListResult.add(facilityStageLast);
        }
        return facilityStageListResult;
    }

    /**
     * 将时间段根据日历时间段进行拆分
     *
     * @param facilityStage        时间段
     * @param everyDayShiftDTOList 日历时间段
     * @return : java.util.List<net.airuima.web.rest.procedure.oee.dto.FacilitySliceDashboardDTO.FacilityStage>
     * <AUTHOR>
     * @date 2023/5/24
     **/
    public static List<FacilitySliceDashboardDTO.FacilityStage> splitFacilityStageTime(FacilitySliceDashboardDTO.FacilityStage facilityStage, List<EveryDayShiftDTO> everyDayShiftDTOList) {
        LocalTime begin = facilityStage.getStartTime();
        LocalTime end = facilityStage.getEndTime();

        List<FacilitySliceDashboardDTO.FacilityStage> facilityStageList = new ArrayList<>();

        boolean isStart = Constants.FALSE;
        for (int i = everyDayShiftDTOList.size() - Constants.INT_ONE; i >= 0; i--) {
            LocalTime actualStartTime = everyDayShiftDTOList.get(i).getActualStartTime();
            LocalTime actualEndTime = everyDayShiftDTOList.get(i).getActualEndTime();

            //当开始时间和结束时间均在同一时间段中
            if (DateUtils.localTimeBetween(begin, List.of(Arrays.asList(actualStartTime, actualEndTime)))
                    && DateUtils.localTimeBetween(end, List.of(Arrays.asList(actualStartTime, actualEndTime)))
                    && !isStart) {
                facilityStageList.add(new FacilitySliceDashboardDTO.FacilityStage().setStartTime(begin).setEndTime(end).setStatus(facilityStage.getStatus()));
                break;
            }
            //当开始时间在时间段中，结束时间不在时间段中，当前为初始时间段
            else if (DateUtils.localTimeBetween(begin, List.of(Arrays.asList(actualStartTime, actualEndTime)))
                    && !DateUtils.localTimeBetween(end, List.of(Arrays.asList(actualStartTime, actualEndTime)))
                    && !isStart) {
                //时间段1
                facilityStageList.add(new FacilitySliceDashboardDTO.FacilityStage().setStartTime(begin).setEndTime(actualEndTime).setStatus(facilityStage.getStatus()));
                isStart = Constants.TRUE;
            }
            //当开始时间不在时间段中，结束时间在时间段中，则获取最近一次空闲时间段，当前为最终时间段
            else if (!DateUtils.localTimeBetween(begin, List.of(Arrays.asList(actualStartTime, actualEndTime)))
                    && DateUtils.localTimeBetween(end, List.of(Arrays.asList(actualStartTime, actualEndTime)))
                    && isStart) {
                //之前空闲时间
                if (!everyDayShiftDTOList.get(i + 1).getActualEndTime().equals(actualStartTime)) {
                    facilityStageList.add(new FacilitySliceDashboardDTO.FacilityStage().setStartTime(everyDayShiftDTOList.get(i + 1).getActualEndTime()).setEndTime(actualStartTime).setStatus(Constants.INT_THREE));
                }
                //时间段2
                facilityStageList.add(new FacilitySliceDashboardDTO.FacilityStage().setStartTime(actualStartTime).setEndTime(end).setStatus(facilityStage.getStatus()));
                break;
            }
            //当开始时间在上一次结束时间 到 当前开始时间 的空闲时间段中，且未超过开始时间
            else if (i != everyDayShiftDTOList.size() - Constants.INT_ONE
                    && DateUtils.localTimeBetween(begin, List.of(Arrays.asList(everyDayShiftDTOList.get(i + 1).getActualEndTime(), actualStartTime)))
                    && !isStart) {
                //之前空闲时间
                if (!begin.equals(actualStartTime)) {
                    facilityStageList.add(new FacilitySliceDashboardDTO.FacilityStage().setStartTime(begin).setEndTime(actualStartTime).setStatus(Constants.INT_THREE));
                }

                if (DateUtils.localTimeBetween(end, List.of(Arrays.asList(actualStartTime, actualEndTime)))) {
                    //时间段
                    facilityStageList.add(new FacilitySliceDashboardDTO.FacilityStage().setStartTime(actualStartTime).setEndTime(end).setStatus(facilityStage.getStatus()));
                }
            }
            //当结束时间在上一次结束时间 到 当前开始时间 的空闲时间段中，且已经超过开始时间，则存在空闲时间
            else if (i != everyDayShiftDTOList.size() - Constants.INT_ONE
                    && DateUtils.localTimeBetween(end, List.of(Arrays.asList(everyDayShiftDTOList.get(i + 1).getActualEndTime(), actualStartTime)))
                    && isStart) {
                //之前空闲时间
                if (!everyDayShiftDTOList.get(i + 1).getActualEndTime().equals(end)) {
                    facilityStageList.add(new FacilitySliceDashboardDTO.FacilityStage().setStartTime(everyDayShiftDTOList.get(i + 1).getActualEndTime()).setEndTime(end).setStatus(Constants.INT_THREE));
                }
            }
            //当开始时间/结束时间均不在时间段中，且已超过开始时间，则为有空闲情况
            else if (!DateUtils.localTimeBetween(begin, List.of(Arrays.asList(actualStartTime, actualEndTime)))
                    && !DateUtils.localTimeBetween(end, List.of(Arrays.asList(actualStartTime, actualEndTime)))
                    && isStart) {
                //之前空闲时间
                if (!everyDayShiftDTOList.get(i + 1).getActualEndTime().equals(actualStartTime)) {
                    facilityStageList.add(new FacilitySliceDashboardDTO.FacilityStage().setStartTime(everyDayShiftDTOList.get(i + 1).getActualEndTime()).setEndTime(actualStartTime).setStatus(Constants.INT_THREE));
                }
                //时间段2
                facilityStageList.add(new FacilitySliceDashboardDTO.FacilityStage().setStartTime(actualStartTime).setEndTime(actualEndTime).setStatus(facilityStage.getStatus()));
            }
        }
        return facilityStageList;
    }

    /**
     * 获取当日最终时间
     *
     * @param everyDayShiftDTOList
     * @param endTime
     * <AUTHOR>
     * @date 2022/12/22
     **/
    public EveryDayShiftDTO getLastTime(List<EveryDayShiftDTO> everyDayShiftDTOList, LocalTime endTime) {
        // 通过实际结束时间和当日工作结束时间之间的距离进行正序排序，第一个为最晚时间
        everyDayShiftDTOList = everyDayShiftDTOList.stream().sorted(Comparator.comparing(i -> DateUtils.getWorkTime(i.getActualEndTime(), endTime))).collect(Collectors.toList());
        return everyDayShiftDTOList.get(Constants.INT_ZERO);
    }

    public void facilityOeeRemind(Map<String, Object> param) {
        log.info("======================>监听OEE处理前一天数据事件");
        try {
            boolean existWorkStation = FuncKeyUtil.checkApi(FuncKeyConstants.WORK_STATION_FUNC_KEY);
            Long workId = Long.valueOf(String.valueOf(param.get("workStationId")));
            LocalDate recordDateToday = LocalDate.parse(String.valueOf(param.get("recordDate")));
            LocalDate recordDateYesterday = recordDateToday.minusDays(1);
            //获取工作日历工作时间
            Map<String, LocalTime> workTimeMap = this.getWorkDayTime();
            LocalTime endTime = workTimeMap.get("end");

            //1. 更新昨天已结束的设备OEE
            //获取待处理OEE
            List<FacilityOee> facilityOeeList = existWorkStation ? facilityOeeRepository.findByWorkCellWorkStationIdInAndRecordDateAndDeleted(List.of(workId), recordDateYesterday, Constants.LONG_ZERO)
                    : facilityOeeRepository.findByWorkCellWorkLineIdInAndRecordDateAndDeleted(List.of(workId), recordDateYesterday, Constants.LONG_ZERO);

            Map<Long, Boolean> facilityStopGroup = new HashMap<>();
            if (CollectionUtils.isNotEmpty(facilityOeeList)) {
                FacilityStatusChangeRequestDTO facilityStatusChangeRequestDTO = new FacilityStatusChangeRequestDTO();
                facilityStatusChangeRequestDTO.setFacilityIds(facilityOeeList.stream().map(FacilityOee::getFacilityId).toList()).setRecordDate(recordDateYesterday).setTargetStatus(Constants.INT_THREE).setLatest(Boolean.TRUE);
                List<FacilityStatusChangeDTO> facilityStatusChangeDTOS = rbaseFacilityProxy.findByConditions(facilityStatusChangeRequestDTO);
                if (CollectionUtils.isNotEmpty(facilityStatusChangeDTOS)) {
                    facilityStatusChangeDTOS.forEach(facilityStatusChangeDTO -> {
                        facilityStopGroup.put(facilityStatusChangeDTO.getFacilityId(), Boolean.TRUE);
                    });
                }
                //获取工作日历
                for (FacilityOee facilityOee : facilityOeeList) {
                    if (facilityStopGroup.containsKey(facilityOee.getFacilityId())) {
                        //工站指定日历
                        CalendarDTO calendarDTO = existWorkStation && Objects.nonNull(facilityOee.getWorkCell().getWorkStation()) ? oeeCalendarProxy.findTop1ByWorkStationIdAndWorkLineIdNullAndWorkTimeAndDeleted(facilityOee.getWorkCell().getWorkStation().getId(), recordDateYesterday, Constants.LONG_ZERO)
                                : oeeCalendarProxy.findTop1ByWorkLineIdAndWorkTimeAndWorkStationIdIsNullAndDeleted(facilityOee.getWorkCell().getWorkLine().getId(), recordDateYesterday, Constants.LONG_ZERO);
                        //重新计算停线时间
                        Integer downLineDuration = this.calculationDownLineDuration(facilityOee.getWorkCell().getId(), facilityOee.getFacilityId(), calendarDTO.getShiftJsonList(), facilityOee.getRecordDate(), endTime);
                        facilityOee.setDownLineDuration(facilityOee.getDownLineDuration() + downLineDuration)
                                //稼动时间(负荷时间-停线时间)(s)
                                .setActivationDuration(facilityOee.getLoadDuration() - facilityOee.getDownLineDuration());
                    }
                }
                facilityOeeRepository.saveAll(facilityOeeList);
            }


            //2. 新增昨天已结束的设备OEE
            FacilityOeeCreateDTO facilityOeeCreateDTO = new FacilityOeeCreateDTO();
            //获取今天工作日历集合
            List<CalendarDTO> calendarDTOList = oeeCalendarProxy.findByWorkTimeTomorrow(recordDateToday.toString());
            List<Long> workIdList = calendarDTOList.stream().map(i -> existWorkStation ? i.getWorkStationId() : i.getWorkLineId()).toList();
            //新增有数据OEE
            if (!org.springframework.util.CollectionUtils.isEmpty(workIdList) && workIdList.contains(workId)) {
                CalendarDTO calendarDTO = calendarDTOList.stream().filter(i -> (existWorkStation ? i.getWorkStationId().equals(workId) : i.getWorkLineId().equals(workId)) && !i.getShiftJsonList().isEmpty()).findFirst().orElse(new CalendarDTO());
                Map<Long, List<EveryDayShiftDTO>> map = new HashMap<>();
                map.put(workId, calendarDTO.getShiftJsonList());
                facilityOeeCreateDTO.setWorkIdAndWorkTime(map);
                this.updateByWorkStationOrWorkLine(facilityOeeCreateDTO, recordDateToday);
            } else {
                //没有日历，则将前一天设备数据直接赋值到当前天，状态继承，其它数据为0
                this.addByNoCalendar(workId, recordDateToday);
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }

    }

    /**
     * 手动修改状态
     *
     * @param facilityOeeId 设备OEE记录ID
     * @param status        修改的状态
     */
    public boolean manualFacilityStatus(FacilityOee facilityOee, int status) {
        boolean statusChanged = BeanUtil.getHighestPrecedenceBean(IFacilityOeeService.class).changeFacilityStatus(facilityOee, status, null, LocalDateTime.now());
        Object facilityLatestStatusObject = redisUtils.get("facility_latest_status");
        if (Objects.isNull(facilityLatestStatusObject)) {
            return statusChanged;
        }
        List<FacilityStatusRedisDTO> facilityStatusRedisDTOList = JSON.parseObject(facilityLatestStatusObject.toString(), new TypeReference<>() {
        });
        if (CollectionUtils.isEmpty(facilityStatusRedisDTOList)) {
            return statusChanged;
        }
        List<FacilityStatusRedisDTO> finalFacilityStatusRedisDTOList = facilityStatusRedisDTOList;
        facilityStatusRedisDTOList.stream().filter(facilityStatusRedisDTO -> facilityStatusRedisDTO.getFacilityCode().equals(facilityOee.getFacilityDto().getCode())).findFirst().ifPresent(facilityStatusRedisDTO -> {
            finalFacilityStatusRedisDTOList.remove(facilityStatusRedisDTO);
        });
        facilityStatusRedisDTOList = org.springframework.util.CollectionUtils.isEmpty(facilityStatusRedisDTOList) ? new ArrayList<>() : facilityStatusRedisDTOList;
        redisUtils.set("facility_latest_status", JSON.toJSONString(facilityStatusRedisDTOList));
        return statusChanged;
    }

    /**
     * 设备调用进行修改状态
     *
     * @param workCellCode
     * @param facilityId
     * @param status
     */
    @Klock(keys = {"#facilityId"}, waitTime = 60, leaseTime = 60, lockTimeoutStrategy = LockTimeoutStrategy.KEEP_ACQUIRE)
    public boolean autoChangeFacilityStatus(String workCellCode, Long facilityId, Long changeTimeStamp, Integer status, Integer number) {
        LocalDateTime changeDateTime = Objects.nonNull(changeTimeStamp) ? LocalDateTime.ofInstant(Instant.ofEpochMilli(changeTimeStamp), ZoneId.systemDefault()) : LocalDateTime.now();
        if (!changeDateTime.toLocalDate().equals(LocalDate.now())) {
            return Boolean.FALSE;
        }
        AtomicBoolean statusRealyChanged = new AtomicBoolean(false);
        List<FacilityOee> facilityOeeList = facilityOeeRepository.findByWorkCellCodeAndFacilityIdAndRecordDateAndDeleted(workCellCode, facilityId, changeDateTime.toLocalDate(), Constants.LONG_ZERO);
        try {
            if (!CollectionUtils.isEmpty(facilityOeeList)) {
                facilityOeeList.forEach(facilityOee -> {
                    boolean statusChanged = BeanUtil.getHighestPrecedenceBean(IFacilityOeeService.class).changeFacilityStatus(facilityOee, status, number, changeDateTime);
                    if (statusChanged) {
                        statusRealyChanged.set(true);
                    }
                });
            } else {
                return Boolean.FALSE;
//                List<WorkCellStepFacility> workCellStepFacilityList = workCellStepFacilityRepository.findByFacilityIdAndDeleted(facilityId, Constants.LONG_ZERO);
//                if (CollectionUtils.isEmpty(workCellStepFacilityList)) {
//                    return Boolean.FALSE;
//                }
//                List<FacilityOee> facilityOees = addFacilityOeeBySingleFacility(new HashSet<>(workCellStepFacilityList), status, number, changeDateTime);
//                if (!CollectionUtils.isEmpty(facilityOees)) {
//                    facilityOees = facilityOeeRepository.saveAll(facilityOees);
//                    facilityOees.forEach(facilityOee -> {
//                        boolean statusChanged = BeanUtil.getHighestPrecedenceBean(IFacilityOeeService.class).changeFacilityStatus(facilityOee, status, number, changeDateTime);
//                        if (statusChanged) {
//                            statusRealyChanged.set(true);
//                        }
//                    });
//                }
            }
        } catch (ResponseException e) {
            log.info(e.getErrorMessage());
        }
        return statusRealyChanged.get();
    }

    /**
     * 修改设备状态信息
     *
     * @param facilityOee
     * @param status
     */
    @Override
    @Klock(keys = {"#facilityOee.facilityId"}, waitTime = 60, leaseTime = 60, lockTimeoutStrategy = LockTimeoutStrategy.KEEP_ACQUIRE)
    public boolean changeFacilityStatus(FacilityOee facilityOee, int status, Integer number, LocalDateTime changeDateTime) {
        FacilityLatestStatusDTO facilityLatestStatusDTO = rbaseFacilityProxy.findByFacilityIdAndDeleted(facilityOee.getFacilityId(), Constants.LONG_ZERO);
        //1. 校验参数
        this.validCreate(facilityOee, facilityLatestStatusDTO, status, changeDateTime);
        //2. 获取工作日历工作时间
        Map<String, LocalTime> workTimeMap = this.getWorkDayTime();
        LocalTime endTime = workTimeMap.get("end");
        //2. 获取当前工位或者设备最新异常停机的OEE
        FacilityOee facilityOeeByAbnormalStatus = getFacilityOeeByAbnormalStatus(facilityOee.getWorkCell().getId(), facilityOee.getFacilityId());
        //3. 如果是改目标状态为异常停机状态，且之前没有异常停机状态的OEE，则更新异常停机开始时间，比如[...停线...维护....停线...]，则依然从第一个停线开始算时间
        if ((status == Constants.INT_ZERO || status == Constants.INT_THREE || status == Constants.INT_TWO) && ObjectUtils.isEmpty(facilityOeeByAbnormalStatus)) {
            facilityOee.setAbnormalStatusStartTime(changeDateTime);
        } else if (status == Constants.INT_ONE && !ObjectUtils.isEmpty(facilityOeeByAbnormalStatus)) {
            //获取工作日历
            boolean existWorkStation = FuncKeyUtil.checkApi(FuncKeyConstants.WORK_STATION_FUNC_KEY);
            CalendarDTO calendarDTO = existWorkStation ? oeeCalendarProxy.findTop1ByWorkStationIdAndWorkLineIdNullAndWorkTimeAndDeleted(facilityOee.getWorkCell().getWorkStation().getId(), facilityOee.getRecordDate(), Constants.LONG_ZERO) : oeeCalendarProxy.findTop1ByWorkLineIdAndWorkTimeAndWorkStationIdIsNullAndDeleted(facilityOee.getWorkCell().getWorkLine().getId(), facilityOee.getRecordDate(), Constants.LONG_ZERO);
            //重新计算停线时间
            Integer downLineDuration = reloadDownLineDuration(facilityOeeByAbnormalStatus, calendarDTO.getShiftJsonList(), changeDateTime.toLocalDate(), endTime, Boolean.TRUE);
            facilityOee.setDownLineDuration(facilityOee.getDownLineDuration() + downLineDuration)
                    //稼动时间(负荷时间-停线时间)(s)
                    .setActivationDuration(facilityOee.getLoadDuration() - facilityOee.getDownLineDuration());

            //正常运行后，置空异常开始时间
            facilityOee.setAbnormalStatusStartTime(null);
        }
        facilityOee.setLatestStatusOperateTime(changeDateTime);
        if (Objects.nonNull(number) && number > Constants.INT_ZERO) {
            facilityOee.setFinishNumber(number);
        }
        facilityOeeRepository.save(facilityOee);
        if (!ObjectUtils.isEmpty(facilityOee) && Objects.nonNull(facilityLatestStatusDTO) && facilityLatestStatusDTO.getLatestStatus() == status) {
            return Boolean.FALSE;
        }
        FacilityStatusChangeDTO facilityStatusChangeDTO = new FacilityStatusChangeDTO();
        //3. 组装参数
        facilityStatusChangeDTO.setRecordDate(changeDateTime.toLocalDate()).setRecordTime(changeDateTime);
        facilityStatusChangeDTO.setFacilityId(facilityOee.getFacilityId()).setOriginStatus(Objects.nonNull(facilityLatestStatusDTO) ? facilityLatestStatusDTO.getLatestStatus() : Constants.NEGATIVE_ONE).setTargetStatus(status);
        return rbaseFacilityProxy.manualUpdate(facilityStatusChangeDTO);
    }

    /**
     * 校验参数
     *
     * <AUTHOR>
     * @date 2022/12/22
     **/
    public void validCreate(FacilityOee facilityOee, FacilityLatestStatusDTO facilityLatestStatusDTO, int targetStatus, LocalDateTime changeDateTime) {
        //查询当前Oee最新状态
//        if (!ObjectUtils.isEmpty(facilityOee) && Objects.nonNull(facilityLatestStatusDTO) && facilityLatestStatusDTO.getLatestStatus() == targetStatus) {
//            throw new ResponseException("error.statusIsRepeat", "目标状态不可与当前状态相同");
//        }
        // 计划停止时间范围内，无法更改状态为异常停机
        if (targetStatus == Constants.INT_THREE) {
            List<FacilityPlanDownHistory> facilityPlanDownHistoryList = facilityPlanDownHistoryRepository.findByFacilityOeeIdAndDeleted(facilityOee.getId(), Constants.LONG_ZERO);
            boolean isBetweenWorkTime = facilityPlanDownHistoryList.stream().anyMatch(i -> changeDateTime.toLocalTime().isBefore(i.getEndTime()) && changeDateTime.toLocalTime().isAfter(i.getStartTime()));
            if (isBetweenWorkTime) {
                throw new ResponseException("error.UpdateStatusFailed", "计划停止时间范围内，无法更改状态为异常停机");
            }
        }
        //需要在工作时间内操作
        boolean existWorkStation = FuncKeyUtil.checkApi(FuncKeyConstants.WORK_STATION_FUNC_KEY);
        if (existWorkStation && Objects.isNull(facilityOee.getWorkCell().getWorkStation())) {
            throw new ResponseException("error.workcell.error", "当前工位未绑定工站");
        }
        if (!existWorkStation && Objects.isNull(facilityOee.getWorkCell().getWorkLine())) {
            throw new ResponseException("error.workcell.error", "当前工位未绑定产线");
        }

        CalendarDTO calendarDTO = existWorkStation ? oeeCalendarProxy.findTop1ByWorkStationIdAndWorkLineIdNullAndWorkTimeAndDeleted(facilityOee.getWorkCell().getWorkStation().getId(), changeDateTime.toLocalDate(), Constants.LONG_ZERO) : oeeCalendarProxy.findTop1ByWorkLineIdAndWorkTimeAndWorkStationIdIsNullAndDeleted(facilityOee.getWorkCell().getWorkLine().getId(), changeDateTime.toLocalDate(), Constants.LONG_ZERO);
        if (ObjectUtils.isEmpty(calendarDTO)) {
            throw new ResponseException("error.TimeExpire", "需要在工作时间内操作");
        } else {
            List<List<LocalTime>> planDownTimeList = calendarDTO.getShiftJsonList().stream().map(i -> Arrays.asList(i.getActualStartTime(), i.getActualEndTime())).collect(Collectors.toList());
            Boolean isOK = DateUtils.localTimeBetween(changeDateTime.toLocalTime(), planDownTimeList);
            if (Boolean.FALSE.equals(isOK)) {
                throw new ResponseException("error.TimeExpire", "需要在工作时间内操作");
            }
        }
    }

    /**
     * 重新计算停线时间
     *
     * @param facilityOeeByAbnormalStatus 获取当前工位或者设备最新异常停机的OEE
     * @param everyDayShiftDTOListByParam 当日工作日历排班集合
     * @param recordDate                  记录日期
     * @param endTime                     当日最终工作时间
     * @param isAbnormalStatusStartTime   是否考虑异常开始时间
     * @return : java.lang.Integer
     * <AUTHOR>
     * @date 2022/12/22
     **/
    public Integer reloadDownLineDuration(FacilityOee facilityOeeByAbnormalStatus, List<EveryDayShiftDTO> everyDayShiftDTOListByParam, LocalDate recordDate, LocalTime endTime, Boolean isAbnormalStatusStartTime) {
        // 通过实际结束时间和当日工作结束时间之间的距离进行正序排序，第一个元素为最晚时间段，最后一个元素为最早时间段
        List<EveryDayShiftDTO> everyDayShiftDTOList = everyDayShiftDTOListByParam.stream().sorted(Comparator.comparing(i -> DateUtils.getWorkTime(i.getActualEndTime(), endTime))).collect(Collectors.toList());
        List<LocalTime> todayAbnormalTime;
        //开始时间
        LocalTime actualStartTime = everyDayShiftDTOList.get(everyDayShiftDTOList.size() - Constants.INT_ONE).getActualStartTime();
        //结束时间(有可能是第二天)
        LocalTime actualEndTime = everyDayShiftDTOList.get(Constants.INT_ZERO).getActualEndTime();

        //工作结束时间
        LocalDateTime dayWorkEndTime = (actualEndTime.isBefore(actualStartTime) ? facilityOeeByAbnormalStatus.getRecordDate().plusDays(Constants.INT_ONE) : facilityOeeByAbnormalStatus.getRecordDate()).atTime(actualEndTime);
        //若"异常停机开始时间"不是当天, 则停线时间为“异常停机开始时间” 至 当天结束时间
        LocalDateTime localDateTime = LocalDateTime.now();
        if (localDateTime.isAfter(dayWorkEndTime) && Boolean.TRUE.equals(isAbnormalStatusStartTime)) {
            todayAbnormalTime = Arrays.asList(facilityOeeByAbnormalStatus.getAbnormalStatusStartTime().toLocalTime(), actualEndTime);
        } else {
            //若"异常停机开始时间"是今天, 则停线时间为“当天实际工作开始时间” 至 当前时间
            todayAbnormalTime = Arrays.asList(facilityOeeByAbnormalStatus.getAbnormalStatusStartTime().toLocalTime(), localDateTime.toLocalTime());
        }
        //对比交叉时间，得到停线时间
        List<List<LocalTime>> dayShiftTimeList = everyDayShiftDTOListByParam.stream().map(i -> Arrays.asList(i.getActualStartTime(), i.getActualEndTime())).collect(Collectors.toList());
        Integer downLineDuration = CollectionUtils.isEmpty(todayAbnormalTime) ? Constants.INT_ZERO : DateUtils.timeOfCrossingByWorkTimeList(Arrays.asList(todayAbnormalTime), dayShiftTimeList);
        return downLineDuration;
    }

    /**
     * 重新计算停线时间
     *
     * @param workCellId 工位ID
     * @param facilityId 设备ID
     * @param endTime    当日最终工作时间
     * @return : java.lang.Integer
     * <AUTHOR>
     * @date 2022/12/23
     **/
    public Integer calculationDownLineDuration(Long workCellId, Long facilityId, List<EveryDayShiftDTO> everyDayShiftDTOList, LocalDate recordDate, LocalTime endTime) {
        Integer downLineDuration = Constants.INT_ZERO;
        //获取最新异常停机开始时间不为空的OEE
        FacilityOee facilityOeeByAbnormalStatus = getFacilityOeeByAbnormalStatus(workCellId, facilityId);
        //如果不为空，则重新计算停线时间
        if (!ObjectUtils.isEmpty(facilityOeeByAbnormalStatus)) {
            downLineDuration = reloadDownLineDuration(facilityOeeByAbnormalStatus, everyDayShiftDTOList, recordDate, endTime, Boolean.TRUE);
        }
        return downLineDuration;
    }

    /**
     * 获取当前工位或者设备最新异常停机的OEE
     *
     * @param workCellId 工位ID
     * @param facilityId 设备ID
     * @return : net.airuima.domain.procedure.oee.FacilityOee
     * <AUTHOR>
     * @date 2022/12/22
     **/
    public FacilityOee getFacilityOeeByAbnormalStatus(Long workCellId, Long facilityId) {
        //1. OEE类别（0:工位OEE，1:设备OEE）
        Integer oeeCategoryKey = this.getOeeCategoryKey();
        //2. 获取当前工位或者设备最新异常停机的OEE
        FacilityOee facilityOeeByAbnormalStatus = null;
        if (oeeCategoryKey == Constants.INT_ZERO) {
            facilityOeeByAbnormalStatus = facilityOeeRepository.findTop1ByWorkCellIdAndFacilityIdIsNullAndAbnormalStatusStartTimeIsNotNullAndDeleted(workCellId, Constants.LONG_ZERO);
        } else if (oeeCategoryKey == Constants.INT_ONE) {
            facilityOeeByAbnormalStatus = facilityOeeRepository.findTop1ByWorkCellIdAndFacilityIdAndAbnormalStatusStartTimeIsNotNullAndDeleted(workCellId, facilityId, Constants.LONG_ZERO);
        }
        return facilityOeeByAbnormalStatus;
    }

    /**
     * 导入设备OEE
     *
     * @param file 导入文件
     */
    public void importTableExcel(MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams importParams = new ImportParams();
        List<FacilityOeeImportDTO> facilityOeeImportDTOList = ExcelImportUtil.importExcel(file.getInputStream(), FacilityOeeImportDTO.class, importParams);
        if (CollectionUtils.isEmpty(facilityOeeImportDTOList)) {
            return;
        }
        boolean existWorkStation = FuncKeyUtil.checkApi(FuncKeyConstants.WORK_STATION_FUNC_KEY);
        Map<String, LocalTime> workTimeMap = this.getWorkDayTime();
        LocalTime workStartTime = workTimeMap.get("start");
        LocalTime workEndTime = workTimeMap.get("end");
        List<FacilityOeeImportDTO> errorList = new ArrayList<>();
        facilityOeeImportDTOList.forEach(facilityOeeImportDTO -> {
            try {
                WorkCell workCell = workCellRepository.findByCodeAndDeleted(facilityOeeImportDTO.getWorkCellCode(), Constants.LONG_ZERO).orElse(null);
                if (Objects.isNull(workCell) || Objects.isNull(workCell.getId())) {
                    errorList.add(facilityOeeImportDTO.setMessage("工位不存在"));
                    return;
                }
                FacilityDTO facilityDTO = rbaseFacilityProxy.findByCodeAndDeleted(facilityOeeImportDTO.getFacilityCode(), Constants.LONG_ZERO);
                if (Objects.isNull(facilityDTO) || Objects.isNull(facilityDTO.getId())) {
                    errorList.add(facilityOeeImportDTO.setMessage("设备不存在"));
                    return;
                }
                /*List<EveryDayShiftDTO> everyDayShiftDTOList = new ArrayList<>();
                CalendarDTO calendarDTO = existWorkStation && Objects.nonNull(workCell.getWorkStation()) ? oeeCalendarProxy.findTop1ByWorkStationIdAndWorkLineIdNullAndWorkTimeAndDeleted(workCell.getWorkStation().getId(), facilityOeeImportDTO.getRecordDate(), Constants.LONG_ZERO) : oeeCalendarProxy.findTop1ByWorkLineIdAndWorkTimeAndWorkStationIdIsNullAndDeleted(workCell.getWorkLine().getId(), facilityOeeImportDTO.getRecordDate(), Constants.LONG_ZERO);
                if (ObjectUtils.isEmpty(calendarDTO)) {
                    errorList.add(facilityOeeImportDTO.setMessage("未配置生产日历"));
                    return;
                }
                // 通过实际结束时间和当日工作结束时间之间的距离进行正序排序，第一个为最晚时间
                everyDayShiftDTOList = calendarDTO.getShiftJsonList().stream().sorted(Comparator.comparing(i -> DateUtils.getWorkTime(i.getActualEndTime(), workEndTime))).collect(Collectors.toList());
                //异常停机开始时间
                LocalDateTime abnormalStatusStartTime = null;
                //获取最新异常停机开始时间不为空的OEE
                FacilityOee facilityOeeByAbnormalStatus = this.getFacilityOeeByAbnormalStatus(workCell.getId(), facilityDTO.getId());
                //2.1 获取实际工作时长
                Integer actualWorkDuration = everyDayShiftDTOList.stream().mapToInt(i -> DateUtils.getWorkTime(i.getActualStartTime(), i.getActualEndTime())).sum();*/
                FacilityOee facilityOee = facilityOeeRepository.findByWorkCellIdAndFacilityIdAndRecordDateAndDeleted(workCell.getId(), facilityDTO.getId(), facilityOeeImportDTO.getRecordDate(), Constants.LONG_ZERO).orElse(new FacilityOee());
                /*if (Objects.nonNull(facilityOee) && Objects.nonNull(facilityOee.getId()) && Objects.nonNull(facilityOee.getAbnormalStatusStartTime())) {
                    Integer downLineDuration = reloadDownLineDuration(facilityOee, calendarDTO.getShiftJsonList(), LocalDate.now(), workEndTime, Boolean.TRUE);
                    facilityOee.setDownLineDuration(facilityOee.getDownLineDuration() + downLineDuration);
                } else if (Objects.isNull(facilityOee.getId()) && Objects.nonNull(facilityOeeByAbnormalStatus) && facilityOeeByAbnormalStatus.getRecordDate().isBefore(facilityOeeImportDTO.getRecordDate())) {
                    abnormalStatusStartTime = facilityOeeImportDTO.getRecordDate().atTime(everyDayShiftDTOList.get(everyDayShiftDTOList.size() - 1).getActualStartTime());
                    facilityOee.setAbnormalStatusStartTime(abnormalStatusStartTime);
                    Integer downLineDuration = reloadDownLineDuration(facilityOee, calendarDTO.getShiftJsonList(), LocalDate.now(), workEndTime, Boolean.TRUE);
                    facilityOee.setDownLineDuration(facilityOee.getDownLineDuration() + downLineDuration);
                    //将旧OEE异常停机开始时间置为空
                    facilityOeeByAbnormalStatus.setAbnormalStatusStartTime(null);
                    facilityOeeRepository.save(facilityOeeByAbnormalStatus);
                }*/
                facilityOee.setWorkCell(workCell).setFacilityId(facilityDTO.getId())
                        .setFinishNumber(facilityOeeImportDTO.getFinishNumber())
                        .setQualifiedNumber(facilityOeeImportDTO.getQualifiedNumber()).setUnqualifiedNumber(facilityOeeImportDTO.getFinishNumber() - facilityOeeImportDTO.getQualifiedNumber())
                        .setRecordDate(facilityOeeImportDTO.getRecordDate())
                      //  .setAbnormalStatusStartTime(null)
                        //负荷时间(实际作业时间-计划停止时间)(s)
                       // .setLoadDuration(actualWorkDuration - facilityOee.getPlanDownDuration())
                        //稼动时间(负荷时间-停线时间)(s)
                       // .setActivationDuration(facilityOee.getLoadDuration() - facilityOee.getDownLineDuration())
                        .setDeleted(Constants.LONG_ZERO);
                FacilityCadence facilityCadence = facilityCadenceRepository.findByWorkCellIdAndFacilityIdAndDeleted(workCell.getId(), facilityDTO.getId(), Constants.LONG_ZERO);
                if (Objects.nonNull(facilityCadence)) {
                    int theoryRunTime = facilityCadence.getUnit() == Constants.INT_ZERO ? facilityCadence.getDuration().intValue() : facilityCadence.getUnit() == Constants.INT_ONE ? (facilityCadence.getDuration()).intValue() * 60 : facilityCadence.getDuration().intValue() * 3600;
                    facilityOee.setIdealWorkDuration(facilityOee.getFinishNumber() * theoryRunTime);
                }
                facilityOeeRepository.save(facilityOee);
//                FacilityLatestStatusDTO facilityLatestStatusDTO = rbaseFacilityProxy.findByFacilityIdAndDeleted(facilityOee.getFacilityId(), Constants.LONG_ZERO);
//                FacilityStatusChangeDTO facilityStatusChangeDTO = new FacilityStatusChangeDTO();
//                facilityStatusChangeDTO.setFacilityId(facilityOee.getFacilityId()).setOriginStatus(Objects.nonNull(facilityLatestStatusDTO) ? facilityLatestStatusDTO.getLatestStatus() : Constants.NEGATIVE_ONE).setTargetStatus(Constants.INT_ONE);
//                rbaseFacilityProxy.manualUpdate(facilityStatusChangeDTO);
            } catch (Exception e) {
                errorList.add(facilityOeeImportDTO.setMessage(e.getMessage()));
            }
        });
        if (ValidateUtils.isValid(errorList)) {
            ExportParams exportParams = new ExportParams();
            exportParams.setType(ExcelType.XSSF);
            exportParams.setFreezeCol(Constants.INT_TWO);
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, FacilityOeeImportDTO.class, errorList);
            String originalFilename = ValidateUtils.isValid(file.getOriginalFilename()) ? file.getOriginalFilename() : "设备OEE信息";
            response.setContentType(originalFilename.contains("xlsx") ? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" : "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(originalFilename, StandardCharsets.UTF_8));
            response.setStatus(HttpStatus.BAD_REQUEST.value());
            response.setHeader("X-app-alert", "app.import.failure");
            String errorMessage = "上传数据" + facilityOeeImportDTOList.size() + "条,导入成功" + (facilityOeeImportDTOList.size() - errorList.size()) + "条,导入失败" + errorList.size() + "条,请检查下载的文件,检查失败的详细原因";
            response.setHeader(HeaderUtil.APP_PARAMS, URLEncoder.encode(errorMessage, StandardCharsets.UTF_8));
            response.setHeader(HeaderUtil.APP_ERROR_MESSAGE, URLEncoder.encode(errorMessage, StandardCharsets.UTF_8));
            workbook.write(response.getOutputStream());
        }
    }

    /**
     * rworker生产时更新oee产量等数据
     *
     * @param workCellId
     * @param facilityIds
     * @param finishNumber
     * @param qualifiedNumber
     * @param unqualifiedNumber
     * @param recordDate
     */
    public void updateFacilityOeeProductionByRworker(Long workCellId, List<Long> facilityIds, Integer finishNumber, Integer qualifiedNumber, Integer unqualifiedNumber, LocalDate recordDate) {
        Map<String, LocalTime> workTimeMap = this.getWorkDayTime();
        LocalTime workStartTime = workTimeMap.get("start");
        LocalTime workEndTime = workTimeMap.get("end");
        boolean existWorkStation = FuncKeyUtil.checkApi(FuncKeyConstants.WORK_STATION_FUNC_KEY);
        WorkCell workCell = workCellRepository.getReferenceById(workCellId);
        List<EveryDayShiftDTO> everyDayShiftDTOList = new ArrayList<>();
        CalendarDTO calendarDTO = existWorkStation && Objects.nonNull(workCell.getWorkStation()) ? oeeCalendarProxy.findTop1ByWorkStationIdAndWorkLineIdNullAndWorkTimeAndDeleted(workCell.getWorkStation().getId(), recordDate, Constants.LONG_ZERO) : oeeCalendarProxy.findTop1ByWorkLineIdAndWorkTimeAndWorkStationIdIsNullAndDeleted(workCell.getWorkLine().getId(), recordDate, Constants.LONG_ZERO);
        if (ObjectUtils.isEmpty(calendarDTO)) {
            return;
        }
        // 通过实际结束时间和当日工作结束时间之间的距离进行正序排序，第一个为最晚时间
        everyDayShiftDTOList = calendarDTO.getShiftJsonList().stream().sorted(Comparator.comparing(i -> DateUtils.getWorkTime(i.getActualEndTime(), workEndTime))).collect(Collectors.toList());
        List<EveryDayShiftDTO> finalEveryDayShiftDTOList = everyDayShiftDTOList;
        List<EveryDayShiftDTO> finalEveryDayShiftDTOList1 = everyDayShiftDTOList;
        facilityIds.forEach(facilityId -> {
            //异常停机开始时间
            LocalDateTime abnormalStatusStartTime = null;
            //获取最新异常停机开始时间不为空的OEE
            FacilityOee facilityOeeByAbnormalStatus = this.getFacilityOeeByAbnormalStatus(workCell.getId(), facilityId);
            Integer actualWorkDuration = finalEveryDayShiftDTOList.stream().mapToInt(i -> DateUtils.getWorkTime(i.getActualStartTime(), i.getActualEndTime())).sum();
            FacilityOee facilityOee = facilityOeeRepository.findByWorkCellIdAndFacilityIdAndRecordDateAndDeleted(workCell.getId(), facilityId, recordDate, Constants.LONG_ZERO).orElse(new FacilityOee());
            if (Objects.nonNull(facilityOee) && Objects.nonNull(facilityOee.getId()) && Objects.nonNull(facilityOee.getAbnormalStatusStartTime())) {
                Integer downLineDuration = reloadDownLineDuration(facilityOee, calendarDTO.getShiftJsonList(), LocalDate.now(), workEndTime, Boolean.TRUE);
                facilityOee.setDownLineDuration(facilityOee.getDownLineDuration() + downLineDuration);
            } else if (Objects.isNull(facilityOee.getId()) && Objects.nonNull(facilityOeeByAbnormalStatus) && facilityOeeByAbnormalStatus.getRecordDate().isBefore(recordDate)) {
                abnormalStatusStartTime = recordDate.atTime(finalEveryDayShiftDTOList1.get(finalEveryDayShiftDTOList1.size() - 1).getActualStartTime());
                facilityOee.setAbnormalStatusStartTime(abnormalStatusStartTime);
                Integer downLineDuration = reloadDownLineDuration(facilityOee, calendarDTO.getShiftJsonList(), LocalDate.now(), workEndTime, Boolean.TRUE);
                facilityOee.setDownLineDuration(facilityOee.getDownLineDuration() + downLineDuration);
                //将旧OEE异常停机开始时间置为空
                facilityOeeByAbnormalStatus.setAbnormalStatusStartTime(null);
                facilityOeeRepository.save(facilityOeeByAbnormalStatus);
            }
            //2.1 获取实际工作时长

            facilityOee.setWorkCell(workCell).setFacilityId(facilityId)
                    .setFinishNumber(facilityOee.getFinishNumber() + finishNumber)
                    .setQualifiedNumber(facilityOee.getQualifiedNumber() + qualifiedNumber).setUnqualifiedNumber(facilityOee.getUnqualifiedNumber() + unqualifiedNumber)
                    .setRecordDate(recordDate)
                    .setAbnormalStatusStartTime(abnormalStatusStartTime)
                    //负荷时间(实际作业时间-计划停止时间)(s)
                    .setLoadDuration(actualWorkDuration - facilityOee.getPlanDownDuration())
                    //稼动时间(负荷时间-停线时间)(s)
                    .setActivationDuration(facilityOee.getLoadDuration() - facilityOee.getDownLineDuration())
                    .setDeleted(Constants.LONG_ZERO);
            FacilityCadence facilityCadence = facilityCadenceRepository.findByWorkCellIdAndFacilityIdAndDeleted(workCell.getId(), facilityId, Constants.LONG_ZERO);
            if (Objects.nonNull(facilityCadence)) {
                int theoryRunTime = facilityCadence.getUnit() == Constants.INT_ZERO ? facilityCadence.getDuration().intValue() : facilityCadence.getUnit() == Constants.INT_ONE ? (facilityCadence.getDuration()).intValue() * 60 : facilityCadence.getDuration().intValue() * 3600;
                facilityOee.setIdealWorkDuration(facilityOee.getFinishNumber() * theoryRunTime);
            }
            facilityOeeRepository.save(facilityOee);
            FacilityLatestStatusDTO facilityLatestStatusDTO = rbaseFacilityProxy.findByFacilityIdAndDeleted(facilityOee.getFacilityId(), Constants.LONG_ZERO);
            FacilityStatusChangeDTO facilityStatusChangeDTO = new FacilityStatusChangeDTO();
            facilityStatusChangeDTO.setFacilityId(facilityOee.getFacilityId()).setOriginStatus(Objects.nonNull(facilityLatestStatusDTO) ? facilityLatestStatusDTO.getLatestStatus() : Constants.NEGATIVE_ONE).setTargetStatus(Constants.INT_ONE);
            rbaseFacilityProxy.manualUpdate(facilityStatusChangeDTO);
        });


    }


    /**
     * 状态转换成前端的枚举类型
     *
     * @param status
     * @return
     */
    private int convertStatus(int status) {
        if (status == Constants.INT_ONE) {
            status = Constants.INT_ZERO;
        } else if (status == Constants.INT_TWO) {
            status = Constants.INT_ONE;
        } else if (status == Constants.INT_THREE) {
            status = Constants.INT_TWO;
        } else if (status == Constants.INT_FOUR) {
            status = Constants.INT_TWO;
        } else if (status == Constants.INT_SIX) {
            status = Constants.INT_THREE;
        } else if (status >= Constants.INT_SEVEN) {
            status = Constants.INT_THREE;
        } else if (status == Constants.NEGATIVE_ONE) {
            status = Constants.INT_THREE;
        } else if (status == Constants.INT_ZERO) {
            status = Constants.INT_TWO;
        }
        return status;
    }


}
