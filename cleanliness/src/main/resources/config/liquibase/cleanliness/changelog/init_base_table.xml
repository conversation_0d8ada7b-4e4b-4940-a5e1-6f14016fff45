<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="RMPC (generated)" id="1714012348314-1">
        <createTable remarks="洁净度等级" tableName="base_cleanliness_grade">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="code" remarks="等级编码" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="name" remarks="等级名称" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714012348314-2">
        <createTable remarks="洁净度粒子半径" tableName="base_cleanliness_particles_radius">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="code" remarks="粒径编码" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="name" remarks="粒径名称" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714012348314-3">
        <createTable remarks="洁净度检测标准表" tableName="base_cleanliness_standard">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column defaultValueNumeric="3" name="test_times" remarks="测试次数" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="period_number" remarks="周期数值" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="1" name="period_unit" remarks="周期单位(0:小时，1:天，2:周，3:月，4:年)" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="abnormal_id" remarks="异常配置ID" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="area_id" remarks="部门区域ID" type="BIGINT"/>
            <column defaultValueNumeric="0" name="warn_duration" remarks="提醒数值" type="TINYINT(3)"/>
            <column defaultValueNumeric="0" name="warn_unit" remarks="提醒周期" type="TINYINT(3)"/>
            <column name="particles_radius" remarks="离子半径" type="JSON">
                <constraints nullable="false"/>
            </column>
            <column name="particles_number" remarks="洁净度值" type="JSON">
                <constraints nullable="false"/>
            </column>
            <column name="cleanliness_grade_id" remarks="洁净度等级" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714012348314-4">
        <createTable remarks="洁净度检测历史表" tableName="procedure_cleanliness_check_history">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="result" remarks="结果（0：不合格, 1：合格）" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="operator_id" remarks="检验人ID" type="BIGINT"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="record_date" remarks="检验日期" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="area_id" remarks="部门区域ID" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714012348314-5">
        <createTable remarks="洁净度检测历史表详情" tableName="procedure_cleanliness_check_history_detail">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="history_id" remarks="洁净度检测历史ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="record" remarks="检测记录，多个检测记录则用逗号,分割" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="average" remarks="平均值" type="DECIMAL(10, 4)"/>
            <column name="result" remarks="结果(0：不合格,1：合格)" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714012348314-6">
        <createTable remarks="最新环境检测结果表" tableName="procedure_latest_cleanliness_check_result">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="temperature" type="DECIMAL(10, 1)"/>
            <column name="humidity" type="DECIMAL(10, 1)"/>
            <column name="result" remarks="结果（0：停线, 1：合格，2：预警）" type="INT"/>
            <column name="latest_operator_id" remarks="最新检测人ID" type="BIGINT"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="latest_inspect_date" remarks="最新检测日期" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="next_inspect_date" remarks="下次检测日期" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column name="area_id" remarks="部门区域ID" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714012348314-7">
        <addUniqueConstraint columnNames="code, deleted" constraintName="base_cleanliness_grade_unique" tableName="base_cleanliness_grade"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714012348314-8">
        <addUniqueConstraint columnNames="code, deleted" constraintName="base_cleanliness_particles_radius_code_unique" tableName="base_cleanliness_particles_radius"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714012348314-9">
        <addUniqueConstraint columnNames="name, deleted" constraintName="base_cleanliness_particles_radius_name_unique" tableName="base_cleanliness_particles_radius"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714012348314-10">
        <addUniqueConstraint columnNames="area_id, deleted" constraintName="base_cleanliness_standard_unique" tableName="base_cleanliness_standard"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714012348314-11">
        <addUniqueConstraint columnNames="area_id, deleted" constraintName="procedure_latest_enviroment_check_result_unique" tableName="procedure_latest_cleanliness_check_result"/>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714012348314-12">
        <createIndex associatedWith="" indexName="base_cleanliness_grade_code_index" tableName="base_cleanliness_grade">
            <column name="code"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714012348314-13">
        <createIndex associatedWith="" indexName="base_cleanliness_grade_name_index" tableName="base_cleanliness_grade">
            <column name="name"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714012348314-14">
        <createIndex associatedWith="" indexName="base_cleanliness_particles_radius_code_index" tableName="base_cleanliness_particles_radius">
            <column name="code"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714012348314-15">
        <createIndex associatedWith="" indexName="base_cleanliness_particles_radius_name_index" tableName="base_cleanliness_particles_radius">
            <column name="name"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714012348314-16">
        <createIndex associatedWith="" indexName="base_cleanliness_standard_area_id_index" tableName="base_cleanliness_standard">
            <column name="area_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714012348314-17">
        <createIndex associatedWith="" indexName="procedure_cleanliness_check_history_area_id_index" tableName="procedure_cleanliness_check_history">
            <column name="area_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714012348314-18">
        <createIndex associatedWith="" indexName="procedure_cleanliness_check_history_detail_history_id_index" tableName="procedure_cleanliness_check_history_detail">
            <column name="history_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="RMPC (generated)" id="1714012348314-19">
        <createIndex associatedWith="" indexName="procedure_latest_enviroment_check_result_area_id_index" tableName="procedure_latest_cleanliness_check_result">
            <column name="area_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
