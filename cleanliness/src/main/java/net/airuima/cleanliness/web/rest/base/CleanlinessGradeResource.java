package net.airuima.cleanliness.web.rest.base;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.cleanliness.domain.base.CleanlinessGrade;
import net.airuima.cleanliness.service.base.CleanlinessGradeService;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 洁净度等级 Resource
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
@Tag(name = "洁净度等级Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/cleanliness-grades")
@AuthorityRegion("环境管控")
@FuncInterceptor("EnvCleanliness")
public class CleanlinessGradeResource extends ProtectBaseResource<CleanlinessGrade> {

    public static final String MODULE = "洁净度等级";

    public CleanlinessGradeResource() {
        this.mapUri = "/api/cleanliness-grades";
    }

    @Autowired
    private CleanlinessGradeService cleanlinessGradeService;

    /**
     * 通过名称和编码 模糊查询 洁净度等级
     *
     * @param text 名称或编码
     * @param size 查询数量
     * @return List
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过名称和编码模糊查询洁净度等级")
    @GetMapping("/by-name-or-code")
    public ResponseEntity<ResponseData<List<CleanlinessGrade>>> findByNameOrCode(@RequestParam(value = "text") String text, @RequestParam(value = "size") Integer size) {
        return ResponseData.ok(cleanlinessGradeService.findByCodeOrName(text, size));
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }
}
