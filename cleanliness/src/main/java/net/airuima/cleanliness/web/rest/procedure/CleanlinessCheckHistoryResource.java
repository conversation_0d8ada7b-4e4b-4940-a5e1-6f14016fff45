package net.airuima.cleanliness.web.rest.procedure;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import net.airuima.cleanliness.domain.procedure.CleanlinessCheckHistory;
import net.airuima.cleanliness.domain.procedure.CleanlinessCheckHistoryDetail;
import net.airuima.cleanliness.service.procedure.CleanlinessCheckHistoryDetailService;
import net.airuima.cleanliness.service.procedure.CleanlinessCheckHistoryService;
import net.airuima.cleanliness.service.procedure.api.ICleanlinessCheckHistoryService;
import net.airuima.cleanliness.web.rest.procedure.dto.CleanlinessCheckHistorySaveDTO;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.net.URISyntaxException;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 洁净度检测历史表Resource
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Tag(name = "洁净度检测历史表Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/cleanliness-check-histories")
@AuthorityRegion("环境管控")
@FuncInterceptor("EnvCleanliness")
@AuthSkip("I")
public class CleanlinessCheckHistoryResource extends ProtectBaseResource<CleanlinessCheckHistory> {

    @Autowired
    private CleanlinessCheckHistoryService cleanlinessCheckHistoryService;
    @Autowired
    private CleanlinessCheckHistoryDetailService cleanlinessCheckHistoryDetailService;
    @Autowired
    private ICleanlinessCheckHistoryService[] cleanlinessCheckHistoryServices;

    /**
     * 新增洁净检测历史
     *
     * @param saveDto 洁净度检测历史DTO
     * @return ResponseEntity<ResponseData<CleanlinessCheckHistory>>
     * <AUTHOR>
     * @date 2022-06-24
     **/
    @Operation(summary = "新增洁净检测历史")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping("/custom")
    public ResponseEntity<ResponseData<CleanlinessCheckHistory>> create(@Valid @RequestBody CleanlinessCheckHistorySaveDTO saveDto) throws URISyntaxException {
        try {
            cleanlinessCheckHistoryServices[0].createCleanlinessCheckHistory(saveDto);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 通过洁净度检测历史ID查询详情列表
     *
     * @param historyId 洁净度检测历史主表ID
     * @return ResponseEntity<List < CleanlinessCheckHistoryDetail>>
     * <AUTHOR>
     * @date 2022-06-24
     **/
    @Operation(summary = "通过洁净度检测历史ID查询详情列表")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @GetMapping("/{historyId}/details")
    public ResponseEntity<ResponseData<CleanlinessCheckHistoryDetail>> findDetailsByHistoryId(@PathVariable("historyId") Long historyId) {
        try {
            CleanlinessCheckHistoryDetail cleanlinessCheckHistoryDetail = cleanlinessCheckHistoryDetailService.findDetailsByHistoryId(historyId);
            return ResponseData.ok(cleanlinessCheckHistoryDetail);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            return ResponseData.error(e);
        }
    }



    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览洁净度检测历史";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建洁净度检测历史";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改洁净度检测历史";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除洁净度检测历史";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_IMPORT)) {
            return "导入洁净度检测历史";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出洁净度检测历史";
        }
        return "";
    }

}
