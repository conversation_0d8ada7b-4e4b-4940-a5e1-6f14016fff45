package net.airuima.cleanliness.web.rest.base.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;

import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 洁净度检测标准表 导出导入DTO
 *
 * <AUTHOR>
 * @date 2023/5/12 10:40
 */
public class CleanlinessStandardExcelDTO {

    @Excel(name = "区域编码")
    private String areaCode;

    @Excel(name = "洁净等级")
    private String grade;

    @Excel(name = "测试次数")
    private Integer testTimes;

    @Excel(name = "周期数值")
    private Integer periodNumber;

    @Excel(name = "周期单位")
    private PeriodUnitEnum periodUnitEnum;

    @ExcelCollection(name = "粒径最大浓度限制")
    private List<Particle> particleList;

    public static class Particle {

        @Excel(name = "粒子半径/(um)")
        private String code;

        @Excel(name = "洁净度值/(pc/m)")
        private Integer particlesNumber;

        public String getCode() {
            return code;
        }

        public Particle setCode(String code) {
            this.code = code;
            return this;
        }

        public Integer getParticlesNumber() {
            return particlesNumber;
        }

        public void setParticlesNumber(Integer particlesNumber) {
            this.particlesNumber = particlesNumber;
        }
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }

    public Integer getTestTimes() {
        return testTimes;
    }

    public void setTestTimes(Integer testTimes) {
        this.testTimes = testTimes;
    }

    public Integer getPeriodNumber() {
        return periodNumber;
    }

    public void setPeriodNumber(Integer periodNumber) {
        this.periodNumber = periodNumber;
    }

    public String getPeriodUnitEnum() {
        return periodUnitEnum != null ? periodUnitEnum.getKey() : null;
    }

    public void setPeriodUnitEnum(String periodUnit) {
        this.periodUnitEnum = PeriodUnitEnum.getByKey(periodUnit);
    }

    public List<Particle> getParticleList() {
        return particleList;
    }

    public void setParticleList(List<Particle> particleList) {
        this.particleList = particleList;
    }

    /**
     * 周期单位枚举
     **/
    public enum PeriodUnitEnum {
        HOUR("小时", 0),
        DAY("天", 1),
        WEEK("周", 2),
        MONTH("月", 3),
        YEAR("年", 4);

        /**
         * 周期单位key
         */
        private String key;

        /**
         * 周期单位value
         */
        private int value;

        PeriodUnitEnum(String key, int value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return key;
        }

        public int getValue() {
            return value;
        }

        /**
         * 根据Key，返回枚举
         **/
        public static PeriodUnitEnum getByKey(String key) {
            PeriodUnitEnum result = null;
            for (PeriodUnitEnum value : values()) {
                if (value.getKey().equals(key)) {
                    result = value;
                    break;
                }
            }
            return result;
        }

        /**
         * 根据value，返回枚举
         **/
        public static PeriodUnitEnum getByValue(int value) {
            PeriodUnitEnum result = null;
            for (PeriodUnitEnum unitEnum : values()) {
                if (unitEnum.getValue() == value) {
                    result = unitEnum;
                    break;
                }
            }
            return result;
        }
    }
}
