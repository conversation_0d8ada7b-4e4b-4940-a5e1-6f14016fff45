package net.airuima.cleanliness.web.rest.procedure;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.cleanliness.domain.procedure.LatestCleanlinessCheckResult;
import net.airuima.cleanliness.service.procedure.LatestCleanlinessCheckResultService;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 最新洁净度检测结果Resource
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Tag(name = "最新洁净度检测结果Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/latest-cleanliness-check-results")
@AuthorityRegion("环境管控")
@FuncInterceptor("EnvCleanliness || EnvHumiture")
@AuthSkip("ICUD")
public class LatestCleanlinessCheckResultResource extends ProtectBaseResource<LatestCleanlinessCheckResult> {

    private final LatestCleanlinessCheckResultService latestCleanlinessCheckResultService;

    public LatestCleanlinessCheckResultResource(LatestCleanlinessCheckResultService latestCleanlinessCheckResultService) {
        this.latestCleanlinessCheckResultService = latestCleanlinessCheckResultService;
        this.mapUri = "/api/latest-cleanliness-check-results";
    }

    /**
     * Rworker验证工位区域洁净度合规性
     * @param workCellId 工位ID
     */
    @PreAuthorize("@sc.checkSecurity()")
    @PostMapping("/rworker/validate/{workCellId}")
    public ResponseEntity<ResponseData<Void>> validate(@PathVariable("workCellId") Long workCellId){
        try{
            latestCleanlinessCheckResultService.validate(workCellId);
        }catch (ResponseException e){
            return ResponseData.error(e.getErrorKey(),e.getErrorMessage());
        }
        return ResponseData.ok();
    }

    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览最新洁净度检测结果";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建最新洁净度检测结果";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改最新洁净度检测结果";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除最新洁净度检测结果";
        }else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_IMPORT)) {
            return "导入最新洁净度检测结果";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出最新洁净度检测结果";
        }
        return "";
    }

}
