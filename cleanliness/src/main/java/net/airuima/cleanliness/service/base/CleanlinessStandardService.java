package net.airuima.cleanliness.service.base;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import com.google.common.collect.Lists;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.cleanliness.domain.base.CleanlinessGrade;
import net.airuima.cleanliness.domain.base.CleanlinessParticlesRadius;
import net.airuima.cleanliness.domain.base.CleanlinessStandard;
import net.airuima.cleanliness.repository.base.CleanlinessGradeRepository;
import net.airuima.cleanliness.repository.base.CleanlinessParticlesRadiusRepository;
import net.airuima.cleanliness.repository.base.CleanlinessStandardRepository;
import net.airuima.cleanliness.web.rest.base.dto.CleanlinessStandardExcelDTO;
import net.airuima.dto.ExportDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.scene.OrganizationArea;
import net.airuima.rbase.repository.base.scene.OrganizationAreaRepository;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.SpreadsheetVersion;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 洁净度检测标准Service
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class CleanlinessStandardService extends CommonJpaService<CleanlinessStandard> {

    private final String CLEAN_LINESS_STANDARD_ENTITY_GRAPH = "cleanlinessStandardEntityGraph";
    @Autowired
    private OrganizationAreaRepository organizationAreaRepository;
    @Autowired
    private CleanlinessStandardRepository cleanlinessStandardRepository;
    @Autowired
    private CleanlinessGradeRepository cleanlinessGradeRepository;
    @Autowired
    private CleanlinessParticlesRadiusRepository cleanlinessParticlesRadiusRepository;

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<CleanlinessStandard> find(Specification<CleanlinessStandard> spec, Pageable pageable) {
        return cleanlinessStandardRepository.findAll(spec, pageable,new NamedEntityGraph(CLEAN_LINESS_STANDARD_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public List<CleanlinessStandard> find(Specification<CleanlinessStandard> spec) {
        return cleanlinessStandardRepository.findAll(spec,new NamedEntityGraph(CLEAN_LINESS_STANDARD_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<CleanlinessStandard> findAll(Pageable pageable) {
        return cleanlinessStandardRepository.findAll(pageable,new NamedEntityGraph(CLEAN_LINESS_STANDARD_ENTITY_GRAPH));
    }

    /**
     * 通过 区域ID 和 删除状态，查询 洁净度标准
     *
     * @param areaId 区域ID
     * @return CleanlinessStandard
     */
    @Transactional(readOnly = true)
    public CleanlinessStandard findByAreaId(Long areaId) {
        Optional<CleanlinessStandard> cleanlinessStandardOptional = cleanlinessStandardRepository.findByAreaIdAndDeleted(areaId, Constants.LONG_ZERO);
        if (cleanlinessStandardOptional.isEmpty()) {
            throw new ResponseException("error.CleanlinessStandardNotExist", "当前部门未配置洁净度标准");
        }
        return cleanlinessStandardOptional.get();
    }

    /**
     * 洁净度检测标准表 导出
     *
     * @param excelTitle           excel标题
     * @param cleanlinessStandards 洁净度检测标准数据列表
     * @param response             response
     */
    public void exportTableExcel(ExportDTO exportDTO, List<CleanlinessStandard> cleanlinessStandards, HttpServletResponse response) throws IOException {
        List<CleanlinessStandardExcelDTO> cleanlinessStandardExcelDTOList = Lists.newArrayList();
        if(!CollectionUtils.isEmpty(cleanlinessStandards)) {
            cleanlinessStandards.forEach(cleanlinessStandard -> {
                CleanlinessStandardExcelDTO cleanlinessStandardExcelDTO = new CleanlinessStandardExcelDTO();
                cleanlinessStandardExcelDTO.setAreaCode(cleanlinessStandard.getArea().getCode());
                cleanlinessStandardExcelDTO.setGrade(cleanlinessStandard.getCleanlinessGrade().getCode());
                cleanlinessStandardExcelDTO.setTestTimes(cleanlinessStandard.getTestTimes());
                cleanlinessStandardExcelDTO.setPeriodNumber(cleanlinessStandard.getPeriodNumber());
                cleanlinessStandardExcelDTO.setPeriodUnitEnum(
                        CleanlinessStandardExcelDTO.PeriodUnitEnum.getByValue(cleanlinessStandard.getPeriodUnit()).getKey()
                );

                List<CleanlinessStandardExcelDTO.Particle> particleList = Lists.newArrayList();

                for (int index = 0; index < cleanlinessStandard.getParticlesRadiusList().size(); index++) {
                    CleanlinessStandardExcelDTO.Particle particle = new CleanlinessStandardExcelDTO.Particle();
                    particle.setParticlesNumber(cleanlinessStandard.getParticlesNumberList().get(index));
                    particle.setCode(cleanlinessStandard.getParticlesRadiusList().get(index));
                    particleList.add(particle);
                }
                cleanlinessStandardExcelDTO.setParticleList(particleList);
                cleanlinessStandardExcelDTOList.add(cleanlinessStandardExcelDTO);
            });
        }
        ExportParams exportParams = new ExportParams(null, exportDTO.getExcelTitle(), ExcelType.XSSF);
        exportParams.setMaxNum(SpreadsheetVersion.EXCEL2007.getLastRowIndex());
        if(StringUtils.isNotBlank(exportDTO.getExcelType()) && exportDTO.getExcelType().equals("xls")){
            exportParams = new ExportParams(null, exportDTO.getExcelTitle(), ExcelType.HSSF);
            exportParams.setMaxNum(SpreadsheetVersion.EXCEL97.getLastRowIndex());
        }
        String prefix = StringUtils.isNotBlank(exportDTO.getExcelType()) && exportDTO.getExcelType().equals("xls")?".xls":".xlsx";
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, CleanlinessStandardExcelDTO.class, cleanlinessStandardExcelDTOList);
        String fileName = URLEncoder.encode(exportDTO.getExcelTitle() +prefix, StandardCharsets.UTF_8);
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);
        response.setHeader("message", "export!");
        ServletOutputStream out = response.getOutputStream();
        workbook.write(out);
        workbook.close();
        out.close();
    }

    /**
     * excel 导入
     *
     * @param cleanlinessStandardExcelDtoList 洁净度检测标准表 导入列表
     */
    public void importTableExcel(List<CleanlinessStandardExcelDTO> cleanlinessStandardExcelDtoList) {
        if (cleanlinessStandardExcelDtoList.isEmpty()) {
            return;
        }
        List<OrganizationArea> organizationAreaList = Lists.newArrayList();
        List<CleanlinessGrade> cleanlinessGradeList = Lists.newArrayList();
        // 验证 导入的参数信息
        verifyImportExcel(organizationAreaList, cleanlinessGradeList, cleanlinessStandardExcelDtoList);
        cleanlinessStandardExcelDtoList.forEach(cleanlinessStandardExcelDTO -> {
            OrganizationArea organizationAreaTemp = organizationAreaRepository.findByCodeAndDeleted(cleanlinessStandardExcelDTO.getAreaCode(), Constants.LONG_ZERO).orElse(null);
            if(Objects.isNull(organizationAreaTemp)){
                throw new ResponseException("error.organizationAreaNotExist",cleanlinessStandardExcelDTO.getAreaCode()+"对应的区域不存在");
            }
            CleanlinessStandard cleanlinessStandard = cleanlinessStandardRepository.findByAreaCodeAndDeleted(cleanlinessStandardExcelDTO.getAreaCode(), Constants.LONG_ZERO).orElse(null);
            // 粒子半径列表
            List<String> particlesRadiusCodeList = cleanlinessStandardExcelDTO.getParticleList().stream().map(CleanlinessStandardExcelDTO.Particle::getCode).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(particlesRadiusCodeList)){
                particlesRadiusCodeList.forEach(particlesRadius->{
                    List<CleanlinessParticlesRadius> cleanlinessParticlesRadiusList = cleanlinessParticlesRadiusRepository.findByNameAndDeleted(particlesRadius,Constants.LONG_ZERO);
                    if(CollectionUtils.isEmpty(cleanlinessParticlesRadiusList)){
                        throw new ResponseException("error.cleanlinessParticlesRadiusNotExist",particlesRadius+"对应的粒子半径不存在");
                    }
                });
            }
            // 洁净度值列表
            List<Integer> particlesNumberList = cleanlinessStandardExcelDTO.getParticleList().stream().map(CleanlinessStandardExcelDTO.Particle::getParticlesNumber).collect(Collectors.toList());
            // 新增
            if (cleanlinessStandard == null) {
                cleanlinessStandard = new CleanlinessStandard();
                OrganizationArea area = organizationAreaList.stream()
                        .filter(organizationArea -> Objects.equals(cleanlinessStandardExcelDTO.getAreaCode(), organizationArea.getCode()))
                        .findFirst().orElse(null);
                cleanlinessStandard.setArea(area);
            }
            Optional<CleanlinessGrade> cleanlinessGradeOptional = cleanlinessGradeList.stream().filter(cleanlinessGrade -> Objects.equals(cleanlinessStandardExcelDTO.getGrade(), cleanlinessGrade.getCode())).findFirst();
            if(cleanlinessGradeOptional.isEmpty()){
                throw new ResponseException("error.cleanlinessGradeNotExist",cleanlinessStandardExcelDTO.getGrade()+"对应的洁净度等级不存在");
            }
            cleanlinessGradeOptional.ifPresent(cleanlinessStandard::setCleanlinessGrade);
            cleanlinessStandard.setTestTimes(cleanlinessStandardExcelDTO.getTestTimes())
                    .setPeriodNumber(cleanlinessStandardExcelDTO.getPeriodNumber())
                    .setPeriodUnit(CleanlinessStandardExcelDTO.PeriodUnitEnum.getByKey(cleanlinessStandardExcelDTO.getPeriodUnitEnum()).getValue())
                    .setParticlesRadiusList(particlesRadiusCodeList)
                    .setParticlesNumberList(particlesNumberList);
            cleanlinessStandardRepository.save(cleanlinessStandard);
        });
    }

    /**
     * 验证 导入的参数信息
     *
     * @param organizationAreaList            区域列表
     * @param cleanlinessGradeList            洁净度等级列表
     * @param cleanlinessStandardExcelDtoList excel
     */
    private void verifyImportExcel(List<OrganizationArea> organizationAreaList, List<CleanlinessGrade> cleanlinessGradeList, List<CleanlinessStandardExcelDTO> cleanlinessStandardExcelDtoList) {
        for (CleanlinessStandardExcelDTO cleanlinessStandardExcelDTO : cleanlinessStandardExcelDtoList) {
            if (StringUtils.isBlank(cleanlinessStandardExcelDTO.getPeriodUnitEnum())) {
                throw new ResponseException("error.periodUnitNotExist", "周期单位不存在");
            }
            String areaCode = cleanlinessStandardExcelDTO.getAreaCode();
            String grade = cleanlinessStandardExcelDTO.getGrade();
            OrganizationArea organizationArea = organizationAreaRepository.findByCodeAndDeleted(areaCode, Constants.LONG_ZERO).orElseThrow(() ->
                    new ResponseException("error.organizationAreaNotExist", "区域不存在"));
            organizationAreaList.add(organizationArea);
            CleanlinessGrade cleanlinessGrade = cleanlinessGradeRepository.findByCodeAndDeleted(grade, Constants.LONG_ZERO).orElseThrow(() ->
                    new ResponseException("error.CleanlinessGradeNotExist", "洁净度等级不存在"));
            cleanlinessGradeList.add(cleanlinessGrade);
        }
    }

    @Override
    public CleanlinessStandard save(CleanlinessStandard entity) {
        long count = entity.getParticlesRadiusList().stream().distinct().count();
        if (count != entity.getParticlesRadiusList().size()) {
            throw new ResponseException("error.ParticlesRadiusRepeat", "粒子半径存在重复");
        }
        return super.save(entity);
    }
}
