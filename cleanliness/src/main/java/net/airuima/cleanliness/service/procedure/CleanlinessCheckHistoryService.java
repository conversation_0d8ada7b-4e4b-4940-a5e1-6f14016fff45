package net.airuima.cleanliness.service.procedure;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.cleanliness.domain.base.CleanlinessStandard;
import net.airuima.cleanliness.domain.procedure.CleanlinessCheckHistory;
import net.airuima.cleanliness.domain.procedure.CleanlinessCheckHistoryDetail;
import net.airuima.cleanliness.repository.base.CleanlinessStandardRepository;
import net.airuima.cleanliness.repository.procedure.CleanlinessCheckHistoryDetailRepository;
import net.airuima.cleanliness.repository.procedure.CleanlinessCheckHistoryRepository;
import net.airuima.cleanliness.web.rest.procedure.dto.CleanlinessCheckHistorySaveDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.scene.OrganizationArea;
import net.airuima.rbase.repository.base.scene.OrganizationAreaRepository;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 洁净度检测历史Service
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class CleanlinessCheckHistoryService extends CommonJpaService<CleanlinessCheckHistory> {

    private final String CLEANl_LINESS_CHECK_HISTORY_ENRITY_GRAPH = "cleanlinessCheckHistoryEntityGraph";
    @Autowired
    private CleanlinessCheckHistoryRepository cleanlinessCheckHistoryRepository;
    @Autowired
    private CleanlinessCheckHistoryDetailRepository cleanlinessCheckHistoryDetailRepository;
    @Autowired
    private CleanlinessStandardRepository cleanlinessStandardRepository;
    @Autowired
    private LatestCleanlinessCheckResultService latestCleanlinessCheckResultService;
    @Autowired
    private OrganizationAreaRepository organizationAreaRepository;

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<CleanlinessCheckHistory> find(Specification<CleanlinessCheckHistory> spec, Pageable pageable) {
        return cleanlinessCheckHistoryRepository.findAll(spec, pageable,new NamedEntityGraph(CLEANl_LINESS_CHECK_HISTORY_ENRITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<CleanlinessCheckHistory> find(Specification<CleanlinessCheckHistory> spec) {
        return cleanlinessCheckHistoryRepository.findAll(spec,new NamedEntityGraph(CLEANl_LINESS_CHECK_HISTORY_ENRITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<CleanlinessCheckHistory> findAll(Pageable pageable) {
        return cleanlinessCheckHistoryRepository.findAll(pageable,new NamedEntityGraph(CLEANl_LINESS_CHECK_HISTORY_ENRITY_GRAPH));
    }

    /**
     * 新增洁净度度检测历史
     *
     * @param saveDto 洁净度检测历史参数
     * <AUTHOR>
     * @date 2022-06-24
     **/
    public CleanlinessCheckHistory create(CleanlinessCheckHistorySaveDTO saveDto) {
        // 获取洁净度检测标准
        CleanlinessStandard cleanlinessStandard = cleanlinessStandardRepository.findByAreaIdAndDeleted(saveDto.getAreaId(), Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("error.CleanlinessStandardNotExist", "洁净度检测标准不存在"));
        // 获取区域
        OrganizationArea area = organizationAreaRepository.findByIdAndDeleted(saveDto.getAreaId(), Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("error.organizationAreaNotExist", "区域信息不存在"));
        // 洁净度值
        List<Integer> particlesNumberList = cleanlinessStandard.getParticlesNumberList();
        // 粒子半径
        List<String> particlesRadiusList = cleanlinessStandard.getParticlesRadiusList();
        // 保存洁净度检测历史对象
        CleanlinessCheckHistory cleanlinessCheckHistory = new CleanlinessCheckHistory();
        cleanlinessCheckHistory.setArea(area);
        cleanlinessCheckHistory.setOperatorId(saveDto.getOperatorId());
        cleanlinessCheckHistory.setRecordDate(LocalDateTime.now());
        // 保存洁净度检测历史详情对象
        CleanlinessCheckHistoryDetail detail = new CleanlinessCheckHistoryDetail();
        detail.setRecord(saveDto.getRecord());
        detail.setCustom1(JSON.toJSONString(particlesRadiusList));
        detail.setResult(Boolean.TRUE);
        List<List<BigDecimal>> lists = JSON.parseObject(detail.getRecord(), new TypeReference<List<List<BigDecimal>>>() {
        });
        for (List<BigDecimal> list : lists) {
            for (int index = Constants.INT_ZERO; index < particlesNumberList.size(); index++) {
                // 如果 输入的数值 > 标准值，则不合格
                if (list.get(index).doubleValue() > particlesNumberList.get(index)) {
                    // 不合格
                    detail.setResult(Boolean.FALSE);
                    break;
                }
            }
        }
        cleanlinessCheckHistory.setResult(detail.getResult());
        cleanlinessCheckHistoryRepository.save(cleanlinessCheckHistory);
        // 保存详情
        detail.setHistory(cleanlinessCheckHistory);
        cleanlinessCheckHistoryDetailRepository.save(detail);
        // 更新最新环境检测结果表
        latestCleanlinessCheckResultService.updateFinal(cleanlinessCheckHistory);
        return cleanlinessCheckHistory;
    }
}
