package net.airuima.cleanliness.service.base;


import net.airuima.cleanliness.domain.base.CleanlinessGrade;
import net.airuima.cleanliness.repository.base.CleanlinessGradeRepository;
import net.airuima.rbase.constant.Constants;
import net.airuima.service.CommonJpaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 洁净度等级 Service
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class CleanlinessGradeService extends CommonJpaService<CleanlinessGrade> {

    @Autowired
    private CleanlinessGradeRepository cleanlinessGradeRepository;

    @Override
    public Page<CleanlinessGrade> find(Specification<CleanlinessGrade> spec, Pageable pageable) {
        return cleanlinessGradeRepository.findAll(spec, pageable);
    }

    @Override
    public List<CleanlinessGrade> find(Specification<CleanlinessGrade> spec) {
        return cleanlinessGradeRepository.findAll(spec);
    }

    @Override
    public Page<CleanlinessGrade> findAll(Pageable pageable) {
        return cleanlinessGradeRepository.findAll(pageable);
    }

    /**
     * 通过名称和编码 模糊查询 洁净度等级
     *
     * @param text 名称或编码
     * @param size 查询数量
     * @return List
     */
    public List<CleanlinessGrade> findByCodeOrName(String text, Integer size) {
        Page<CleanlinessGrade> page = cleanlinessGradeRepository.findByNameOrCode(text, PageRequest.of(Constants.INT_ZERO, size));
        return Optional.ofNullable(page).map(Slice::getContent).orElse(null);
    }
}
