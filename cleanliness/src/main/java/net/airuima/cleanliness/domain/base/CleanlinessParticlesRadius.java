package net.airuima.cleanliness.domain.base;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 洁净度粒子半径 Domain
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
@Schema(name = "洁净度等级(CleanlinessParticlesRadius)", description = "洁净度粒子半径")
@Entity
@Table(name = "base_cleanliness_particles_radius", uniqueConstraints = {
        @UniqueConstraint(name = "base_cleanliness_particles_radius_unique", columnNames = {"code", "deleted"})
})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "洁净度粒子半径")
public class CleanlinessParticlesRadius extends CustomBaseEntity implements Serializable {

    /**
     * 粒径编码
     */
    @NotNull
    @Schema(description = "粒径编码", required = true)
    @Column(name = "code", nullable = false)
    private String code;

    /**
     * 粒径名称
     */
    @NotNull
    @Schema(description = "粒径名称", required = true)
    @Column(name = "name", nullable = false)
    private String name;

    public CleanlinessParticlesRadius() {
    }

    public CleanlinessParticlesRadius(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public CleanlinessParticlesRadius setCode(String code) {
        this.code = code;
        return this;
    }

    public String getName() {
        return name;
    }

    public CleanlinessParticlesRadius setName(String name) {
        this.name = name;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CleanlinessParticlesRadius cleanlinessParticlesRadius = (CleanlinessParticlesRadius) o;
        if (cleanlinessParticlesRadius.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), cleanlinessParticlesRadius.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }
}
