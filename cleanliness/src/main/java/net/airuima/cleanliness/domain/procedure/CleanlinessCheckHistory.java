package net.airuima.cleanliness.domain.procedure;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.scene.OrganizationArea;
import net.airuima.rbase.dto.organization.StaffDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 洁净度检测历史Domain
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Schema(name = "洁净度检测历史(CleanlinessCheckHistory)", description = "洁净度检测历史")
@Entity
@Table(name = "procedure_cleanliness_check_history")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@FetchEntity
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "cleanlinessCheckHistoryEntityGraph",attributeNodes = {@NamedAttributeNode("area")})
public class CleanlinessCheckHistory extends CustomBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 部门区域ID
     */
    @NotNull
    @ManyToOne
    @Schema(description = "部门区域ID")
    @JoinColumn(name = "area_id", nullable = false)
    private OrganizationArea area;
    
    /**
     * 结果（0：不合格, 1：合格）
     */
    @Schema(description = "结果（0：不合格, 1：合格）", required = true)
    @Column(name = "result", nullable = false)
    private boolean result;

    /**
     * 检验人ID
     */
    @Schema(description = "检验人ID", required = true)
    @Column(name = "operator_id", nullable = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private long operatorId;

    @Transient
    @Schema(description = "检验人DTO", required = true)
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "operatorId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "operator_id")
    private StaffDTO operatorDto = new StaffDTO();

    /**
     * 检验日期
     */
    @Schema(description = "检验日期", required = true)
    @Column(name = "record_date", nullable = false)
    private LocalDateTime recordDate;


    public boolean getResult() {
        return result;
    }

    public CleanlinessCheckHistory setResult(boolean result) {
        this.result = result;
        return this;
    }

    public OrganizationArea getArea() {
        return area;
    }

    public CleanlinessCheckHistory setArea(OrganizationArea area) {
        this.area = area;
        return this;
    }

    public long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(long operatorId) {
        this.operatorId = operatorId;
    }

    public StaffDTO getOperatorDto() {
        return operatorDto;
    }

    public void setOperatorDto(StaffDTO operatorDto) {
        this.operatorDto = operatorDto;
    }

    public LocalDateTime getRecordDate() {
        return recordDate;
    }

    public void setRecordDate(LocalDateTime recordDate) {
        this.recordDate = recordDate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CleanlinessCheckHistory cleanlinessCheckHistory = (CleanlinessCheckHistory) o;
        if (cleanlinessCheckHistory.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), cleanlinessCheckHistory.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
