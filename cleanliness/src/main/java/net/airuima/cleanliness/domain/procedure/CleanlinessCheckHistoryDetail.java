package net.airuima.cleanliness.domain.procedure;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 洁净度检测历史表详情Domain
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Schema(name = "洁净度检测历史表详情(CleanlinessCheckHistoryDetail)", description = "洁净度检测历史表详情")
@Entity
@Table(name = "procedure_cleanliness_check_history_detail")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "cleanlinessCheckHistoryDetailEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "history",subgraph = "cleanlinessCheckHistoryEntityGraph")},
        subgraphs = {@NamedSubgraph(name = "cleanlinessCheckHistoryEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "area")})})
public class CleanlinessCheckHistoryDetail extends CustomBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 洁净度检测历史ID
     */
    @Schema(description = "洁净度检测历史ID", required = true)
    @ManyToOne
    @JoinColumn(name = "history_id", nullable = false)
    private CleanlinessCheckHistory history;

    /**
     * 检测记录，JSON数组(多个检测记录则用逗号,分割)
     */
    @NotNull
    @Schema(description = "检测记录，JSON数组(多个检测记录则用逗号,分割)", required = true)
    @Column(name = "record", nullable = false)
    private String record;

    /**
     * 平均值
     */
    @Schema(description = "平均值", required = true)
    @Column(name = "average", nullable = false)
    private BigDecimal average;

    /**
     * 结果(0：不合格,1：合格)
     */
    @Schema(description = "结果(0：不合格,1：合格)", required = true)
    @Column(name = "result", nullable = false)
    private boolean result;

    public String getRecord() {
        return record;
    }

    public CleanlinessCheckHistoryDetail setRecord(String record) {
        this.record = record;
        return this;
    }

    public CleanlinessCheckHistory getHistory() {
        return history;
    }

    public void setHistory(CleanlinessCheckHistory history) {
        this.history = history;
    }

    public BigDecimal getAverage() {
        return average;
    }

    public void setAverage(BigDecimal average) {
        this.average = average;
    }

    public boolean getResult() {
        return result;
    }

    public CleanlinessCheckHistoryDetail setResult(boolean result) {
        this.result = result;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CleanlinessCheckHistoryDetail cleanlinessCheckHistoryDetail = (CleanlinessCheckHistoryDetail) o;
        if (cleanlinessCheckHistoryDetail.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), cleanlinessCheckHistoryDetail.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
