package net.airuima.cleanliness.domain.base;

import io.hypersistence.utils.hibernate.type.json.JsonType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Table;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.scene.OrganizationArea;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.*;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 洁净度检测标准Domain
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Schema(name = "洁净度检测标准(CleanlinessStandard)", description = "洁净度检测标准")
@Entity
@Table(name = "base_cleanliness_standard", uniqueConstraints = {
        @UniqueConstraint(name = "base_cleanliness_standard_unique", columnNames = {"area_id", "deleted"})
})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "洁净度检测标准数据")
@NamedEntityGraph(name = "cleanlinessStandardEntityGraph",attributeNodes = {@NamedAttributeNode("area")})
public class CleanlinessStandard extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门区域ID
     */
    @NotNull
    @ManyToOne
    @Schema(description = "部门区域ID")
    @JoinColumn(name = "area_id", nullable = false)
    private OrganizationArea area;

    /**
     * 粒子半径
     */
    @NotNull
    @Schema(description = "粒子半径", required = true)
    @Column(name = "particles_radius", nullable = false)
    @Type(JsonType.class)
    private List<String> particlesRadiusList;

    /**
     * 最大浓度限制（pc/m）
     */
    @NotNull
    @Schema(description = "最大浓度限制（pc/m）", required = true)
    @Column(name = "particles_number", nullable = false)
    @Type(JsonType.class)
    private List<Integer> particlesNumberList;

    /**
     * 洁净等级
     */
    @NotNull
    @ManyToOne
    @Schema(description = "洁净等级", required = true)
    @JoinColumn(name = "cleanliness_grade_id", nullable = false)
    private CleanlinessGrade cleanlinessGrade;

    /**
     * 测试次数
     */
    @NotNull
    @Schema(description = "测试次数", required = true)
    @Column(name = "test_times", nullable = false)
    private int testTimes;

    /**
     * 周期数值
     */
    @NotNull
    @Schema(description = "周期数值", required = true)
    @Column(name = "period_number", nullable = false)
    private int periodNumber;

    /**
     * 周期单位(0:小时，1:天，2:周，3:月，4:年)
     */
    @NotNull
    @Schema(description = "周期单位(0:小时，1:天，2:周，3:月，4:年)", required = true)
    @Column(name = "period_unit", nullable = false)
    private int periodUnit;

    /**
     * 提醒数值
     */
    @Schema(description = "提前提醒周期")
    @Column(name = "warn_duration", nullable = false)
    private int warnDuration;

    /**
     * 提醒周期
     */
    @Schema(description = "提前提醒周期单位(0:小时，1:天，2:周，3:月，4:年)")
    @Column(name = "warn_unit", nullable = false)
    private int warnUnit;

    public OrganizationArea getArea() {
        return area;
    }

    public CleanlinessStandard setArea(OrganizationArea area) {
        this.area = area;
        return this;
    }

    public List<String> getParticlesRadiusList() {
        return particlesRadiusList;
    }

    public CleanlinessStandard setParticlesRadiusList(List<String> particlesRadiusList) {
        this.particlesRadiusList = particlesRadiusList;
        return this;
    }

    public List<Integer> getParticlesNumberList() {
        return particlesNumberList;
    }

    public CleanlinessStandard setParticlesNumberList(List<Integer> particlesNumberList) {
        this.particlesNumberList = particlesNumberList;
        return this;
    }


    public CleanlinessGrade getCleanlinessGrade() {
        return cleanlinessGrade;
    }

    public CleanlinessStandard setCleanlinessGrade(CleanlinessGrade cleanlinessGrade) {
        this.cleanlinessGrade = cleanlinessGrade;
        return this;
    }

    public int getTestTimes() {
        return testTimes;
    }

    public CleanlinessStandard setTestTimes(int testTimes) {
        this.testTimes = testTimes;
        return this;
    }

    public int getPeriodNumber() {
        return periodNumber;
    }

    public CleanlinessStandard setPeriodNumber(int periodNumber) {
        this.periodNumber = periodNumber;
        return this;
    }

    public int getPeriodUnit() {
        return periodUnit;
    }

    public CleanlinessStandard setPeriodUnit(int periodUnit) {
        this.periodUnit = periodUnit;
        return this;
    }

    public int getWarnDuration() {
        return warnDuration;
    }

    public CleanlinessStandard setWarnDuration(int warnDuration) {
        this.warnDuration = warnDuration;
        return this;
    }

    public int getWarnUnit() {
        return warnUnit;
    }

    public CleanlinessStandard setWarnUnit(int warnUnit) {
        this.warnUnit = warnUnit;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CleanlinessStandard cleanlinessStandard = (CleanlinessStandard) o;
        if (cleanlinessStandard.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), cleanlinessStandard.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
