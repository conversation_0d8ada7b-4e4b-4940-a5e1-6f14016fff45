package net.airuima.standardpart.service.procedure;

import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.client.ClientStandardPartCheckResultSaveDTO;
import net.airuima.rbase.proxy.bom.RbaseMaterialProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.util.ToolUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.standardpart.domain.base.StandardPart;
import net.airuima.standardpart.domain.base.StandardPartConfig;
import net.airuima.standardpart.domain.procedure.StandardPartCheckResult;
import net.airuima.standardpart.domain.procedure.StandardPartCheckResultDetail;
import net.airuima.standardpart.repository.base.StandardPartConfigRepository;
import net.airuima.standardpart.repository.base.StandardPartRepository;
import net.airuima.standardpart.repository.procedure.StandardPartCheckResultDetailRepository;
import net.airuima.standardpart.repository.procedure.StandardPartCheckResultRepository;
import net.airuima.standardpart.web.rest.procedure.dto.StandardPartCheckResultDetailEditDTO;
import net.airuima.standardpart.web.rest.procedure.dto.StandardPartCheckResultDetailExpandDTO;
import net.airuima.standardpart.web.rest.procedure.dto.StandardPartCheckResultDetailSaveDTO;
import net.airuima.standardpart.web.rest.procedure.dto.StandardPartCheckResultSaveDTO;
import net.airuima.util.DateTimeUtil;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 标准件测试数据明细Service
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class StandardPartCheckResultDetailService extends CommonJpaService<StandardPartCheckResultDetail> {

    private final StandardPartCheckResultRepository standardPartCheckResultRepository;
    private final StandardPartRepository standardPartRepository;
    private final StandardPartCheckResultDetailRepository standardPartCheckResultDetailRepository;
    private final CommonService commonService;
    @Autowired
    private StandardPartCheckResultService  standardPartCheckResultService;
    @Autowired
    private RbaseMaterialProxy rbaseMaterialProxy;
    private final PedigreeRepository pedigreeRepository;

    public StandardPartCheckResultDetailService(StandardPartCheckResultRepository standardPartCheckResultRepository,
                                                StandardPartRepository standardPartRepository, StandardPartCheckResultDetailRepository standardPartCheckResultDetailRepository,
                                                CommonService commonService, PedigreeRepository pedigreeRepository) {
        this.standardPartCheckResultRepository = standardPartCheckResultRepository;
        this.standardPartRepository = standardPartRepository;
        this.standardPartCheckResultDetailRepository = standardPartCheckResultDetailRepository;
        this.commonService = commonService;
        this.pedigreeRepository = pedigreeRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<StandardPartCheckResultDetail> find(Specification<StandardPartCheckResultDetail> spec, Pageable pageable) {
        return standardPartCheckResultDetailRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<StandardPartCheckResultDetail> find(Specification<StandardPartCheckResultDetail> spec) {
        return standardPartCheckResultDetailRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<StandardPartCheckResultDetail> findAll(Pageable pageable) {
        return standardPartCheckResultDetailRepository.findAll(pageable);
    }

    /**
     * 通过标准件检测历史ID获取横向展开的检测项明细数据
     *
     * @param checkResultId 标准件检测历史ID
     * @return List<StandardPartCheckResultDetailExpandDTO>
     */
    @Transactional(readOnly = true)
    public List<StandardPartCheckResultDetailExpandDTO> getExpandCheckResultDetail(Long checkResultId) {
        List<StandardPartCheckResultDetailExpandDTO> checkResultDetailExpandDtoList = Lists.newArrayList();
        List<StandardPartCheckResultDetail> checkResultDetailList = standardPartCheckResultDetailRepository.findByStandardPartCheckResultIdAndDeleted(checkResultId, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(checkResultDetailList)) {
            return checkResultDetailExpandDtoList;
        }
        //按照工具编号及测试次数进行分组
        Map<String, List<StandardPartCheckResultDetail>> groupMap = checkResultDetailList.stream().collect(Collectors.groupingBy(resultDetail -> resultDetail.getTool() + Constants.STR_PRAGMA + resultDetail.getTimes()));
        groupMap.forEach((toolKey, detailList) -> {
            String tool = toolKey.split(Constants.STR_PRAGMA)[Constants.INT_ZERO];
            int times = Integer.parseInt(toolKey.split(Constants.STR_PRAGMA)[Constants.INT_ONE]);
            StandardPartCheckResultDetailExpandDTO standardPartCheckResultDetailExpandDto = new StandardPartCheckResultDetailExpandDTO();
            standardPartCheckResultDetailExpandDto.setTool(tool).setTimes(times);
            //再次按照检测项目编码和单位进行分组
            Map<String, List<StandardPartCheckResultDetail>> groupCheckItemMap = detailList.stream().collect(Collectors.groupingBy(StandardPartCheckResultDetail::getCheckItemCode));
            List<StandardPartCheckResultDetailExpandDTO.CheckItemInfo> checkItemInfoList = Lists.newArrayList();
            groupCheckItemMap.forEach((checkItemCode, resultDetailList) -> {
                StandardPartCheckResultDetailExpandDTO.CheckItemInfo checkItemInfo = new StandardPartCheckResultDetailExpandDTO.CheckItemInfo();
                checkItemInfo.setCheckItemCode(checkItemCode);
                List<StandardPartCheckResultDetailExpandDTO.CheckItemInfo.CheckItemValue> checkItemValueList = Lists.newArrayList();
                resultDetailList.stream().sorted(Comparator.comparing(StandardPartCheckResultDetail::getSubModule));
                resultDetailList.forEach(resultDetail -> {
                    StandardPartCheckResultDetailExpandDTO.CheckItemInfo.CheckItemValue checkItemValue = new StandardPartCheckResultDetailExpandDTO.CheckItemInfo.CheckItemValue();
                    checkItemValue.setSubModule(resultDetail.getSubModule()).setNumber(resultDetail.getNumber());
                    checkItemValueList.add(checkItemValue);
                });
                checkItemInfo.setCheckItemValueList(checkItemValueList);
                checkItemInfoList.add(checkItemInfo);
            });
            standardPartCheckResultDetailExpandDto.setCheckItemInfoList(checkItemInfoList);
            checkResultDetailExpandDtoList.add(standardPartCheckResultDetailExpandDto);
        });
        return checkResultDetailExpandDtoList;
    }

    /**
     * 通过标准件检测历史ID获取可修改的检测数据明细
     *
     * @param checkResultId 标准件检测历史ID
     * @return List<StandardPartCheckResultDetailEditDTO>
     */
    @Transactional(readOnly = true)
    public StandardPartCheckResultDetailEditDTO getEditCheckResultDetail(Long checkResultId) {
        List<StandardPartCheckResultDetail> checkResultDetailList = standardPartCheckResultDetailRepository.findByStandardPartCheckResultIdAndDeleted(checkResultId, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(checkResultDetailList)) {
            return null;
        }
        //组装标准件检测历史DTO基础数据
        StandardPartCheckResultDetailEditDTO standardPartCheckResultDetailEditDto = new StandardPartCheckResultDetailEditDTO();
        StandardPartCheckResult standardPartCheckResult = checkResultDetailList.get(Constants.INT_ZERO).getStandardPartCheckResult();
        standardPartCheckResultDetailEditDto.setSn(standardPartCheckResult.getStandardPart().getSn())
                .setMaterialCode(standardPartCheckResult.getStandardPart().getMaterialCode())
                .setTestTime(standardPartCheckResult.getTestTime());
        //组装标准件检测历史明细DTO列表数据
        List<StandardPartCheckResultDetailEditDTO.CheckResultDetailInfo> checkResultDetailInfoList = Lists.newArrayList();
        Map<String, List<StandardPartCheckResultDetail>> groupCheckResultDetailMap = checkResultDetailList.stream().collect(Collectors.groupingBy(StandardPartCheckResultDetail::getTool));
        groupCheckResultDetailMap.forEach((tool, groupCheckResultDetailList) -> {
            groupCheckResultDetailList.sort(Comparator.comparing(StandardPartCheckResultDetail::getTimes));
            groupCheckResultDetailList.sort(Comparator.comparing(StandardPartCheckResultDetail::getSubModule));
            groupCheckResultDetailList.forEach(checkResultDetail -> {
                StandardPartCheckResultDetailEditDTO.CheckResultDetailInfo checkResultDetailInfo = net.airuima.rbase.util.MapperUtils.map(checkResultDetail, StandardPartCheckResultDetailEditDTO.CheckResultDetailInfo.class);
                checkResultDetailInfo.setTester(standardPartCheckResult.getTester());
                checkResultDetailInfoList.add(checkResultDetailInfo);
            });
        });
        standardPartCheckResultDetailEditDto.setCheckResultDetailInfoList(checkResultDetailInfoList);
        return standardPartCheckResultDetailEditDto;
    }

    /**
     * 批量修改标准件检测历史明细数据
     *
     * @param standardPartCheckResultDetailSaveDtoList 批量待保存的检测结果历史明细数据参数DTO
     */
    public void batchUpdateCheckResultDetail(List<StandardPartCheckResultDetailSaveDTO> standardPartCheckResultDetailSaveDtoList) {
        standardPartCheckResultDetailSaveDtoList.forEach(standardPartCheckResultDetailSaveDto -> {
            Optional<StandardPartCheckResultDetail> standardPartCheckResultDetailOptional = standardPartCheckResultDetailRepository.findById(standardPartCheckResultDetailSaveDto.getId());
            standardPartCheckResultDetailOptional.ifPresent(standardPartCheckResultDetail -> {
                standardPartCheckResultDetail.setUpperNumber(standardPartCheckResultDetailSaveDto.getUpperNumber())
                        .setLowerNumber(standardPartCheckResultDetailSaveDto.getLowerNumber())
                        .setMiddleNumber(standardPartCheckResultDetailSaveDto.getMiddleNumber())
                        .setNumber(standardPartCheckResultDetailSaveDto.getNumber()).setDeleted(Constants.LONG_ZERO);
                standardPartCheckResultDetailRepository.save(standardPartCheckResultDetail);
            });
        });
    }


    /**
     * 保存页面导入或者客户端上传的标准件测试数据明细
     * @param standardPartCheckResultSaveDTOS 标准件测试数据参数列表列表DTO
     * @return BaseClientDTO
     */
    public BaseClientDTO saveCheckResultDetail(List<StandardPartCheckResultSaveDTO> standardPartCheckResultSaveDTOS) {
        //验证导入的标准件测试数据中是否存在不同的SN
        long distinctSnSize = standardPartCheckResultSaveDTOS.stream().map(StandardPartCheckResultSaveDTO::getSn).distinct().count();
        if (distinctSnSize > Constants.INT_ONE) {
            return new BaseClientDTO(Constants.KO, "请保持待保存的数据中SN一致");
        }
        //验证导入的标准件测试数据中是否存在不同的测试日期
        long distinctTestTimeSize = standardPartCheckResultSaveDTOS.stream().map(StandardPartCheckResultSaveDTO::getTestTime).distinct().count();
        if (distinctTestTimeSize > Constants.INT_ONE) {
            return new BaseClientDTO(Constants.KO, "请保持待保存的数据中测试日期一致");
        }
        //验证物料编码是否存在
        MaterialDTO materialDto = rbaseMaterialProxy.findByCodeAndDeleted(standardPartCheckResultSaveDTOS.get(Constants.INT_ZERO).getMaterialCode(),Constants.LONG_ZERO).orElse(null);
        if (null == materialDto) {
            return new BaseClientDTO(Constants.KO, "物料编码在基础物料信息中不存在");
        }
        //验证物料编码是否存在产品谱系对应关系
        Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByMaterialIdAndDeleted(materialDto.getId(), Constants.LONG_ZERO);
        if (pedigreeOptional.isEmpty()) {
            return new BaseClientDTO(Constants.KO, "物料编码产品谱系中不存在");
        }
        StandardPartConfig standardPartConfig = standardPartCheckResultService.findStandardPartConfig(pedigreeOptional.get());
        if (null == standardPartConfig) {
            return new BaseClientDTO(Constants.KO, "不存在物料编码对应的产品谱系的标准件有效期配置项");
        }

        this.saveStandardPartCheckResultDetail(standardPartCheckResultSaveDTOS, standardPartConfig);
        return new BaseClientDTO(Constants.OK);
    }

    /**
     * 保存检测历史及明细数据
     * @param clientStandardPartCheckResultSaveDtoList 标准件测试数据参数列表列表DTO
     * @param standardPartConfig 标准件周期配置
     */
    public void saveStandardPartCheckResultDetail(List<StandardPartCheckResultSaveDTO> standardPartCheckResultSaveDTOList, StandardPartConfig standardPartConfig) {
        StandardPartCheckResultSaveDTO firstImportDto = standardPartCheckResultSaveDTOList.get(Constants.INT_ZERO);
        String sn = firstImportDto.getSn();
        StandardPart standardPart = standardPartRepository.findBySnAndDeleted(sn, Constants.LONG_ZERO).orElse(new StandardPart());
        LocalDateTime testDate = firstImportDto.getTestTime();
        if (standardPart.getId() == null) {
            standardPart = new StandardPart();
            standardPart.setRecordDate(testDate)
                    .setSn(firstImportDto.getSn())
                    .setExpireDate(testDate.plusDays(ToolUtils.getCycleDays(standardPartConfig.getUnit(), standardPartConfig.getPeriod())))
                    .setStatus(standardPart.getExpireDate().isBefore(LocalDateTime.now()) ? Constants.INT_ONE : Constants.INT_ZERO)
                    .setMaterialCode(firstImportDto.getMaterialCode())
                    .setAuthor(firstImportDto.getTester())
                    .setDeleted(Constants.LONG_ZERO);
        }
        if(DateTimeUtil.isAfterOrEqualLocalDateTime(testDate,standardPart.getRecordDate())){
            standardPart.setRecordDate(testDate).setExpireDate(testDate.plusDays(ToolUtils.getCycleDays(standardPartConfig.getUnit(), standardPartConfig.getPeriod())))
                    .setStatus(standardPart.getExpireDate().isBefore(LocalDateTime.now()) ? Constants.INT_ONE : Constants.INT_ZERO).setAuthor(firstImportDto.getTester());
        }
        standardPartRepository.save(standardPart);
        StandardPartCheckResult oldStandardPartCheckResult = standardPartCheckResultRepository.findByStandardPartIdAndTestTimeAndDeleted(standardPart.getId(),firstImportDto.getTestTime(),Constants.LONG_ZERO);
        if(null != oldStandardPartCheckResult){
            standardPartCheckResultDetailRepository.batchDeleteByCheckResultId(oldStandardPartCheckResult.getId());
            oldStandardPartCheckResult.setDeleted(oldStandardPartCheckResult.getId());
            standardPartCheckResultRepository.save(oldStandardPartCheckResult);
        }
        StandardPartCheckResult latestStandardPartCheckResult = standardPartCheckResultRepository.findLatestStandardPartCheckResultByStandardPartId(standardPart.getId(),Constants.LONG_ZERO).orElse(null);
        StandardPartCheckResult standardPartCheckResult = new StandardPartCheckResult();
        standardPartCheckResult.setStandardPart(standardPart)
                .setTestTime(testDate)
                .setTester(firstImportDto.getTester())
                .setIsLatest(Boolean.FALSE)
                .setDeleted(Constants.LONG_ZERO);
        if(null == latestStandardPartCheckResult || DateTimeUtil.isBeforeOrEqualLocalDateTime(latestStandardPartCheckResult.getTestTime(),standardPartCheckResult.getTestTime())){
            standardPartCheckResult.setIsLatest(Boolean.TRUE);
            //将历史标准测试记录标志为非最新记录
            standardPartCheckResultRepository.batchUpdateStandardPartCheckResultIsLatest(standardPart.getId(), Boolean.FALSE);
        }
        standardPartCheckResultRepository.save(standardPartCheckResult);
        //保存标准件检测历史明细数据
        standardPartCheckResultSaveDTOList.forEach(standardPartCheckResultDetailInfo -> {
            StandardPartCheckResultDetail standardPartCheckResultDetail = net.airuima.rbase.util.MapperUtils.map(standardPartCheckResultDetailInfo, StandardPartCheckResultDetail.class);
            standardPartCheckResultDetail.setStandardPartCheckResult(standardPartCheckResult).setDeleted(Constants.LONG_ZERO);
            standardPartCheckResultDetailRepository.save(standardPartCheckResultDetail);
        });
    }

    /**
     * 通过检测历史ID获取明细数据
     * @param checkResultId 检测历史ID
     * @return List<StandardPartCheckResultDetail>
     */
    public List<StandardPartCheckResultDetail> findByStandardPartCheckResultId(Long checkResultId){
        return standardPartCheckResultDetailRepository.findByStandardPartCheckResultIdAndDeleted(checkResultId,Constants.LONG_ZERO);
    }

}
