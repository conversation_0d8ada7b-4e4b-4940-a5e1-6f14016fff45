package net.airuima.standardpart.service.procedure.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.constant.Constants;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.dto.client.ClientStandardPartCheckResultSaveDTO;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.standardpart.domain.procedure.StandardPartCheckResultDetail;
import net.airuima.standardpart.service.procedure.IStandardPartCheckResultDetailService;
import net.airuima.standardpart.service.procedure.StandardPartCheckResultDetailService;
import net.airuima.standardpart.web.rest.procedure.dto.StandardPartCheckResultSaveDTO;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class StandardPartCheckResultDetailServiceImpl implements IStandardPartCheckResultDetailService {

    @Autowired
    private StandardPartCheckResultDetailService standardPartCheckResultDetailService;

    /**
     * 标准件测试数据明细导入
     *
     * @param file 导入的Excel文件
     * @return BaseClientDTO
     */
    @Override
    public BaseClientDTO importExcel(MultipartFile file) throws Exception {
        BaseClientDTO baseClientDto = new BaseClientDTO(Constants.OK);
        ImportParams importParams = new ImportParams();
        importParams.setHeadRows(Constants.INT_ONE);
        List<StandardPartCheckResultSaveDTO> standardPartCheckResultSaveDTOList = ExcelImportUtil.importExcel(file.getInputStream(), StandardPartCheckResultSaveDTO.class, importParams);
        if (ValidateUtils.isValid(standardPartCheckResultSaveDTOList)) {
            baseClientDto = standardPartCheckResultDetailService.saveCheckResultDetail(standardPartCheckResultSaveDTOList);
        }
        return baseClientDto;
    }

    /**
     * 通过标准件测试历史ID导出明细数据
     *
     * @param id       标准件测试历史ID
     * @param response 请求响应
     */
    @Override
    public void export(Long id, HttpServletResponse response) throws IOException {
        List<StandardPartCheckResultDetail> standardPartCheckResultDetails = standardPartCheckResultDetailService.findByStandardPartCheckResultId(id);
        List<StandardPartCheckResultSaveDTO> standardPartCheckResultSaveDTOList = standardPartCheckResultDetails.stream().map(StandardPartCheckResultSaveDTO::new).collect(Collectors.toList());
        ExportParams exportParams = new ExportParams();
        exportParams.setType(ExcelType.XSSF);
        String fileName = "标准件测试数据" + "_" + UUID.randomUUID() + ".xlsx";
        exportParams.setFreezeCol(Constants.INT_TWO);
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, StandardPartCheckResultSaveDTO.class, standardPartCheckResultSaveDTOList);
        workbook.write(byteArrayOutputStream);

        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(fileName, "utf-8"));
        response.getOutputStream().write(byteArrayOutputStream.toByteArray());
    }
}
