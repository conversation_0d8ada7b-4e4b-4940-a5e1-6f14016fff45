package net.airuima.standardpart.service.base;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.service.CommonJpaService;
import net.airuima.standardpart.domain.base.StandardPartConfig;
import net.airuima.standardpart.repository.base.StandardPartConfigRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 标准件有效期配置表Service
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class StandardPartConfigService extends CommonJpaService<StandardPartConfig> {

    private final String STANDARD_PART_CONFIG_ENTITY_GRAPH = "standardPartConfigEntityGraph";
    private final StandardPartConfigRepository standardPartConfigRepository;

    public StandardPartConfigService(StandardPartConfigRepository standardPartConfigRepository) {
        this.standardPartConfigRepository = standardPartConfigRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<StandardPartConfig> find(Specification<StandardPartConfig> spec, Pageable pageable) {
        return standardPartConfigRepository.findAll(spec, pageable,new NamedEntityGraph(STANDARD_PART_CONFIG_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public List<StandardPartConfig> find(Specification<StandardPartConfig> spec) {
        return standardPartConfigRepository.findAll(spec,new NamedEntityGraph(STANDARD_PART_CONFIG_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<StandardPartConfig> findAll(Pageable pageable) {
        return standardPartConfigRepository.findAll(pageable,new NamedEntityGraph(STANDARD_PART_CONFIG_ENTITY_GRAPH));
    }

}
