package net.airuima.standardpart.service.procedure;

import net.airuima.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.proxy.bom.RbaseMaterialProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.util.ToolUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.standardpart.domain.base.StandardPart;
import net.airuima.standardpart.domain.base.StandardPartConfig;
import net.airuima.standardpart.domain.procedure.StandardPartCheckResult;
import net.airuima.standardpart.repository.base.StandardPartConfigRepository;
import net.airuima.standardpart.repository.base.StandardPartRepository;
import net.airuima.standardpart.repository.procedure.StandardPartCheckResultDetailRepository;
import net.airuima.standardpart.repository.procedure.StandardPartCheckResultRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 标准件测试历史表Service
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class StandardPartCheckResultService extends CommonJpaService<StandardPartCheckResult> {

    private final CommonService commonService;
    @Autowired
    private RbaseMaterialProxy rbaseMaterialProxy;
    @Autowired
    private StandardPartConfigRepository standardPartConfigRepository;
    private final PedigreeRepository pedigreeRepository;
    private final StandardPartRepository standardPartRepository;
    private final StandardPartCheckResultRepository standardPartCheckResultRepository;
    private final StandardPartCheckResultDetailRepository standardPartCheckResultDetailRepository;

    public StandardPartCheckResultService(CommonService commonService, PedigreeRepository pedigreeRepository,
                                          StandardPartRepository standardPartRepository, StandardPartCheckResultRepository standardPartCheckResultRepository,
                                          StandardPartCheckResultDetailRepository standardPartCheckResultDetailRepository) {
        this.commonService = commonService;
        this.pedigreeRepository = pedigreeRepository;
        this.standardPartRepository = standardPartRepository;
        this.standardPartCheckResultRepository = standardPartCheckResultRepository;
        this.standardPartCheckResultDetailRepository = standardPartCheckResultDetailRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<StandardPartCheckResult> find(Specification<StandardPartCheckResult> spec, Pageable pageable) {
        return standardPartCheckResultRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<StandardPartCheckResult> find(Specification<StandardPartCheckResult> spec) {
        return standardPartCheckResultRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<StandardPartCheckResult> findAll(Pageable pageable) {
        return standardPartCheckResultRepository.findAll(pageable);
    }

    /**
     * 通过ID删除标准件检测历史及对应明细数据
     *
     * @param id 标准件检测历史ID
     * @return BaseDTO
     */
    public BaseDTO deleteCheckResultAndDetailById(Long id) {
        Optional<StandardPartCheckResult> standardPartCheckResultOptional = standardPartCheckResultRepository.findById(id);
        if (standardPartCheckResultOptional.isPresent()) {
            StandardPartCheckResult standardPartCheckResult = standardPartCheckResultOptional.get();
            StandardPart standardPart = standardPartCheckResult.getStandardPart();
            MaterialDTO materialDto = rbaseMaterialProxy.findByCodeAndDeleted(standardPart.getMaterialCode(), Constants.LONG_ZERO).orElse(null);
            if (null == materialDto) {
                return new BaseDTO(Constants.KO, "标准件物料编码在基础物料信息中不存在");
            }
            //验证物料编码是否存在产品谱系对应关系
            Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByMaterialIdAndDeleted(materialDto.getId(), Constants.LONG_ZERO);
            if (!pedigreeOptional.isPresent()) {
                return new BaseDTO(Constants.KO, "物料编码产品谱系中不存在");
            }
            //验证是否存在产品谱系配置的标准件有效期配置
            StandardPartConfig standardPartConfig = this.findStandardPartConfig(pedigreeOptional.get());
            if (null == standardPartConfig) {
                return new BaseDTO(Constants.KO, "不存在物料编码对应的产品谱系的标准件有效期配置项");
            }
            //逻辑删除检测历史及对应历史明细数据
            standardPartCheckResult.setDeleted(standardPartCheckResult.getId());
            standardPartCheckResultRepository.save(standardPartCheckResult);
            standardPartCheckResultDetailRepository.batchDeleteByCheckResultId(id);
            if (!standardPartCheckResult.getIsLatest()) {
                return null;
            }
            //获取最新检测历史记录并更新标准件相关数据
            StandardPartCheckResult latestCheckResult = standardPartCheckResultRepository.findTop1ByStandardPartIdAndDeletedOrderByTestTimeDescIdDesc(standardPartCheckResult.getStandardPart().getId(), Constants.LONG_ZERO);
            //无最新检测历史数据则逻辑删除标准件
            if (null == latestCheckResult) {
                standardPart.setDeleted(standardPart.getId());
            } else {
                latestCheckResult.setIsLatest(Boolean.TRUE);
                standardPart.setRecordDate(latestCheckResult.getTestTime())
                        .setAuthor(latestCheckResult.getTester()).
                        setExpireDate(latestCheckResult.getTestTime().plusDays(ToolUtils.getCycleDays(standardPartConfig.getUnit(), standardPartConfig.getPeriod())));
                if (standardPart.getStatus() != Constants.INT_TWO) {
                    standardPart.setStatus(standardPart.getExpireDate().isAfter(LocalDateTime.now()) ? Constants.INT_ZERO : Constants.INT_ONE);
                }
                standardPartCheckResultRepository.save(latestCheckResult);
            }
            standardPartRepository.save(standardPart);
        }
        return null;
    }


    /**
     * 根据产品谱系按照优先顺序获取标准件检测周期
     *
     * @param pedigree 产品谱系
     * @return StandardPartConfig
     */
    @Transactional(readOnly = true)
    public StandardPartConfig findStandardPartConfig(Pedigree pedigree) {
        Map<Integer, List<Pedigree>> pedigreeMap = commonService.findParentPedigreeGroupByLevel(pedigree);
        for (Integer level : pedigreeMap.keySet()) {
            for (Pedigree pedigreeTemp : pedigreeMap.get(level)) {
                Optional<StandardPartConfig> standardPartConfigOptional = standardPartConfigRepository.findByPedigreeIdAndDeleted(pedigreeTemp.getId(), net.airuima.rbase.constant.Constants.LONG_ZERO);
                if (standardPartConfigOptional.isPresent()) {
                    return standardPartConfigOptional.get();
                }
            }
        }
        return null;
    }

}
