package net.airuima.standardpart.web.rest.procedure;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.standardpart.domain.procedure.StandardPartCheckResultDetail;
import net.airuima.standardpart.service.procedure.StandardPartCheckResultDetailService;
import net.airuima.standardpart.web.rest.procedure.dto.StandardPartCheckResultDetailEditDTO;
import net.airuima.standardpart.web.rest.procedure.dto.StandardPartCheckResultDetailExpandDTO;
import net.airuima.standardpart.web.rest.procedure.dto.StandardPartCheckResultDetailSaveDTO;
import net.airuima.util.ResponseData;
import net.airuima.web.BaseResource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 标准件测试数据明细Resource
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Tag(name = "标准件测试数据明细Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/standard-part-check-result-details")
@AuthorityRegion("标准件管理")
@FuncInterceptor("StandardPart")
public class StandardPartCheckResultDetailResource extends BaseResource<StandardPartCheckResultDetail> {

    private final StandardPartCheckResultDetailService standardPartCheckResultDetailService;

    public StandardPartCheckResultDetailResource(StandardPartCheckResultDetailService standardPartCheckResultDetailService) {
        this.standardPartCheckResultDetailService = standardPartCheckResultDetailService;
        this.mapUri = "/api/standard-part-check-result-details";
    }

    /**
     * 通过标准件检测历史ID获取横向展开的检测项明细数据
     *
     * @param checkResultId 标准件检测历史ID
     * @return List<StandardPartCheckResultDetailExpandDTO>
     */
    @PreAuthorize("hasAnyAuthority('STANDARDPARTCHECKRESULT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "通过标准件检测历史ID获取横向展开的检测项明细数据")
    @GetMapping("/expand/{checkResultId}")
    public ResponseEntity<ResponseData<List<StandardPartCheckResultDetailExpandDTO>>> expandCheckResultDetail(@PathVariable("checkResultId") Long checkResultId) {
        return ResponseData.ok(standardPartCheckResultDetailService.getExpandCheckResultDetail(checkResultId));
    }

    /***
     * 通过标准件检测历史ID获取可修改的检测数据明细
     * @param checkResultId 标准件检测历史ID
     * @return List<StandardPartCheckResultDetailEditDTO>
     */
    @PreAuthorize("hasAnyAuthority('STANDARDPARTCHECKRESULT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "通过标准件检测历史ID获取可修改的检测数据明细")
    @GetMapping("/edit/{checkResultId}")
    public ResponseEntity<ResponseData<StandardPartCheckResultDetailEditDTO>> editCheckResultDetail(@PathVariable("checkResultId") Long checkResultId) {
        return ResponseData.ok(standardPartCheckResultDetailService.getEditCheckResultDetail(checkResultId));
    }

    /**
     * 批量保存手动修改的标准件历史明细数据
     *
     * @param standardPartCheckResultDetailSaveDtoList 批量待保存的检测结果历史明细数据参数DTO
     */
    @PreAuthorize("hasAnyAuthority('STANDARDPARTCHECKRESULT_UPDATE') or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "保存手动修改的标准件历史明细数据")
    @PutMapping("/batch")
    public ResponseEntity<ResponseData<Void>> batchUpdateCheckResultDetail(@RequestBody List<StandardPartCheckResultDetailSaveDTO> standardPartCheckResultDetailSaveDtoList) {
        try {
            standardPartCheckResultDetailService.batchUpdateCheckResultDetail(standardPartCheckResultDetailSaveDtoList);
            return ResponseData.save();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

}
