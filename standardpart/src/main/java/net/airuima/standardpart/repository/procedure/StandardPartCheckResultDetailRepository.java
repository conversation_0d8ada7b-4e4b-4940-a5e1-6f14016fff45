package net.airuima.standardpart.repository.procedure;

import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.standardpart.domain.procedure.StandardPartCheckResultDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 标准件测试数据明细Repository
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Repository
public interface StandardPartCheckResultDetailRepository extends LogicDeleteableRepository<StandardPartCheckResultDetail>,
        JpaSpecificationExecutor<StandardPartCheckResultDetail>, JpaRepository<StandardPartCheckResultDetail, Long> {

    /**
     * 通过检测历史主键ID获取明细数据
     * @param checkResultId 检测历史主键ID
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.standardpart.StandardPartCheckResultDetail> 标准件测试数据明细列表
     */
    List<StandardPartCheckResultDetail> findByStandardPartCheckResultIdAndDeleted(Long checkResultId,Long deleted);

    /**
     * 根据检测历史主键ID按照检测项目及子模块分组获取数据
     * @param checkResultId 检测历史主键ID
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.standardpart.StandardPartCheckResultDetail> 标准件测试数据明细列表
     */
    @Query("select detail from StandardPartCheckResultDetail detail where detail.standardPartCheckResult.id=?1 and detail.deleted=?2 group by detail.checkItemCode,detail.subModule")
    List<StandardPartCheckResultDetail> findByStandardPartCheckResultIdAndDeletedGroupByCheckItemAndSubModule(Long checkResultId,Long deleted );

    /**
     * 通过标准件检测历史主键ID批量逻辑删除明细记录
     * @param checkResultId 检测历史主键ID
     */
    @Modifying
    @Query("update StandardPartCheckResultDetail set deleted=id where standardPartCheckResult.id=?1 and deleted=0")
    void batchDeleteByCheckResultId(Long checkResultId);
}
