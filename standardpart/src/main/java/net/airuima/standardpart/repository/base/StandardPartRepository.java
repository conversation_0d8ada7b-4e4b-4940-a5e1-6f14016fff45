package net.airuima.standardpart.repository.base;

import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.standardpart.domain.base.StandardPart;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 标准件基础信息表Repository
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Repository
public interface StandardPartRepository extends LogicDeleteableRepository<StandardPart>,
        JpaSpecificationExecutor<StandardPart>, JpaRepository<StandardPart, Long> {

    /**
     * 通过标准件SN获取标准件
     *
     * @param sn      标准件SN
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.standardpart.StandardPart> 标准件清单
     */
    Optional<StandardPart> findBySnAndDeleted(String sn, Long deleted);

    /**
     * 通过标准件SN集合获取标准件
     *
     * @param sn      标准件SN集合
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.standardpart.StandardPart> 标准件清单列表
     */
    List<StandardPart> findBySnInAndDeleted(List<String> sn, Long deleted);

    /**
     * 通过标准件ID获取标准件
     *
     * @param standardPartId 标准件ID
     * @param deleted        逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.standardpart.StandardPart> 标准件清单
     */
    Optional<StandardPart> findByIdAndDeleted(Long standardPartId, Long deleted);

    /**
     * 根据项目名称查询校准项目
     *
     * @param sn       项目名称
     * @param pageable 分页
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.base.standardpart.StandardPart> 标准件清单分页
     * <AUTHOR>
     * @date 2022/7/6
     **/
    @Query("select sp from StandardPart sp where sp.deleted = 0L and sp.sn like concat('%',?1,'%') and sp.status = 0 and sp.expireDate > ?2")
    Page<StandardPart> findBySnLike(String sn, LocalDateTime now, PageRequest pageable);
}
