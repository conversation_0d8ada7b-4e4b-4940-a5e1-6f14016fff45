package net.airuima.standardpart.domain.procedure;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 标准件测试数据明细Domain
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Schema(name = "标准件测试数据明细(StandardPartCheckResultDetail)", description = "标准件测试数据明细")
@Entity
@Table(name = "procedure_standard_part_check_result_detail")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
public class StandardPartCheckResultDetail extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标准件测试历史ID
     */
    @NotNull
    @Schema(description = "标准件测试历史ID", required = true)
    @ManyToOne
    @JoinColumn(name = "check_result_id", nullable = false)
    private StandardPartCheckResult standardPartCheckResult;

    /**
     * 工具编号
     */
    @Schema(description = "工具编号")
    @Column(name = "tool")
    private String tool;

    /**
     * 测试次数
     */
    @Schema(description = "测试次数")
    @Column(name = "times")
    private int times;

    /**
     * 检测项目编码
     */
    @Schema(description = "检测项目编码")
    @Column(name = "check_item_code")
    private String checkItemCode;

    /**
     * 检测项单位
     */
    @Schema(description = "检测项单位")
    @Column(name = "check_item_unit")
    private String checkItemUnit;

    /**
     * 标准件子模块
     */
    @Schema(description = "标准件子模块")
    @Column(name = "sub_module")
    private String subModule;

    /**
     * 下限值
     */
    @Schema(description = "下限值")
    @Column(name = "lower_number")
    private double lowerNumber;

    /**
     * 上限值
     */
    @Schema(description = "上限值")
    @Column(name = "upper_number")
    private double upperNumber;

    /**
     * 中心值
     */
    @Schema(description = "中心值")
    @Column(name = "middle_number")
    private double middleNumber;

    /**
     * 测试值
     */
    @Schema(description = "测试值")
    @Column(name = "number")
    private double number;

    public StandardPartCheckResult getStandardPartCheckResult() {
        return standardPartCheckResult;
    }

    public StandardPartCheckResultDetail setStandardPartCheckResult(StandardPartCheckResult standardPartCheckResult) {
        this.standardPartCheckResult = standardPartCheckResult;
        return this;
    }

    public String getTool() {
        return tool;
    }

    public StandardPartCheckResultDetail setTool(String tool) {
        this.tool = tool;
        return this;
    }

    public int getTimes() {
        return times;
    }

    public StandardPartCheckResultDetail setTimes(int times) {
        this.times = times;
        return this;
    }

    public String getCheckItemCode() {
        return checkItemCode;
    }

    public StandardPartCheckResultDetail setCheckItemCode(String checkItemCode) {
        this.checkItemCode = checkItemCode;
        return this;
    }

    public String getCheckItemUnit() {
        return checkItemUnit;
    }

    public StandardPartCheckResultDetail setCheckItemUnit(String checkItemUnit) {
        this.checkItemUnit = checkItemUnit;
        return this;
    }

    public String getSubModule() {
        return subModule;
    }

    public StandardPartCheckResultDetail setSubModule(String subModule) {
        this.subModule = subModule;
        return this;
    }

    public double getLowerNumber() {
        return lowerNumber;
    }

    public StandardPartCheckResultDetail setLowerNumber(double lowerNumber) {
        this.lowerNumber = lowerNumber;
        return this;
    }

    public double getUpperNumber() {
        return upperNumber;
    }

    public StandardPartCheckResultDetail setUpperNumber(double upperNumber) {
        this.upperNumber = upperNumber;
        return this;
    }

    public double getMiddleNumber() {
        return middleNumber;
    }

    public StandardPartCheckResultDetail setMiddleNumber(double middleNumber) {
        this.middleNumber = middleNumber;
        return this;
    }

    public double getNumber() {
        return number;
    }

    public StandardPartCheckResultDetail setNumber(double number) {
        this.number = number;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        StandardPartCheckResultDetail standardPartCheckResultDetail = (StandardPartCheckResultDetail) o;
        if (standardPartCheckResultDetail.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), standardPartCheckResultDetail.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
