package net.airuima.rworker.service.rworker.process;

import com.google.common.collect.Lists;
import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepSpecification;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.dto.rworker.process.dto.general.ProcessDocumentGetInfo;

import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 获取待做工序的额外扩展配置信息Service
 *
 * <AUTHOR>
 * @date 2023/2/20
 */
@FuncDefault
public interface IToDoStepConfigService {

    /**
     * 通过产品谱系、工艺路线及工序和客户获取工序技术规格和sop
     *
     * @param pedigree 产品谱系
     * @param workFlow 工艺路线
     * @param step     工序
     * @param clientId 客户主键id
     * @return PedigreeStepSpecification 产品谱系工序指标
     */
    @FuncInterceptor("ESop")
    default PedigreeStepSpecification findStepSpecificationSop(Pedigree pedigree, WorkFlow workFlow, Step step, Long clientId) {
        return null;
    }

    /**
     * 获取工序sop 配置信息
     *
     * @param pedigreeStepSpecification 产品谱系工序指标
     * @return List<ProcessDocumentGetInfo>
     */
    @FuncInterceptor("ESop")
    default List<ProcessDocumentGetInfo> getStepSpecificationSop(PedigreeStepSpecification pedigreeStepSpecification) {
        return Lists.newArrayList();
    }
}
