package net.airuima.rworker.service.enviroment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.AbstractDto;
import net.airuima.rbase.domain.base.scene.OrganizationArea;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 洁净度检测标准Domain
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Schema(name = "洁净度检测标准(CleanlinessStandard)", description = "洁净度检测标准")
public class CleanlinessStandardDTO extends AbstractDto implements Serializable {

    /**
     * 部门区域ID
     */
    @Schema(description = "部门区域ID")
    private OrganizationArea area;

    /**
     * 粒子半径
     */
    @Schema(description = "粒子半径", required = true)
    private List<String> particlesRadiusList;

    /**
     * 最大浓度限制（pc/m）
     */
    @Schema(description = "最大浓度限制（pc/m）", required = true)
    private List<Integer> particlesNumberList;

    /**
     * 洁净等级
     */
    @Schema(description = "洁净等级", required = true)
    private CleanlinessGradeDTO cleanlinessGradeDTO;

    /**
     * 测试次数
     */
    @Schema(description = "测试次数", required = true)
    private int testTimes;

    /**
     * 周期数值
     */
    @Schema(description = "周期数值", required = true)
    private int periodNumber;

    /**
     * 周期单位(0:小时，1:天，2:周，3:月，4:年)
     */
    @Schema(description = "周期单位(0:小时，1:天，2:周，3:月，4:年)", required = true)
    private int periodUnit;

    /**
     * 提醒数值
     */
    @Schema(description = "提前提醒周期")
    private int warnDuration;

    /**
     * 提醒周期
     */
    @Schema(description = "提前提醒周期单位(0:小时，1:天，2:周，3:月，4:年)")
    private int warnUnit;

    public OrganizationArea getArea() {
        return area;
    }

    public CleanlinessStandardDTO setArea(OrganizationArea area) {
        this.area = area;
        return this;
    }

    public List<String> getParticlesRadiusList() {
        return particlesRadiusList;
    }

    public CleanlinessStandardDTO setParticlesRadiusList(List<String> particlesRadiusList) {
        this.particlesRadiusList = particlesRadiusList;
        return this;
    }

    public List<Integer> getParticlesNumberList() {
        return particlesNumberList;
    }

    public CleanlinessStandardDTO setParticlesNumberList(List<Integer> particlesNumberList) {
        this.particlesNumberList = particlesNumberList;
        return this;
    }


    public CleanlinessGradeDTO getCleanlinessGrade() {
        return cleanlinessGradeDTO;
    }

    public CleanlinessStandardDTO setCleanlinessGrade(CleanlinessGradeDTO cleanlinessGradeDTO) {
        this.cleanlinessGradeDTO = cleanlinessGradeDTO;
        return this;
    }

    public int getTestTimes() {
        return testTimes;
    }

    public CleanlinessStandardDTO setTestTimes(int testTimes) {
        this.testTimes = testTimes;
        return this;
    }

    public int getPeriodNumber() {
        return periodNumber;
    }

    public CleanlinessStandardDTO setPeriodNumber(int periodNumber) {
        this.periodNumber = periodNumber;
        return this;
    }

    public int getPeriodUnit() {
        return periodUnit;
    }

    public CleanlinessStandardDTO setPeriodUnit(int periodUnit) {
        this.periodUnit = periodUnit;
        return this;
    }

    public int getWarnDuration() {
        return warnDuration;
    }

    public CleanlinessStandardDTO setWarnDuration(int warnDuration) {
        this.warnDuration = warnDuration;
        return this;
    }

    public int getWarnUnit() {
        return warnUnit;
    }

    public CleanlinessStandardDTO setWarnUnit(int warnUnit) {
        this.warnUnit = warnUnit;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CleanlinessStandardDTO cleanlinessStandardDTO = (CleanlinessStandardDTO) o;
        if (cleanlinessStandardDTO.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), cleanlinessStandardDTO.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
