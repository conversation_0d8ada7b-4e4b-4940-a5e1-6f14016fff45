package net.airuima.rworker.service.client.maintaincase.impl;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.Container;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.domain.procedure.single.SnRework;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.dto.batch.PreContainerDetailInfo;
import net.airuima.rbase.dto.client.ClientSaveStepInfoDTO;
import net.airuima.rbase.repository.base.quality.UnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.rbase.repository.procedure.batch.ContainerDetailRepository;
import net.airuima.rbase.repository.procedure.batch.ContainerRepository;
import net.airuima.rbase.repository.procedure.single.SnReworkRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkStatusRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rworker.service.client.ClientStepService;
import net.airuima.rworker.service.client.maintaincase.ProcessModeService;
import net.airuima.util.ResponseException;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/9/28
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
@FuncDefault
public class DefaultProcessModeServiceImpl implements ProcessModeService {

    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private ContainerRepository containerRepository;
    @Autowired
    private ClientStepService clientStepService;
    @Autowired
    private SnWorkStatusRepository snWorkStatusRepository;
    @Autowired
    private SnWorkDetailRepository snWorkDetailRepository;
    @Autowired
    private UnqualifiedItemRepository unqualifiedItemRepository;
    @Autowired
    private SnReworkRepository snReworkRepository;
    @Autowired
    private CommonService commonService;


    @Override
    public BaseClientDTO maintainAnalyse(List<Long> requestContainerIds, SubWorkSheet subWorkSheet, WsStep currWsStep) {
        return ProcessModeService.super.maintainAnalyse(requestContainerIds, subWorkSheet,currWsStep);
    }

    /**
     * 保存工序批次详情信息（默认实现）
     * @param subWorkSheet 子工单
     * @param wsStep 工序
     * @param workCell 工位
     * @param clientSaveStepInfoDto 请求保存信息DTO
     * <AUTHOR>
     * @date  2022/9/28
     * @return BatchWorkDetail
     */
    @Override
    public BatchWorkDetail saveBatchWorkDetail(SubWorkSheet subWorkSheet, WsStep wsStep, WorkCell workCell, ClientSaveStepInfoDTO clientSaveStepInfoDto) {
        Optional<BatchWorkDetail> batchWorkDetailOptional = batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), wsStep.getStep().getId(), Constants.LONG_ZERO);
        //保存工序批量生产详情
        BatchWorkDetail batchWorkDetail = batchWorkDetailOptional.orElseGet(BatchWorkDetail::new);
        batchWorkDetail.setInputNumber(batchWorkDetail.getInputNumber() + clientSaveStepInfoDto.getNumber())
                .setFinishNumber(batchWorkDetail.getFinishNumber() + clientSaveStepInfoDto.getNumber())
                .setUnqualifiedNumber(batchWorkDetail.getUnqualifiedNumber() + clientSaveStepInfoDto.getUnqualifiedNumber())
                .setQualifiedNumber(batchWorkDetail.getQualifiedNumber() + clientSaveStepInfoDto.getQualifiedNumber())
                .setEffectNumber(batchWorkDetail.getEffectNumber() + clientSaveStepInfoDto.getNumber())
                .setStartDate(batchWorkDetail.getId() == null ? clientSaveStepInfoDto.getStartTime() : batchWorkDetail.getStartDate())
                .setEndDate(clientSaveStepInfoDto.getEndTime())
                .setWorkHour(ChronoUnit.MINUTES.between(clientSaveStepInfoDto.getStartTime(), clientSaveStepInfoDto.getEndTime()))
                .setStep(wsStep.getStep())
                .setOperatorId(clientSaveStepInfoDto.getStaffId())
                .setSubWorkSheet(subWorkSheet)
                .setWorkCell(workCell)
                .setFinish(Constants.INT_ZERO);
        return batchWorkDetail;
    }

    /**
     * 保存容器详情信息
     *
     * @param clientSaveStepInfoDto 请求保存的参数
     * @param batchWorkDetail       批量详情
     * @param isLastStep            是否为最后一个工序
     * @return ContainerDetail
     **/
    @Override
    public ContainerDetail saveContainerInfo(ClientSaveStepInfoDTO clientSaveStepInfoDto, BatchWorkDetail batchWorkDetail, WsStep wsStep,boolean isLastStep) {

        //保存前置容器详情信息，创建当前容器详情
        ContainerDetail containerDetail = savePreContainerDetails(clientSaveStepInfoDto, batchWorkDetail, wsStep, isLastStep);

        if (null != containerDetail){
            if (isLastStep){
                containerDetail.setTransferNumber(Constants.INT_ZERO);
                containerDetail.setUnbindTime(batchWorkDetail.getEndDate());
                containerDetail.setStatus(ConstantsEnum.UNBIND.getCategoryName());
            }
            //合格数量为零时，解绑容器
            if (Constants.INT_ZERO == containerDetail.getQualifiedNumber()){
                containerDetail.setUnbindTime(LocalDateTime.now())
                        .setStatus(ConstantsEnum.UNBIND.getCategoryName());
                containerDetail.getContainer().setStatus(Constants.FALSE);
                //当前容器是当前工序的最后一个容器且合格数为零，需要修改当前工序后置所有工序的状态（依据条件而定）
                if (ConstantsEnum.FINISH_STATUS.getCategoryName() == batchWorkDetail.getFinish()){
                    clientStepService.updateBatchDetail(batchWorkDetail);
                }
            }
            containerDetailRepository.save(containerDetail);
            return containerDetail;
        }
        return null;
    }

    /**
     * 保存前置容器详情信息，创建当前容器详情
     * @param clientSaveStepInfoDto 保存内容
     * @param batchWorkDetail 当前批次
     * @param isLastStep 是否为最后工序
     * <AUTHOR>
     * @date  2022/10/18
     * @return ContainerDetail
     */
    @Override
    public ContainerDetail savePreContainerDetails(ClientSaveStepInfoDTO clientSaveStepInfoDto, BatchWorkDetail batchWorkDetail,WsStep wsStep, boolean isLastStep){
        //原容器id以及对应的待下交数量
        List<ClientSaveStepInfoDTO.RequestContainerInfo> requestContainerInfos = clientSaveStepInfoDto.getRequestContainerInfos();
        //请求(原)容器id列表
        List<Long> requestContainerIdList = ValidateUtils.isValid(requestContainerInfos)?requestContainerInfos.stream().map(ClientSaveStepInfoDTO.RequestContainerInfo::getContainerId).collect(Collectors.toList()):null;
        //对于不切换容器时当前容器作为下个工序的绑定容器
        Long bindContainerId = null == clientSaveStepInfoDto.getBindContainerId() && ValidateUtils.isValid(requestContainerIdList) ? requestContainerIdList.get(Constants.INT_ZERO) : clientSaveStepInfoDto.getBindContainerId();
        //是否为A+B->A的模式，即容器复用
        boolean isReUseContainer = ValidateUtils.isValid(requestContainerIdList) && null != bindContainerId && requestContainerIdList.contains(bindContainerId);
        //判断当前工序是否为返修工序
        boolean isReworkStep = batchWorkDetail.getStep().getCategory() == Constants.INT_ONE || batchWorkDetail.getStep().getCategory() == Constants.INT_TWO;
        //获取请求的容器未解绑的容器详情
        List<ContainerDetail> preContainerDetailList = ValidateUtils.isValid(requestContainerIdList) ? containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndContainerIdInAndStatusAndDeletedOrderByIdDesc(
                clientSaveStepInfoDto.getSubWsId(), requestContainerIdList, Constants.INT_ONE, Constants.LONG_ZERO) : null;
        //当前工序为返修工序且前置工序为预返修工序时，需要统计前置工序的待下交数量与合格数
        int preContainerTransferNumber = Constants.INT_ZERO;
        int preContainerQualifiedNumber = Constants.INT_ZERO;
        if (batchWorkDetail.getStep().getCategory() == Constants.INT_ONE && ValidateUtils.isValid(preContainerDetailList)) {
            preContainerTransferNumber = preContainerDetailList.stream().mapToInt(ContainerDetail::getTransferNumber).sum();
            preContainerQualifiedNumber = preContainerDetailList.stream().mapToInt(ContainerDetail::getQualifiedNumber).sum();
        }
        //获取容器详情前置数据ID，方便后面批量删除回退数据
        List<PreContainerDetailInfo> preContainerDetailInfos = Lists.newArrayList();
        if (ValidateUtils.isValid(preContainerDetailList)){
            preContainerDetailList.forEach(preContainerDetail ->{
                ClientSaveStepInfoDTO.RequestContainerInfo requestContainerInfo = requestContainerInfos.stream().filter(requestContainer -> requestContainer.getContainerId().equals(preContainerDetail.getContainer().getId())).findFirst().orElse(null);
                if (requestContainerInfo != null){
                    PreContainerDetailInfo preContainerDetailInfo = new PreContainerDetailInfo(preContainerDetail.getId(),requestContainerInfo.getNumber());
                    preContainerDetailInfos.add(preContainerDetailInfo);
                }
            });
        }
        String preContainerCodeList = ValidateUtils.isValid(preContainerDetailList)? StringUtils.join(preContainerDetailList.stream().map(containerDetail -> containerDetail.getContainer().getCode()).collect(Collectors.toList()), Constants.STR_SEMICOLON):null;
        //处理A+B->A模式的各个容器的前置工序的剩余数量
        if (isReUseContainer) {
            Optional<ContainerDetail> containerDetailOptional = preContainerDetailList.stream().filter(containerDetail -> containerDetail.getContainer().getId().equals(bindContainerId)).findFirst();
            containerDetailOptional.ifPresent(containerDetail -> {
                //原容器待下交信息
                ClientSaveStepInfoDTO.RequestContainerInfo requestContainerInfo = requestContainerInfos.stream().filter(requestContainer -> requestContainer.getContainerId().equals(containerDetail.getContainer().getId())).findFirst().get();
                //更新原容器状态
                containerDetail.setTransferNumber(!isReworkStep ? containerDetail.getTransferNumber() - requestContainerInfo.getNumber() : Constants.INT_ZERO);
                containerDetail.setStatus(containerDetail.getTransferNumber() == Constants.INT_ZERO ? Constants.INT_ZERO : Constants.INT_ONE);
                containerDetail.setUnbindTime(containerDetail.getTransferNumber() == Constants.INT_ZERO ? batchWorkDetail.getEndDate() : null);
                containerDetail.setAfterContainerCodeList(null == containerDetail.getAfterContainerCodeList()?containerRepository.getReferenceById(bindContainerId).getCode():containerDetail.getAfterContainerCodeList()+Constants.STR_SEMICOLON+containerRepository.getReferenceById(bindContainerId).getCode());
                containerDetailRepository.save(containerDetail);
            });
            //更新各个前置工序的剩余待流转数量(如果一个前置工序有多个容器则按照流转数量从小到大进行扣数)
            List<ContainerDetail> containerDetailList = preContainerDetailList.stream().filter(containerDetail -> !containerDetail.getContainer().getId().equals(bindContainerId)).collect(Collectors.toList());
            if (ValidateUtils.isValid(containerDetailList)) {
                //按照前置工序进行分组
                Map<BatchWorkDetail, List<ContainerDetail>> map = containerDetailList.stream().collect(Collectors.groupingBy(ContainerDetail::getBatchWorkDetail));
                map.forEach((key, value) -> {
                    clientStepService.updatePreStepContainerDetailInfo(clientSaveStepInfoDto,containerDetailList);
                });
            }
        } else if (ValidateUtils.isValid(requestContainerIdList)) {
            //处理非A+B->A的模式
            List<ContainerDetail> containerDetailList = containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndContainerIdInAndStatusAndDeletedOrderByIdDesc(
                    clientSaveStepInfoDto.getSubWsId(),requestContainerIdList, Constants.INT_ONE, Constants.LONG_ZERO);
            if (ValidateUtils.isValid(containerDetailList)) {
                Map<BatchWorkDetail, List<ContainerDetail>> map = containerDetailList.stream().collect(Collectors.groupingBy(ContainerDetail::getBatchWorkDetail));
                map.forEach((key, value) -> {
                    clientStepService.updatePreStepContainerDetailInfo(clientSaveStepInfoDto,containerDetailList);
                });
            }
        }

        if (null != bindContainerId) {
            Container bindContainer = containerRepository.getReferenceById(bindContainerId);
            ContainerDetail containerDetail = containerDetailRepository.findTop1ByBatchWorkDetailIdAndContainerIdAndStatusAndDeletedOrderByIdDesc(batchWorkDetail.getId(),
                    bindContainerId, Constants.INT_ONE, Constants.LONG_ZERO).orElseGet(ContainerDetail::new);
            containerDetail.setBatchWorkDetail(batchWorkDetail);
            containerDetail.setBindTime(containerDetail.getId() != null ? containerDetail.getBindTime() : batchWorkDetail.getEndDate());
            containerDetail.setStaffId(clientSaveStepInfoDto.getStaffId());
            containerDetail.setRecordDate(containerDetail.getId() != null ? containerDetail.getRecordDate() : batchWorkDetail.getEndDate());
            containerDetail.setContainer(bindContainer);
            containerDetail.setContainerCode(bindContainer.getCode());
            containerDetail.setStatus(Constants.INT_ONE);
            containerDetail.setInputNumber(containerDetail.getInputNumber() + clientSaveStepInfoDto.getNumber());
            containerDetail.setQualifiedNumber(containerDetail.getQualifiedNumber() + clientSaveStepInfoDto.getQualifiedNumber());
            containerDetail.setUnqualifiedNumber(containerDetail.getUnqualifiedNumber() + clientSaveStepInfoDto.getUnqualifiedNumber());
            containerDetail.setWorkCell(batchWorkDetail.getWorkCell()).setStartTime(containerDetail.getId() != null ? containerDetail.getStartTime():clientSaveStepInfoDto.getStartTime());
            if (ValidateUtils.isValid(containerDetail.getPreContainerDetailInfoList())){
                List<PreContainerDetailInfo> preContainerDetailInfoList = containerDetail.getPreContainerDetailInfoList();
                preContainerDetailInfoList.addAll(preContainerDetailInfos);
                containerDetail.setPreContainerDetailInfoList(preContainerDetailInfoList);
            }else {
                containerDetail.setPreContainerDetailInfoList(preContainerDetailInfos);
            }
            containerDetail.setPreContainerCodeList(ValidateUtils.isValid(containerDetail.getPreContainerCodeList())?containerDetail.getPreContainerCodeList()+Constants.STR_SEMICOLON+preContainerCodeList:preContainerCodeList);
            if (!isLastStep) {
                //当前工序为返修工序，若前面工序为预返修工序则待下交数量=前工序的待下交数+返修合格数量，否则=前工序的合格数+返修合格数
                if (batchWorkDetail.getStep().getCategory() == Constants.INT_ONE) {
                    final int preContainerTransferTempNumber = preContainerTransferNumber;
                    final int preContainerQualifiedTempNumber = preContainerQualifiedNumber;
                    Optional<ContainerDetail> containerDetailOptional = preContainerDetailList.stream().filter(containerDetailTemp -> containerDetailTemp.getContainer().getId().equals(bindContainerId)).findFirst();
                    containerDetailOptional.ifPresent(containerDetailTemp -> {
                        containerDetail.setTransferNumber(containerDetailTemp.getBatchWorkDetail().getStep().getCategory() == Constants.INT_TWO ? preContainerTransferTempNumber : preContainerQualifiedTempNumber + clientSaveStepInfoDto.getQualifiedNumber());
                    });
                } else if (batchWorkDetail.getStep().getCategory() == Constants.INT_TWO) {
                    //当前工序为预返修工序时待下交数量为容器上工序合格数
                    containerDetail.setTransferNumber(preContainerTransferNumber);
                } else {
                    containerDetail.setTransferNumber(containerDetail.getTransferNumber() + clientSaveStepInfoDto.getQualifiedNumber());
                }
            }
            return containerDetail;
        }
        return null;
    }

    /**
     * 保存SN相关信息(标准流程)
     *
     * @param clientSaveStepInfoDto 待保存的工序信息
     * @param subWorkSheet          子工单
     * @param step                  工序
     * @param workCell              工位
     * @param containerDetail       容器详情
     * @param isLastStep            是否为最后一个工序
     */
    @Override
    public List<SnWorkDetail> saveSnInfo(ClientSaveStepInfoDTO clientSaveStepInfoDto, SubWorkSheet subWorkSheet, Step step, WorkCell workCell, ContainerDetail containerDetail, boolean isLastStep) {
        List<SnWorkDetail> snWorkDetailList = Lists.newArrayList();
        clientSaveStepInfoDto.getSnInfoList().forEach(snInfo -> {
            UnqualifiedItem unqualifiedItem = snInfo.getResult() == Constants.INT_ZERO && snInfo.getUnqualifiedItemId() != null ? unqualifiedItemRepository.getReferenceById(snInfo.getUnqualifiedItemId()) : null;
            //保存SN生产详情
            SnWorkDetail snWorkDetail = new SnWorkDetail();
            snWorkDetail.setSn(snInfo.getSn())
                    .setStartDate(snInfo.getStartTime())
                    .setEndDate(snInfo.getEndTime())
                    .setWorkHour(ChronoUnit.MINUTES.between(snInfo.getStartTime(), snInfo.getEndTime()))
                    .setReworkTime(Constants.INT_ZERO)
                    .setResult(snInfo.getResult())
                    .setSubWorkSheet(subWorkSheet)
                    .setStep(step)
                    .setOperatorId(clientSaveStepInfoDto.getStaffId())
                    .setUnqualifiedItem(unqualifiedItem)
                    .setWorkCell(workCell)
                    .setContainerDetail(containerDetail);
            snWorkDetail = snWorkDetailRepository.save(snWorkDetail);
            //保存或更新SN生产状态
            Optional<SnWorkStatus> snWorkStatusOptional = snWorkStatusRepository.findBySnAndDeleted(snInfo.getSn(), Constants.LONG_ZERO);
            SnWorkStatus snWorkStatus = snWorkStatusOptional.orElse(null);
            //获取sn生产状态
            int status = clientStepService.getSnWorkStatusByStatus(snInfo,subWorkSheet,isLastStep);
            if (snWorkStatus == null) {
                snWorkStatus = new SnWorkStatus();
                //获取定制工序中工艺路线
                WorkFlow snapshotWorkFlow = commonService.findSnapshotWorkFlow(subWorkSheet.getWorkSheet(), subWorkSheet,step);
                snWorkStatus.setSn(snInfo.getSn())
                        .setReworkTime(Constants.INT_ZERO)
                        .setStatus(status)
                        .setIsUpdateBatchWorkDetail(false)
                        .setStartDate(snInfo.getStartTime())
                        .setEndDate(snInfo.getEndTime())
                        .setSubWorkSheet(subWorkSheet)
                        .setWorkFlow(snapshotWorkFlow);
            } else {
                snWorkStatus.setStatus(status);
                //当前sn绑定的工单是否与当前请求工作工单一致，不一致则，验证是否存在 返修关联关系，存在认为当前sn为第一次反工录入，需要切换工单号，反之报错，sn混盘
                if (!subWorkSheet.getId().equals(snWorkStatus.getSubWorkSheet().getId())){
                    //sn绑定的工单不是正常单
                    if (subWorkSheet.getWorkSheet().getCategory() != Constants.INT_ONE){
                        //验证当前sn是否与当前请求工单存在返工关联关系
                        Optional<SnRework> snReworkOptional = snReworkRepository.findBySnWorkStatusIdAndWsReworkReworkWorkSheetIdAndDeleted(snWorkStatus.getId(), subWorkSheet.getWorkSheet().getId(), Constants.LONG_ZERO);
                        if (snReworkOptional.isPresent()){
                            snWorkStatus.setSubWorkSheet(subWorkSheet);
                        }else {
                            throw new ResponseException("error.snWorkSheetError", "当前sn["+snWorkStatus.getSn()+"]绑定工单["+snWorkStatus.getSubWorkSheet().getSerialNumber()+"]与请求工单["+subWorkSheet.getSerialNumber()+"]不一致");
                        }
                    }else {
                        throw new ResponseException("error.snWorkSheetError", "当前sn["+snWorkStatus.getSn()+"]绑定工单["+snWorkStatus.getSubWorkSheet().getSerialNumber()+"]与请求工单["+subWorkSheet.getSerialNumber()+"]不一致");
                    }
                }
            }
            if (isLastStep) {
                snWorkStatus.setEndDate(snInfo.getEndTime());
            }
            if (snInfo.getResult() == Constants.INT_ZERO) {
                snWorkStatus.setLatestReworkSnWorkDetail(snWorkDetail);
                snWorkStatus.setReworkTime(snWorkStatus.getReworkTime() + Constants.INT_ONE);
                snWorkStatus.setLatestUnqualifiedItem(unqualifiedItem);
                //保存SN不良信息
                clientStepService.saveSnUnqualifiedItemInfo(subWorkSheet, step, snInfo,snWorkDetail);
            }
            snWorkStatus.setLatestSnWorkDetail(snWorkDetail);
            snWorkStatusRepository.save(snWorkStatus);
            snWorkDetailList.add(snWorkDetail);
        });
        return snWorkDetailList;
    }
}
