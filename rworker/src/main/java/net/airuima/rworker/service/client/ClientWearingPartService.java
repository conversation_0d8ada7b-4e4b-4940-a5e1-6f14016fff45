package net.airuima.rworker.service.client;

import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.WearingPartCategoryEnum;
import net.airuima.rbase.domain.base.wearingpart.WearingPart;
import net.airuima.rbase.domain.base.wearingpart.WearingPartExchange;
import net.airuima.rbase.dto.client.ClientGetWearingPartInfoDTO;
import net.airuima.rbase.repository.base.wearingpart.WearingPartExchangeRepository;
import net.airuima.rbase.repository.base.wearingpart.WearingPartRepository;
import net.airuima.rbase.util.DateUtils;
import net.airuima.rbase.util.MapperUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rworker.service.client.api.IClientWearingPartService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/6/24
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class ClientWearingPartService implements IClientWearingPartService {

    @Autowired
    private WearingPartExchangeRepository wearingPartExchangeRepository;

    @Autowired
    private WearingPartRepository wearingPartRepository;

    /**
     * 验证易损件是否可用
     *
     * @param wearingPartInfoDto 易损件相关信息参数
     * @return net.airuima.dto.client.ClientGetWearingPartInfoDTO 易损件相关信息
     */
    @Transactional(readOnly = true)
    @Override
    public ClientGetWearingPartInfoDTO validateWearingPartStatus(ClientGetWearingPartInfoDTO wearingPartInfoDto) {
        if (!ValidateUtils.isValid(wearingPartInfoDto.getCode())) {
            return new ClientGetWearingPartInfoDTO(new BaseClientDTO(Constants.KO, "sn号不能为空"));
        }
        List<WearingPart> wearingParts = wearingPartRepository.findByCodeAndDeleted(wearingPartInfoDto.getCode(), Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(wearingParts)) {
            return new ClientGetWearingPartInfoDTO(new BaseClientDTO(Constants.KO, "sn号不存在"));
        }
        WearingPart wearingPart = wearingParts.get(Constants.INT_ZERO);
        // 易损件使用状态（0可用，1在用，2超期，3报废）,累计重置次数大于最大重置次数
        if ((wearingPart.getStatus() == WearingPartCategoryEnum.SCRAP.getCategory())
                || (wearingPart.getMaxResetNumber() < wearingPart.getAccumulateResetNumber())) {
            return new ClientGetWearingPartInfoDTO(new BaseClientDTO(Constants.KO, "sn号已报废"));
        }
        // 易损件的状态为超期，需要通知人工手动重置
        if (wearingPart.getStatus() == WearingPartCategoryEnum.EXCEED.getCategory()) {
            return new ClientGetWearingPartInfoDTO(new BaseClientDTO(Constants.KO, "sn号已超期，未处理"));
        }
        // 判断易损件是否符合替换关系，相同类型跳过校验
        if (!wearingPartInfoDto.getWearingPartGroupId().equals(wearingPart.getWearingPartGroup().getId())){
            WearingPartExchange wearingPartExchange = wearingPartExchangeRepository.findByOriginWearingPartGroupIdAndExchangeWearingPartGroupIdAndDeleted(wearingPartInfoDto.getWearingPartGroupId(), wearingPart.getWearingPartGroup().getId(), Constants.LONG_ZERO);
            if (wearingPartExchange == null){
                return new ClientGetWearingPartInfoDTO(new BaseClientDTO(Constants.KO, "当前易损件，与sn号对应的易损件不符合替代关系"));
            }
        }
        // 提前预警易损件（次数提前预警，时间给出剩余时间）
        ClientGetWearingPartInfoDTO clientGetWearingPartInfoDto = proactiveManualModelValidate(wearingPart);
        if (Constants.OK.equals(clientGetWearingPartInfoDto.getStatus())) {
            ClientGetWearingPartInfoDTO newClientGetWearingPartInfoDto = MapperUtils.map(wearingPart,
                    ClientGetWearingPartInfoDTO.class);
            newClientGetWearingPartInfoDto.setWearingPartId(wearingPart.getId())
                    .setWearingPartGroupId(wearingPart.getWearingPartGroup().getId())
                    .setWarningMessageDto(clientGetWearingPartInfoDto.getWarningMessageDto());
            newClientGetWearingPartInfoDto.setStatus(clientGetWearingPartInfoDto.getStatus());
            return newClientGetWearingPartInfoDto;
        }
        return clientGetWearingPartInfoDto;

    }

    /**
     * 提前预警手动模式下，各管控类型状态
     *
     * @param wearingPart 易损件信息
     * @return net.airuima.dto.client.ClientGetWearingPartInfoDTO
     */
    public ClientGetWearingPartInfoDTO proactiveManualModelValidate(WearingPart wearingPart) {
        ClientGetWearingPartInfoDTO clientGetWearingPartInfoDto = new ClientGetWearingPartInfoDTO();
        ClientGetWearingPartInfoDTO.WarningMessageDTO warningMessageDto = new ClientGetWearingPartInfoDTO.WarningMessageDTO();
        if (null != wearingPart) {
            // 易损件管控类型为0：次数
            String wearingPartCode = "{易损件编码:" + wearingPart.getCode();
            if (wearingPart.getCategory() == WearingPartCategoryEnum.FREQUENCY.getCategory()) {
                if (wearingPart.getMaxUseNumber() < wearingPart.getAccumulateUseNumber()
                        + Constants.INT_ONE) {
                    return new ClientGetWearingPartInfoDTO(new BaseClientDTO(Constants.KO,
                            wearingPartCode + "累计使用次数(大于)最大使用次数},请手动重置易损件状态(管控类型:次数)"));
                }
                warningMessageDto.setControlCategory("易损件管控类型为0：次数").setResidueNumber(
                        wearingPart.getMaxUseNumber() - wearingPart.getAccumulateUseNumber());
            }
            // 易损件管控类型为1：时长
            else if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION.getCategory()) {
                int currentAccumulateUseTime = wearingPart.getAccumulateUseTime();
                if (currentAccumulateUseTime > wearingPart.getMaxUseTime()) {
                    return new ClientGetWearingPartInfoDTO(new BaseClientDTO(Constants.KO,
                            wearingPartCode + "累计使用时间(大于)最大使用时间},请手动重置易损件状态(管控类型为:时长)"));
                }
                warningMessageDto.setControlCategory("易损件管控类型为1：时长").setResidueTime(
                        wearingPart.getMaxUseTime() - wearingPart.getAccumulateUseTime());
            }
            // 易损件管控类型为2：有效期
            else if (wearingPart.getCategory() == WearingPartCategoryEnum.VALIDITY.getCategory()) {
                if (wearingPart.getExpireDate().isBefore(LocalDateTime.now())) {
                    return new ClientGetWearingPartInfoDTO(new BaseClientDTO(Constants.KO,
                            wearingPartCode + "不在有效期内},请手动重置易损件状态(管控类型:有效期)"));
                }
                warningMessageDto.setControlCategory("易损件管控类型为2：有效期")
                        .setResidueDay(DateUtils.formatTime(Duration
                                .between(LocalDateTime.now(),
                                        wearingPart.getExpireDate())
                                .getSeconds() * 1000));
            }
            // 易损件管控类型为3：时长+次数
            else if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION_FREQUENCY.getCategory()) {
                if (wearingPart.getMaxUseNumber() < wearingPart.getAccumulateUseNumber()
                        + Constants.INT_ONE) {
                    return new ClientGetWearingPartInfoDTO(new BaseClientDTO(Constants.KO,
                            wearingPartCode + "累计使用次数(大于)最大使用次数},请手动重置易损件状态(管控类型:时长+次数)"));
                }
                int currentAccumulateUseTime = wearingPart.getAccumulateUseTime();
                if (currentAccumulateUseTime > wearingPart.getMaxUseTime()) {
                    return new ClientGetWearingPartInfoDTO(new BaseClientDTO(Constants.KO,
                            wearingPartCode + "累计使用时间(大于)最大使用时间},请手动重置易损件状态(管控类型:时长+次数)"));
                }
                warningMessageDto.setControlCategory("易损件管控类型为3：时长+次数")
                        .setResidueTime(wearingPart.getMaxUseTime()
                                - wearingPart.getAccumulateUseTime())
                        .setResidueNumber(wearingPart.getMaxUseNumber()
                                - wearingPart.getAccumulateUseNumber());
            }
            // 易损件管控类型为4：时长+有效期
            else if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION_VALIDITY.getCategory()) {
                int currentAccumulateUseTime = wearingPart.getAccumulateUseTime();
                if (currentAccumulateUseTime > wearingPart.getMaxUseTime()) {
                    return new ClientGetWearingPartInfoDTO(new BaseClientDTO(Constants.KO,
                            wearingPartCode + "累计使用时间(大于)最大使用时间},请手动重置易损件状态(管控类型:时长+有效期)"));
                }
                if (wearingPart.getExpireDate().isBefore(LocalDateTime.now())) {
                    return new ClientGetWearingPartInfoDTO(new BaseClientDTO(Constants.KO,
                            wearingPartCode + "不在有效期内},请手动重置易损件状态(管控类型:时长+有效期)"));
                }
                warningMessageDto.setControlCategory("易损件管控类型为4：时长+有效期")
                        .setResidueTime(wearingPart.getMaxUseTime()
                                - wearingPart.getAccumulateUseTime())
                        .setResidueDay(DateUtils.formatTime(Duration
                                .between(LocalDateTime.now(),
                                        wearingPart.getExpireDate())
                                .getSeconds() * 1000));
            }
            // 易损件管控类型为5：次数+有效期
            else if (wearingPart.getCategory() == WearingPartCategoryEnum.FREQUENCY_VALIDITY.getCategory()) {
                if (wearingPart.getMaxUseNumber() < wearingPart.getAccumulateUseNumber()
                        + Constants.INT_ONE) {
                    return new ClientGetWearingPartInfoDTO(new BaseClientDTO(Constants.KO,
                            wearingPartCode + "累计使用次数(大于)最大使用次数},请手动重置易损件状态(管控类型:次数+有效期)"));
                }
                if (wearingPart.getExpireDate().isBefore(LocalDateTime.now())) {
                    return new ClientGetWearingPartInfoDTO(new BaseClientDTO(Constants.KO,
                            wearingPartCode + "不在有效期内},请手动重置易损件状态(管控类型:次数+有效期)"));
                }
                warningMessageDto.setControlCategory("易损件管控类型为5：次数+有效期")
                        .setResidueNumber(wearingPart.getMaxUseNumber()
                                - wearingPart.getAccumulateUseNumber())
                        .setResidueDay(DateUtils.formatTime(Duration
                                .between(LocalDateTime.now(),
                                        wearingPart.getExpireDate())
                                .getSeconds() * 1000));
            }
            // 易损件管控类型为6：时长+次数+有效期
            else if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION_FREQUENCY_VALIDITY.getCategory()) {
                int currentAccumulateUseTime = wearingPart.getAccumulateUseTime();
                if (currentAccumulateUseTime > wearingPart.getMaxUseTime()) {
                    return new ClientGetWearingPartInfoDTO(new BaseClientDTO(Constants.KO,
                            wearingPartCode + "累计使用时间(大于)最大使用时间},请手动重置易损件状态(管控类型:时长+次数+有效期)"));
                }
                if (wearingPart.getMaxUseNumber() < wearingPart.getAccumulateUseNumber()
                        + Constants.INT_ONE) {
                    return new ClientGetWearingPartInfoDTO(new BaseClientDTO(Constants.KO,
                            wearingPartCode + "累计使用次数(大于)最大使用次数},请手动重置易损件状态(管控类型:时长+次数+有效期)"));
                }
                if (wearingPart.getExpireDate().isBefore(LocalDateTime.now())) {
                    return new ClientGetWearingPartInfoDTO(new BaseClientDTO(Constants.KO,
                            wearingPartCode + "不在有效期内},请手动重置易损件状态(管控类型:时长+次数+有效期)"));
                }
                warningMessageDto.setControlCategory("易损件管控类型为6：时长+次数+有效期")
                        .setResidueTime(wearingPart.getMaxUseTime()
                                - wearingPart.getAccumulateUseTime())
                        .setResidueNumber(wearingPart.getMaxUseNumber()
                                - wearingPart.getAccumulateUseNumber())
                        .setResidueDay(DateUtils.formatTime(Duration
                                .between(LocalDateTime.now(),
                                        wearingPart.getExpireDate())
                                .getSeconds() * 1000));
            }
        }
        // 是否存在提前预警信息
        if (ValidateUtils.isValid(warningMessageDto.getControlCategory())) {
            clientGetWearingPartInfoDto.setStatus(Constants.OK);
            clientGetWearingPartInfoDto.setWarningMessageDto(warningMessageDto);
        } else {
            clientGetWearingPartInfoDto.setStatus(Constants.KO);
            clientGetWearingPartInfoDto.setMessage("易损件记录不存在");
        }
        return clientGetWearingPartInfoDto;
    }
}
