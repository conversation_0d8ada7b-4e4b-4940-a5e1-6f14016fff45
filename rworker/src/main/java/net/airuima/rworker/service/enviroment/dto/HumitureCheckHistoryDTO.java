package net.airuima.rworker.service.enviroment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.AbstractDto;
import net.airuima.rbase.domain.base.scene.OrganizationArea;
import net.airuima.rbase.dto.organization.StaffDTO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 温湿度检测历史Domain
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Schema(name = "温湿度检测历史(HumitureCheckHistory)", description = "温湿度检测历史")
public class HumitureCheckHistoryDTO extends AbstractDto implements Serializable {

    /**
     * 部门区域ID
     */
    @Schema(description = "部门区域ID")
    private OrganizationArea area;

    /**
     * 温度
     */
    @Schema(description = "温度", required = true)
    private BigDecimal temperature;

    /**
     * 湿度
     */
    @Schema(description = "湿度", required = true)
    private BigDecimal humidity;

    /**
     * 结果（0：停线, 1：合格，2：预警）
     */
    @Schema(description = "结果（0：停线, 1：合格，2：预警）", required = true)
    private int result;

    /**
     * 检验人ID
     */
    @Schema(description = "检验人ID", required = true)
    private long operatorId;

    /**
     * 检验人DTO
     */
    @Schema(description = "检验人DTO", required = true)
    private StaffDTO operatorDto = new StaffDTO();

    /**
     * 检验日期
     */
    @Schema(description = "检验日期")
    private LocalDateTime recordDate;

    public BigDecimal getTemperature() {
        return temperature;
    }

    public HumitureCheckHistoryDTO setTemperature(BigDecimal temperature) {
        this.temperature = temperature;
        return this;
    }

    public BigDecimal getHumidity() {
        return humidity;
    }

    public HumitureCheckHistoryDTO setHumidity(BigDecimal humidity) {
        this.humidity = humidity;
        return this;
    }

    public int getResult() {
        return result;
    }

    public HumitureCheckHistoryDTO setResult(int result) {
        this.result = result;
        return this;
    }

    public long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(long operatorId) {
        this.operatorId = operatorId;
    }

    public OrganizationArea getArea() {
        return area;
    }

    public HumitureCheckHistoryDTO setArea(OrganizationArea area) {
        this.area = area;
        return this;
    }

    public StaffDTO getOperatorDto() {
        return operatorDto;
    }

    public void setOperatorDto(StaffDTO operatorDto) {
        this.operatorDto = operatorDto;
    }

    public LocalDateTime getRecordDate() {
        return recordDate;
    }

    public HumitureCheckHistoryDTO setRecordDate(LocalDateTime recordDate) {
        this.recordDate = recordDate;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        HumitureCheckHistoryDTO humitureCheckHistoryDTO = (HumitureCheckHistoryDTO) o;
        if (humitureCheckHistoryDTO.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), humitureCheckHistoryDTO.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
