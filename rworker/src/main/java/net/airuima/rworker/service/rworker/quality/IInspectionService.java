package net.airuima.rworker.service.rworker.quality;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.dto.base.BaseDTO;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/4/28
 */
@FuncDefault
public interface IInspectionService {

    /**
     * 首检检测
     * @param productWorkSheetId  待投产子工单/工单主键ID
     * @param workCell 检测工位
     * @param firstTimeWork 是否开班：true：开班 ：false：非开班
     * author YangS
     * @date  2023/5/4
     * @return net.airuima.rbase.dto.base.BaseDTO  基础响应信息
     */
    @FuncInterceptor(value = "FAI")
    default BaseDTO faiInspectionInfo(Boolean firstTimeWork,Long productWorkSheetId, WorkCell workCell){
        return new BaseDTO(Constants.OK);
    }

    /**
     * 巡检检测
     * @param productWorkSheetId  待投产子工单/工单主键ID
     * @param workCell  检测工位
     * @param firstTimeWork 是否开班：true：开班 ：false：非开班
     * author YangS
     * @date  2023/5/4
     * @return net.airuima.rbase.dto.base.BaseDTO  基础响应信息
     */
    @FuncInterceptor(value = "IPQC")
    default BaseDTO ipqcInspectionInfo(Boolean firstTimeWork,Long productWorkSheetId, WorkCell workCell){
        return new BaseDTO(Constants.OK);
    }

    @FuncInterceptor(value = "PQC")
    default void pqcInspectionInfo(){

    }

    @FuncInterceptor(value = "FQC")
    default void fqcInspectionInfo(){

    }

    @FuncInterceptor(value = "LQC")
    default void lqcInspectionInfo(){

    }
}
