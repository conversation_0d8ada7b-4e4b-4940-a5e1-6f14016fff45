package net.airuima.rworker.service.client;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.scene.WorkCellStaffStatus;
import net.airuima.rbase.dto.client.ClientStaffDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.rbase.proxy.organization.RbaseStaffProxy;
import net.airuima.rbase.proxy.rule.RbaseSysCodeProxy;
import net.airuima.rbase.repository.base.scene.WorkCellStaffRepository;
import net.airuima.rbase.repository.procedure.scene.WorkCellStaffStatusRepository;
import net.airuima.rworker.service.client.api.IClientStaffService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * RWork员工相关Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class ClientStaffService implements IClientStaffService {

    @Autowired
    private RbaseStaffProxy rbaseStaffProxy;
    @Autowired
    private WorkCellStaffRepository workCellStaffRepository;
    @Autowired
    private RbaseSysCodeProxy rbaseSysCodeProxy;
    @Autowired
    private WorkCellStaffStatusRepository workCellStaffStatusRepository;

    /**
     * 通过员工号获取员工及工位信息
     *
     * @param clientStaffDto 员工信息DTO
     * @return net.airuima.dto.client.ClientStaffDTO 员工信息
     */
    @Override
    @Transactional(readOnly = true)
    public ClientStaffDTO getStaffInfo(ClientStaffDTO clientStaffDto) {
        StaffDTO staffDto = rbaseStaffProxy.findByCodeAndIsLeaveAndDeleted(clientStaffDto.getStaffCode(),Boolean.FALSE,Constants.LONG_ZERO).orElse(null);
        if (staffDto == null) {
            clientStaffDto.setStatus(Constants.KO);
            clientStaffDto.setMessage("员工不存在!");
            return clientStaffDto;
        }
        List<WorkCell> workCellList = workCellStaffRepository.findWorkCellByStaffId(staffDto.getId());
        List<ClientStaffDTO.WorkCellDTO> collect = workCellList.stream().map(ClientStaffDTO.WorkCellDTO::new).collect(Collectors.toList());
        clientStaffDto.setWorkCellDtoList(collect);
        clientStaffDto.setStaffId(staffDto.getId());
        clientStaffDto.setStaffName(staffDto.getName());
        clientStaffDto.setStaffOrganizationName(staffDto.getOrganization().getName());
        clientStaffDto.setStaffOrganizationCode(staffDto.getOrganization().getCode());
        clientStaffDto.setStatus(Constants.OK);
        return clientStaffDto;
    }

    /**
     * 验证员工脱岗时长合法性
     * 数据字典无配置或者配置不检查时则不验证
     * @param staffId 员工主键ID
     * @param workCellId 工位主键ID
     * @return Boolean true:合规 false:非法
     */
    @Override
    public Boolean validateJobOffTime(Long staffId,Long workCellId){
        //获取数据字典配置
        String result = rbaseSysCodeProxy.findByCode(Constants.KEY_VALIDATE_OFF_JOB);
        if(StringUtils.isBlank(result)){
            return Boolean.TRUE;
        }
        WorkCellStaffStatus workCellStaffStatus = workCellStaffStatusRepository.findByWorkCellIdAndStaffIdAndDeleted(workCellId,staffId,Constants.LONG_ZERO);
        if(null == workCellStaffStatus){
            workCellStaffStatus = new WorkCellStaffStatus();
            workCellStaffStatus.setWorkCell(new WorkCell(workCellId)).setStaffId(staffId).setLatestLoginTime(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
            workCellStaffStatusRepository.save(workCellStaffStatus);
            return Boolean.TRUE;
        }
        int expireDay = Integer.parseInt(result);
        boolean legal = LocalDateTime.now().compareTo(workCellStaffStatus.getLatestLoginTime().plusDays(expireDay))<Constants.INT_ZERO;
        if(legal){
            workCellStaffStatus.setLatestLoginTime(LocalDateTime.now());
            workCellStaffStatusRepository.save(workCellStaffStatus);
        }
        return legal;

    }
}
