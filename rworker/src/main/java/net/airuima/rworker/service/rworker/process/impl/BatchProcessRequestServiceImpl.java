package net.airuima.rworker.service.rworker.process.impl;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.constant.MaintainEnum;
import net.airuima.rbase.constant.WsEnum;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepSpecification;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.quality.StepWarningStandard;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.domain.procedure.quality.CheckHistory;
import net.airuima.rbase.domain.procedure.quality.InspectTask;
import net.airuima.rbase.domain.procedure.reinspect.StepReinspect;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.dto.document.DocumentDTO;
import net.airuima.rbase.dto.maintain.MaintainHistoryDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.rbase.dto.rworker.process.dto.BaseWaringDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerBatchToDoStepGetDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerBatchToDoStepRequestDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerStepProcessBaseDTO;
import net.airuima.rbase.dto.rworker.process.dto.general.FacilityGetInfo;
import net.airuima.rbase.dto.rworker.process.dto.general.ProcessDocumentGetInfo;
import net.airuima.rbase.dto.rworker.process.dto.general.WsStepGetInfo;
import net.airuima.rbase.proxy.document.RbaseDocumentProxy;
import net.airuima.rbase.proxy.maintain.RbaseMaintainHistoryProxy;
import net.airuima.rbase.proxy.organization.RbaseStaffProxy;
import net.airuima.rbase.proxy.rmps.RbaseRmpsProxy;
import net.airuima.rbase.proxy.rule.RbaseSysCodeProxy;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.quality.OnlineReworkRuleRepository;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.repository.base.scene.WorkCellStepRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepUnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.quality.CheckHistoryRepository;
import net.airuima.rbase.repository.procedure.quality.InspectTaskRepository;
import net.airuima.rbase.repository.procedure.reinspect.StepReinspectRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.ocmes.BakeCycleBakeAgeingModelService;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.rworker.service.rworker.cache.IRworkerCacheService;
import net.airuima.rworker.service.rworker.dynamic.IDynamicService;
import net.airuima.rworker.service.rworker.event.IEventService;
import net.airuima.rworker.service.rworker.facility.*;
import net.airuima.rworker.service.rworker.material.IMaterialService;
import net.airuima.rworker.service.rworker.oem.IOemService;
import net.airuima.rworker.service.rworker.process.IBatchProcessRequestService;
import net.airuima.rworker.service.rworker.process.ISnProcessRequestService;
import net.airuima.rworker.service.rworker.process.IToDoStepConfigService;
import net.airuima.rworker.service.rworker.process.IToDoStepStepValidateService;
import net.airuima.rworker.service.rworker.quality.IEnvironmentService;
import net.airuima.rworker.service.rworker.quality.IQualityService;
import net.airuima.util.BeanUtil;
import net.airuima.util.ResponseException;
import net.airuima.util.ValidateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/1/6
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class BatchProcessRequestServiceImpl implements IBatchProcessRequestService {
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private WorkCellStepRepository workCellStepRepository;
    @Autowired
    private WsStepUnqualifiedItemRepository wsStepUnqualifiedItemRepository;
    @Autowired
    private OnlineReworkRuleRepository onlineReworkRuleRepository;
    @Autowired
    private StepRepository stepRepository;
    @Autowired
    private WorkCellRepository workCellRepository;
    @Autowired
    private RbaseStaffProxy rbaseStaffProxy;
    @Autowired
    private RbaseMaintainHistoryProxy rbaseMaintainHistoryProxy;
    @Autowired
    private InspectTaskRepository inspectTaskRepository;
    @Autowired
    private CheckHistoryRepository checkHistoryRepository;
    @Autowired
    private IMaterialService[] materialServices;
    @Autowired
    private IToDoStepStepValidateService[] toDoStepStepValidateServices;
    @Autowired
    private IEnvironmentService[] environmentServices;
    @Autowired
    private IFacilityService[] facilityServices;
    @Autowired
    private IFacilityCalibrateService[] facilityCalibrateServices;
    @Autowired
    private IFacilityInspectionService[] facilityInspectionServices;
    @Autowired
    private IFacilityMaintainService[] facilityMaintainServices;
    @Autowired
    private IQualityService[] qualityServices;
    @Autowired
    private IDynamicService[] dynamicServices;
    @Autowired
    private IToDoStepConfigService[] toDoStepConfigServices;
    @Autowired
    private IWearingPartService[] wearingPartServices;
    @Autowired
    private ISnProcessRequestService[] snProcessRequestServices;
    @Autowired
    private BakeCycleBakeAgeingModelService[] bakeCycleBakeAgeingModelServices;
    @Autowired
    private IEventService[] eventServices;
    @Autowired
    private IRworkerCacheService[] rworkerCacheServices;
    @Autowired
    private StepReinspectRepository stepReinspectRepository;
    @Autowired
    private RbaseRmpsProxy rbaseRmpsProxy;
    @Autowired
    private RbaseDocumentProxy rbaseDocumentProxy;
    @Autowired
    private RbaseSysCodeProxy rbaseSysCodeProxy;
    @Autowired
    private IOemService[] oemServices;

    /**
     * 获取工单请求模式下的待做工序信息
     * @param rworkerBatchToDoStepRequestDTO 请求批量待做工序参数
     * @return net.airuima.web.rest.rworker.process.dto.RworkerBatchToDoStepGetDTO 批量待做工序信息
     */
    @Override
    @Transactional(readOnly = true)
    public RworkerBatchToDoStepGetDTO batchToDoStep(RworkerBatchToDoStepRequestDTO rworkerBatchToDoStepRequestDTO) {
        //获取系统配置的投产粒度(子工单或者工单)
        boolean subWsProductionMode = commonService.subWsProductionMode();
        SubWorkSheet subWorkSheet = subWsProductionMode ? subWorkSheetRepository.getReferenceById(rworkerBatchToDoStepRequestDTO.getProductWorkSheetId()):null;
        WorkSheet workSheet = subWsProductionMode && null != subWorkSheet? subWorkSheet.getWorkSheet() : workSheetRepository.getReferenceById(rworkerBatchToDoStepRequestDTO.getProductWorkSheetId());
        //检查工单状态是否合规可继续投产
        if (subWsProductionMode) {
            BeanUtil.getHighestPrecedenceBean(IBatchProcessRequestService.class).validateSubWorkSheetStatus(subWorkSheet);
        } else {
            BeanUtil.getHighestPrecedenceBean(IBatchProcessRequestService.class).validateWorkSheetStatus(workSheet);
        }
        RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO = new RworkerStepProcessBaseDTO();
        rworkerStepProcessBaseDTO.setSubWorkSheet(subWorkSheet).setWorkSheet(workSheet);
        //若投产粒度为子工单则获取子工单待投产工序信息,否则获取工单待投产工序信息
        RworkerBatchToDoStepGetDTO rworkerBatchToDoStepGetDTO = subWsProductionMode ? BeanUtil.getHighestPrecedenceBean(IBatchProcessRequestService.class).findSubWsNextTodoStep(rworkerBatchToDoStepRequestDTO, rworkerStepProcessBaseDTO) : BeanUtil.getHighestPrecedenceBean(IBatchProcessRequestService.class).findWorkSheetNextToDoStep(rworkerBatchToDoStepRequestDTO, rworkerStepProcessBaseDTO);
        //验证外协工序不能进行投产
        oemServices[0].validProcessOemStep(new WsStep().setCategory(rworkerBatchToDoStepGetDTO.getCategory()));
        //验证工序时间间隔是否合规
        BaseResultDTO baseResultDTO = toDoStepStepValidateServices[0].validateBatchStepInterval(rworkerStepProcessBaseDTO,subWsProductionMode);
        if(Objects.nonNull(baseResultDTO) && baseResultDTO.getStatus().equals(Constants.KO)){
            return new RworkerBatchToDoStepGetDTO(Constants.KO, baseResultDTO.getMessage(), baseResultDTO.getKey());

        }
        //验证指定工单工序工位是否合规
        toDoStepStepValidateServices[0].validWsStepWorkCell(rworkerStepProcessBaseDTO);
        //验证员工技能是否匹配当前待做工序
        toDoStepStepValidateServices[0].validateStaffSkill(rworkerStepProcessBaseDTO);
        //验证工位GRR是否合规
        toDoStepStepValidateServices[0].validateWorkCellGrr(rworkerStepProcessBaseDTO);
        //验证环境-温湿度及洁净度是否合规
        BaseWaringDTO baseWaringDTO = environmentServices[0].validateHumiture(rworkerStepProcessBaseDTO);
        if(null != baseWaringDTO){
            rworkerBatchToDoStepGetDTO.setKey(baseWaringDTO.getKey()).setMessage(baseWaringDTO.getMessage());
        }
        environmentServices[0].validateCleanliness(rworkerStepProcessBaseDTO);
        //若工序类型为在线调整工序则获取调整投产数
        if (rworkerBatchToDoStepGetDTO.getCategory() == ConstantsEnum.STEP_ONLINE_PRE_REWORK_CATEGORY.getCategoryName() || rworkerBatchToDoStepGetDTO.getCategory() == ConstantsEnum.STEP_ONLINE_REWORK_CATEGORY.getCategoryName()) {
            rworkerBatchToDoStepGetDTO.setNumber(this.findOnlineReworkStepTransferNumber(rworkerBatchToDoStepRequestDTO.getProductWorkSheetId(), rworkerBatchToDoStepGetDTO.getId(), subWsProductionMode,rworkerStepProcessBaseDTO));
        }
        //获取工位工序设备信息
        List<FacilityGetInfo> facilityGetInfoList = facilityServices[0].findStepFacilityInfo(rworkerBatchToDoStepRequestDTO.getWorkCellId(), rworkerBatchToDoStepGetDTO.getId());
        rworkerStepProcessBaseDTO.setFacilityGetInfoList(facilityGetInfoList);
        rworkerBatchToDoStepGetDTO.setFacilityInfoList(facilityGetInfoList);
        List<Long> facilityIdList = CollectionUtils.isEmpty(facilityGetInfoList)?null:facilityGetInfoList.stream().map(FacilityGetInfo::getId).collect(Collectors.toList());
        //验证请求的待做工序的基础信息是否存在未处理的事件
        eventServices[0].validateRequestStepExistUnProcessedEvent(rworkerStepProcessBaseDTO);
        //验证工位设备校准
        facilityCalibrateServices[0].validateFacilityCalibrate(rworkerBatchToDoStepRequestDTO.getWorkCellId(),facilityIdList);
        //验证设备基础状态
        facilityInspectionServices[0].validateFacilityBaseStatus(facilityIdList);
        //验证设备点检等是否合规
        facilityInspectionServices[0].validateFacilityPointInspection(facilityIdList);
        //验证设备巡检等是否合规
        facilityInspectionServices[0].validateFacilityPatrolInspection(facilityIdList);
        //验证设备是否存在逾期维保任务
        facilityMaintainServices[0].validateFacilityMaintain(facilityIdList);
        //获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = rworkerStepProcessBaseDTO.getWorkFlow();
        rworkerBatchToDoStepGetDTO.setWorkFlowId(snapshotWorkFlow.getId());
        //获取工序不良信息
        rworkerBatchToDoStepGetDTO.setUnqualifiedItemInfoList(qualityServices[0].findStepUnqualifiedItemInfo(rworkerBatchToDoStepGetDTO.getId(), workSheet.getPedigree(), snapshotWorkFlow.getId(), workSheet.getClientId()));
        //获取BOM物料清单列表
        rworkerBatchToDoStepGetDTO.setBomMaterialInfoList(materialServices[0].findBomMaterialInfo(workSheet.getId()));
        //获取当前工单工序快照
        List<WsStepGetInfo> wsStepGetInfoList = rworkerBatchToDoStepGetDTO.getWsStepInfoList();
        Optional<WsStepGetInfo> wsStepInfoOptional = wsStepGetInfoList.stream().filter(wsStepInfo -> wsStepInfo.getId().equals(rworkerBatchToDoStepGetDTO.getId())).findFirst();
        //工序生产过程物料库存管控级别(0:不管控物料库存;1:工单物料库存;2:工位物料库存)
        int materialControlLevel = commonService.getMaterialControlLevel();
        //获取工序是否管控物料及库存级别
        wsStepInfoOptional.ifPresent(wsStepInfo -> rworkerBatchToDoStepGetDTO.setIsFeedingMaterial(wsStepInfo.getControlMaterial()).setMaterialControlLevel(materialControlLevel));
        //获取工序动态数据信息
        rworkerBatchToDoStepGetDTO.setStepDynamicDataGetDto(dynamicServices[0].getStepDynamicDataInfo(workSheet.getPedigree(), snapshotWorkFlow, rworkerStepProcessBaseDTO.getStep()));
        rworkerBatchToDoStepGetDTO.setStepDynamicDataGetVisibleDtoList(dynamicServices[0].getBatchStepDynamicDataVisibleInfo(rworkerStepProcessBaseDTO.getSubWorkSheet(), rworkerStepProcessBaseDTO.getWorkSheet(), rworkerStepProcessBaseDTO.getStep()));
        //获取工序技术指标及SOP
        PedigreeStepSpecification pedigreeStepSpecification = toDoStepConfigServices[0].findStepSpecificationSop(workSheet.getPedigree(), snapshotWorkFlow, rworkerStepProcessBaseDTO.getStep(), workSheet.getClientId());
        if (null != pedigreeStepSpecification) {
            //设置技术指标
            rworkerBatchToDoStepGetDTO.setSpecification(pedigreeStepSpecification.getQualification());
            //设置工序SOP信息
            rworkerBatchToDoStepGetDTO.setStepSopInfoList(toDoStepConfigServices[0].getStepSpecificationSop(pedigreeStepSpecification));
        }
        //获取BOM里定义的工艺图列表
        List<DocumentDTO> documentDTOList = rbaseDocumentProxy.getByRecordId(workSheet.getBomInfoId());
        if(!CollectionUtils.isEmpty(documentDTOList)){
            rworkerBatchToDoStepGetDTO.setProductsCraftInfoList(documentDTOList.stream().map(ProcessDocumentGetInfo::new).collect(Collectors.toList()));
        }
        //获取当前待做工序的易损件信息
        rworkerBatchToDoStepGetDTO.setWearingPartGroupInfoList(wearingPartServices[0].getWearingPartInfo(rworkerStepProcessBaseDTO));
        //获取工单(子工单)在当前工序可投产的SN信息列表
        snProcessRequestServices[0].findToDoBatchSn(rworkerStepProcessBaseDTO,rworkerBatchToDoStepGetDTO);
        //烘烤温循老化相关
        bakeCycleBakeAgeingModelServices[0].bakeCycleBakeAgeingBatchInfo(rworkerBatchToDoStepGetDTO,rworkerStepProcessBaseDTO);
        //请求工序时更新设备状态为正常运行
        facilityServices[0].updateFacilityStatus(facilityIdList, ConstantsEnum.FACILITY_STATUS_RUNNING.getCategoryName());
        return rworkerBatchToDoStepGetDTO;
    }


    /**
     * 获取工单请求模式下的在线调整工序的投产数量
     *
     * @param workSheetId         工单主键ID（子工单主键ID）
     * @param stepId              工序主键ID
     * @param subWsProductionMode 投产粒度(true:子工单,false:工单)
     * @return int 在线调整数量
     */
    @Override
    @Transactional(readOnly = true)
    public int findOnlineReworkStepTransferNumber(long workSheetId, long stepId, boolean subWsProductionMode,RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        List<UnqualifiedItem> unqualifiedItemList = onlineReworkRuleRepository.findUnqualifiedItemByStepIdAndDeleted(stepId, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(unqualifiedItemList)) {
            throw new ResponseException("error.notExistOnlineReworkRule", "在线调整工序未配置规则");
        }
        rworkerStepProcessBaseDTO.setOnlineReworkUnQualifiedItemList(unqualifiedItemList);
        Long unqualifiedNumber;
        if (subWsProductionMode) {
            unqualifiedNumber = wsStepUnqualifiedItemRepository.sumNumberBySubWorkSheetIdAndUnqualifiedItemIdInAndDeleted(workSheetId, unqualifiedItemList.stream().map(UnqualifiedItem::getId).collect(Collectors.toList()), Constants.LONG_ZERO);
        } else {
            unqualifiedNumber = wsStepUnqualifiedItemRepository.sumNumberByWorkSheetIdAndUnqualifiedItemIdInAndDeleted(workSheetId, unqualifiedItemList.stream().map(UnqualifiedItem::getId).collect(Collectors.toList()), Constants.LONG_ZERO);
        }
        return null == unqualifiedNumber ? Constants.INT_ZERO : unqualifiedNumber.intValue();
    }

    /**
     * 获取子工单粒度下的下个待投产工序信息
     * @param rworkerBatchToDoStepRequestDTO 求批量待做工序参数
     * @param rworkerStepProcessBaseDTO 通用基础信息
     * @return net.airuima.web.rest.rworker.process.dto.RworkerBatchToDoStepGetDTO  批量待做工序信息
     */
    @Override
    public RworkerBatchToDoStepGetDTO findSubWsNextTodoStep(RworkerBatchToDoStepRequestDTO rworkerBatchToDoStepRequestDTO, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        SubWorkSheet subWorkSheet = rworkerStepProcessBaseDTO.getSubWorkSheet();
        //获取当前子工单批量详情列表
        List<BatchWorkDetail> batchWorkDetailList = batchWorkDetailRepository.findBySubWorkSheetIdAndDeleted(rworkerBatchToDoStepRequestDTO.getProductWorkSheetId(), Constants.LONG_ZERO);
        //优先获取子工单工艺快照，若不存在则继续获取工单工艺快照
        List<WsStep> wsSteps = wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(wsSteps)) {
            wsSteps = wsStepRepository.findByWorkSheetIdAndDeleted(subWorkSheet.getWorkSheet().getId(), Constants.LONG_ZERO);
        }
        WorkCell workCell = workCellRepository.getReferenceById(rworkerBatchToDoStepRequestDTO.getWorkCellId());
        //获取工位绑定的工序列表
        List<Step> stepList = workCellStepRepository.findByWorkCellId(rworkerBatchToDoStepRequestDTO.getWorkCellId());
        //获取待投产工序信息
        WsStep nextTodoWsStep = findNextTodoWsStep(wsSteps, stepList, batchWorkDetailList);
        if (null == nextTodoWsStep) {
            List<List<WsStep>> lists = commonService.dealWsStep(wsSteps);
            if(CollectionUtils.isEmpty(batchWorkDetailList)){
                throw new ResponseException("error.notExistTodoStep", "工单待做工序为【"+lists.get(Constants.INT_ZERO).get(Constants.INT_ZERO).getStep().getName()+"】,当前工位不可生产该工序");
            }
            for (List<WsStep> wsStepList : lists) {
                for (WsStep wsStep : wsStepList){
                    if(batchWorkDetailList.stream().noneMatch(batchWorkDetail -> batchWorkDetail.getStep().getId().equals(wsStep.getStep().getId()) && batchWorkDetail.getFinish()== net.airuima.constant.Constants.INT_ONE)){
                        throw new ResponseException("error.notExistTodoStep", "工单待做工序为【"+wsStep.getStep().getName()+"】,当前工位不可生产该工序");
                    }
                }
            }
            throw new ResponseException("error.notExistTodoStep", "当前工位无工序可生产");
        }
        //验证工序快照的请求模式是否为容器或者SN，若是则返回提示信息
        if (nextTodoWsStep.getRequestMode() == ConstantsEnum.CONTAINER_REQUEST_MODE.getCategoryName()) {
            throw new ResponseException("error.containerRequestModeError", "请扫描容器进行请求待做工序");
        }
        if (nextTodoWsStep.getRequestMode() == ConstantsEnum.SN_REQUEST_MODE.getCategoryName()) {
            throw new ResponseException("error.snRequestModeError", "请扫描单支SN进行请求待做工序");
        }
        //初始化返回Rworker待做工序
        RworkerBatchToDoStepGetDTO rworkerBatchToDoStepGetDTO = new RworkerBatchToDoStepGetDTO(nextTodoWsStep);
        if(subWorkSheet.getWorkSheet().getCategory() == ConstantsEnum.WORK_SHEET_ONLINE_CATEGORY.getCategoryName()
                || !nextTodoWsStep.getWorkFlow().getId().equals(subWorkSheet.getWorkFlow().getId())||subWorkSheet.getWorkSheet().getCategory() == WsEnum.OFFLINE_RE_WS.getCategory()){
            rworkerBatchToDoStepGetDTO.setOnlineMaintainOrTransferWorkFlow(Boolean.TRUE);
        }
        String onlineMaintainMaterialControl = null;
        if(subWorkSheet.getWorkSheet().getCategory() == ConstantsEnum.WORK_SHEET_ONLINE_CATEGORY.getCategoryName()){
            onlineMaintainMaterialControl =  rbaseSysCodeProxy.findByCode(Constants.KEY_ONLINE_MAINTAIN_MATERIAL_CONTROL);
        }else if(subWorkSheet.getWorkSheet().getCategory() == ConstantsEnum.WORK_SHEET_OFFLINE_CATEGORY.getCategoryName()){
            onlineMaintainMaterialControl = rbaseSysCodeProxy.findByCode(Constants.KEY_OFFLINE_MAINTAIN_MATERIAL_CONTROL);
        }else if(!nextTodoWsStep.getWorkFlow().getId().equals(subWorkSheet.getWorkFlow().getId())){
            onlineMaintainMaterialControl = rbaseSysCodeProxy.findByCode(Constants.KEY_TRANSFER_MAINTAIN_MATERIAL_CONTROL);
        }
        rworkerBatchToDoStepGetDTO.setOnlineMaintainMaterialControl(StringUtils.isNotBlank(onlineMaintainMaterialControl) ? Integer.parseInt(onlineMaintainMaterialControl): net.airuima.constant.Constants.INT_ZERO);
        //获取待做工序的投产数
        BeanUtil.getHighestPrecedenceBean(IBatchProcessRequestService.class).findNextTodoStepNumber(rworkerBatchToDoStepGetDTO, batchWorkDetailList, nextTodoWsStep, subWorkSheet.getNumber());
        //获取工单工艺快照信息
        rworkerBatchToDoStepGetDTO.setWsStepInfoList(wsSteps.stream().map(WsStepGetInfo::new).collect(Collectors.toList()));
        StaffDTO staffDTO = rbaseStaffProxy.findByIdAndDeleted(rworkerBatchToDoStepRequestDTO.getStaffId(),Constants.LONG_ZERO);
        //获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = null!=nextTodoWsStep.getWorkFlow()?nextTodoWsStep.getWorkFlow():subWorkSheet.getWorkFlow();
        rworkerStepProcessBaseDTO.setWsStepList(wsSteps).setWorkFlow(snapshotWorkFlow).setStaffDTO(staffDTO).setStep(nextTodoWsStep.getStep()).setWsStep(nextTodoWsStep).setWorkCell(workCell).setSubWsProductionMode(Boolean.TRUE);
        return rworkerBatchToDoStepGetDTO;
    }


    /**
     * 获取工单粒度下的下个待投产工序信息
     * @param rworkerBatchToDoStepRequestDTO 批量待做工序参数
     * @param rworkerStepProcessBaseDTO 通用基础信息
     * @return net.airuima.web.rest.rworker.process.dto.RworkerBatchToDoStepGetDTO  批量待做工序信息
     */
    @Override
    public RworkerBatchToDoStepGetDTO findWorkSheetNextToDoStep(RworkerBatchToDoStepRequestDTO rworkerBatchToDoStepRequestDTO, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        WorkSheet workSheet = rworkerStepProcessBaseDTO.getWorkSheet();
        //获取当前工单批量详情列表
        List<BatchWorkDetail> batchWorkDetailList = batchWorkDetailRepository.findByWorkSheetIdAndDeleted(rworkerBatchToDoStepRequestDTO.getProductWorkSheetId(), Constants.LONG_ZERO);
        //获取工单工艺快照
        List<WsStep> wsSteps = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        WorkCell workCell = workCellRepository.getReferenceById(rworkerBatchToDoStepRequestDTO.getWorkCellId());
        //获取工位绑定的工序列表
        List<Step> stepList = workCellStepRepository.findByWorkCellId(rworkerBatchToDoStepRequestDTO.getWorkCellId());
        //获取待投产工序
        WsStep nextTodoWsStep = findNextTodoWsStep(wsSteps, stepList, batchWorkDetailList);
        if (null == nextTodoWsStep) {
            List<List<WsStep>> lists = commonService.dealWsStep(wsSteps);
            if(CollectionUtils.isEmpty(batchWorkDetailList)){
                throw new ResponseException("error.notExistTodoStep", "工单待做工序为【"+lists.get(Constants.INT_ZERO).get(Constants.INT_ZERO).getStep().getName()+",当前工位不可生产该工序");
            }
            for (List<WsStep> wsStepList : lists) {
                for (WsStep wsStep : wsStepList){
                    if(batchWorkDetailList.stream().noneMatch(batchWorkDetail -> batchWorkDetail.getStep().getId().equals(wsStep.getStep().getId()) && batchWorkDetail.getFinish()== net.airuima.constant.Constants.INT_ONE)){
                        throw new ResponseException("error.notExistTodoStep", "工单待做工序为【"+wsStep.getStep().getName()+",当前工位不可生产该工序");
                    }
                }
            }
            throw new ResponseException("error.notExistTodoStep", "当前工位无工序可生产");
        }
        //验证工序快照的请求模式是否为容器或者SN，若是则返回提示信息
        if (nextTodoWsStep.getRequestMode() == ConstantsEnum.CONTAINER_REQUEST_MODE.getCategoryName()) {
            throw new ResponseException("error.containerRequestModeError", "请扫描容器进行请求待做工序");
        }
        if (nextTodoWsStep.getRequestMode() == ConstantsEnum.SN_REQUEST_MODE.getCategoryName()) {
            throw new ResponseException("error.snRequestModeError", "请扫描单支SN进行请求待做工序");
        }
        //初始化返回Rworker待做工序
        RworkerBatchToDoStepGetDTO rworkerBatchToDoStepGetDTO = new RworkerBatchToDoStepGetDTO(nextTodoWsStep);
        //判断当前投产工序是否存在转工艺或者为在线返工
        if(workSheet.getCategory() == ConstantsEnum.WORK_SHEET_ONLINE_CATEGORY.getCategoryName()
                || !nextTodoWsStep.getWorkFlow().getId().equals(workSheet.getWorkFlow().getId())
                ||workSheet.getCategory() == WsEnum.OFFLINE_RE_WS.getCategory()){
            rworkerBatchToDoStepGetDTO.setOnlineMaintainOrTransferWorkFlow(Boolean.TRUE);
        }
        String onlineMaintainMaterialControl = null;
        if(workSheet.getCategory() == ConstantsEnum.WORK_SHEET_ONLINE_CATEGORY.getCategoryName()){
            onlineMaintainMaterialControl =  rbaseSysCodeProxy.findByCode(Constants.KEY_ONLINE_MAINTAIN_MATERIAL_CONTROL);
        }else if(workSheet.getCategory() == ConstantsEnum.WORK_SHEET_OFFLINE_CATEGORY.getCategoryName()){
            onlineMaintainMaterialControl = rbaseSysCodeProxy.findByCode(Constants.KEY_OFFLINE_MAINTAIN_MATERIAL_CONTROL);
        }else if(!nextTodoWsStep.getWorkFlow().getId().equals(workSheet.getWorkFlow().getId())){
            onlineMaintainMaterialControl = rbaseSysCodeProxy.findByCode(Constants.KEY_TRANSFER_MAINTAIN_MATERIAL_CONTROL);
        }
        rworkerBatchToDoStepGetDTO.setOnlineMaintainMaterialControl(StringUtils.isNotBlank(onlineMaintainMaterialControl) ? Integer.parseInt(onlineMaintainMaterialControl): net.airuima.constant.Constants.INT_ZERO);
        //获取待做工序的投产数
        BeanUtil.getHighestPrecedenceBean(IBatchProcessRequestService.class).findNextTodoStepNumber(rworkerBatchToDoStepGetDTO, batchWorkDetailList, nextTodoWsStep, workSheet.getNumber());
        StaffDTO staffDTO = rbaseStaffProxy.findByIdAndDeleted(rworkerBatchToDoStepRequestDTO.getStaffId(),Constants.LONG_ZERO);
        //获取工单工艺快照信息
        rworkerBatchToDoStepGetDTO.setWsStepInfoList(wsSteps.stream().map(WsStepGetInfo::new).collect(Collectors.toList()));
        //获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = null!=nextTodoWsStep.getWorkFlow()?nextTodoWsStep.getWorkFlow():workSheet.getWorkFlow();
        rworkerStepProcessBaseDTO.setWsStepList(wsSteps).setWorkFlow(snapshotWorkFlow).setStep(nextTodoWsStep.getStep()).setWsStep(nextTodoWsStep).setWorkCell(workCell).setSubWsProductionMode(Boolean.FALSE).setStaffDTO(staffDTO);
        return rworkerBatchToDoStepGetDTO;
    }


    /**
     * 获取当前工单待做工序信息
     *
     * @param wsStepList          工单工艺快照列表
     * @param stepList            工位绑定的工序列表
     * @param batchWorkDetailList 批量详情列表
     * @return WsStep
     */
    private WsStep findNextTodoWsStep(List<WsStep> wsStepList, List<Step> stepList, List<BatchWorkDetail> batchWorkDetailList) {
        // 校验工艺快照列表和工序列表
        if(CollectionUtils.isEmpty(wsStepList) || CollectionUtils.isEmpty(stepList)){
            return null;
        }
        //查找工艺快照中的第一个且工位匹配的工序
        List<WsStep> wsStepCollect = wsStepList.stream().filter(wsStep -> !ValidateUtils.isValid(wsStep.getPreStepId()) && stepList.stream().anyMatch(
                step -> step.getId().equals(wsStep.getStep().getId()))).toList();
        //若不存在批量详情且工位能匹配第一个工序则待做工序为快照的第一个工序
        if (!ValidateUtils.isValid(batchWorkDetailList) && ValidateUtils.isValid(wsStepCollect)) {
            return wsStepCollect.get(Constants.INT_ZERO);
        }
        if (!ValidateUtils.isValid(batchWorkDetailList) && !ValidateUtils.isValid(wsStepCollect)) {
            return null;
        }
        List<List<WsStep>> lists = commonService.dealWsStep(wsStepList);
        //按照工序对工单详情进行分组
        Map<Long, List<BatchWorkDetail>> batchWorkDetailGroup = batchWorkDetailList.stream().collect(Collectors.groupingBy(batchWorkDetail -> batchWorkDetail.getStep().getId()));
        for (List<WsStep> wsSteps : lists) {
            List<WsStep> mayNextToDoWsSteps = wsSteps.stream().filter(wsStep -> stepList.stream().anyMatch(step -> step.getId().equals(wsStep.getStep().getId()))).toList();
            if (!ValidateUtils.isValid(mayNextToDoWsSteps)) {
                continue;
            }
            for (WsStep wsStep : mayNextToDoWsSteps) {
                List<BatchWorkDetail> batchWorkDetails = batchWorkDetailGroup.get(wsStep.getStep().getId());
                BatchWorkDetail batchWorkDetail = ValidateUtils.isValid(batchWorkDetails) ? batchWorkDetails.get(Constants.INT_ZERO) : null;
                //若工序已完成则继续判断下一个待做工序
                if (null != batchWorkDetail && batchWorkDetail.getFinish() == ConstantsEnum.FINISH_STATUS.getCategoryName()) {
                    continue;
                }
                //第一个工序未完成则返回该待做工序
                if (StringUtils.isBlank(wsStep.getPreStepId()) && (null == batchWorkDetail || batchWorkDetail.getFinish() == ConstantsEnum.UNFINISHED_STATUS.getCategoryName())) {
                    return wsStep;
                }
                //前置工序未完成则继续判断下一个匹配到待做工序
                if (Arrays.stream(wsStep.getPreStepId().split(Constants.STR_COMMA))
                        .anyMatch(preStepId -> batchWorkDetailGroup.get(Long.parseLong(preStepId)) == null ||
                                batchWorkDetailGroup.get(Long.parseLong(preStepId)).stream().anyMatch(currBatchWorkDetail -> currBatchWorkDetail.getFinish() == ConstantsEnum.UNFINISHED_STATUS.getCategoryName()))) {
                    continue;
                }
                //若前置工序都已完成将当前未完成的待做工序返回
                return wsStep;

            }
        }
        return null;
    }

    /**
     * 获取下个待做投产工序的数量
     *
     * @param batchWorkDetails 工单批量详情数据
     * @param nextTodoWsStep   下个待做的工序快照
     * @param workSheetNumber  工单(子工单)批量
     * @return int
     */
    @Override
    public void findNextTodoStepNumber(RworkerBatchToDoStepGetDTO rworkerBatchToDoStepGetDTO, List<BatchWorkDetail> batchWorkDetails, WsStep nextTodoWsStep, int workSheetNumber) {
        //获取当前待做工序批量生产详情
        List<BatchWorkDetail> currTodoStepBatchWorkDetails = batchWorkDetails.stream().filter(i -> i.getStep().getId().equals(nextTodoWsStep.getStep().getId())).collect(Collectors.toList());
        int transferNumber;
        //1. 未投产则投产数为工单数量
        if (!ValidateUtils.isValid(batchWorkDetails)) {
            transferNumber = workSheetNumber;
        }
        //2. 工单已投产，且前置工序为空
        else if (StringUtils.isBlank(nextTodoWsStep.getPreStepId())) {
            transferNumber = workSheetNumber - (ValidateUtils.isValid(currTodoStepBatchWorkDetails) ? currTodoStepBatchWorkDetails.get(Constants.INT_ZERO).getFinishNumber() : Constants.INT_ZERO);
        }
        //3. 工单已投产，且存在前置工序
        else {
            //获取所有前置工序批量详情
            List<BatchWorkDetail> preStepBatchWorkDetailList = batchWorkDetails.stream().filter(detail -> nextTodoWsStep.getPreStepId().contains(detail.getStep().getId().toString())).collect(Collectors.toList());
            //获取前置工序最小流转数
            int preStepTransferNumber = preStepBatchWorkDetailList.stream().mapToInt(BatchWorkDetail::getTransferNumber).min().getAsInt();
            //获取当前工序已完成数
            int currToDoStepFinishedNumber = ValidateUtils.isValid(currTodoStepBatchWorkDetails) ? currTodoStepBatchWorkDetails.get(Constants.INT_ZERO).getFinishNumber() : Constants.INT_ZERO;
            //根据投产比例进行换算真实投产数(若有小数则向上取整)
            transferNumber = (preStepTransferNumber == Constants.INT_ZERO) ? Constants.INT_ZERO : (int) Math.ceil(NumberUtils.multiply((preStepTransferNumber - currToDoStepFinishedNumber), nextTodoWsStep.getInputRate()).doubleValue());
        }
        rworkerBatchToDoStepGetDTO.setNumber(transferNumber);
    }

    /**
     * 验证子工单状态是否能进行投产
     *
     * @param subWorkSheet  子工单
     */
    @Override
    public void validateSubWorkSheetStatus(SubWorkSheet subWorkSheet) {
        //验证请求的子工单是否存在缓存，如果有缓存则需要提示
        rworkerCacheServices[0].validateCacheWhenRequestTodoStep(Constants.INT_ZERO,subWorkSheet.getSerialNumber());
        if (subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName() || subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName()) {
            throw new ResponseException("error.subWorkSheetFinished", "子工单已完成!");
        }
        WorkSheet workSheet = subWorkSheet.getWorkSheet();
        if (workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName() || workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName()) {
            throw new ResponseException("error.workSheetFinished", "工单已完成!");
        }
        if (subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName()) {
            throw new ResponseException("error.subWorkSheetPaused", "子工单已暂停投产!");
        }
        if (workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName()) {
            throw new ResponseException("error.workSheetPaused", "工单已暂停投产!");
        }
        //是否存在待检任务
        List<InspectTask> inspectTaskList = inspectTaskRepository.findBySubWorkSheetIdAndWorkSheetIsNullAndStatusAndDeleted(subWorkSheet.getId(), Boolean.FALSE, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(inspectTaskList)){
            throw new ResponseException("error.subWorkSheetWaitInspectTask", "子工单需要进行待检任务!");
        }
        Optional<CheckHistory> checkHistoryOptional = checkHistoryRepository.findTop1BySubWorkSheetIdAndWorkSheetIsNullAndContainerCodeIsNullAndCategoryGreaterThanAndStatusAndDeleted(subWorkSheet.getId(), Constants.INT_ONE, Boolean.FALSE, Constants.LONG_ZERO);
        if (checkHistoryOptional.isPresent()){
            throw new ResponseException("error.subWorkSheetWaitCheckHistory", "子工单需要处理检测记录!");
        }
        //验证子工单是否处于维修分析状态中
        MaintainHistoryDTO maintainHistory = rbaseMaintainHistoryProxy.findTop1BySubWorkSheetIdAndSnWorkStatusIsNullAndContainerDetailIsNullAndDeletedOrderByIdDesc(subWorkSheet.getId(), Constants.LONG_ZERO).orElse(null);
        if (null != maintainHistory) {
            if (maintainHistory.getStatus() == MaintainEnum.WAIT_ANALYZE_STATUS.getStatus()) {
                throw new ResponseException("error.subWorkSheetWaitForAnalyze", "子工单处于待分析中!");
            }
            if (maintainHistory.getStatus() == MaintainEnum.WAIT_MAINTAIN_STATUS.getStatus()) {
                throw new ResponseException("error.subWorkSheetWaitForMaintain", "子工单处于待维修中!");
            }
        }
        //验证子工单是否处于待复检状态中
        StepReinspect stepReinspect = stepReinspectRepository.findTop1BySubWorkSheetIdAndStatusAndDeleted(subWorkSheet.getId(),Boolean.FALSE,Constants.LONG_ZERO);
        if(null != stepReinspect){
            throw new ResponseException("error.subWorkSheetWaitForReinspect", "子工单处于待复检中!");
        }
    }

    /**
     * 验证工单状态是否能进行投产
     *
     * @param workSheet  工单
     */
    @Override
    public void validateWorkSheetStatus(WorkSheet workSheet) {
        //验证请求的工单是否存在缓存，如果有缓存则需要提示
        rworkerCacheServices[0].validateCacheWhenRequestTodoStep(Constants.INT_ONE,workSheet.getSerialNumber());
        if (workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName() || workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName()) {
            throw new ResponseException("error.workSheetFinished", "工单已完成!");
        }
        if (workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName()) {
            throw new ResponseException("error.workSheetPaused", "工单已暂停投产!");
        }
        //是否存在待检任务
        List<InspectTask> inspectTaskList = inspectTaskRepository.findByWorkSheetIdAndSubWorkSheetIsNullAndStatusAndDeleted(workSheet.getId(), Boolean.FALSE, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(inspectTaskList)){
            throw new ResponseException("error.subWorkSheetWaitInspectTask", "工单需要进行待检任务!");
        }
        Optional<CheckHistory> checkHistoryOptional = checkHistoryRepository.findTop1ByWorkSheetIdAndSubWorkSheetIsNullAndContainerCodeIsNullAndCategoryGreaterThanAndStatusAndDeleted(workSheet.getId(), Constants.INT_ONE, Boolean.FALSE, Constants.LONG_ZERO);
        if (checkHistoryOptional.isPresent()){
            throw new ResponseException("error.subWorkSheetWaitCheckHistory", "工单需要处理检测记录!");
        }
        //验证工单是否处于维修分析状态中
        MaintainHistoryDTO maintainHistory = rbaseMaintainHistoryProxy.findTop1ByWorkSheetIdAndSnWorkStatusIsNullAndContainerDetailIsNullAndDeletedOrderByIdDesc(workSheet.getId(), Constants.LONG_ZERO).orElse(null);
        if (null != maintainHistory) {
            if (maintainHistory.getStatus() == MaintainEnum.WAIT_ANALYZE_STATUS.getStatus()) {
                throw new ResponseException("error.subWorkSheetWaitForAnalyze", "工单处于待分析中!");
            }
            if (maintainHistory.getStatus() == MaintainEnum.WAIT_MAINTAIN_STATUS.getStatus()) {
                throw new ResponseException("error.subWorkSheetWaitForMaintain", "工单处于待维修中!");
            }
        }
        //验证工单是否处于待复检状态中
        StepReinspect stepReinspect = stepReinspectRepository.findTop1ByWorkSheetIdAndStatusAndDeleted(workSheet.getId(),Boolean.FALSE,Constants.LONG_ZERO);
        if(null != stepReinspect){
            throw new ResponseException("error.subWorkSheetWaitForReinspect", "工单尚处于待复检中!");
        }
    }

    /**
     * 预留请求工序扩展接口
     * @param rworkerBatchToDoStepRequestDTO 请求工序参数
     * @param rworkerBatchToDoStepGetDTO 返回参数
     * @return  net.airuima.web.rest.rworker.process.dto.RworkerBatchToDoStepGetDTO  批量待做工序信息
     */
    @Override
    public RworkerBatchToDoStepGetDTO findExtendInfo(RworkerBatchToDoStepRequestDTO rworkerBatchToDoStepRequestDTO, RworkerBatchToDoStepGetDTO rworkerBatchToDoStepGetDTO) {
        return IBatchProcessRequestService.super.findExtendInfo(rworkerBatchToDoStepRequestDTO, rworkerBatchToDoStepGetDTO);
    }
}
