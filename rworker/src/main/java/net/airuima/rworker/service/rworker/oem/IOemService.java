package net.airuima.rworker.service.rworker.oem;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.WsStep;

@FuncDefault
public interface IOemService {

    /**
     * 自动创建工序外协工单
     *
     * @param batchWorkDetail 当前批次
     */
    @FuncInterceptor("StepOem")
    default void grenadeStepOemOrder(BatchWorkDetail batchWorkDetail) {
        return;
    }

    /**
     * 验证外协工序禁止投产
     *
     * @param wsStep 工序快照
     */
    @FuncInterceptor("StepOem")
    default void validProcessOemStep(WsStep wsStep) {

    }
}
