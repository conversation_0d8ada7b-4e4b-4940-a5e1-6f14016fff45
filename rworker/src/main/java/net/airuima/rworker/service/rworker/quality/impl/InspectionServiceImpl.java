package net.airuima.rworker.service.rworker.quality.impl;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.WorkCellStartCheckEnum;
import net.airuima.rbase.constant.WorkCellStartCheckFlagEnum;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.quality.WorkCellCheckStartRule;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.quality.InspectTask;
import net.airuima.rbase.domain.procedure.quality.LatestCheckResult;
import net.airuima.rbase.domain.procedure.report.StaffPerform;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.qms.VarietyDTO;
import net.airuima.rbase.dto.quality.InspectionTaskDTO;
import net.airuima.rbase.repository.base.quality.WorkCellCheckStartRuleRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.rbase.repository.procedure.quality.InspectTaskRepository;
import net.airuima.rbase.repository.procedure.quality.LatestCheckResultRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rworker.service.rworker.quality.IInspectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/4/28
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class InspectionServiceImpl implements IInspectionService {

    @Autowired
    private WorkCellCheckStartRuleRepository workCellCheckStartRuleRepository;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;

    @Autowired
    private LatestCheckResultRepository latestCheckResultRepository;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private InspectTaskRepository inspectTaskRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private StaffPerformRepository staffPerformRepository;

    /**
     * 首检检测
     * @param productWorkSheetId  待投产子工单/工单主键ID
     * @param workCell 检测工位
     * @param firstTimeWork 是否开班：true：开班 ：false：非开班
     * @date  2023/5/4
     * @return net.airuima.rbase.dto.base.BaseDTO  基础响应信息
     */
    @Override
    public BaseDTO faiInspectionInfo(Boolean firstTimeWork,Long productWorkSheetId, WorkCell workCell) {
        AtomicReference<BaseDTO> baseDto = new AtomicReference<>(new BaseDTO(Constants.OK));
        List<LatestCheckResult> latestCheckResultList = latestCheckResultRepository.findByWorkCellIdAndCategoryAndDeleted(workCell.getId(),WorkCellStartCheckEnum.FIRST_INSPECTION.getCategory(),Constants.LONG_ZERO);
        if(!CollectionUtils.isEmpty(latestCheckResultList) && latestCheckResultList.stream().anyMatch(latestCheckResult -> (!latestCheckResult.getResult() && latestCheckResult.getDealWay()!=Constants.INT_THREE))){
            return new BaseDTO(Constants.KO,"当前工位最新首检未通过，请对工位继续首检");
        }
        //获取工位发起检测规则
        List<WorkCellCheckStartRule> workCellCheckStartRuleList = workCellCheckStartRuleRepository.findByWorkCellIdAndCategoryAndIsEnableAndDeleted(workCell.getId(), WorkCellStartCheckEnum.FIRST_INSPECTION.getCategory(),Boolean.TRUE, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(workCellCheckStartRuleList)){
            return baseDto.get();
        }
        //TODO:开班时清空工位的首检最新记录,暂时屏蔽->目前逻辑为登陆视为开班
//        if(Boolean.TRUE.equals(firstTimeWork)){
//            latestCheckResultRepository.deleteByWorkCellIdAndCategory(workCell.getId(),WorkCellStartCheckEnum.FIRST_INSPECTION.getCategory());
//        }
        //子工单首检
        Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findByIdAndDeleted(productWorkSheetId, Constants.LONG_ZERO);
        subWorkSheetOptional.ifPresent(subWorkSheet -> baseDto.set(checkProcessInspectionSubWorkSheet(subWorkSheet, workCell, WorkCellStartCheckEnum.FIRST_INSPECTION.getCategory(), workCellCheckStartRuleList)));
        //工单首检
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findByIdAndDeleted(productWorkSheetId, Constants.LONG_ZERO);
        workSheetOptional.ifPresent(workSheet -> baseDto.set(checkProcessInspectionWorkSheet(workSheet,workCell,WorkCellStartCheckEnum.FIRST_INSPECTION.getCategory(), workCellCheckStartRuleList)));
        return baseDto.get();
    }

    /**
     * 巡检检测
     * @param productWorkSheet 检检工单
     * @param workCell  检测工位
     * @param firstTimeWork 是否开班：true：开班 ：false：非开班
     * @date  2023/5/4
     * @return void
     */
    @Override
    public BaseDTO ipqcInspectionInfo(Boolean firstTimeWork,Long productWorkSheet, WorkCell workCell) {
        AtomicReference<BaseDTO> baseDto = new AtomicReference<>(new BaseDTO(Constants.OK));
        List<LatestCheckResult> latestCheckResultList = latestCheckResultRepository.findByWorkCellIdAndCategoryAndDeleted(workCell.getId(),WorkCellStartCheckEnum.IPQC_INSPECTION.getCategory(),Constants.LONG_ZERO);
        if(!CollectionUtils.isEmpty(latestCheckResultList) && latestCheckResultList.stream().anyMatch(latestCheckResult -> (!latestCheckResult.getResult() && latestCheckResult.getDealWay()!=Constants.INT_THREE))){
            return new BaseDTO(Constants.KO,"当前工位最新巡检未通过，请对工位继续巡检");
        }
        //获取工位发起检测规则
        List<WorkCellCheckStartRule> workCellCheckStartRuleList = workCellCheckStartRuleRepository.findByWorkCellIdAndCategoryAndIsEnableAndDeleted(workCell.getId(),WorkCellStartCheckEnum.IPQC_INSPECTION.getCategory(),Boolean.TRUE,Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(workCellCheckStartRuleList)){
            return baseDto.get();
        }
        //TODO:开班时清空工位的首检最新记录，暂时屏蔽->目前逻辑为登陆视为开班
//        if(Boolean.TRUE.equals(firstTimeWork)){
//            latestCheckResultRepository.deleteByWorkCellIdAndCategory(workCell.getId(),WorkCellStartCheckEnum.IPQC_INSPECTION.getCategory());
//        }
        //子工单巡检
        Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findByIdAndDeleted(productWorkSheet, Constants.LONG_ZERO);
        subWorkSheetOptional.ifPresent(subWorkSheet -> baseDto.set(checkProcessInspectionSubWorkSheet(subWorkSheet,workCell,WorkCellStartCheckEnum.IPQC_INSPECTION.getCategory(), workCellCheckStartRuleList)));
        //工单巡检
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findByIdAndDeleted(productWorkSheet, Constants.LONG_ZERO);
        workSheetOptional.ifPresent(workSheet -> baseDto.set(checkProcessInspectionWorkSheet(workSheet,workCell,WorkCellStartCheckEnum.IPQC_INSPECTION.getCategory(), workCellCheckStartRuleList)));
        return baseDto.get();
    }

    @Override
    public void pqcInspectionInfo() {
        IInspectionService.super.pqcInspectionInfo();
    }

    @Override
    public void fqcInspectionInfo() {
        IInspectionService.super.fqcInspectionInfo();
    }

    @Override
    public void lqcInspectionInfo() {
        IInspectionService.super.lqcInspectionInfo();
    }

    /**
     * 工单 工位检测首检 PQC检测
     *
     * @param workSheet               工单
     * @param workCell                工位
     * @param category                检测类型
     * @param workCellCheckStartRules 工位发起规则
     * @return net.airuima.rbase.web.rest.rworker.quality.dto.RworkerCheckProcessQualityGetDTO
     * <AUTHOR>
     * @date 2023/2/22
     */
    public BaseDTO checkProcessInspectionWorkSheet(WorkSheet workSheet, WorkCell workCell, Integer category, List<WorkCellCheckStartRule> workCellCheckStartRules) {
        BaseDTO baseDto = new BaseDTO(Constants.OK);
        StringBuilder msg = new StringBuilder();
        String singleWsProduct = commonService.getDictionaryData(Constants.KEY_SINGLE_WS_PRODUCT);
        StaffPerform staffPerform = staffPerformRepository.findTop1ByWorkCellIdAndDeletedOrderByIdDesc(workCell.getId(),Constants.LONG_ZERO);

        Boolean switchModel = Boolean.FALSE;


        //验证工位发起时机
        for (WorkCellCheckStartRule workCellCheckStartRule : workCellCheckStartRules) {
            LatestCheckResult latestCheckResult = latestCheckResultRepository.findByWorkCellIdAndCategoryAndVarietyAndDeleted(workCellCheckStartRule.getWorkCell().getId(), workCellCheckStartRule.getCategory(), workCellCheckStartRule.getVariety(), Constants.LONG_ZERO).orElse(null);
            if (validLastCheckResult(workSheet, workCell, category, msg, workCellCheckStartRule, latestCheckResult)) continue;
            Pedigree pedigree = workSheet.getPedigree();
            WorkSheet latestWorkSheet = Objects.isNull(staffPerform) ? latestCheckResult.getWorkSheet():staffPerform.getWorkSheet();
            Pedigree latestPedigree = latestWorkSheet.getPedigree();
            //非第一次开机若切换不同型号生产时需要进行检测
            if (workCellCheckStartRule.getFlag() == Constants.INT_ZERO && !latestPedigree.getId().equals(pedigree.getId())) {
                baseDto = updateLatestCheckResultByNextCheckDate( workCellCheckStartRule, null, workSheet);
                if (baseDto.getStatus().equals(Constants.KO)){
                    switchModel = Boolean.TRUE;
                    msg.append(baseDto.getMessage());
                    continue;
                }
            }
            //不存在当前单工单投产系统配置或者存在系统配置且等于true
            if ( (ObjectUtils.isEmpty(singleWsProduct) || Boolean.parseBoolean(singleWsProduct))
                    && (workCellCheckStartRule.getFlag() == Constants.INT_ONE && !latestWorkSheet.getId().equals(workSheet.getId())) && !switchModel) {
                //非第一次开机若切换不同总工单生产时需要进行检测
                baseDto = updateLatestCheckResultByNextCheckDate(workCellCheckStartRule, null, workSheet);
                if (baseDto.getStatus().equals(Constants.KO)){
                    msg.append(baseDto.getMessage());
                    continue;
                }
            }
            //非第一次开机若下次检测时间超过当前时间需要检测
            if (latestCheckResult.getNextCheckDate() != null && latestCheckResult.getNextCheckDate().isBefore(LocalDateTime.now())) {
                //检查最新结果宽放时长为0，触发规则为实际执行宽放时长，宽放时长大于0 -》 放行本次触发检查 ，推延下次待检时间
                if (latestCheckResult.getExtendTime() == Constants.INT_ZERO && workCellCheckStartRule.getExtendType() == Constants.INT_ONE
                        && workCellCheckStartRule.getExtendTime() > Constants.INT_ZERO) {
                    latestCheckResult.setNextCheckDate(LocalDateTime.now().plusMinutes((long) (workCellCheckStartRule.getExtendTime() * 60)))
                            .setExtendTime(workCellCheckStartRule.getExtendTime());
                    latestCheckResultRepository.save(latestCheckResult);
                    continue;
                }
                baseDto =  updateLatestCheckResultByNextCheckDate(workCellCheckStartRule, null, workSheet);
                if (baseDto.getStatus().equals(Constants.KO)){
                    msg.append(baseDto.getMessage());
                    continue;
                }
            }
            //当前为周期检测，但无下次待检时间
            if ( workCellCheckStartRule.getFlag() == Constants.INT_THREE && Objects.isNull(latestCheckResult.getNextCheckDate())) {
                //当前不管是按计划，还是按实际执行，都已当前时间+宽放时长，类型为实际执行的话，需要添加宽放时间，便于二次宽放验证，是否已宽放
                latestCheckResult.setNextCheckDate(LocalDateTime.now().plusMinutes((long) ((workCellCheckStartRule.getDuration()+workCellCheckStartRule.getExtendTime()) * 60)));
                if (workCellCheckStartRule.getExtendType() == Constants.INT_ONE){
                    latestCheckResult.setExtendTime(workCellCheckStartRule.getExtendTime());
                }
                latestCheckResultRepository.save(latestCheckResult);
                continue;
            }
            //非第一次开机若到达指定时间需要检测
            if (workCellCheckStartRule.getFlag() == Constants.INT_FOUR) {
                if (!ValidateUtils.isValid(workCellCheckStartRule.getSpecifyTime())) {
                    continue;
                }
                //获取当前时间最贴近上次检测的指定时间
                LocalDateTime lastNeedCheckLocalDate = this.findLastNeedCheckLocalDate(workCellCheckStartRule.getSpecifyTime());
                //上次检测的指定时间 之后 存在 的检测记录时间
                if (latestCheckResult.getRecordDate().isAfter(lastNeedCheckLocalDate)) {
                    continue;
                }
                //生产待检任务
                addInspectTask(new InspectionTaskDTO(workSheet, workCellCheckStartRule.getVarietyObj(), workCellCheckStartRule.getCategory(), workCell));
                msg.append("指定时间达到，需要重新检测");
            }
        }
        return ObjectUtils.isEmpty(msg.toString())?baseDto:baseDto.setStatus(Constants.KO).setMessage(msg.toString());
    }

    /**
     * 验证最新检测结果
     * @param workSheet 工单
     * @param workCell 工位
     * @param category 检测类型
     * @param msg 信息
     * @param workCellCheckStartRule 工位检测配置
     * @param latestCheckResult 检测结果最新状态
     * @return boolean
     */
    private boolean validLastCheckResult(WorkSheet workSheet, WorkCell workCell, Integer category, StringBuilder msg, WorkCellCheckStartRule workCellCheckStartRule, LatestCheckResult latestCheckResult) {
        //非第一次开机若没有最新检测结果则，自动创建最新检测结果，添加放宽时长
        if (null == latestCheckResult && workCellCheckStartRule.getExtendTime()>Constants.INT_ZERO) {
            LatestCheckResult latestCheck = new LatestCheckResult();
            latestCheck.setCategory(category).setWorkSheet(workSheet).setResult(Boolean.TRUE).setDealWay(Constants.INT_ONE).setStatus(Boolean.TRUE)
                    .setVarietyId(workCellCheckStartRule.getVarietyId())
                    .setWorkCell(workCell).setRecordDate(LocalDateTime.now())
                    .setNextCheckDate(LocalDateTime.now().plusMinutes((long) (workCellCheckStartRule.getExtendTime() * 60)));
            latestCheckResultRepository.save(latestCheck);
            return true;
        }else if(Objects.isNull(latestCheckResult)){
            addInspectTask(new InspectionTaskDTO(workSheet, workCellCheckStartRule.getVarietyObj(), workCellCheckStartRule.getCategory(), workCell));
            msg.append("当前工位需要进行"+(category==WorkCellStartCheckEnum.FIRST_INSPECTION.getCategory()?WorkCellStartCheckEnum.FIRST_INSPECTION.getRemark():WorkCellStartCheckEnum.IPQC_INSPECTION.getRemark()));
            return true;
        }
        //非第一次开机若最新结果不合格 且不放行 则认为需要检测
        if (!latestCheckResult.getResult() && latestCheckResult.getDealWay() != 3) {
            //生产待检任务
            addInspectTask(new InspectionTaskDTO(workSheet, workCellCheckStartRule.getVarietyObj(), workCellCheckStartRule.getCategory(), workCell));
            msg.append("最新检测结果不合格且未放行，需要重新检测;");
            return true;
        }

        //属于切型号，切（子）工单，且已添加过放宽时长
        if (workCellCheckStartRule.getFlag() != Constants.INT_THREE && latestCheckResult.getNextCheckDate() != null && latestCheckResult.getNextCheckDate().isBefore(LocalDateTime.now())) {
            //生产待检任务
            addInspectTask(new InspectionTaskDTO(workSheet, workCellCheckStartRule.getVarietyObj(), workCellCheckStartRule.getCategory(), workCell));
            msg.append("工位切换型号，需要重新检测;");
            return true;
        }
        return false;
    }

    /**
     * 子工单 工位检测首检 PQC检测
     *
     * @param subWorkSheet            子工单
     * @param workCell                工位
     * @param category                检测类型
     * @param workCellCheckStartRules 工位发起规则
     * @return net.airuima.rbase.web.rest.rworker.quality.dto.RworkerCheckProcessQualityGetDTO
     * <AUTHOR>
     * @date 2023/2/22
     */
    public BaseDTO checkProcessInspectionSubWorkSheet(SubWorkSheet subWorkSheet, WorkCell workCell, Integer category, List<WorkCellCheckStartRule> workCellCheckStartRules) {

        BaseDTO baseDto = new BaseDTO(Constants.OK);

        StringBuilder msg = new StringBuilder();

        String singleWsProduct = commonService.getDictionaryData(Constants.KEY_SINGLE_WS_PRODUCT);

        Boolean switchModel = Boolean.FALSE;

        //验证工位发起时机
        for (WorkCellCheckStartRule workCellCheckStartRule : workCellCheckStartRules) {
            LatestCheckResult latestCheckResult = latestCheckResultRepository.findByWorkCellIdAndCategoryAndVarietyIdAndDeleted(workCellCheckStartRule.getWorkCell().getId(), workCellCheckStartRule.getCategory(), ObjectUtils.isEmpty(workCellCheckStartRule.getVarietyObj())?null:workCellCheckStartRule.getVarietyObj().getId(), Constants.LONG_ZERO).orElse(null);
            //非第一次开机若没有最新检测结果则，自动创建最新检测结果，添加放宽时长
            if (null == latestCheckResult && workCellCheckStartRule.getExtendTime()>Constants.INT_ZERO) {
                LatestCheckResult latestCheck = new LatestCheckResult();
                latestCheck.setCategory(category).setSubWorkSheet(subWorkSheet).setResult(Boolean.TRUE).setDealWay(Constants.INT_ONE).setStatus(Boolean.TRUE).setDisplay(Boolean.FALSE)
                        .setVarietyId(workCellCheckStartRule.getVarietyId()).setWorkCell(workCell).setRecordDate(LocalDateTime.now())
                        .setNextCheckDate(LocalDateTime.now().plusMinutes((long) (workCellCheckStartRule.getExtendTime() * 60)));
                latestCheckResultRepository.save(latestCheck);
                continue;
            }else if(Objects.isNull(latestCheckResult)){
                addInspectTask(new InspectionTaskDTO(subWorkSheet, workCellCheckStartRule.getVarietyObj(), workCellCheckStartRule.getCategory(), workCell));
                msg.append("当前工位需要进行"+(category==WorkCellStartCheckEnum.FIRST_INSPECTION.getCategory()?WorkCellStartCheckEnum.FIRST_INSPECTION.getRemark():WorkCellStartCheckEnum.IPQC_INSPECTION.getRemark()));
                continue;
            }
            //非第一次开机若最新结果不合格 且不放行 则认为需要检测
            if (!latestCheckResult.getResult() && latestCheckResult.getDealWay() != 3) {
                //生产待检任务
                addInspectTask(new InspectionTaskDTO(subWorkSheet, workCellCheckStartRule.getVarietyObj(), workCellCheckStartRule.getCategory(), workCell));
                msg.append((category==WorkCellStartCheckEnum.FIRST_INSPECTION.getCategory()?WorkCellStartCheckEnum.FIRST_INSPECTION.getRemark():WorkCellStartCheckEnum.IPQC_INSPECTION.getRemark())+"最新检测结果不合格且未放行，需要重新检测;");
                continue;
            }
            Pedigree pedigree = subWorkSheet.getWorkSheet().getPedigree();
            WorkSheet workSheet = subWorkSheet.getWorkSheet();
            WorkSheet latestWorkSheet = latestCheckResult.getSubWorkSheet().getWorkSheet();
            Pedigree latestPedigree = latestWorkSheet.getPedigree();
            SubWorkSheet latestSubWorkSheet = latestCheckResult.getSubWorkSheet();

            //当前时间超过待检时间需要检验
            if (latestCheckResult.getNextCheckDate() != null && latestCheckResult.getNextCheckDate().isBefore(LocalDateTime.now())) {
                //检查最新结果宽放时长为0，触发规则为实际执行宽放时长，宽放时长大于0 -》 放行本次触发检查 ，推延下次待检时间
                if (latestCheckResult.getExtendTime() == Constants.INT_ZERO && workCellCheckStartRule.getExtendType() == Constants.INT_ONE
                        && workCellCheckStartRule.getExtendTime() > Constants.INT_ZERO) {
                    latestCheckResult.setNextCheckDate(LocalDateTime.now().plusMinutes((long) ((workCellCheckStartRule.getExtendTime()) * 60)))
                            .setExtendTime(workCellCheckStartRule.getExtendTime());
                    latestCheckResultRepository.save(latestCheckResult);
                    continue;
                }
                //生产待检任务
                addInspectTask(new InspectionTaskDTO(subWorkSheet, workCellCheckStartRule.getVarietyObj(), workCellCheckStartRule.getCategory(), workCell));
                msg.append("时间已超过("+(category==WorkCellStartCheckEnum.FIRST_INSPECTION.getCategory()?WorkCellStartCheckEnum.FIRST_INSPECTION.getRemark():WorkCellStartCheckEnum.IPQC_INSPECTION.getRemark())+")待检时间，需要重新检测;");
                continue;
            }
            //当前为周期检测，但无下次待检时间
            if ( workCellCheckStartRule.getFlag() == Constants.INT_THREE && Objects.isNull(latestCheckResult.getNextCheckDate())) {
                //当前不管是按计划，还是按实际执行，都已当前时间+宽放时长，类型为实际执行的话，需要添加宽放时间，便于二次宽放验证，是否已宽放
                latestCheckResult.setNextCheckDate(LocalDateTime.now().plusMinutes((long) ((workCellCheckStartRule.getDuration()+workCellCheckStartRule.getExtendTime()) * 60)));
                if (workCellCheckStartRule.getExtendType() == Constants.INT_ONE){
                    latestCheckResult.setExtendTime(workCellCheckStartRule.getExtendTime());
                }
                latestCheckResultRepository.save(latestCheckResult);
                continue;
            }
            //若切换不同型号生产时需要进行检测
            if (workCellCheckStartRule.getFlag() == Constants.INT_ZERO && !latestPedigree.getId().equals(pedigree.getId())) {
                baseDto = updateLatestCheckResultByNextCheckDate(workCellCheckStartRule, subWorkSheet, null);
                if (baseDto.getStatus().equals(Constants.KO)){
                    msg.append(baseDto.getMessage());
                    switchModel = Boolean.TRUE;
                    continue;
                }
            }
            //不存在当前单工单投产系统配置或者存在系统配置且等于true
            if (ObjectUtils.isEmpty(singleWsProduct) || !Boolean.parseBoolean(singleWsProduct)) {
                //非第一次开机若切换不同工单生产时需要进行检测
                if (workCellCheckStartRule.getFlag() == Constants.INT_ONE && !latestWorkSheet.getId().equals(workSheet.getId()) && !switchModel) {
                    baseDto = updateLatestCheckResultByNextCheckDate( workCellCheckStartRule, subWorkSheet, null);
                    if (baseDto.getStatus().equals(Constants.KO)){
                        msg.append(baseDto.getMessage());
                        continue;
                    }
                }
                //非第一次开机若切换不同子工单生产时需要检测
                if (workCellCheckStartRule.getFlag() == Constants.INT_TWO && !latestSubWorkSheet.getId().equals(subWorkSheet.getId()) && !switchModel) {
                    baseDto = updateLatestCheckResultByNextCheckDate( workCellCheckStartRule, subWorkSheet, null);
                    if (baseDto.getStatus().equals(Constants.KO)){
                        msg.append(baseDto.getMessage());
                        continue;
                    }
                }
            }
            //非第一次开机若到达指定时间需要检测
            if (workCellCheckStartRule.getFlag() == Constants.INT_FOUR) {
                if (!ValidateUtils.isValid(workCellCheckStartRule.getSpecifyTime())) {
                    continue;
                }
                //获取当前时间最贴近上次检测的指定时间
                LocalDateTime lastNeedCheckLocalDate = this.findLastNeedCheckLocalDate(workCellCheckStartRule.getSpecifyTime());
                //上次检测的指定时间 之后 存在 的检测记录时间
                if (latestCheckResult.getRecordDate().isAfter(lastNeedCheckLocalDate)) {
                    continue;
                }
                //生产待检任务
                addInspectTask(new InspectionTaskDTO(subWorkSheet, workCellCheckStartRule.getVarietyObj(), workCellCheckStartRule.getCategory(), workCell));
                msg.append("指定时间达到，需要重新检测");
            }
        }
        return ObjectUtils.isEmpty(msg.toString())?baseDto:baseDto.setStatus(Constants.KO).setMessage(msg.toString());
    }

    /**
     * 更新工位最新检测时长添加宽放时长
     *
     * @param latestCheckResult      检测结果最新状态
     * @param workCellCheckStartRule 检测规则
     * <AUTHOR>
     * @date 2022/11/5
     */
    public BaseDTO updateLatestCheckResultByNextCheckDate(WorkCellCheckStartRule workCellCheckStartRule, SubWorkSheet subWorkSheet, WorkSheet workSheet) {
        //生产待检任务
        if (!ObjectUtils.isEmpty(subWorkSheet)) {
            addInspectTask(new InspectionTaskDTO(subWorkSheet, workCellCheckStartRule.getVarietyObj(), workCellCheckStartRule.getCategory(), workCellCheckStartRule.getWorkCell()));
        } else {
            addInspectTask(new InspectionTaskDTO(workSheet, workCellCheckStartRule.getVarietyObj(), workCellCheckStartRule.getCategory(), workCellCheckStartRule.getWorkCell()));
        }
        return new BaseDTO(Constants.KO,WorkCellStartCheckFlagEnum.getRemark(workCellCheckStartRule.getFlag())+", 需重新检测;");
    }

    /**
     * 获取前时间 最贴近上次检测的指定时间
     *
     * @param specifyTimes 指定时间列表
     * @return java.time.LocalDateTime
     * <AUTHOR>
     * @date 2022/8/23
     */
    public LocalDateTime findLastNeedCheckLocalDate(String specifyTimes) {
        LocalDateTime nowDateTime = LocalDateTime.now();
        //获取当前时间 拼接成-》HH:mm 格式
        StringBuilder nowTime = new StringBuilder();
        nowTime.append(nowDateTime.getHour() > Constants.INT_NINE ? nowDateTime.getHour() : "0" + nowDateTime.getHour()).append(":").append(nowDateTime.getMinute() > Constants.INT_NINE ? nowDateTime.getMinute() : "0" + nowDateTime.getMinute());
        //指定时间
        List<String> specifyTimeList = Arrays.stream(specifyTimes.split(Constants.STR_COMMA)).collect(Collectors.toList());
        specifyTimeList.add(nowTime.toString());
        //排序升序
        specifyTimeList = specifyTimeList.stream().distinct().sorted().collect(Collectors.toList());

        int count = 0;
        boolean min = false;
        String lastTime = nowTime.toString();

        for (String specifyTime : specifyTimeList) {

            if (specifyTime.equals(nowTime.toString()) && count == 0) {
                min = true;
                lastTime = specifyTimeList.get(specifyTimeList.size() - Constants.INT_ONE);
                break;
            }
            if (specifyTime.equals(nowTime.toString())) {
                break;
            }
            lastTime = specifyTime;
            count++;
        }

        //当前时间为指定时间中最小,说明最靠近上次检测时间得往前推移一天
        LocalDateTime lastDateTime = min ? nowDateTime.plusDays(Constants.NEGATIVE_ONE) : nowDateTime;
        //截取获取 小时 以及 分钟
        List<Integer> hourMinutes = Arrays.stream(Arrays.stream(lastTime.split(":")).mapToInt(Integer::parseInt).toArray()).boxed().collect(Collectors.toList());
        //构造 当前时间 最贴近上次检测的指定时间
        return LocalDateTime.of(lastDateTime.getYear(), lastDateTime.getMonthValue(), lastDateTime.getDayOfMonth(), hourMinutes.get(Constants.INT_ZERO), hourMinutes.get(Constants.INT_ONE));
    }


    /**
     * 首检 巡检添加 待检任务
     * @param inspectionTaskDto  待检任务Dto
     * <AUTHOR>
     * @date  2023/5/5
     * @return void
     */
    public void addInspectTask(InspectionTaskDTO inspectionTaskDto){

        InspectTask inspectTask = inspectTaskRepository.findByWorkCellIdAndCategoryAndVarietyIdAndStatusAndDeleted(inspectionTaskDto.getWorkCell().getId(), inspectionTaskDto.getCategory(),
                ObjectUtils.isEmpty(inspectionTaskDto.getVariety()) ? null : inspectionTaskDto.getVariety().getId(), Boolean.FALSE, Constants.LONG_ZERO).orElse(new InspectTask());

        inspectTask.setCategory(inspectionTaskDto.getCategory())
                .setWorkCell(inspectionTaskDto.getWorkCell())
                .setVarietyId(Optional.ofNullable(inspectionTaskDto.getVariety()).map(VarietyDTO::getId).orElse(null))
                .setWorkSheet(inspectionTaskDto.getWorkSheet())
                .setSubWorkSheet(inspectionTaskDto.getSubWorkSheet())
                .setStatus(Boolean.FALSE);
        inspectTaskRepository.save(inspectTask);
    }

}
