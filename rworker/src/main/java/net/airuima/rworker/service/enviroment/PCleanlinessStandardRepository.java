package net.airuima.rworker.service.enviroment;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rworker.service.enviroment.dto.CleanlinessStandardDTO;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class PCleanlinessStandardRepository {

    @BeanDefine("cleanlinessStandardRepository")
    public Optional<CleanlinessStandardDTO> findByAreaIdAndDeleted(Long areaId, Long deleted) {
        return Optional.empty();
    }

}
