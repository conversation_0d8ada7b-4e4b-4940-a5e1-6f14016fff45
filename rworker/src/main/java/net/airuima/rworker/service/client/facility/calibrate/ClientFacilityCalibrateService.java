package net.airuima.rworker.service.client.facility.calibrate;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.dto.client.ClientGetStepInfoDTO;
import org.apache.commons.compress.utils.Lists;

import java.util.List;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/9/7
 */
@FuncDefault
public interface ClientFacilityCalibrateService {

    /**
     * 通过工位主键ID及设备主键ID列表验证设备校准是否逾期及提醒
     * @param workCellId 工位主键ID
     * @param facilityIds 设备主键ID列表
     * @return java.util.List<net.airuima.dto.client.ClientGetStepInfoDTO.FacilityCalibrateWaringInfo> 设备校准提醒信息
     */
    @FuncInterceptor(value = "FBase && FCalibration")
    default List<ClientGetStepInfoDTO.FacilityCalibrateWaringInfo> validateFacilityCalibrate(Long workCellId, List<Long> facilityIds){
        return Lists.newArrayList();
    }
}
