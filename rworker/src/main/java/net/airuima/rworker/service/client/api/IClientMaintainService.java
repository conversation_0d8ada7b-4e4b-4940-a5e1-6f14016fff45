package net.airuima.rworker.service.client.api;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.rbase.dto.client.GetMaintainAnalyseDTO;
import net.airuima.rbase.dto.client.MaintainAnalyseInfoDTO;


/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * RWorker维修分析相关Service
 *
 * <AUTHOR>
 * @date 2023/2/12
 */
@FuncDefault
public interface IClientMaintainService {

    /**
     * 获取维修分析信息
     *
     * @param getMaintainAnalyseDto 请求维修分析参数DTO
     * @return net.airuima.rbase.web.rest.procedure.maintaincase.dto.MaintainAnalyseInfoDTO 维修分析信息
     * <AUTHOR>
     * @date 2022/9/30
     */
    default MaintainAnalyseInfoDTO getMaintainAnalyseInfo(GetMaintainAnalyseDTO getMaintainAnalyseDto) {
        return null;
    }
}
