package net.airuima.rworker.service.enviroment;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rworker.service.enviroment.dto.LatestCleanlinessCheckResultDTO;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class PLatestCleanlinessCheckResultRepository {

    @BeanDefine("latestCleanlinessCheckResultRepository")
    public Optional<LatestCleanlinessCheckResultDTO> findByAreaIdAndDeleted(Long areaId, Long deleted) {
        return Optional.empty();
    }
}