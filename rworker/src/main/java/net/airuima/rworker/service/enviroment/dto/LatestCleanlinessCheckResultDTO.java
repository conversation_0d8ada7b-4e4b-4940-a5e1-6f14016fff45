package net.airuima.rworker.service.enviroment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.AbstractDto;
import net.airuima.rbase.domain.base.scene.OrganizationArea;
import net.airuima.rbase.dto.organization.StaffDTO;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 最新洁净度检测结果Domain
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Schema(name = "最新洁净度检测结果(LatestEnviromentCheckResult)", description = "最新洁净度检测结果")
public class LatestCleanlinessCheckResultDTO extends AbstractDto implements Serializable {

    /**
     * 部门区域
     */
    @Schema(description = "部门区域")
    private OrganizationArea area;

    /**
     * 结果（0：不合格, 1：合格）
     */
    @Schema(description = "结果（0：不合格, 1：合格）", required = true)
    private int result;

    /**
     * 最新检测人ID
     */
    @Schema(description = "最新检测人ID", required = true)
    private long latestOperatorId;

    /**
     * 最新检测人DTO
     */
    @Schema(description = "最新检测人DTO", required = true)
    private StaffDTO latestOperatorDto = new StaffDTO();

    /**
     * 最新检测日期
     */
    @Schema(description = "最新检测日期", required = true)
    private LocalDateTime latestInspectDate;

    /**
     * 下次检测日期
     */
    @Schema(description = "下次检测日期", required = true)
    private LocalDateTime nextInspectDate;

    public int getResult() {
        return result;
    }

    public LatestCleanlinessCheckResultDTO setResult(int result) {
        this.result = result;
        return this;
    }

    public Long getLatestOperatorId() {
        return latestOperatorId;
    }

    public void setLatestOperatorId(Long latestOperatorId) {
        this.latestOperatorId = latestOperatorId;
    }

    public OrganizationArea getArea() {
        return area;
    }

    public LatestCleanlinessCheckResultDTO setArea(OrganizationArea area) {
        this.area = area;
        return this;
    }

    public LatestCleanlinessCheckResultDTO setLatestOperatorId(long latestOperatorId) {
        this.latestOperatorId = latestOperatorId;
        return this;
    }

    public StaffDTO getLatestOperatorDto() {
        return latestOperatorDto;
    }

    public void setLatestOperatorDto(StaffDTO latestOperatorDto) {
        this.latestOperatorDto = latestOperatorDto;
    }

    public LocalDateTime getLatestInspectDate() {
        return latestInspectDate;
    }

    public LatestCleanlinessCheckResultDTO setLatestInspectDate(LocalDateTime latestInspectDate) {
        this.latestInspectDate = latestInspectDate;
        return this;
    }

    public LocalDateTime getNextInspectDate() {
        return nextInspectDate;
    }

    public void setNextInspectDate(LocalDateTime nextInspectDate) {
        this.nextInspectDate = nextInspectDate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LatestCleanlinessCheckResultDTO latestCleanlinessCheckResultDTO = (LatestCleanlinessCheckResultDTO) o;
        if (latestCleanlinessCheckResultDTO.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), latestCleanlinessCheckResultDTO.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
