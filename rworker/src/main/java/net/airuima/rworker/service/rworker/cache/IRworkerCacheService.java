package net.airuima.rworker.service.rworker.cache;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.dto.rworker.cache.dto.RworkerCacheSaveRequestDTO;
import net.airuima.rworker.domain.RworkerCache;

import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/11/15
 */
@FuncDefault
public interface IRworkerCacheService {

    /**
     * 新增或更新Rworker工作缓存
     *
     * @param rworkerCacheSaveRequestDTO 待保存rworker工作缓存参数
     */
    @FuncInterceptor("RworkerCache")
    default void saveInstance(RworkerCacheSaveRequestDTO rworkerCacheSaveRequestDTO) {
    }

    /**
     * 通过工位主键ID获取缓存唯一识别码
     *
     * @param workCellId 工位ID
     * @return java.lang.String 缓存识别码
     */
    @FuncInterceptor("RworkerCache")
    default String findUuidByWorkCellId(Long workCellId) {
        return null;
    }

    /**
     * 通过识别码获取缓存内容
     *
     * @param uuid 缓存识别码
     * @return java.lang.String 缓存内容
     */
    @FuncInterceptor("RworkerCache")
    default String findCacheByUuid(String uuid) {
        return null;
    }

    /**
     * 通过工位主键ID获取缓存内容
     *
     * @param workCellId 工位主键ID
     * @return net.airuima.rbase.domain.procedure.cache.RworkerCache 缓存内容
     */
    @FuncInterceptor("RworkerCache")
    default RworkerCache findCacheByWorkCell(Long workCellId){
        return null;
    }

    /**
     * 通过请求工序对象类型以及对应序列号验证缓存是否存在
     * @param category 请求工序对象类型(0:子工单号;1:工单号；2:容器号；3:SN号)
     * @param serialNumber 请求工序对象序列号
     */
    @FuncInterceptor("RworkerCache")
    default void validateCacheWhenRequestTodoStep(int category,String serialNumber){

    }

    /**
     * 通过工位主键ID删除缓存
     * @param workCellId 工位主键ID
     */
    @FuncInterceptor("RworkerCache")
    default void deletedCacheByWorkCell(Long workCellId){

    }

    /**
     * 通过批量工序生产详情删除缓存
     * @param batchWorkDetail 批量工序生产详情
     * @param containerDetailList 容器详情列表
     * @param snWorkDetailList SN详情列表
     */
    @FuncInterceptor("RworkerCache")
    default void deleteCacheByBatchWorkDetail(BatchWorkDetail batchWorkDetail, List<ContainerDetail> containerDetailList,List<SnWorkDetail> snWorkDetailList){

    }

    /**
     * 通过容器详情删除缓存
     * @param containerDetail 容器详情
     * @param snWorkDetailList SN详情列表
     */
    @FuncInterceptor("RworkerCache")
    default void deleteCacheByContainerDetail(ContainerDetail containerDetail,List<SnWorkDetail> snWorkDetailList){

    }

    /**
     * 通过SN详情删除缓存
     * @param snWorkDetail SN详情
     */
    @FuncInterceptor("RworkerCache")
    default void deleteCacheBySnWorkDetail(SnWorkDetail snWorkDetail){

    }

}
