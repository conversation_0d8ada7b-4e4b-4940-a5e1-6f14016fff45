package net.airuima.rworker.service.rworker.dynamic;

import com.google.common.collect.Lists;
import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.dto.dynamic.StepDynamicDataColumnGetDTO;
import net.airuima.rbase.dto.dynamic.StepDynamicDataGetDTO;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *  Rworker生产动态数据Service
 * <AUTHOR>
 * @date 2023/2/8
 */
@FuncDefault
public interface IDynamicService {

    /**
     * 获取产品谱系工艺路线和工序获取动态数据表单
     * @param pedigree 产品谱系
     * @param workFlow 工艺路线
     * @param step 工序
     * @return net.airuima.dto.dynamic.StepDynamicDataGetDTO 动态数据表单
     */
    @FuncInterceptor(value = "StepDynamicData")
    default StepDynamicDataGetDTO getStepDynamicDataInfo(Pedigree pedigree, WorkFlow workFlow, Step step){
        return null;
    }


    /**
     * 获取当前工单的工序的可见工序的动态数据 (批量)
     * @param subWorkSheet 子工单
     * @param workSheet 工单
     * @param step
     * @return java.util.List<net.airuima.dto.dynamic.StepDynamicDataColumnGetDTO> 动态数据集合
     */
    @FuncInterceptor(value = "StepDynamicData")
    default List<StepDynamicDataGetDTO> getBatchStepDynamicDataVisibleInfo(SubWorkSheet subWorkSheet, WorkSheet workSheet, Step step){
        return Lists.newArrayList();
    }

    /**
     * 获取当前工单的工序的可见工序的动态数据 (容器)
     * @param subWorkSheet 子工单
     * @param workSheet 工单
     * @param step
     * @return java.util.List<net.airuima.dto.dynamic.StepDynamicDataColumnGetDTO> 动态数据集合
     */
    @FuncInterceptor(value = "StepDynamicData && Container")
    default List<StepDynamicDataGetDTO> getContainerStepDynamicDataVisibleInfo(SubWorkSheet subWorkSheet, WorkSheet workSheet, Step step,Long containerId){
        return Lists.newArrayList();
    }


    /**
     * 获取当前工单的工序的可见工序的动态数据 (SN)
     * @param subWorkSheet 子工单
     * @param workSheet 工单
     * @param step
     * @return java.util.List<net.airuima.dto.dynamic.StepDynamicDataColumnGetDTO> 动态数据集合
     */
    @FuncInterceptor(value = "StepDynamicData && Single")
    default List<StepDynamicDataGetDTO> getSnStepDynamicDataVisibleInfo(SubWorkSheet subWorkSheet, WorkSheet workSheet, Step step,String sn){
        return Lists.newArrayList();
    }


    /**
     * 保存批量详情动态数据信息
     * @param stepDynamicDataColumnGetDto 下交保存动态数据信息
     * @param batchWorkDetail 批次信息
     * <AUTHOR>
     * @date  2023/2/9
     */
    @FuncInterceptor(value = "StepDynamicData")
    default void saveBatchWorkDetailStepDynamicDataInfo(BatchWorkDetail batchWorkDetail, StepDynamicDataColumnGetDTO stepDynamicDataColumnGetDto){

    }

    /**
     * 保存容器详情动态数据信息
     * @param stepDynamicDataColumnGetDto 下交保存动态数据信息
     * @param containerDetail 容器详情
     * <AUTHOR>
     * @date  2023/2/9
     */
    @FuncInterceptor(value = "StepDynamicData && Container")
    default void saveContainerWorkDetailStepDynamicDataInfo(ContainerDetail containerDetail, StepDynamicDataColumnGetDTO stepDynamicDataColumnGetDto){

    }

    /**
     * 保存SN详情动态数据信息
     * @param stepDynamicDataColumnGetDto 下交保存动态数据信息
     * <AUTHOR>
     * @date  2023/2/9
     */
    @FuncInterceptor(value = "StepDynamicData && Single")
    default void saveSnWorkDetailStepDynamicDataInfo(SnWorkDetail snWorkDetail, StepDynamicDataColumnGetDTO stepDynamicDataColumnGetDto){

    }
}
