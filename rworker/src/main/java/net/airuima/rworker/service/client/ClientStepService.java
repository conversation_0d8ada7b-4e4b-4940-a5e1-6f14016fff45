package net.airuima.rworker.service.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.constant.*;
import net.airuima.rbase.domain.base.pedigree.*;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.quality.OnlineReworkRule;
import net.airuima.rbase.domain.base.quality.StepWarningStandard;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.base.quality.UnqualifiedItemWarningStandard;
import net.airuima.rbase.domain.base.scene.AreaWorkCell;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.base.scene.WorkCellStepFacility;
import net.airuima.rbase.domain.base.wearingpart.PedigreeStepWearingPartGroup;
import net.airuima.rbase.domain.base.wearingpart.WearingPart;
import net.airuima.rbase.domain.base.wearingpart.WearingPartExchange;
import net.airuima.rbase.domain.base.wearingpart.WearingPartGroup;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.rbase.domain.procedure.batch.*;
import net.airuima.rbase.domain.procedure.material.WsMaterialBatch;
import net.airuima.rbase.domain.procedure.material.WsWorkCellMaterialBatch;
import net.airuima.rbase.domain.procedure.quality.*;
import net.airuima.rbase.domain.procedure.report.StaffPerform;
import net.airuima.rbase.domain.procedure.report.StaffPerformUnqualifiedItem;
import net.airuima.rbase.domain.procedure.single.*;
import net.airuima.rbase.domain.procedure.wearingpart.BatchWorkDetailWearingPart;
import net.airuima.rbase.domain.procedure.wearingpart.ContainerDetailWearingPart;
import net.airuima.rbase.domain.procedure.wearingpart.SnWorkDetailWearingPart;
import net.airuima.rbase.dto.batch.MaterialBatchInfo;
import net.airuima.rbase.dto.batch.PreContainerDetailInfo;
import net.airuima.rbase.dto.bom.BomInfoDTO;
import net.airuima.rbase.dto.client.*;
import net.airuima.rbase.dto.document.DocumentDTO;
import net.airuima.rbase.dto.dynamic.*;
import net.airuima.rbase.dto.ocmes.BakeCycleBakeAgeingSaveDTO;
import net.airuima.rbase.dto.qms.SampleCaseDTO;
import net.airuima.rbase.dto.quality.FqcDealDTO;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.proxy.bom.RbaseBomProxy;
import net.airuima.rbase.proxy.document.RbaseDocumentProxy;
import net.airuima.rbase.proxy.dynamicdata.RbaseDynamicDataColumnProxy;
import net.airuima.rbase.proxy.dynamicdata.RbaseStepDynamicDataColumnProxy;
import net.airuima.rbase.proxy.dynamicdata.RbaseStepDynamicDataProxy;
import net.airuima.rbase.proxy.organization.RbaseSupplierProxy;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepCheckItemRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepCheckRuleRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepMaterialRuleRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeWorkFlowRepository;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.quality.OnlineReworkRuleRepository;
import net.airuima.rbase.repository.base.quality.StepWarningStandardRepository;
import net.airuima.rbase.repository.base.quality.UnqualifiedItemRepository;
import net.airuima.rbase.repository.base.scene.AreaWorkCellRepository;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.repository.base.scene.WorkCellStepRepository;
import net.airuima.rbase.repository.base.wearingpart.WearingPartExchangeRepository;
import net.airuima.rbase.repository.base.wearingpart.WearingPartGroupRepository;
import net.airuima.rbase.repository.base.wearingpart.WearingPartRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WsReworkRepository;
import net.airuima.rbase.repository.procedure.batch.*;
import net.airuima.rbase.repository.procedure.material.WsMaterialBatchRepository;
import net.airuima.rbase.repository.procedure.material.WsWorkCellMaterialBatchRepository;
import net.airuima.rbase.repository.procedure.quality.*;
import net.airuima.rbase.repository.procedure.report.StaffPerformRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformUnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.scene.WorkCellStepFacilityRepository;
import net.airuima.rbase.repository.procedure.single.*;
import net.airuima.rbase.repository.procedure.wearingpart.BatchWorkDetailWearingPartRepository;
import net.airuima.rbase.repository.procedure.wearingpart.ContainerDetailWearingPartRepository;
import net.airuima.rbase.repository.procedure.wearingpart.SnWorkDetailWearingPartRepository;
import net.airuima.rbase.service.base.wearingpart.WearingPartService;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.ocmes.BakeCycleBakeAgeingModelService;
import net.airuima.rbase.service.ocmes.GlueModeService;
import net.airuima.rbase.service.procedure.aps.SubWorkSheetService;
import net.airuima.rbase.service.procedure.batch.BatchWorkDetailService;
import net.airuima.rbase.service.procedure.batch.ContainerDetailService;
import net.airuima.rbase.service.procedure.batch.StaffSkillModelService;
import net.airuima.rbase.service.procedure.batch.WsStepWorkCellModeService;
import net.airuima.rbase.service.procedure.batch.api.IRollbackStepService;
import net.airuima.rbase.service.procedure.batch.dto.ContainerDetailReplaceDTO;
import net.airuima.rbase.service.procedure.quality.FqcCheckResultService;
import net.airuima.rbase.util.*;
import net.airuima.rbase.web.rest.error.NumberDeductException;
import net.airuima.rbase.web.rest.error.SaveRepeatException;
import net.airuima.rbase.web.rest.procedure.batch.dto.RollBackDTO;
import net.airuima.rworker.dto.client.GrrCycleConfigDTO;
import net.airuima.rworker.dto.client.GrrLedgerDTO;
import net.airuima.rworker.proxy.RworkerGrrHistoryProxy;
import net.airuima.rworker.proxy.RworkerGrrLedgerProxy;
import net.airuima.rworker.service.client.api.IClientControlInspectService;
import net.airuima.rworker.service.client.api.IClientStepService;
import net.airuima.rworker.service.client.facility.calibrate.ClientFacilityCalibrateService;
import net.airuima.rworker.service.client.facility.inspect.ClientFacilityInspectService;
import net.airuima.rworker.service.client.maintaincase.ProcessModeService;
import net.airuima.rworker.web.rest.client.dto.ClientTodoStepDTO;
import net.airuima.rworker.web.rest.client.dto.ClientWsNoContainerDTO;
import net.airuima.util.BeanUtil;
import net.airuima.util.RedisUtils;
import net.airuima.util.ResponseException;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.klock.annotation.Klock;
import org.springframework.boot.autoconfigure.klock.model.LockTimeoutStrategy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * RWork工序相关Service
 *
 * <AUTHOR>
 * @date 2020/12/29
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class ClientStepService implements IClientStepService {
    private static final String SUB_WORKSHEET_NOT_EXIST = "子工单不存在!";
    private static final String ERR_MSG = "Rwork请求工单返回参数不存在";
    private static final String CLIENT_STEP_SUB_WS_INFO_DTONOT_EXIST = "clientStepSubWsInfoDTONotExist";
    private static final String PREFIX_MSG = "{易损件编码:";
    private static final String SUFFIX_MSG = "累计使用次数(大于)最大使用次数}";
    private static final String WS_MATERIAL_BATCH_MSG = "工单库存：物料(";
    private static final String WS_WORK_CELL_MATERIAL_BATCH_MSG = "工单工位库存：物料(";
    private static final String NUMBER_NOT_ENOUGH = ")数量不够扣减!";
    @Autowired
    private AreaWorkCellRepository areaWorkCellRepository;

    @Autowired
    private WsStepWorkCellModeService[] wsStepWorkCellModeService;
    @Autowired
    private StaffSkillModelService[] staffSkillModelService;
    @Autowired
    private ClientFacilityInspectService[] clientFacilityInspectService;
    @Autowired
    private ClientFacilityCalibrateService[] clientFacilityCalibrateService;
    @Autowired
    private ProcessModeService[] processModeService;
    @Autowired
    private IClientControlInspectService[] clientControlInspectService;

    private final SubWorkSheetRepository subWorkSheetRepository;
    private final SnWorkStatusRepository snWorkStatusRepository;
    private final WorkCellStepRepository workCellStepRepository;
    private final WsStepRepository wsStepRepository;
    private final BatchWorkDetailRepository batchWorkDetailRepository;
    private final PedigreeStepMaterialRuleRepository pedigreeStepMaterialRuleRepository;
    private final WsMaterialRepository wsMaterialRepository;
    private final StepRepository stepRepository;
    private final WorkCellRepository workCellRepository;
    private final WsStepUnqualifiedItemRepository wsStepUnqualifiedItemRepository;
    private final UnqualifiedItemRepository unqualifiedItemRepository;
    private final SnWorkDetailRepository snWorkDetailRepository;
    private final SnUnqualifiedItemRepository snUnqualifiedItemRepository;
    private final WorkSheetRepository workSheetRepository;
    private final ContainerRepository containerRepository;
    private final ContainerDetailRepository containerDetailRepository;
    private final SnReworkRepository snReworkRepository;
    private final OnlineReworkRuleRepository onlineReworkRuleRepository;
    private final WsMaterialBatchRepository wsMaterialBatchRepository;
    private final WsWorkCellMaterialBatchRepository wsWorkCellMaterialBatchRepository;
    private final WorkCellStepFacilityRepository workCellStepFacilityRepository;
    private final UnqualifiedEventRepository unqualifiedEventRepository;
    private final WsReworkRepository wsReworkRepository;
    private final CommonService commonService;
    private final PedigreeStepCheckRuleRepository pedigreeStepCheckRuleRepository;
    private final PedigreeWorkFlowRepository pedigreeWorkFlowRepository;
    @Autowired
    private WearingPartRepository wearingPartRepository;
    @Autowired
    private WearingPartExchangeRepository wearingPartExchangeRepository;
    @Autowired
    private WearingPartService wearingPartService;
    private final RedisUtils redisUtils;
    @Autowired
    private  RworkerGrrHistoryProxy rworkerGrrHistoryProxy;
    @Autowired
    private RworkerGrrLedgerProxy rworkerGrrLedgerProxy;
    private RbaseDocumentProxy rbaseDocumentProxy;
    private final StepWarningStandardRepository stepWarningStandardRepository;
    private final PedigreeStepCheckItemRepository pedigreeStepCheckItemRepository;
    private final BatchWorkDetailService batchWorkDetailService;
    private final ContainerDetailService containerDetailService;
    @Autowired
    private RbaseStepDynamicDataProxy rbaseStepDynamicDataProxy;
    @Autowired
    private RbaseStepDynamicDataColumnProxy rbaseStepDynamicDataColumnProxy;
    @Autowired
    private RbaseBomProxy rbaseBomProxy;
    @Autowired
    private RbaseDynamicDataColumnProxy rbaseDynamicDataColumnProxy;
    @Autowired
    private ContainerDetailUnqualifiedItemRepository containerDetailUnqualifiedItemRepository;
    @Autowired
    private BatchWorkDetailMaterialBatchRepository batchWorkDetailMaterialBatchRepository;
    @Autowired
    private ContainerDetailMaterialBatchRepository containerDetailMaterialBatchRepository;
    @Autowired
    private SnWorkDetailMaterialBatchRepository snWorkDetailMaterialBatchRepository;
    @Autowired
    private BatchWorkDetailFacilityRepository batchWorkDetailFacilityRepository;
    @Autowired
    private ContainerDetailFacilityRepository containerDetailFacilityRepository;
    @Autowired
    private SnWorkDetailFacilityRepository snWorkDetailFacilityRepository;
    @Autowired
    private BatchWorkDetailWearingPartRepository batchWorkDetailWearingPartRepository;
    @Autowired
    private ContainerDetailWearingPartRepository containerDetailWearingPartRepository;
    @Autowired
    private SnWorkDetailWearingPartRepository snWorkDetailWearingPartRepository;
    @Autowired
    private LatestCheckResultRepository latestCheckResultRepository;
    @Autowired
    private FqcCheckResultRepository fqcCheckResultRepository;
    @Autowired
    private CheckHistoryRepository checkHistoryRepository;
    @Autowired
    private CheckHistoryDetailRepository checkHistoryDetailRepository;
    @Autowired
    private StaffPerformRepository staffPerformRepository;
    @Autowired
    private StaffPerformUnqualifiedItemRepository staffPerformUnqualifiedItemRepository;
    @Autowired
    private FqcCheckResultService fqcCheckResultService;
    @Autowired
    private RbaseSupplierProxy rbaseSupplierProxy;
    @Autowired
    private BakeCycleBakeAgeingModelService[] bakeCycleBakeAgeingModelServices;
    @Autowired
    private GlueModeService[] glueModeServices;
    @Autowired
    private IRollbackStepService[] rollbackStepServices;
    @Autowired
    private SubWorkSheetService subWorkSheetService;
    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;
    @Autowired
    private WearingPartGroupRepository wearingPartGroupRepository;

    public ClientStepService(SnWorkStatusRepository snWorkStatusRepository,
                             SubWorkSheetRepository subWorkSheetRepository,
                             SnReworkRepository snReworkRepository,
                             ContainerDetailService containerDetailService,
                             WorkCellStepRepository workCellStepRepository,
                             WsStepRepository wsStepRepository,
                             ContainerRepository containerRepository,
                             BatchWorkDetailRepository batchWorkDetailRepository,
                             PedigreeStepMaterialRuleRepository pedigreeStepMaterialRuleRepository,
                             WsMaterialRepository wsMaterialRepository,
                             SnUnqualifiedItemRepository snUnqualifiedItemRepository,
                             WsReworkRepository wsReworkRepository,
                             BatchWorkDetailService batchWorkDetailService,
                             SnWorkDetailRepository snWorkDetailRepository,
                             PedigreeWorkFlowRepository pedigreeWorkFlowRepository,
                             StepRepository stepRepository,
                             WsMaterialBatchRepository wsMaterialBatchRepository,
                             WorkCellRepository workCellRepository,
                             UnqualifiedEventRepository unqualifiedEventRepository,
                             WorkCellStepFacilityRepository workCellStepFacilityRepository,
                             WorkSheetRepository workSheetRepository,
                             WsStepUnqualifiedItemRepository wsStepUnqualifiedItemRepository,
                             UnqualifiedItemRepository unqualifiedItemRepository,
                             ContainerDetailRepository containerDetailRepository,
                             WsWorkCellMaterialBatchRepository wsWorkCellMaterialBatchRepository,
                             CommonService commonService,
                             PedigreeStepCheckItemRepository pedigreeStepCheckItemRepository,
                             PedigreeStepCheckRuleRepository pedigreeStepCheckRuleRepository,
                             OnlineReworkRuleRepository onlineReworkRuleRepository,
                             StepWarningStandardRepository stepWarningStandardRepository,
                             RedisUtils redisUtils) {
        this.snWorkStatusRepository = snWorkStatusRepository;
        this.subWorkSheetRepository = subWorkSheetRepository;
        this.snReworkRepository = snReworkRepository;
        this.containerDetailService = containerDetailService;
        this.workCellStepRepository = workCellStepRepository;
        this.wsStepRepository = wsStepRepository;
        this.containerRepository = containerRepository;
        this.batchWorkDetailRepository = batchWorkDetailRepository;
        this.pedigreeStepMaterialRuleRepository = pedigreeStepMaterialRuleRepository;
        this.wsMaterialRepository = wsMaterialRepository;
        this.snUnqualifiedItemRepository = snUnqualifiedItemRepository;
        this.wsReworkRepository = wsReworkRepository;
        this.batchWorkDetailService = batchWorkDetailService;
        this.snWorkDetailRepository = snWorkDetailRepository;
        this.pedigreeWorkFlowRepository = pedigreeWorkFlowRepository;
        this.stepRepository = stepRepository;
        this.wsMaterialBatchRepository = wsMaterialBatchRepository;
        this.workCellRepository = workCellRepository;
        this.unqualifiedEventRepository = unqualifiedEventRepository;
        this.workCellStepFacilityRepository = workCellStepFacilityRepository;
        this.workSheetRepository = workSheetRepository;
        this.wsStepUnqualifiedItemRepository = wsStepUnqualifiedItemRepository;
        this.unqualifiedItemRepository = unqualifiedItemRepository;
        this.containerDetailRepository = containerDetailRepository;
        this.wsWorkCellMaterialBatchRepository = wsWorkCellMaterialBatchRepository;
        this.commonService = commonService;
        this.pedigreeStepCheckItemRepository = pedigreeStepCheckItemRepository;
        this.pedigreeStepCheckRuleRepository = pedigreeStepCheckRuleRepository;
        this.onlineReworkRuleRepository = onlineReworkRuleRepository;
        this.stepWarningStandardRepository = stepWarningStandardRepository;
        this.redisUtils = redisUtils;
    }


    /**
     * 获取子工单信息
     *
     * @param subWorkSheetInfo 请求传过来的子工单信息
     * @return ClientStepSubWsInfoDTO
     */
    @Transactional(readOnly = true)
    public ClientStepSubWsInfoDTO getSubWsInfo(ClientStepSubWsInfoDTO.SubWorkSheetInfo subWorkSheetInfo) {
        Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findBySerialNumberAndDeleted(subWorkSheetInfo.getSerialNumber(), Constants.LONG_ZERO);
        if (!subWorkSheetOptional.isPresent()) {
            return new ClientStepSubWsInfoDTO(new BaseClientDTO(Constants.KO, SUB_WORKSHEET_NOT_EXIST));
        }
        SubWorkSheet subWorkSheet = subWorkSheetOptional.get();
        //判断是否需要校验工单状态
        BaseClientDTO baseClientDto = checkSubWs(subWorkSheet);
        if (Constants.KO.equals(baseClientDto.getStatus())) {
            return new ClientStepSubWsInfoDTO(baseClientDto);
        }
        //查找子工单相关信息
        ClientStepSubWsInfoDTO clientStepSubWsInfoDto = Optional.ofNullable(subWorkSheet).map(ClientStepSubWsInfoDTO::new).orElseThrow(() -> new ResponseException(CLIENT_STEP_SUB_WS_INFO_DTONOT_EXIST, ERR_MSG));
        if (subWorkSheetInfo.getNeedSn()) {
            clientStepSubWsInfoDto.setSnInfoList(snWorkStatusRepository.findSnBySubWorkSheetId(subWorkSheet.getId()));
        }
        //物料图片转base64编码
        BomInfoDTO bomInfoDto = rbaseBomProxy.get(subWorkSheet.getWorkSheet().getBomInfoId());
        List<DocumentDTO> documentDtoList = bomInfoDto.getDocumentDTOList();
        List<String> base64Codes = Lists.newArrayList();
        if (ValidateUtils.isValid(documentDtoList)) {
            documentDtoList.forEach(documentDto -> {
                base64Codes.add(documentDto.getPath() != null ? Base64Util.transformBase64(documentDto.getPath()) : null);
            });
        }
        clientStepSubWsInfoDto.setPictureBase64Codes(base64Codes.stream().filter(base64Code -> base64Code != null).collect(Collectors.toList()));
        //添加物料基础信息
        clientStepSubWsInfoDto.setMaterialDto(subWorkSheet.getWorkSheet().getBomInfoDto().getMaterial());
        //生产过程批次管控级别(0:不管控物料库存;1:验证总工单物料库存;2:验证工位物料库存)
        clientStepSubWsInfoDto.setMaterialControlLevel(commonService.getMaterialControlLevel());
        return clientStepSubWsInfoDto;
    }

    /**
     * 验证容器及获取容器绑定的SN信息
     *
     * @param containerInfo 请求传过来的容器信息
     * @return ClientStepSubWsInfoDTO
     * <AUTHOR>
     * @date 2021-01-11
     **/
    public ClientStepSubWsInfoDTO getContainerInfo(ClientStepSubWsInfoDTO.ContainerInfo containerInfo) {
        Optional<Container> containerOptional = containerRepository.findByCodeAndDeleted(containerInfo.getValidateContainer(), Constants.LONG_ZERO);
        //获取未下交的容器详情记录
        Optional<ContainerDetail> containerDetailOptional = containerOptional.flatMap(value -> containerDetailRepository.findTop1ByContainerCodeAndStatusAndDeletedOrderByIdDesc(value.getCode(),
                Constants.INT_ONE, Constants.LONG_ZERO));
        //获取未下交的在线返修SN容器关联记录
        List<SnRework> snReworkList = containerOptional.map(value -> snReworkRepository.findByContainerIdAndStatusAndDeleted(value.getId(), Constants.INT_ZERO, Constants.LONG_ZERO)).orElseGet(ArrayList::new);
        //过滤掉未生成返修单的SN
        snReworkList = snReworkList.stream().filter(snRework -> snRework.getWsRework() != null).collect(Collectors.toList());
        //0:请求的旧容器号验证;
        if (containerInfo.getValidateType() == Constants.INT_ZERO) {
            if (!containerDetailOptional.isPresent() && !ValidateUtils.isValid(snReworkList)) {
                return new ClientStepSubWsInfoDTO(new BaseClientDTO(Constants.KO, "容器尚未绑定!"));
            }
            Optional<SubWorkSheet> subWorkSheetOptional = Optional.empty();
            if (ValidateUtils.isValid(snReworkList)) {
                subWorkSheetOptional = subWorkSheetRepository.findTop1ByWorkSheetIdAndDeleted(snReworkList.get(Constants.INT_ZERO).getWsRework().getReworkWorkSheet().getId(), Constants.LONG_ZERO);
            }
            if (!subWorkSheetOptional.isPresent()) {
                if (!containerDetailOptional.isPresent()) {
                    return new ClientStepSubWsInfoDTO(new BaseClientDTO(Constants.KO, "容器生产详情不存在!"));
                }
                subWorkSheetOptional = Optional.ofNullable(containerDetailOptional.get().getBatchWorkDetail().getSubWorkSheet());
            }
            if (!subWorkSheetOptional.isPresent()) {
                return new ClientStepSubWsInfoDTO(new BaseClientDTO(Constants.KO, SUB_WORKSHEET_NOT_EXIST));
            }
            if (!containerOptional.isPresent()) {
                return new ClientStepSubWsInfoDTO(new BaseClientDTO(Constants.KO, "容器不存在!"));
            }
            //查询容器对应的工单信息
            ClientStepSubWsInfoDTO clientStepSubWsInfoDto = subWorkSheetOptional.map(ClientStepSubWsInfoDTO::new).orElseThrow(() -> new ResponseException(CLIENT_STEP_SUB_WS_INFO_DTONOT_EXIST, ERR_MSG));
            clientStepSubWsInfoDto.setContainerId(containerOptional.get().getId());
            //查询容器里对应的SN
            this.findSnsOfContainer(subWorkSheetOptional.get(), containerOptional.get(), clientStepSubWsInfoDto);
            if (!ValidateUtils.isValid(clientStepSubWsInfoDto.getSnInfoList())) {
                if (!containerDetailOptional.isPresent()) {
                    return new ClientStepSubWsInfoDTO(new BaseClientDTO(Constants.KO, "容器生产详情不存在!"));
                }
                clientStepSubWsInfoDto.setContainerNumber(containerDetailOptional.get().getTransferNumber());
            }
            return clientStepSubWsInfoDto;
        }
        //1:绑定新容器时验证容器是否可用
        Container container = containerOptional.orElse(null);
        ClientStepSubWsInfoDTO clientStepSubWsInfoDto = new ClientStepSubWsInfoDTO();

        if (null == container) {
            container = new Container().setName(containerInfo.getValidateContainer()).setCode(containerInfo.getValidateContainer()).setStatus(Constants.FALSE);
            container.setDeleted(Constants.LONG_ZERO);
            container = containerRepository.save(container);
        }
        if ((!ValidateUtils.isValid(containerInfo.getRequestContainers()) || !containerInfo.getRequestContainers().contains(container.getCode())) && ValidateUtils.isValid(snReworkList)) {
            return new ClientStepSubWsInfoDTO(new BaseClientDTO(Constants.KO, "容器已占用!"));
        }
        //原容器指定为为新容器时需要给定当前容器绑定数量
        if (containerDetailOptional.isPresent()) {
            clientStepSubWsInfoDto.setContainerNumber(containerDetailOptional.get().getTransferNumber()).setContainerIsBind(containerDetailOptional.get().getStatus());
            clientStepSubWsInfoDto.setSerialNumber(containerDetailOptional.get().getBatchWorkDetail().getSubWorkSheet().getSerialNumber());
            //容器存在维修分析不可使用原容器绑定
            Optional<SnRework> snReworkOptional = snReworkRepository.findTop1ByContainerIdAndStatusAndDeletedOrderByIdDesc(container.getId(), Constants.INT_ONE, Constants.LONG_ZERO);
            if (snReworkOptional.isPresent()) {
                return new ClientStepSubWsInfoDTO(new BaseClientDTO(Constants.KO, "容器已占用!"));
            }
        }
        //验证绑定的新容器是否存在维修分析，存在则验证提供当前容器绑定的子工单信息以及投产数量
        if (!containerDetailOptional.isPresent()) {
            Optional<SnRework> snReworkOptional = snReworkRepository.findTop1ByContainerIdAndStatusAndDeletedOrderByIdDesc(container.getId(), Constants.INT_ONE, Constants.LONG_ZERO);
            if (snReworkOptional.isPresent()) {
                containerDetailOptional = containerDetailRepository.findTop1ByContainerCodeAndStatusAndDeletedOrderByIdDesc(container.getCode(), ConstantsEnum.UNBIND.getCategoryName(), Constants.LONG_ZERO);
                if (containerDetailOptional.isPresent()) {
                    clientStepSubWsInfoDto.setContainerNumber(containerDetailOptional.get().getInputNumber()).setContainerIsBind(containerDetailOptional.get().getStatus());
                    List<SubWorkSheet> subWorkSheets = subWorkSheetRepository.findByWorkSheetIdAndDeleted(snReworkOptional.get().getWsRework().getReworkWorkSheet().getId(), Constants.LONG_ZERO);
                    if (ValidateUtils.isValid(subWorkSheets)) {
                        SubWorkSheet subWorkSheet = subWorkSheets.get(Constants.INT_ZERO);
                        ClientStepSubWsInfoDTO clientStepSubWsInfoDtoSub = new ClientStepSubWsInfoDTO(subWorkSheet);
                        return clientStepSubWsInfoDtoSub.setContainerNumber(containerDetailOptional.get().getInputNumber()).setContainerId(container.getId()).setContainerIsBind(containerDetailOptional.get().getStatus());
                    }
                }
            }
        }
        clientStepSubWsInfoDto.setContainerId(container.getId());
        return clientStepSubWsInfoDto;
    }


    /**
     * 查询容器里对应的SN
     *
     * @param subWorkSheet           子工单
     * @param container              容器
     * @param clientStepSubWsInfoDto 请求参数
     * <AUTHOR>
     * @date 2021-01-12
     **/
    @Transactional(readOnly = true)
    public void findSnsOfContainer(SubWorkSheet subWorkSheet, Container container, ClientStepSubWsInfoDTO clientStepSubWsInfoDto) {
        //1.优先查找未下交的容器内在线返修单的SN
        List<SnRework> snReworkList = snReworkRepository.findByContainerIdAndStatusAndDeleted(container.getId(), Constants.INT_ZERO, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(snReworkList)) {
            clientStepSubWsInfoDto.setContainerNumber(snReworkList.size());
            clientStepSubWsInfoDto.setSnInfoList(snReworkList.stream().map(SnRework::getSnWorkStatus).collect(Collectors.toList())
                    .stream().map(ClientStepSubWsInfoDTO.SnInfo::new).collect(Collectors.toList()));
            return;
        }
        //2.若容器内不存在在线返修单的SN，则获取当前容器最近一个下交的生产模式为SN且非在线返修工序的容器工作详情信息
        List<ContainerDetail> containerDetailList = containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndContainerIdAndDeletedOrderByIdDesc(subWorkSheet.getId(), container.getId(), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(containerDetailList)) {
            return;
        }
        List<WsStep> wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(subWorkSheet.getWorkSheet().getId(), Constants.LONG_ZERO);
        containerDetailList.forEach(containerDetail -> {
            Step step = containerDetail.getBatchWorkDetail().getStep();
            WsStep currWsStep = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(step.getId())).findFirst().orElse(null);
            if (null != currWsStep && currWsStep.getControlMode() == Constants.INT_ONE && currWsStep.getCategory() != Constants.INT_ONE && currWsStep.getCategory() != Constants.INT_TWO) {
                List<SnWorkDetail> snWorkDetailList = snWorkDetailRepository.findByContainerDetailIdAndDeleted(containerDetail.getId(), Constants.LONG_ZERO);
                if (ValidateUtils.isValid(snWorkDetailList)) {
                    clientStepSubWsInfoDto.setContainerNumber(snWorkDetailList.size());
                    clientStepSubWsInfoDto.setSnInfoList(snWorkStatusRepository
                            .findBySnInAndDeleted(snWorkDetailList.stream().map(SnWorkDetail::getSn).collect(Collectors.toList()), Constants.LONG_ZERO)
                            .stream().map(ClientStepSubWsInfoDTO.SnInfo::new).collect(Collectors.toList()));
                }
            }
        });
    }

    /**
     * 校验子工单是否能够生产
     *
     * @param subWorkSheet 子工单
     * @return BaseClientDTO
     */
    @Transactional(readOnly = true)
    public BaseClientDTO checkSubWs(SubWorkSheet subWorkSheet) {
        if (subWorkSheet.getStatus() > Constants.FINISH) {
            return new BaseClientDTO(Constants.KO, "子工单已完成!");
        }
        if (subWorkSheet.getStatus() == Constants.PAUSE) {
            return new BaseClientDTO(Constants.KO, "子工单已暂停!");
        }
        if (subWorkSheet.getWorkSheet().getStatus() == Constants.FINISH) {
            return new BaseClientDTO(Constants.KO, "总工单已完成!");
        }
        if (subWorkSheet.getWorkSheet().getStatus() == Constants.PAUSE) {
            return new BaseClientDTO(Constants.KO, "总工单已暂停!");
        }
        if (subWorkSheet.getWorkSheet().getStatus() == Constants.NORMAL_CLOSED
                || subWorkSheet.getWorkSheet().getStatus() == Constants.ABNORMAL_CLOSED) {
            return new BaseClientDTO(Constants.KO, "总工单已结单!");
        }
        //验证总工单是否需要检查领料及核料
        if (Boolean.TRUE.equals(validateIsCheckWsReceiveMaterial(subWorkSheet.getWorkSheet()))) {
            WorkSheet workSheet = subWorkSheet.getWorkSheet();
            if (workSheet.getCategory() == WsEnum.ONLINE_RE_WS.getCategory()) {
                WsRework wsRework = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO).orElse(null);
                if (null != wsRework) {
                    workSheet = wsRework.getOriginalWorkSheet();
                }
            }
            Long count = wsMaterialBatchRepository.countByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            if (null == count || count.intValue() == Constants.INT_ZERO) {
                return new BaseClientDTO(Constants.KO, "总工单未核料!");
            }
        }
        return new BaseClientDTO(Constants.OK);
    }

    /**
     * 获取工序信息
     *
     * @param stepInfo 请求传过来的工序信息
     * @return ClientGetStepInfoDTO
     */
    @Transactional(readOnly = true)
    public ClientGetStepInfoDTO getStepInfo(ClientGetStepInfoDTO.StepInfo stepInfo) {
        Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findByIdAndDeleted(stepInfo.getSubWorkSheetId(), Constants.LONG_ZERO);
        if (!subWorkSheetOptional.isPresent()) {
            return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, SUB_WORKSHEET_NOT_EXIST));
        }
        SubWorkSheet subWorkSheet = subWorkSheetOptional.get();
        //校验子工单是否能够生产
        BaseClientDTO baseClientDto = checkSubWs(subWorkSheet);
        if (Constants.KO.equals(baseClientDto.getStatus())) {
            return new ClientGetStepInfoDTO(baseClientDto);
        }
        //优先获取IP对应的工位
        Optional<WorkCell> workCellOptional = StringUtils.isNotBlank(stepInfo.getWorkCellIp()) ? workCellRepository.findByIpAndDeleted(stepInfo.getWorkCellIp(), Constants.LONG_ZERO) : workCellRepository.findByIdAndDeleted(stepInfo.getWorkCellId(), Constants.LONG_ZERO);
        if (!workCellOptional.isPresent()) {
            return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "工位不存在!"));
        }
        List<Step> stepList = workCellStepRepository.findByWorkCellId(stepInfo.getWorkCellId(), Constants.LONG_ZERO);
        //校验工位是否绑定工序
        if (!ValidateUtils.isValid(stepList)) {
            return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "当前工位未绑定工序!"));
        }
        //验证是否管控洁净度
        baseClientDto = this.validateEnviroment(stepInfo.getWorkCellId());
        if (Constants.KO.equals(baseClientDto.getStatus())) {
            return new ClientGetStepInfoDTO(baseClientDto);
        }
        //先查找子工单定制工序，如果不存在查找总工单定制工序
        List<WsStep> wsStepList = wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(wsStepList)) {
            wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(subWorkSheet.getWorkSheet().getId(), Constants.LONG_ZERO);
        }
        if (!ValidateUtils.isValid(wsStepList)) {
            return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "当前工单没有定制工序!"));
        }
        //获得当前待做工序,目前根据请求的参数里是否存在请求容器列表来分别判断下个待做工序
        IClientStepService clientStepService = BeanUtil.getHighestPrecedenceBean(IClientStepService.class);
        ClientGetStepInfoDTO clientGetStepInfoDto = ValidateUtils.isValid(stepInfo.getRequestContainerIds()) ?
                clientStepService.getContainerStepInfo(new ClientGetTodoStepInfoDTO(stepInfo.getRequestContainerIds(), stepInfo.getBindContainerId(), subWorkSheet, wsStepList, stepList))
                : clientStepService.getBatchStepInfo(new ClientGetTodoStepInfoDTO(subWorkSheet, wsStepList, stepList));
        if (Constants.KO.equals(clientGetStepInfoDto.getStatus())) {
            return clientGetStepInfoDto;
        }
        Step nextToDoStep = stepRepository.getReferenceById(clientGetStepInfoDto.getStepId());
        WsStep currWsStep = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(nextToDoStep.getId())).findFirst().orElse(null);
        if (null == currWsStep) {
            return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "当前工单定制工序不存在!"));
        }
        if (currWsStep.getRequestMode() == ConstantsEnum.WORK_SHEET_REQUEST_MODE.getCategoryName() && ValidateUtils.isValid(stepInfo.getRequestContainerIds())) {
            return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "当前工序为工单请求,不需扫描原容器!"));
        }
        if (currWsStep.getRequestMode() == ConstantsEnum.CONTAINER_REQUEST_MODE.getCategoryName() && !ValidateUtils.isValid(stepInfo.getRequestContainerIds())) {
            return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "当前工序为容器请求,需扫描原容器!"));
        }
        //验证上道工序是否存在维修分析不良
        baseClientDto = processModeService[0].maintainAnalyse(stepInfo.getRequestContainerIds(), subWorkSheet, currWsStep);
        if (Constants.KO.equals(baseClientDto.getStatus())) {
            return new ClientGetStepInfoDTO(baseClientDto);
        }
        baseClientDto = wsStepWorkCellModeService[0].validWsStepWorkCell(subWorkSheet, currWsStep.getStep(), workCellOptional.get());
        if (Constants.KO.equals(baseClientDto.getStatus())) {
            return new ClientGetStepInfoDTO(baseClientDto);
        }
        // 验证员工的技能是否匹配(子)工单的产品谱系-工艺路线-工序的技能
        baseClientDto = staffSkillModelService[0].validStaffSkillModel(subWorkSheet, currWsStep, stepInfo);
        if (Constants.KO.equals(baseClientDto.getStatus())) {
            return new ClientGetStepInfoDTO(baseClientDto);
        }
        //验证已配置需要管控的工位是否已做GRR及到期日期
        GrrCycleConfigDTO grrCycleConfigDTO = rworkerGrrHistoryProxy.findByWorkCellCodeAndDeleted(workCellOptional.get().getCode(), Constants.LONG_ZERO).orElse(null);
        if (null != grrCycleConfigDTO && grrCycleConfigDTO.getIsEnable()) {
            GrrLedgerDTO grrLedgerDTO = rworkerGrrLedgerProxy.findByWorkCellCodeAndDeleted(workCellOptional.get().getCode(), Constants.LONG_ZERO).orElse(null);
            if (null == grrLedgerDTO || grrLedgerDTO.getExpireTime().isBefore(LocalDateTime.now())) {
                return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "工位GRR已超期!"));
            }
            clientGetStepInfoDto.setGrrExpireTime(grrLedgerDTO.getExpireTime()).setBeforeGrrTipDay(ToolUtils.getCycleDays(grrCycleConfigDTO.getUnit(), grrCycleConfigDTO.getPeriod()));
        }

        List<ClientGetStepInfoDTO.EquipmentInfo> facilityInfoList = getEquipmentIfo(stepInfo.getWorkCellId(), nextToDoStep.getId());
        //验证设备相关的管控点是否合规
        if (ValidateUtils.isValid(facilityInfoList)) {
            //验证设备点巡检是否合规
            List<Long> facilityIds = facilityInfoList.stream().map(ClientGetStepInfoDTO.EquipmentInfo::getEquipmentId).collect(Collectors.toList());
            BaseClientDTO baseClientDTO = clientFacilityInspectService[0].validateFacilityInspect(facilityIds);
            if (Constants.KO.equals(baseClientDTO.getStatus())) {
                return new ClientGetStepInfoDTO(baseClientDTO);
            }
            //验证设备校准是否合规
            clientGetStepInfoDto.setFacilityCalibrateWaringInfoList(clientFacilityCalibrateService[0].validateFacilityCalibrate(workCellOptional.get().getId(), facilityIds));
        }
        //当前工序若管控物料则获取上料信息
        if (currWsStep.getIsControlMaterial()) {
            clientGetStepInfoDto.setMaterialInfoList(getMaterialInfo(subWorkSheet, workCellOptional.get(), nextToDoStep, clientGetStepInfoDto.getNumber()));
        }
        //前置工序是否存在单支SN的生产数据
        boolean preStepExistSn = false;
        //若当前工序的生产模式为批量需要判断该工序的前置工序是否存在单支生产，若存在则不允许投产
        if (currWsStep.getControlMode() == ConstantsEnum.WORK_SHEET_REQUEST_MODE.getCategoryName() && StringUtils.isNotBlank(currWsStep.getPreStepId())) {
            for (String preStepId : currWsStep.getPreStepId().split(Constants.STR_COMMA)) {
                Optional<SnWorkDetail> firstSnWorkDetailOptional = snWorkDetailRepository.findTop1BySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), Long.parseLong(preStepId), Constants.LONG_ZERO);
                if (firstSnWorkDetailOptional.isPresent()) {
                    preStepExistSn = true;
                    break;
                }
            }
        }
        // 获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = null != currWsStep.getWorkFlow() ? currWsStep.getWorkFlow() : subWorkSheet.getWorkFlow();
        // 获取工序时间间隔配置
        List<PedigreeStepIntervalConfig> pedigreeStepIntervalConfigList = commonService.findPedigreeStepIntervalConfig(subWorkSheet.getWorkSheet().getPedigree(), nextToDoStep, snapshotWorkFlow, wsStepList);
        // 存在配置才会去判断
        if (ValidateUtils.isValid(pedigreeStepIntervalConfigList)) {
            // 获取配置的当前工序和当前工序的时间配置
            PedigreeStepIntervalConfig pedigreeStepIntervalConfig = pedigreeStepIntervalConfigList.stream().filter(config -> config.getPreStep().getId().equals(currWsStep.getStep().getId())).findFirst().orElse(new PedigreeStepIntervalConfig());
            clientGetStepInfoDto.setCurrStepTimeInterval(pedigreeStepIntervalConfig.getDuration());
            // 处理工序时间间隔计算
            String result = validateStepInterval(pedigreeStepIntervalConfigList, stepInfo.getRequestContainerIds(), wsStepList, currWsStep, subWorkSheet);
            if (ValidateUtils.isValid(result)) {
                clientGetStepInfoDto.setTimeOut(true);
                clientGetStepInfoDto.setMessage(result);
            }
        }
        //批量SN模式获取SN数组
        if (preStepExistSn) {
            List<SnWorkStatus> list;
            if (currWsStep.getCategory() == Constants.INT_ONE || currWsStep.getCategory() == Constants.INT_TWO) {
                List<UnqualifiedItem> unqualifiedItemList = onlineReworkRuleRepository.findUnqualifiedItemByStepIdAndDeleted(nextToDoStep.getId(), net.airuima.constant.Constants.LONG_ZERO);
                list = snWorkStatusRepository.findBySubWorkSheetIdAndStatusAndLatestUnqualifiedItemIdInAndDeleted(subWorkSheet.getId(), net.airuima.constant.Constants.INT_THREE,
                        unqualifiedItemList.stream().map(UnqualifiedItem::getId).collect(Collectors.toList()), net.airuima.constant.Constants.LONG_ZERO);
            } else {
                list = snWorkStatusRepository.findBySubWorkSheetIdAndStatusAndDeleted(subWorkSheet.getId(), net.airuima.constant.Constants.INT_ONE, net.airuima.constant.Constants.LONG_ZERO);
            }
            clientGetStepInfoDto.setSnInfoList(CollectionUtils.isEmpty(list) ? new ArrayList<>() : list.stream().map(ClientGetStepInfoDTO.SnInfo::new).collect(Collectors.toList()));
        }
        //获取工序不良项目,优先获取产品谱系配置的工序不良项目，最后才获取工序默认配置的不良项目
        clientGetStepInfoDto.setUnqualifiedItemInfoList(getUnqualifiedItemInfo(subWorkSheet, nextToDoStep, clientGetStepInfoDto.getNumber()));
        //获取定制流程
        clientGetStepInfoDto.setWsStepInfoList(wsStepList.stream().map(ClientGetStepInfoDTO.WsStepInfo::new).collect(Collectors.toList()));
        //获取工位工序绑定的设备信息
        clientGetStepInfoDto.setEquipmentInfoList(facilityInfoList);
        //获取FQC信息
        clientGetStepInfoDto.setFqcInfo(getFqcInfo(subWorkSheet.getWorkSheet().getPedigree(), snapshotWorkFlow, nextToDoStep, clientGetStepInfoDto.getNumber(), subWorkSheet.getWorkSheet().getClientId()));

        //获取抽检信息
        clientGetStepInfoDto.setRandomInspectGetInfo(getRandomInspectInfo(subWorkSheet, subWorkSheet.getWorkSheet().getPedigree(), snapshotWorkFlow, nextToDoStep, stepInfo.getWorkCellId(), Double.valueOf(Math.ceil(clientGetStepInfoDto.getNumber())).intValue()));
        //获取预警规则
        StepWarningStandard stepWarningStandard = getStepWarningStandard(subWorkSheet.getWorkSheet(), nextToDoStep, clientGetStepInfoDto.getNumber(), null);
        if (!Objects.isNull(stepWarningStandard) && clientGetStepInfoDto.getNumber() >= stepWarningStandard.getBaseNumber()) {
            clientGetStepInfoDto.setMaxUnQualifiedNumber((int) Math.ceil(clientGetStepInfoDto.getNumber() * stepWarningStandard.getWaringRate()));
        } else {
            clientGetStepInfoDto.setMaxUnQualifiedNumber(Constants.MAX_INT_DATA);
        }
        //获取待做工序的技术指标和SOP文件流
        clientGetStepInfoDto = this.getStepSpecificationSop(clientGetStepInfoDto, subWorkSheet.getWorkSheet().getPedigree(), snapshotWorkFlow, nextToDoStep, subWorkSheet.getWorkSheet().getClientId());
        // 获取当前工序的实际完成数,以及待完成数
        clientStepService.getTodoStepPendingNumber(clientGetStepInfoDto, subWorkSheet, currWsStep);
        //获取动态元数据列
        this.getStepDynamicDataGetDTO(subWorkSheet, clientGetStepInfoDto);
        //组织工位端需要的其它数据
        clientGetStepInfoDto.setPedigreeName(subWorkSheet.getWorkSheet().getPedigree().getName())
                .setPedigreeCode(subWorkSheet.getWorkSheet().getPedigree().getCode())
                .setPedigreeSpecification(subWorkSheet.getWorkSheet().getPedigree().getSpecification())
                .setSubWsId(subWorkSheet.getId())
                .setNote(subWorkSheet.getNote())
                .setWsId(subWorkSheet.getWorkSheet().getId())
                .setWorkCellId(stepInfo.getWorkCellId())
                .setClientName(null != subWorkSheet.getWorkSheet().getClientDTO() ? subWorkSheet.getWorkSheet().getClientDTO().getName() : null)
                .setClientCode(null != subWorkSheet.getWorkSheet().getClientDTO() ? subWorkSheet.getWorkSheet().getClientDTO().getCode() : null)
                .setControlMode(currWsStep.getControlMode())
                .setRequestMode(currWsStep.getRequestMode())
                .setIsBindContainer(currWsStep.getIsBindContainer())
                .setIsControlMaterial(currWsStep.getIsControlMaterial())
                .setStepCategory(currWsStep.getCategory())
                .setRealInputnumber(Double.valueOf(Math.ceil(clientGetStepInfoDto.getNumber())).intValue())
                .setWorkFlowInfo(new ClientGetStepInfoDTO.WorkFlowInfo(snapshotWorkFlow))
                .setXsrfToken(ToolUtils.generateUuId());
        //获取当前待做工序的易损件信息
        clientGetStepInfoDto.setPedigreeStepWearingPartGroupInfoList(getWearingPartInfo(subWorkSheet, currWsStep, workCellOptional.get()));
        //获取烘烤温循老化配置信息(如果获取当前配置不是最后则需要验证返回参数状态，是否为ko)
        clientGetStepInfoDto = bakeCycleBakeAgeingModelServices[0].queryBakeCycleBakeAgeingConfig(clientGetStepInfoDto);
        //若是烘烤、温循、老化取出工序 则需要获取烘烤、温循、老化历史记录的放入时间和截至时间
        BakeCycleBakeAgeingHistoryPutInDateDTO bakeCycleBakeAgeingHistoryPutInDateDto = null;
        if (currWsStep.getCategory() == StepCategoryEnum.PULL_OUT_BAKE_STEP.getStatus()) {
            bakeCycleBakeAgeingHistoryPutInDateDto = bakeCycleBakeAgeingModelServices[0].queryBakeHistoryPutInDate(stepInfo.getRequestContainerIds(), subWorkSheet.getId(), currWsStep.getCategory());
        }
        if (currWsStep.getCategory() == StepCategoryEnum.PULL_OUT_CYCLE_BAKE_STEP.getStatus()) {
            bakeCycleBakeAgeingHistoryPutInDateDto = bakeCycleBakeAgeingModelServices[0].queryCycleBakeHistoryPutInDate(stepInfo.getRequestContainerIds(), subWorkSheet.getId(), currWsStep.getCategory());
        }
        if (currWsStep.getCategory() == StepCategoryEnum.PULL_OUT_AGEING_STEP.getStatus()) {
            bakeCycleBakeAgeingHistoryPutInDateDto = bakeCycleBakeAgeingModelServices[0].queryAgeingHistoryPutInDate(stepInfo.getRequestContainerIds(), subWorkSheet.getId(), currWsStep.getCategory());
        }
        if (currWsStep.getCategory() == StepCategoryEnum.PULL_OUT_BAKE_STEP.getStatus() ||
                currWsStep.getCategory() == StepCategoryEnum.PULL_OUT_CYCLE_BAKE_STEP.getStatus() ||
                currWsStep.getCategory() == StepCategoryEnum.PULL_OUT_AGEING_STEP.getStatus()) {
            //若不存在历史记录 则返回提示
            if (bakeCycleBakeAgeingHistoryPutInDateDto == null) {
                return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "未检测到放入记录!"));
            }
            return clientGetStepInfoDto.setBakeCycleBakeAgeingHistoryPutInDateDTO(bakeCycleBakeAgeingHistoryPutInDateDto);
        }
        return clientGetStepInfoDto;
    }

    /**
     * 获取动态元数据列
     *
     * @param subWorkSheet         子工单
     * @param clientGetStepInfoDto 请求工序接口DTO
     * <AUTHOR>
     * @date 2022/11/17 10:27
     */
    private void getStepDynamicDataGetDTO(SubWorkSheet subWorkSheet, ClientGetStepInfoDTO clientGetStepInfoDto) {
        //获取动态数据
        List<Long> pedigreeIdList = commonService.getAllParent(subWorkSheet.getWorkSheet().getPedigree());
        pedigreeIdList.add(subWorkSheet.getWorkSheet().getPedigree().getId());
        // 获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = commonService.findSnapshotWorkFlow(subWorkSheet.getWorkSheet(), subWorkSheet, stepRepository.getOne(clientGetStepInfoDto.getStepId()));
        Optional<StepDynamicDataDTO> optionalStepDynamicData = rbaseStepDynamicDataProxy.findByPedigreeIdInAndWorkFlowIdAndStepIdAndDeleted(pedigreeIdList, snapshotWorkFlow.getId(), clientGetStepInfoDto.getStepId(), Boolean.TRUE, net.airuima.constant.Constants.LONG_ZERO).stream().max(Comparator.comparing(i -> i.getPedigree().getType()));
        if (optionalStepDynamicData.isPresent()) {
            StepDynamicDataDTO stepDynamicData = optionalStepDynamicData.get();

            //动态数据返回对象
            StepDynamicDataGetDTO stepDynamicDataGetDTO = new StepDynamicDataGetDTO();
            stepDynamicDataGetDTO.setDynamicDataId(stepDynamicData.getId()).setDynamicDataName(stepDynamicData.getName()).setDynamicDataCode(stepDynamicData.getCode());
            //动态元数据集合
            List<StepDynamicDataColumnDTO> stepDynamicDataColumnList = rbaseStepDynamicDataColumnProxy.findByStepDynamicDataIdAndDeleted(stepDynamicData.getId(), net.airuima.constant.Constants.LONG_ZERO);
            //过滤掉禁用状态的动态元数据
            List<Long> dynamicDataColumnIdList = stepDynamicDataColumnList.stream().map(StepDynamicDataColumnDTO::getDynamicDataColumn)
                    .filter(DynamicDataColumnDTO::getIsEnable).map(DynamicDataColumnDTO::getId).toList();
            //子动态元数据集合
            List<DynamicDataColumnDTO> subDynamicDataColumnList = rbaseDynamicDataColumnProxy.findByParentIdInAndDeleted(dynamicDataColumnIdList, net.airuima.constant.Constants.LONG_ZERO);
            //Map<动态元数据ID, 子动态元数据集合>
            Map<Long, List<DynamicDataColumnDTO>> subStepDynamicDataColumnMap = subDynamicDataColumnList.stream().collect(Collectors.groupingBy(i -> i.getParent().getId()));
            //组装动态元数据及子元数据
            List<StepDynamicDataGetDTO.ColumnInfo> columnInfoList = stepDynamicDataColumnList.stream().map(stepDynamicDataColumn -> {
                StepDynamicDataGetDTO.ColumnInfo columnInfo = net.airuima.util.MapperUtils.map(stepDynamicDataColumn.getDynamicDataColumn(), StepDynamicDataGetDTO.ColumnInfo.class);
                columnInfo.setColumnName(stepDynamicDataColumn.getDynamicDataColumn().getName()).setColumnCode(stepDynamicDataColumn.getDynamicDataColumn().getCode())
                        .setFormOrder(stepDynamicDataColumn.getFormOrder());
                //若当前动态元数据存在子动态元数据
                if (subStepDynamicDataColumnMap.containsKey(stepDynamicDataColumn.getDynamicDataColumn().getId())) {
                    columnInfo.setSubStepDynamicDataColumnGetDTOList(net.airuima.util.MapperUtils.mapAll(subStepDynamicDataColumnMap.get(stepDynamicDataColumn.getDynamicDataColumn().getId()), SubDynamicDataColumnGetDTO.class));
                }
                return columnInfo;
            }).collect(Collectors.toList());
            stepDynamicDataGetDTO.setColumnInfoList(columnInfoList);

            clientGetStepInfoDto.setStepDynamicDataGetDTO(stepDynamicDataGetDTO);
        }
    }

    /**
     * 获取预警规则
     *
     * @param workSheet     工单
     * @param step          工序
     * @param finishNumber  工序完成数
     * @param qualifiedRate 工序合格率
     * @return : net.airuima.rbase.domain.base.quality.StepWarningStandard
     * <AUTHOR>
     * @date 2022/10/26
     **/
    public StepWarningStandard getStepWarningStandard(WorkSheet workSheet, Step step, Integer finishNumber, Double qualifiedRate) {
        //递归获取产品谱系所有父级ID集合
        List<Long> pedigreeIdList = commonService.getAllParent(workSheet.getPedigree());
        //优先级模式（优先级相对最高的预警规则。）
        //    获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = commonService.findSnapshotWorkFlow(workSheet, null, step);
        List<StepWarningStandard> stepWarningStandardList = stepWarningStandardRepository.findAllStandardByElementOrderByPriorityAndPedigree(pedigreeIdList, workSheet.getId(), workSheet.getCategory(), Objects.isNull(step.getStepGroup()) ? null : step.getStepGroup().getId(), step.getId(), snapshotWorkFlow.getId(), workSheet.getClientId(), Constants.INT_ZERO, Constants.LONG_ZERO);
        List<StepWarningStandard> stepWarningStandardListSort = new ArrayList<StepWarningStandard>();
        if (stepWarningStandardList.size() > 1) {
            //1. 获取优先级排序最高集合
            StepWarningStandard stepWarningStandard = stepWarningStandardList.stream().sorted(Comparator.comparing(i -> i.getPriorityElementConfig().getPriority())).findFirst().orElseThrow(() -> new ResponseException("error.stepWarningStandardNotExist", "生产工序预警标准不存在"));
            stepWarningStandardList = stepWarningStandardList.stream().filter(i -> i.getPriorityElementConfig().getPriority() == stepWarningStandard.getPriorityElementConfig().getPriority()).collect(Collectors.toList());
            if (stepWarningStandardList.size() > 1) {
                //2. 对产品谱系进行排序（有可能产品谱系为空）
                List<StepWarningStandard> stepWarningStandardListPedigreeNull = stepWarningStandardList.stream().filter(i -> Objects.isNull(i.getPedigree())).collect(Collectors.toList());
                List<StepWarningStandard> stepWarningStandardListPedigreeNotNull = stepWarningStandardList.stream().filter(i -> !Objects.isNull(i.getPedigree())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(stepWarningStandardListPedigreeNull)) {
                    stepWarningStandardListSort.addAll(stepWarningStandardListPedigreeNull);
                }
                if (!CollectionUtils.isEmpty(stepWarningStandardListPedigreeNotNull)) {
                    //根据产品谱系类型进行正序排序（此时“预警条件”优先级均相同）
                    stepWarningStandardListPedigreeNotNull = stepWarningStandardListPedigreeNotNull.stream().sorted(Comparator.comparing(i -> i.getPedigree().getType())).collect(Collectors.toList());
                    stepWarningStandardListSort.addAll(stepWarningStandardListPedigreeNotNull);
                }
            } else {
                stepWarningStandardListSort.addAll(stepWarningStandardList);
            }
        } else if (stepWarningStandardList.size() == 1) {
            stepWarningStandardListSort.addAll(stepWarningStandardList);
        }
        return CollectionUtils.isEmpty(stepWarningStandardListSort) ? null : stepWarningStandardListSort.get(stepWarningStandardListSort.size() - Constants.INT_ONE);
    }

    /**
     * 验证车间环境检测是否合规
     *
     * @param workCellId 工位Id
     * @return BaseClientDTO
     */
    public BaseClientDTO validateEnviroment(Long workCellId) {
        // 通过区域工位获取唯一记录
        AreaWorkCell areaWorkCell = areaWorkCellRepository.findByWorkCellIdAndIsEnableAndDeleted(workCellId, Constants.TRUE, Constants.LONG_ZERO).orElse(null);
        if (areaWorkCell == null) {
            return new BaseClientDTO(Constants.OK);
        }
        BaseClientDTO cleanlinessDto = clientControlInspectService[0].checkCleanliness(areaWorkCell);
        if (cleanlinessDto != null) {
            return cleanlinessDto;
        }
        BaseClientDTO humitureDTO = clientControlInspectService[0].checkHumiture(areaWorkCell);
        if (humitureDTO != null) {
            return humitureDTO;
        }
        return new BaseClientDTO(Constants.OK);
    }


    /**
     * 处理工序时间间隔计算
     *
     * @param pedigreeStepIntervalConfigList 获取工序时间间隔配置
     * @param requestContainerIds            原容器号
     * @param wsStepList                     工单定制工序列表
     * @param currWsStep                     当前工序
     * @param subWorkSheet                   子工单
     * @return String
     */
    public String validateStepInterval(List<PedigreeStepIntervalConfig> pedigreeStepIntervalConfigList, List<Long> requestContainerIds,
                                       List<WsStep> wsStepList, WsStep currWsStep, SubWorkSheet subWorkSheet) {
        List<BatchWorkDetail> batchWorkDetails = Lists.newArrayList();
        List<ContainerDetail> containerDetails = Lists.newArrayList();
        // 获取子工单对应的容器详情列表
        List<ContainerDetail> containerDetailList = containerDetailRepository.findBySubWorkSheetId(subWorkSheet.getId());
        // 通过子工单对应的生产详情列表
        List<BatchWorkDetail> batchWorkDetailList = batchWorkDetailRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);

        // 递归获取子工单的工序的生命周期
        if (ValidateUtils.isValid(requestContainerIds)) {
            // 获取每一个容器前置工序的详情信息
            findContainerDetail(batchWorkDetails, containerDetails, containerDetailList, batchWorkDetailList, requestContainerIds, wsStepList, subWorkSheet);
        } else {
            // 获取批量前置工序的详情信息
            findBatchDetail(containerDetails, batchWorkDetails, batchWorkDetailList, currWsStep, wsStepList, subWorkSheet);
        }
        StringBuilder result = new StringBuilder();
        for (PedigreeStepIntervalConfig config : pedigreeStepIntervalConfigList) {
            // 检查容器详情是否超出与配置的间隔时长
            ContainerDetail containerDetail = containerDetails.stream().filter(detail -> config.getPreStep().getId().equals(detail.getBatchWorkDetail().getStep().getId()))
                    .findFirst().orElse(null);
            if (containerDetail != null) {
                LocalDateTime startTime = containerDetail.getBindTime();
                double hourMinus = DateUtils.LocalDateTimeHourMinus(startTime, LocalDateTime.now());
                if (!validateStepIntervalWhenSave(startTime, LocalDateTime.now(), config)) {
                    result.append("容器号为【").append(containerDetail.getContainerCode()).append("】的当前工序【").append(config.getStep().getName())
                            .append("】与前置工序【").append(config.getPreStep().getName()).append("】配置的时间间隔超出, 请注意!!!");
                }
                if (ValidateUtils.isValid(result.toString())) {
                    return result.toString();
                }
            }
            // 检查批量详情是否超出与配置的间隔时长
            BatchWorkDetail batchWorkDetail = batchWorkDetails.stream().filter(detail -> config.getPreStep().getId().equals(detail.getStep().getId()))
                    .findFirst().orElse(null);
            if (batchWorkDetail != null) {
                LocalDateTime startTime = batchWorkDetail.getEndDate();
                double hourMinus = DateUtils.LocalDateTimeHourMinus(startTime, LocalDateTime.now());
                if (!validateStepIntervalWhenSave(startTime, LocalDateTime.now(), config)) {
                    result.append("当前工序【").append(config.getStep().getName()).append("】与前置工序【").append(config.getPreStep().getName()).append("】配置的时间间隔超出, 请注意!!!");
                }
                if (ValidateUtils.isValid(result.toString())) {
                    return result.toString();
                }
            }
        }
        return null;
    }

    /**
     * 获取前置容器详情
     *
     * @param batchWorkDetails    前置容器详情集合
     * @param containerDetails    前置批量详情集合
     * @param containerDetailList 子工单对应的容器详情列表
     * @param batchWorkDetailList 子工单对应的生产详情列表
     * @param requestContainerIds 原容器号
     * @param wsStepList          工单定制工序列表
     * @param subWorkSheet        子工单
     */
    public void findContainerDetail(List<BatchWorkDetail> batchWorkDetails, List<ContainerDetail> containerDetails, List<ContainerDetail> containerDetailList,
                                    List<BatchWorkDetail> batchWorkDetailList, List<Long> requestContainerIds, List<WsStep> wsStepList, SubWorkSheet subWorkSheet) {
        for (Long requestContainerId : requestContainerIds) {
            // 获取容器详情
            Optional<ContainerDetail> first = containerDetailList.stream().sorted(Comparator.comparing(ContainerDetail::getBindTime).reversed()).filter(preContainerDetail -> preContainerDetail.getContainer().getId().equals(requestContainerId)).findFirst();
            if (first.isPresent()) {
                containerDetails.add(first.get());
                if (ValidateUtils.isValid(first.get().getPreContainerDetailInfoList())) {
                    List<Long> preContainerDetailIds = first.get().getPreContainerDetailInfoList().stream().map(PreContainerDetailInfo::getPreContainerDetailId).collect(Collectors.toList());
                    findContainerDetail(batchWorkDetails, containerDetails, containerDetailList, batchWorkDetailList, preContainerDetailIds, wsStepList, subWorkSheet);
                } else {
                    // 没有前置容器详情，那么判断还有没有上一道工序
                    wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(first.get().getBatchWorkDetail().getStep().getId()))
                            .findFirst().ifPresent(wsStep -> {
                                // 判断还有没有前置工序
                                if (ValidateUtils.isValid(wsStep.getPreStepId())) {
                                    // 分割前置工序
                                    List<Long> preStepIds = Arrays.stream(wsStep.getPreStepId().split(Constants.STR_COMMA)).map(Long::parseLong).collect(Collectors.toList());
                                    // 循环递归
                                    preStepIds.forEach(preStepId -> {
                                        // 判断前置工序是否存在生产工单定制工序里面
                                        Optional<WsStep> wsStepOptional = wsStepList.stream().filter(proWsStep -> proWsStep.getStep().getId().equals(preStepId)).findFirst();
                                        wsStepOptional.ifPresent(currWsStep -> findBatchDetail(containerDetails, batchWorkDetails, batchWorkDetailList, currWsStep, wsStepList, subWorkSheet));
                                    });
                                }
                            });
                }
            }
        }
    }

    /**
     * 获取前置生产详情
     *
     * @param containerDetails    前置批量详情集合
     * @param batchWorkDetails    前置容器详情集合
     * @param batchWorkDetailList 子工单对应的生产详情列表
     * @param currWsStep          当前工序
     * @param wsStepList          工单定制工序列表
     * @param subWorkSheet        子工单
     */
    public void findBatchDetail(List<ContainerDetail> containerDetails, List<BatchWorkDetail> batchWorkDetails, List<BatchWorkDetail> batchWorkDetailList, WsStep currWsStep, List<WsStep> wsStepList, SubWorkSheet subWorkSheet) {
        // 请求模式是工单, 不绑定容器
        if (currWsStep.getRequestMode() == Constants.INT_ZERO && !currWsStep.getIsBindContainer()) {
            // 获取
            batchWorkDetailList.stream().filter(batchWorkDetail -> batchWorkDetail.getStep().getId().equals(currWsStep.getStep().getId())).findFirst().ifPresent(batchWorkDetails::add);
        }
        // 请求模式是容器, 绑定容器
        if (currWsStep.getRequestMode() == Constants.INT_ONE || currWsStep.getIsBindContainer()) {
            // 通过子工单、工序获取绑定时间最早的一条容器详情
            containerDetailRepository.findTop1ByBatchWorkDetailSubWorkSheetIdAndBatchWorkDetailStepIdAndDeletedOrderByBindTime(subWorkSheet.getId(), currWsStep.getStep().getId(), Constants.LONG_ZERO)
                    .ifPresent(containerDetails::add);
        }
        // 判断有没有前置工序，有前置工序递归继续获取数据信息
        if (ValidateUtils.isValid(currWsStep.getPreStepId())) {
            List<Long> preStepIds = Arrays.stream(currWsStep.getPreStepId().split(Constants.STR_COMMA)).map(Long::parseLong).collect(Collectors.toList());
            preStepIds.forEach(preStepId -> {
                Optional<WsStep> wsStepOptional = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(preStepId)).findFirst();
                wsStepOptional.ifPresent(wsStep -> findBatchDetail(containerDetails, batchWorkDetails, batchWorkDetailList, wsStep, wsStepList, subWorkSheet));
            });
        }
    }

    /**
     * 获得fqc信息
     *
     * @param pedigree
     * @param step
     * @return
     */
    public ClientGetStepInfoDTO.FqcInfo getFqcInfo(Pedigree pedigree, WorkFlow workFlow, Step step, int number, Long clientId) {
        Optional<PedigreeStepCheckRule> pedigreeStepCheckRuleOptional = pedigreeStepCheckRuleRepository
                .findByPedigreeIdAndWorkFlowIdAndStepIdAndCategoryAndDeleted(pedigree.getId(), workFlow.getId(), step.getId(), Constants.INT_THREE, Constants.LONG_ZERO);
        if (pedigreeStepCheckRuleOptional.isPresent()) {
            PedigreeStepCheckRule pedigreeStepCheckRule = pedigreeStepCheckRuleOptional.get();
            ClientGetStepInfoDTO.FqcInfo fqcInfo = new ClientGetStepInfoDTO.FqcInfo();
            fqcInfo.setFqcCheckNumber(NumberUtils.multiply(pedigreeStepCheckRule.getRate(), number).intValue());
            List<WorkFlow> workFlowList = commonService.findWorkFlowByPedigreeIdAndClientId(pedigree.getId(), clientId);
            Assert.notEmpty(workFlowList, "流程框图不存在");
            fqcInfo.setWorkFlowInfoList(workFlowList.stream().map(ClientGetStepInfoDTO.WorkFlowInfo::new).collect(Collectors.toList()));
            return fqcInfo;
        }
        return null;
    }

    /**
     * 获取生产抽检信息
     *
     * @param subWorkSheet 子工单
     * @param pedigree     产品谱系
     * @param workFlow     工艺路线
     * @param step         工序
     * @param workCellId   工位ID
     * @param number       投产数
     * @return ClientGetStepInfoDTO.RandomInspectInfo
     */
    @Transactional(readOnly = true)
    public ClientGetStepInfoDTO.RandomInspectGetInfo getRandomInspectInfo(SubWorkSheet subWorkSheet, Pedigree pedigree, WorkFlow workFlow, Step step, Long workCellId, int number) {
        PedigreeStepCheckRule pedigreeStepCheckRule = commonService.findPedigreeStepCheckRule(subWorkSheet.getWorkSheet(), subWorkSheet, pedigree, step, Constants.INSPECT_PQC_CATEGORY, workCellId, null);
        if (null == pedigreeStepCheckRule) {
            return null;
        }
        SampleCaseDTO sampleCase = pedigreeStepCheckRule.getSampleCase();
        int category = sampleCase.getCategory();

        ClientGetStepInfoDTO.RandomInspectGetInfo randomInspectInfo = new ClientGetStepInfoDTO.RandomInspectGetInfo();
        randomInspectInfo.setCategory(Constants.INSPECT_PQC_CATEGORY);
        randomInspectInfo.setCheckNumber(category == SampleCaseCategoryEnum.SPECIFY_QUANTITY.getCategory() ? sampleCase.getNumber() : Double.valueOf(Math.ceil(NumberUtils.multiply(sampleCase.getRate(), number).doubleValue())).intValue());
        randomInspectInfo.setMaxUnQualifiedNumber(category == SampleCaseCategoryEnum.SPECIFY_QUANTITY.getCategory() ? sampleCase.getAc() : Double.valueOf(Math.ceil(NumberUtils.multiply(1 - sampleCase.getQualifiedRate(), number).doubleValue())).intValue());
        List<PedigreeStepCheckItem> pedigreeStepCheckItems = pedigreeStepCheckItemRepository.findByPedigreeStepCheckRuleIdAndDeleted(pedigreeStepCheckRule.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(pedigreeStepCheckItems)) {
            List<ClientGetCheckInfoDTO.CheckRuleInfo.CheckItemInfo> checkItemInfoList = Lists.newArrayList();
            pedigreeStepCheckItems.forEach(pedigreeStepCheckItem -> {
                ClientGetCheckInfoDTO.CheckRuleInfo.CheckItemInfo checkItemInfo = new ClientGetCheckInfoDTO.CheckRuleInfo.CheckItemInfo();
                checkItemInfo.setCheckItemCode(pedigreeStepCheckItem.getCheckItem().getCode())
                        .setCheckItemId(pedigreeStepCheckItem.getCheckItem().getId())
                        .setCheckItemName(pedigreeStepCheckItem.getCheckItem().getName())
                        .setQualifiedRange(pedigreeStepCheckItem.getQualifiedRange());
//                        .setVariety(pedigreeStepCheckItem.getCheckItem().getVariety());
                checkItemInfoList.add(checkItemInfo);
            });
            randomInspectInfo.setCheckItemInfoList(checkItemInfoList);
        }
        return randomInspectInfo;
    }


    /**
     * 根据工位和工序获取绑定的设备信息
     * 优先获取工位工序设备，其次获取工位绑定的设备信息
     *
     * @param workCellId 工位ID
     * @param stepId     工序Id
     * @return List<EquipmentInfo>
     * <AUTHOR>
     * @date 2021-01-18
     **/
    public List<ClientGetStepInfoDTO.EquipmentInfo> getEquipmentIfo(Long workCellId, Long stepId) {
        List<WorkCellStepFacility> workCellStepFacilities = workCellStepFacilityRepository.findByWorkCellIdAndStepIdAndDeleted(workCellId, stepId, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(workCellStepFacilities)) {
            return workCellStepFacilities.stream().map(WorkCellStepFacility::getFacilityDto).toList().stream().map(ClientGetStepInfoDTO.EquipmentInfo::new).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 获取不良项目以及最大不合格项目数量
     *
     * @param subWorkSheet 子工单
     * @param nextTodoStep 待做工序
     * @param number       投产数
     * @return List<UnqualifiedItemInfo>
     * <AUTHOR>
     * @date 2021-04-24
     **/
    public List<ClientGetStepInfoDTO.UnqualifiedItemInfo> getUnqualifiedItemInfo(SubWorkSheet subWorkSheet, Step nextTodoStep, int number) {
        //获取工序不良项目,优先获取产品谱系配置的工序不良项目，最后才获取工序默认配置的不良项目
        // 获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = commonService.findSnapshotWorkFlow(subWorkSheet.getWorkSheet(), subWorkSheet, nextTodoStep);
        List<UnqualifiedItem> unqualifiedItems = commonService.findPedigreeStepUnqualifiedItem(subWorkSheet.getWorkSheet().getPedigree(), snapshotWorkFlow.getId(), nextTodoStep.getId(), subWorkSheet.getWorkSheet().getClientId());
        List<ClientGetStepInfoDTO.UnqualifiedItemInfo> unqualifiedItemInfoList = Lists.newArrayList();
        if (ValidateUtils.isValid(unqualifiedItems)) {
            unqualifiedItems.forEach(unqualifiedItem -> {
                UnqualifiedItemWarningStandard unqualifiedItemWarningStandard = commonService.findUnqualifiedItemWaringStandard(subWorkSheet, subWorkSheet.getWorkSheet().getPedigree(), nextTodoStep, unqualifiedItem, number, null);
                ClientGetStepInfoDTO.UnqualifiedItemInfo unqualifiedItemInfo = new ClientGetStepInfoDTO.UnqualifiedItemInfo(unqualifiedItem);
                if (null != unqualifiedItemWarningStandard) {
                    unqualifiedItemInfo.setMaxUnQualifiedNumber(unqualifiedItemWarningStandard.getBaseNumber() > number ? unqualifiedItemWarningStandard.getWaringNumber() : (int) Math.ceil(number * unqualifiedItemWarningStandard.getWaringRate()));
                } else {
                    unqualifiedItemInfo.setMaxUnQualifiedNumber(Constants.MAX_INT_DATA);
                }
                unqualifiedItemInfoList.add(unqualifiedItemInfo);
            });
        }
        return unqualifiedItemInfoList;
    }

    /**
     * 获取工位需要上料信息
     *
     * @param subWorkSheet 子工单
     * @param workCell     当前请求的工位
     * @param toDoStep     待做工序
     * @param putInNumber  投产数
     * @return List<ClientGetStepInfoDTO.MaterialInfo>
     */
    @Transactional(readOnly = true)
    public List<ClientGetStepInfoDTO.MaterialRuleInfo> getMaterialInfo(SubWorkSheet subWorkSheet, WorkCell workCell, Step toDoStep, int putInNumber) {
        List<ClientGetStepInfoDTO.MaterialRuleInfo> materialRuleInfoList = new ArrayList<>();
        // 获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = commonService.findSnapshotWorkFlow(subWorkSheet.getWorkSheet(), subWorkSheet, toDoStep);
        //查找谱系工序上料规则
        List<PedigreeStepMaterialRule> pedigreeStepMaterialRuleList = commonService.findPedigreeStepMaterialRule(subWorkSheet.getWorkSheet().getClientId(), subWorkSheet.getWorkSheet().getPedigree().getId(), snapshotWorkFlow.getId(), toDoStep.getId());
        if (!ValidateUtils.isValid(pedigreeStepMaterialRuleList)) {
            throw new ResponseException("error.notExistStepMaterialRule", "工序(" + toDoStep.getName() + ")需要管控物料,但尚未配置工序上料规则");
        }
        //查找工单投料单
        List<WsMaterial> wsMaterialList = wsMaterialRepository.findByWorkSheetIdAndDeleted(subWorkSheet.getWorkSheet().getId(), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(wsMaterialList)) {
            throw new ResponseException("error.notExistWsMaterial", "工序(" + toDoStep.getName() + ")需要管控物料,但工单投料单不存在");
        }
        //过滤出投料单与上料规则交集的上料规则列表
        List<PedigreeStepMaterialRule> pedigreeStepMaterialRules = pedigreeStepMaterialRuleList.stream().filter(pedigreeStepMaterialRule -> wsMaterialList.stream().map(WsMaterial::getOriginMaterialId).collect(Collectors.toList()).contains(pedigreeStepMaterialRule.getMaterialId())).collect(Collectors.toList());
        if (!ValidateUtils.isValid(pedigreeStepMaterialRules)) {
            throw new ResponseException("error.notExistMatchedStepMaterialRule", "工序(" + toDoStep.getName() + ")需要管控物料,但上料规则和投料单无任何物料相匹配");
        }
        //工序生产过程物料库存管控级别(0:不管控物料库存;1:总工单物料库存;2:工位物料库存)
        int materialControlLevel = commonService.getMaterialControlLevel();
        WorkSheet originalWorkSheet = subWorkSheet.getWorkSheet();
        if (originalWorkSheet.getCategory() == WsEnum.ONLINE_RE_WS.getCategory()) {
            WsRework wsRework = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(originalWorkSheet.getId(), Constants.LONG_ZERO).orElse(null);
            if (null != wsRework) {
                originalWorkSheet = wsRework.getOriginalWorkSheet();
            }
        }
        pedigreeStepMaterialRules.forEach(pedigreeStepMaterialRule -> {
            List<WsMaterial> wsMaterials = wsMaterialList.stream().filter(wsMaterial -> wsMaterial.getOriginMaterialId().equals(pedigreeStepMaterialRule.getMaterialId())).collect(Collectors.toList());
            ClientGetStepInfoDTO.MaterialRuleInfo materialRuleInfo = new ClientGetStepInfoDTO.MaterialRuleInfo();
            List<ClientGetStepInfoDTO.MaterialRuleInfo.MaterialGroupInfo> materialGroupInfos = Lists.newArrayList();
            materialRuleInfo.setMaterialId(pedigreeStepMaterialRule.getMaterialId())
                    .setMaterialName(pedigreeStepMaterialRule.getMaterialDto().getName())
                    .setMaterialCode(pedigreeStepMaterialRule.getMaterialDto().getCode())
                    .setProportion(pedigreeStepMaterialRule.getProportion())
                    //如果需要验证库存且上料规则配置的需要扣数则计算上料总数,后面会根据库存扣减
                    .setNumber(putInNumber * pedigreeStepMaterialRule.getProportion())
                    .setLeftNumber(Constants.DOUBLE_ZERRO)
                    .setCheckMaterial(pedigreeStepMaterialRule.getIsCheckMaterial())
                    .setCheckMaterialBatch(pedigreeStepMaterialRule.getIsCheckMaterialBatch())
                    .setDeduct(pedigreeStepMaterialRule.getIsDeduct())
                    .setControlMaterialGranularity(pedigreeStepMaterialRule.getControlMaterialGranularity())
                    .setSerialNumberRule(pedigreeStepMaterialRule.getSerialNumberRule())
                    .setControlSnCount(pedigreeStepMaterialRule.getControlSnCount())
                    .setIsGlue(Boolean.FALSE);
            //获取上料物料及及其替换料
            wsMaterials.forEach(wsMaterial -> {
                ClientGetStepInfoDTO.MaterialRuleInfo.MaterialGroupInfo materialGroupInfo = new ClientGetStepInfoDTO.MaterialRuleInfo.MaterialGroupInfo();
                materialGroupInfos.add(materialGroupInfo.setMaterialCode(wsMaterial.getMaterialDto().getCode()).setMaterialName(wsMaterial.getMaterialDto().getName()).setMaterialId(wsMaterial.getMaterialId()));
            });
            materialRuleInfo.setMaterialGroupInfoList(materialGroupInfos);
            materialRuleInfoList.add(materialRuleInfo);
        });
        if (!ValidateUtils.isValid(materialRuleInfoList)) {
            return Collections.emptyList();
        }
        //获取物料对应的批次，如果需要扣数则给出对应的物料剩余数量
        WorkSheet finalOriginalWorkSheet = originalWorkSheet;
        materialRuleInfoList.forEach(materialRuleInfo -> {
            List<Long> materialIds = materialRuleInfo.getMaterialGroupInfoList().stream().map(ClientGetStepInfoDTO.MaterialRuleInfo.MaterialGroupInfo::getMaterialId).collect(Collectors.toList());
            //物料对应的剩余数量
            if (Boolean.TRUE.equals(materialRuleInfo.isDeduct()) && ValidateUtils.isValid(materialIds) && materialControlLevel != ConstantsEnum.MATERIAL_NOT_CONTROL_LEVEL.getCategoryName()) {
                BigDecimal leftNumber = materialControlLevel == ConstantsEnum.MATERIAL_WORKSHEET_CONTROL_LEVEL.getCategoryName() ? wsMaterialBatchRepository.sumLeftNumberByWorkSheetIdAndMaterialIdInAndDeleted(subWorkSheet.getWorkSheet().getId(), materialIds, Constants.LONG_ZERO) : wsWorkCellMaterialBatchRepository.sumLeftNumberByWorkSheetIdAndWorkCellIdAndMaterialIdInAndDeleted(subWorkSheet.getWorkSheet().getId(),
                        workCell.getId(), materialIds, Constants.LONG_ZERO);
                materialRuleInfo.setLeftNumber(null != leftNumber ? leftNumber.doubleValue() : Constants.DOUBLE_ZERRO);
                if (null != leftNumber && leftNumber.doubleValue() > Constants.INT_ZERO) {
                    materialRuleInfo.setNumber(materialRuleInfo.getNumber() > leftNumber.doubleValue() ? materialRuleInfo.getNumber() - leftNumber.doubleValue() : Constants.DOUBLE_ZERRO);
                }
            }
            List<ClientGetStepInfoDTO.MaterialRuleInfo.MaterialGroupInfo> materialGroupInfos = materialRuleInfo.getMaterialGroupInfoList();
            //获取物料组里面每个物料批次的剩余数量
            if (ValidateUtils.isValid(materialGroupInfos)) {
                materialGroupInfos.forEach(materialGroupInfo -> {
                    List<ClientGetStepInfoDTO.MaterialRuleInfo.MaterialGroupInfo.MaterialBatchInfo> materialBatchInfoList = Lists.newArrayList();
                    //工位库存管控且工位库存足够，返回当前物料的工位批次信息
                    if (materialRuleInfo.getNumber() == Constants.DOUBLE_ZERRO && materialControlLevel == ConstantsEnum.MATERIAL_WORK_CELL_CONTROL_LEVEL.getCategoryName()) {
                        List<WsWorkCellMaterialBatch> wsWorkCellMaterialBatches = wsWorkCellMaterialBatchRepository.findByWorkSheetIdAndWorkCellIdAndMaterialIdInAndLeftNumberGreaterThanAndDeleted(finalOriginalWorkSheet.getId(), workCell.getId(), materialIds, Constants.DOUBLE_ZERRO, Constants.LONG_ZERO);
                        wsWorkCellMaterialBatches.stream().filter(wsWorkCellMaterialBatch -> wsWorkCellMaterialBatch.getMaterialId().equals(materialGroupInfo.getMaterialId())).forEach(wsWorkCellMaterialBatch -> {
                            ClientGetStepInfoDTO.MaterialRuleInfo.MaterialGroupInfo.MaterialBatchInfo materialBatchInfo = new ClientGetStepInfoDTO.MaterialRuleInfo.MaterialGroupInfo.MaterialBatchInfo();
                            materialBatchInfo.setMaterialBatch(wsWorkCellMaterialBatch.getBatch());
                            materialBatchInfo.setNumber(wsWorkCellMaterialBatch.getLeftNumber());
                            materialBatchInfoList.add(materialBatchInfo);
                        });
                    } else {
                        List<WsMaterialBatch> wsMaterialBatches = wsMaterialBatchRepository.findByWorkSheetIdAndMaterialIdInAndLeftNumberIsGreaterThanAndDeleted(finalOriginalWorkSheet.getId(), materialIds, Constants.DOUBLE_ZERRO, Constants.LONG_ZERO);
                        wsMaterialBatches.stream().filter(wsMaterialBatch -> wsMaterialBatch.getMaterialId().equals(materialGroupInfo.getMaterialId())).forEach(wsMaterialBatch -> {
                            ClientGetStepInfoDTO.MaterialRuleInfo.MaterialGroupInfo.MaterialBatchInfo materialBatchInfo = new ClientGetStepInfoDTO.MaterialRuleInfo.MaterialGroupInfo.MaterialBatchInfo();
                            materialBatchInfo.setMaterialBatch(wsMaterialBatch.getBatch());
                            materialBatchInfo.setNumber(wsMaterialBatch.getLeftNumber());
                            materialBatchInfoList.add(materialBatchInfo);
                        });
                    }
                    materialGroupInfo.setMaterialBatchList(materialBatchInfoList);
                });
            }
        });
        //添加胶水标记
        return glueModeServices[0].isGlueMaterial(materialRuleInfoList, pedigreeStepMaterialRuleList, putInNumber);
    }

    /**
     * 获取当前待做工序的易损件信息
     *
     * @param subWorkSheet 子工单
     * @param currWsStep   当前待做定制工序
     * @return
     * <AUTHOR>
     * @date 2021/8/25
     */
    public List<ClientGetStepInfoDTO.PedigreeStepWearingPartGroupInfo> getWearingPartInfo(SubWorkSheet subWorkSheet, WsStep currWsStep, WorkCell workCell) {
        //获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = null != currWsStep.getWorkFlow() ? currWsStep.getWorkFlow() : subWorkSheet.getWorkFlow();
        //获取当前工序易损件的类型信息
        List<PedigreeStepWearingPartGroup> pedigreeStepWearingPartGroupList = commonService.findPedigreeWorkFlowStepByWearingGroupInfo(subWorkSheet.getWorkSheet().getPedigree(), snapshotWorkFlow, currWsStep.getStep(), workCell);
        List<ClientGetStepInfoDTO.PedigreeStepWearingPartGroupInfo> pedigreeStepWearingPartGroupInfoList = Lists.newArrayList();
        // 判断当前工序有没有易损件的类型信息
        if (ValidateUtils.isValid(pedigreeStepWearingPartGroupList)) {
            // 循环易损件的类型信息
            pedigreeStepWearingPartGroupList.forEach(pedigreeStepWearingPartGroup -> {
                WearingPartGroup wearingPartGroup = wearingPartGroupRepository.findByIdAndDeleted(pedigreeStepWearingPartGroup.getWearingPartGroup().getId(), Constants.LONG_ZERO).orElse(null);
                if (null != wearingPartGroup) {
                    pedigreeStepWearingPartGroupInfoList.add(new ClientGetStepInfoDTO.PedigreeStepWearingPartGroupInfo(wearingPartGroup.getId(), wearingPartGroup.getName(), pedigreeStepWearingPartGroup.getNumber()));
                }
            });
        }

        if (ValidateUtils.isValid(pedigreeStepWearingPartGroupInfoList)) {
            List<WearingPartExchange> wearingPartExchangeList = wearingPartExchangeRepository.findByOriginWearingPartGroupIdInAndDeleted(pedigreeStepWearingPartGroupInfoList.stream().map(ClientGetStepInfoDTO.PedigreeStepWearingPartGroupInfo::getWearingPartGroupId).collect(Collectors.toList()), Constants.LONG_ZERO);
            Map<Long, List<WearingPartExchange>> collect = wearingPartExchangeList.stream().collect(Collectors.groupingBy(wearingPartExchange -> wearingPartExchange.getOriginWearingPartGroup().getId()));
            if (!ValidateUtils.isValid(collect)) {
                return pedigreeStepWearingPartGroupInfoList;
            }
            pedigreeStepWearingPartGroupInfoList.forEach(pedigreeStepWearingPartGroupInfo -> {
                if (!collect.containsKey(pedigreeStepWearingPartGroupInfo.getWearingPartGroupId())) {
                    return;
                }
                List<WearingPartExchange> exchangeList = collect.get(pedigreeStepWearingPartGroupInfo.getWearingPartGroupId());
                pedigreeStepWearingPartGroupInfo.setPedigreeStepWearingPartGroupExchangeInfoList((List<ClientGetStepInfoDTO.PedigreeStepWearingPartGroupInfo>) exchangeList.stream().map(exchange -> new ClientGetStepInfoDTO.PedigreeStepWearingPartGroupInfo(exchange)));
            });
        }
        return pedigreeStepWearingPartGroupInfoList;
    }

    /**
     * 保存工序信息
     *
     * @param clientSaveStepInfoDto 待保存的工序信息
     * @return BaseClientDTO
     */
    @Transactional(rollbackFor = Exception.class)
    @Klock(keys = {"#clientSaveStepInfoDto.subWsId", "#clientSaveStepInfoDto.stepId"}, waitTime = 60, leaseTime = 60, lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST)
    public BaseClientDTO saveStepInfo(ClientSaveStepInfoDTO clientSaveStepInfoDto) {
        Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findByIdAndDeleted(clientSaveStepInfoDto.getSubWsId(), Constants.LONG_ZERO);
        if (!subWorkSheetOptional.isPresent()) {
            return new BaseClientDTO(Constants.KO, SUB_WORKSHEET_NOT_EXIST);
        }
        Optional<Step> stepOptional = stepRepository.findByIdAndDeleted(clientSaveStepInfoDto.getStepId(), Constants.LONG_ZERO);
        if (!stepOptional.isPresent()) {
            return new BaseClientDTO(Constants.KO, "工序不存在!");
        }
        Optional<WorkCell> workCellOptional = workCellRepository.findByIdAndDeleted(clientSaveStepInfoDto.getWorkCellId(), Constants.LONG_ZERO);
        if (!workCellOptional.isPresent()) {
            return new BaseClientDTO(Constants.KO, "工位不存在!");
        }
        //根据token获取redis里面的记录，防止重复提交
        if (StringUtils.isNotBlank(clientSaveStepInfoDto.getXsrfToken()) && null != redisUtils.get(clientSaveStepInfoDto.getXsrfToken())) {
            return new BaseClientDTO(Constants.KO, "当前生产数据已保存,请勿重复提交!");
        }
        SubWorkSheet subWorkSheet = subWorkSheetOptional.get();
        WorkSheet workSheet = subWorkSheet.getWorkSheet();
        Step step = stepOptional.get();
        WorkCell workCell = workCellOptional.get();
        //校验子工单状态
        BaseClientDTO checkSubWs = checkSubWs(subWorkSheet);
        if (Constants.KO.equals(checkSubWs.getStatus())) {
            return checkSubWs;
        }
        Optional<WsStep> wsStepOptional = wsStepRepository.findBySubWorkSheetIdAndStepIdAndDeleted(
                subWorkSheet.getId(), step.getId(), Constants.LONG_ZERO);
        if (!wsStepOptional.isPresent()) {
            wsStepOptional = wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), step.getId(), Constants.LONG_ZERO);
        }
        if (!wsStepOptional.isPresent()) {
            return new BaseClientDTO(Constants.KO, "工单投产工序不存在!");
        }
        //是否是最后一道工序
        boolean isLastStep = false;
        //前置工序是否已经完成
        boolean preStepIsFinish = true;
        //验证前置工序是否都已经完成
        if (!ValidateUtils.isValid(wsStepOptional.get().getAfterStepId())) {
            isLastStep = true;
        }
        WsStep wsStep = wsStepOptional.get();
        if (ValidateUtils.isValid(wsStepOptional.get().getPreStepId())) {
            String[] preStepIds = wsStepOptional.get().getPreStepId().split(Constants.STR_COMMA);
            for (String preStepId : preStepIds) {
                Optional<BatchWorkDetail> batchWorkDetailOptional = batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(
                        subWorkSheet.getId(), Long.parseLong(preStepId), Constants.LONG_ZERO);
                if (!batchWorkDetailOptional.isPresent() || batchWorkDetailOptional.get().getFinish() == Constants.INT_ZERO) {
                    Optional<Step> optionalStep = stepRepository.findById(Long.parseLong(preStepId));
                    if (!optionalStep.isPresent()) {
                        return new BaseClientDTO(Constants.KO, "上一工序不存在!");
                    }
                    preStepIsFinish = false;
                    //工单请求模式时前置工序未完成则直接返回
                    if (wsStep.getRequestMode() == ConstantsEnum.WORK_SHEET_REQUEST_MODE.getCategoryName()) {
                        return new BaseClientDTO(Constants.KO, "上一工序(" + optionalStep.get().getName() + ")未完成!");
                    }
                }
            }
        }
        //验证当前下交工序配置是否与下交信息匹配
        BaseClientDTO checkStepRequestMode = checkStepRequestMode(clientSaveStepInfoDto, wsStep);
        if (Constants.KO.equals(checkStepRequestMode.getStatus())) {
            return checkStepRequestMode;
        }
        boolean isWorkSheetDeductMaterialInventory = validateIsCheckWsReceiveMaterial(workSheet);
        //检查工位上的物料库存是否足量
        if (Boolean.TRUE.equals(isWorkSheetDeductMaterialInventory) && ValidateUtils.isValid(clientSaveStepInfoDto.getMaterialBatchInfoList())) {
            BaseClientDTO baseClientDTO = this.validateMaterialInventory(workCell, workSheet, stepOptional.get(), clientSaveStepInfoDto.getNumber(), clientSaveStepInfoDto.getMaterialBatchInfoList().stream().map(MaterialBatchInfo::new).collect(Collectors.toList()));
            if (baseClientDTO.getStatus().equals(Constants.KO)) {
                return baseClientDTO;
            }
        }
        //查找当前工序生产详情
        Optional<BatchWorkDetail> batchWorkDetailOptional = batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(
                subWorkSheet.getId(), step.getId(), Constants.LONG_ZERO);
        if (batchWorkDetailOptional.isPresent() && batchWorkDetailOptional.get().getFinish() > Constants.INT_ZERO) {
            return new BaseClientDTO(Constants.KO, "当前工序已完成!");
        }
        if (ValidateUtils.isValid(clientSaveStepInfoDto.getWearingPartInfoList())) {
            //重置易损件参数
            BaseClientDTO baseClientDto = saveWearingPart(clientSaveStepInfoDto);
            if (Constants.KO.equals(baseClientDto.getStatus())) {
                return baseClientDto;
            }
        }
        //验证容器流转待下交数量合法性
        BaseClientDTO baseClientDto = checkRequestContainerInfo(clientSaveStepInfoDto);
        if (Constants.KO.equals(baseClientDto.getStatus())) {
            return baseClientDto;
        }
        //验证烘烤温循老化
        baseClientDto = null;
        if (Objects.nonNull(clientSaveStepInfoDto.getBakeHistoryInfoList())) {
            baseClientDto = bakeCycleBakeAgeingModelServices[0].validateBakeStepInfo(new BakeCycleBakeAgeingSaveDTO(clientSaveStepInfoDto).setSubWorkSheet(subWorkSheet).setWsStep(wsStep).setStep(step));
        }
        if (Objects.isNull(baseClientDto) && Objects.nonNull(clientSaveStepInfoDto.getCycleBakeHistoryInfoList())) {
            baseClientDto = bakeCycleBakeAgeingModelServices[0].validateCycleBakeStepInfo(new BakeCycleBakeAgeingSaveDTO(clientSaveStepInfoDto).setSubWorkSheet(subWorkSheet).setWsStep(wsStep).setStep(step));
        }
        if (Objects.isNull(baseClientDto) && Objects.nonNull(clientSaveStepInfoDto.getAgeingHistoryInfoList())) {
            baseClientDto = bakeCycleBakeAgeingModelServices[0].validateAgeingStepInfo(new BakeCycleBakeAgeingSaveDTO(clientSaveStepInfoDto).setSubWorkSheet(subWorkSheet).setWsStep(wsStep).setStep(step));
        }
        if (Constants.KO.equals(baseClientDto.getStatus())) {
            return baseClientDto;
        }
        // 调用可能插件中扩展的保存工序前验证接口
        IClientStepService clientStepService = BeanUtil.getHighestPrecedenceBean(IClientStepService.class);
        baseClientDto = clientStepService.validateStepSaved(clientSaveStepInfoDto);
        if (Constants.KO.equals(baseClientDto.getStatus())) {
            return baseClientDto;
        }
        BaseClientDTO baseClientDTO = clientStepService.saveStepInfo(clientSaveStepInfoDto, subWorkSheet, wsStepOptional.get(), workCell, preStepIsFinish, isLastStep);
        // 调用可能插件中扩展的保存工序后需要完成的逻辑
        clientStepService.afterStepBatchSaved(clientSaveStepInfoDto);
        return baseClientDTO;
    }

    /**
     * 保存批次工作详情
     *
     * @param batchWorkDetailSavedDTO 批次详情保存参数
     * @return BatchWorkDetail
     */
    @Override
    public BatchWorkDetail batchWorkDetailSaved(BatchWorkDetailSavedDTO batchWorkDetailSavedDTO) {
        BatchWorkDetail batchWorkDetail = batchWorkDetailSavedDTO.getBatchWorkDetail();
        WsStep wsStep = batchWorkDetailSavedDTO.getWsStep();
        SubWorkSheet subWorkSheet = batchWorkDetailSavedDTO.getSubWorkSheet();
        WorkSheet workSheet = subWorkSheet.getWorkSheet();
        if (batchWorkDetail.getInputNumber() > batchWorkDetailSavedDTO.getSubWorkSheet().getNumber()) {
            throw new NumberDeductException("工序投入数已超过工单批量");
        }
        if (batchWorkDetailSavedDTO.getPreStepIsFinish()) {
            int preStepTransferNumber = this.getStepPutInNumber(subWorkSheet, wsStep);
            if (batchWorkDetail.getInputNumber() > preStepTransferNumber) {
                throw new NumberDeductException("工序投入数已超前置工序下交数量");
            }
            // 批量工作详情完成的前提是前置工序全部完成且完成的投产数等于前置工序往下流转数
            batchWorkDetail.setFinish(batchWorkDetail.getInputNumber() == preStepTransferNumber ? Constants.INT_ONE : Constants.INT_ZERO);
        }
        //只有工序完成时才会计算往下流转数,这里需要区分工序类型
        if (batchWorkDetail.getFinish() == ConstantsEnum.TRUE_INT.getCategoryName()) {
            //普通生产工序的待流转数=其合格数
            if (wsStep.getCategory() != ConstantsEnum.CONTAINER_REQUEST_MODE.getCategoryName() && wsStep.getCategory() != ConstantsEnum.SN_REQUEST_MODE.getCategoryName()) {
                batchWorkDetail.setTransferNumber(batchWorkDetail.getQualifiedNumber());
            } else {
                int preStepTransferNumber = this.getPreStepTransferNumber(subWorkSheet, wsStep);
                //在线返修工序的向下流转数量=上工序往下流转数量+在线返修工序返修合格的数量
                if (wsStep.getCategory() == ConstantsEnum.CONTAINER_REQUEST_MODE.getCategoryName()) {
                    batchWorkDetail.setTransferNumber(batchWorkDetail.getQualifiedNumber() + preStepTransferNumber);
                } else if (wsStep.getCategory() == ConstantsEnum.SN_REQUEST_MODE.getCategoryName()) {
                    //预返修工序的向下流转数量=上工序往下的流转数量
                    batchWorkDetail.setTransferNumber(preStepTransferNumber);
                }
            }
        }
        //(容器转工单请求)工单请求模式时判断上道工序（可能不存在）是否为容器请求，如果是需要解除容器请求的绑定的容器状态
        if (wsStep.getRequestMode() == ConstantsEnum.WORK_SHEET_REQUEST_MODE.getCategoryName() && ValidateUtils.isValid(wsStep.getPreStepId())) {
            String[] preStepIds = wsStep.getPreStepId().split(Constants.STR_COMMA);
            //存在分支闭合的情况，逐一判断
            for (String preStepId : preStepIds) {
                Optional<WsStep> preWsStepOptional = wsStepRepository.findBySubWorkSheetIdAndStepIdAndDeleted(
                        subWorkSheet.getId(), Long.parseLong(preStepId), Constants.LONG_ZERO);
                if (!preWsStepOptional.isPresent()) {
                    preWsStepOptional = wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), Long.parseLong(preStepId), Constants.LONG_ZERO);
                }
                //通过前置工序生产详情获取道前置工序的容器生产详情
                if (preWsStepOptional.isPresent() && preWsStepOptional.get().getRequestMode() == ConstantsEnum.CONTAINER_REQUEST_MODE.getCategoryName()) {
                    Optional<BatchWorkDetail> preBatchWorkDetailOptional = batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), Long.parseLong(preStepId), Constants.LONG_ZERO);
                    List<ContainerDetail> preContainerDetailList = containerDetailRepository.findByBatchWorkDetailIdAndDeleted(preBatchWorkDetailOptional.orElseThrow(() -> new ResponseException("error.batchWorkDetailNotExist", "批量工序生产详情不存在")).getId(), Constants.LONG_ZERO);
                    preContainerDetailList = preContainerDetailList.stream().peek(preContainerDetail -> {
                        preContainerDetail.setUnbindTime(LocalDateTime.now())
                                .setStatus(ConstantsEnum.UNBIND.getCategoryName())
                                .setTransferNumber(Constants.INT_ZERO);
                    }).collect(Collectors.toList());
                    containerDetailRepository.saveAll(preContainerDetailList);
                }
            }
        }
        return batchWorkDetail;
    }

    /**
     * 下交接口具体保存逻辑
     *
     * @param clientSaveStepInfoDto 待保存的工序信息
     * @param subWorkSheet          子工单
     * @param wsStep                当前定制工序
     * @param workCell              工位
     * @param preStepIsFinish       前置工序是否完成
     * @param isLastStep            是否为最后一个工序
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO  结果信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseClientDTO saveStepInfo(ClientSaveStepInfoDTO clientSaveStepInfoDto, SubWorkSheet subWorkSheet, WsStep wsStep, WorkCell workCell, boolean preStepIsFinish, boolean isLastStep) {
        WorkSheet workSheet = subWorkSheet.getWorkSheet();
        Step step = wsStep.getStep();
        //保存工序批量生产详情
        BatchWorkDetail batchWorkDetail = processModeService[0].saveBatchWorkDetail(subWorkSheet, wsStep, workCell, clientSaveStepInfoDto);
        IClientStepService clientStepService = BeanUtil.getHighestPrecedenceBean(IClientStepService.class);
        batchWorkDetail = clientStepService.batchWorkDetailSaved(new BatchWorkDetailSavedDTO(clientSaveStepInfoDto, subWorkSheet, wsStep, workCell, preStepIsFinish, batchWorkDetail));

        batchWorkDetail = batchWorkDetailRepository.save(batchWorkDetail);
        //更新批次完成，修改完成数量
        if (batchWorkDetail.getFinish() == ConstantsEnum.TRUE_INT.getCategoryName()) {
            Long stepCompNumber = batchWorkDetailRepository.countBySubWorkSheetIdAndFinishAndDeleted(subWorkSheet.getId(), ConstantsEnum.FINISH_STATUS.getCategoryName(), net.airuima.constant.Constants.LONG_ZERO);
            subWorkSheet.setStepCompNumber(Objects.nonNull(stepCompNumber) ? stepCompNumber.intValue() : net.airuima.constant.Constants.INT_ZERO);
            subWorkSheetRepository.save(subWorkSheet);
        }
        //更新子工单实际开工时间
        if (subWorkSheet.getActualStartDate() == null) {
            subWorkSheet.setActualStartDate(LocalDateTime.now());
            //更新子工单状态
            if (subWorkSheet.getStatus() == Constants.CREATED) {
                subWorkSheet.setStatus(Constants.PRODUCING);
            }
            subWorkSheetRepository.save(subWorkSheet);
            if (subWorkSheet.getWorkSheet().getCategory() < Constants.INT_ZERO) {
                Optional<WsRework> wsReworkOptional = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(subWorkSheet.getWorkSheet().getId(), Constants.LONG_ZERO);
                wsReworkOptional.ifPresent(wsRework -> snReworkRepository.updateStatusByWsReworkId(Constants.INT_ONE, wsRework.getId(), Constants.LONG_ZERO));
            }
        }
        //更新总工单实际开工时间
        if (workSheet.getActualStartDate() == null) {
            workSheet.setActualStartDate(LocalDateTime.now());
            //更新总工单状态
            if (workSheet.getStatus() == Constants.CREATED) {
                workSheet.setStatus(Constants.PRODUCING);
            }
            workSheetRepository.save(workSheet);
        }
        ContainerDetail containerDetail = null;
        //如果请求的容器列表或者新绑定的容器ID不为空时需要保存容器详情
        if (ValidateUtils.isValid(clientSaveStepInfoDto.getRequestContainerInfos()) || null != clientSaveStepInfoDto.getBindContainerId()) {
            containerDetail = processModeService[0].saveContainerInfo(clientSaveStepInfoDto, batchWorkDetail, wsStep, isLastStep);
        }
        List<SnWorkDetail> snWorkDetailList = null;
        //保存SN工作详情信息
        if (ValidateUtils.isValid(clientSaveStepInfoDto.getSnInfoList())) {
            snWorkDetailList = processModeService[0].saveSnInfo(clientSaveStepInfoDto, subWorkSheet, step, workCell, containerDetail, isLastStep);
        }
        //保存工序不良现象
        if (ValidateUtils.isValid(clientSaveStepInfoDto.getUnqualifiedItemInfoList())) {
            this.saveWorkDetailUnqualifiedItem(batchWorkDetail, containerDetail, subWorkSheet, clientSaveStepInfoDto);
        }
        //保存物料批次信息
        if (ValidateUtils.isValid(clientSaveStepInfoDto.getMaterialBatchInfoList())) {
            this.saveWorkDetailMaterialBatch(subWorkSheet, step, batchWorkDetail, containerDetail, snWorkDetailList, clientSaveStepInfoDto);
        }
        //保存设备信息
        if (ValidateUtils.isValid(clientSaveStepInfoDto.getEquipmentInfoList())) {
            this.saveWorkDetailEquipment(batchWorkDetail, containerDetail, snWorkDetailList, clientSaveStepInfoDto, workCell);
        }
        //保存易损件生产追溯历史信息
        if (ValidateUtils.isValid(clientSaveStepInfoDto.getWearingPartInfoList())) {
            this.saveWorkDetailWearingPart(batchWorkDetail, containerDetail, snWorkDetailList, clientSaveStepInfoDto);
        }
        //对于需要核验领料的工单进行工位库存扣数, 同时防止工序不核料，但配置了上料规则
        if (Boolean.TRUE.equals(this.validateIsCheckWsReceiveMaterial(workSheet)) && ValidateUtils.isValid(clientSaveStepInfoDto.getMaterialBatchInfoList())) {
            this.subtractOnlineInventory(step, workSheet, workCell, clientSaveStepInfoDto.getMaterialBatchInfoList(), clientSaveStepInfoDto.getNumber());
        }
        //判定是否需要进行预警
        if (ValidateUtils.isValid(clientSaveStepInfoDto.getUnqualifiedItemInfoList())) {
            this.determineCreateUnqualifiedEvent(batchWorkDetail, clientSaveStepInfoDto);
        }
        //工序全部完成判断是否需要更新子工单和总工单的完成数据
        if (isLastStep && batchWorkDetail.getFinish() == Constants.INT_ONE) {
            this.finishSubWorkSheet(batchWorkDetail, subWorkSheet);
        }
        //FQC处理
        if (clientSaveStepInfoDto.getFqcDealResult() != null) {
            this.fqcDeal(clientSaveStepInfoDto, subWorkSheet, workCell, step);
        }
        //抽检处理
        if (null != clientSaveStepInfoDto.getRandomInspectSaveInfo()) {
            this.saveRandomInspectInfo(clientSaveStepInfoDto, subWorkSheet, workCell, step);
        }
        //保存员工产量报表数据
        this.saveStaffPerformReport(batchWorkDetail, containerDetail, clientSaveStepInfoDto);
        if (StringUtils.isNotBlank(clientSaveStepInfoDto.getXsrfToken())) {
            if (null == redisUtils.get(clientSaveStepInfoDto.getXsrfToken())) {
                redisUtils.set(clientSaveStepInfoDto.getXsrfToken(), clientSaveStepInfoDto.getXsrfToken(), 60);
            } else {
                throw new SaveRepeatException();
            }
        }
        BaseClientDTO baseClientDTO = new BaseClientDTO(Constants.OK);
        //保存烘烤温循老化历史数据
        if (Objects.nonNull(clientSaveStepInfoDto.getBakeHistoryInfoList())) {
            baseClientDTO = bakeCycleBakeAgeingModelServices[0].saveBakeHistoryInfo(new BakeCycleBakeAgeingSaveDTO(clientSaveStepInfoDto).setSubWorkSheet(subWorkSheet).setWsStep(wsStep).setStep(step));
        }
        if (Objects.nonNull(clientSaveStepInfoDto.getCycleBakeHistoryInfoList())) {
            baseClientDTO = bakeCycleBakeAgeingModelServices[0].saveCycleBakeHistoryInfo(new BakeCycleBakeAgeingSaveDTO(clientSaveStepInfoDto).setSubWorkSheet(subWorkSheet).setWsStep(wsStep).setStep(step));
        }
        if (Objects.nonNull(clientSaveStepInfoDto.getAgeingHistoryInfoList())) {
            baseClientDTO = bakeCycleBakeAgeingModelServices[0].saveAgeingHistoryInfo(new BakeCycleBakeAgeingSaveDTO(clientSaveStepInfoDto).setSubWorkSheet(subWorkSheet).setWsStep(wsStep).setStep(step));
        }
        return baseClientDTO;
    }

    /**
     * 保存批量及容器详情的不良信息
     *
     * @param batchWorkDetail       批量详情
     * @param containerDetail       容器详情
     * @param subWorkSheet          子工单
     * @param clientSaveStepInfoDto 不良参数
     * @return void
     * <AUTHOR>
     * @date 2021-01-19
     **/
    public void saveWorkDetailUnqualifiedItem(BatchWorkDetail batchWorkDetail, ContainerDetail containerDetail, SubWorkSheet subWorkSheet, ClientSaveStepInfoDTO clientSaveStepInfoDto) {
        if (ValidateUtils.isValid(clientSaveStepInfoDto.getUnqualifiedItemInfoList())) {
            //保存批量详情的不良信息
            if (null != batchWorkDetail) {
                List<WsStepUnqualifiedItem> wsStepUnqualifiedItemList = wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), batchWorkDetail.getStep().getId(), Constants.LONG_ZERO);
                clientSaveStepInfoDto.getUnqualifiedItemInfoList().forEach(unqualifiedItemInfo -> {
                    WsStepUnqualifiedItem wsStepUnqualifiedItem = wsStepUnqualifiedItemList.stream().filter(wsStepUnqualifiedItemTemp -> wsStepUnqualifiedItemTemp.getUnqualifiedItem().getId()
                            .equals(unqualifiedItemInfo.getUnqualifiedItemId())).findAny().orElseGet(WsStepUnqualifiedItem::new);
                    wsStepUnqualifiedItem.setStep(batchWorkDetail.getStep())
                            .setSubWorkSheet(subWorkSheet)
                            .setOperatorId(batchWorkDetail.getOperatorId())
                            .setUnqualifiedItem(unqualifiedItemRepository.getReferenceById(unqualifiedItemInfo.getUnqualifiedItemId()))
                            .setNumber(wsStepUnqualifiedItem.getNumber() + unqualifiedItemInfo.getNumber())
                            .setRecordDate(LocalDate.now())
                            .setFlag(false);
                    wsStepUnqualifiedItemRepository.save(wsStepUnqualifiedItem);
                });
            }
            //保存容器详情的不良信息
            if (null != containerDetail) {
                List<ContainerDetailUnqualifiedItem> containerDetailUnqualifiedItemList = containerDetailUnqualifiedItemRepository.findByContainerDetailIdAndDeleted(containerDetail.getId(), Constants.LONG_ZERO);
                clientSaveStepInfoDto.getUnqualifiedItemInfoList().forEach(unqualifiedItemInfo -> {
                    ContainerDetailUnqualifiedItem containerDetailUnqualifiedItem = containerDetailUnqualifiedItemList.stream().filter(containerDetailUnqualifiedItemTemp -> containerDetailUnqualifiedItemTemp.getUnqualifiedItem().getId()
                            .equals(unqualifiedItemInfo.getUnqualifiedItemId())).findAny().orElseGet(ContainerDetailUnqualifiedItem::new);
                    containerDetailUnqualifiedItem.setContainerDetail(containerDetail)
                            .setUnqualifiedItem(unqualifiedItemRepository.getReferenceById(unqualifiedItemInfo.getUnqualifiedItemId()))
                            .setNumber(containerDetailUnqualifiedItem.getNumber() + unqualifiedItemInfo.getNumber());
                    containerDetailUnqualifiedItemRepository.save(containerDetailUnqualifiedItem);
                });
            }
        }
    }

    /**
     * 保存批量工作详情、容器工作详情及单支工作详情的
     *
     * @param batchWorkDetail
     * @param containerDetail
     * @param snWorkDetailList
     * @param clientSaveStepInfoDto
     * @return void
     * <AUTHOR>
     * @date 2021-01-19
     **/
    public void saveWorkDetailMaterialBatch(SubWorkSheet subWorkSheet, Step step, BatchWorkDetail batchWorkDetail, ContainerDetail containerDetail, List<SnWorkDetail> snWorkDetailList, ClientSaveStepInfoDTO clientSaveStepInfoDto) {
        //工序生产过程物料库存管控级别(0:不管控物料库存;1:总工单物料库存;2:工位物料库存)
        int materialControlLevel = commonService.getMaterialControlLevel();
        boolean isWorkSheetDeductMaterialInventory = validateIsCheckWsReceiveMaterial(subWorkSheet.getWorkSheet());
        //获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = commonService.findSnapshotWorkFlow(subWorkSheet.getWorkSheet(), subWorkSheet, step);
        //判断当前工序是否上料
        List<PedigreeStepMaterialRule> pedigreeStepMaterialRuleList = commonService.findPedigreeStepMaterialRule(subWorkSheet.getWorkSheet().getClientId(), subWorkSheet.getWorkSheet().getPedigree().getId(), snapshotWorkFlow.getId(), step.getId()).stream().filter(PedigreeStepMaterialRule::getIsDeduct).collect(Collectors.toList());
        //是否扣料 不管控物料库存并却上料规则中没配置则不记录物料数
        Boolean isBuckleMaterial = materialControlLevel == ConstantsEnum.MATERIAL_NOT_CONTROL_LEVEL.getCategoryName() || !ValidateUtils.isValid(pedigreeStepMaterialRuleList) || !isWorkSheetDeductMaterialInventory;
        if (ValidateUtils.isValid(clientSaveStepInfoDto.getMaterialBatchInfoList())) {
            //保存批量工作详情的物料批次信息
            if (null != batchWorkDetail) {
                clientSaveStepInfoDto.getMaterialBatchInfoList().forEach(materialBatchInfo -> {
                    Optional<BatchWorkDetailMaterialBatch> batchWorkDetailMaterialBatchOptional = Optional.empty();
                    if (materialBatchInfo.getMaterialId() != null && StringUtils.isNotBlank(materialBatchInfo.getMaterialBatch())) {
                        batchWorkDetailMaterialBatchOptional = batchWorkDetailMaterialBatchRepository.findByBatchWorkDetailIdAndMaterialIdAndMaterialBatchAndDeleted(batchWorkDetail.getId(), materialBatchInfo.getMaterialId(), materialBatchInfo.getMaterialBatch(), Constants.LONG_ZERO);
                    }
                    if (!batchWorkDetailMaterialBatchOptional.isPresent() && materialBatchInfo.getMaterialId() != null && StringUtils.isBlank(materialBatchInfo.getMaterialBatch())) {
                        batchWorkDetailMaterialBatchOptional = batchWorkDetailMaterialBatchRepository.findByBatchWorkDetailIdAndMaterialIdAndDeleted(batchWorkDetail.getId(), materialBatchInfo.getMaterialId(), Constants.LONG_ZERO);
                    }
                    if (!batchWorkDetailMaterialBatchOptional.isPresent() && materialBatchInfo.getMaterialId() == null && StringUtils.isNotBlank(materialBatchInfo.getMaterialBatch())) {
                        batchWorkDetailMaterialBatchOptional = batchWorkDetailMaterialBatchRepository.findByBatchWorkDetailIdAndMaterialBatchAndDeleted(batchWorkDetail.getId(), materialBatchInfo.getMaterialBatch(), Constants.LONG_ZERO);
                    }
                    if (!batchWorkDetailMaterialBatchOptional.isPresent()) {
                        BatchWorkDetailMaterialBatch batchWorkDetailMaterialBatch = new BatchWorkDetailMaterialBatch();
                        batchWorkDetailMaterialBatch.setBatchWorkDetail(batchWorkDetail)
                                .setMaterialId(materialBatchInfo.getMaterialId())
                                .setMaterialBatch(materialBatchInfo.getMaterialBatch())
                                .setSerial(materialBatchInfo.getSerial())
                                .setNumber(materialBatchInfo.getNumber())
                                .setType(isBuckleMaterial ? ConstantsEnum.MATERIAL_NOT_CONTROL_LEVEL.getCategoryName() : materialControlLevel)
                                .setSupplierId(ValidateUtils.isValid(materialBatchInfo.getSupplierCode()) ? rbaseSupplierProxy.findByCodeAndDeleted(materialBatchInfo.getSupplierCode(), Constants.LONG_ZERO).getId() : null);
                        batchWorkDetailMaterialBatchRepository.save(batchWorkDetailMaterialBatch);
                    } else {
                        //存在同一个工作详情下出现多次分批次下交，需要累加对应的物料使用数量
                        BatchWorkDetailMaterialBatch batchWorkDetailMaterialBatch = batchWorkDetailMaterialBatchOptional.get();
                        batchWorkDetailMaterialBatch.setNumber(NumberUtils.add(batchWorkDetailMaterialBatch.getNumber(), materialBatchInfo.getNumber()).doubleValue());
                        batchWorkDetailMaterialBatchRepository.save(batchWorkDetailMaterialBatch);
                    }
                });
            }
            //保存容器详情的物料批次信息
            if (null != containerDetail) {
                clientSaveStepInfoDto.getMaterialBatchInfoList().forEach(materialBatchInfo -> {
                    Optional<ContainerDetailMaterialBatch> containerDetailMaterialBatchOptional = Optional.empty();
                    if (materialBatchInfo.getMaterialId() != null && StringUtils.isNotBlank(materialBatchInfo.getMaterialBatch())) {
                        containerDetailMaterialBatchOptional = containerDetailMaterialBatchRepository.findByContainerDetailIdAndMaterialIdAndBatchAndDeleted(containerDetail.getId(), materialBatchInfo.getMaterialId(), materialBatchInfo.getMaterialBatch(), Constants.LONG_ZERO);
                    }
                    if (!containerDetailMaterialBatchOptional.isPresent() && materialBatchInfo.getMaterialId() != null && StringUtils.isBlank(materialBatchInfo.getMaterialBatch())) {
                        containerDetailMaterialBatchOptional = containerDetailMaterialBatchRepository.findByContainerDetailIdAndMaterialIdAndDeleted(containerDetail.getId(), materialBatchInfo.getMaterialId(), Constants.LONG_ZERO);
                    }
                    if (!containerDetailMaterialBatchOptional.isPresent() && materialBatchInfo.getMaterialId() == null && StringUtils.isNotBlank(materialBatchInfo.getMaterialBatch())) {
                        containerDetailMaterialBatchOptional = containerDetailMaterialBatchRepository.findByContainerDetailIdAndBatchAndDeleted(containerDetail.getId(), materialBatchInfo.getMaterialBatch(), Constants.LONG_ZERO);
                    }
                    if (!containerDetailMaterialBatchOptional.isPresent()) {
                        ContainerDetailMaterialBatch containerDetailMaterialBatch = new ContainerDetailMaterialBatch();
                        containerDetailMaterialBatch.setContainerDetail(containerDetail)
                                .setMaterialId(materialBatchInfo.getMaterialId())
                                .setBatch(materialBatchInfo.getMaterialBatch())
                                .setNumber(materialBatchInfo.getNumber())
                                .setType(isBuckleMaterial ? ConstantsEnum.MATERIAL_NOT_CONTROL_LEVEL.getCategoryName() : materialControlLevel)
                                .setSupplierId(ValidateUtils.isValid(materialBatchInfo.getSupplierCode()) ? rbaseSupplierProxy.findByCodeAndDeleted(materialBatchInfo.getSupplierCode(), Constants.LONG_ZERO).getId() : null);
                        containerDetailMaterialBatchRepository.save(containerDetailMaterialBatch);
                    }
                });
            }
            //保存SN工作详情的物料批次信息
            if (ValidateUtils.isValid(snWorkDetailList)) {
                clientSaveStepInfoDto.getMaterialBatchInfoList().forEach(materialBatchInfo -> {
                    snWorkDetailList.forEach(snWorkDetail -> {
                        SnWorkDetailMaterialBatch snWorkDetailMaterialBatch = new SnWorkDetailMaterialBatch();
                        snWorkDetailMaterialBatch.setMaterialBatch(materialBatchInfo.getMaterialBatch())
                                .setMaterialId(materialBatchInfo.getMaterialId())
                                .setSnWorkDetail(snWorkDetail)
                                .setNumber(materialBatchInfo.getNumber())
                                .setType(isBuckleMaterial ? ConstantsEnum.MATERIAL_NOT_CONTROL_LEVEL.getCategoryName() : materialControlLevel)
                                .setSupplierId(ValidateUtils.isValid(materialBatchInfo.getSupplierCode()) ? rbaseSupplierProxy.findByCodeAndDeleted(materialBatchInfo.getSupplierCode(), Constants.LONG_ZERO).getId() : null);
                        snWorkDetailMaterialBatchRepository.save(snWorkDetailMaterialBatch);
                    });
                });
            }
        }
    }

    /**
     * 保存工单详情、容器详情、SN详情的设备信息
     *
     * @param batchWorkDetail       工单详情
     * @param containerDetail       容器详情
     * @param snWorkDetailList      SN详情列表
     * @param clientSaveStepInfoDto 设备参数
     * @return void
     * <AUTHOR>
     * @date 2021-01-19
     **/
    public void saveWorkDetailEquipment(BatchWorkDetail batchWorkDetail, ContainerDetail containerDetail, List<SnWorkDetail> snWorkDetailList, ClientSaveStepInfoDTO clientSaveStepInfoDto, WorkCell workCell) {
        if (ValidateUtils.isValid(clientSaveStepInfoDto.getEquipmentInfoList())) {
            //保存工单详情的设备信息
            if (null != batchWorkDetail) {
                clientSaveStepInfoDto.getEquipmentInfoList().forEach(equipmentInfo -> {
                    BatchWorkDetailFacility batchWorkDetailFacility = batchWorkDetailFacilityRepository.findByBatchWorkDetailIdAndFacilityIdAndDeleted(batchWorkDetail.getId(), equipmentInfo.getEquipmentId(), Constants.LONG_ZERO).orElseGet(BatchWorkDetailFacility::new);
                    if (null == batchWorkDetailFacility.getId()) {
                        batchWorkDetailFacility.setFacilityId(equipmentInfo.getEquipmentId());
                        batchWorkDetailFacility.setBatchWorkDetail(batchWorkDetail);
                        batchWorkDetailFacilityRepository.save(batchWorkDetailFacility);
                    }
                });
            }
            //保存容器详情的设备信息
            if (null != containerDetail) {
                clientSaveStepInfoDto.getEquipmentInfoList().forEach(equipmentInfo -> {
                    ContainerDetailFacility containerDetailFacility = containerDetailFacilityRepository.findByContainerDetailIdAndFacilityIdAndDeleted(containerDetail.getId(), equipmentInfo.getEquipmentId(), Constants.LONG_ZERO).orElseGet(ContainerDetailFacility::new);
                    if (null == containerDetailFacility.getId()) {
                        containerDetailFacility.setContainerDetail(containerDetail);
                        containerDetailFacility.setFacilityId(equipmentInfo.getEquipmentId());
                        containerDetailFacilityRepository.save(containerDetailFacility);
                    }
                });
            }
            //保存SN工作详情设备信息
            if (ValidateUtils.isValid(snWorkDetailList)) {
                clientSaveStepInfoDto.getEquipmentInfoList().forEach(equipmentInfo -> {
                    snWorkDetailList.forEach(snWorkDetail -> {
                        SnWorkDetailFacility snWorkDetailFacility = new SnWorkDetailFacility();
                        snWorkDetailFacility.setSnWorkDetail(snWorkDetail)
                                .setFacilityId(equipmentInfo.getEquipmentId());
                        snWorkDetailFacilityRepository.save(snWorkDetailFacility);
                    });
                });
            }
        }
    }

    /**
     * 保存工单详情、容器详情、SN详情的设备信息
     *
     * @param batchWorkDetail       工单详情
     * @param containerDetail       容器详情
     * @param snWorkDetailList      SN详情列表
     * @param clientSaveStepInfoDto 易损件参数
     * <AUTHOR>
     * @date 2021-06-23
     */
    public void saveWorkDetailWearingPart(BatchWorkDetail batchWorkDetail, ContainerDetail containerDetail, List<SnWorkDetail> snWorkDetailList, ClientSaveStepInfoDTO clientSaveStepInfoDto) {
        if (ValidateUtils.isValid(clientSaveStepInfoDto.getWearingPartInfoList())) {
            //保存工单详情的易损件信息
            if (null != batchWorkDetail) {
                clientSaveStepInfoDto.getWearingPartInfoList().forEach(wearingPartInfo -> {
                    BatchWorkDetailWearingPart batchWorkDetailWearingPart = batchWorkDetailWearingPartRepository.findByBatchWorkDetailIdAndWearingPartIdAndDeleted(batchWorkDetail.getId(), wearingPartInfo.getWearingPartId(), Constants.LONG_ZERO).orElseGet(BatchWorkDetailWearingPart::new);
                    if (null == batchWorkDetailWearingPart.getId()) {
                        batchWorkDetailWearingPart.setWearingPart(new WearingPart(wearingPartInfo.getWearingPartId()));
                        batchWorkDetailWearingPart.setBatchWorkDetail(batchWorkDetail);
                        batchWorkDetailWearingPartRepository.save(batchWorkDetailWearingPart);
                    }
                });
            }
            //保存容器详情的易损件信息
            if (null != containerDetail) {
                clientSaveStepInfoDto.getWearingPartInfoList().forEach(wearingPartInfo -> {
                    ContainerDetailWearingPart containerDetailWearingPart = containerDetailWearingPartRepository.findByContainerDetailIdAndWearingPartIdAndDeleted(containerDetail.getId(), wearingPartInfo.getWearingPartId(), Constants.LONG_ZERO).orElseGet(ContainerDetailWearingPart::new);
                    if (null == containerDetailWearingPart.getId()) {
                        containerDetailWearingPart.setWearingPart(new WearingPart(wearingPartInfo.getWearingPartId()));
                        containerDetailWearingPart.setContainerDetail(containerDetail);
                        containerDetailWearingPartRepository.save(containerDetailWearingPart);
                    }
                });
            }
            //保存SN工作详情易损件信息
            if (ValidateUtils.isValid(snWorkDetailList)) {
                clientSaveStepInfoDto.getWearingPartInfoList().forEach(wearingPartInfo -> {
                    snWorkDetailList.forEach(snWorkDetail -> {
                        SnWorkDetailWearingPart snWorkDetailWearingPart = snWorkDetailWearingPartRepository.findBySnWorkDetailIdAndWearingPartIdAndDeleted(snWorkDetail.getId(), wearingPartInfo.getWearingPartId(), Constants.LONG_ZERO).orElseGet(SnWorkDetailWearingPart::new);
                        if (null != snWorkDetailWearingPart) {
                            snWorkDetailWearingPart.setWearingPart(new WearingPart(wearingPartInfo.getWearingPartId()));
                            snWorkDetailWearingPart.setSnWorkDetail(snWorkDetail);
                            snWorkDetailWearingPartRepository.save(snWorkDetailWearingPart);
                        }
                    });
                });
            }
        }
    }


    /**
     * 验证工单是否需要领料(需要领料则需要验证工单核料以及工位上料扣料等)
     *
     * @param workSheet 总工单
     * @return Boolean
     * <AUTHOR>
     * @date 2021-01-18
     **/
    @Transactional(readOnly = true)
    public Boolean validateIsCheckWsReceiveMaterial(WorkSheet workSheet) {
        //验证总工单是否需要检查领料
        boolean isCheckReceiveMaterial = false;
        //1,优先获取数据字典配置的工单类型是否需要领料检查
        String result = commonService.getDictionaryData(Constants.KEY_CHECK_RECEIVE_MATERIAL);
        if (StringUtils.isNotBlank(result)) {
            List<Map<String, String>> checkReceiveMaterialConfigs = JSON.parseObject(result, new TypeReference<List<Map<String, String>>>() {
            });
            isCheckReceiveMaterial = (checkReceiveMaterialConfigs.stream().anyMatch(config -> config.get("key").equals(String.valueOf(workSheet.getCategory())) && Boolean.parseBoolean(config.get("value"))));
            if (!isCheckReceiveMaterial) {
                return Constants.FALSE;
            }
        }
        //2.其次获取产品谱系配置的是否需要领料检查
        PedigreeConfig pedigreeConfig = commonService.findPedigreeConfig(workSheet.getPedigree());
        isCheckReceiveMaterial = null != pedigreeConfig ? pedigreeConfig.getIsCheckReceiveMaterial() : isCheckReceiveMaterial;
        return isCheckReceiveMaterial;
    }

    /**
     * 根据员工扫描的物料批次以及上料规则扣减工单或者工位在线物料库存
     *
     * @param workSheet             总工单
     * @param step                  当前投产工序
     * @param materialBatchInfoList 员工扫描的物料批次
     * @param finishNumber          当前完成数量
     * <AUTHOR>
     * @date 2021-03-15
     **/
    public void subtractOnlineInventory(Step step, WorkSheet workSheet, WorkCell workCell, List<ClientSaveStepInfoDTO.MaterialBatchInfo> materialBatchInfoList, int finishNumber) {
        // 获取工单地投料单
        List<WsMaterial> wsMaterialList = wsMaterialRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(wsMaterialList)) {
            return;
        }
        //工序生产过程物料库存管控级别(0:不管控物料库存;1:总工单物料库存;2:工位物料库存)
        int materialControlLevel = commonService.getMaterialControlLevel();
        //获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = commonService.findSnapshotWorkFlow(workSheet, null, step);
        //获取当前工序需要扣料的配置信息
        List<PedigreeStepMaterialRule> pedigreeStepMaterialRuleList = commonService.findPedigreeStepMaterialRule(workSheet.getClientId(), workSheet.getPedigree().getId(), snapshotWorkFlow.getId(), step.getId()).stream().filter(PedigreeStepMaterialRule::getIsDeduct).collect(Collectors.toList());
        if (!ValidateUtils.isValid(pedigreeStepMaterialRuleList)) {
            return;
        }
        //返修单核原始物料
        if (workSheet.getCategory() == WsEnum.ONLINE_RE_WS.getCategory()) {
            WsRework wsRework = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO).orElse(null);
            if (null != wsRework) {
                workSheet = wsRework.getOriginalWorkSheet();
            }
        }
        WorkSheet finalWorkSheet = workSheet;
        materialBatchInfoList.forEach(materialBatchInfo -> {
            // 获取上料规则
            PedigreeStepMaterialRule pedigreeStepMaterialRule = pedigreeStepMaterialRuleList.stream().filter(materialRule -> {
                Optional<WsMaterial> wsMaterialOptional = wsMaterialList.stream().filter(wsMaterial -> Objects.equals(wsMaterial.getMaterialId(), materialBatchInfo.getMaterialId())).findFirst();
                return wsMaterialOptional.filter(wsMaterial -> Objects.equals(materialRule.getMaterialId(), wsMaterial.getOriginMaterialId())).isPresent();
            }).findFirst().orElse(null);
            if (pedigreeStepMaterialRule == null) {
                return;
            }
            AtomicReference<Double> reduceNumber = new AtomicReference<>(NumberUtils.multiply(finishNumber, pedigreeStepMaterialRule.getProportion()).doubleValue());
            if (pedigreeStepMaterialRule.getIsCheckMaterialBatch()) {
                //根据物料批次严格扣除库存
                this.subtractMaterialBatchOnlineInventory(workCell, finalWorkSheet, materialBatchInfo, materialControlLevel);
                return;
            }
            wsMaterialList.stream().filter(wsMaterial -> Objects.equals(wsMaterial.getMaterialId(), pedigreeStepMaterialRule.getMaterialId())).findFirst().ifPresent(wsMaterial -> {
                // 获取包括替换料在内的投料单信息
                List<WsMaterial> wsMaterials = wsMaterialList.stream().filter(originWsMaterial -> Objects.equals(wsMaterial.getOriginMaterialId(), originWsMaterial.getOriginMaterialId())).collect(Collectors.toList());
                //根据物料按从大到小扣除
                this.subtractMaterialOnlineInventory(workCell, finalWorkSheet, wsMaterials, materialBatchInfo, reduceNumber, materialControlLevel);
            });
        });
    }

    /**
     * 根据批次扣减严格扣减物料
     *
     * @param workCell             工位
     * @param workSheet            工单
     * @param materialBatchInfo    上料信息
     * @param materialControlLevel 管控级别
     * <AUTHOR>
     * @date 2022/9/5
     */
    public void subtractMaterialBatchOnlineInventory(WorkCell workCell, WorkSheet workSheet, ClientSaveStepInfoDTO.MaterialBatchInfo materialBatchInfo, int materialControlLevel) {
        //工单库存（根据批次）
        if (materialControlLevel == ConstantsEnum.MATERIAL_WORKSHEET_CONTROL_LEVEL.getCategoryName()) {
            wsMaterialBatchRepository.findByWorkSheetIdAndMaterialIdAndBatchAndDeleted(workSheet.getId(), materialBatchInfo.getMaterialId(), materialBatchInfo.getMaterialBatch(), Constants.LONG_ZERO).ifPresent(wsMaterialBatch -> {
                if (wsMaterialBatch.getLeftNumber() >= materialBatchInfo.getNumber()) {
                    wsMaterialBatchRepository.save(wsMaterialBatch.setLeftNumber(wsMaterialBatch.getLeftNumber() - materialBatchInfo.getNumber()));
                }
            });
        }
        //工单工位库存（根据批次）
        if (materialControlLevel == ConstantsEnum.MATERIAL_WORK_CELL_CONTROL_LEVEL.getCategoryName()) {
            wsWorkCellMaterialBatchRepository.findByWorkSheetIdAndWorkCellIdAndMaterialIdAndBatchAndDeleted(workSheet.getId(), workCell.getId(), materialBatchInfo.getMaterialId(), materialBatchInfo.getMaterialBatch(), Constants.LONG_ZERO).ifPresent(wsWorkCellMaterialBatch -> {
                if (wsWorkCellMaterialBatch.getLeftNumber() >= materialBatchInfo.getNumber()) {
                    wsWorkCellMaterialBatchRepository.save(wsWorkCellMaterialBatch.setLeftNumber(wsWorkCellMaterialBatch.getLeftNumber() - materialBatchInfo.getNumber()));
                }
            });
        }
    }

    /**
     * 不根据批次扣减（直接按大到小依次扣减）
     *
     * @param workCell             工位
     * @param workSheet            工单
     * @param wsMaterialList       工单投料单
     * @param materialBatchInfo    上料信息
     * @param reduceNumber         投产数
     * @param materialControlLevel 管控层级(扣料)
     * <AUTHOR>
     * @date 2022/9/5
     */
    public void subtractMaterialOnlineInventory(WorkCell workCell, WorkSheet workSheet, List<WsMaterial> wsMaterialList, ClientSaveStepInfoDTO.MaterialBatchInfo materialBatchInfo, AtomicReference<Double> reduceNumber, int materialControlLevel) {
        //工单库存
        if (materialControlLevel == ConstantsEnum.MATERIAL_WORKSHEET_CONTROL_LEVEL.getCategoryName()) {
            List<WsMaterialBatch> wsMaterialBatchList = wsMaterialBatchRepository.findByWorkSheetIdAndMaterialIdInAndLeftNumberIsGreaterThanAndDeleted(workSheet.getId(), wsMaterialList.stream().map(WsMaterial::getMaterialId).collect(Collectors.toList()), Constants.DOUBLE_ZERRO, Constants.LONG_ZERO);
            if (ValidateUtils.isValid(wsMaterialBatchList)) {
                List<WsMaterialBatch> sortedWsMaterialBatchList = wsMaterialBatchList.stream().sorted(Comparator.comparing(WsMaterialBatch::getLeftNumber)).collect(Collectors.toList());
                // 获取扫描的批次与工单批次库存匹配的集合数据,扣减员工扫描的物料批次的剩余库存
                handleSortedWsMateriaBatchList(materialBatchInfo, reduceNumber, sortedWsMaterialBatchList);
            }
        }

        subtractWorkCellMaterialOnlineInventory(workCell, workSheet, wsMaterialList, reduceNumber, materialControlLevel);
    }

    /**
     * 获取扫描的批次与工单批次库存匹配的集合数据,扣减员工扫描的物料批次的剩余库存
     *
     * @param materialBatchInfo         上料信息
     * @param reduceNumber              投产数
     * @param sortedWsMaterialBatchList 工单领料明细列表
     */
    private void handleSortedWsMateriaBatchList(ClientSaveStepInfoDTO.MaterialBatchInfo materialBatchInfo, AtomicReference<Double> reduceNumber, List<WsMaterialBatch> sortedWsMaterialBatchList) {
        sortedWsMaterialBatchList.forEach(wsMaterialBatch -> {
            if (reduceNumber.get() == Constants.DOUBLE_ZERRO) {
                return;
            }
            if (materialBatchInfo.getMaterialId().equals(wsMaterialBatch.getMaterialId()) && materialBatchInfo.getMaterialBatch().equals(wsMaterialBatch.getBatch())) {
                if (wsMaterialBatch.getLeftNumber() > reduceNumber.get()) {
                    wsMaterialBatch.setLeftNumber(NumberUtils.subtract(wsMaterialBatch.getLeftNumber(), reduceNumber.get()).doubleValue());
                    reduceNumber.set(Constants.DOUBLE_ZERRO);
                } else {
                    reduceNumber.set(NumberUtils.subtract(reduceNumber.get(), wsMaterialBatch.getLeftNumber()).doubleValue());
                    wsMaterialBatch.setLeftNumber(Constants.DOUBLE_ZERRO);
                }
                wsMaterialBatchRepository.save(wsMaterialBatch);
            }
        });
    }

    /**
     * 不根据批次扣减（直接按大到小依次扣减）
     *
     * @param workCell             工位
     * @param workSheet            工单
     * @param wsMaterialList       工单投料单
     * @param reduceNumber         投产数
     * @param materialControlLevel 管控层级(扣料)
     */
    private void subtractWorkCellMaterialOnlineInventory(WorkCell workCell, WorkSheet workSheet, List<WsMaterial> wsMaterialList, AtomicReference<Double> reduceNumber, int materialControlLevel) {
        //工单工位库存
        if (materialControlLevel == ConstantsEnum.MATERIAL_WORK_CELL_CONTROL_LEVEL.getCategoryName()) {
            List<WsWorkCellMaterialBatch> wsWorkCellMaterialBatchList = wsWorkCellMaterialBatchRepository.findByWorkSheetIdAndWorkCellIdAndMaterialIdInAndLeftNumberGreaterThanAndDeleted(workSheet.getId(), workCell.getId(),
                    wsMaterialList.stream().map(WsMaterial::getMaterialId).collect(Collectors.toList()), Constants.DOUBLE_ZERRO, Constants.LONG_ZERO);
            if (ValidateUtils.isValid(wsWorkCellMaterialBatchList)) {
                //将库存数据按照从小到大的顺序排序
                List<WsWorkCellMaterialBatch> sortedWsWorkCellMaterialBatchList = wsWorkCellMaterialBatchList.stream().sorted(Comparator.comparing(WsWorkCellMaterialBatch::getLeftNumber)).collect(Collectors.toList());
                sortedWsWorkCellMaterialBatchList.forEach(wsWorkCellMaterialBatch -> {
                    if (wsWorkCellMaterialBatch.getLeftNumber() > reduceNumber.get()) {
                        wsWorkCellMaterialBatch.setLeftNumber(NumberUtils.subtract(wsWorkCellMaterialBatch.getLeftNumber(), reduceNumber.get()).doubleValue());
                        reduceNumber.set(Constants.DOUBLE_ZERRO);
                    } else {
                        reduceNumber.set(NumberUtils.subtract(reduceNumber.get(), wsWorkCellMaterialBatch.getLeftNumber()).doubleValue());
                        wsWorkCellMaterialBatch.setLeftNumber(Constants.DOUBLE_ZERRO);
                    }
                    wsWorkCellMaterialBatchRepository.save(wsWorkCellMaterialBatch);
                });
            }
        }
    }

    /**
     * 判定是否不良预警或者工序良率预警
     *
     * @param batchWorkDetail       工单详情
     * @param clientSaveStepInfoDto 不良参数
     * @return void
     * <AUTHOR>
     * @date 2021-01-20
     **/
    public void determineCreateUnqualifiedEvent(BatchWorkDetail batchWorkDetail, ClientSaveStepInfoDTO clientSaveStepInfoDto) {
        if (ValidateUtils.isValid(clientSaveStepInfoDto.getUnqualifiedItemInfoList())) {
            List<UnqualifiedEvent> unqualifiedEventList = unqualifiedEventRepository.findBySubWorkSheetIdAndStepIdAndDeleted(batchWorkDetail.getSubWorkSheet().getId(), batchWorkDetail.getStep().getId(), Constants.LONG_ZERO);
            //每个子工单同一个工序只预警一次
            if (!ValidateUtils.isValid(unqualifiedEventList)) {
                //不良项目预警
                boolean isWaringed = clientSaveStepInfoDto.getUnqualifiedItemInfoList().stream().anyMatch(unqualifiedItemInfo -> this.saveUnqualifiedItemWaring(batchWorkDetail, unqualifiedItemRepository.getReferenceById(unqualifiedItemInfo.getUnqualifiedItemId()), clientSaveStepInfoDto.getVariety()));
                if (!isWaringed) {
                    //工序良率预警
                    this.saveStepWaring(batchWorkDetail);
                }
            }
        }
    }

    /**
     * 不良项目预警
     *
     * @param batchWorkDetail 批量详情
     * @param unqualifiedItem 不良项目
     * @return boolean
     * <AUTHOR>
     * @date 2021-01-20
     **/
    public boolean saveUnqualifiedItemWaring(BatchWorkDetail batchWorkDetail, UnqualifiedItem unqualifiedItem, Integer variety) {
        SubWorkSheet subWorkSheet = batchWorkDetail.getSubWorkSheet();
        Pedigree pedigree = subWorkSheet.getWorkSheet().getPedigree();
        Step step = batchWorkDetail.getStep();
        //通过子工单ID、工序ID,不良项目ID获取满足条件的不良总数
        Long number = wsStepUnqualifiedItemRepository.sumNumberBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(subWorkSheet.getId(), step.getId(), unqualifiedItem.getId(), Constants.LONG_ZERO);
        //计算不良率
        double occupancy = null != number ? NumberUtils.divide(number, batchWorkDetail.getFinishNumber(), Constants.INT_FOUR).doubleValue() : Constants.DOUBLE_ZERRO;
        //获取不良标准集合
        UnqualifiedItemWarningStandard unqualifiedItemWarningStandard = commonService.findUnqualifiedItemWaringStandard(subWorkSheet, pedigree, step, unqualifiedItem, batchWorkDetail.getFinishNumber(), occupancy);
        //标准不存在直接返回
        if (null == unqualifiedItemWarningStandard) {
            return false;
        }
        //当前工序完成数量小于标准的基数值则按照预警数量进行验证
        if (batchWorkDetail.getFinishNumber() < unqualifiedItemWarningStandard.getBaseNumber()) {
            if (null != number && number >= unqualifiedItemWarningStandard.getWaringNumber()) {
                this.saveUnqualifiedEvent(Constants.INT_ZERO, Constants.INT_ZERO, occupancy, batchWorkDetail, unqualifiedItem);

                //判断是否触发首件检测，是则更新首检/QC检测最新状态下次检测日期为当前日期
                faiAndUpdateLatestCheckResule(unqualifiedItemWarningStandard.getIsFai(), batchWorkDetail.getWorkCell().getId(), variety);
                return true;
            }
            return false;
        }
        //不良占有率低于预警值则不预警
        if (occupancy < unqualifiedItemWarningStandard.getWaringRate()) {
            return false;
        }
        //不良占有率大于预警值且小于停线值则预警
        if (occupancy >= unqualifiedItemWarningStandard.getWaringRate() && occupancy <= unqualifiedItemWarningStandard.getStopRate()) {
            this.saveUnqualifiedEvent(Constants.INT_ZERO, Constants.INT_ZERO, occupancy, batchWorkDetail, unqualifiedItem);

            //判断是否触发首件检测，是则更新首检/QC检测最新状态下次检测日期为当前日期
            faiAndUpdateLatestCheckResule(unqualifiedItemWarningStandard.getIsFai(), batchWorkDetail.getWorkCell().getId(), variety);
            return true;
        }
        //不良占用率大于停线值则停线
        if (occupancy >= unqualifiedItemWarningStandard.getStopRate()) {
            this.saveUnqualifiedEvent(Constants.INT_ONE, Constants.INT_ZERO, occupancy, batchWorkDetail, unqualifiedItem);

            //判断是否触发首件检测，是则更新首检/QC检测最新状态下次检测日期为当前日期
            faiAndUpdateLatestCheckResule(unqualifiedItemWarningStandard.getIsFai(), batchWorkDetail.getWorkCell().getId(), variety);
            return true;
        }
        return false;
    }

    /**
     * 保存预警停线记录
     *
     * @param eventType       类型 0:预警;1:停线
     * @param reasonType      原因类型 0:不良超标;1:合格率不达标
     * @param rate            工序合格率或者不良占有率
     * @param batchWorkDetail 工单详情
     * @param unqualifiedItem 不良项目
     * @return void
     * <AUTHOR>
     * @date 2021-01-20
     **/
    public void saveUnqualifiedEvent(int eventType, int reasonType, double rate, BatchWorkDetail batchWorkDetail, UnqualifiedItem unqualifiedItem) {
        SerialNumberDTO serialNumberDto = new SerialNumberDTO(eventType == Constants.INT_ZERO ? Constants.UNQUALIFIED_EVENT_WARING_SERIAL_NUMBER : Constants.UNQUALIFIED_EVENT_STOP_SERIAL_NUMBER, null, null);
        UnqualifiedEvent unqualifiedEvent = new UnqualifiedEvent();
        unqualifiedEvent.setOwnerId(batchWorkDetail.getOperatorId())
                .setEventType(eventType)
                .setReasonType(reasonType)
                .setQualifiedRate(eventType == Constants.INT_ONE ? rate : Constants.DOUBLE_ZERRO)
                .setUnqualifiedRate(eventType == Constants.INT_ZERO ? rate : Constants.DOUBLE_ZERRO)
                .setStep(batchWorkDetail.getStep())
                .setWorkCell(batchWorkDetail.getWorkCell())
                .setUnqualifiedItem(unqualifiedItem)
                .setSubWorkSheet(batchWorkDetail.getSubWorkSheet())
                .setStatus(Constants.INT_ZERO)
                .setRecordTime(LocalDateTime.now())
                .setSerialNumber(rbaseSerialNumberProxy.generate(serialNumberDto));
        unqualifiedEventRepository.save(unqualifiedEvent);
        //预警默认暂停当前子工单投产
        if (eventType == Constants.INT_ZERO) {
            SubWorkSheet subWorkSheet = batchWorkDetail.getSubWorkSheet();
            subWorkSheet.setStatus(Constants.PAUSE);
            subWorkSheetRepository.save(subWorkSheet);
        }
        //停线默认暂停当前总工单投产
        if (eventType == Constants.INT_ONE) {
            WorkSheet workSheet = batchWorkDetail.getSubWorkSheet().getWorkSheet();
            workSheet.setStatus(Constants.PAUSE);
            workSheetRepository.save(workSheet);
        }
    }

    /**
     * 判断触发首件检测，是则更新首检/QC检测最新状态下次检测日期为当前日期
     *
     * @param isFai      是否触发首检
     * @param workCellId 工位ID
     * <AUTHOR>
     * @date 2022/9/7
     **/
    public void faiAndUpdateLatestCheckResule(Boolean isFai, Long workCellId, Integer variety) {
        if (isFai) {
            //更新首检/QC检测最新状态下次检测日期为当前日期
            LatestCheckResult latestCheckResult = latestCheckResultRepository.findByWorkCellIdAndCategoryAndVarietyAndDeleted(workCellId, Constants.INT_ZERO, variety, Constants.LONG_ZERO).orElse(null);
            if (Objects.nonNull(latestCheckResult)) {
                latestCheckResult.setNextCheckDate(LocalDateTime.now());
                latestCheckResultRepository.save(latestCheckResult);
            }
        }
    }

    /**
     * 判断工序良率是否预警
     *
     * @param batchWorkDetail 工单详情
     * @return boolean
     * <AUTHOR>
     * @date 2021-01-20
     **/
    public boolean saveStepWaring(BatchWorkDetail batchWorkDetail) {
        Double qualifiedRate = NumberUtils.divide(batchWorkDetail.getQualifiedNumber(), batchWorkDetail.getFinishNumber(), Constants.INT_FOUR).doubleValue();
        //获取预警规则
        StepWarningStandard stepWarningStandard = getStepWarningStandard(batchWorkDetail.getSubWorkSheet().getWorkSheet(), batchWorkDetail.getStep(), batchWorkDetail.getFinishNumber(), qualifiedRate);

        if (Objects.isNull(stepWarningStandard) || batchWorkDetail.getFinishNumber() < stepWarningStandard.getBaseNumber()) {
            // 未找到匹配的工序良率预警标准则 或者 预警基数不满足直接返回
            return false;
        } else if (qualifiedRate > stepWarningStandard.getStopRate() && qualifiedRate <= stepWarningStandard.getWaringRate()) {
            //预警基数小于等于工序完成数 + 工序良率大于停线标准且小于等于预警标准则预警
            this.saveUnqualifiedEvent(Constants.INT_ZERO, Constants.INT_ONE, qualifiedRate, batchWorkDetail, null);
            return true;
        } else if (qualifiedRate <= stepWarningStandard.getStopRate()) {
            //预警基数小于等于工序完成数 + 工序良率小于等于停线标准则停线
            this.saveUnqualifiedEvent(Constants.INT_ONE, Constants.INT_ONE, qualifiedRate, batchWorkDetail, null);
            return true;
        } else {
            return false;
        }
    }


    /**
     * 最后一个工单详情完成时判断子工单是否完成及总工单是否完成
     *
     * @param batchWorkDetail 工单详情
     * @param subWorkSheet    子工单
     * @return void
     * <AUTHOR>
     * @date 2021-01-19
     **/
    public void finishSubWorkSheet(BatchWorkDetail batchWorkDetail, SubWorkSheet subWorkSheet) {
        subWorkSheet.setQualifiedNumber(batchWorkDetail.getTransferNumber())
                .setUnqualifiedNumber(subWorkSheet.getNumber() - batchWorkDetail.getTransferNumber())
                .setStatus(Constants.FINISH).setActualEndDate(LocalDateTime.now());
        subWorkSheet = subWorkSheetRepository.save(subWorkSheet);
        WorkSheet workSheet = subWorkSheet.getWorkSheet();
        final int qualifiedNumber = subWorkSheet.getQualifiedNumber();
        final int unqualifiedNumber = subWorkSheet.getUnqualifiedNumber();
        //子工单数量
        Long subWsCount = subWorkSheetRepository.countByWorkSheetIdAndDeleted(
                subWorkSheet.getWorkSheet().getId(), Constants.LONG_ZERO);
        //完成的子工单数量
        Long subWsFinishCount = subWorkSheetRepository.countByWorkSheetIdAndStatusGreaterThanEqualAndDeleted(
                subWorkSheet.getWorkSheet().getId(), Constants.FINISH, Constants.LONG_ZERO);
        workSheet.setQualifiedNumber(workSheet.getQualifiedNumber() + qualifiedNumber);
        workSheet.setUnqualifiedNumber(workSheet.getUnqualifiedNumber() + unqualifiedNumber);
        //判断总工单是否能够完成（1.所有子工单都已完成 2：总工单的合格数+不合格数=投产数，防止分单没分完）
        if (subWsCount.equals(subWsFinishCount) && workSheet.getNumber() == (workSheet.getQualifiedNumber() + workSheet.getUnqualifiedNumber())) {
            workSheet.setActualEndDate(LocalDateTime.now())
                    .setStatus(Constants.FINISH);
        }
        //如果当前工单为在线返修单则完成后需要更新对应的原始正常单数据
        if (workSheet.getCategory() == Constants.NEGATIVE_ONE) {
            Optional<WsRework> wsReworkOptional = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            wsReworkOptional.ifPresent(wsRework -> {
                WorkSheet originalWorkSheet = wsRework.getOriginalWorkSheet();
                originalWorkSheet.setQualifiedNumber(originalWorkSheet.getQualifiedNumber() + qualifiedNumber);
                originalWorkSheet.setUnqualifiedNumber(originalWorkSheet.getUnqualifiedNumber() - qualifiedNumber);
                originalWorkSheet.setReworkQualifiedNumber(originalWorkSheet.getReworkQualifiedNumber() + qualifiedNumber);
                workSheetRepository.save(originalWorkSheet);
            });
        }
        workSheetRepository.save(workSheet);
        //更新工单的子工单个数以及完成个数(包括正常、异常)
        subWorkSheetService.updateWorkSheetSubWsNumberInfo(workSheet);
    }

    /**
     * fqc处理
     *
     * @param clientSaveStepInfoDto
     */
    @Transactional(rollbackFor = Exception.class)
    public void fqcDeal(ClientSaveStepInfoDTO clientSaveStepInfoDto, SubWorkSheet subWorkSheet, WorkCell workCell, Step step) {
        //获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = commonService.findSnapshotWorkFlow(subWorkSheet.getWorkSheet(), subWorkSheet, step);
        Optional<PedigreeStepCheckRule> pedigreeStepCheckRuleOptional = pedigreeStepCheckRuleRepository
                .findByPedigreeIdAndWorkFlowIdAndStepIdAndCategoryAndDeleted(
                        subWorkSheet.getWorkSheet().getPedigree().getId(), snapshotWorkFlow.getId(), clientSaveStepInfoDto.getStepId(), Constants.INT_THREE, Constants.LONG_ZERO);
        if (!pedigreeStepCheckRuleOptional.isPresent()) {
            throw new ResponseException("error.error.pedigreeStepStandardNotExist", "FQC检测规则不存在!");
        }
        PedigreeStepCheckRule pedigreeStepCheckRule = pedigreeStepCheckRuleOptional.get();
        //保存基本信息
        FqcCheckResult fqcCheckResult = new FqcCheckResult();
        fqcCheckResult.setWorkCell(workCell)
                .setOriginalSubWorkSheet(subWorkSheet)
                .setDealStaffId(clientSaveStepInfoDto.getStaffId())
                .setCode(UUID.randomUUID().toString())
                .setCheckNumber(clientSaveStepInfoDto.getNumber())
                .setUnqualifiedNumber(clientSaveStepInfoDto.getUnqualifiedNumber())
                .setReason(clientSaveStepInfoDto.getFqcReason())
                .setStep(step)
                .setResult(true);
        //如果允收标准比例为0%，如果出现不合格，则FQC不合格
        if (pedigreeStepCheckRule.getQualifiedRate() == 0 && clientSaveStepInfoDto.getUnqualifiedNumber() > 0) {
            fqcCheckResult.setResult(false);
        }
        //如果允收标准比例大于0%，则判断不合格数量是否超过计算的允收不合格数量
        if (pedigreeStepCheckRule.getQualifiedRate() > 0
                && NumberUtils.divide(clientSaveStepInfoDto.getQualifiedNumber(), clientSaveStepInfoDto.getNumber(), Constants.INT_FOUR).doubleValue()
                < pedigreeStepCheckRule.getQualifiedRate()) {
            fqcCheckResult.setResult(false);
        }
        fqcCheckResult = fqcCheckResultRepository.save(fqcCheckResult);
        if (clientSaveStepInfoDto.getFqcDealResult() == Constants.INT_ZERO) {
            //批退
            fqcCheckResult.setType(Constants.INT_ZERO)
                    .setStatus(Constants.INT_ONE);
            fqcCheckResult = fqcCheckResultRepository.save(fqcCheckResult);
            fqcCheckResultService.generate(new FqcDealDTO(
                    fqcCheckResult.getId(), Constants.INT_ZERO,
                    clientSaveStepInfoDto.getFqcWorkFlowId(),
                    clientSaveStepInfoDto.getFqcReason(), clientSaveStepInfoDto.getStaffId()), fqcCheckResult);
        } else if (clientSaveStepInfoDto.getFqcDealResult() == Constants.INT_ONE) {
            //放行
            fqcCheckResult.setDealStaffId(clientSaveStepInfoDto.getStaffId())
                    .setStatus(Constants.INT_ONE)
                    .setType(Constants.INT_ONE);
            fqcCheckResultRepository.save(fqcCheckResult);
            //更新子工单状态
            SubWorkSheet originalSubWorkSheet = fqcCheckResult.getOriginalSubWorkSheet();
            originalSubWorkSheet.setStatus(Constants.PRODUCING);
            subWorkSheetRepository.save(originalSubWorkSheet);
        } else if (clientSaveStepInfoDto.getFqcDealResult() == Constants.INT_TWO) {
            //网页端处理
            fqcCheckResult.setStatus(Constants.INT_ZERO)
                    .setDealWay(Constants.INT_ONE);
            fqcCheckResultRepository.save(fqcCheckResult);
            //工单暂停
            subWorkSheet.setStatus(Constants.PAUSE);
            subWorkSheetRepository.save(subWorkSheet);
        }
    }

    /**
     * 保存抽检数据
     *
     * @param clientSaveStepInfoDto Rwork请求参数DTO
     * @param subWorkSheet          子工单
     * @param workCell              工位
     * @param step                  工序
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveRandomInspectInfo(ClientSaveStepInfoDTO clientSaveStepInfoDto, SubWorkSheet subWorkSheet, WorkCell workCell, Step step) {
        //首先保存历史数据，因为下面历史详情会用到
        ClientSaveStepInfoDTO.RandomInspectSaveInfo randomInspectInfo = clientSaveStepInfoDto.getRandomInspectSaveInfo();
        CheckHistory checkHistory = new CheckHistory();
        checkHistory.setCategory(randomInspectInfo.getCategory())
                .setNumber(randomInspectInfo.getNumber())
                .setOperatorId(clientSaveStepInfoDto.getStaffId())
                .setQualifiedNumber(randomInspectInfo.getQualifiedNumber())
                .setStep(step)
                .setSubWorkSheet(subWorkSheet)
                .setWorkCell(workCell)
                .setUnqualifiedNumber(randomInspectInfo.getUnQualifiedNumber())
                .setResult(randomInspectInfo.getResult())
                .setRecordDate(LocalDateTime.now())
                .setDeleted(Constants.LONG_ZERO);
        checkHistoryRepository.save(checkHistory);
        if (ValidateUtils.isValid(randomInspectInfo.getSnCheckItemInfoList())) {
            randomInspectInfo.getSnCheckItemInfoList().forEach(snCheckItemInfo -> {
                snCheckItemInfo.getCheckItemInfoList().forEach(checkItemInfo -> {
                    CheckHistoryDetail checkHistoryDetail = new CheckHistoryDetail();
                    checkHistoryDetail.setSn(snCheckItemInfo.getSn())
                            .setCheckHistory(checkHistory)
                            .setCheckItemId(checkItemInfo.getCheckItemId())
                            .setCheckData(checkItemInfo.getCheckData())
                            .setQualifiedRange(checkItemInfo.getQualifiedRange())
                            .setResult(checkItemInfo.getResult())
                            .setSn(snCheckItemInfo.getSn())
                            .setDeleted(Constants.LONG_ZERO);
                    checkHistoryDetailRepository.save(checkHistoryDetail);
                });

            });
        }
    }

    /**
     * 保存员工产量报表数据
     *
     * @param batchWorkDetail       批次详情
     * @param containerDetail       容器详情
     * @param clientSaveStepInfoDto 保存信息
     * @return void
     * <AUTHOR>
     * @date 2022/12/5
     */
    public void saveStaffPerformReport(BatchWorkDetail batchWorkDetail, ContainerDetail containerDetail, ClientSaveStepInfoDTO clientSaveStepInfoDto) {
        //获取当前记录日期
        LocalDate recordDate = commonService.staffWorkRecordDate();
        //保存员工产量信息及不良信息
        saveStaffPerform(batchWorkDetail, containerDetail, clientSaveStepInfoDto, recordDate);
    }

    /**
     * 保存员工产量信息及不良信息
     *
     * @param batchWorkDetail       工序批次信息
     * @param containerDetail       容器详情信息
     * @param clientSaveStepInfoDto 保存信息
     * @param recordDate            记录日期
     * @return void
     * <AUTHOR>
     * @date 2022/12/5
     */
    public void saveStaffPerform(BatchWorkDetail batchWorkDetail, ContainerDetail containerDetail, ClientSaveStepInfoDTO clientSaveStepInfoDto, LocalDate recordDate) {
        SubWorkSheet subWorkSheet = subWorkSheetRepository.getReferenceById(clientSaveStepInfoDto.getSubWsId());
        //保存员工产量信息
        StaffPerform staffPerform = new StaffPerform();
        staffPerform
                .setBatchWorkDetailId(batchWorkDetail != null ? batchWorkDetail.getId() : null)
                .setContainerDetailId(containerDetail != null ? containerDetail.getId() : null)
                .setWorkHour(batchWorkDetail.getWorkHour())
                .setSnWorkDetailId(null)
                .setStaffId(clientSaveStepInfoDto.getStaffId())
                .setStep(stepRepository.getReferenceById(clientSaveStepInfoDto.getStepId()))
                .setSubWorkSheet(subWorkSheet)
                .setWorkSheet(subWorkSheet.getWorkSheet())
                .setWorkCell(workCellRepository.getReferenceById(clientSaveStepInfoDto.getWorkCellId()))
                .setRecordDate(recordDate)
                .setRecordTime(LocalDateTime.now())
                .setInputNumber(clientSaveStepInfoDto.getNumber())
                .setQualifiedNumber(clientSaveStepInfoDto.getQualifiedNumber())
                .setUnqualifiedNumber(clientSaveStepInfoDto.getUnqualifiedNumber()).setDeleted(Constants.LONG_ZERO);
        staffPerformRepository.save(staffPerform);

        //若有不良则更新员工不良明细信息（不存在sn）
        if (clientSaveStepInfoDto.getUnqualifiedNumber() > 0 && ValidateUtils.isValid(clientSaveStepInfoDto.getUnqualifiedItemInfoList())) {
            clientSaveStepInfoDto.getUnqualifiedItemInfoList().forEach(unqualifiedItemInfo -> {
                StaffPerformUnqualifiedItem staffPerformUnqualifiedItem = new StaffPerformUnqualifiedItem();
                staffPerformUnqualifiedItem.setStaffPerform(staffPerform).setRecordDate(staffPerform.getRecordDate())
                        .setRecordTime(staffPerform.getRecordTime())
                        .setUnqualifiedItem(unqualifiedItemRepository.getReferenceById(unqualifiedItemInfo.getUnqualifiedItemId()))
                        .setNumber(unqualifiedItemInfo.getNumber()).setDeleted(Constants.LONG_ZERO);
                staffPerformUnqualifiedItemRepository.save(staffPerformUnqualifiedItem);
            });
        }
    }

    /**
     * 默认获取实际还需待做数量
     *
     * @param clientGetStepInfoDto Rwork请求待做工序返回信息
     * @param subWorkSheet         子工单
     * @param wsStep               当前定制工序
     */
    @Override
    public void getTodoStepPendingNumber(ClientGetStepInfoDTO clientGetStepInfoDto, SubWorkSheet subWorkSheet, WsStep wsStep) {
        //子工单第一次请求时(子工单),赋初始值，后续根据条件重新修改
        clientGetStepInfoDto.setPendingNumber(subWorkSheet.getNumber()).setFinishNumber(Constants.INT_ZERO);

        Optional<BatchWorkDetail> batchWorkDetailOptional = batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), wsStep.getStep().getId(), Constants.LONG_ZERO);
        //在线调整工序是获取不良项目总数
        if (wsStep.getCategory() == Constants.INT_ONE || wsStep.getCategory() == Constants.INT_TWO) {
            List<UnqualifiedItem> unqualifiedItemList = onlineReworkRuleRepository.findUnqualifiedItemByStepIdAndDeleted(wsStep.getStep().getId(), Constants.LONG_ZERO);
            if (ValidateUtils.isValid(unqualifiedItemList)) {
                Long unqualifiedNumber = wsStepUnqualifiedItemRepository.sumNumberBySubWorkSheetIdAndUnqualifiedItemIdInAndDeleted(subWorkSheet.getId(), unqualifiedItemList.stream().map(UnqualifiedItem::getId).collect(Collectors.toList()), Constants.LONG_ZERO);
                int pendingNumber = null == unqualifiedNumber ? Constants.INT_ZERO : unqualifiedNumber.intValue();
                if (batchWorkDetailOptional.isPresent()) {
                    clientGetStepInfoDto.setPendingNumber(pendingNumber - batchWorkDetailOptional.get().getFinishNumber()).setFinishNumber(batchWorkDetailOptional.get().getFinishNumber());
                } else {
                    clientGetStepInfoDto.setPendingNumber(pendingNumber).setFinishNumber(Constants.INT_ZERO);
                }
            }
            return;
        }
        //除第一道工序外
        if (ValidateUtils.isValid(wsStep.getPreStepId())) {
            //上道工序的待流转数（当前工序的待完成数）
            List<Long> preStepIds = Arrays.stream(wsStep.getPreStepId().split(Constants.STR_COMMA)).map(Long::parseLong).collect(Collectors.toList());
            List<BatchWorkDetail> batchWorkDetails = batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdInAndDeleted(subWorkSheet.getId(), preStepIds, Constants.LONG_ZERO);
            if (ValidateUtils.isValid(batchWorkDetails)) {
                int finishNumber = batchWorkDetailOptional.map(BatchWorkDetail::getFinishNumber).orElse(Constants.INT_ZERO);
                //存在上一道工序为调整时,需要使用上道工序的待流转数量作为下道工序的待做数量
                Optional<BatchWorkDetail> preBatchWorkDetailOptional = batchWorkDetails.stream().filter(batchWorkDetail -> batchWorkDetail.getStep().getCategory() == Constants.INT_ONE).findFirst();
                int pendingNumber = preBatchWorkDetailOptional.map(batchWorkDetail -> batchWorkDetail.getTransferNumber() - finishNumber).orElseGet(() -> batchWorkDetails.stream().map(BatchWorkDetail::getQualifiedNumber).min(Integer::compareTo).get() - finishNumber);
                clientGetStepInfoDto.setPendingNumber(pendingNumber);
            }
            //当前工序完成数
            clientGetStepInfoDto.setFinishNumber(batchWorkDetailOptional.map(BatchWorkDetail::getFinishNumber).orElse(Constants.INT_ZERO));
        } else {
            //第一道工序以开始
            batchWorkDetailOptional.ifPresent(batchWorkDetail -> clientGetStepInfoDto.setFinishNumber(batchWorkDetail.getFinishNumber()).setPendingNumber(subWorkSheet.getNumber() - batchWorkDetail.getFinishNumber()));
        }
    }

    /**
     * 获取容器当前待做的工序信息
     *
     * @param clientGetTodoStepInfoDto 请求工序的容器参数
     * @return void
     **/
    @Override
    public ClientGetStepInfoDTO getContainerStepInfo(ClientGetTodoStepInfoDTO clientGetTodoStepInfoDto) {
        if (clientGetTodoStepInfoDto.getRequestContainerIds().size() > Constants.INT_ONE && null == clientGetTodoStepInfoDto.getBindContainerId()) {
            return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "多个容器下交必须扫描新容器!"));
        }
        //验证绑定容器，是否存在返修关系，存在则需要验证绑定工单是否一致
        if (!org.springframework.util.ObjectUtils.isEmpty(clientGetTodoStepInfoDto.getBindContainerId())) {
            Optional<SnRework> snReworkOptional = snReworkRepository.findTop1ByContainerIdAndStatusAndDeletedOrderByIdDesc(clientGetTodoStepInfoDto.getBindContainerId(), Constants.INT_ONE, Constants.LONG_ZERO);
            if (snReworkOptional.isPresent()) {
                SnRework snRework = snReworkOptional.get();
                if (!snRework.getWsRework().getReworkWorkSheet().getId().equals(clientGetTodoStepInfoDto.getSubWorkSheet().getWorkSheet().getId())) {
                    return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "当前返修容器绑定的工单号为：" + snRework.getWsRework().getReworkWorkSheet().getSerialNumber() + "请输入合法的子工单与之对应！"));
                }
            }
        }
        //获取请求容器列表最新完成且未下交的容器详情列表
        List<ContainerDetail> preFinishedContainerDetailList = containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndContainerIdInAndStatusAndDeletedOrderByIdDesc(clientGetTodoStepInfoDto.getSubWorkSheet().getId(), clientGetTodoStepInfoDto.getRequestContainerIds(), Constants.INT_ONE, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(preFinishedContainerDetailList)) {
            return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "容器无待下交的工序!"));
        }
        //判断每个请求的容器是否都处于待下交状态
        Long alreadyTransferedId = clientGetTodoStepInfoDto.getRequestContainerIds().stream().filter(requestContainerId -> preFinishedContainerDetailList.stream().noneMatch(preFinishedContainerDetail -> preFinishedContainerDetail.getContainer().getId().equals(requestContainerId))).findFirst().orElse(null);
        if (null != alreadyTransferedId) {
            Container container = containerRepository.getReferenceById(alreadyTransferedId);
            return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "容器(" + container.getCode() + ")不存在待下交数量!"));
        }
        //获取请求的容器已完成的工序对应的定制工序
        List<WsStep> finishedWsStepList = clientGetTodoStepInfoDto.getWsStepList().stream().filter(wsStep -> preFinishedContainerDetailList.stream().map(ContainerDetail::getBatchWorkDetail).
                collect(Collectors.toList()).stream().map(BatchWorkDetail::getStep).collect(Collectors.toList()).stream().
                map(Step::getId).collect(Collectors.toList()).contains(wsStep.getStep().getId())).collect(Collectors.toList());
        //判断各个请求的容器下个待做工序是否一致
        if (finishedWsStepList.stream().map(WsStep::getAfterStepId).distinct().count() > Constants.INT_ONE) {
            return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "容器下道生产工序不一致!"));
        }
        //是否复用旧容器 即A+B->A的情形，此时A为主容器
        boolean reuseContainer = ValidateUtils.isValid(clientGetTodoStepInfoDto.getRequestContainerIds()) && null != clientGetTodoStepInfoDto.getBindContainerId() && clientGetTodoStepInfoDto.getRequestContainerIds().contains(clientGetTodoStepInfoDto.getBindContainerId());
        String nextTodoStepId = finishedWsStepList.get(Constants.INT_ZERO).getAfterStepId();
        Optional<WsStep> optionalWsStep = clientGetTodoStepInfoDto.getWsStepList().stream().filter(wsStep -> wsStep.getStep().getId().toString().equals(nextTodoStepId)).findFirst();
        if (!optionalWsStep.isPresent()) {
            return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "生产工单定制工序不存在!"));
        }
        WsStep nextTodoWsStep = optionalWsStep.get();
        List<Step> preStepList = clientGetTodoStepInfoDto.getWsStepList().stream().filter(wsStep -> nextTodoWsStep.getPreStepId().contains(wsStep.getStep().getId().toString())).collect(Collectors.toList()).stream().map(WsStep::getStep).collect(Collectors.toList());
        Step nextToDoStep = clientGetTodoStepInfoDto.getStepList().stream().filter(step -> step.getId().toString().equals(nextTodoStepId)).findFirst().orElse(null);
        int nextToDoStepNumber = Constants.INT_ZERO;
        if (null == nextToDoStep) {
            return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "当前工位需要配置工序为:" + stepRepository.findByIdAndDeleted(Long.parseLong(nextTodoStepId), Constants.LONG_ZERO).orElse(new Step()).getName()));
        }
        //获取下个待做工序对应的工单定制工序
        Optional<WsStep> optionalCurrWsStep = clientGetTodoStepInfoDto.getWsStepList().stream().filter(wsStep -> wsStep.getStep().getId().equals(nextToDoStep.getId())).findFirst();
        if (!optionalCurrWsStep.isPresent()) {
            return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "下个待做工序对应的工单定制工序不存在!"));
        }
        WsStep currWsStep = optionalCurrWsStep.get();
        //判断下个待做工序是否属于分叉聚合工序
        boolean isSplitProcess = StringUtils.isNotBlank(currWsStep.getPreStepId()) && currWsStep.getPreStepId().split(Constants.STR_COMMA).length > Constants.INT_ONE;
        //A+B->A分叉聚合工序且复用容器场景
        if (reuseContainer && isSplitProcess) {
            Container mainContainer = containerRepository.getReferenceById(clientGetTodoStepInfoDto.getBindContainerId());
            //获取当前主容器未下交的容器详情
            ContainerDetail currContainerDetail = containerDetailRepository.findTop1ByBatchWorkDetailSubWorkSheetIdAndContainerIdAndStatusAndDeletedOrderByIdDesc(clientGetTodoStepInfoDto.getSubWorkSheet().getId(), clientGetTodoStepInfoDto.getBindContainerId(), Constants.INT_ONE, Constants.LONG_ZERO).orElse(null);
            if (null == currContainerDetail) {
                return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "容器(" + mainContainer.getCode() + ")不存在未下交工序!"));
            }
            Optional<ContainerDetail> containerDetailOptional = preFinishedContainerDetailList.stream().filter(containerDetail -> containerDetail.getContainer().getId().equals(clientGetTodoStepInfoDto.getBindContainerId())).findFirst();
            if (!containerDetailOptional.isPresent()) {
                return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "容器容器生产详情不不存在!"));
            }
            int nextToDoStepPutInNumber = containerDetailOptional.get().getTransferNumber();
            //对于A+B->A的场景目前只支持A工序下交一个容器,B工序可以下交多个容器
            if (preFinishedContainerDetailList.stream().filter(containerDetail -> containerDetail.getBatchWorkDetail().getStep().getId().equals(currContainerDetail.getBatchWorkDetail().getStep().getId())).collect(Collectors.toList())
                    .stream().anyMatch(containerDetail -> !containerDetail.getContainerCode().equals(mainContainer.getCode()))) {
                return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "工序(" + currContainerDetail.getBatchWorkDetail().getStep().getName() + ")只允许一个容器请求!"));
            }
            Map<BatchWorkDetail, Integer> collect = preFinishedContainerDetailList.stream().filter(containerDetail -> !containerDetail.getContainer().getId().equals(clientGetTodoStepInfoDto.getBindContainerId())).collect(Collectors.groupingBy(ContainerDetail::getBatchWorkDetail,
                    Collectors.summingInt(ContainerDetail::getTransferNumber)));
            Map.Entry<BatchWorkDetail, Integer> batchWorkDetailIntegerEntry = collect.entrySet().stream().filter(map -> map.getValue() < nextToDoStepPutInNumber).findFirst().orElse(null);
            if (null != batchWorkDetailIntegerEntry) {
                return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "工序(" + batchWorkDetailIntegerEntry.getKey().getStep().getName() + ")容器下交数量不够!"));
            }
            nextToDoStepNumber = nextToDoStepPutInNumber;
        }
        //A+B->C的模式的时候强制各个分叉工序下交的数量一致且扫描的容器数量足数
        if (!reuseContainer && isSplitProcess) {
            ContainerDetail currContainerDetail = containerDetailRepository.findTop1ByBatchWorkDetailSubWorkSheetIdAndContainerIdAndStatusAndDeletedOrderByIdDesc(clientGetTodoStepInfoDto.getSubWorkSheet().getId(), clientGetTodoStepInfoDto.getBindContainerId(), Constants.INT_ONE, Constants.LONG_ZERO).orElse(null);
            if (currContainerDetail != null) {
                return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "容器id(" + clientGetTodoStepInfoDto.getBindContainerId() + ")已绑定!"));
            }
            //按照工序分组取到各个分叉上下交下来的生产工序信息
            Map<BatchWorkDetail, List<ContainerDetail>> collect = preFinishedContainerDetailList.stream().collect(Collectors.groupingBy(ContainerDetail::getBatchWorkDetail));
            List<Integer> preBatchWorkDetailTransferNumberList = Lists.newArrayList();
            collect.forEach((key, value) -> preBatchWorkDetailTransferNumberList.add(value.stream().mapToInt(ContainerDetail::getTransferNumber).sum()));
            //若不存在复用容器且属于分叉聚合的工序时要求各分叉工序下交的待流转数量一致
            if (preBatchWorkDetailTransferNumberList.stream().distinct().count() > Constants.INT_ONE) {
                return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "分叉工序下交投产数量不一致!"));
            }
            //下个工序待做投产数量
            nextToDoStepNumber = preBatchWorkDetailTransferNumberList.get(Constants.INT_ZERO);
            //A+B->C时A,B工序的容器必须都要下交
            Set<Long> preFinishedStepIdList = preFinishedContainerDetailList.stream().map(containerDetail -> containerDetail.getBatchWorkDetail().getStep().getId()).collect(Collectors.toSet());
            Step notTransferStep = preStepList.stream().filter(step -> !preFinishedStepIdList.contains(step.getId())).findAny().orElse(null);
            if (null != notTransferStep) {
                return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "工序(" + notTransferStep.getName() + ")下交的容器未扫描!"));
            }
        }
        //A->B模式,直接将请求容器的所有剩余流转数求和作为待投产工序
        if (!reuseContainer && !isSplitProcess) {
            ContainerDetail currContainerDetail = containerDetailRepository.findTop1ByBatchWorkDetailSubWorkSheetIdAndContainerIdAndStatusAndDeletedOrderByIdDesc(clientGetTodoStepInfoDto.getSubWorkSheet().getId(), clientGetTodoStepInfoDto.getBindContainerId(), Constants.INT_ONE, Constants.LONG_ZERO).orElse(null);
            //新容器堆叠
            nextToDoStepNumber = preFinishedContainerDetailList.stream().mapToInt(ContainerDetail::getTransferNumber).sum();
            //旧容器堆叠，需要验证请求容器的下道工序与当前绑定容器的工序当前工序是否一致
            if (currContainerDetail != null && (!currWsStep.getStep().getCode().equals(currContainerDetail.getBatchWorkDetail().getStep().getCode()))) {
                return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "容器id(" + clientGetTodoStepInfoDto.getBindContainerId() + ")请求容器列表与绑定容器工序不一致!"));
            }
        }

        //如果待做工序为在线返修工序则需要按照工序属性配置分别获取单支待返修数量和容器待返修数量
        if (currWsStep.getCategory() == Constants.INT_ONE || currWsStep.getCategory() == Constants.INT_TWO) {
            nextToDoStepNumber = Constants.INT_ZERO;
            List<UnqualifiedItem> unqualifiedItemList = onlineReworkRuleRepository.findUnqualifiedItemByStepIdAndDeleted(nextToDoStep.getId(), Constants.LONG_ZERO);
            if (!ValidateUtils.isValid(unqualifiedItemList)) {
                return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "返修工序(" + nextToDoStep.getName() + ")未配置在线返修规则!"));
            }
            //单支模式时根据SN生产状态表来判断需要进行返修的个数
            if (currWsStep.getControlMode() == Constants.INT_ONE) {
                List<SnWorkStatus> snWorkStatusList = snWorkStatusRepository.findBySubWorkSheetIdAndStatusAndLatestUnqualifiedItemIdInAndDeleted(clientGetTodoStepInfoDto.getSubWorkSheet().getId(), Constants.INT_THREE,
                        unqualifiedItemList.stream().map(UnqualifiedItem::getId).collect(Collectors.toList()), Constants.LONG_ZERO);
                nextToDoStepNumber = (int) snWorkStatusList.stream().filter(snWorkStatus -> preFinishedContainerDetailList.stream()
                        .anyMatch(containerDetail -> snWorkStatus.getLatestReworkSnWorkDetail().getContainerDetail().getId().equals(containerDetail.getId()))).count();
            }
            //批量模式时根据容器详情不良表来判断需要进行返修的个数
            if (currWsStep.getControlMode() == Constants.INT_ZERO) {
                List<ContainerDetail> preContainerDetailList = containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndContainerIdInAndDeleted(clientGetTodoStepInfoDto.getSubWorkSheet().getId(), clientGetTodoStepInfoDto.getRequestContainerIds(), Constants.LONG_ZERO);
                Long sumNpiNumber = containerDetailUnqualifiedItemRepository.findSumNumberByContainerDetailIdInAndUnqualifiedItemInAndDeleted(preContainerDetailList.stream().map(ContainerDetail::getId).collect(Collectors.toList()),
                        unqualifiedItemList.stream().map(UnqualifiedItem::getId).collect(Collectors.toList()), Constants.LONG_ZERO);
                nextToDoStepNumber = null != sumNpiNumber ? sumNpiNumber.intValue() : Constants.INT_ZERO;
            }
        }
        ClientGetStepInfoDTO clientGetStepInfoDto = new ClientGetStepInfoDTO();
        clientGetStepInfoDto.setStepId(nextToDoStep.getId())
                .setStepName(nextToDoStep.getName())
                .setStepCode(nextToDoStep.getCode())
                .setNumber(nextToDoStepNumber);
        return clientGetStepInfoDto;
    }


    /**
     * 批量获得当前待做工序
     *
     * @param clientGetTodoStepInfoDto 批量请求待做工序信息
     * @return ClientGetStepInfoDTO
     */
    @Transactional(readOnly = true)
    @Override
    public ClientGetStepInfoDTO getBatchStepInfo(ClientGetTodoStepInfoDTO clientGetTodoStepInfoDto) {
        List<BatchWorkDetail> batchWorkDetailList = batchWorkDetailRepository.findBySubWorkSheetIdAndDeleted(clientGetTodoStepInfoDto.getSubWorkSheet().getId(), Constants.LONG_ZERO);
        List<List<WsStep>> lists = commonService.dealWsStep(clientGetTodoStepInfoDto.getWsStepList());
        //当前工位待做工序
        Step nextToDoStep = null;
        //当前工位待做工序的投产数
        int nextTodoStepPutInNumber = Constants.INT_ZERO;
        if (!ValidateUtils.isValid(batchWorkDetailList)) {
            //查找第一个工序
            List<WsStep> collect = clientGetTodoStepInfoDto.getWsStepList().stream().filter(wsStep ->
                    !ValidateUtils.isValid(wsStep.getPreStepId()) && clientGetTodoStepInfoDto.getStepList().stream().anyMatch(
                            step -> step.getId().equals(wsStep.getStep().getId()))).collect(Collectors.toList());
            if (ValidateUtils.isValid(collect)) {
                nextToDoStep = collect.get(Constants.INT_ZERO).getStep();
                nextTodoStepPutInNumber = clientGetTodoStepInfoDto.getSubWorkSheet().getNumber();
            }
        } else {
            //按照工序对工单详情进行分组
            Map<Long, List<BatchWorkDetail>> collect = batchWorkDetailList.stream().collect(Collectors.groupingBy(batchWorkDetail -> batchWorkDetail.getStep().getId()));
            //按层级顺序遍历流程
            ONE:
            for (List<WsStep> list : lists) {
                for (WsStep wsStep : list) {
                    for (Step step : clientGetTodoStepInfoDto.getStepList()) {
                        if (wsStep.getStep().getId().equals(step.getId())) {
                            List<BatchWorkDetail> batchWorkDetails = collect.get(wsStep.getStep().getId());
                            BatchWorkDetail batchWorkDetail = ValidateUtils.isValid(batchWorkDetails) ? batchWorkDetails.get(Constants.INT_ZERO) : null;
                            //当前工序对应的工单详情不为空且已完成
                            boolean isFinish = null != batchWorkDetail && batchWorkDetail.getFinish() > Constants.INT_ZERO;
                            if (!isFinish) {
                                //第一个工序未完成
                                if (StringUtils.isBlank(wsStep.getPreStepId())) {
                                    nextToDoStep = step;
                                    nextTodoStepPutInNumber = clientGetTodoStepInfoDto.getSubWorkSheet().getNumber() - (null != batchWorkDetail ? batchWorkDetail.getFinishNumber() : Constants.INT_ZERO);
                                    break ONE;
                                }
                                //上面工序未完成则继续判断下一个匹配到的工序
                                if (Arrays.stream(wsStep.getPreStepId().split(Constants.STR_COMMA))
                                        .anyMatch(preStepId -> collect.get(Long.parseLong(preStepId)) == null ||
                                                collect.get(Long.parseLong(preStepId)).stream().anyMatch(currBatchWorkDetail -> currBatchWorkDetail.getFinish() == Constants.INT_ZERO))) {
                                    break;
                                }
                                nextToDoStep = step;
                                //TODO 这个地方多分叉下交时存在两种情况 1:下个待做工序的投产数=上面多个工序下交的总和 2:下个待做工序的投产数=上面某一个工序的下交数
                                Optional<Map.Entry<Long, List<BatchWorkDetail>>> firstOptional = collect.entrySet().stream().filter(map -> wsStep.getPreStepId().contains(map.getKey().toString())).findFirst();
                                nextTodoStepPutInNumber = firstOptional.map(longListEntry -> longListEntry.getValue().get(Constants.INT_ZERO).getTransferNumber()).orElse(Constants.INT_ZERO);
                                nextTodoStepPutInNumber = nextTodoStepPutInNumber == Constants.INT_ZERO ? nextTodoStepPutInNumber : nextTodoStepPutInNumber - (null != batchWorkDetail ? batchWorkDetail.getFinishNumber() : Constants.INT_ZERO);
                                break ONE;
                            }
                        }
                    }
                }
            }
        }
        if (null == nextToDoStep) {
            return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "当前工位无可待做工序!"));
        }
        Step finalNextToDoStep = nextToDoStep;
        Optional<WsStep> wsStepOptional = clientGetTodoStepInfoDto.getWsStepList().stream().filter(wsStep -> wsStep.getStep().getId().equals(finalNextToDoStep.getId())).findFirst();
        if (!wsStepOptional.isPresent()) {
            return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "生产工单定制工序不存在!"));
        }
        WsStep currWsStep = wsStepOptional.get();
        //在线调整工序是获取不良项目总数
        if (currWsStep.getCategory() == Constants.INT_ONE || currWsStep.getCategory() == Constants.INT_TWO) {
            List<UnqualifiedItem> unqualifiedItemList = onlineReworkRuleRepository.findUnqualifiedItemByStepIdAndDeleted(nextToDoStep.getId(), Constants.LONG_ZERO);
            if (!ValidateUtils.isValid(unqualifiedItemList)) {
                return new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, "在线调整工序(" + nextToDoStep.getName() + ")未配置规则!"));
            }
            Long unqualifiedNumber = wsStepUnqualifiedItemRepository.sumNumberBySubWorkSheetIdAndUnqualifiedItemIdInAndDeleted(clientGetTodoStepInfoDto.getSubWorkSheet().getId(), unqualifiedItemList.stream().map(UnqualifiedItem::getId).collect(Collectors.toList()), Constants.LONG_ZERO);
            nextTodoStepPutInNumber = null == unqualifiedNumber ? Constants.INT_ZERO : unqualifiedNumber.intValue();
        }

        ClientGetStepInfoDTO clientGetStepInfoDto = new ClientGetStepInfoDTO();
        clientGetStepInfoDto.setStepId(nextToDoStep.getId())
                .setStepName(nextToDoStep.getName())
                .setStepCode(nextToDoStep.getCode())
                .setNumber(nextTodoStepPutInNumber);
        return clientGetStepInfoDto;
    }

    /**
     * 根据子工单与当前工序获取其待完成的投产数
     * 主要用来判断当前需要保存的工单详情是否为完成
     *
     * @param subWorkSheet 子工单
     * @param wsStep       待完成的定制工序
     * @return int
     * <AUTHOR>
     * @date 2021-01-18
     **/
    @Transactional(readOnly = true)
    public int getStepPutInNumber(SubWorkSheet subWorkSheet, WsStep wsStep) {
        if (StringUtils.isBlank(wsStep.getPreStepId())) {
            return subWorkSheet.getNumber();
        }
        List<Long> preStepIds = Arrays.stream(wsStep.getPreStepId().split(Constants.STR_COMMA)).map(Long::parseLong).collect(Collectors.toList());
        //当前工序为非返修工序时的投产数=前置工序的流转数，目前暂时默认前置工序往下流转的数据必须一致
        if (wsStep.getCategory() != Constants.INT_ONE && wsStep.getCategory() != Constants.INT_TWO) {
            List<BatchWorkDetail> preWorkDetailList = batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdInAndDeleted(subWorkSheet.getId(), preStepIds, Constants.LONG_ZERO);
            if (!ValidateUtils.isValid(preWorkDetailList)) {
                return Constants.NEGATIVE_ONE;
            }
            boolean isNotSameTransferNumber = preWorkDetailList.stream().anyMatch(preWorkDetail -> preWorkDetailList.stream()
                    .anyMatch(preWorkDetailTemp -> preWorkDetail.getTransferNumber() != preWorkDetailTemp.getTransferNumber()));
            return isNotSameTransferNumber ? Constants.NEGATIVE_ONE : preWorkDetailList.get(Constants.INT_ZERO).getTransferNumber();
        }
        // 当前工序为返修工序时，则其投产数=前置所有工序产生的需要返修的不良总数
        if (wsStep.getCategory() == Constants.INT_ONE || wsStep.getCategory() == Constants.INT_TWO) {
            List<UnqualifiedItem> unqualifiedItemList = onlineReworkRuleRepository.findUnqualifiedItemByStepIdAndDeleted(wsStep.getStep().getId(), Constants.LONG_ZERO);
            if (!ValidateUtils.isValid(unqualifiedItemList)) {
                return Constants.INT_ZERO;
            }
            List<WsStep> wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(subWorkSheet.getWorkSheet().getId(), Constants.LONG_ZERO);
            List<WsStep> parentWsStepList = org.apache.commons.compress.utils.Lists.newArrayList();
            commonService.findParentWsStep(wsStepList, parentWsStepList, wsStep);
            if (!ValidateUtils.isValid(parentWsStepList)) {
                return Constants.INT_ZERO;
            }
            List<Long> preStepIdList = parentWsStepList.stream().map(WsStep::getStep).map(Step::getId).collect(Collectors.toList());
            List<Long> unqualifiedItemIdList = unqualifiedItemList.stream().map(UnqualifiedItem::getId).collect(Collectors.toList());
            Long sumNpiNumber = wsStepUnqualifiedItemRepository.sumNumberBySubWorkSheetIdAndStepIdInAndUnqualifiedItemIdInAndDeleted(subWorkSheet.getId(), preStepIdList, unqualifiedItemIdList, Constants.LONG_ZERO);
            return null != sumNpiNumber ? sumNpiNumber.intValue() : Constants.INT_ZERO;
        }
        return Constants.NEGATIVE_ONE;
    }

    /**
     * 获取前置工序的流转数
     *
     * @param subWorkSheet
     * @param wsStep
     * @return int
     * <AUTHOR>
     * @date 2021-01-18
     **/
    public int getPreStepTransferNumber(SubWorkSheet subWorkSheet, WsStep wsStep) {
        List<Long> preStepIds = Arrays.stream(wsStep.getPreStepId().split(Constants.STR_COMMA)).map(Long::parseLong).collect(Collectors.toList());
        List<BatchWorkDetail> preWorkDetailList = batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdInAndDeleted(subWorkSheet.getId(), preStepIds, Constants.LONG_ZERO);
        return ValidateUtils.isValid(preWorkDetailList) ? preWorkDetailList.get(Constants.INT_ZERO).getTransferNumber() : Constants.INT_ZERO;
    }

    /**
     * 扣减使用数量，时间，扣减重置数量，
     *
     * @param clientSaveStepInfoDto 待保存的工序信息
     * @return BaseClientDTO
     */
    public BaseClientDTO saveWearingPart(ClientSaveStepInfoDTO clientSaveStepInfoDto) {
        BaseClientDTO baseClientDto = new BaseClientDTO();
        for (ClientSaveStepInfoDTO.WearingPartInfo wearingPartInfo : clientSaveStepInfoDto.getWearingPartInfoList()) {
            // 根据易损件编码获取易损件的信息
            List<WearingPart> wearingParts = wearingPartRepository.findByCodeAndDeleted(wearingPartInfo.getWearingPartCode(), Constants.LONG_ZERO);
            WearingPart wearingPart = CollectionUtils.isEmpty(wearingParts) ? null : wearingParts.get(Constants.INT_ZERO);
            if (null != wearingPart) {
                // 修改易损件的状态为在用
                wearingPartRepository.updateStatusById(wearingPart.getId(), Constants.INT_ONE);
                //（0手动,1自动)
                // 手动
                if (wearingPart.getResetWay() == WearingPartCategoryEnum.MANUAL.getCategory()) {
                    // 易损件管控类型（0：次数；1：时长；2：有效期；3：时长+次数；4：时长+有效期；5：次数+有效期；6：时长+次数+有效期）
                    // 易损件管控类型为0：次数
                    if (wearingPart.getCategory() == WearingPartCategoryEnum.FREQUENCY.getCategory()) {
                        if (wearingPart.getMaxUseNumber() < wearingPart.getAccumulateUseNumber() + wearingPartInfo.getAbatementNumber()) {
                            return new BaseClientDTO(Constants.KO, PREFIX_MSG + wearingPart.getCode() + SUFFIX_MSG);
                        }
                        wearingPartRepository.updateAccumulateUseNumberById(wearingPart.getAccumulateUseNumber() + wearingPartInfo.getAbatementNumber(), wearingPart.getId());
                        continue;
                    }
                    // 易损件管控类型为1：时长
                    if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION.getCategory()) {
                        Integer currentAccumulateUseTime = wearingPart.getAccumulateUseTime() + Integer.parseInt(String.valueOf(ChronoUnit.SECONDS.between(clientSaveStepInfoDto.getStartTime(), clientSaveStepInfoDto.getEndTime())));
                        if (currentAccumulateUseTime > wearingPart.getMaxUseTime()) {
                            //累计使用时间(大于)最大使用时间 状态变为超期：2
                            wearingPartRepository.updateAccumulateUseTimeAndStatusById(currentAccumulateUseTime, Constants.INT_TWO, wearingPart.getId());
                        } else {
                            wearingPartRepository.updateAccumulateUseTimeById(currentAccumulateUseTime, wearingPart.getId());
                        }
                        continue;
                    }
                    // 易损件管控类型为2：有效期
                    if (wearingPart.getCategory() == WearingPartCategoryEnum.VALIDITY.getCategory()) {
                        if (wearingPart.getExpireDate().isBefore(LocalDateTime.now())) {
                            //不在有效期类 状态变为超期：2
                            wearingPartRepository.updateStatusById(wearingPart.getId(), Constants.INT_TWO);
                        }
                        continue;
                    }
                    // 易损件管控类型为3：时长+次数
                    if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION_FREQUENCY.getCategory()) {
                        if (wearingPart.getMaxUseNumber() < wearingPart.getAccumulateUseNumber() + wearingPartInfo.getAbatementNumber()) {
                            return new BaseClientDTO(Constants.KO, PREFIX_MSG + wearingPart.getCode() + SUFFIX_MSG);
                        }
                        Integer currentAccumulateUseTime = wearingPart.getAccumulateUseTime() + Integer.parseInt(String.valueOf(ChronoUnit.SECONDS.between(clientSaveStepInfoDto.getStartTime(), clientSaveStepInfoDto.getEndTime())));
                        if (currentAccumulateUseTime > wearingPart.getMaxUseTime()) {
                            //累计使用时间(大于)最大使用时间 状态变为超期：2
                            wearingPartRepository.updateAccumulateUseNumberAndAccumulateUseTimeAndStatusById(wearingPart.getAccumulateUseNumber() + wearingPartInfo.getAbatementNumber(), currentAccumulateUseTime, Constants.INT_TWO, wearingPart.getId());
                        } else {
                            wearingPartRepository.updateAccumulateUseNumberAndAccumulateUseTimeById(wearingPart.getAccumulateUseNumber() + wearingPartInfo.getAbatementNumber(), currentAccumulateUseTime, wearingPart.getId());
                        }
                        continue;
                    }
                    // 易损件管控类型为4：时长+有效期
                    if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION_VALIDITY.getCategory()) {
                        Integer currentAccumulateUseTime = wearingPart.getAccumulateUseTime() + Integer.parseInt(String.valueOf(ChronoUnit.SECONDS.between(clientSaveStepInfoDto.getStartTime(), clientSaveStepInfoDto.getEndTime())));
                        if (currentAccumulateUseTime > wearingPart.getMaxUseTime() || wearingPart.getExpireDate().isBefore(LocalDateTime.now())) {
                            //累计使用时间(大于)最大使用时间 状态变为超期：2;
                            wearingPartRepository.updateAccumulateUseTimeAndStatusById(currentAccumulateUseTime, Constants.INT_TWO, wearingPart.getId());
                        } else {
                            wearingPartRepository.updateAccumulateUseTimeById(currentAccumulateUseTime, wearingPart.getId());
                        }
                        continue;
                    }
                    //易损件管控类型为5：次数+有效期
                    if (wearingPart.getCategory() == WearingPartCategoryEnum.FREQUENCY_VALIDITY.getCategory()) {
                        if (wearingPart.getMaxUseNumber() < wearingPart.getAccumulateUseNumber() + wearingPartInfo.getAbatementNumber()) {
                            return new BaseClientDTO(Constants.KO, PREFIX_MSG + wearingPart.getCode() + SUFFIX_MSG);
                        }
                        if (wearingPart.getExpireDate().isBefore(LocalDateTime.now())) {
                            //不在有效期类，状态变为超期：2
                            wearingPartRepository.updateAccumulateUseNumberAndStatusById(wearingPart.getAccumulateUseNumber() + wearingPartInfo.getAbatementNumber(), Constants.INT_TWO, wearingPart.getId());
                        } else {
                            wearingPartRepository.updateAccumulateUseNumberById(wearingPart.getAccumulateUseNumber() + wearingPartInfo.getAbatementNumber(), wearingPart.getId());
                        }
                        continue;
                    }
                    //易损件管控类型为6：时长+次数+有效期
                    if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION_FREQUENCY_VALIDITY.getCategory()) {
                        Integer currentAccumulateUseTime = wearingPart.getAccumulateUseTime() + Integer.parseInt(String.valueOf(ChronoUnit.SECONDS.between(clientSaveStepInfoDto.getStartTime(), clientSaveStepInfoDto.getEndTime())));
                        if (wearingPart.getMaxUseNumber() < wearingPart.getAccumulateUseNumber() + wearingPartInfo.getAbatementNumber()) {
                            return new BaseClientDTO(Constants.KO, PREFIX_MSG + wearingPart.getCode() + SUFFIX_MSG);
                        }
                        if ((currentAccumulateUseTime > wearingPart.getMaxUseTime()) || (wearingPart.getExpireDate().isBefore(LocalDateTime.now()))) {
                            //累计使用时间(大于)最大使用时间&不在有效期类，状态变为超期：2
                            wearingPartRepository.updateAccumulateUseNumberAndAccumulateUseTimeAndStatusById(wearingPart.getAccumulateUseNumber() + wearingPartInfo.getAbatementNumber(), currentAccumulateUseTime, Constants.INT_TWO, wearingPart.getId());
                        } else {
                            wearingPartRepository.updateAccumulateUseNumberAndAccumulateUseTimeById(wearingPart.getAccumulateUseNumber() + wearingPartInfo.getAbatementNumber(), currentAccumulateUseTime, wearingPart.getId());
                        }
                        continue;
                    }
                }
                // 自动
                if (wearingPart.getResetWay() == WearingPartCategoryEnum.AUTO.getCategory()) {
                    // 易损件管控类型（0：次数；1：时长；2：有效期；3：时长+次数；4：时长+有效期；5：次数+有效期；6：时长+次数+有效期）
                    // 易损件管控类型为0：次数
                    if (wearingPart.getCategory() == WearingPartCategoryEnum.FREQUENCY.getCategory()) {
                        if (wearingPart.getAccumulateUseNumber() + wearingPartInfo.getAbatementNumber() > wearingPart.getMaxUseNumber()) {
                            wearingPartRepository.updateAccumulateUseNumberAndAccumulateResetNumberById(Constants.INT_ZERO, wearingPart.getAccumulateResetNumber() + Constants.INT_ONE, wearingPart.getId());
                            if (wearingPart.getMaxResetNumber() < wearingPart.getAccumulateResetNumber() + Constants.INT_ONE) {
                                //累计重置次数(大于)最大重置次数,易损件状态变为报废 ：3
                                wearingPartRepository.updateStatusById(wearingPart.getId(), Constants.INT_THREE);
                            }
                            continue;
                        }
                        wearingPartRepository.updateAccumulateUseNumberById(wearingPart.getAccumulateUseNumber() + wearingPartInfo.getAbatementNumber(), wearingPart.getId());
                    }
                    // 易损件管控类型为1：时长
                    if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION.getCategory()) {
                        Integer currentAccumulateUseTime = wearingPart.getAccumulateUseTime() + Integer.parseInt(String.valueOf(ChronoUnit.SECONDS.between(clientSaveStepInfoDto.getStartTime(), clientSaveStepInfoDto.getEndTime())));
                        if (currentAccumulateUseTime > wearingPart.getMaxUseTime()) {
                            wearingPartRepository.updateAccumulateUseTimeAndAccumulateResetNumberById(Constants.INT_ZERO, wearingPart.getAccumulateResetNumber() + Constants.INT_ONE, wearingPart.getId());
                            if (wearingPart.getMaxResetNumber() < wearingPart.getAccumulateResetNumber() + Constants.INT_ONE) {
                                //累计重置次数(大于)最大重置次数,易损件状态变为报废 ：3
                                wearingPartRepository.updateStatusById(wearingPart.getId(), Constants.INT_THREE);
                            }
                            continue;
                        }
                        wearingPartRepository.updateAccumulateUseTimeById(currentAccumulateUseTime, wearingPart.getId());
                    }
                    // 易损件管控类型为2：有效期
                    if (wearingPart.getCategory() == WearingPartCategoryEnum.VALIDITY.getCategory()) {
                        if (wearingPart.getExpireDate().isBefore(LocalDateTime.now())) {
                            LocalDateTime expireDate = LocalDateTime.now().plus(wearingPart.getWearingPartGroup().getEffectiveDay(), ChronoUnit.DAYS);
                            wearingPartRepository.updateExpireDateAndAccumulateResetNumberById(expireDate, wearingPart.getAccumulateResetNumber() + Constants.INT_ONE, wearingPart.getId());
                        }
                        if (wearingPart.getMaxResetNumber() < wearingPart.getAccumulateResetNumber() + Constants.INT_ONE) {
                            //累计重置次数(大于)最大重置次数,易损件状态变为报废 ：3
                            wearingPartRepository.updateStatusById(wearingPart.getId(), Constants.INT_THREE);
                        }
                    }
                    // 易损件管控类型为3：时长+次数
                    if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION_FREQUENCY.getCategory()) {
                        Integer currentAccumulateUseTime = wearingPart.getAccumulateUseTime() + Integer.parseInt(String.valueOf(ChronoUnit.SECONDS.between(clientSaveStepInfoDto.getStartTime(), clientSaveStepInfoDto.getEndTime())));
                        if (wearingPart.getAccumulateUseNumber() + wearingPartInfo.getAbatementNumber() > wearingPart.getMaxUseNumber() || currentAccumulateUseTime > wearingPart.getMaxUseTime()) {
                            wearingPartRepository.updateAccumulateUseNumberAndAccumulateUseTimeAndAccumulateResetNumberById(Constants.INT_ZERO, Constants.INT_ZERO, wearingPart.getAccumulateResetNumber() + Constants.INT_ONE, wearingPart.getId());
                            if (wearingPart.getMaxResetNumber() < wearingPart.getAccumulateResetNumber() + Constants.INT_ONE) {
                                //累计重置次数(大于)最大重置次数,易损件状态变为报废 ：3
                                wearingPartRepository.updateStatusById(wearingPart.getId(), Constants.INT_THREE);
                            }
                            continue;
                        }
                        wearingPartRepository.updateAccumulateUseNumberAndAccumulateUseTimeById(wearingPart.getAccumulateUseNumber() + wearingPartInfo.getAbatementNumber(), currentAccumulateUseTime, wearingPart.getId());
                    }
                    // 易损件管控类型为4：时长+有效期
                    if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION_VALIDITY.getCategory()) {
                        Integer currentAccumulateUseTime = wearingPart.getAccumulateUseTime() + Integer.parseInt(String.valueOf(ChronoUnit.SECONDS.between(clientSaveStepInfoDto.getStartTime(), clientSaveStepInfoDto.getEndTime())));
                        if (currentAccumulateUseTime > wearingPart.getMaxUseTime() || wearingPart.getExpireDate().isBefore(LocalDateTime.now())) {
                            LocalDateTime expireDate = LocalDateTime.now().plus(wearingPart.getWearingPartGroup().getEffectiveDay(), ChronoUnit.DAYS);
                            wearingPartRepository.updateAccumulateUseTimeAndExpireDateAndAccumulateResetNumberById(Constants.INT_ZERO, expireDate, wearingPart.getAccumulateResetNumber() + Constants.INT_ONE, wearingPart.getId());
                            if (wearingPart.getMaxResetNumber() < wearingPart.getAccumulateResetNumber() + Constants.INT_ONE) {
                                //累计重置次数(大于)最大重置次数,易损件状态变为报废 ：3
                                wearingPartRepository.updateStatusById(wearingPart.getId(), Constants.INT_THREE);
                            }
                            continue;
                        }
                        wearingPartRepository.updateAccumulateUseTimeById(currentAccumulateUseTime, wearingPart.getId());
                    }
                    // 易损件管控类型为5：次数+有效期
                    if (wearingPart.getCategory() == WearingPartCategoryEnum.FREQUENCY_VALIDITY.getCategory()) {
                        if (wearingPart.getAccumulateUseNumber() + wearingPartInfo.getAbatementNumber() > wearingPart.getMaxUseNumber() || wearingPart.getExpireDate().isBefore(LocalDateTime.now())) {
                            LocalDateTime expireDate = LocalDateTime.now().plus(wearingPart.getWearingPartGroup().getEffectiveDay(), ChronoUnit.DAYS);
                            wearingPartRepository.updateAccumulateUseNumberAndExpireDateAndAccumulateResetNumberById(Constants.INT_ZERO, expireDate, wearingPart.getAccumulateResetNumber() + Constants.INT_ONE, wearingPart.getId());
                            if (wearingPart.getMaxResetNumber() < wearingPart.getAccumulateResetNumber() + Constants.INT_ONE) {
                                //累计重置次数(大于)最大重置次数,易损件状态变为报废 ：3
                                wearingPartRepository.updateStatusById(wearingPart.getId(), Constants.INT_THREE);
                            }
                            continue;
                        }
                        wearingPartRepository.updateAccumulateUseNumberById(wearingPart.getAccumulateUseNumber() + wearingPartInfo.getAbatementNumber(), wearingPart.getId());
                    }
                    // 易损件管控类型为6：时长+次数+有效期
                    if (wearingPart.getCategory() == WearingPartCategoryEnum.DURATION_FREQUENCY_VALIDITY.getCategory()) {
                        Integer currentAccumulateUseTime = wearingPart.getAccumulateUseTime() + Integer.parseInt(String.valueOf(ChronoUnit.SECONDS.between(clientSaveStepInfoDto.getStartTime(), clientSaveStepInfoDto.getEndTime())));
                        if (wearingPart.getAccumulateUseNumber() + wearingPartInfo.getAbatementNumber() > wearingPart.getMaxUseNumber() || currentAccumulateUseTime > wearingPart.getMaxUseTime() || wearingPart.getExpireDate().isBefore(LocalDateTime.now())) {
                            LocalDateTime expireDate = LocalDateTime.now().plus(wearingPart.getWearingPartGroup().getEffectiveDay(), ChronoUnit.DAYS);
                            wearingPartRepository.updateAccumulateUseNumberAndAccumulateUseTimeAndExpireDateAndAccumulateResetNumberById(Constants.INT_ZERO, Constants.INT_ZERO, expireDate, wearingPart.getAccumulateResetNumber() + Constants.INT_ONE, wearingPart.getId());
                            if (wearingPart.getMaxResetNumber() < wearingPart.getAccumulateResetNumber() + Constants.INT_ONE) {
                                //累计重置次数(大于)最大重置次数,易损件状态变为报废 ：3
                                wearingPartRepository.updateStatusById(wearingPart.getId(), Constants.INT_THREE);
                            }
                            continue;
                        }
                        wearingPartRepository.updateAccumulateUseNumberAndAccumulateUseTimeById(wearingPart.getAccumulateUseNumber() + wearingPartInfo.getAbatementNumber(), currentAccumulateUseTime, wearingPart.getId());
                    }
                }
            }
        }
        return baseClientDto.setStatus(Constants.OK);
    }

    /**
     * 当前容器是当前工序的最后一个容器下交，且合格数为0
     * 修改后置工序详情的状态以及待流转数
     *
     * @param batchWorkDetail 当前批次信息
     * @return void
     * <AUTHOR>
     * @date 2022/5/11
     */
    public void updateBatchDetail(BatchWorkDetail batchWorkDetail) {
        //获取当前工单工序的后置工序(未完成，并非未开始工序)
        Step currentStep = batchWorkDetail.getStep();
        List<WsStep> wsStepList = wsStepRepository.findBySubWorkSheetIdAndDeleted(batchWorkDetail.getSubWorkSheet().getId(), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(wsStepList)) {
            wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(batchWorkDetail.getSubWorkSheet().getWorkSheet().getId(), Constants.LONG_ZERO);
        }
        if (ValidateUtils.isValid(wsStepList)) {
            updateAfterBatchDetail(batchWorkDetail, currentStep, wsStepList);
        }
    }

    /**
     * 递归的修改后置工序详情的状态以及待流转数
     *
     * @param currentBatchWorkDetail 当前工序详情
     * @param currentStep            当前工序
     * @param wsStepList             （子）工单的工序快照
     * @return void
     * <AUTHOR>
     * @date 2022/5/11
     */
    private void updateAfterBatchDetail(BatchWorkDetail currentBatchWorkDetail, Step currentStep, List<WsStep> wsStepList) {
        //获取当前工序快照配置
        WsStep wsStep = wsStepList.stream().filter(step -> step.getStep().getId().equals(currentStep.getId())).findFirst().orElse(null);
        //如果不存在后置工序说明为最后一道工序
        if (wsStep != null && ValidateUtils.isValid(wsStep.getAfterStepId())) {
            //存在分叉工序
            List<Long> afterStepIds = Arrays.stream(wsStep.getAfterStepId().split(Constants.STR_COMMA)).map(Long::parseLong).collect(Collectors.toList());
            List<Step> afterSteps = stepRepository.findByIdInAndDeleted(afterStepIds, Constants.LONG_ZERO);
            afterSteps.forEach(afterStep -> {
                Optional<BatchWorkDetail> afterBatchWorkDetailOptional = batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(currentBatchWorkDetail.getSubWorkSheet().getId(), afterStep.getId(), Constants.LONG_ZERO);
                //不存在工序详情说明下道工序还未开始做
                if (afterBatchWorkDetailOptional.isPresent()) {
                    BatchWorkDetail afterBatchWorkDetail = afterBatchWorkDetailOptional.get();
                    //当前工序详情的待流转数等于下一道工序的完成数量 说明下道工序已完成
                    if (currentBatchWorkDetail.getTransferNumber() == afterBatchWorkDetail.getFinishNumber()) {
                        afterBatchWorkDetail.setFinish(ConstantsEnum.FINISH_STATUS.getCategoryName())
                                .setEndDate(LocalDateTime.now())
                                .setTransferNumber(afterBatchWorkDetail.getQualifiedNumber());
                        BatchWorkDetail finalBatchWorkDetail = batchWorkDetailRepository.save(afterBatchWorkDetail);
                        updateAfterBatchDetail(finalBatchWorkDetail, afterStep, wsStepList);
                    }
                }
            });
        } else {
            if (ConstantsEnum.FINISH_STATUS.getCategoryName() == currentBatchWorkDetail.getFinish()) {
                //没有后置工序说明是最后一道工序，需要更新（子）工单相关状态
                finishSubWorkSheet(currentBatchWorkDetail, currentBatchWorkDetail.getSubWorkSheet());
            }
            return;
        }
    }

    /**
     * @param clientSaveStepInfoDto      请求保存容器工序参数
     * @param preStepContainerDetailList 前置容器工序详情列表
     * @return void
     * <AUTHOR>
     * @date 2021-08-27
     **/
    public void updatePreStepContainerDetailInfo(ClientSaveStepInfoDTO clientSaveStepInfoDto, List<ContainerDetail> preStepContainerDetailList) {
        List<ClientSaveStepInfoDTO.RequestContainerInfo> requestContainerInfos = clientSaveStepInfoDto.getRequestContainerInfos();
        int finishNumber = clientSaveStepInfoDto.getNumber();
        for (ContainerDetail preContainerDetail : preStepContainerDetailList) {
            List<SnWorkDetail> preStepSnWorkDetailList = snWorkDetailRepository.findByContainerDetailIdAndDeleted(preContainerDetail.getId(), Constants.LONG_ZERO);
            if (ValidateUtils.isValid(preStepSnWorkDetailList) && ValidateUtils.isValid(clientSaveStepInfoDto.getSnInfoList())) {
                //根据RWorker传递的snInfoList中的sn集合,与当前容器绑定的sn比对 获取待扣减数量
                long number = clientSaveStepInfoDto.getSnInfoList().stream().filter(snInfo -> preStepSnWorkDetailList.stream().map(SnWorkDetail::getSn).collect(Collectors.toList()).stream().anyMatch(preStepSn -> snInfo.getSn().equals(preStepSn))).count();
                preContainerDetail.setTransferNumber(preContainerDetail.getTransferNumber() - (int) number);
                preContainerDetail.setStatus(preContainerDetail.getTransferNumber() == Constants.INT_ZERO ? Constants.INT_ZERO : Constants.INT_ONE);
                preContainerDetail.setUnbindTime(preContainerDetail.getTransferNumber() == Constants.INT_ZERO ? LocalDateTime.now() : null);
                preContainerDetail.setAfterContainerCodeList(null == preContainerDetail.getAfterContainerCodeList() ? containerRepository.getReferenceById(clientSaveStepInfoDto.getBindContainerId()).getCode() : preContainerDetail.getAfterContainerCodeList() + Constants.STR_SEMICOLON + containerRepository.getReferenceById(clientSaveStepInfoDto.getBindContainerId()).getCode());
                finishNumber = finishNumber - (int) number;
            } else if (finishNumber > Constants.INT_ZERO) {
                //前置容器在当前工序待下交数量信息
                ClientSaveStepInfoDTO.RequestContainerInfo requestContainerInfo = requestContainerInfos.stream().filter(requestContainer -> requestContainer.getContainerId().equals(preContainerDetail.getContainer().getId())).findFirst().orElseThrow(() -> new ResponseException("error.containerNotExist", "容器不存在"));
                int number = requestContainerInfo.getNumber();
                preContainerDetail.setTransferNumber(preContainerDetail.getTransferNumber() == number ? Constants.INT_ZERO : preContainerDetail.getTransferNumber() - number);
                preContainerDetail.setStatus(preContainerDetail.getTransferNumber() == Constants.INT_ZERO ? Constants.INT_ZERO : Constants.INT_ONE);
                preContainerDetail.setUnbindTime(preContainerDetail.getTransferNumber() == Constants.INT_ZERO ? LocalDateTime.now() : null);
                preContainerDetail.setAfterContainerCodeList(null == preContainerDetail.getAfterContainerCodeList() ? containerRepository.getReferenceById(clientSaveStepInfoDto.getBindContainerId()).getCode() : preContainerDetail.getAfterContainerCodeList() + Constants.STR_SEMICOLON + containerRepository.getReferenceById(clientSaveStepInfoDto.getBindContainerId()).getCode());
                finishNumber = finishNumber > number ? finishNumber - number : Constants.INT_ZERO;
            }
            if (preContainerDetail.getTransferNumber() < Constants.INT_ZERO) {
                throw new NumberDeductException("原容器(" + preContainerDetail.getContainerCode() + ")待下交数不够扣减");
            }
            containerDetailRepository.save(preContainerDetail);
        }
    }

    /**
     * 保存SN不良信息
     *
     * @param subWorkSheet 子工单
     * @param step         工序
     * @param snInfo       SN信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveSnUnqualifiedItemInfo(SubWorkSheet subWorkSheet, Step step, ClientSaveStepInfoDTO.SnInfo snInfo, SnWorkDetail snWorkDetail) {
        Optional<UnqualifiedItem> unqualifiedItemOptional = unqualifiedItemRepository
                .findByIdAndDeleted(snInfo.getUnqualifiedItemId(), Constants.LONG_ZERO);
        if (unqualifiedItemOptional.isPresent()) {
            SnUnqualifiedItem snUnqualifiedItem = new SnUnqualifiedItem();
            snUnqualifiedItem.setSn(snInfo.getSn())
                    .setSubWorkSheet(subWorkSheet)
                    .setStep(step)
                    .setUnqualifiedItem(unqualifiedItemOptional.get())
                    .setSnWorkDetail(snWorkDetail);
            snUnqualifiedItemRepository.save(snUnqualifiedItem);
        }
    }

    /**
     * 根据批次扣减严格扣减物料
     *
     * @param workCell                 工位
     * @param workSheet                工单
     * @param pedigreeStepMaterialRule 上料规则
     * @param materialBatchInfoList    上料信息
     * @param materialControlLevel     管控级别
     * @return void
     * <AUTHOR>
     * @date 2022/9/5
     */
    public void subtractMaterialBatchOnlineInventory(WorkCell workCell, WorkSheet workSheet, PedigreeStepMaterialRule pedigreeStepMaterialRule, List<ClientSaveStepInfoDTO.MaterialBatchInfo> materialBatchInfoList, int materialControlLevel) {
        //获取当前上料规则需要扣减数量
        materialBatchInfoList = materialBatchInfoList.stream().filter(materialBatchInfo -> materialBatchInfo.getMaterialId().equals(pedigreeStepMaterialRule.getMaterialId())).collect(Collectors.toList());
        if (!ValidateUtils.isValid(materialBatchInfoList)) {
            return;
        }
        //工单库存（根据批次）
        if (materialControlLevel == ConstantsEnum.MATERIAL_WORKSHEET_CONTROL_LEVEL.getCategoryName()) {
            materialBatchInfoList.forEach(materialBatchInfo -> {
                Optional<WsMaterialBatch> wsMaterialBatchOptional = wsMaterialBatchRepository.findByWorkSheetIdAndMaterialIdAndBatchAndDeleted(workSheet.getId(), materialBatchInfo.getMaterialId(), materialBatchInfo.getMaterialBatch(), Constants.LONG_ZERO);
                if (wsMaterialBatchOptional.isPresent()) {
                    WsMaterialBatch wsMaterialBatch = wsMaterialBatchOptional.get();
                    if (wsMaterialBatch.getLeftNumber() >= materialBatchInfo.getNumber()) {
                        wsMaterialBatchRepository.save(wsMaterialBatch.setLeftNumber(wsMaterialBatch.getLeftNumber() - materialBatchInfo.getNumber()));
                    }
                }
            });
        }

        //工单工位库存（根据批次）
        if (materialControlLevel == ConstantsEnum.MATERIAL_WORK_CELL_CONTROL_LEVEL.getCategoryName()) {
            materialBatchInfoList.forEach(materialBatchInfo -> {
                Optional<WsWorkCellMaterialBatch> wsWorkCellMaterialBatchOptional = wsWorkCellMaterialBatchRepository.findByWorkSheetIdAndWorkCellIdAndMaterialIdAndBatchAndDeleted(workSheet.getId(), workCell.getId(), materialBatchInfo.getMaterialId(), materialBatchInfo.getMaterialBatch(), Constants.LONG_ZERO);
                if (wsWorkCellMaterialBatchOptional.isPresent()) {
                    WsWorkCellMaterialBatch wsWorkCellMaterialBatch = wsWorkCellMaterialBatchOptional.get();
                    if (wsWorkCellMaterialBatch.getLeftNumber() >= materialBatchInfo.getNumber()) {
                        wsWorkCellMaterialBatchRepository.save(wsWorkCellMaterialBatch.setLeftNumber(wsWorkCellMaterialBatch.getLeftNumber() - materialBatchInfo.getNumber()));
                    }
                }
            });
        }
    }


    /**
     * 不根据批次扣减（直接按大到小依次扣减）
     *
     * @param workCell              工位
     * @param workSheet             工单
     * @param wsMaterialList        工单投料单
     * @param materialBatchInfoList 上料信息
     * @param reduceNumber          投产数
     * @param materialControlLevel  管控层级(扣料)
     * @return void
     * <AUTHOR>
     * @date 2022/9/5
     */
    public void subtractMaterialOnlineInventory(WorkCell workCell, WorkSheet workSheet, List<WsMaterial> wsMaterialList, List<ClientSaveStepInfoDTO.MaterialBatchInfo> materialBatchInfoList, AtomicReference<Double> reduceNumber, int materialControlLevel) {
        //工单库存
        if (materialControlLevel == ConstantsEnum.MATERIAL_WORKSHEET_CONTROL_LEVEL.getCategoryName()) {
            List<WsMaterialBatch> wsMaterialBatchList = wsMaterialBatchRepository.findByWorkSheetIdAndMaterialIdInAndLeftNumberIsGreaterThanAndDeleted(workSheet.getId(), wsMaterialList.stream().map(WsMaterial::getMaterialId).collect(Collectors.toList()), Constants.DOUBLE_ZERRO, Constants.LONG_ZERO);
            if (ValidateUtils.isValid(wsMaterialBatchList)) {
                List<WsMaterialBatch> sortedWsMaterialBatchList = wsMaterialBatchList.stream().sorted(Comparator.comparing(WsMaterialBatch::getLeftNumber)).collect(Collectors.toList());
                //获取扫描的批次与工单批次库存匹配的集合数据,扣减员工扫描的物料批次的剩余库存
                List<WsMaterialBatch> matchedWsMaterialBatchList = sortedWsMaterialBatchList.stream().filter(wsMaterialBatch -> materialBatchInfoList.stream().anyMatch(materialBatchInfo -> materialBatchInfo.getMaterialId().equals(wsMaterialBatch.getMaterialId()) && materialBatchInfo.getMaterialBatch().equals(wsMaterialBatch.getBatch()))).collect(Collectors.toList());
                matchedWsMaterialBatchList.forEach(wsMaterialBatch -> {
                    if (wsMaterialBatch.getLeftNumber() > reduceNumber.get()) {
                        wsMaterialBatch.setLeftNumber(NumberUtils.subtract(wsMaterialBatch.getLeftNumber(), reduceNumber.get()).doubleValue());
                        reduceNumber.set(Constants.DOUBLE_ZERRO);
                    } else {
                        reduceNumber.set(NumberUtils.subtract(reduceNumber.get(), wsMaterialBatch.getLeftNumber()).doubleValue());
                        wsMaterialBatch.setLeftNumber(Constants.DOUBLE_ZERRO);
                    }
                    wsMaterialBatchRepository.save(wsMaterialBatch);
                });
            }
        }

        //工单工位库存
        if (materialControlLevel == ConstantsEnum.MATERIAL_WORK_CELL_CONTROL_LEVEL.getCategoryName()) {
            List<WsWorkCellMaterialBatch> wsWorkCellMaterialBatchList = wsWorkCellMaterialBatchRepository.findByWorkSheetIdAndWorkCellIdAndMaterialIdInAndLeftNumberGreaterThanAndDeleted(workSheet.getId(), workCell.getId(),
                    wsMaterialList.stream().map(WsMaterial::getMaterialId).collect(Collectors.toList()), Constants.DOUBLE_ZERRO, Constants.LONG_ZERO);
            if (ValidateUtils.isValid(wsWorkCellMaterialBatchList)) {
                //将库存数据按照从小到大的顺序排序
                List<WsWorkCellMaterialBatch> sortedWsWorkCellMaterialBatchList = wsWorkCellMaterialBatchList.stream().sorted(Comparator.comparing(WsWorkCellMaterialBatch::getLeftNumber)).collect(Collectors.toList());
                sortedWsWorkCellMaterialBatchList.forEach(wsWorkCellMaterialBatch -> {
                    if (wsWorkCellMaterialBatch.getLeftNumber() > reduceNumber.get()) {
                        wsWorkCellMaterialBatch.setLeftNumber(NumberUtils.subtract(wsWorkCellMaterialBatch.getLeftNumber(), reduceNumber.get()).doubleValue());
                        reduceNumber.set(Constants.DOUBLE_ZERRO);
                    } else {
                        reduceNumber.set(NumberUtils.subtract(reduceNumber.get(), wsWorkCellMaterialBatch.getLeftNumber()).doubleValue());
                        wsWorkCellMaterialBatch.setLeftNumber(Constants.DOUBLE_ZERRO);
                    }
                    wsWorkCellMaterialBatchRepository.save(wsWorkCellMaterialBatch);
                });
            }
        }

    }


    /**
     * 验证工位在当前工单上的剩余物料是否足够
     *
     * @param workCell       工位
     * @param workSheet      总工单
     * @param step           工序
     * @param finishedNumber 工单完成数
     * @return BaseClientDTO
     * <AUTHOR>
     * @date 2021-01-18
     **/
    @Transactional(readOnly = true)
    public BaseClientDTO validateMaterialInventory(WorkCell workCell, WorkSheet workSheet, Step step, int finishedNumber, List<MaterialBatchInfo> materialBatchInfoList) {
        AtomicReference<BaseClientDTO> baseClientDto = new AtomicReference<>(new BaseClientDTO(Constants.OK));
        //工序生产过程物料库存管控级别(0:不管控物料库存;1:总工单物料库存;2:工位物料库存)
        int materialControlLevel = commonService.getMaterialControlLevel();
        if (ConstantsEnum.MATERIAL_NOT_CONTROL_LEVEL.getCategoryName() == materialControlLevel) {
            return baseClientDto.get();
        }
        // 获取工单地投料单
        List<WsMaterial> wsMaterialList = wsMaterialRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        //获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = commonService.findSnapshotWorkFlow(workSheet, null, step);
        // 获取上料规则
        List<PedigreeStepMaterialRule> pedigreeStepMaterialRuleList = commonService.findPedigreeStepMaterialRule(workSheet.getClientId(), workSheet.getPedigree().getId(), snapshotWorkFlow.getId(), step.getId());
        if (!ValidateUtils.isValid(pedigreeStepMaterialRuleList)) {
            return new BaseClientDTO(Constants.OK);
        }
        // key为上料规则的ID，value 为属于上料规则的物料批次列表
        Map<Long, List<MaterialBatchInfo>> materialBatchInfoMap = new HashMap<>();
        materialBatchInfoList.forEach(materialBatchInfo -> {
            // 获取上料规则
            PedigreeStepMaterialRule pedigreeStepMaterialRule = pedigreeStepMaterialRuleList.stream().filter(materialRule -> {
                Optional<WsMaterial> wsMaterialOptional = wsMaterialList.stream().filter(wsMaterial -> Objects.equals(wsMaterial.getMaterialId(), materialBatchInfo.getMaterialId())).findFirst();
                return wsMaterialOptional.filter(wsMaterial -> Objects.equals(materialRule.getMaterialId(), wsMaterial.getOriginMaterialId())).isPresent();
            }).findFirst().orElse(null);
            if (pedigreeStepMaterialRule == null) {
                return;
            }
            if (materialBatchInfoMap.containsKey(pedigreeStepMaterialRule.getId())) {
                materialBatchInfoMap.get(pedigreeStepMaterialRule.getId()).add(materialBatchInfo);
            } else {
                materialBatchInfoMap.put(pedigreeStepMaterialRule.getId(), Lists.newArrayList(materialBatchInfo));
            }
        });
        //计算剩余数量，两种 方式：
        // ① 核批次 根据物料 以及 物料批次 获取当前物料批次的剩余数量进行验证
        // ② 不核批次 根据物料 获取当前物料的所有批次进行剩余数量统计 进行验证
        materialBatchInfoMap.forEach((pedigreeStepMaterialRuleId, materialBatchInfos) -> {
            if (Constants.KO.equals(baseClientDto.get().getStatus())) {
                return;
            }
            pedigreeStepMaterialRuleList.stream().filter(pedigreeStepMaterialRule -> Objects.equals(pedigreeStepMaterialRule.getId(), pedigreeStepMaterialRuleId)).findFirst().ifPresent(pedigreeStepMaterialRule -> {
                //不扣料
                if (!pedigreeStepMaterialRule.getIsDeduct()) {
                    return;
                }
                //当前工序需要物料数量
                double reduceNumber = NumberUtils.multiply(finishedNumber, pedigreeStepMaterialRule.getProportion()).doubleValue();
                //是否核批次
                if (pedigreeStepMaterialRule.getIsCheckMaterialBatch()) {
                    baseClientDto.set(validateIsCheckMaterialBatch(workSheet, workCell, materialControlLevel, pedigreeStepMaterialRule, materialBatchInfos, reduceNumber));
                } else {
                    baseClientDto.set(validateIsNotCheckMaterialBatch(workSheet, workCell, materialControlLevel, pedigreeStepMaterialRule, materialBatchInfos, reduceNumber));
                }
            });
        });
        return baseClientDto.get();
    }

    /**
     * 核批次验证扣料数量
     *
     * @param workSheet                工单
     * @param workCell                 工位
     * @param materialControlLevel     扣料层级
     * @param pedigreeStepMaterialRule 上料规则
     * @param materialBatchInfoList    上料信息
     * @param reduceNumber             当前工序所需物料数量
     * @return BaseClientDTO
     * <AUTHOR>
     * @date 2022/9/2
     */
    public BaseClientDTO validateIsCheckMaterialBatch(WorkSheet workSheet, WorkCell workCell, Integer materialControlLevel, PedigreeStepMaterialRule pedigreeStepMaterialRule,
                                                      List<MaterialBatchInfo> materialBatchInfoList, Double reduceNumber) {
        BaseClientDTO baseClientDto = new BaseClientDTO(Constants.OK);
        List<WsMaterialBatch> wsMaterialBatchList = Lists.newArrayList();
        List<WsWorkCellMaterialBatch> wsWorkCellMaterialBatchList = Lists.newArrayList();
        if (workSheet.getCategory() == WsEnum.ONLINE_RE_WS.getCategory()) {
            WsRework wsRework = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO).orElse(null);
            if (null != wsRework) {
                workSheet = wsRework.getOriginalWorkSheet();
            }
        }
        //验证当前批次是否足量
        for (MaterialBatchInfo materialBatchInfo : materialBatchInfoList) {
            //工单扣料
            if (ConstantsEnum.MATERIAL_WORKSHEET_CONTROL_LEVEL.getCategoryName() == materialControlLevel) {
                Optional<WsMaterialBatch> wsMaterialBatchOptional = wsMaterialBatchRepository.findByWorkSheetIdAndMaterialIdAndBatchAndDeleted(workSheet.getId(), materialBatchInfo.getMaterialId(), materialBatchInfo.getMaterialBatch(), Constants.LONG_ZERO);
                if (!wsMaterialBatchOptional.isPresent()) {
                    baseClientDto.setStatus(Constants.KO).setMessage("工单库存：没有对应的物料(" + pedigreeStepMaterialRule.getMaterialDto().getName() + ")," + "批次[" + materialBatchInfo.getMaterialBatch() + "]" + "扣减!");
                    return baseClientDto;
                }
                WsMaterialBatch wsMaterialBatch = wsMaterialBatchOptional.get();
                if (NumberUtils.compare(String.valueOf(materialBatchInfo.getNumber()), String.valueOf(wsMaterialBatch.getLeftNumber()))) {
                    baseClientDto.setStatus(Constants.KO).setMessage(WS_MATERIAL_BATCH_MSG + pedigreeStepMaterialRule.getMaterialDto().getName() + ")," + "批次[" + materialBatchInfo.getMaterialBatch() + "]" + "数量不够扣减!");
                    return baseClientDto;
                }
                wsMaterialBatchList.add(wsMaterialBatchOptional.get());
            }
            //工位扣料
            if (ConstantsEnum.MATERIAL_WORK_CELL_CONTROL_LEVEL.getCategoryName() == materialControlLevel) {
                Optional<WsWorkCellMaterialBatch> wsWorkCellMaterialBatchOptional = wsWorkCellMaterialBatchRepository.findByWorkSheetIdAndWorkCellIdAndMaterialIdAndBatchAndDeleted(workSheet.getId(), workCell.getId(), materialBatchInfo.getMaterialId(), materialBatchInfo.getMaterialBatch(), Constants.LONG_ZERO);
                if (!wsWorkCellMaterialBatchOptional.isPresent()) {
                    baseClientDto.setStatus(Constants.KO).setMessage("工单工位库存：没有对应的物料(" + pedigreeStepMaterialRule.getMaterialDto().getName() + ")," + "批次[" + materialBatchInfo.getMaterialBatch() + "]" + "扣减!");
                    return baseClientDto;
                }
                WsWorkCellMaterialBatch wsWorkCellMaterialBatch = wsWorkCellMaterialBatchOptional.get();
                if (NumberUtils.compare(String.valueOf(materialBatchInfo.getNumber()), String.valueOf(wsWorkCellMaterialBatch.getLeftNumber()))) {
                    baseClientDto.setStatus(Constants.KO).setMessage(WS_WORK_CELL_MATERIAL_BATCH_MSG + pedigreeStepMaterialRule.getMaterialDto().getName() + ")," + "批次[" + materialBatchInfo.getMaterialBatch() + "]" + "数量不够扣减!");
                    return baseClientDto;
                }
                wsWorkCellMaterialBatchList.add(wsWorkCellMaterialBatch);
            }
        }
        //总（同一物料不同批次累加）：当前物料是否足量(工单库存)
        if (ConstantsEnum.MATERIAL_WORKSHEET_CONTROL_LEVEL.getCategoryName() == materialControlLevel && NumberUtils.compare(String.valueOf(reduceNumber), String.valueOf(wsMaterialBatchList.stream().mapToDouble(WsMaterialBatch::getLeftNumber).sum()))) {
            baseClientDto.setStatus(Constants.KO).setMessage(WS_MATERIAL_BATCH_MSG + pedigreeStepMaterialRule.getMaterialDto().getName() + NUMBER_NOT_ENOUGH);
            return baseClientDto;
        }
        //总（同一物料不同批次累加）：当前物料是否足量(工单工位库存)
        if (ConstantsEnum.MATERIAL_WORK_CELL_CONTROL_LEVEL.getCategoryName() == materialControlLevel && NumberUtils.compare(String.valueOf(reduceNumber), String.valueOf(wsWorkCellMaterialBatchList.stream().mapToDouble(WsWorkCellMaterialBatch::getLeftNumber).sum()))) {
            baseClientDto.setStatus(Constants.KO).setMessage(WS_WORK_CELL_MATERIAL_BATCH_MSG + pedigreeStepMaterialRule.getMaterialDto().getName() + NUMBER_NOT_ENOUGH);
        }
        return baseClientDto;
    }

    /**
     * 不核批次验证扣料数量
     *
     * @param workSheet                工单
     * @param workCell                 工位
     * @param materialControlLevel     扣料层级
     * @param pedigreeStepMaterialRule 上料规则
     * @param materialBatchInfoList    上料信息
     * @param reduceNumber             当前工序所需物料数量
     * @return BaseClientDTO
     * <AUTHOR>
     * @date 2022/9/2
     */
    public BaseClientDTO validateIsNotCheckMaterialBatch(WorkSheet workSheet, WorkCell workCell, Integer materialControlLevel, PedigreeStepMaterialRule pedigreeStepMaterialRule,
                                                         List<MaterialBatchInfo> materialBatchInfoList, Double reduceNumber) {
        BaseClientDTO baseClientDto = new BaseClientDTO(Constants.OK);

        double planNumber = materialBatchInfoList.stream().mapToDouble(MaterialBatchInfo::getNumber).sum();
        if (NumberUtils.compare(String.valueOf(reduceNumber), String.valueOf(planNumber))) {
            baseClientDto.setStatus(Constants.KO).setMessage("下交数量小于计划数量：物料(" + pedigreeStepMaterialRule.getMaterialDto().getName() + ")");
            return baseClientDto;
        }
        if (workSheet.getCategory() == WsEnum.ONLINE_RE_WS.getCategory()) {
            WsRework wsRework = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO).orElse(null);
            if (null != wsRework) {
                workSheet = wsRework.getOriginalWorkSheet();
            }
        }
        List<Long> materialIds = materialBatchInfoList.stream().map(MaterialBatchInfo::getMaterialId).collect(Collectors.toList());
        //总（同一物料不同批次累加）：当前物料是否足量(工单库存)
        if (ConstantsEnum.MATERIAL_WORKSHEET_CONTROL_LEVEL.getCategoryName() == materialControlLevel) {
            BigDecimal sumLeftNumber = wsMaterialBatchRepository.sumLeftNumberByWorkSheetIdAndMaterialIdInAndDeleted(workSheet.getId(), materialIds, Constants.LONG_ZERO);
            if (sumLeftNumber == null) {
                baseClientDto.setStatus(Constants.KO).setMessage("工单库存：没有对应的物料(" + pedigreeStepMaterialRule.getMaterialDto().getName() + ")扣减!");
                return baseClientDto;
            }
            if (NumberUtils.compare(String.valueOf(reduceNumber), String.valueOf(sumLeftNumber.doubleValue()))) {
                baseClientDto.setStatus(Constants.KO).setMessage(WS_MATERIAL_BATCH_MSG + pedigreeStepMaterialRule.getMaterialDto().getName() + NUMBER_NOT_ENOUGH);
                return baseClientDto;
            }
        }
        //总（同一物料不同批次累加）：当前物料是否足量(工单工位库存)
        if (ConstantsEnum.MATERIAL_WORK_CELL_CONTROL_LEVEL.getCategoryName() == materialControlLevel) {
            List<WsWorkCellMaterialBatch> wsWorkCellMaterialBatchList = wsWorkCellMaterialBatchRepository.findByWorkSheetIdAndWorkCellIdAndMaterialIdInAndLeftNumberGreaterThanAndDeleted(workSheet.getId(), workCell.getId(), materialIds, Constants.DOUBLE_ZERRO, Constants.LONG_ZERO);
            if (!ValidateUtils.isValid(wsWorkCellMaterialBatchList)) {
                baseClientDto.setStatus(Constants.KO).setMessage("工单工位库存：没有对应的物料(" + pedigreeStepMaterialRule.getMaterialDto().getName() + ")扣减!");
                return baseClientDto;
            }
            if (NumberUtils.compare(String.valueOf(reduceNumber), String.valueOf(wsWorkCellMaterialBatchList.stream().mapToDouble(WsWorkCellMaterialBatch::getLeftNumber).sum()))) {
                baseClientDto.setStatus(Constants.KO).setMessage(WS_WORK_CELL_MATERIAL_BATCH_MSG + pedigreeStepMaterialRule.getMaterialDto().getName() + NUMBER_NOT_ENOUGH);
                return baseClientDto;
            }
        }

        return baseClientDto;
    }

    /**
     * 获取子工单未开工信息
     *
     * @return List<SubWorkSheet>
     * <AUTHOR>
     * @date 2021/10/19
     */
    public ClientStepSubWsInfoDTO getSubWorkSheetStepInfo(String serialNumber) {
        SubWorkSheet subWorkSheet = subWorkSheetRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO).orElse(null);

        if (subWorkSheet == null) {
            return new ClientStepSubWsInfoDTO(new BaseClientDTO(Constants.KO, "子工单不存在"));
        }
        BaseClientDTO baseClientDto = this.checkSubWs(subWorkSheet);
        if (Constants.KO.equals(baseClientDto.getStatus())) {
            return new ClientStepSubWsInfoDTO(baseClientDto);
        }
        //通过实际开工时间来推算子工单是否以完成第一个工序actualStartDate
        if (subWorkSheet.getActualStartDate() != null) {
            return new ClientStepSubWsInfoDTO(new BaseClientDTO(Constants.KO, "子工单已开工"));
        }
        ClientStepSubWsInfoDTO clientStepSubWsInfoDto = net.airuima.rbase.util.MapperUtils.map(subWorkSheet, ClientStepSubWsInfoDTO.class);
        clientStepSubWsInfoDto.setStatus(Constants.OK);
        return clientStepSubWsInfoDto;
    }

    /**
     * 获取子工单列表
     *
     * @param workWellId 工位id
     * @return List<String>
     * <AUTHOR>
     * @date 2021/10/19
     */
    public List<String> getSubWorkSheetInfo(Long workWellId) {

        WorkCell workCell = workCellRepository.findByIdAndDeleted(workWellId, Constants.LONG_ZERO).orElse(null);
        //通过工位获取到对应的多个工序
        List<Step> stepList = workCellStepRepository.findByWorkCellId(workWellId, Constants.LONG_ZERO);
        if (null == workCell && !ValidateUtils.isValid(stepList)) {
            return Collections.emptyList();
        }
        List<SubWorkSheet> subWorkSheetList = new ArrayList<>();

        if (null != workCell && null != workCell.getWorkLine()) {
            subWorkSheetList = subWorkSheetRepository.findByStatusAndWorkLineIdAndDeleted(Constants.FINISH, workCell.getWorkLine().getId(), Constants.LONG_ZERO);
        }
        if (!ValidateUtils.isValid(subWorkSheetList) && null != workCell && null != workCell.getOrganizationId()) {
            subWorkSheetList = subWorkSheetRepository.findByStatusAndDeletedAndWorkSheetOrganizationId(Constants.FINISH, workCell.getOrganizationId(), Constants.LONG_ZERO);
        }
        if (!ValidateUtils.isValid(subWorkSheetList)) {
            subWorkSheetList = subWorkSheetRepository.findByStatusLessThanAndDeleted(Constants.FINISH, Constants.LONG_ZERO);
        }
        if (!ValidateUtils.isValid(subWorkSheetList)) {
            return Collections.emptyList();
        }
        List<SubWorkSheet> subWorkSheets = new ArrayList<>();
        //过滤掉不合法的子工单
        subWorkSheetList = subWorkSheetList.stream().filter(subWorkSheet -> Constants.OK.equals(this.checkSubWs(subWorkSheet).getStatus())).collect(Collectors.toList());
        if (ValidateUtils.isValid(subWorkSheetList)) {
            subWorkSheetList.forEach(subWorkSheet -> {
                //获取当前工单对应的工序快照
                List<WsStep> wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(subWorkSheet.getWorkSheet().getId(), Constants.LONG_ZERO);
                //判断当前工位的工序是否完成
                stepList.forEach(step -> {
                    WsStep currentWsStep = wsStepList.stream().filter(wsStep -> step.getId().equals(wsStep.getStep().getId())).findFirst().orElse(null);
                    if (null == currentWsStep) {
                        return;
                    }
                    //前置工序完成或者为空
                    boolean bPreStepIsFinishOrNull = false;
                    // 前置工序不存在
                    if (!ValidateUtils.isValid(currentWsStep.getPreStepId())) {
                        // 前置工序存在并已经完成
                        bPreStepIsFinishOrNull = true;
                    } else {
                        //验证上一道工序工序是否完成（可能存在分支合并的情况）
                        String[] preStepList = currentWsStep.getPreStepId().split(Constants.STR_COMMA);
                        List<Long> stepIds = Arrays.stream(preStepList).map(stepId -> Long.parseLong(stepId)).collect(Collectors.toList());
                        List<BatchWorkDetail> batchWorkDetailList = batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdInAndDeleted(subWorkSheet.getId(), stepIds, Constants.LONG_ZERO);
                        //上一道工序，工序生产详情数量=工序完成数量=上道工序数量
                        if (ValidateUtils.isValid(batchWorkDetailList)
                                && (batchWorkDetailList.size() == stepIds.size())
                                && (batchWorkDetailList.stream().filter(batchWorkDetail -> ConstantsEnum.TRUE_INT.getCategoryName() == batchWorkDetail.getFinish()).collect(Collectors.toList()).size() == batchWorkDetailList.size())) {
                            bPreStepIsFinishOrNull = true;
                        }
                    }
                    if (bPreStepIsFinishOrNull) {
                        BatchWorkDetail batchWorkDetail = batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), currentWsStep.getStep().getId(), Constants.LONG_ZERO).orElse(new BatchWorkDetail());
                        if (!ObjectUtils.equals(batchWorkDetail.getFinish(), ConstantsEnum.TRUE_INT.getCategoryName())) {
                            subWorkSheets.add(subWorkSheet);
                            return;
                        }
                    }
                });
            });
            return subWorkSheets.stream().map(SubWorkSheet::getSerialNumber).distinct().collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 获取（子）工单 完成后的参数信息
     *
     * @param clientGetFinishWsInfoDto （子）工单号DTO
     * @return ClientStepSubWsInfoDto
     * <AUTHOR>
     * @date 2022/3/24
     */
    @Transactional(readOnly = true)
    public ClientStepSubWsInfoDTO getFinishWsInfo(ClientGetFinishWsInfoDTO clientGetFinishWsInfoDto) {
        //TODO:目前Rworker端未明确返修单，如何提供合格数逻辑，此接口未对返修单做特殊化处理
        if (ValidateUtils.isValid(clientGetFinishWsInfoDto.getSubWsSerialNumber()) || ValidateUtils.isValid(clientGetFinishWsInfoDto.getWsSerialNumber())) {

            if (ValidateUtils.isValid(clientGetFinishWsInfoDto.getSubWsSerialNumber())) {
                Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findBySerialNumberAndDeleted(clientGetFinishWsInfoDto.getSubWsSerialNumber(), Constants.LONG_ZERO);
                if (!subWorkSheetOptional.isPresent()) {
                    return new ClientStepSubWsInfoDTO(new BaseClientDTO(Constants.KO, SUB_WORKSHEET_NOT_EXIST));
                }
                SubWorkSheet subWorkSheet = subWorkSheetOptional.get();
                if (subWorkSheet.getStatus() < Constants.FINISH) {
                    return new ClientStepSubWsInfoDTO(new BaseClientDTO(Constants.KO, "子工单未完成!"));
                }
                //查找子工单相关信息
                ClientStepSubWsInfoDTO clientStepSubWsInfoDto = Optional.ofNullable(subWorkSheet).map(ClientStepSubWsInfoDTO::new).orElseThrow(() -> new ResponseException(CLIENT_STEP_SUB_WS_INFO_DTONOT_EXIST, ERR_MSG));
                //添加物料基础信息
                clientStepSubWsInfoDto.setMaterialDto(subWorkSheet.getWorkSheet().getBomInfoDto().getMaterial());
                return clientStepSubWsInfoDto;
            }
            if (ValidateUtils.isValid(clientGetFinishWsInfoDto.getWsSerialNumber())) {
                Optional<WorkSheet> workSheetOptional = workSheetRepository.findBySerialNumberAndDeleted(clientGetFinishWsInfoDto.getWsSerialNumber(), Constants.LONG_ZERO);
                if (!workSheetOptional.isPresent()) {
                    return new ClientStepSubWsInfoDTO(new BaseClientDTO(Constants.KO, "总工单不存在!"));
                }
                WorkSheet workSheet = workSheetOptional.get();
                //查找总工单相关信息
                ClientStepSubWsInfoDTO clientStepSubWsInfoDto = Optional.ofNullable(workSheet).map(ClientStepSubWsInfoDTO::new).orElseThrow(() -> new ResponseException(CLIENT_STEP_SUB_WS_INFO_DTONOT_EXIST, ERR_MSG));
                //添加物料基础信息
                clientStepSubWsInfoDto.setMaterialDto(workSheet.getBomInfoDto().getMaterial());
                return clientStepSubWsInfoDto;
            }
        }
        return new ClientStepSubWsInfoDTO(new BaseClientDTO(Constants.KO, "需要提供（子）工单号"));
    }

    /**
     * 获取待做工序技术规格和sop
     *
     * @param clientGetStepInfoDto 返回Rwork结果DTO
     * @param pedigree             产品谱系
     * @param workFlow             工艺路线
     * @param step                 待做工序
     * @param clientId             客户主键id
     * @return ClientGetStepInfoDTO
     */
    @Transactional(readOnly = true)
    public ClientGetStepInfoDTO getStepSpecificationSop(ClientGetStepInfoDTO clientGetStepInfoDto, Pedigree pedigree, WorkFlow workFlow, Step step, Long clientId) {
        PedigreeStepSpecification pedigreeStepSpecification = commonService.findPedigreeStepSpecification(pedigree.getId(), workFlow.getId(), step.getId(), clientId);
        if (null == pedigreeStepSpecification) {
            return clientGetStepInfoDto;
        }
        clientGetStepInfoDto.setSpecification(pedigreeStepSpecification.getQualification());

        //查询文件
        List<DocumentDTO> documentDTOList = rbaseDocumentProxy.getByRecordId(pedigreeStepSpecification.getId());
        if (!CollectionUtils.isEmpty(documentDTOList)) {
            clientGetStepInfoDto.setDocumentDTOList(documentDTOList);
        }
        return clientGetStepInfoDto;
    }

    /**
     * 验证容器流转待下交数量合法性
     *
     * @param clientSaveStepInfoDto 工序下交信息
     * @return BaseClientDTO
     * <AUTHOR>
     * @date 2022/8/24
     */
    public BaseClientDTO checkRequestContainerInfo(ClientSaveStepInfoDTO clientSaveStepInfoDto) {

        if (!ValidateUtils.isValid(clientSaveStepInfoDto.getRequestContainerInfos())) {
            return new BaseClientDTO(Constants.OK);
        }

        List<ClientSaveStepInfoDTO.RequestContainerInfo> requestContainerInfos = clientSaveStepInfoDto.getRequestContainerInfos();

        //原容器列表
        List<ContainerDetail> preContainerDetailList = containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndContainerIdInAndStatusAndDeletedOrderByIdDesc(
                clientSaveStepInfoDto.getSubWsId(), requestContainerInfos.stream().map(ClientSaveStepInfoDTO.RequestContainerInfo::getContainerId).collect(Collectors.toList()), Constants.INT_ONE, Constants.LONG_ZERO);

        //容器请求 不绑定新容器 -> 容器待下交数量必须与容器待流转数量一致
        if (ValidateUtils.isValid(clientSaveStepInfoDto.getRequestContainerInfos()) && clientSaveStepInfoDto.getBindContainerId() == null) {
            boolean numberDiff = requestContainerInfos.stream().allMatch(requestContainer -> preContainerDetailList.stream()
                    .filter(preContainerDetail -> preContainerDetail.getContainer().getId().equals(requestContainer.getContainerId()))
                    .findFirst().get().getTransferNumber() == requestContainer.getNumber());
            if (!numberDiff) {
                return new BaseClientDTO(Constants.KO, "容器待下交数量必须与容器待流转数量一致");
            }
        }
        //A-》A; A->B; A->B+C;
        if (ValidateUtils.isValid(clientSaveStepInfoDto.getRequestContainerInfos()) && clientSaveStepInfoDto.getBindContainerId() != null) {
            if ((clientSaveStepInfoDto.getRequestContainerInfos().stream().mapToInt(ClientSaveStepInfoDTO.RequestContainerInfo::getNumber).sum()) != clientSaveStepInfoDto.getNumber()) {
                return new BaseClientDTO(Constants.KO, "原容器待下交数之和不等于投产数");
            }
            //原容器待下交数量必须小于等于待流转数量
            boolean numberDiff = requestContainerInfos.stream().allMatch(requestContainer -> preContainerDetailList.stream()
                    .filter(preContainerDetail -> preContainerDetail.getContainer().getId().equals(requestContainer.getContainerId()))
                    .findFirst().get().getTransferNumber() >= requestContainer.getNumber());
            if (!numberDiff) {
                return new BaseClientDTO(Constants.KO, "原容器待下交数量必须小于等于待流转数量");
            }
        }
        return new BaseClientDTO(Constants.OK);
    }

    /**
     * 获取当前sn 生产状态
     *
     * @param snInfo       sn
     * @param subWorkSheet 子工单id
     * @param isLastStep   最后 一道工序
     * @return int
     * <AUTHOR>
     * @date 2022/11/18
     */
    public int getSnWorkStatusByStatus(ClientSaveStepInfoDTO.SnInfo snInfo, SubWorkSheet subWorkSheet, boolean isLastStep) {
        int status = SnWorkStatusEnum.PUT_INTO_PRODUCTION.getStatus();

        if (snInfo.getResult() == Constants.INT_ONE) {
            if (subWorkSheet.getWorkSheet().getCategory() < WsEnum.NORMAL_WS.getCategory()) {
                status = SnWorkStatusEnum.IN_THE_REPAIR.getStatus();
            }
            if (isLastStep) {
                status = SnWorkStatusEnum.QUALIFIED.getStatus();
            }
        } else {
            //获取当前sn 不合格项目 对应的调整类型
            Optional<UnqualifiedItem> unqualifiedItemOptional = unqualifiedItemRepository.findByIdAndDeleted(snInfo.getUnqualifiedItemId(), Constants.LONG_ZERO);

            if (unqualifiedItemOptional.isPresent()) {
                //默认待返修
                status = SnWorkStatusEnum.TO_BE_REPAIR.getStatus();
                UnqualifiedItem unqualifiedItem = unqualifiedItemOptional.get();
                List<OnlineReworkRule> onlineReworkRules = onlineReworkRuleRepository.findByUnqualifiedItemIdAndDeleted(unqualifiedItemOptional.get().getId(), Constants.LONG_ZERO);
                //不良项目处理方式：0,在线返修;1,流程返修;2,报废;3,维修分析
                if (ValidateUtils.isValid(onlineReworkRules) && unqualifiedItem.getDealWay() == Constants.INT_ZERO) {
                    status = SnWorkStatusEnum.IN_THE_REPAIR.getStatus();
                }
                if (unqualifiedItem.getDealWay() == Constants.INT_TWO) {
                    status = SnWorkStatusEnum.SCRAP.getStatus();
                }
            }
        }
        return status;
    }

    /**
     * 验证回退容器合法性
     *
     * @param containerId 容器id
     * @return ClientContainerRollBakeDTO
     * <AUTHOR>
     * @date 2022/11/28
     */
    public ClientContainerRollBakeDTO checkContainer(Long containerId) {
        Optional<ContainerDetail> containerDetailOptional = containerDetailRepository.findTop1ByContainerIdAndDeletedOrderByIdDesc(containerId, Constants.LONG_ZERO);

        if (!containerDetailOptional.isPresent()) {
            return new ClientContainerRollBakeDTO(Constants.KO, "容器详情不存在");
        }

        ContainerDetail containerDetail = containerDetailOptional.get();
        //验证容器回退合法性
        ContainerDetailReplaceDTO containerDetailReplaceDto = batchWorkDetailService.validateContainer(containerDetail.getBatchWorkDetail(), containerDetail);

        if (Constants.KO.equals(containerDetailReplaceDto.getStatus())) {
            return new ClientContainerRollBakeDTO(Constants.KO, "子工单已完成&容器存在后置工序&容器已解绑：" + containerDetailReplaceDto.getMessage());
        }
        if (ValidateUtils.isValid(containerDetailReplaceDto.getContainerDetailList())) {
            List<ClientContainerRollBakeDTO.ClientContainerDetailInfo> clientContainerDetailInfos = containerDetailReplaceDto.getContainerDetailList().stream().map(ClientContainerRollBakeDTO.ClientContainerDetailInfo::new)
                    .collect(Collectors.toList());
            return new ClientContainerRollBakeDTO(Constants.KO, "原容器已被占用需替换容器", containerDetail.getId(), clientContainerDetailInfos);
        }
        return new ClientContainerRollBakeDTO(Constants.OK, "容器回退验证通过", containerDetail.getId());
    }

    /**
     * Rworker调用容器回退
     *
     * @param rollBackDto 容器回退信息
     * @return ClientContainerRollBakeDTO
     * <AUTHOR>
     * @date 2022/11/28
     */
    public ClientContainerRollBakeDTO rollBakeContainerDetail(RollBackDTO rollBackDto) {
        //容器回退
        ContainerDetailReplaceDTO containerDetailReplaceDto = rollbackStepServices[0].deleteContainerDetailById(rollBackDto);
        if (Constants.KO.equals(containerDetailReplaceDto.getStatus())) {
            return new ClientContainerRollBakeDTO(Constants.KO, containerDetailReplaceDto.getMessage());
        }
        if (Constants.OK.equals(containerDetailReplaceDto.getStatus()) && ValidateUtils.isValid(containerDetailReplaceDto.getContainerDetailList())) {
            List<ClientContainerRollBakeDTO.ClientContainerDetailInfo> clientContainerDetailInfos = containerDetailReplaceDto.getContainerDetailList().stream().map(ClientContainerRollBakeDTO.ClientContainerDetailInfo::new)
                    .collect(Collectors.toList());
            return new ClientContainerRollBakeDTO(Constants.KO, "原容器已被占用需替换容器", rollBackDto.getContainerDetailId(), clientContainerDetailInfos);
        }
        return new ClientContainerRollBakeDTO(Constants.OK, "容器回退成功");
    }

    /**
     * 验证请求模式是否合法
     *
     * @param clientSaveStepInfoDto 保存工序详情下下
     * @param currentStep           当前工序
     * @return BaseClientDTO
     * <AUTHOR>
     * @date 2022/11/29
     */
    public BaseClientDTO checkStepRequestMode(ClientSaveStepInfoDTO clientSaveStepInfoDto, WsStep currentStep) {

        //工单请求 || 单支请求
        if (currentStep.getRequestMode() == ConstantsEnum.WORK_SHEET_REQUEST_MODE.getCategoryName() || currentStep.getRequestMode() == ConstantsEnum.SN_REQUEST_MODE.getCategoryName()) {
            return checkControlMode(clientSaveStepInfoDto, currentStep);
        }
        //容器请求
        if (currentStep.getRequestMode() == ConstantsEnum.CONTAINER_REQUEST_MODE.getCategoryName()) {
            if (!ValidateUtils.isValid(clientSaveStepInfoDto.getRequestContainerInfos())) {
                return new BaseClientDTO(Constants.KO, "请输入原容器");
            }
            return checkControlMode(clientSaveStepInfoDto, currentStep);
        }
        return new BaseClientDTO(Constants.OK);

    }

    /**
     * 验证管控模式是否合法
     *
     * @param clientSaveStepInfoDto 保存工序详情下下
     * @param currentStep           当前工序
     * @return BaseClientDTO
     * <AUTHOR>
     * @date 2022/11/29
     */
    public BaseClientDTO checkControlMode(ClientSaveStepInfoDTO clientSaveStepInfoDto, WsStep currentStep) {
        //是否新绑容器
        if (currentStep.getIsBindContainer() && clientSaveStepInfoDto.getBindContainerId() == null) {
            return new BaseClientDTO(Constants.KO, "当前需要绑定新容器");
        }
        //管控模式
        if (currentStep.getControlMode() == ConstantsEnum.SN_CONTROL_MODE.getCategoryName() && !ValidateUtils.isValid(clientSaveStepInfoDto.getSnInfoList())) {
            return new BaseClientDTO(Constants.KO, "当前需要录入sn单支");
        }
        return new BaseClientDTO(Constants.OK);
    }

    /**
     * @param workCellId 工位id
     * @description: 通过工位id获取工序列表
     * @return: java.util.List<net.airuima.rbase.domain.base.process.Step>
     * @author: xiaoyu
     * @time: 2023/1/3 10:30
     */
    public List<Step> findByWorkCellId(Long workCellId) {
        return workCellStepRepository.findByWorkCellId(workCellId);
    }

    /**
     * 验证工序时间间隔是否合规
     *
     * @param startTime                  开始时间
     * @param endTime                    结束时间
     * @param pedigreeStepIntervalConfig 间隔配置
     * @return 比较结果
     */
    private boolean validateStepIntervalWhenSave(LocalDateTime startTime, LocalDateTime endTime, PedigreeStepIntervalConfig pedigreeStepIntervalConfig) {
        String waitCompareDuration = String.valueOf(DateUtils.LocalDateTimeSeconds(startTime, endTime));
        String range = pedigreeStepIntervalConfig.getDuration();
        boolean result = Boolean.TRUE;
        if (pedigreeStepIntervalConfig.getDurationUnit() == StepIntervalEnum.MINUTE.getUnit()) {
            result = ToolUtils.compareIntervalWithUnit(range, waitCompareDuration, 60);
        }
        if (pedigreeStepIntervalConfig.getDurationUnit() == StepIntervalEnum.HOUR.getUnit()) {
            result = ToolUtils.compareIntervalWithUnit(range, waitCompareDuration, 3600);
        }
        if (pedigreeStepIntervalConfig.getDurationUnit() == StepIntervalEnum.DAY.getUnit()) {
            result = ToolUtils.compareIntervalWithUnit(range, waitCompareDuration, 86400);
        }
        return result;
    }

    /**
     * 工序保存完后后续操作扩展接口
     *
     * @param clientSaveStepInfoDto 保存工序请求参数
     */
    @Override
    public void afterStepBatchSaved(ClientSaveStepInfoDTO clientSaveStepInfoDto) {
        IClientStepService.super.afterStepBatchSaved(clientSaveStepInfoDto);
    }

    /**
     * 工序保存前验证扩展接口
     *
     * @param clientSaveStepInfoDto 保存工序请求参数
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO  结果信息
     * <AUTHOR>
     * @date 2023/2/1
     */
    @Override
    public BaseClientDTO validateStepSaved(ClientSaveStepInfoDTO clientSaveStepInfoDto) {
        return IClientStepService.super.validateStepSaved(clientSaveStepInfoDto);
    }

    /**
     * Rworker 通过容器或工单 获取待做工序
     *
     * @param clientWsNoContainerDto 通过容器或工单 获取待做工序
     * @return ClientContainerRollBakeDTO
     * <AUTHOR>
     */
    public ClientTodoStepDTO wsNoOrContainerCodeTodoStep(ClientWsNoContainerDTO clientWsNoContainerDto) {

        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        Boolean subProduce = (StringUtils.isBlank(mode) || Boolean.parseBoolean(mode)) ? Boolean.TRUE : Boolean.FALSE;

        WorkSheet workSheet = null;
        SubWorkSheet subWorkSheet = null;
        if (ValidateUtils.isValid(clientWsNoContainerDto.getSerialNumber())) {
            if (subProduce) {
                subWorkSheet = subWorkSheetRepository.findBySerialNumberAndDeleted(clientWsNoContainerDto.getSerialNumber(), Constants.LONG_ZERO)
                        .orElseThrow(() -> new ResponseException("subWsNotFind", "子工单编码不存在"));
                workSheet = subWorkSheet.getWorkSheet();
            } else {
                workSheet = workSheetRepository.findBySerialNumberAndDeleted(clientWsNoContainerDto.getSerialNumber(), Constants.LONG_ZERO)
                        .orElseThrow(() -> new ResponseException("wsNotFind", "工单编码不存在"));
            }
        }
        Container container = null;
        if (ValidateUtils.isValid(clientWsNoContainerDto.getContainerCode())) {
            container = containerRepository.findByCodeAndDeleted(clientWsNoContainerDto.getContainerCode(), Constants.LONG_ZERO)
                    .orElseThrow(() -> new ResponseException("containerCodeNotFind", "容器编码不存在"));
        }
        if (Objects.isNull(workSheet) && Objects.isNull(container)) {
            throw new ResponseException("paramData", "未提供工单号或者容器编码");
        }

        if (Objects.nonNull(container)) {
            return produceContainerToDoStep(subProduce, container,subWorkSheet,workSheet);
        } else {
            Map<Step, Integer> stepNumberMap = produceWsToDoStep(subProduce, workSheet, subWorkSheet);
            Map.Entry<Step, Integer> stepNumberEntry = stepNumberMap.entrySet().stream().findFirst().get();
            return bulidClientTodoStepDTO(subProduce, workSheet, subWorkSheet, stepNumberEntry.getKey(),stepNumberEntry.getValue());
        }
    }


    /**
     * 获取待做工序
     *
     * @param subProduce   是否子工单投产
     * @param workSheet    工单
     * @param subWorkSheet 子工单
     * @return 待做工序
     */
    public Map<Step,Integer> produceWsToDoStep(Boolean subProduce, WorkSheet workSheet, SubWorkSheet subWorkSheet) {
        Map<Step,Integer> stepNumberMap = new HashMap<>();
        checkProduce(subProduce,subWorkSheet,workSheet);

        List<BatchWorkDetail> batchWorkDetailList = subProduce ? batchWorkDetailRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO)
                : batchWorkDetailRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);

        List<WsStep> wsStepList = null != subWorkSheet ? wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO) : null;
        if (!ValidateUtils.isValid(wsStepList)) {
            wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        }
        if (null == wsStepList) {
            throw new ResponseException("error.wsStepNotExist", "工单工艺快照不存在");
        }

        BatchWorkDetail batchWorkDetail = null;

        if (ValidateUtils.isValid(batchWorkDetailList)) {
            batchWorkDetail = batchWorkDetailList.stream().filter(entity -> entity.getFinish() == Constants.INT_ZERO)
                    .min(Comparator.comparing(BatchWorkDetail::getId)).orElse(null);

            if (Objects.nonNull(batchWorkDetail)) {
                BatchWorkDetail finalBatchWorkDetail1 = batchWorkDetail;
                WsStep currentStep = wsStepList.stream().filter(wsStep -> wsStep.getStep().equals(finalBatchWorkDetail1.getStep())).findFirst()
                        .orElseThrow(() -> new ResponseException("error.afterStepNotExist", "请求工单待生产工序不存在"));
                if (!ValidateUtils.isValid(currentStep.getPreStepId())){
                    stepNumberMap.put(finalBatchWorkDetail1.getStep(),subProduce?subWorkSheet.getNumber()-finalBatchWorkDetail1.getInputNumber():
                            workSheet.getNumber()-finalBatchWorkDetail1.getInputNumber());
                    return stepNumberMap;
                }else {
                    Long stepId = Arrays.stream(currentStep.getPreStepId().split(Constants.STR_COMMA)).map(Long::parseLong).findFirst().get();
                    BatchWorkDetail batchWorkDetailOptional = (subProduce ? batchWorkDetailRepository.findByStepIdAndSubWorkSheetIdAndDeleted(stepId,subWorkSheet.getId(),Constants.LONG_ZERO)
                            : batchWorkDetailRepository.findByStepIdAndWorkSheetIdAndDeleted(stepId,workSheet.getId(), Constants.LONG_ZERO))
                            .orElseThrow(() -> new ResponseException("batchWorkDetailIsNull","前置工序详情不存在"));
                    stepNumberMap.put(finalBatchWorkDetail1.getStep(),batchWorkDetailOptional.getQualifiedNumber()-finalBatchWorkDetail1.getInputNumber());
                    return stepNumberMap;
                }

            } else {
                batchWorkDetail = batchWorkDetailList.stream().filter(entity -> entity.getFinish() == Constants.INT_ONE)
                        .max(Comparator.comparing(BatchWorkDetail::getId)).orElse(null);
                if (Objects.nonNull(batchWorkDetail)) {
                    BatchWorkDetail finalBatchWorkDetail = batchWorkDetail;
                    WsStep latestFinishedWsStep = wsStepList.stream().filter(wsStep -> finalBatchWorkDetail.getStep().getId().equals(wsStep.getStep().getId())).findFirst().orElse(null);
                    if (null == latestFinishedWsStep || StringUtils.isBlank(latestFinishedWsStep.getAfterStepId())) {
                        throw new ResponseException("error.afterStepNotExist", "请求工单不存在后置待生产工序");
                    }
                    List<Long> stepIds = Arrays.stream(latestFinishedWsStep.getAfterStepId().split(Constants.STR_COMMA)).map(Long::parseLong).collect(Collectors.toList());
                    List<Step> stepList = stepRepository.findByIdInAndDeleted(stepIds, Constants.LONG_ZERO);
                    if (!ValidateUtils.isValid(stepList)) {
                        throw new ResponseException("error.afterStepNotExist", "请求工单待生产工序不存在");
                    }
                    stepNumberMap.put(stepList.get(Constants.INT_ZERO),finalBatchWorkDetail.getTransferNumber());
                    return stepNumberMap;
                }
                throw new ResponseException("error.afterStepNotExist", "请求工单不存在后置待生产工序");
            }
        } else {
            WsStep wsStep = wsStepList.stream().filter(entity -> !ValidateUtils.isValid(entity.getPreStepId())).findFirst().orElseThrow(() ->
                    new ResponseException("error.todoStepNotExist", "请求工单待生产工序不存在"));
            stepNumberMap.put(wsStep.getStep(),subProduce?subWorkSheet.getNumber():workSheet.getNumber());
            return stepNumberMap;
        }


    }

    /**
     * 容器请求待做工序信息
     *
     * @param subProduce        是否子工单投产
     * @param container         容器
     * @return ClientTodoStepDTO
     */
    public ClientTodoStepDTO produceContainerToDoStep(Boolean subProduce, Container container,SubWorkSheet subWorkSheet,WorkSheet workSheet) {

        ContainerDetail containerDetail = containerDetailRepository.findBindingContainerDetailByContainerIdAndDeleted(container.getId(), Constants.LONG_ZERO);
        if (Objects.isNull(containerDetail)) {
            throw new ResponseException("", "当前容器未绑定,获取不到待做工序信息");
        }
        subWorkSheet  = subProduce && Objects.isNull(subWorkSheet)? containerDetail.getBatchWorkDetail().getSubWorkSheet() : subWorkSheet;
        workSheet = Objects.isNull(subWorkSheet) ? containerDetail.getBatchWorkDetail().getWorkSheet() : subWorkSheet.getWorkSheet();
        //检查工单状态
        checkProduce(subProduce,subWorkSheet,workSheet);
        if (subProduce && Objects.nonNull(subWorkSheet)) {
            if (!containerDetail.getBatchWorkDetail().getSubWorkSheet().equals(subWorkSheet)) {
                throw new ResponseException("", "容器绑定子工单：" + containerDetail.getBatchWorkDetail().getSubWorkSheet().getSerialNumber() + "与请求上传子工单不一致");
            }
        }
        if (!subProduce && Objects.nonNull(workSheet)) {
            if (!containerDetail.getBatchWorkDetail().getWorkSheet().equals(workSheet)) {
                throw new ResponseException("", "容器绑定工单：" + containerDetail.getBatchWorkDetail().getWorkSheet().getSerialNumber() + "与请求上传工单不一致");
            }
        }

        List<WsStep> wsStepList = null != subWorkSheet ? wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO) : null;
        if (!ValidateUtils.isValid(wsStepList)) {
            wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        }
        if (null == wsStepList) {
            throw new ResponseException("error.wsStepNotExist", "工单工艺快照不存在");
        }

        WsStep latestFinishedWsStep = wsStepList.stream().filter(wsStep -> containerDetail.getBatchWorkDetail().getStep().getId().equals(wsStep.getStep().getId())).findFirst().orElse(null);
        if (null == latestFinishedWsStep || StringUtils.isBlank(latestFinishedWsStep.getAfterStepId())) {
            throw new ResponseException("error.afterStepNotExist", "请求容器不存在后置待生产工序");
        }
        List<Long> stepIds = Arrays.stream(latestFinishedWsStep.getAfterStepId().split(Constants.STR_COMMA)).map(Long::parseLong).collect(Collectors.toList());
        List<Step> stepList = stepRepository.findByIdInAndDeleted(stepIds, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(stepList)) {
            throw new ResponseException("error.afterStepNotExist", "请求容器待生产工序不存在");
        }
        Step step = stepList.get(Constants.INT_ZERO);
        return bulidClientTodoStepDTO(subProduce, workSheet, subWorkSheet, step,containerDetail.getTransferNumber());
    }

    /**
     * 构建返回的待做工序信息
     *
     * @param subProduce   是否子工单投产
     * @param workSheet    工单
     * @param subWorkSheet 子工单
     * @param step         工序
     * @return ClientTodoStepDTO
     */
    public ClientTodoStepDTO bulidClientTodoStepDTO(Boolean subProduce, WorkSheet workSheet, SubWorkSheet subWorkSheet, Step step,Integer number) {
        ClientTodoStepDTO clientTodoStepDto = new ClientTodoStepDTO(Constants.OK);
        clientTodoStepDto.setStepDto(new ClientTodoStepDTO.StepDTO(step))
                .setPedigreeDto(new ClientTodoStepDTO.PedigreeDTO(workSheet.getPedigree()))
                .setWorkSheetId(workSheet.getId()).setWsSerialNumber(workSheet.getSerialNumber())
                .setNumber(number);
        if (subProduce && Objects.nonNull(subWorkSheet)) {
            clientTodoStepDto.setSubWorkSheetId(subWorkSheet.getId())
                    .setSubSerialNumber(subWorkSheet.getSerialNumber());
        }
        return clientTodoStepDto;
    }

    /**
     * 验证（子）工单 合法性
     * @param subProduce 是否子工单投产
     * @param subWorkSheet 子工单投产
     * @param workSheet 工单投产
     */
    public void checkProduce(Boolean subProduce,SubWorkSheet subWorkSheet,WorkSheet workSheet){
        if (subProduce){
            if (subWorkSheet.getStatus() == Constants.FINISH) {
                throw new ResponseException("subWorkSheetFinish","子工单已完成!");
            }
            if (subWorkSheet.getStatus() == Constants.PAUSE) {
                throw new ResponseException("subWorkSheetPause","子工单已暂停!");
            }
            if (subWorkSheet.getStatus() == Constants.NORMAL_CLOSED
                    || subWorkSheet.getStatus() == Constants.ABNORMAL_CLOSED) {
                throw new ResponseException("subWorkSheetClosed","总工单已结单!");
            }
        }
        if (workSheet.getStatus() == Constants.FINISH) {
            throw new ResponseException("workSheetFinish","总工单已完成!");
        }
        if (workSheet.getStatus() == Constants.PAUSE) {
            throw new ResponseException("workSheetPause","总工单已暂停!");
        }
        if (workSheet.getStatus() == Constants.NORMAL_CLOSED
                || workSheet.getStatus() == Constants.ABNORMAL_CLOSED) {
            throw new ResponseException("workSheetClosed","总工单已结单!");
        }

    }
}
