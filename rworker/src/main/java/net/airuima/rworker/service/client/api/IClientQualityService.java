package net.airuima.rworker.service.client.api;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.constant.Constants;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.dto.client.ClientGetCheckInfoDTO;
import net.airuima.rbase.dto.client.ClientGrrHistorySaveDTO;
import net.airuima.rbase.dto.client.ClientStandardPartCheckResultSaveDTO;

import java.util.List;

/**
 * Rworker 客户端 质量检测模块
 */

@FuncDefault
public interface IClientQualityService {

    /**
     * Rworker客户端 验证工位，工单 是否 需要进行首检，巡检
     *
     * @param clientGetCheckInfoDto RWorker验证及获取首检，巡检相关信息DTO
     * @return net.airuima.dto.client.ClientGetCheckInfoDTO 验证及获取首中末检相关信息
     * <AUTHOR>
     * @date 2023/4/27
     */
    @FuncInterceptor("FAI || IPQC")
    default ClientGetCheckInfoDTO checkProcessInspection(ClientGetCheckInfoDTO clientGetCheckInfoDto) {
        return new ClientGetCheckInfoDTO(new BaseClientDTO(Constants.OK));
    }

    /**
     * Rworker客户端 验证是否需要首检
     *
     * @param subWorkSheet 子工单
     * @param workCell     工位
     * @return net.airuima.dto.client.ClientGetCheckInfoDTO 验证及获取首中末检相关信息
     */
    @FuncInterceptor("FAI")
    default ClientGetCheckInfoDTO faiInspectionInfo(ClientGetCheckInfoDTO clientGetCheckInfoDto, SubWorkSheet subWorkSheet, WorkCell workCell) {
        return new ClientGetCheckInfoDTO(new BaseClientDTO(Constants.OK));
    }

    /**
     * Rworker 客户端 验证是否需要巡检
     *
     * @param subWorkSheet 子工单
     * @param workCell     工位
     * @return net.airuima.dto.client.ClientGetCheckInfoDTO 验证及获取首中末检相关信息
     */
    @FuncInterceptor("IPQC")
    default ClientGetCheckInfoDTO ipqcInspectionInfo(ClientGetCheckInfoDTO clientGetCheckInfoDto, SubWorkSheet subWorkSheet, WorkCell workCell) {
        return new ClientGetCheckInfoDTO(new BaseClientDTO(Constants.OK));
    }

    /**
     * Rworker根据检测工位请求获取待检测工位及对应工序列表
     *
     * @param clientGetCheckInfoDto Rworker请求参数
     * @return net.airuima.dto.client.ClientGetCheckInfoDTO 验证及获取首中末检相关信息
     * <AUTHOR>
     * @date 2021-03-23
     **/
    default ClientGetCheckInfoDTO getToInspectedWorkCellAndStepInfo(ClientGetCheckInfoDTO clientGetCheckInfoDto) {
        return new ClientGetCheckInfoDTO(new BaseClientDTO(Constants.OK));
    }


    /**
     * 保存页面导入或者客户端上传的标准件测试数据明细
     *
     * @param clientStandardPartCheckResultSaveDtoList 标准件测试数据参数列表列表DTO
     * @return BaseClientDTO
     */
    @FuncInterceptor("StandardPart")
    default BaseClientDTO saveStandardPartCheckResultDetail(List<ClientStandardPartCheckResultSaveDTO> clientStandardPartCheckResultSaveDtoList) {
        return new BaseClientDTO(Constants.OK);
    }


    /**
     * Rworker上传或者页面导入GRR测试数据DTO
     * @param clientGrrHistorySaveDTOList GRR测试数据参数列表列表DTO
     * @return BaseClientDTO
     */
    default BaseClientDTO saveGrrHistory(List<ClientGrrHistorySaveDTO> clientGrrHistorySaveDTOList) {
        return new BaseClientDTO(Constants.OK);
    }


}
