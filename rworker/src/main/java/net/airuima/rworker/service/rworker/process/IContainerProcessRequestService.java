package net.airuima.rworker.service.rworker.process;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.dto.rworker.process.dto.*;
import net.airuima.rworker.service.rworker.process.impl.ContainerProcessRequestServiceImpl;
import net.airuima.util.ResponseException;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * Rworker容器模式生产过程请求相关Service
 * <AUTHOR>
 * @date 2023/1/31
 */
@FuncDefault
public interface IContainerProcessRequestService {

    /**
     * 验证绑定的容器编码是否合规
     *
     * @param rworkerBindContainerValidateRequestDTO 验证待绑定容器合规性参数DTO
     * @return net.airuima.rbase.web.rest.rworker.process.dto.RworkerBindContainerValidateGetDTO 待绑定容器信息
     */
    @FuncInterceptor(value = "Container")
    default RworkerBindContainerValidateGetDTO validateBindContainer(RworkerBindContainerValidateRequestDTO rworkerBindContainerValidateRequestDTO){
        return null;
    }

    /**
     * 获取容器待做工序信息
     * @param containerToDoStepRequestDTO 容器请求工序生产参数
     * @return net.airuima.rbase.web.rest.rworker.process.dto.RworkerContainerToDoStepGetDTO 容器待做工序信息
     */
    @FuncInterceptor(value = "Container")
    default RworkerContainerToDoStepGetDTO containerToDoStep(RworkerContainerToDoStepRequestDTO containerToDoStepRequestDTO){
        return null;
    }

    /**
     * 获取容器请求模式下的在线调整工序的投产数量
     * @param stepId 待做工序主键ID
     * @param requestContainerId 请求容器主键ID
     * @return int 在线调整数量
     */
    @FuncInterceptor(value = "OnlineAdjustment")
    default int findOnlineReworkStepTransferNumber(long stepId, Long requestContainerId,RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO){
        return Constants.INT_ZERO;
    }


    /**
     * 维修分析的容器请求生产工序时验证维修数据获取对应投产工单
     * @param subWsProductionMode 投产粒度(true:子工单,false:工单)
     * @param isBindingContainerRequest 是否为绑定状态的容器请求工序
     * @param containerId 请求容器主键ID
     * @param rworkerStepProcessBaseDTO 生产过程通用基础新信息
     */
    @FuncInterceptor("RepaireAnalysis")
    default void validateRequestContainerMaintain(boolean subWsProductionMode, boolean isBindingContainerRequest, long containerId, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO){
        if(!isBindingContainerRequest) {
            throw new ResponseException("error.requestContainerIsNotBinding", "请求工序生产的容器尚未绑定");
        }
    }



    /**
     * 绑定新容器时验证状态
     * @param containerId 新容器主键ID
     */
    @FuncInterceptor("RepaireAnalysis")
    default void validateBindContainerMaintain(long containerId){

    }

    /**
     * 容器请求工序或者绑定新容器时验证容器复检
     * @param containerId 容器ID
     */
    @FuncInterceptor("QReinspection")
    default void validateContainerReinspect(long containerId){

    }

    /**
     * 计算当前容器投产数
     *
     * @param containerNextToDoStepInfo     容器待做工序信息
     * @param containerNextToDoStepBaseInfo 待做工序基础信息
     * @param nextToDoWsStep                待做工序定制工序
     * @param nextToDoStep                  待做工序
     * <AUTHOR>
     * @date 2023/3/18
     **/
    default void getNextToDoStepNumber(RworkerContainerToDoStepGetDTO containerNextToDoStepInfo, ContainerProcessRequestServiceImpl.ContainerNextToDoStepBaseInfo containerNextToDoStepBaseInfo,
                                       WsStep nextToDoWsStep, Step nextToDoStep,RworkerStepProcessBaseDTO stepProcessBaseDTO) {

    }

}
