package net.airuima.rworker.service.rworker.process;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.dto.rworker.process.dto.RworkerBatchStepSaveRequestDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerStepProcessBaseDTO;

import java.util.List;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 * Rworker生产过程管控批量下交Service
 *
 * <AUTHOR>
 * @date 2022/12/22
 */
@FuncDefault
public interface IBatchProcessSaveService {

    /**
     * 保存子工单(工单)批量工序生产详情数据
     *
     * @param rworkerBatchStepSaveRequestDTO 保存工序生产数据参数DTO
     * @param batchWorkDetail                待保存工序详情记录
     * @param preStepTransferNumber          前置工序待流转数
     * @param preStepQualifiedNumber         前置工序合格数
     * @param batchStepSaveBaseInfo          公共区通用参数
     * @param onlineReworkStep               是否为在线调整工序
     * @return net.airuima.rbase.domain.procedure.batch.BatchWorkDetail 批量工序生产详情
     */
    default BatchWorkDetail saveBatchWorkDetail(RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDTO,
                                                RworkerStepProcessBaseDTO batchStepSaveBaseInfo, BatchWorkDetail batchWorkDetail, int preStepTransferNumber,
                                                int preStepQualifiedNumber, boolean onlineReworkStep) {
        return null;
    }

    /**
     * 验证保存批量工序参数参数
     *
     * @param rworkerBatchStepSaveRequestDTO 请求保存参数
     * @param batchStepSaveBaseInfo          工序生产过程基础信息
     * @param batchWorkDetail                批量工序生产详情
     * @param preStepTransferNumber          前置流转数
     * @return : void
     * <AUTHOR>
     * @date 2023/2/15
     **/
    default void validateInputNumber(RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDTO, RworkerStepProcessBaseDTO batchStepSaveBaseInfo, BatchWorkDetail batchWorkDetail, Integer preStepTransferNumber) {

    }

    /**
     * 批量模式下所有工序都完成后进行更新相关统计数据
     *
     * @param containerStepSaveBaseInfo 工序保存通用信息
     * @param lastStepQualifiedNumber   最后一个工序的合格数
     */
    default void calculateBatchAfterAllStepFinished(RworkerStepProcessBaseDTO containerStepSaveBaseInfo, int lastStepQualifiedNumber) {

    }

    /**
     * 预留工序保存可扩展接口
     *
     * @param rworkerBatchStepSaveRequestDTO 待保存工序参数DTO
     */
    default void createExtendInfo(RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDTO) {

    }

    /**
     * 判断是否存在未完成返工单
     *
     * @param rworkerStepProcessBaseDTO 工序保存通用信息
     * @param workSheet                 工单
     * @return boolean 是否存在未完成的返工单
     */
    default boolean existReworkSheetNotFinished(RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO, WorkSheet workSheet) {
        return false;
    }

    /**
     * 更新返工单对应正常单的合格数、不合格数以及返工合格数，同时根据条件更新工单可能完成的状态
     *
     * @param reworkWorkSheet 返工单
     * @param qualifiedNumber 合格数
     */
    default void calculateBatchAfterOnlineReworkSheetFinished(WorkSheet reworkWorkSheet, int qualifiedNumber) {

    }

    /**
     * 更新销售订单的完成数量
     *
     * @param workSheet 工单
     * @param number    完成数量
     */
    default void calculateBatchSaleOrderAfterAllStepFinished(WorkSheet workSheet, int number) {

    }

    /**
     * 保存批量模式下生产工序信息
     *
     * @param rworkerBatchStepSaveRequestDTOList 保存批量模式工序生产信息DTO列表
     */
    default void createStep(List<RworkerBatchStepSaveRequestDTO> rworkerBatchStepSaveRequestDTOList) {

    }
}
