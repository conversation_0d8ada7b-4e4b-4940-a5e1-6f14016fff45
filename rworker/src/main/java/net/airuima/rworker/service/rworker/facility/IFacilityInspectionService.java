package net.airuima.rworker.service.rworker.facility;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;

import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/5/10
 */
@FuncDefault
public interface IFacilityInspectionService {

    @FuncInterceptor("FBase")
    default void validateFacilityBaseStatus(List<Long> facilityIdList){

    }

    /**
     * 验证设备点检等状态是否合规
     * @param facilityIdList 设备ID列表
     */
    @FuncInterceptor("FBase && FacilityPointInspection")
    default void validateFacilityPointInspection(List<Long> facilityIdList){

    }


    /**
     * 验证设备巡检等状态是否合规
     * @param facilityIdList 设备ID列表
     */
    @FuncInterceptor("FBase && FacilityPatrolInspection")
    default void validateFacilityPatrolInspection(List<Long> facilityIdList){

    }
}
