package net.airuima.rworker.service.rworker.facility.impl;

import net.airuima.rbase.proxy.rfms.RbaseFacilityProxy;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rworker.service.rworker.facility.IFacilityMaintainService;
import net.airuima.util.ResponseException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/6/6
 */

@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class FacilityMaintainServiceImpl implements IFacilityMaintainService {


    @Autowired
    private RbaseFacilityProxy rbaseFacilityProxy;

    /**
     * 验证设备保养是否预期未做
     * @param facilityIdList 设备主键ID列表
     */
    @Override
    public void validateFacilityMaintain(List<Long> facilityIdList) {
        if (!ValidateUtils.isValid(facilityIdList)){
            return;
        }
        String result = rbaseFacilityProxy.validateMaintainTaskByFacilityIds(facilityIdList);
        if (StringUtils.isNotBlank(result)) {
            throw  new ResponseException("error.maintainTaskOverdue",result);
        }
    }
}
