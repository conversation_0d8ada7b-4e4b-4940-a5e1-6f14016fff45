package net.airuima.rworker.service.client.api;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.base.scene.AreaWorkCell;
import net.airuima.rbase.dto.client.base.BaseClientDTO;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产管控
 *
 * <AUTHOR>
 * @date 2023/08/08
 */
@FuncDefault
public interface IClientControlInspectService {


    /**
     * 清洁度管控
     *
     * @param areaWorkCell 区域工位
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO 结果信息
     */
    @FuncInterceptor(value = "EnvCleanliness")
    default BaseClientDTO checkCleanliness(AreaWorkCell areaWorkCell) {
        return null;
    }

    /**
     * 温湿度管控
     *
     * @param areaWorkCell 区域工位
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO 结果信息
     */
    @FuncInterceptor(value = "EnvHumiture")
    default BaseClientDTO checkHumiture(AreaWorkCell areaWorkCell) {
        return null;
    }

}
