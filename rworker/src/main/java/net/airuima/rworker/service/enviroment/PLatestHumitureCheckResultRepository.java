package net.airuima.rworker.service.enviroment;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rworker.service.enviroment.dto.LatestHumitureCheckResultDTO;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class PLatestHumitureCheckResultRepository {

    @BeanDefine("latestHumitureCheckResultRepository")
    public Optional<LatestHumitureCheckResultDTO> findByAreaIdAndDeleted(Long areaId, Long deleted) {
        return Optional.empty();
    }
}