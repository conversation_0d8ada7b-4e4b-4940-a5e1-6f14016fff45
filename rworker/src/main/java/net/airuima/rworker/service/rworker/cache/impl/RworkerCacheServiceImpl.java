package net.airuima.rworker.service.rworker.cache.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.constant.RworkerCacheEnum;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.dto.rworker.cache.dto.RworkerCacheSaveRequestDTO;
import net.airuima.rbase.proxy.rule.RbaseSysCodeProxy;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.rbase.repository.procedure.batch.ContainerDetailRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailRepository;
import net.airuima.rworker.domain.RworkerCache;
import net.airuima.rworker.repository.RworkerCacheRepository;
import net.airuima.rworker.service.rworker.cache.IRworkerCacheService;
import net.airuima.util.ResponseException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.klock.annotation.Klock;
import org.springframework.boot.autoconfigure.klock.model.LockTimeoutStrategy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/11/15
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class RworkerCacheServiceImpl implements IRworkerCacheService {

    @Autowired
    private RworkerCacheRepository rworkerCacheRepository;
    @Autowired
    private RbaseSysCodeProxy rbaseSysCodeProxy;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private SnWorkDetailRepository snWorkDetailRepository;


    /**
     * 新增或更新Rworker工作缓存
     *
     * @param rworkerCacheSaveRequestDTO 待保存rworker工作缓存参数
     */
    @Override
    @Klock(keys = {"#rworkerCacheSaveRequestDTO.workCellId"}, waitTime = 60, leaseTime = 60, lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST)
    public void saveInstance(RworkerCacheSaveRequestDTO rworkerCacheSaveRequestDTO) {
        //验证待保存rworker工作缓存参数合法性
        this.validate(rworkerCacheSaveRequestDTO);
        //新增或更新Rworker工作缓存
        RworkerCache rworkerCache = rworkerCacheRepository.findByWorkCellIdAndDeleted(rworkerCacheSaveRequestDTO.getWorkCellId(), Constants.LONG_ZERO).orElse(new RworkerCache(rworkerCacheSaveRequestDTO));
        if (null == rworkerCache.getId()) {
            rworkerCache.setWorkCell(new WorkCell(rworkerCacheSaveRequestDTO.getWorkCellId()));
            rworkerCache.setStartTime( LocalDateTime.now());
            String expireDay = rbaseSysCodeProxy.findByCode(Constants.KEY_RWORKER_CACHE_EXPIRE_DAY);
            rworkerCache.setExpireDate(LocalDate.now().plusDays(StringUtils.isBlank(expireDay) ? Constants.INT_FIVE : Integer.parseInt(expireDay)));
            rworkerCache.setDeleted(Constants.LONG_ZERO);
        }
        rworkerCache.setStep(new Step(rworkerCacheSaveRequestDTO.getStepId()));
        rworkerCache.setUuid(rworkerCacheSaveRequestDTO.getUuid());
        rworkerCache.setCache(rworkerCacheSaveRequestDTO.getCache());
        rworkerCache.setSerialNumber(rworkerCacheSaveRequestDTO.getSerialNumber());
        rworkerCache.setCategory(rworkerCacheSaveRequestDTO.getCategory());
        rworkerCacheRepository.save(rworkerCache);
    }

    /**
     * 通过工位主键ID获取缓存唯一识别码
     *
     * @param workCellId 工位ID
     * @return java.lang.String 缓存识别码
     */
    @Override
    public String findUuidByWorkCellId(Long workCellId) {
        RworkerCache rworkerCache = rworkerCacheRepository.findByWorkCellIdAndDeleted(workCellId, Constants.LONG_ZERO).orElse(null);
        return Objects.nonNull(rworkerCache) ? rworkerCache.getUuid() : null;
    }

    /**
     * 通过识别码获取缓存内容
     *
     * @param uuid 缓存识别码
     * @return 缓存内容
     */
    @Override
    public String findCacheByUuid(String uuid) {
        RworkerCache rworkerCache = rworkerCacheRepository.findByUuidAndDeleted(uuid, Constants.LONG_ZERO).orElse(null);
        return Objects.nonNull(rworkerCache) ? rworkerCache.getCache() : null;
    }

    /**
     * 通过工位主键ID获取缓存内容
     *
     * @param workCellId 工位主键ID
     * @return net.airuima.domain.procedure.cache.RworkerCache 缓存内容
     */
    @Override
    public RworkerCache findCacheByWorkCell(Long workCellId) {
        return rworkerCacheRepository.findByWorkCellIdAndDeleted(workCellId, Constants.LONG_ZERO).orElse(null);
    }

    /**
     * 通过请求工序对象类型以及对应序列号验证缓存是否存在
     *
     * @param category     请求工序对象类型(0:子工单号;1:工单号；2:容器号；3:SN号)
     * @param serialNumber 请求工序对象序列号
     */
    @Override
    public void validateCacheWhenRequestTodoStep(int category, String serialNumber) {
        RworkerCache rworkerCache = rworkerCacheRepository.findByCategoryAndSerialNumber(category, "\"" + serialNumber + "\"").orElse(null);
        if (Objects.nonNull(rworkerCache)) {
            if (category == Constants.INT_ZERO) {
                throw new ResponseException("error.subWorkSheetInCache", "子工单(" + serialNumber + ")正在工位(" + rworkerCache.getWorkCell().getName() + ")缓存生产中");
            }
            if (category == Constants.INT_ONE) {
                throw new ResponseException("error.subWorkSheetInCache", "工单(" + serialNumber + ")正在工位(" + rworkerCache.getWorkCell().getName() + ")缓存生产中");
            }
            if (category == Constants.INT_TWO) {
                throw new ResponseException("error.subWorkSheetInCache", "容器(" + serialNumber + ")正在工位(" + rworkerCache.getWorkCell().getName() + ")缓存生产中");
            }
            if (category == Constants.INT_THREE) {
                throw new ResponseException("error.subWorkSheetInCache", "SN(" + serialNumber + ")正在工位(" + rworkerCache.getWorkCell().getName() + ")缓存生产中");
            }
        }
    }

    /**
     * 通过工位主键ID删除缓存
     * @param workCellId 工位主键ID
     */
    @Override
    @Klock(keys = {"#workCellId"}, waitTime = 60, leaseTime = 60, lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST)
    public void deletedCacheByWorkCell(Long workCellId) {
        rworkerCacheRepository.deleteByWorkCellId(workCellId);
    }

    /**
     * 验证待保存rworker工作缓存参数合法性
     *
     * @param rworkerCacheSaveRequestDTO 待保存rworker工作缓存参数
     */
    private void validate(RworkerCacheSaveRequestDTO rworkerCacheSaveRequestDTO) {
        if (StringUtils.isBlank(rworkerCacheSaveRequestDTO.getUuid())) {
            throw new ResponseException("error.cacheUuidIsEmpty", "缓存唯一识别码不可为空");
        }
        if (null == rworkerCacheSaveRequestDTO.getCategory()) {
            throw new ResponseException("error.cacheCategoryIsEmpty", "工序请求对象类型不可为空");
        }
        if (CollectionUtils.isEmpty(rworkerCacheSaveRequestDTO.getSerialNumber())) {
            throw new ResponseException("error.cacheSerialNumberIsEmpty", "工序请求对象序列号列表不可为空");
        }
        if (Objects.isNull(rworkerCacheSaveRequestDTO.getWorkCellId())) {
            throw new ResponseException("error.cacheWorkCellIsEmpty", "生产工位不可为空");
        }
        if (Objects.isNull(rworkerCacheSaveRequestDTO.getStepId())) {
            throw new ResponseException("error.cacheStepIsEmpty", "生产工序不可为空");
        }
        if (Objects.isNull(rworkerCacheSaveRequestDTO.getCache())) {
            throw new ResponseException("error.cacheBodyIsEmpty", "缓存内容不可为空");
        }
        rworkerCacheSaveRequestDTO.getSerialNumber().forEach(serialNumber -> {
            RworkerCache rworkerCache = rworkerCacheRepository.findByCategoryAndSerialNumber(rworkerCacheSaveRequestDTO.getCategory(), "\"" + serialNumber + "\"").orElse(null);
            if (Objects.nonNull(rworkerCache) && !rworkerCache.getWorkCell().getId().equals(rworkerCacheSaveRequestDTO.getWorkCellId())) {
                throw new ResponseException("error.cacheIsIllegal", serialNumber + "正在" + rworkerCache.getWorkCell().getName() + "工位生产中,暂时不可在当前工位生产");
            }
        });
        JSONObject jsonObject = JSON.parseObject(rworkerCacheSaveRequestDTO.getCache());
        if (Objects.nonNull(jsonObject) && Objects.nonNull(jsonObject.get("selectedWorkSheets"))) {
            List<RworkerCacheWorkSheet> rworkerCacheWorkSheets = JSON.parseArray(jsonObject.get("selectedWorkSheets").toString(), RworkerCacheWorkSheet.class);
            if(rworkerCacheSaveRequestDTO.getCategory() == RworkerCacheEnum.SUB_WORK_SHEET_STEP_REQUEST.getCategory()){
                rworkerCacheWorkSheets.forEach(rworkerCacheWorkSheet -> batchWorkDetailRepository.findByStepIdAndSubWorkSheetIdAndDeleted(rworkerCacheSaveRequestDTO.getStepId(),rworkerCacheWorkSheet.getProductWorkSheetId(),Constants.LONG_ZERO).ifPresent(batchWorkDetail -> {
                    if(batchWorkDetail.getFinish() == ConstantsEnum.FINISH_STATUS.getCategoryName()){
                        throw new ResponseException("error.cacheStepHasFinished","子工单("+rworkerCacheWorkSheet.getSerialNumber()+")"+batchWorkDetail.getStep().getName()+"工序已完成");
                    }
                }));
            }
            if(rworkerCacheSaveRequestDTO.getCategory() == RworkerCacheEnum.WORK_SHEET_STEP_REQUEST.getCategory()){
                rworkerCacheWorkSheets.forEach(rworkerCacheWorkSheet -> batchWorkDetailRepository.findByStepIdAndWorkSheetIdAndDeleted(rworkerCacheSaveRequestDTO.getStepId(),rworkerCacheWorkSheet.getProductWorkSheetId(),Constants.LONG_ZERO).ifPresent(batchWorkDetail -> {
                    if(batchWorkDetail.getFinish() == ConstantsEnum.FINISH_STATUS.getCategoryName()){
                        throw new ResponseException("error.cacheStepHasFinished","工单("+rworkerCacheWorkSheet.getSerialNumber()+")"+batchWorkDetail.getStep().getName()+"工序已完成");
                    }
                }));
            }
        }
    }


    /**
     * 通过批量工序生产详情删除缓存
     * @param batchWorkDetail 批量工序生产详情
     * @param containerDetailList 容器详情列表
     * @param snWorkDetailList SN详情列表
     */
    @Override
    public void deleteCacheByBatchWorkDetail(BatchWorkDetail batchWorkDetail, List<ContainerDetail> containerDetailList, List<SnWorkDetail> snWorkDetailList) {
        //删除可能的工单请求模式下的缓存
        String serialNumber = Objects.nonNull(batchWorkDetail.getSubWorkSheet()) ? batchWorkDetail.getSubWorkSheet().getSerialNumber() : batchWorkDetail.getWorkSheet().getSerialNumber();
        rworkerCacheRepository.deleteByCategoryAndSerialNumberAndStepId(Objects.nonNull(batchWorkDetail.getSubWorkSheet()) ? Constants.INT_ZERO : Constants.INT_ONE, "\"" + serialNumber + "\"", batchWorkDetail.getStep().getId());
        //若容情详情列表存在则还需要判断是否删除容器缓存
        if (CollectionUtils.isNotEmpty(containerDetailList)) {
            containerDetailList.forEach(containerDetail -> rworkerCacheRepository.findByCategoryAndSerialNumber(Constants.INT_TWO, "\"" + containerDetail.getContainerCode() + "\"").ifPresent(rworkerCache -> {
                if (StringUtils.isNotBlank(rworkerCache.getCache())) {
                    JSONObject jsonObject = JSON.parseObject(rworkerCache.getCache());
                    if (Objects.nonNull(jsonObject.get("selectedWorkSheets"))) {
                        List<RworkerCacheWorkSheet> rworkerCacheWorkSheets = JSON.parseArray(jsonObject.get("selectedWorkSheets").toString(), RworkerCacheWorkSheet.class);
                        if (CollectionUtils.isNotEmpty(rworkerCacheWorkSheets)
                                && rworkerCacheWorkSheets.stream().anyMatch(rworkerCacheWorkSheet -> rworkerCacheWorkSheet.getSerialNumber().equals(serialNumber))
                                && rworkerCache.getStep().getId().equals(batchWorkDetail.getStep().getId())) {
                            rworkerCacheRepository.delete(rworkerCache);
                        }
                    }
                }
            }));
        }
        //若SN详情列表存在则需要进一步删除SN缓存
        if (CollectionUtils.isNotEmpty(snWorkDetailList)) {
            snWorkDetailList.forEach(snWorkDetail -> rworkerCacheRepository.findByCategoryAndSerialNumber(Constants.INT_THREE, "\"" + snWorkDetail.getSn() + "\"").ifPresent(rworkerCache -> {
                if (StringUtils.isNotBlank(rworkerCache.getCache())) {
                    JSONObject jsonObject = JSON.parseObject(rworkerCache.getCache());
                    if (Objects.nonNull(jsonObject.get("selectedWorkSheets"))) {
                        List<RworkerCacheWorkSheet> rworkerCacheWorkSheets = JSON.parseArray(jsonObject.get("selectedWorkSheets").toString(), RworkerCacheWorkSheet.class);
                        if (CollectionUtils.isNotEmpty(rworkerCacheWorkSheets)
                                && rworkerCacheWorkSheets.stream().anyMatch(rworkerCacheWorkSheet -> rworkerCacheWorkSheet.getSerialNumber().equals(serialNumber))
                                && rworkerCache.getStep().getId().equals(batchWorkDetail.getStep().getId())) {
                            rworkerCacheRepository.delete(rworkerCache);
                        }
                    }
                }
            }));
        }
    }

    /**
     * 通过容器详情删除缓存
     *
     * @param containerDetail  容器详情
     * @param snWorkDetailList SN详情列表
     */
    @Override
    public void deleteCacheByContainerDetail(ContainerDetail containerDetail, List<SnWorkDetail> snWorkDetailList) {
        BatchWorkDetail batchWorkDetail = containerDetail.getBatchWorkDetail();
        String serialNumber = Objects.nonNull(batchWorkDetail.getSubWorkSheet()) ? batchWorkDetail.getSubWorkSheet().getSerialNumber() : batchWorkDetail.getWorkSheet().getSerialNumber();
        rworkerCacheRepository.findByCategoryAndSerialNumber(Constants.INT_TWO, "\"" + containerDetail.getContainerCode() + "\"").ifPresent(rworkerCache -> {
            if (StringUtils.isNotBlank(rworkerCache.getCache())) {
                JSONObject jsonObject = JSON.parseObject(rworkerCache.getCache());
                if (Objects.nonNull(jsonObject.get("selectedWorkSheets"))) {
                    List<RworkerCacheWorkSheet> rworkerCacheWorkSheets = JSON.parseArray(jsonObject.get("selectedWorkSheets").toString(), RworkerCacheWorkSheet.class);
                    if (CollectionUtils.isNotEmpty(rworkerCacheWorkSheets)
                            && rworkerCacheWorkSheets.stream().anyMatch(rworkerCacheWorkSheet -> rworkerCacheWorkSheet.getSerialNumber().equals(serialNumber))
                            && rworkerCache.getStep().getId().equals(containerDetail.getBatchWorkDetail().getStep().getId())) {
                        rworkerCacheRepository.delete(rworkerCache);
                    }
                }
            }
        });
        //若SN详情列表存在则需要进一步删除SN缓存
        if (CollectionUtils.isNotEmpty(snWorkDetailList)) {
            snWorkDetailList.forEach(snWorkDetail -> rworkerCacheRepository.findByCategoryAndSerialNumber(Constants.INT_THREE, "\"" + snWorkDetail.getSn() + "\"").ifPresent(rworkerCache -> {
                if (StringUtils.isNotBlank(rworkerCache.getCache())) {
                    JSONObject jsonObject = JSON.parseObject(rworkerCache.getCache());
                    if (Objects.nonNull(jsonObject.get("selectedWorkSheets"))) {
                        List<RworkerCacheWorkSheet> rworkerCacheWorkSheets = JSON.parseArray(jsonObject.get("selectedWorkSheets").toString(), RworkerCacheWorkSheet.class);
                        if (CollectionUtils.isNotEmpty(rworkerCacheWorkSheets)
                                && rworkerCacheWorkSheets.stream().anyMatch(rworkerCacheWorkSheet -> rworkerCacheWorkSheet.getSerialNumber().equals(serialNumber))
                                && rworkerCache.getStep().getId().equals(batchWorkDetail.getStep().getId())) {
                            rworkerCacheRepository.delete(rworkerCache);
                        }
                    }
                }
            }));
        }
    }

    /**
     * 通过SN详情删除缓存
     *
     * @param snWorkDetail SN详情
     */
    @Override
    public void deleteCacheBySnWorkDetail(SnWorkDetail snWorkDetail) {
        String serialNumber = Objects.nonNull(snWorkDetail.getSubWorkSheet()) ? snWorkDetail.getSubWorkSheet().getSerialNumber() : snWorkDetail.getWorkSheet().getSerialNumber();
        rworkerCacheRepository.findByCategoryAndSerialNumber(Constants.INT_THREE, "\"" + snWorkDetail.getSn() + "\"").ifPresent(rworkerCache -> {
            if (StringUtils.isNotBlank(rworkerCache.getCache())) {
                JSONObject jsonObject = JSON.parseObject(rworkerCache.getCache());
                if (Objects.nonNull(jsonObject.get("selectedWorkSheets"))) {
                    List<RworkerCacheWorkSheet> rworkerCacheWorkSheets = JSON.parseArray(jsonObject.get("selectedWorkSheets").toString(), RworkerCacheWorkSheet.class);
                    if (CollectionUtils.isNotEmpty(rworkerCacheWorkSheets)
                            && rworkerCacheWorkSheets.stream().anyMatch(rworkerCacheWorkSheet -> rworkerCacheWorkSheet.getSerialNumber().equals(serialNumber))
                            && rworkerCache.getStep().getId().equals(snWorkDetail.getStep().getId())) {
                        rworkerCacheRepository.delete(rworkerCache);
                    }
                }
            }
        });
    }


    /**
     * rworker缓存投产工单信息类(目前仅供删除转换判断使用)
     */
    private static class RworkerCacheWorkSheet {
        /**
         * 投产(子)工单ID
         */
        private Long productWorkSheetId;

        /**
         * 工单ID
         */
        private Long workSheetId;

        /**
         * 投产工单序列号
         */
        private String serialNumber;

        public Long getProductWorkSheetId() {
            return productWorkSheetId;
        }

        public RworkerCacheWorkSheet setProductWorkSheetId(Long productWorkSheetId) {
            this.productWorkSheetId = productWorkSheetId;
            return this;
        }

        public Long getWorkSheetId() {
            return workSheetId;
        }

        public RworkerCacheWorkSheet setWorkSheetId(Long workSheetId) {
            this.workSheetId = workSheetId;
            return this;
        }

        public String getSerialNumber() {
            return serialNumber;
        }

        public RworkerCacheWorkSheet setSerialNumber(String serialNumber) {
            this.serialNumber = serialNumber;
            return this;
        }
    }
}
