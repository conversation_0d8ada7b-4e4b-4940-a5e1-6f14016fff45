package net.airuima.rworker.service.rworker.facility;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/6/6
 */
@FuncDefault
public interface IFacilityMaintainService {

    /**
     * 验证设备保养是否预期未做
     * @param facilityIdList 设备主键ID列表
     */
    @FuncInterceptor(value = "FBase && FMaintenance")
    default void validateFacilityMaintain(List<Long> facilityIdList){

    }
}
