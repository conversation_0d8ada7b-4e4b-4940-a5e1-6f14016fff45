package net.airuima.rworker.service.rworker.process;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.dto.rworker.process.dto.RworkerBatchToDoStepGetDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerBatchToDoStepRequestDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerStepProcessBaseDTO;

import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * Rworker生产过程管控批量请求Service
 * <AUTHOR>
 * @date 2023/1/6
 */
@FuncDefault
public interface IBatchProcessRequestService {

    /**
     * 验证子工单状态是否能进行投产
     *
     * @param subWorkSheet  子工单
     */
    default void validateSubWorkSheetStatus(SubWorkSheet subWorkSheet){

    }

    /**
     * 验证工单状态是否能进行投产
     *
     * @param workSheet  工单
     */
    default void validateWorkSheetStatus(WorkSheet workSheet){

    }

    /**
     * 获取工单请求模式下的待做工序信息
     * @param rworkerBatchToDoStepRequestDTO 请求批量待做工序参数
     * @return net.airuima.rbase.web.rest.rworker.process.dto.RworkerBatchToDoStepGetDTO 批量待做工序信息
     */
    default RworkerBatchToDoStepGetDTO batchToDoStep(RworkerBatchToDoStepRequestDTO rworkerBatchToDoStepRequestDTO){
        return new RworkerBatchToDoStepGetDTO();
    }


    /**
     * 获取子工单粒度下的下个待投产工序信息
     * @param rworkerBatchToDoStepRequestDTO 批量待做工序参数
     * @param rworkerStepProcessBaseDTO 通用基础信息
     * @return net.airuima.rbase.web.rest.rworker.process.dto.RworkerBatchToDoStepGetDTO  批量待做工序信息
     */
    default RworkerBatchToDoStepGetDTO findSubWsNextTodoStep(RworkerBatchToDoStepRequestDTO rworkerBatchToDoStepRequestDTO, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO){
        return new RworkerBatchToDoStepGetDTO();
    }

    /**
     * 获取工单粒度下的下个待投产工序信息
     * @param rworkerBatchToDoStepRequestDTO 批量待做工序参数
     * @param rworkerStepProcessBaseDTO 通用基础信息
     * @return net.airuima.rbase.web.rest.rworker.process.dto.RworkerBatchToDoStepGetDTO  批量待做工序信息
     */
    default RworkerBatchToDoStepGetDTO findWorkSheetNextToDoStep(RworkerBatchToDoStepRequestDTO rworkerBatchToDoStepRequestDTO, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        return new RworkerBatchToDoStepGetDTO();
    }

    /**
     * 获取工单请求模式下的在线调整工序的投产数量
     *
     * @param workSheetId         工单主键ID（子工单主键ID）
     * @param stepId              工序主键ID
     * @param subWsProductionMode 投产粒度(true:子工单,false:工单)
     * @return int 在线调整数量
     */
    @FuncInterceptor(value = "OnlineAdjustment")
    default int findOnlineReworkStepTransferNumber(long workSheetId,long stepId,boolean subWsProductionMode,RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO){
        return Constants.INT_ZERO;
    }


    /**
     * 预留请求工序扩展接口
     * @param rworkerBatchToDoStepRequestDTO 请求工序参数
     * @param rworkerBatchToDoStepGetDTO 返回参数
     * @return  net.airuima.rbase.web.rest.rworker.process.dto.RworkerBatchToDoStepGetDTO  批量待做工序信息
     */
    default RworkerBatchToDoStepGetDTO findExtendInfo(RworkerBatchToDoStepRequestDTO rworkerBatchToDoStepRequestDTO, RworkerBatchToDoStepGetDTO rworkerBatchToDoStepGetDTO){
        return rworkerBatchToDoStepGetDTO;
    }

    /**
     * 获取下个工序待投产数量
     *
     * @param rworkerBatchToDoStepGetDTO 请求返回批量待做工序信息DTO
     * @param batchWorkDetails           工单批量详情数据
     * @param nextTodoWsStep             下个待做的工序快照
     * @param workSheetNumber            工单(子工单)投产数量
     * <AUTHOR>
     * @date 2023/3/24
     **/
    default void findNextTodoStepNumber(RworkerBatchToDoStepGetDTO rworkerBatchToDoStepGetDTO, List<BatchWorkDetail> batchWorkDetails, WsStep nextTodoWsStep, int workSheetNumber) {
    }

}
