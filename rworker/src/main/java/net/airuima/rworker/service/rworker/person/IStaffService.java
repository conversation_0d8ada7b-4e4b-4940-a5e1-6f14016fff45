package net.airuima.rworker.service.rworker.person;

import jakarta.servlet.http.HttpServletRequest;
import net.airuima.config.annotation.FuncDefault;
import net.airuima.rbase.dto.rworker.person.dto.StaffLoginDTO;
import net.airuima.rbase.dto.rworker.person.dto.StaffLoginGetDTO;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 * Rworker员工登陆Service
 * <AUTHOR>
 * @date 2022/12/20
 */
@FuncDefault
public interface IStaffService {

    /**
     * 员工请求登陆
     * @param staffRequestDTO 员工登陆请求参数
     * @param httpServletRequest HttpServletRequest
     * @return net.airuima.rbase.web.rest.rworker.person.dto.StaffLoginGetDTO 员工登陆返回信息
     */
    default StaffLoginGetDTO login(StaffLoginDTO staffRequestDTO, HttpServletRequest httpServletRequest){
        return new StaffLoginGetDTO();
    }
}
