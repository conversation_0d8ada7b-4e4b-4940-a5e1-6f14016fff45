package net.airuima.rworker.service.rworker.quality.impl;

import com.google.common.collect.ImmutableBiMap;
import net.airuima.rbase.constant.*;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckItem;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.quality.StepWarningStandard;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.base.quality.UnqualifiedItemWarningStandard;
import net.airuima.rbase.domain.base.quality.WorkCellCheckStartRule;
import net.airuima.rbase.domain.base.scene.OrganizationArea;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.base.scene.WorkCellStep;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.*;
import net.airuima.rbase.domain.procedure.quality.*;
import net.airuima.rbase.domain.procedure.reinspect.StepReinspect;
import net.airuima.rbase.domain.procedure.single.SnUnqualifiedItem;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.dto.document.DocumentDTO;
import net.airuima.rbase.dto.document.DocumentRelationDTO;
import net.airuima.rbase.dto.dynamic.StepDynamicDataColumnGetDTO;
import net.airuima.rbase.dto.maintain.MaintainHistoryDTO;
import net.airuima.rbase.dto.organization.OrganizationDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.rbase.dto.qms.DefectDTO;
import net.airuima.rbase.dto.qms.TestDataDTO;
import net.airuima.rbase.dto.qms.TestDataSaveDTO;
import net.airuima.rbase.dto.qms.VarietyDTO;
import net.airuima.rbase.dto.quality.InspectionTaskDTO;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerBatchStepSaveRequestDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerContainerStepSaveRequestDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerStepProcessBaseDTO;
import net.airuima.rbase.dto.rworker.process.dto.general.UnqualifiedItemGetInfo;
import net.airuima.rbase.dto.rworker.process.dto.general.UnqualifiedItemSaveInfo;
import net.airuima.rbase.dto.rworker.quality.dto.*;
import net.airuima.rbase.proxy.document.RbaseDocumentProxy;
import net.airuima.rbase.proxy.maintain.RbaseMaintainHistoryProxy;
import net.airuima.rbase.proxy.oem.RbaseOemProxy;
import net.airuima.rbase.proxy.organization.RbaseStaffProxy;
import net.airuima.rbase.proxy.qms.RbaseDefectCheckItemProxy;
import net.airuima.rbase.proxy.qms.RbaseDefectProxy;
import net.airuima.rbase.proxy.qms.RbaseVarietyProxy;
import net.airuima.rbase.proxy.rqms.RbaseQmsProxy;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepCheckItemRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepCheckRuleRepository;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.quality.StepWarningStandardRepository;
import net.airuima.rbase.repository.base.quality.UnqualifiedItemRepository;
import net.airuima.rbase.repository.base.quality.UnqualifiedItemWarningStandardRepository;
import net.airuima.rbase.repository.base.quality.WorkCellCheckStartRuleRepository;
import net.airuima.rbase.repository.base.scene.OrganizationAreaRepository;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.repository.base.scene.WorkCellStepRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.*;
import net.airuima.rbase.repository.procedure.quality.*;
import net.airuima.rbase.repository.procedure.reinspect.StepReinspectRepository;
import net.airuima.rbase.repository.procedure.single.SnUnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkStatusRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.procedure.quality.CheckHistoryService;
import net.airuima.rbase.service.procedure.quality.IqcCheckHistoryService;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.rworker.proxy.RworkerQuerySamplingStrategyProxy;
import net.airuima.rworker.service.enviroment.PHumitureCheckHistoryService;
import net.airuima.rworker.service.enviroment.PHumitureStandardRepository;
import net.airuima.rworker.service.enviroment.dto.HumitureCheckHistoryDTO;
import net.airuima.rworker.service.enviroment.dto.HumitureStandardDTO;
import net.airuima.rworker.service.rworker.event.IEventService;
import net.airuima.rworker.service.rworker.quality.IInspectionService;
import net.airuima.rworker.service.rworker.quality.IQualityService;
import net.airuima.rworker.web.rest.rworker.quality.dto.RworkerCancelTaskDTO;
import net.airuima.rworker.web.rest.rworker.quality.dto.RworkerQualityCacheSaveDTO;
import net.airuima.util.BeanUtil;
import net.airuima.util.FuncKeyUtil;
import net.airuima.util.ResponseException;
import net.airuima.util.ValidateUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/22
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class QualityServiceImpl implements IQualityService {
    private final String WC_NOT_INSPECTION_WS_PUT_INTO_PRODUCTION_STEP = "WcNotInspectionWsPutIntoProductionStep";
    private final String NOT_EXIST_PRODUCT_WORK_SHEET = "notExistProductWorkSheet";
    private final String NOT_EXIST_PRODUCT_WORK_SHEET_MSG = "检测工单不存在";
    private final String PRODUCTION_WORK_SHEET_IS_NOT_EXIST = "ProductionWorkSheetIsNotExist";
    private final String WARNING = "warning";

    @Autowired
    private CommonService commonService;
    @Autowired
    private StepRepository stepRepository;
    @Autowired
    private UnqualifiedItemRepository unqualifiedItemRepository;
    @Autowired
    private WsStepUnqualifiedItemRepository wsStepUnqualifiedItemRepository;
    @Autowired
    private ContainerDetailUnqualifiedItemRepository containerDetailUnqualifiedItemRepository;
    @Autowired
    private WorkCellRepository workCellRepository;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private WorkCellCheckStartRuleRepository workCellCheckStartRuleRepository;
    @Autowired
    private LatestCheckResultRepository latestCheckResultRepository;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private WorkCellStepRepository workCellStepRepository;
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private PedigreeStepCheckItemRepository pedigreeStepCheckItemRepository;
    @Autowired
    private CheckHistoryRepository checkHistoryRepository;
    @Autowired
    private CheckHistoryDetailRepository checkHistoryDetailRepository;
    @Autowired
    private StepWarningStandardRepository stepWarningStandardRepository;
    @Autowired
    private UnqualifiedEventRepository unqualifiedEventRepository;
    @Autowired
    private UnqualifiedItemWarningStandardRepository unqualifiedItemWarningStandardRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private RbaseMaintainHistoryProxy rbaseMaintainHistoryProxy;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private RworkerQuerySamplingStrategyProxy querySamplingStrategyService;
    @Autowired
    private RbaseDefectCheckItemProxy defectCheckItemRepository;
    @Autowired
    private PedigreeStepCheckRuleRepository pedigreeStepCheckRuleRepository;
    @Autowired
    private RbaseVarietyProxy varietyRepository;
    @Autowired
    private RbaseDefectProxy defectRepository;
    @Autowired
    private CheckHistoryItemSnapshotRepository checkHistoryItemSnapshotRepository;
    @Autowired
    private InspectTaskRepository inspectTaskRepository;
    @Autowired
    private SnWorkDetailRepository snWorkDetailRepository;
    @Autowired
    private CheckHistoryService checkHistoryService;
    @Autowired
    private IInspectionService[] inspectionServices;
    @Autowired
    private IEventService[] eventServices;
    @Autowired
    private SnUnqualifiedItemRepository snUnqualifiedItemRepository;
    @Autowired
    private OrganizationAreaRepository organizationAreaRepository;
    @Autowired
    private PHumitureStandardRepository humitureStandardRepository;
    @Autowired
    private PHumitureCheckHistoryService humitureCheckHistoryService;
    @Autowired
    private IqcCheckHistoryService iqcCheckHistoryService;
    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;
    @Autowired
    private StepReinspectRepository stepReinspectRepository;
    @Autowired
    private RbaseDocumentProxy rbaseDocumentProxy;
    @Autowired
    private IqcCheckHistoryRepository iqcCheckHistoryRepository;
    @Autowired
    private RbaseQmsProxy rbaseQmsProxy;
    @Autowired
    private RbaseStaffProxy rbaseStaffProxy;
    @Autowired
    private RbaseOemProxy rbaseOemProxy;
    @Autowired
    private SnWorkStatusRepository snWorkStatusRepository;


    /**
     * 获取待投产工序绑定的不良项目信息
     *
     * @param stepId     工序主键ID
     * @param pedigree   产品谱系
     * @param workFlowId 工艺路线主键ID
     * @param clientId   客户主键id
     * @return java.util.List<net.airuima.web.rest.rworker.process.dto.general.UnqualifiedItemGetInfo> 工序不合格项目信息
     */
    @Override
    @Transactional(readOnly = true)
    public List<UnqualifiedItemGetInfo> findStepUnqualifiedItemInfo(long stepId, Pedigree pedigree, long workFlowId, Long clientId) {
        //获取工序不良项目,优先获取产品谱系配置的工序不良项目，最后才获取工序默认配置的不良项目
        List<UnqualifiedItem> unqualifiedItems = commonService.findPedigreeStepUnqualifiedItem(pedigree, workFlowId, stepId, clientId);
        if (!ValidateUtils.isValid(unqualifiedItems)) {
            return Lists.newArrayList();
        }
        return unqualifiedItems.stream().map(UnqualifiedItemGetInfo::new).collect(Collectors.toList());
    }


    /**
     * 保存工序生产不良信息
     *
     * @param subWsProductionMode     true:子工单粒度，false:工单粒度
     * @param batchWorkDetail         批量工序生产详情
     * @param unqualifiedItemInfoList 工序对应不良项目信息列表
     */
    @Override
    public void saveBatchWorkDetailUnqualifiedItem(boolean subWsProductionMode, BatchWorkDetail batchWorkDetail, List<UnqualifiedItemSaveInfo> unqualifiedItemInfoList) {
        if (!ValidateUtils.isValid(unqualifiedItemInfoList)) {
            return;
        }
        unqualifiedItemInfoList.forEach(unqualifiedItemInfo -> {
            if (unqualifiedItemInfo.getNumber() <= net.airuima.constant.Constants.INT_ZERO) {
                return;
            }
            WsStepUnqualifiedItem wsStepUnqualifiedItem;
            if (subWsProductionMode) {
                wsStepUnqualifiedItem = wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(batchWorkDetail.getSubWorkSheet().getId(), batchWorkDetail.getStep().getId(), unqualifiedItemInfo.getId(), Constants.LONG_ZERO).orElse(new WsStepUnqualifiedItem());
            } else {
                wsStepUnqualifiedItem = wsStepUnqualifiedItemRepository.findByWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(batchWorkDetail.getWorkSheet().getId(), batchWorkDetail.getStep().getId(), unqualifiedItemInfo.getId(), Constants.LONG_ZERO).orElse(new WsStepUnqualifiedItem());
            }
            wsStepUnqualifiedItem.setUnqualifiedItem(unqualifiedItemRepository.getReferenceById(unqualifiedItemInfo.getId()))
                    .setWorkSheet(subWsProductionMode ? null : batchWorkDetail.getWorkSheet())
                    .setSubWorkSheet(subWsProductionMode ? batchWorkDetail.getSubWorkSheet() : null)
                    .setStep(batchWorkDetail.getStep())
                    .setNumber(wsStepUnqualifiedItem.getNumber() + unqualifiedItemInfo.getNumber())
                    .setOperatorId(batchWorkDetail.getOperatorId())
                    .setRecordDate(LocalDate.now())
                    .setDeleted(Constants.LONG_ZERO);
            wsStepUnqualifiedItemRepository.save(wsStepUnqualifiedItem);
        });
    }

    /**
     * 保存容器生产详情不良信息
     *
     * @param containerDetail         容器生产详情
     * @param unqualifiedItemInfoList 工序对应不良项目信息列表
     */
    @Override
    public void saveContainerWorkDetailUnqualifiedItem(ContainerDetail containerDetail, List<UnqualifiedItemSaveInfo> unqualifiedItemInfoList) {
        if (!ValidateUtils.isValid(unqualifiedItemInfoList)) {
            return;
        }
        List<UnqualifiedItem> unqualifiedItems = unqualifiedItemRepository.findByIdInAndDeleted(unqualifiedItemInfoList.stream().map(UnqualifiedItemSaveInfo::getId).collect(Collectors.toList()), Constants.LONG_ZERO);
        Map<Long, List<UnqualifiedItem>> unqualifiedItemGroup = unqualifiedItems.stream().collect(Collectors.groupingBy(UnqualifiedItem::getId));
        unqualifiedItemInfoList.forEach(unqualifiedItemInfo -> {
            if (unqualifiedItemInfo.getNumber() <= net.airuima.constant.Constants.INT_ZERO) {
                return;
            }
            ContainerDetailUnqualifiedItem containerDetailUnqualifiedItem = containerDetailUnqualifiedItemRepository.findByContainerDetailIdAndUnqualifiedItemIdAndDeleted(containerDetail.getId(), unqualifiedItemInfo.getId(), Constants.LONG_ZERO).orElse(new ContainerDetailUnqualifiedItem());
            containerDetailUnqualifiedItem.setContainerDetail(containerDetail).setUnqualifiedItem(unqualifiedItemGroup.get(unqualifiedItemInfo.getId()).get(Constants.INT_ZERO)).setNumber(containerDetailUnqualifiedItem.getNumber() + unqualifiedItemInfo.getNumber()).setDeleted(Constants.LONG_ZERO);
            containerDetailUnqualifiedItemRepository.save(containerDetailUnqualifiedItem);
        });
    }


    /**
     * 保存sn生产详情不良信息
     *
     * @param snWorkDetail            sn生产详情
     * @param unqualifiedItemSaveInfo 工序对应不良项目信息列表
     */
    @Override
    public void saveSnWorkDetailUnqualifiedItem(SnWorkDetail snWorkDetail, UnqualifiedItemSaveInfo unqualifiedItemSaveInfo) {
        if (ObjectUtils.isEmpty(unqualifiedItemSaveInfo)) {
            return;
        }
        SnUnqualifiedItem snUnqualifiedItem = new SnUnqualifiedItem();
        snUnqualifiedItem.setSn(snWorkDetail.getSn()).setWorkSheet(snWorkDetail.getWorkSheet()).setSubWorkSheet(snWorkDetail.getSubWorkSheet())
                .setStep(snWorkDetail.getStep()).setUnqualifiedItem(new UnqualifiedItem(unqualifiedItemSaveInfo.getId())).setFlag(Boolean.FALSE).setSnWorkDetail(snWorkDetail);
        snUnqualifiedItemRepository.save(snUnqualifiedItem);
    }

    /**
     * 通过检测工位、投产工单获取可检测的生产工位及对应工序列表
     *
     * @param rworkerCheckWorkCellStepDto 请求首检工位绑定工序请求参数
     * @return net.airuima.web.rest.rworker.quality.dto.RworkerToCheckWorkCellStepGetDTO 投产工单待检工位工序信息
     * <AUTHOR>
     * @date 2023/1/31
     */
    @Override
    public RworkerToCheckWorkCellStepGetDTO getToInspectedWorkCellAndStepInfo(RworkerCheckWorkCellStepDTO rworkerCheckWorkCellStepDto) {
        RworkerToCheckWorkCellStepGetDTO rworkerToCheckWorkCellStepGetDto = new RworkerToCheckWorkCellStepGetDTO();

        //获取系统配置的投产粒度(子工单或者工单)
        boolean subWsProductionMode = commonService.subWsProductionMode();
        SubWorkSheet subWorkSheet = subWsProductionMode ? subWorkSheetRepository.findBySerialNumberAndDeleted(rworkerCheckWorkCellStepDto.getSerialNumber(), Constants.LONG_ZERO).orElse(null) : null;
        WorkSheet workSheet = subWsProductionMode && !ObjectUtils.isEmpty(subWorkSheet) ? subWorkSheet.getWorkSheet() : workSheetRepository.findBySerialNumberAndDeleted(rworkerCheckWorkCellStepDto.getSerialNumber(), Constants.LONG_ZERO).orElse(null);

        if (ObjectUtils.isEmpty(workSheet)) {
            throw new ResponseException(PRODUCTION_WORK_SHEET_IS_NOT_EXIST, NOT_EXIST_PRODUCT_WORK_SHEET_MSG);
        }
        WorkCell workCell = workCellRepository.findByIdAndDeleted(rworkerCheckWorkCellStepDto.getWorkCellId(), Constants.LONG_ZERO).orElse(null);
        if (ObjectUtils.isEmpty(workCell)) {
            throw new ResponseException("error.InspectedWorkCellIsNotExist", "当前检测工位不存在");
        }
        if (workCell.getCategory() != WorkCellEnum.FIRST_INSPECTION_WC.getCategory() && workCell.getCategory() != WorkCellEnum.PQC_WC.getCategory()) {
            throw new ResponseException("error.NonFirstPQCInspectionWorkCell", "当前工位非(首检/PQC工位)");
        }
        List<Step> stepList = workCellStepRepository.findByWorkCellId(workCell.getId());
        if (!ValidateUtils.isValid(stepList)) {
            throw new ResponseException("error.WorkCellNotBindStep", "工位未绑定任何工序");
        }
        List<WsStep> wsStepList = Lists.newArrayList();
        if (subWsProductionMode) {
            List<WsStep> subWsSteps = wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
            if (ValidateUtils.isValid(subWsSteps)) {
                wsStepList.addAll(subWsSteps);
            }
        }
        if (!ValidateUtils.isValid(wsStepList)) {
            List<WsStep> wsSteps = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            wsStepList.addAll(wsSteps);
        }
        if (!ValidateUtils.isValid(wsStepList)) {
            throw new ResponseException("error.WsNotPutIntoProductionStep", "当前工单不存在投产工序");
        }
        List<Step> todoStepList = wsStepList.stream().filter(wsStep -> stepList.stream().anyMatch(step -> wsStep.getStep().getId().equals(step.getId()))).map(WsStep::getStep).collect(Collectors.toList());
        if (!ValidateUtils.isValid(todoStepList)) {
            throw new ResponseException(WC_NOT_INSPECTION_WS_PUT_INTO_PRODUCTION_STEP, "当前工位不能检测工单投产工序");
        }

        //获取当前检测工位能够检测的生产工位以及对应的工序列表
        List<WorkCellStep> workCellSteps = workCellStepRepository.findByStepIdInAndWorkCellCategoryAndDeleted(todoStepList.stream().map(Step::getId).collect(Collectors.toList()), Constants.INT_ONE, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(workCellSteps)) {
            throw new ResponseException(WC_NOT_INSPECTION_WS_PUT_INTO_PRODUCTION_STEP, "当前生产部门的生产工位不能生产检测工位里的任何工序");
        }
        WorkCellCategoryCorrespondenceEnums workCellCategoryCorrespondenceEnum = WorkCellCategoryCorrespondenceEnums.getKey(workCell.getCategory());
        if (ObjectUtils.isEmpty(workCellCategoryCorrespondenceEnum)) {
            throw new ResponseException("error. workCellCategoryCorrespondenceIsNotExist", "工位对应的检测规则不存在");
        }
        List<WorkCellStep> workCellStepList = Lists.newArrayList();
        //获取当前工位同部门，同产线得工位对应工序列表
        if (!ObjectUtils.isEmpty(workCell.getOrganizationId()) && !ObjectUtils.isEmpty(workSheet.getOrganizationId())) {
            workCellStepList = workCellSteps.stream().filter(workCellStep -> !ObjectUtils.isEmpty(workCellStep.getWorkCell().getOrganizationId())).filter(workCellStep -> workCellStep.getWorkCell().getOrganizationId().equals(workSheet.getOrganizationId())).collect(Collectors.toList());

        } else if (!ObjectUtils.isEmpty(subWorkSheet) && !ObjectUtils.isEmpty(subWorkSheet.getWorkLine()) && !ObjectUtils.isEmpty(workCell.getWorkLine())) {
            workCellStepList = workCellSteps.stream().filter(workCellStep -> !ObjectUtils.isEmpty(workCellStep.getWorkCell().getWorkLine())).filter(workCellStep -> workCellStep.getWorkCell().getWorkLine().getId().equals(subWorkSheet.getWorkLine().getId())).collect(Collectors.toList());

        } else if (!ObjectUtils.isEmpty(workSheet.getWorkLine()) && !ObjectUtils.isEmpty(workCell.getWorkLine())) {
            workCellStepList = workCellSteps.stream().filter(workCellStep -> !ObjectUtils.isEmpty(workCellStep.getWorkCell().getWorkLine())).filter(workCellStep -> workCellStep.getWorkCell().getWorkLine().getId().equals(workSheet.getWorkLine().getId())).collect(Collectors.toList());
        }
        if (!ValidateUtils.isValid(workCellStepList)) {
            throw new ResponseException(WC_NOT_INSPECTION_WS_PUT_INTO_PRODUCTION_STEP, "当前生产部门的生产工位不能生产检测工位里的任何工序");
        }
        rworkerToCheckWorkCellStepGetDto = subWsProductionMode ? rworkerToCheckWorkCellStepGetDto.setProductWorkSheetId(subWorkSheet.getId()) : rworkerToCheckWorkCellStepGetDto.setProductWorkSheetId(workSheet.getId());
        //集成可以检查的工位对应工序
        List<RworkerToCheckWorkCellStepGetDTO.ToCheckWorkCellStepInfo> workCellStepInfos = Lists.newArrayList();
        workCellStepList.stream().collect(Collectors.groupingBy(WorkCellStep::getWorkCell)).forEach((workCellTemp, toCheckWorkCellSteps) -> {
            RworkerToCheckWorkCellStepGetDTO.ToCheckWorkCellStepInfo workCellStepInfo = new RworkerToCheckWorkCellStepGetDTO.ToCheckWorkCellStepInfo();
            List<RworkerToCheckWorkCellStepGetDTO.StepInfo> stepInfos = Lists.newArrayList();
            toCheckWorkCellSteps.forEach(workCellStep -> stepInfos.add(new RworkerToCheckWorkCellStepGetDTO.StepInfo(workCellStep.getStep())));
            workCellStepInfo.setWorkCellName(workCellTemp.getName()).setWorkCellId(workCellTemp.getId()).setStepInfoList(stepInfos);
            workCellStepInfos.add(workCellStepInfo);
        });
        //返回首检/巡检类型
        rworkerToCheckWorkCellStepGetDto.setCategory(workCellCategoryCorrespondenceEnum.getCheckRuleCategory());
        return rworkerToCheckWorkCellStepGetDto.setToCheckWorkCellStepInfos(workCellStepInfos);
    }

    /**
     * 通过投产工单及工序获取检测规则及检测项目信息
     *
     * @param rworkerCheckItemRequestDto 请求验证首中末QC检请求参数
     * @return net.airuima.web.rest.rworker.quality.dto.RworkerCheckRuleInfoGetDTO 检查项目规则
     * <AUTHOR>
     * @date 2023/1/31
     */
    @Override
    public RworkerCheckRuleInfoGetDTO getCheckRuleInfo(RworkerCheckItemRequestDTO rworkerCheckItemRequestDto) {
        //获取系统配置的投产粒度(子工单或者工单)
        boolean subWsProductionMode = commonService.subWsProductionMode();
        SubWorkSheet subWorkSheet = subWsProductionMode ? subWorkSheetRepository.getReferenceById(rworkerCheckItemRequestDto.getProductWorkSheetId()) : null;
        WorkSheet workSheet = subWsProductionMode ? subWorkSheet.getWorkSheet() : workSheetRepository.getReferenceById(rworkerCheckItemRequestDto.getProductWorkSheetId());
        if (ObjectUtils.isEmpty(workSheet)) {
            throw new ResponseException(PRODUCTION_WORK_SHEET_IS_NOT_EXIST, NOT_EXIST_PRODUCT_WORK_SHEET_MSG);
        }
        Step step = stepRepository.findByIdAndDeleted(rworkerCheckItemRequestDto.getStepId(), Constants.LONG_ZERO).orElse(null);
        if (ObjectUtils.isEmpty(step)) {
            throw new ResponseException("error.TodoInspectionStepIsNotExist", "当前待检测工序不存在");
        }
        //获取定制工序中工艺路线
        PedigreeStepCheckRule pedigreeStepCheckRule = commonService.findPedigreeStepCheckRule(workSheet, subWorkSheet, workSheet.getPedigree(), step, rworkerCheckItemRequestDto.getCategory(), rworkerCheckItemRequestDto.getWorkCellId(), null);
        if (ObjectUtils.isEmpty(pedigreeStepCheckRule)) {
            throw new ResponseException("error.TodoInspectionStepCheckRuleIsNotExist", "当前待检测工序未配置检测规则");
        }
        //分别获取谱系工序检测规则以及对应的检测项目
        RworkerCheckRuleInfoGetDTO checkRuleInfo = new RworkerCheckRuleInfoGetDTO();
        List<RworkerCheckRuleInfoGetDTO.CheckItemInfo> checkItemInfos = Lists.newArrayList();
        //添加检测项目
        List<PedigreeStepCheckItem> pedigreeStepCheckItemList = pedigreeStepCheckItemRepository.findByPedigreeStepCheckRuleIdAndDeleted(pedigreeStepCheckRule.getId(), Constants.LONG_ZERO);
        pedigreeStepCheckItemList = pedigreeStepCheckItemList.stream().filter(pedigreeStepCheckItem -> Objects.equals(
                pedigreeStepCheckItem.getCheckItem().getVariety(), pedigreeStepCheckRule.getVariety())).toList();
        double value = subWsProductionMode ? subWorkSheet.getNumber() * pedigreeStepCheckRule.getRate() : workSheet.getNumber() * pedigreeStepCheckRule.getRate();
        int sampleNumber = pedigreeStepCheckRule.getJudgeWay() == Constants.INT_ZERO ? pedigreeStepCheckRule.getBaseNumber() : (int) Math.ceil(value);
        if (ValidateUtils.isValid(pedigreeStepCheckItemList)) {
            pedigreeStepCheckItemList.forEach(pedigreeStepCheckItem ->
                    checkItemInfos.add(new RworkerCheckRuleInfoGetDTO.CheckItemInfo(pedigreeStepCheckItem, sampleNumber))
            );
        }
        checkRuleInfo.setNumber(sampleNumber)
                .setJudgeWay(pedigreeStepCheckRule.getJudgeWay())
                .setQualifiedNumber(pedigreeStepCheckRule.getQualifiedNumber())
                .setQualifiedRate(pedigreeStepCheckRule.getQualifiedRate())
                .setCheckItemInfoList(ValidateUtils.isValid(checkItemInfos) ? checkItemInfos : null)
                .setVariety(pedigreeStepCheckRule.getVariety());
        return checkRuleInfo;
    }

    /**
     * 更新最新检测结果数据,当首检合格时QC抽检按照周期往后顺延
     *
     * @param checkHistory               检测历史
     * @param category                   类型
     * @param result                     结果
     * @param rworkerCheckSaveRequestDto 下交保存参数
     * @return void
     **/
    public void updateLatestCheckResult(CheckHistory checkHistory, Integer category, Boolean result, RworkerCheckSaveRequestDTO rworkerCheckSaveRequestDto) {
        LatestCheckResult latestCheckResult = latestCheckResultRepository.findByWorkCellIdAndCategoryAndVarietyAndDeleted(checkHistory.getWorkCell().getId(), category, rworkerCheckSaveRequestDto.getVariety(), Constants.LONG_ZERO).orElseGet(LatestCheckResult::new);
        latestCheckResult.setCategory(category)
                .setRecordDate(LocalDateTime.now())
                .setResult(result)
                .setDealWay(result ? Constants.INT_ONE : Constants.INT_ZERO)
                .setStatus(result ? Constants.TRUE : Constants.FALSE)
                .setWorkCell(checkHistory.getWorkCell())
                .setSerialNumber(checkHistory.getSerialNumber())
                .setSubWorkSheet(checkHistory.getSubWorkSheet())
                .setDisplay(Boolean.TRUE)
                .setWorkSheet(checkHistory.getWorkSheet())
                .setVariety(rworkerCheckSaveRequestDto.getVariety())
                .setNextCheckDate(null)
                .setDeleted(Constants.LONG_ZERO);

        //检查有无配置周期性配置，若有则更新下次检测时间
        Optional<WorkCellCheckStartRule> workCellCheckStartRuleOptional = workCellCheckStartRuleRepository.findByWorkCellIdAndCategoryAndFlagAndVarietyAndIsEnableAndDeleted(latestCheckResult.getWorkCell().getId(), latestCheckResult.getCategory(), net.airuima.constant.Constants.INT_THREE, rworkerCheckSaveRequestDto.getVariety(), Boolean.TRUE, net.airuima.constant.Constants.LONG_ZERO);
        workCellCheckStartRuleOptional.ifPresent(workCellCheckStartRule -> latestCheckResult.setNextCheckDate(LocalDateTime.now().plusMinutes((long) ((workCellCheckStartRule.getDuration() + workCellCheckStartRule.getExtendTime()) * 60))));

        latestCheckResultRepository.save(latestCheckResult);
        //当首检合格时PQC抽检按照周期往后顺延
        if (category == WorkCellStartCheckEnum.FIRST_INSPECTION.getCategory() && checkHistory.getResult()) {
            Optional<WorkCellCheckStartRule> workCellCheckStartRuleOpt = workCellCheckStartRuleRepository.findByWorkCellIdAndCategoryAndFlagAndVarietyAndIsEnableAndDeleted(latestCheckResult.getWorkCell().getId(), net.airuima.constant.Constants.INT_ONE, net.airuima.constant.Constants.INT_THREE, rworkerCheckSaveRequestDto.getVariety(), Boolean.TRUE, net.airuima.constant.Constants.LONG_ZERO);
            workCellCheckStartRuleOpt.ifPresent(workCellCheckStartRule ->
                    this.updateLatestCheckResult(checkHistory, net.airuima.constant.Constants.INT_ONE, true, rworkerCheckSaveRequestDto)
            );
        }
    }

    /**
     * 获取生产工序预警标准
     *
     * @param batchStepSaveBaseInfo 工序生产过程通用保存参数
     * @return net.airuima.domain.base.quality.StepWarningStandard 生产工序预警标准
     */
    @Override
    public StepWarningStandard findStepWarningStandard(RworkerStepProcessBaseDTO batchStepSaveBaseInfo) {
        WorkSheet workSheet = batchStepSaveBaseInfo.getWorkSheet();
        Step step = batchStepSaveBaseInfo.getStep();
        //递归获取产品谱系所有父级ID集合
        List<Long> pedigreeIdList = commonService.getAllParent(workSheet.getPedigree());
        //获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = null != batchStepSaveBaseInfo.getWsStep() && null != batchStepSaveBaseInfo.getWsStep().getWorkFlow() ? batchStepSaveBaseInfo.getWsStep().getWorkFlow() : batchStepSaveBaseInfo.getWorkFlow();
        List<StepWarningStandard> stepWarningStandardList = stepWarningStandardRepository.findAllStandardByElementOrderByPriorityAndPedigree(pedigreeIdList, workSheet.getId(), workSheet.getCategory(), Objects.isNull(step.getStepGroup()) ? null : step.getStepGroup().getId(), step.getId(), snapshotWorkFlow.getId(), workSheet.getClientId(), Constants.INT_ZERO, Constants.LONG_ZERO);
        List<StepWarningStandard> stepWarningStandardListSort = Lists.newArrayList();
        if (!ValidateUtils.isValid(stepWarningStandardList)) {
            return null;
        }
        if (stepWarningStandardList.size() == Constants.INT_ONE) {
            return stepWarningStandardList.get(Constants.INT_ZERO);
        }
        //获取优先级排序最高集合
        StepWarningStandard stepWarningStandard = stepWarningStandardList.stream().min(Comparator.comparing(i -> i.getPriorityElementConfig().getPriority())).orElse(null);
        if (null == stepWarningStandard) {
            return null;
        }
        //取到优先级最高的预警标准列表
        stepWarningStandardList = stepWarningStandardList.stream().filter(i -> i.getPriorityElementConfig().getPriority() == stepWarningStandard.getPriorityElementConfig().getPriority()).collect(Collectors.toList());
        //如果优先级最高的只有一个则直接返回
        if (stepWarningStandardList.size() == Constants.INT_ONE) {
            return stepWarningStandardList.get(Constants.INT_ZERO);
        }
        //如果有多个则可能需要对产品谱系进行优先级排序（有可能产品谱系为空）
        List<StepWarningStandard> stepWarningStandardListPedigreeNull = stepWarningStandardList.stream().filter(i -> Objects.isNull(i.getPedigree())).toList();
        List<StepWarningStandard> stepWarningStandardListPedigreeNotNull = stepWarningStandardList.stream().filter(i -> !Objects.isNull(i.getPedigree())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(stepWarningStandardListPedigreeNull)) {
            stepWarningStandardListSort.addAll(stepWarningStandardListPedigreeNull);
        }
        if (!CollectionUtils.isEmpty(stepWarningStandardListPedigreeNotNull)) {
            //根据产品谱系类型进行正序排序（此时“预警条件”优先级均相同）
            stepWarningStandardListPedigreeNotNull = stepWarningStandardListPedigreeNotNull.stream().sorted(Comparator.comparing(i -> i.getPedigree().getType())).toList();
            stepWarningStandardListSort.addAll(stepWarningStandardListPedigreeNotNull);
        }
        return CollectionUtils.isEmpty(stepWarningStandardListSort) ? null : stepWarningStandardListSort.get(stepWarningStandardListSort.size() - Constants.INT_ONE);
    }


    /**
     * 通过工单、工序及不良项目获取产品谱系工序不良项目预警标准
     *
     * @param batchStepSaveBaseInfo 工序生产过程通用保存参数
     * @param unqualifiedItem       不良项目数据
     * @return net.airuima.domain.base.quality.UnqualifiedItemWarningStandard  产品谱系工序不良项目预警标准
     */
    @Override
    public UnqualifiedItemWarningStandard findUnqualifiedItemWaringStandard(RworkerStepProcessBaseDTO batchStepSaveBaseInfo, UnqualifiedItem unqualifiedItem) {
        WorkSheet workSheet = batchStepSaveBaseInfo.getWorkSheet();
        Step step = batchStepSaveBaseInfo.getStep();
        //递归获取产品谱系所有父级ID集合
        List<Long> pedigreeIdList = commonService.getAllParent(batchStepSaveBaseInfo.getWorkSheet().getPedigree());
        //获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = null != batchStepSaveBaseInfo.getWsStep() && null != batchStepSaveBaseInfo.getWsStep().getWorkFlow() ? batchStepSaveBaseInfo.getWsStep().getWorkFlow() : batchStepSaveBaseInfo.getWorkFlow();
        // 查出规则集合
        List<UnqualifiedItemWarningStandard> unqualifiedItemWarningStandardList = unqualifiedItemWarningStandardRepository.findAllStandardByElementOrderByPriorityAndPedigree(pedigreeIdList, workSheet.getId(), workSheet.getCategory(), step.getStepGroup() != null ? step.getStepGroup().getId() : null, step.getId(), snapshotWorkFlow.getId(), workSheet.getClientId() != null ? workSheet.getClientId() : null, unqualifiedItem.getId(), unqualifiedItem.getUnqualifiedGroup() != null ? unqualifiedItem.getUnqualifiedGroup().getId() : null, Constants.INT_ONE, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(unqualifiedItemWarningStandardList)) {
            return null;
        }
        if (unqualifiedItemWarningStandardList.size() == Constants.INT_ONE) {
            return unqualifiedItemWarningStandardList.get(Constants.INT_ZERO);
        }
        //获取优先级排序最高集合
        UnqualifiedItemWarningStandard unqualifiedItemWarningStandard = unqualifiedItemWarningStandardList.stream().min(Comparator.comparing(i -> i.getPriorityElementConfig().getPriority())).orElse(null);
        if (null == unqualifiedItemWarningStandard) {
            return null;
        }
        //取到优先级最高的预警标准列表
        unqualifiedItemWarningStandardList = unqualifiedItemWarningStandardList.stream().filter(i -> i.getPriorityElementConfig().getPriority() == unqualifiedItemWarningStandard.getPriorityElementConfig().getPriority()).collect(Collectors.toList());
        //如果优先级最高的只有一个则直接返回
        if (unqualifiedItemWarningStandardList.size() == Constants.INT_ONE) {
            return unqualifiedItemWarningStandardList.get(Constants.INT_ZERO);
        }
        List<UnqualifiedItemWarningStandard> unqualifiedItemWarningStandardListSort = Lists.newArrayList();
        //如果有多个则可能需要对产品谱系进行优先级排序（有可能产品谱系为空）
        List<UnqualifiedItemWarningStandard> unqualifiedItemWarningStandardListPedigreeNull = unqualifiedItemWarningStandardList.stream().filter(i -> Objects.isNull(i.getPedigree())).collect(Collectors.toList());
        List<UnqualifiedItemWarningStandard> unqualifiedItemWarningStandardListPedigreeNotNull = unqualifiedItemWarningStandardList.stream().filter(i -> !Objects.isNull(i.getPedigree())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(unqualifiedItemWarningStandardListPedigreeNull)) {
            unqualifiedItemWarningStandardListSort.addAll(unqualifiedItemWarningStandardListPedigreeNull);
        }
        if (!CollectionUtils.isEmpty(unqualifiedItemWarningStandardListPedigreeNotNull)) {
            //根据产品谱系类型进行正序排序（此时“预警条件”优先级均相同）
            unqualifiedItemWarningStandardListPedigreeNotNull = unqualifiedItemWarningStandardListPedigreeNotNull.stream().sorted(Comparator.comparing(i -> i.getPedigree().getType())).collect(Collectors.toList());
            unqualifiedItemWarningStandardListSort.addAll(unqualifiedItemWarningStandardListPedigreeNotNull);
        }
        return org.apache.commons.collections.CollectionUtils.isEmpty(unqualifiedItemWarningStandardListSort) ? null : unqualifiedItemWarningStandardListSort.get(unqualifiedItemWarningStandardListSort.size() - Constants.INT_ONE);
    }

    /**
     * 保存批量下交待维修分析数据
     *
     * @param batchStepSaveRequestDTO   保存批量工序参数
     * @param rworkerStepProcessBaseDTO 工序生产过程通用基础数据
     * @param batchWorkDetail           批量工序生产详情
     */
    @Override
    public boolean saveBatchProcessMaintainInfo(RworkerBatchStepSaveRequestDTO batchStepSaveRequestDTO, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO, BatchWorkDetail batchWorkDetail) {
        if (!ValidateUtils.isValid(batchStepSaveRequestDTO.getUnqualifiedItemInfoList())) {
            return false;
        }
        //只要有不良的处理类型为维修分析则将工单整体开出，待分析状态
        List<UnqualifiedItem> unqualifiedItemList = unqualifiedItemRepository.findByIdInAndDeleted(batchStepSaveRequestDTO.getUnqualifiedItemInfoList().stream().map(UnqualifiedItemSaveInfo::getId).collect(Collectors.toList()), Constants.LONG_ZERO);
        UnqualifiedItem matainUnqualifiedItem = unqualifiedItemList.stream().filter(unqualifiedItem -> unqualifiedItem.getDealWay() == ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_MAINTAIN_ANALYSE.getCategoryName()).findFirst().orElse(null);
        if (null == matainUnqualifiedItem) {
            return false;
        }
        MaintainHistoryDTO maintainHistory = new MaintainHistoryDTO();
        maintainHistory.setNumber(batchStepSaveRequestDTO.getNumber())
                .setStatus(MaintainEnum.WAIT_ANALYZE_STATUS.getStatus())
                .setResult(MaintainEnum.MAINTAIN_RESULT_WAIT_DEAL.getStatus())
                .setStep(batchWorkDetail.getStep())
                .setSubWorkSheet(batchWorkDetail.getSubWorkSheet())
                .setWorkSheet(batchWorkDetail.getWorkSheet())
                .setUnqualifiedItem(matainUnqualifiedItem)
                .setStartDate(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
        rbaseMaintainHistoryProxy.saveInstance(maintainHistory);
        return Boolean.TRUE;
    }

    /**
     * 保存容器下交待维修分析数据,需要根据条件更新批量详情及工单相关数据
     *
     * @param bingContainerInfo 容器工序请求生产数据
     * @param containerDetail   容器生产详情
     */
    @Override
    public boolean saveContainerProcessMaintainInfo(RworkerContainerStepSaveRequestDTO.BingContainerInfo bingContainerInfo, ContainerDetail containerDetail) {
        if (!ValidateUtils.isValid(bingContainerInfo.getUnqualifiedItemInfoList())) {
            return Boolean.FALSE;
        }
        List<UnqualifiedItem> unqualifiedItemList = unqualifiedItemRepository.findByIdInAndDeleted(bingContainerInfo.getUnqualifiedItemInfoList().stream().map(UnqualifiedItemSaveInfo::getId).collect(Collectors.toList()), Constants.LONG_ZERO);
        UnqualifiedItem maintainUnqualifiedItem = unqualifiedItemList.stream().filter(unqualifiedItem -> unqualifiedItem.getDealWay() == ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_MAINTAIN_ANALYSE.getCategoryName()).findFirst().orElse(null);
        if (null == maintainUnqualifiedItem) {
            return Boolean.FALSE;
        }
        MaintainHistoryDTO maintainHistory = new MaintainHistoryDTO();
        maintainHistory.setNumber(bingContainerInfo.getNumber())
                .setStatus(MaintainEnum.WAIT_ANALYZE_STATUS.getStatus())
                .setResult(MaintainEnum.MAINTAIN_RESULT_WAIT_DEAL.getStatus())
                .setStep(containerDetail.getBatchWorkDetail().getStep())
                .setSubWorkSheet(containerDetail.getBatchWorkDetail().getSubWorkSheet())
                .setWorkSheet(containerDetail.getBatchWorkDetail().getWorkSheet())
                .setContainerDetail(containerDetail)
                .setUnqualifiedItem(maintainUnqualifiedItem)
                .setStartDate(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
        rbaseMaintainHistoryProxy.saveInstance(maintainHistory);
        return Boolean.TRUE;
    }


    /**
     * 保存SN下交待维修分析数据,需要根据条件更新批量详情及工单相关数据
     *
     * @param snWorkStatus sn生产状态
     */
    @Override
    public boolean saveSnProcessMaintainInfo(SnWorkStatus snWorkStatus) {
        UnqualifiedItem unqualifiedItem = snWorkStatus.getLatestUnqualifiedItem();
        if (unqualifiedItem.getDealWay() != ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_MAINTAIN_ANALYSE.getCategoryName()) {
            return Boolean.FALSE;
        }
        MaintainHistoryDTO maintainHistory = new MaintainHistoryDTO();
        maintainHistory.setNumber(Constants.INT_ONE)
                .setStatus(MaintainEnum.WAIT_ANALYZE_STATUS.getStatus())
                .setResult(MaintainEnum.MAINTAIN_RESULT_WAIT_DEAL.getStatus())
                .setStep(snWorkStatus.getLatestSnWorkDetail().getStep())
                .setSubWorkSheet(snWorkStatus.getSubWorkSheet())
                .setWorkSheet(snWorkStatus.getWorkSheet())
                .setContainerDetail(snWorkStatus.getLatestSnWorkDetail().getContainerDetail())
                .setUnqualifiedItem(snWorkStatus.getLatestUnqualifiedItem())
                .setSnWorkStatus(snWorkStatus)
                .setStartDate(LocalDateTime.now()).setDeleted(Constants.LONG_ZERO);
        rbaseMaintainHistoryProxy.saveInstance(maintainHistory);
        return Boolean.TRUE;
    }

    /**
     * 保存批量下交待复检数据
     *
     * @param batchStepSaveRequestDTO     保存批量工序参数
     * @param rworkerStepProcessBaseDTO   工序生产过程通用基础数据
     * @param batchWorkDetail             批量工序生产详情
     */
    @Override
    public boolean saveBatchProcessReinspectInfo(RworkerBatchStepSaveRequestDTO batchStepSaveRequestDTO, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO, BatchWorkDetail batchWorkDetail) {
        if (!ValidateUtils.isValid(batchStepSaveRequestDTO.getUnqualifiedItemInfoList())) {
            return Boolean.FALSE;
        }
        List<UnqualifiedItem> unqualifiedItemList = unqualifiedItemRepository.findByIdInAndDeleted(batchStepSaveRequestDTO.getUnqualifiedItemInfoList().stream().map(UnqualifiedItemSaveInfo::getId).collect(Collectors.toList()), Constants.LONG_ZERO);
        Map<Long, List<UnqualifiedItem>> unqualifiedItemGroup = unqualifiedItemList.stream().collect(Collectors.groupingBy(UnqualifiedItem::getId));
        List<StepReinspect> stepReinspectHistories = new ArrayList<>();
        batchStepSaveRequestDTO.getUnqualifiedItemInfoList().forEach(unqualifiedItemSaveInfo -> {
            StepReinspect stepReinspect = new StepReinspect();
            stepReinspect.setWorkSheet(rworkerStepProcessBaseDTO.getWorkSheet()).setSubWorkSheet(rworkerStepProcessBaseDTO.getSubWorkSheet())
                    .setStep(batchWorkDetail.getStep()).setWorkCell(batchWorkDetail.getWorkCell()).setSponsorId(batchWorkDetail.getOperatorId())
                    .setOriginUnqualifiedItem(unqualifiedItemGroup.get(unqualifiedItemSaveInfo.getId()).get(Constants.INT_ZERO)).setNumber(unqualifiedItemSaveInfo.getNumber())
                    .setSponsorDate(LocalDateTime.now()).setSerialNumber(rworkerStepProcessBaseDTO.getStepReinspectSerialNumber()).setLastStep(StringUtils.isBlank(rworkerStepProcessBaseDTO.getWsStep().getAfterStepId())).setDeleted(Constants.LONG_ZERO);
            stepReinspectHistories.add(stepReinspect);
        });
        stepReinspectRepository.saveAll(stepReinspectHistories);
        return Boolean.TRUE;
    }


    /**
     * 保存容器下交待复检数据,需要根据条件更新批量详情及工单相关数据
     *
     * @param bingContainerInfo           容器工序请求生产数据
     * @param containerDetail             容器生产详情
     */
    @Override
    public boolean saveContainerProcessReinspectInfo(RworkerContainerStepSaveRequestDTO.BingContainerInfo bingContainerInfo, RworkerStepProcessBaseDTO containerStepSaveBaseInfo,  ContainerDetail containerDetail) {
        if (!ValidateUtils.isValid(bingContainerInfo.getUnqualifiedItemInfoList())) {
            return Boolean.FALSE;
        }
        List<UnqualifiedItem> unqualifiedItemList = unqualifiedItemRepository.findByIdInAndDeleted(bingContainerInfo.getUnqualifiedItemInfoList().stream().map(UnqualifiedItemSaveInfo::getId).collect(Collectors.toList()), Constants.LONG_ZERO);
        Map<Long, List<UnqualifiedItem>> unqualifiedItemGroup = unqualifiedItemList.stream().collect(Collectors.groupingBy(UnqualifiedItem::getId));
        List<StepReinspect> stepReinspectHistories = new ArrayList<>();
        bingContainerInfo.getUnqualifiedItemInfoList().forEach(unqualifiedItemSaveInfo -> {
            StepReinspect stepReinspect = new StepReinspect();
            stepReinspect.setWorkSheet(containerStepSaveBaseInfo.getWorkSheet()).setSubWorkSheet(containerStepSaveBaseInfo.getSubWorkSheet()).setContainerDetail(containerDetail)
                    .setStep(containerDetail.getBatchWorkDetail().getStep()).setWorkCell(containerDetail.getWorkCell()).setSponsorId(containerDetail.getStaffId())
                    .setOriginUnqualifiedItem(unqualifiedItemGroup.get(unqualifiedItemSaveInfo.getId()).get(Constants.INT_ZERO)).setNumber(unqualifiedItemSaveInfo.getNumber())
                    .setSponsorDate(LocalDateTime.now()).setSerialNumber(containerStepSaveBaseInfo.getStepReinspectSerialNumber()).setLastStep(StringUtils.isBlank(containerStepSaveBaseInfo.getWsStep().getAfterStepId())).setDeleted(Constants.LONG_ZERO);
            stepReinspectHistories.add(stepReinspect);
        });
        stepReinspectRepository.saveAll(stepReinspectHistories);
        return Boolean.TRUE;
    }

    /**
     * 保存SN下交待复检数据,需要根据条件更新批量详情及工单相关数据
     *
     * @param snWorkStatus                sn生产状态
     */
    @Override
    public boolean saveSnProcessReinspectInfo(SnWorkStatus snWorkStatus, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        SnWorkDetail latestSnWorkDetail = snWorkStatus.getLatestSnWorkDetail();
        StepReinspect stepReinspect = new StepReinspect();
        stepReinspect.setWorkSheet(rworkerStepProcessBaseDTO.getWorkSheet()).setSubWorkSheet(rworkerStepProcessBaseDTO.getSubWorkSheet()).setSnWorkStatus(snWorkStatus)
                .setStep(latestSnWorkDetail.getStep()).setWorkCell(latestSnWorkDetail.getWorkCell()).setSponsorId(latestSnWorkDetail.getOperatorId()).setContainerDetail(latestSnWorkDetail.getContainerDetail())
                .setOriginUnqualifiedItem(snWorkStatus.getLatestUnqualifiedItem()).setNumber(Constants.INT_ONE)
                .setSponsorDate(LocalDateTime.now()).setSerialNumber(rworkerStepProcessBaseDTO.getStepReinspectSerialNumber()).setLastStep(StringUtils.isBlank(rworkerStepProcessBaseDTO.getWsStep().getAfterStepId())).setDeleted(Constants.LONG_ZERO);
        stepReinspectRepository.save(stepReinspect);
        return Boolean.TRUE;
    }

    /**
     * 保存工序生产过程中的预警信息
     *
     * @param batchWorkDetail                批量工序生产详情
     * @param batchStepSaveBaseInfo          工序生产过程通用基础数据
     * @param rworkerBatchStepSaveRequestDTO 工序请求保存参数
     */
    @Override
    public void saveStepProcessWaringInfo(BatchWorkDetail batchWorkDetail, RworkerStepProcessBaseDTO batchStepSaveBaseInfo, RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDTO) {
        if (!ValidateUtils.isValid(rworkerBatchStepSaveRequestDTO.getUnqualifiedItemInfoList())) {
            return;
        }
        //产品中(子)工单相同工序只预警一次
        List<UnqualifiedEvent> unqualifiedEventList = batchStepSaveBaseInfo.getSubWsProductionMode() ? unqualifiedEventRepository.findBySubWorkSheetIdAndStepIdAndDeleted(rworkerBatchStepSaveRequestDTO.getProductWorkSheetId(), batchStepSaveBaseInfo.getStep().getId(), Constants.LONG_ZERO) :
                unqualifiedEventRepository.findByWorkSheetIdAndStepIdAndDeleted(rworkerBatchStepSaveRequestDTO.getProductWorkSheetId(), batchStepSaveBaseInfo.getStep().getId(), Constants.LONG_ZERO);
        if (!CollectionUtils.isEmpty(unqualifiedEventList)) {
            return;
        }
        //保存工序不良项目预警事件
        boolean isWaringed = BeanUtil.getHighestPrecedenceBean(IQualityService.class).saveStepUnqualifiedItemWaringInfo(batchWorkDetail, batchStepSaveBaseInfo, rworkerBatchStepSaveRequestDTO);
        //保存工序良率预警事件
        if (!isWaringed) {
            BeanUtil.getHighestPrecedenceBean(IQualityService.class).saveStepPassRateWaringInfo(batchWorkDetail, batchStepSaveBaseInfo);
        }
    }

    /**
     * 保存工序良率预警信息
     *
     * @param batchWorkDetail       批量工序生产详情
     * @param batchStepSaveBaseInfo 工序生产过程通用基础数据
     */
    @Override
    public void saveStepPassRateWaringInfo(BatchWorkDetail batchWorkDetail, RworkerStepProcessBaseDTO batchStepSaveBaseInfo) {
        double qualifiedRate = NumberUtils.divide(batchWorkDetail.getQualifiedNumber(), batchWorkDetail.getFinishNumber(), Constants.INT_FOUR).doubleValue();
        StepWarningStandard stepWarningStandard = this.findStepWarningStandard(batchStepSaveBaseInfo);
        //良率预警标准不存在或者完成数小于基数时不做任何判断
        if (null == stepWarningStandard || batchWorkDetail.getFinishNumber() < stepWarningStandard.getBaseNumber()) {
            return;
        }
        //良率小于停线标准则停线
        if (qualifiedRate < stepWarningStandard.getStopRate()) {
            BeanUtil.getHighestPrecedenceBean(IQualityService.class).saveUnqualifiedEvent(ConstantsEnum.UNQUALIFIED_EVENT_STOP_TYPE.getCategoryName(), ConstantsEnum.UNQUALIFIED_EVENT_REASON_PASS_RATE_TYPE.getCategoryName(),
                    qualifiedRate, Constants.INT_ONE - qualifiedRate, batchWorkDetail.getUnqualifiedNumber(), batchStepSaveBaseInfo, null);
            return;
        }
        //良率小于预警标准则预警
        if (qualifiedRate < stepWarningStandard.getWaringRate()) {
            BeanUtil.getHighestPrecedenceBean(IQualityService.class).saveUnqualifiedEvent(ConstantsEnum.UNQUALIFIED_EVENT_WARNING_TYPE.getCategoryName(), ConstantsEnum.UNQUALIFIED_EVENT_REASON_PASS_RATE_TYPE.getCategoryName(),
                    qualifiedRate, Constants.INT_ONE - qualifiedRate, batchWorkDetail.getUnqualifiedNumber(), batchStepSaveBaseInfo, null);
        }
    }


    /**
     * 保存工序不良预警信息
     *
     * @param batchWorkDetail                批量工序生产详情
     * @param batchStepSaveBaseInfo          工序生产过程通用基础数据
     * @param rworkerBatchStepSaveRequestDTO 工序请求保存参数
     * @return boolean 是否预警
     */
    @Override
    public boolean saveStepUnqualifiedItemWaringInfo(BatchWorkDetail batchWorkDetail, RworkerStepProcessBaseDTO batchStepSaveBaseInfo, RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDTO) {
        for (UnqualifiedItemSaveInfo unqualifiedItemInfo : rworkerBatchStepSaveRequestDTO.getUnqualifiedItemInfoList()) {
            UnqualifiedItem unqualifiedItem = unqualifiedItemRepository.getReferenceById(unqualifiedItemInfo.getId());
            UnqualifiedItemWarningStandard unqualifiedItemWarningStandard = this.findUnqualifiedItemWaringStandard(batchStepSaveBaseInfo, unqualifiedItem);
            if (null == unqualifiedItemWarningStandard) {
                continue;
            }
            Long sumUnqualifiedItemNumber = batchStepSaveBaseInfo.getSubWsProductionMode() ? wsStepUnqualifiedItemRepository.sumNumberBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(batchStepSaveBaseInfo.getSubWorkSheet().getId(), batchStepSaveBaseInfo.getStep().getId(), unqualifiedItemInfo.getId(), Constants.LONG_ZERO)
                    : wsStepUnqualifiedItemRepository.sumNumberByWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(batchStepSaveBaseInfo.getWorkSheet().getId(), batchStepSaveBaseInfo.getStep().getId(), unqualifiedItemInfo.getId(), Constants.LONG_ZERO);
            if (Objects.isNull(sumUnqualifiedItemNumber) || Constants.LONG_ZERO == sumUnqualifiedItemNumber) {
                continue;
            }
            //计算不良率
            double occupancy = NumberUtils.divide(sumUnqualifiedItemNumber, batchWorkDetail.getFinishNumber(), Constants.INT_FOUR).doubleValue();
            double qualifiedRate = NumberUtils.divide(batchWorkDetail.getQualifiedNumber(), batchWorkDetail.getFinishNumber(), Constants.INT_FOUR).doubleValue();
            //投产数小于基数时则判断不良数量是否超过预警数量
            if (batchWorkDetail.getFinishNumber() < unqualifiedItemWarningStandard.getBaseNumber() && unqualifiedItemInfo.getNumber() > unqualifiedItemWarningStandard.getWaringNumber()) {
                BeanUtil.getHighestPrecedenceBean(IQualityService.class).saveUnqualifiedEvent(ConstantsEnum.UNQUALIFIED_EVENT_WARNING_TYPE.getCategoryName(), ConstantsEnum.UNQUALIFIED_EVENT_REASON_UNQUALIFIED_ITEM_OVER_TYPE.getCategoryName(),
                        qualifiedRate, occupancy, unqualifiedItemInfo.getNumber(), batchStepSaveBaseInfo, unqualifiedItem);
                return Boolean.TRUE;
            }
            //停线：投产数大于基数时则判断不良占比是否超过停线比例
            if (batchWorkDetail.getFinishNumber() >= unqualifiedItemWarningStandard.getBaseNumber() && occupancy > unqualifiedItemWarningStandard.getStopRate()) {
                BeanUtil.getHighestPrecedenceBean(IQualityService.class).saveUnqualifiedEvent(ConstantsEnum.UNQUALIFIED_EVENT_STOP_TYPE.getCategoryName(), ConstantsEnum.UNQUALIFIED_EVENT_REASON_UNQUALIFIED_ITEM_OVER_TYPE.getCategoryName(),
                        qualifiedRate, occupancy, unqualifiedItemInfo.getNumber(), batchStepSaveBaseInfo, unqualifiedItem);
                return Boolean.TRUE;
            }
            //停线：投产数大于基数时则判断不良占比是否超过预警比例
            if (batchWorkDetail.getFinishNumber() >= unqualifiedItemWarningStandard.getBaseNumber() && occupancy > unqualifiedItemWarningStandard.getWaringRate()) {
                BeanUtil.getHighestPrecedenceBean(IQualityService.class).saveUnqualifiedEvent(ConstantsEnum.UNQUALIFIED_EVENT_WARNING_TYPE.getCategoryName(), ConstantsEnum.UNQUALIFIED_EVENT_REASON_UNQUALIFIED_ITEM_OVER_TYPE.getCategoryName(),
                        qualifiedRate, occupancy, unqualifiedItemInfo.getNumber(), batchStepSaveBaseInfo, unqualifiedItem);
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }


    /**
     * 保存预警停线事件记录
     *
     * @param eventType             类型 0:预警;1:停线
     * @param reasonType            原因类型 0:不良超标;1:合格率不达标
     * @param qualifiedRate         工序合格率
     * @param unqualifiedRate       不良占用率
     * @param batchStepSaveBaseInfo 工序生产过程通用基础数据
     * @param unqualifiedItem       不良项目
     * @return net.airuima.domain.procedure.quality.UnqualifiedEvent 不合格事件
     **/
    @Override
    public UnqualifiedEvent saveUnqualifiedEvent(int eventType, int reasonType, double qualifiedRate, double unqualifiedRate, int unqualifiedNumber, RworkerStepProcessBaseDTO batchStepSaveBaseInfo, UnqualifiedItem unqualifiedItem) {
        SerialNumberDTO serialNumberDto = new SerialNumberDTO(eventType == Constants.INT_ZERO ? Constants.UNQUALIFIED_EVENT_WARING_SERIAL_NUMBER : Constants.UNQUALIFIED_EVENT_STOP_SERIAL_NUMBER, null, null);
        UnqualifiedEvent unqualifiedEvent = new UnqualifiedEvent();
        unqualifiedEvent.setOwnerId(batchStepSaveBaseInfo.getStaffDTO().getId())
                .setEventType(eventType)
                .setReasonType(reasonType)
                .setQualifiedRate(qualifiedRate)
                .setUnqualifiedRate(unqualifiedRate)
                .setStep(batchStepSaveBaseInfo.getStep())
                .setWorkCell(batchStepSaveBaseInfo.getWorkCell())
                .setUnqualifiedItem(unqualifiedItem)
                .setSubWorkSheet(batchStepSaveBaseInfo.getSubWorkSheet())
                .setWorkSheet(batchStepSaveBaseInfo.getWorkSheet())
                .setUnqualifiedNumber(unqualifiedNumber)
                .setStatus(Constants.INT_ZERO)
                .setRecordTime(LocalDateTime.now())
                .setSerialNumber(rbaseSerialNumberProxy.generate(serialNumberDto)).setDeleted(Constants.LONG_ZERO);
        unqualifiedEventRepository.save(unqualifiedEvent);
        boolean stopWorkLine = eventType == ConstantsEnum.UNQUALIFIED_EVENT_STOP_TYPE.getCategoryName() || null == batchStepSaveBaseInfo.getSubWorkSheet();
        //停线或者工单作为投产粒度时都是停工单
        if (stopWorkLine) {
            WorkSheet workSheet = batchStepSaveBaseInfo.getWorkSheet();
            workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName());
            workSheetRepository.save(workSheet);
        }
        //预警且子工单作为投产粒度时才将子工单暂停
        if (!stopWorkLine) {
            SubWorkSheet subWorkSheet = batchStepSaveBaseInfo.getSubWorkSheet();
            subWorkSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName());
            subWorkSheetRepository.save(subWorkSheet);
        }
        //根据事件配置来决定是否启动流程和消息发送
        boolean startProcess = eventServices[0].startProcess(UnqualifiedEvent.class.getSimpleName(), ImmutableBiMap.of(WARNING, !stopWorkLine), unqualifiedEvent.getSerialNumber(), StringUtils.EMPTY);
        if (startProcess) {
            unqualifiedEvent.setStatus(Constants.NEGATIVE_ONE);
            unqualifiedEventRepository.save(unqualifiedEvent);
        }
        return unqualifiedEvent;
    }

    /**
     * 通过投产工单  获取可检测的工序对应的工位列表
     *
     * @param serialNumber 投产工单编码
     * @return java.util.List<net.airuima.web.rest.rworker.quality.dto.RworkerInspectionWorkCellStepDTO> 获取待做工序及工位列表
     * <AUTHOR>
     * @date 2023/4/26
     */
    @Override
    public List<RworkerInspectionWorkCellStepDTO> inspectionWorkCellStep(String serialNumber) {
        //获取系统配置的投产粒度(子工单或者工单)
        boolean subWsProductionMode = commonService.subWsProductionMode();
        //工单、子工单
        SubWorkSheet subWorkSheet = subWsProductionMode ? subWorkSheetRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO).orElse(null) : null;
        WorkSheet workSheet = subWsProductionMode && !ObjectUtils.isEmpty(subWorkSheet) ? subWorkSheet.getWorkSheet() : workSheetRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO).orElse(null);

        if (ObjectUtils.isEmpty(workSheet) || (subWsProductionMode && ObjectUtils.isEmpty(subWorkSheet))) {
            throw new ResponseException(NOT_EXIST_PRODUCT_WORK_SHEET, "投产" + (subWsProductionMode ? "子" : "") + "工单" + serialNumber + "不存在");
        }
        if (Objects.nonNull(subWorkSheet) && (subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_STATEMENT.getCategoryName() || subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName())) {
            throw new ResponseException("error.subWorkSheet.finished", "子工单【" + serialNumber + "】已完成");
        }
        if (Objects.isNull(subWorkSheet) && (workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_STATEMENT.getCategoryName() || workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName())) {
            throw new ResponseException("error.workSheet.finished", "工单【" + serialNumber + "】已完成");
        }
        return subWsProductionMode ? BeanUtil.getHighestPrecedenceBean(IQualityService.class).inspectionWorkCellStepProductWorkSheet(subWorkSheet.getId(), subWorkSheet.getSerialNumber(), subWsProductionMode)
                : BeanUtil.getHighestPrecedenceBean(IQualityService.class).inspectionWorkCellStepProductWorkSheet(workSheet.getId(), workSheet.getSerialNumber(), subWsProductionMode);
    }


    /**
     * 通过 投产工单获取 对应 待检工序及工位信息列表
     *
     * @param productWorkSheetId 工单ID
     * @param serialNumber       工单序列号
     * @param isSubWorkSheet     是否是子工单
     * @return 待检工序及工位信息列表
     */
    @Override
    public List<RworkerInspectionWorkCellStepDTO> inspectionWorkCellStepProductWorkSheet(Long productWorkSheetId, String serialNumber, boolean isSubWorkSheet) {

        List<RworkerInspectionWorkCellStepDTO> rworkerInspectionWorkCellStepDtos = Lists.newArrayList();

        //获取当前工单待做的全部工序
        List<WsStep> wsSteps = isSubWorkSheet
                ? wsStepRepository.findBySubWorkSheetIdAndDeleted(productWorkSheetId, Constants.LONG_ZERO)
                : wsStepRepository.findByWorkSheetIdAndDeleted(productWorkSheetId, Constants.LONG_ZERO);

        if (!ValidateUtils.isValid(wsSteps)) {
            SubWorkSheet subWorkSheet = isSubWorkSheet ? subWorkSheetRepository.findByIdAndDeleted(productWorkSheetId, Constants.LONG_ZERO).orElse(null) : null;
            if (!ObjectUtils.isEmpty(subWorkSheet)) {
                wsSteps = wsStepRepository.findByWorkSheetIdAndDeleted(subWorkSheet.getWorkSheet().getId(), Constants.LONG_ZERO);
            }
        }
        if (!ValidateUtils.isValid(wsSteps)) {
            throw new ResponseException("error.productionSnapshotNotExist", "投产工单" + serialNumber + "工序快照不存在");
        }
        //获取已完成的批次详情
        List<BatchWorkDetail> batchWorkDetails = isSubWorkSheet
                ? batchWorkDetailRepository.findBySubWorkSheetIdAndFinishAndDeleted(productWorkSheetId, Constants.INT_ONE, Constants.LONG_ZERO)
                : batchWorkDetailRepository.findByWorkSheetIdAndFinishAndDeleted(productWorkSheetId, Constants.INT_ONE, Constants.LONG_ZERO);

        if (ValidateUtils.isValid(batchWorkDetails)) {
            wsSteps = wsSteps.stream().filter(wsStep -> batchWorkDetails.stream().noneMatch(batchWorkDetail -> batchWorkDetail.getStep().equals(wsStep.getStep()))).collect(Collectors.toList());
            BatchWorkDetail latestBatchWorkDetail = batchWorkDetails.stream().max(Comparator.comparing(BatchWorkDetail::getId)).get();
            RworkerInspectionWorkCellStepDTO rworkerInspectionWorkCellStepDto = new RworkerInspectionWorkCellStepDTO();
            rworkerInspectionWorkCellStepDto.setStepDto(new RworkerInspectionWorkCellStepDTO.StepDTO(latestBatchWorkDetail.getStep()));
            List<WorkCell> workCells = workCellStepRepository.findByStepIdAndDeletedAndIsEnable(latestBatchWorkDetail.getStep().getId(), Boolean.TRUE);
            Optional.ofNullable(workCells).ifPresent(workCellList -> {
                List<RworkerInspectionWorkCellStepDTO.WorkCellDTO> workCellDtos = workCellList.stream().map(RworkerInspectionWorkCellStepDTO.WorkCellDTO::new).collect(Collectors.toList());
                rworkerInspectionWorkCellStepDto.setWorkCellDtos(workCellDtos);
            });
            rworkerInspectionWorkCellStepDtos.add(rworkerInspectionWorkCellStepDto);
        }
        if (ValidateUtils.isValid(wsSteps)) {
            //处理每个待检工序，获取工位信息
            wsSteps.forEach(wsStep -> {
                RworkerInspectionWorkCellStepDTO rworkerInspectionWorkCellStepDto = new RworkerInspectionWorkCellStepDTO();
                rworkerInspectionWorkCellStepDto.setStepDto(new RworkerInspectionWorkCellStepDTO.StepDTO(wsStep.getStep()));
                List<WorkCell> workCells = workCellStepRepository.findByStepIdAndDeletedAndIsEnable(wsStep.getStep().getId(), Boolean.TRUE);
                Optional.ofNullable(workCells).ifPresent(workCellList -> {
                    List<RworkerInspectionWorkCellStepDTO.WorkCellDTO> workCellDtos = workCellList.stream().map(RworkerInspectionWorkCellStepDTO.WorkCellDTO::new).collect(Collectors.toList());
                    rworkerInspectionWorkCellStepDto.setWorkCellDtos(workCellDtos);
                });
                rworkerInspectionWorkCellStepDtos.add(rworkerInspectionWorkCellStepDto);
            });
        }

        return rworkerInspectionWorkCellStepDtos;
    }

    /**
     * 通过 投产工单编码+检测类型+工位+工序 + 容器 获取质检方案
     *
     * @param rworkerProductWorkSheetCategoryDto 获取质检方案参数
     * @return net.airuima.web.rest.rworker.quality.dto.RworkerQualityInspectionPlanDTO  质检方案
     * <AUTHOR>
     * @date 2023/4/26
     */
    @Override
    public RworkerQualityInspectionPlanDTO qualityInspectionPlan(RworkerProductWorkSheetCategoryDTO rworkerProductWorkSheetCategoryDto) {
        boolean subWsProductionMode = commonService.subWsProductionMode();
        SubWorkSheet subWorkSheet = subWsProductionMode ? subWorkSheetRepository.findBySerialNumberAndDeleted(rworkerProductWorkSheetCategoryDto.getSerialNumber(), Constants.LONG_ZERO).orElse(null) : null;
        WorkSheet ws = ObjectUtils.isEmpty(subWorkSheet) ? null : subWorkSheet.getWorkSheet();
        WorkSheet workSheet = subWsProductionMode ? ws : workSheetRepository.findBySerialNumberAndDeleted(rworkerProductWorkSheetCategoryDto.getSerialNumber(), Constants.LONG_ZERO).orElse(null);
        if (ObjectUtils.isEmpty(workSheet) || (subWsProductionMode && ObjectUtils.isEmpty(subWorkSheet))) {
            throw new ResponseException(NOT_EXIST_PRODUCT_WORK_SHEET, "投产" + (subWsProductionMode ? "子" : "") + "工单" + rworkerProductWorkSheetCategoryDto.getSerialNumber() + "不存在");
        }
        Step step = stepRepository.findByIdAndDeleted(rworkerProductWorkSheetCategoryDto.getStepId(), Constants.LONG_ZERO).orElse(null);
        if (ObjectUtils.isEmpty(step)) {
            throw new ResponseException("error.TodoInspectionStepIsNotExist", "当前待检测工序不存在");
        }
        WorkCell workCell = workCellRepository.findByIdAndDeleted(rworkerProductWorkSheetCategoryDto.getWorkCellId(), Constants.LONG_ZERO).orElse(null);
        if (rworkerProductWorkSheetCategoryDto.getCategory() <= Constants.INT_ONE && ObjectUtils.isEmpty(workCell)) {
            throw new ResponseException("error.TodoInspectionWorkCellIsNotExist", "当前待检测工位不存在");
        }
        //获取定制工序中工艺路线
        PedigreeStepCheckRule pedigreeStepCheckRule = commonService.findPedigreeStepCheckRule(workSheet, subWorkSheet, workSheet.getPedigree(), step, rworkerProductWorkSheetCategoryDto.getCategory(), rworkerProductWorkSheetCategoryDto.getWorkCellId(), rworkerProductWorkSheetCategoryDto.getVarietyId());
        if (ObjectUtils.isEmpty(pedigreeStepCheckRule)) {
            throw new ResponseException("error.TodoInspectionStepCheckRuleIsNotExist", "当前待检测工序未配置质检规则或质检规则已失效");
        }
        RworkerQualityInspectionPlanDTO rworkerQualityInspectionPlanDto = new RworkerQualityInspectionPlanDTO(pedigreeStepCheckRule);
        ContainerDetail containerDetail = null;
        int inputNumber = 0;
        //首检巡检  抽检终检，没有容器情况，计算报检数量
        if (rworkerProductWorkSheetCategoryDto.getCategory() <= Constants.INT_ONE || ObjectUtils.isEmpty(rworkerProductWorkSheetCategoryDto.getContainerCode())) {
            Integer unqualifiedNumber = subWsProductionMode ?
                    batchWorkDetailRepository.sumUnqualifiedNumberBySubWorkSheetId(subWorkSheet.getId()) :
                    batchWorkDetailRepository.sumUnqualifiedNumberByWorkSheetId(workSheet.getId());
            inputNumber = (subWsProductionMode ? subWorkSheet.getNumber() : workSheet.getNumber()) - (ObjectUtils.isEmpty(unqualifiedNumber) ? Constants.INT_ZERO : unqualifiedNumber);
        } else {
            Optional<ContainerDetail> containerDetailOptional = subWsProductionMode ?
                    containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(subWorkSheet.getId(), step.getId(), rworkerProductWorkSheetCategoryDto.getContainerCode(), Constants.LONG_ZERO) :
                    containerDetailRepository.findByBatchWorkDetailWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(workSheet.getId(), step.getId(), rworkerProductWorkSheetCategoryDto.getContainerCode(), Constants.LONG_ZERO);

            containerDetail = containerDetailOptional.orElseThrow(() -> new ResponseException("error.", "未获取到容器对应的生产信息"));
            inputNumber = containerDetail.getQualifiedNumber();
        }
        //计算报检数量-抽样数量
        Integer sampleNumber = querySamplingStrategyService.getSampleResult(pedigreeStepCheckRule.getSampleCase(), inputNumber);
        rworkerQualityInspectionPlanDto.setProductWorkSheetId(subWsProductionMode ? subWorkSheet.getId() : workSheet.getId())
                .setNumber(inputNumber)
                .getSampleCaseDto()
                .setNumber(Math.min(inputNumber, sampleNumber));
        //添加检测项目
        List<PedigreeStepCheckItem> pedigreeStepCheckItemList = pedigreeStepCheckItemRepository.findByPedigreeStepCheckRuleIdAndDeleted(pedigreeStepCheckRule.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(pedigreeStepCheckItemList)) {
            List<RworkerQualityInspectionPlanDTO.CheckItemDTO> checkItems = pedigreeStepCheckItemList.stream().map(pedigreeStepCheckItem -> new RworkerQualityInspectionPlanDTO.CheckItemDTO(pedigreeStepCheckItem, sampleNumber)).collect(Collectors.toList());
            checkItems = checkItems.stream().peek(checkItem -> {
                //添加检测项目关联的文件
                List<DocumentDTO> documentDTOList = rbaseDocumentProxy.getByRecordId(checkItem.getId());
                Optional.ofNullable(documentDTOList).ifPresent(checkItem::setDocumentDtos);
                //添加检测项目关联的缺陷原因
                List<DefectDTO> defectList = defectCheckItemRepository.findByCheckItemId(checkItem.getId());
                Optional.ofNullable(defectList).ifPresent(checkItem::setDefects);
            }).collect(Collectors.toList());
            rworkerQualityInspectionPlanDto.setCheckItems(checkItems);
        }
        //添加sn列表
        rworkerQualityInspectionPlanDto.setSnInfoList(getSnInfoList(subWsProductionMode, workSheet, subWorkSheet, containerDetail, step));
        //添加不良项目
        WorkFlow workFlow = commonService.findSnapshotWorkFlow(workSheet, subWorkSheet, step);
        List<UnqualifiedItem> unqualifiedItems = commonService.findPedigreeStepUnqualifiedItem(workSheet.getPedigree(), workFlow.getId(), step.getId(), workSheet.getClientId());
        if (ValidateUtils.isValid(unqualifiedItems)) {
            //批量管控 添加非维修分析处理方式不良项目  单支 添加非在线返修处理方式不良项目
//            List<RworkerQualityInspectionPlanDTO.UnqualifiedItemDTO> unqualifiedItemDtos = ObjectUtils.isEmpty(rworkerQualityInspectionPlanDto.getSnInfoList()) ?
//                    unqualifiedItems.stream().filter(unqualifiedItem -> unqualifiedItem.getDealWay() != ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_MAINTAIN_ANALYSE.getCategoryName())
//                            .map(RworkerQualityInspectionPlanDTO.UnqualifiedItemDTO::new).collect(Collectors.toList()) :
//                    unqualifiedItems.stream().filter(unqualifiedItem -> unqualifiedItem.getDealWay() != ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_ONLINE_REWORK.getCategoryName())
//                            .map(RworkerQualityInspectionPlanDTO.UnqualifiedItemDTO::new).collect(Collectors.toList());
            List<RworkerQualityInspectionPlanDTO.UnqualifiedItemDTO> unqualifiedItemDtos = unqualifiedItems.stream().map(RworkerQualityInspectionPlanDTO.UnqualifiedItemDTO::new).collect(Collectors.toList());
            rworkerQualityInspectionPlanDto.setUnqualifiedItems(unqualifiedItemDtos);
        }
        //获取可能的质检缓存
        if(Objects.nonNull(rworkerProductWorkSheetCategoryDto.getTaskId())){
            inspectTaskRepository.findByIdAndDeleted(rworkerProductWorkSheetCategoryDto.getTaskId(),Constants.LONG_ZERO).ifPresent(inspectTask -> {
                rworkerQualityInspectionPlanDto.setCache(StringUtils.isNotBlank(inspectTask.getCache()) ? inspectTask.getCache() : null);
            });
        } else if (rworkerProductWorkSheetCategoryDto.getCategory() <= 1 ) {
            //查看是否存在当前工位+检查类型+项目类型+未处理记录 -》存在则调整为已处理
            Optional<InspectTask> inspectTaskOptional = inspectTaskRepository.findByWorkCellIdAndCategoryAndVarietyIdAndStatusAndDeleted(rworkerProductWorkSheetCategoryDto.getWorkCellId(), rworkerProductWorkSheetCategoryDto.getCategory(),
                    ObjectUtils.isEmpty(rworkerProductWorkSheetCategoryDto.getVarietyId()) ? null : rworkerProductWorkSheetCategoryDto.getVarietyId(), Boolean.FALSE, Constants.LONG_ZERO);
            inspectTaskOptional.ifPresent(inspectTask ->
                    rworkerQualityInspectionPlanDto.setCache(inspectTask.getCache())
            );
        }
        return rworkerQualityInspectionPlanDto;
    }

    /**
     * 获取当前容器 工单 待检sn列表
     *
     * @param subWsProductionMode 投产粒度
     * @param workSheet           工单
     * @param subWorkSheet        子工单
     * @param containerDetail     容器详情
     * @param step                工序
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @Date 2023/5/16
     */
    public List<String> getSnInfoList(Boolean subWsProductionMode, WorkSheet workSheet, SubWorkSheet subWorkSheet, ContainerDetail containerDetail, Step step) {
        List<SnWorkDetail> snWorkDetails = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(containerDetail)) {
            snWorkDetails = snWorkDetailRepository.findByContainerDetailIdAndDeleted(containerDetail.getId(), Constants.LONG_ZERO);
        }
        if (!ValidateUtils.isValid(snWorkDetails)) {
            snWorkDetails = subWsProductionMode ?
                    snWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), step.getId(), Constants.LONG_ZERO) :
                    snWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), step.getId(), Constants.LONG_ZERO);
        }
        if (ValidateUtils.isValid(snWorkDetails)) {
            return snWorkDetails.stream().filter(snWorkDetail -> snWorkDetail.getResult() == Constants.INT_ONE).map(SnWorkDetail::getSn).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }


    /**
     * 保存首检巡检 抽检终检检测记录
     *
     * @param rworkerInspectionResultDto 保存首检巡检抽检终检检测结果
     * @return void
     * <AUTHOR>
     * @date 2023/4/27
     */
    @Override
    public void saveInspectionRecord(RworkerInspectionResultDTO rworkerInspectionResultDto) {
        //获取投产粒度
        boolean subWsProductionMode = commonService.subWsProductionMode();
        SubWorkSheet subWorkSheet = subWsProductionMode ? subWorkSheetRepository.findByIdAndDeleted(rworkerInspectionResultDto.getProductWorkSheetId(), Constants.LONG_ZERO).orElse(null) : null;
        WorkSheet ws = ObjectUtils.isEmpty(subWorkSheet) ? null : subWorkSheet.getWorkSheet();
        WorkSheet workSheet = subWsProductionMode ? ws : workSheetRepository.findByIdAndDeleted(rworkerInspectionResultDto.getProductWorkSheetId(), Constants.LONG_ZERO).orElse(null);
        if (ObjectUtils.isEmpty(workSheet) || (subWsProductionMode && ObjectUtils.isEmpty(subWorkSheet))) {
            throw new ResponseException(NOT_EXIST_PRODUCT_WORK_SHEET, "投产" + (subWsProductionMode ? "子" : "") + "工单" + "不存在");
        }
        if (rworkerInspectionResultDto.getSnCheckItemDtoList().stream().anyMatch(snCheckItemDto -> StringUtils.isBlank(snCheckItemDto.getSn()))) {
            throw new ResponseException("error.checkSnExistEmpty", "检测SN不可为空");
        }
        //工位
        WorkCell workCell = workCellRepository.findByIdAndDeleted(rworkerInspectionResultDto.getWorkCellId(), Constants.LONG_ZERO).orElse(null);
        if (rworkerInspectionResultDto.getCategory() <= Constants.INT_ONE && ObjectUtils.isEmpty(workCell)) {
            throw new ResponseException("error.TodoInspectionWorkCellIsNotExist", "当前待检测工位不存在");
        }
        //工序
        Step step = stepRepository.findByIdAndDeleted(rworkerInspectionResultDto.getStepId(), Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("error.stepIsNotExist", "待检测工序不存在"));
        //检测方案
        PedigreeStepCheckRule pedigreeStepCheckRule = pedigreeStepCheckRuleRepository.findByIdAndDeleted(rworkerInspectionResultDto.getCheckRuleId(), Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("error.checkRuleIsNotExist", "检测方案不存在"));
        //项目类型
        VarietyDTO variety = varietyRepository.findByIdAndDeleted(rworkerInspectionResultDto.getVarietyId(),
                Constants.LONG_ZERO).orElse(null);

        // 保存检查历史记录
        CheckHistory checkHistory = BeanUtil.getHighestPrecedenceBean(IQualityService.class).saveCheckHistoryRecord(rworkerInspectionResultDto, step, workCell, pedigreeStepCheckRule, variety, subWorkSheet, workSheet, subWsProductionMode);

        // 保存检测详情数据
        List<CheckHistoryDetail> checkHistoryDetails = saveCheckHistoryDetailsAndSnapshots(rworkerInspectionResultDto, checkHistory);

        // 更新检测任务
        updateInspectTask(checkHistory, subWsProductionMode);

        //抽检-终检-合格通过直接放行
        if (checkHistory.getCategory() > WorkCellStartCheckEnum.IPQC_INSPECTION.getCategory() && checkHistory.getResult()) {
            checkHistoryService.addReleasedInspectUnqualified(checkHistoryDetails);
        }

        // 首检巡检抽检终检检测记录保存测试数据上传至qms
        BeanUtil.getHighestPrecedenceBean(IQualityService.class).uploadInspectionTestData(checkHistory, pedigreeStepCheckRule, rworkerInspectionResultDto.getSnCheckItemDtoList());

    }

    /**
     * 保存检查历史记录
     *
     * @param rworkerInspectionResultDto 下交保存参数
     * @param step                       工序
     * @param workCell                   工位
     * @param pedigreeStepCheckRule      检测规则
     * @param variety                    项目类型
     * @param subWorkSheet               子工单
     * @param workSheet                  工单
     * @param subWsProductionMode        投产粒度
     * @return net.airuima.domain.procedure.quality.CheckHistory
     * <AUTHOR>
     * @Date 2023/5/6
     */
    @Override
    public CheckHistory saveCheckHistoryRecord(RworkerInspectionResultDTO rworkerInspectionResultDto, Step step,
                                               WorkCell workCell, PedigreeStepCheckRule pedigreeStepCheckRule,
                                               VarietyDTO variety, SubWorkSheet subWorkSheet, WorkSheet workSheet,
                                               boolean subWsProductionMode) {
        CheckHistory checkHistory = new CheckHistory();
        SerialNumberDTO serialNumberDTO = new SerialNumberDTO();
        if (rworkerInspectionResultDto.getCategory() == WorkCellStartCheckEnum.FIRST_INSPECTION.getCategory()) {
            serialNumberDTO.setCode(Constants.KEY_FAI_SERIAL_NUMBER);
        } else if (rworkerInspectionResultDto.getCategory() == WorkCellStartCheckEnum.IPQC_INSPECTION.getCategory()) {
            serialNumberDTO.setCode(Constants.KEY_IPQC_SERIAL_NUMBER);
        } else if (rworkerInspectionResultDto.getCategory() == WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory()) {
            serialNumberDTO.setCode(Constants.KEY_PQC_SERIAL_NUMBER);
        } else if (rworkerInspectionResultDto.getCategory() == WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()) {
            serialNumberDTO.setCode(Constants.KEY_FQC_SERIAL_NUMBER);
        } else {
            serialNumberDTO.setCode(Constants.KEY_LQC_SERIAL_NUMBER);
        }
        checkHistory.setCategory(rworkerInspectionResultDto.getCategory())
                .setSerialNumber(rbaseSerialNumberProxy.generate(serialNumberDTO))
                .setNumber(rworkerInspectionResultDto.getCheckNumber())
                .setOperatorId(rworkerInspectionResultDto.getOperatorId())
                .setQualifiedNumber(rworkerInspectionResultDto.getQualifiedNumber())
                .setVirtual(Boolean.TRUE)
                .setUnqualifiedNumber(rworkerInspectionResultDto.getUnqualifiedNumber())
                .setStep(step).setWorkCell(workCell).setCheckRule(pedigreeStepCheckRule).setVarietyId(Optional.ofNullable(variety).map(VarietyDTO::getId).orElse(null))
                .setSubWorkSheet(subWsProductionMode ? subWorkSheet : null).setWorkSheet(!subWsProductionMode ? workSheet : null)
                .setResult(rworkerInspectionResultDto.getResult()).setInspectNumber(rworkerInspectionResultDto.getInspectNumber())
                .setRecordDate(LocalDateTime.now()).setContainerCode(rworkerInspectionResultDto.getContainerCode());
        for (RworkerInspectionResultDTO.SnCheckItemDTO snCheckItemDto : rworkerInspectionResultDto.getSnCheckItemDtoList()) {
            if (snCheckItemDto.getCheckItemResultDtoList().stream().anyMatch(checkItemResultDTO -> !checkItemResultDTO.getVirtual())) {
                checkHistory.setVirtual(Boolean.FALSE);
                break;
            }
        }
        //首检巡检
        if (checkHistory.getCategory() <= 1) {
            //检测合格-》 通过 反之重检
            checkHistory.setDealWay(rworkerInspectionResultDto.getResult() ? 1 : 2);
            //生产待检任务
            if (!rworkerInspectionResultDto.getResult()) {
                InspectionTaskDTO inspectionTaskDTO = Objects.nonNull(subWorkSheet) ? new InspectionTaskDTO(subWorkSheet, checkHistory.getVarietyObj(), checkHistory.getCategory(), workCell) : new InspectionTaskDTO(workSheet, checkHistory.getVarietyObj(), checkHistory.getCategory(), workCell);
                BeanUtil.getBean(InspectionServiceImpl.class).addInspectTask(inspectionTaskDTO);
            }
        } else {
            //检测合格-》 通过 反之待处理
            checkHistory.setDealWay(rworkerInspectionResultDto.getResult() ? 1 : 0);
        }
        checkHistory.setStatus(rworkerInspectionResultDto.getResult() ? Constants.TRUE : Constants.FALSE);
        return checkHistoryRepository.save(checkHistory);
    }

    /**
     * 保存检测详情数据和检测项目快照
     *
     * @param rworkerInspectionResultDto 下交保存参数
     * @param checkHistory               检测历史
     * @return void
     * <AUTHOR>
     * @Date 2023/5/6
     */
    public List<CheckHistoryDetail> saveCheckHistoryDetailsAndSnapshots(RworkerInspectionResultDTO rworkerInspectionResultDto, CheckHistory checkHistory) {
        List<CheckHistoryDetail> checkHistoryDetails = Lists.newArrayList();
        List<CheckHistoryItemSnapshot> checkHistoryItemSnapshots = Lists.newArrayList();
        Map<Long, RworkerInspectionResultDTO.CheckItemResultDTO> checkItemResultGroup = new HashMap<>();
        //保存检测详情数据
        Optional.ofNullable(rworkerInspectionResultDto.getSnCheckItemDtoList()).ifPresent(snCheckItemList -> {
            snCheckItemList.forEach(snCheckItemDto -> {
                UnqualifiedItem unqualifiedItem = ObjectUtils.isEmpty(snCheckItemDto.getUnqualifiedItemId()) ?
                        null : unqualifiedItemRepository.findByIdAndDeleted(snCheckItemDto.getUnqualifiedItemId(), Constants.LONG_ZERO).orElse(null);
                snCheckItemDto.getCheckItemResultDtoList().forEach(checkItemResultDto -> {
                    //检测项目详情
                    CheckHistoryDetail checkHistoryDetail = new CheckHistoryDetail();
                    checkHistoryDetail.setSn(snCheckItemDto.getSn())
                            .setCheckHistory(checkHistory)
                            .setCheckItemId(checkItemResultDto.getId())
                            .setCheckData(checkItemResultDto.getCheckData())
                            .setQualifiedRange(checkItemResultDto.getQualifiedRange())
                            .setResult(checkItemResultDto.getResult()).setVirtual(checkItemResultDto.getVirtual())
                            .setUnqualifiedItem(unqualifiedItem)
                            .setDefect(ObjectUtils.isEmpty(checkItemResultDto.getDefectId()) ? null :
                                    defectRepository.findByIdAndDeleted(checkItemResultDto.getDefectId(),
                                            Constants.LONG_ZERO).orElse(null))
                            .setDeleted(Constants.LONG_ZERO);
                    checkHistoryDetail = checkHistoryDetailRepository.save(checkHistoryDetail);
                    // 绑定数据
                    if (!CollectionUtils.isEmpty(checkItemResultDto.getDocumentList())) {
                        DocumentRelationDTO documentRelationDTO = new DocumentRelationDTO();
                        documentRelationDTO.setDocumentList(Optional.ofNullable(checkItemResultDto.getDocumentList()).orElse(Collections.emptyList()));
                        documentRelationDTO.setServiceName("mom").setRecordId(checkHistoryDetail.getId());
                        rbaseDocumentProxy.relation(documentRelationDTO);
                    }
                    if (!checkItemResultGroup.containsKey(checkItemResultDto.getId())) {
                        checkItemResultGroup.put(checkItemResultDto.getId(), checkItemResultDto);
                    }
                    checkHistoryDetails.add(checkHistoryDetail);
                });
            });
            //检测项目条件
            checkItemResultGroup.forEach((checkItemId, checkItemResultDto) -> {
                CheckHistoryItemSnapshot checkHistoryItemSnapshot = new CheckHistoryItemSnapshot();
                checkHistoryItemSnapshot.setCheckHistory(checkHistory).setCheckItemId(checkItemResultDto.getId()).setQualifiedRange(checkItemResultDto.getQualifiedRange())
                        .setVariety(ObjectUtils.isEmpty(checkItemResultDto.getVarietyId()) ? null :
                                varietyRepository.findByIdAndDeleted(checkItemResultDto.getVarietyId(),
                                        Constants.LONG_ZERO).orElse(null))
                        .setCheckWay(checkItemResultDto.getInspectWay())
                        .setAnalyseWay(checkItemResultDto.getAnalyseWay()).setFacility(checkItemResultDto.getFacility())
                        .setDeleted(Constants.LONG_ZERO);
                checkHistoryItemSnapshots.add(checkHistoryItemSnapshot);
            });
            checkHistoryItemSnapshotRepository.saveAll(checkHistoryItemSnapshots);
        });
        return checkHistoryDetails;
    }

    /**
     * 更新检测任务
     *
     * @param checkHistory        检测历史
     * @param subWsProductionMode 投产粒度
     * @return void
     * <AUTHOR>
     * @Date 2023/5/6
     */
    public void updateInspectTask(CheckHistory checkHistory, Boolean subWsProductionMode) {
        //首检，巡检
        if (checkHistory.getCategory() <= 1 && checkHistory.getResult()) {
            //更新最新检测记录
            BeanUtil.getHighestPrecedenceBean(IQualityService.class).updateLatestCheckResult(checkHistory, checkHistory.getCategory());
            //查看是否存在当前工位+检查类型+项目类型+未处理记录 -》存在则调整为已处理
            Optional<InspectTask> inspectTaskOptional = inspectTaskRepository.findByWorkCellIdAndCategoryAndVarietyIdAndStatusAndDeleted(checkHistory.getWorkCell().getId(), checkHistory.getCategory(),
                    ObjectUtils.isEmpty(checkHistory.getVarietyObj()) ? null : checkHistory.getVarietyObj().getId(), Boolean.FALSE, Constants.LONG_ZERO);
            inspectTaskOptional.ifPresent(inspectTask ->
                    inspectTaskRepository.save(inspectTask.setStatus(Boolean.TRUE))
            );
        } else if (checkHistory.getCategory() > Constants.INT_ONE) {
            Long varietyId = ObjectUtils.isEmpty(checkHistory.getVarietyObj()) ? null : checkHistory.getVarietyObj().getId();
            // 抽检 终检
            Optional<InspectTask> inspectTaskOptional = subWsProductionMode ?
                    inspectTaskRepository.findBySubWorkSheetIdAndStepIdAndVarietyIdAndStatusAndCategoryAndContainerCodeAndDeleted(checkHistory.getSubWorkSheet().getId(), checkHistory.getStep().getId(), varietyId,
                            Boolean.FALSE, checkHistory.getCategory(), checkHistory.getContainerCode(), Constants.LONG_ZERO) :
                    inspectTaskRepository.findByWorkSheetIdAndStepIdAndVarietyIdAndStatusAndCategoryAndContainerCodeAndDeleted(checkHistory.getWorkSheet().getId(), checkHistory.getStep().getId(), varietyId,
                            Boolean.FALSE, checkHistory.getCategory(), checkHistory.getContainerCode(), Constants.LONG_ZERO);
            inspectTaskOptional.ifPresent(inspectTask ->
                    inspectTaskRepository.save(inspectTask.setStatus(Boolean.TRUE))
            );
        }
    }


    /**
     * 更新最新检测记录 - 重置处理状态
     *
     * @param checkHistory 检测记录
     * @return void
     * <AUTHOR>
     * @date 2023/4/27
     */
    @Override
    public LatestCheckResult updateLatestCheckResult(CheckHistory checkHistory, Integer category) {
        LatestCheckResult latestCheckResult = latestCheckResultRepository.findByWorkCellIdAndCategoryAndVarietyIdAndDeleted(checkHistory.getWorkCell().getId(), category, ObjectUtils.isEmpty(checkHistory.getVarietyObj()) ? null : checkHistory.getVarietyObj().getId(), Constants.LONG_ZERO).orElseGet(LatestCheckResult::new);
        latestCheckResult.setCategory(category)
                .setSerialNumber(checkHistory.getSerialNumber())
                .setRecordDate(LocalDateTime.now())
                .setResult(checkHistory.getResult())
                .setWorkCell(checkHistory.getWorkCell())
                .setSubWorkSheet(checkHistory.getSubWorkSheet())
                .setDisplay(Boolean.TRUE)
                .setWorkSheet(checkHistory.getWorkSheet())
                .setVarietyId(checkHistory.getVarietyId())
                .setDealWay(checkHistory.getDealWay())
                .setNextCheckDate(null)
                .setExtendTime(Constants.INT_ZERO)
                .setStatus(checkHistory.getResult() ? Boolean.TRUE : Boolean.FALSE)
                .setDeleted(Constants.LONG_ZERO);

        //首检巡检 不能进行 界面二次操作
        if (checkHistory.getCategory() <= Constants.INT_ONE) {
            latestCheckResult.setStatus(Boolean.TRUE);
            //检查有无配置周期性配置，若有则更新下次检测时间
            Optional<WorkCellCheckStartRule> workCellCheckStartRuleOptional = workCellCheckStartRuleRepository.findByWorkCellIdAndCategoryAndFlagAndVarietyIdAndIsEnableAndDeleted(latestCheckResult.getWorkCell().getId(), latestCheckResult.getCategory(), net.airuima.constant.Constants.INT_THREE, ObjectUtils.isEmpty(latestCheckResult.getVarietyObj()) ? null : latestCheckResult.getVarietyObj().getId(), Boolean.TRUE, net.airuima.constant.Constants.LONG_ZERO);
            workCellCheckStartRuleOptional.ifPresent(workCellCheckStartRule -> {
                if (workCellCheckStartRule.getExtendType() == Constants.INT_ONE){
                    latestCheckResult.setNextCheckDate(LocalDateTime.now().plusMinutes((long) ((workCellCheckStartRule.getDuration()) * 60)));
                }else {
                    latestCheckResult.setNextCheckDate(LocalDateTime.now().plusMinutes((long) ((workCellCheckStartRule.getDuration()+workCellCheckStartRule.getExtendTime()) * 60)))
                            .setExtendTime(workCellCheckStartRule.getExtendTime());
                }
                latestCheckResultRepository.save(latestCheckResult);
            });
            //当首检合格时巡检按照周期往后顺延
            if (category == WorkCellStartCheckEnum.FIRST_INSPECTION.getCategory() && checkHistory.getResult()) {
                Optional<WorkCellCheckStartRule> workCellCheckStartRuleOpt = workCellCheckStartRuleRepository.findByWorkCellIdAndCategoryAndFlagAndVarietyIdAndIsEnableAndDeleted(latestCheckResult.getWorkCell().getId(), net.airuima.constant.Constants.INT_ONE, net.airuima.constant.Constants.INT_THREE, ObjectUtils.isEmpty(latestCheckResult.getVarietyObj()) ? null : latestCheckResult.getVarietyObj().getId(), Boolean.TRUE, net.airuima.constant.Constants.LONG_ZERO);
                workCellCheckStartRuleOpt.ifPresent(workCellCheckStartRule ->
                        BeanUtil.getHighestPrecedenceBean(IQualityService.class).updateLatestCheckResult(checkHistory, net.airuima.constant.Constants.INT_ONE)
                );
            }
        }
        return latestCheckResult;
    }

    /**
     * 验证工位，工单 是否 需要进行首检
     *
     * @param rworkerCheckProcessInspectionDtoList 请求验证首检巡检参数列表
     * @return net.airuima.dto.base.BaseDTO 基础响应信息
     * @return void
     * <AUTHOR>
     * @date 2023/4/27
     */
    @Override
    public BaseDTO checkProcessInspection(List<RworkerCheckProcessInspectionDTO> rworkerCheckProcessInspectionDtoList) {
        //获取系统配置的投产粒度(子工单或者工单)
        boolean subWsProductionMode = commonService.subWsProductionMode();

        for (RworkerCheckProcessInspectionDTO checkProcessInspectionDto : rworkerCheckProcessInspectionDtoList) {

            SubWorkSheet subWorkSheet = subWsProductionMode ? subWorkSheetRepository.findBySerialNumberAndDeleted(checkProcessInspectionDto.getSerialNumber(), Constants.LONG_ZERO).orElse(null) : null;
            WorkSheet ws = ObjectUtils.isEmpty(subWorkSheet) ? null : subWorkSheet.getWorkSheet();
            WorkSheet workSheet = subWsProductionMode ? ws : workSheetRepository.findBySerialNumberAndDeleted(checkProcessInspectionDto.getSerialNumber(), Constants.LONG_ZERO).orElse(null);

            if (ObjectUtils.isEmpty(workSheet) || (subWsProductionMode && ObjectUtils.isEmpty(subWorkSheet))) {
                throw new ResponseException(NOT_EXIST_PRODUCT_WORK_SHEET, "投产" + (subWsProductionMode ? "子" : "") + "工单" + "不存在");
            }
            WorkCell workCell = workCellRepository.findByIdAndDeleted(checkProcessInspectionDto.getWorkCellId(), Constants.LONG_ZERO).orElseThrow(
                    () -> new ResponseException("error.notExistWorkCell", "当前生产工位不存在"));
            Long productWorkSheetId = subWsProductionMode ? subWorkSheet.getId() : workSheet.getId();
            boolean firstTimeWork = rworkerCheckProcessInspectionDtoList.get(Constants.INT_ZERO).getFirstTimeWork();
            //验证是否需要进行首检
            BaseDTO baseDto = inspectionServices[0].faiInspectionInfo(firstTimeWork, productWorkSheetId, workCell);
            if (Constants.KO.equals(baseDto.getStatus())) {
                return baseDto.setMessage("首检：" + baseDto.getMessage());
            }
            //验证是否需要进行巡检
            baseDto = inspectionServices[0].ipqcInspectionInfo(firstTimeWork, productWorkSheetId, workCell);
            if (Constants.KO.equals(baseDto.getStatus())) {
                return baseDto.setMessage("巡检：" + baseDto.getMessage());
            }
        }
        return new BaseDTO(Constants.OK);
    }

    /**
     * 批量模式 创建待检任务
     *
     * @param subWsProductionMode 投产力度
     * @param subWorkSheet        子工单
     * @param workSheet           工单
     * @param step                工序
     * <AUTHOR>
     * @date 2023/5/9
     */
    @Override
    public boolean createBatchInspectTask(Boolean subWsProductionMode, SubWorkSheet subWorkSheet, WorkSheet workSheet, Step step) {
        //获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = commonService.findSnapshotWorkFlow(workSheet, subWorkSheet, step);
        List<WorkCellCheckStartRule> workCellCheckStartRules = workCellCheckStartRuleRepository.findByWorkFlowIdAndStepIdAndIsEnableAndDeleted(snapshotWorkFlow.getId(), step.getId(), Boolean.TRUE, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(workCellCheckStartRules)) {
            return Boolean.FALSE;
        }
        List<WorkCellCheckStartRule> workCellCheckStartRuleList = Lists.newArrayList();
        //抽检
        if (FuncKeyUtil.checkApi(FuncKeyConstants.PQC)) {
            workCellCheckStartRuleList.addAll(workCellCheckStartRules.stream().filter(workCellCheckStartRule -> workCellCheckStartRule.getCategory() == Constants.INT_FOUR).toList());
        }
        //终检
        if (FuncKeyUtil.checkApi(FuncKeyConstants.FQC)) {
            workCellCheckStartRuleList.addAll(workCellCheckStartRules.stream().filter(workCellCheckStartRule -> workCellCheckStartRule.getCategory() == Constants.INT_THREE).toList());
        }
        //末检
        if (FuncKeyUtil.checkApi(FuncKeyConstants.LQC)) {
            workCellCheckStartRuleList.addAll(workCellCheckStartRules.stream().filter(workCellCheckStartRule -> workCellCheckStartRule.getCategory() == Constants.INT_TWO).toList());
        }
        if (!ValidateUtils.isValid(workCellCheckStartRuleList)) {
            //未获取到当前检测规则，查看当前工单是否还存在待检的抽检，终检，末检如果存在则 返回true
           Long todoTaskCount =  subWsProductionMode ?
                    inspectTaskRepository.countBySubWorkSheetIdAndStatusAndCategoryInAndDeleted(subWorkSheet.getId(), Boolean.FALSE,Arrays.asList(WorkCellStartCheckEnum.LQC_INSPECTION.getCategory(),
                            WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory(),
                            WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()) ,Constants.LONG_ZERO):
                    inspectTaskRepository.countByWorkSheetIdAndStatusAndCategoryInAndDeleted(workSheet.getId(), Boolean.FALSE,Arrays.asList(WorkCellStartCheckEnum.LQC_INSPECTION.getCategory(),WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory(),
                            WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()),Constants.LONG_ZERO);
            return Objects.nonNull(todoTaskCount) && (todoTaskCount > Constants.INT_ZERO)? Boolean.TRUE:Boolean.FALSE;
        }
        workCellCheckStartRuleList.forEach(workCellCheckStartRule -> {
            Long varietyObjId = Objects.nonNull(workCellCheckStartRule.getVarietyObj())?workCellCheckStartRule.getVarietyObj().getId():null;
            InspectTask inspectTask = subWsProductionMode ?
            inspectTaskRepository.findBySubWorkSheetIdAndStepIdAndVarietyIdAndStatusAndCategoryAndContainerCodeAndDeleted(subWorkSheet.getId(),step.getId(),varietyObjId,Boolean.FALSE,workCellCheckStartRule.getCategory(),null,Constants.LONG_ZERO).orElse(new InspectTask()):
            inspectTaskRepository.findByWorkSheetIdAndStepIdAndVarietyIdAndStatusAndCategoryAndContainerCodeAndDeleted(workSheet.getId(),step.getId(),varietyObjId,Boolean.FALSE,workCellCheckStartRule.getCategory(),null,Constants.LONG_ZERO).orElse(new InspectTask());
            inspectTask.setWorkSheet(workSheet).setSubWorkSheet(subWorkSheet).setStep(step)
                    .setCategory(workCellCheckStartRule.getCategory()).setStatus(Boolean.FALSE)
                    .setVarietyId(workCellCheckStartRule.getVarietyId());
            inspectTaskRepository.save(inspectTask);
        });
        return Boolean.TRUE;
    }

    /**
     * 容器模式 创建待检任务
     *
     * @param containerDetail 容器生产详情
     * <AUTHOR>
     * @date 2023/5/9
     */
    @Override
    public boolean createContainerInspectTask(ContainerDetail containerDetail) {
        SubWorkSheet subWorkSheet = containerDetail.getBatchWorkDetail().getSubWorkSheet();
        WorkSheet workSheet = containerDetail.getBatchWorkDetail().getWorkSheet();
        //获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = commonService.findSnapshotWorkFlow(workSheet, subWorkSheet, containerDetail.getBatchWorkDetail().getStep());
        List<WorkCellCheckStartRule> workCellCheckStartRules = workCellCheckStartRuleRepository.findByWorkFlowIdAndStepIdAndIsEnableAndDeleted(snapshotWorkFlow.getId(), containerDetail.getBatchWorkDetail().getStep().getId(), Boolean.TRUE, Constants.LONG_ZERO);

        if (!ValidateUtils.isValid(workCellCheckStartRules)) {
            return Boolean.FALSE;
        }
        List<WorkCellCheckStartRule> workCellCheckStartRuleList = Lists.newArrayList();
        //抽检
        if (FuncKeyUtil.checkApi(FuncKeyConstants.PQC)) {
            workCellCheckStartRuleList.addAll(workCellCheckStartRules.stream().filter(workCellCheckStartRule -> workCellCheckStartRule.getCategory() == Constants.INT_FOUR).toList());
        }
        //终检
        if (FuncKeyUtil.checkApi(FuncKeyConstants.FQC)) {
            workCellCheckStartRuleList.addAll(workCellCheckStartRules.stream().filter(workCellCheckStartRule -> workCellCheckStartRule.getCategory() == Constants.INT_THREE).toList());
        }
        //末检
        if (FuncKeyUtil.checkApi(FuncKeyConstants.LQC)) {
            workCellCheckStartRuleList.addAll(workCellCheckStartRules.stream().filter(workCellCheckStartRule -> workCellCheckStartRule.getCategory() == Constants.INT_TWO).toList());
        }
        if (!ValidateUtils.isValid(workCellCheckStartRuleList)) {
            //未获取到当前检测规则，查看当前工单是否还存在待检的抽检，终检，末检如果存在则 返回true
            Long todoTaskCount =  !ObjectUtils.isEmpty(subWorkSheet) ?
                    inspectTaskRepository.countBySubWorkSheetIdAndStatusAndCategoryInAndDeleted(subWorkSheet.getId(), Boolean.FALSE,Arrays.asList(WorkCellStartCheckEnum.LQC_INSPECTION.getCategory(),
                            WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory(),
                            WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()) ,Constants.LONG_ZERO):
                    inspectTaskRepository.countByWorkSheetIdAndStatusAndCategoryInAndDeleted(workSheet.getId(), Boolean.FALSE,Arrays.asList(WorkCellStartCheckEnum.LQC_INSPECTION.getCategory(),WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory(),
                            WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()),Constants.LONG_ZERO);
            return Objects.nonNull(todoTaskCount) && (todoTaskCount > Constants.INT_ZERO)? Boolean.TRUE:Boolean.FALSE;
        }

        workCellCheckStartRuleList.forEach(workCellCheckStartRule -> {
            Long varietyObjId = Objects.nonNull(workCellCheckStartRule.getVarietyObj())?workCellCheckStartRule.getVarietyObj().getId():null;
            InspectTask inspectTask = !ObjectUtils.isEmpty(subWorkSheet) ?
                    inspectTaskRepository.findBySubWorkSheetIdAndStepIdAndVarietyIdAndStatusAndCategoryAndContainerCodeAndDeleted(subWorkSheet.getId(),containerDetail.getBatchWorkDetail().getStep().getId(),varietyObjId,Boolean.FALSE,workCellCheckStartRule.getCategory(),containerDetail.getContainerCode(),Constants.LONG_ZERO).orElse(new InspectTask()):
                    inspectTaskRepository.findByWorkSheetIdAndStepIdAndVarietyIdAndStatusAndCategoryAndContainerCodeAndDeleted(workSheet.getId(),containerDetail.getBatchWorkDetail().getStep().getId(),varietyObjId,Boolean.FALSE,workCellCheckStartRule.getCategory(),containerDetail.getContainerCode(),Constants.LONG_ZERO).orElse(new InspectTask());
            inspectTask.setWorkSheet(workSheet).setSubWorkSheet(subWorkSheet).setStep(containerDetail.getBatchWorkDetail().getStep())
                    .setCategory(workCellCheckStartRule.getCategory()).setStatus(Boolean.FALSE).setContainerCode(containerDetail.getContainerCode())
                    .setVarietyId(workCellCheckStartRule.getVarietyId());
            inspectTaskRepository.save(inspectTask);

        });
        return Boolean.TRUE;
    }

    /**
     * 验证是否存在待检任务
     * @param subWsProductionMode 投产力度
     * @param subWorkSheet 子工单
     * @param workSheet 工单
     */
    @Override
    public boolean getTodoInspectTask(Boolean subWsProductionMode, SubWorkSheet subWorkSheet, WorkSheet workSheet) {
        //未获取到当前检测规则，查看当前工单是否还存在待检的抽检，终检，末检如果存在则 返回true
        Long todoTaskCount =  subWsProductionMode ?
                inspectTaskRepository.countBySubWorkSheetIdAndStatusAndCategoryInAndDeleted(subWorkSheet.getId(), Boolean.FALSE,Arrays.asList(WorkCellStartCheckEnum.LQC_INSPECTION.getCategory(),
                        WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory(),
                        WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()) ,Constants.LONG_ZERO):
                inspectTaskRepository.countByWorkSheetIdAndStatusAndCategoryInAndDeleted(workSheet.getId(), Boolean.FALSE,Arrays.asList(WorkCellStartCheckEnum.LQC_INSPECTION.getCategory(),WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory(),
                        WorkCellStartCheckEnum.LAST_INSPECTION.getCategory()),Constants.LONG_ZERO);
        return Objects.nonNull(todoTaskCount) && (todoTaskCount > Constants.INT_ZERO)? Boolean.TRUE:Boolean.FALSE;

    }

    /**
     * 保存温湿度信息
     *
     * @param humitureSaveRequestDTOList 温湿度待保存参数列表
     * @return net.airuima.dto.base.BaseResultDTO 结果信息
     */
    @Override
    public BaseResultDTO saveHumitureRecord(List<RworkerHumitureSaveRequestDTO> humitureSaveRequestDTOList) {
        humitureSaveRequestDTOList.forEach(humitureSaveRequestDTO -> {
            if (StringUtils.isBlank(humitureSaveRequestDTO.getAreaCode())) {
                return;
            }
            if (Objects.isNull(humitureSaveRequestDTO.getTemperature()) || Objects.isNull(humitureSaveRequestDTO.getHumidity())) {
                return;
            }
            OrganizationArea organizationArea = organizationAreaRepository.findByCodeAndDeleted(humitureSaveRequestDTO.getAreaCode(), Constants.LONG_ZERO).orElse(null);
            if (Objects.isNull(organizationArea)) {
                return;
            }
            HumitureStandardDTO humitureStandard = humitureStandardRepository.findByAreaIdAndDeleted(organizationArea.getId(), Constants.LONG_ZERO).orElse(null);
            if (Objects.isNull(humitureStandard)) {
                return;
            }
            HumitureCheckHistoryDTO entity = new HumitureCheckHistoryDTO();
            entity.setArea(organizationArea).setHumidity(humitureSaveRequestDTO.getHumidity()).setTemperature(humitureSaveRequestDTO.getTemperature()).setRecordDate(LocalDateTime.now());
            try {
                humitureCheckHistoryService.create(entity);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        return new BaseResultDTO(Constants.OK);
    }


    /**
     * 待做的来料检验单
     *
     * @return java.util.List<net.airuima.rbase.domain.procedure.quality.IqcCheckHistory> 来料检验列表
     */
    @Override
    public List<IqcCheckHistory> getIqcTodo() {
        return iqcCheckHistoryService.todo();
    }

    /**
     * 放行待见任务
     * 首检：
     * 巡检：
     * 抽检：
     * 终检：
     * 末检：
     *
     * @param rworkerCancelTaskDto Rworker质检放行DTO
     */
    @Override
    public void cancelTask(RworkerCancelTaskDTO rworkerCancelTaskDto) {
        InspectTask inspectTask = inspectTaskRepository.findByIdAndDeleted(rworkerCancelTaskDto.getTaskId(), Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("inspectTaskIsEmpty", "待检任务不存在"));
        if (inspectTask.getStatus()) {
            throw new ResponseException("taskStatus", "待检任务已处理,请勿重复处理");
        }
        //创建质检历史
        CheckHistory checkHistory = BeanUtil.getHighestPrecedenceBean(IQualityService.class).saveCancelCheckHistoryRecord(inspectTask, rworkerCancelTaskDto.getStaffId());
        //更新最新检测记录
        if (inspectTask.getCategory() <= InspectCategoryEnum.IPQC_INSPECTION.getCategory()){
            BeanUtil.getHighestPrecedenceBean(IQualityService.class).updateLatestCheckResult(checkHistory, checkHistory.getCategory());
        }
        inspectTask.setStatus(Boolean.TRUE);
        inspectTaskRepository.save(inspectTask);
        //抽检、终检及末检可能在最后一个工序时需要更新工单状态
        if(inspectTask.getCategory() > InspectCategoryEnum.IPQC_INSPECTION.getCategory()){
            SubWorkSheet subWorkSheet = checkHistory.getSubWorkSheet();
            WorkSheet workSheet = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet():checkHistory.getWorkSheet();
            checkHistoryService.updateWorkSheetStatus(Objects.nonNull(subWorkSheet),subWorkSheet,workSheet);
        }
    }

    /**
     * 放行待检任务，创建检测历史经理
     *
     * @param inspectTask 待检任务
     * @param staffId     员工id
     * @return CheckHistory
     */
    @Override
    public CheckHistory saveCancelCheckHistoryRecord(InspectTask inspectTask, Long staffId) {
        CheckHistory checkHistory = new CheckHistory();
        SerialNumberDTO serialNumberDTO = new SerialNumberDTO();
        serialNumberDTO.setCode(InspectCategoryEnum.getKeyByCategory(inspectTask.getCategory()));
        checkHistory.setCategory(inspectTask.getCategory())
                .setSerialNumber(rbaseSerialNumberProxy.generate(serialNumberDTO))
                .setNumber(Constants.INT_ZERO)
                .setOperatorId(staffId)
                .setQualifiedNumber(Constants.INT_ZERO)
                .setVirtual(Boolean.FALSE)
                .setUnqualifiedNumber(Constants.INT_ZERO)
                .setStep(inspectTask.getStep()).setWorkCell(Objects.nonNull(inspectTask.getWorkCell()) ? inspectTask.getWorkCell() : null)
                .setVarietyObj(Objects.nonNull(inspectTask.getVariety()) ? inspectTask.getVariety() : null)
                .setSubWorkSheet(Objects.nonNull(inspectTask.getSubWorkSheet()) ? inspectTask.getSubWorkSheet() : null)
                .setWorkSheet(Objects.nonNull(inspectTask.getWorkSheet()) ? inspectTask.getWorkSheet() : null)
                .setResult(Constants.TRUE).setInspectNumber(Constants.INT_ZERO)
                .setRecordDate(LocalDateTime.now())
                .setDealWay(Constants.INT_THREE)
                .setStatus(Boolean.TRUE);
        return checkHistoryRepository.save(checkHistory);
    }

    /**
     * 新增或者更新质检缓存
     *
     * @param rworkerQualityCacheSaveDTO 新增或者更新质检缓存参数DTO
     */
    @Override
    public void saveQualityInspectCache(RworkerQualityCacheSaveDTO rworkerQualityCacheSaveDTO) {
        if (StringUtils.isBlank(rworkerQualityCacheSaveDTO.getCache())) {
            return;
        }
        //若首件，巡检，抽检，终检，末检则更新对应的任务缓存
        if(Objects.nonNull(rworkerQualityCacheSaveDTO.getId()) && rworkerQualityCacheSaveDTO.getCategory() < InspectCategoryEnum.IQC_INSPECTION.getCategory()){
            inspectTaskRepository.updateInspectTaskCache(rworkerQualityCacheSaveDTO.getId(),rworkerQualityCacheSaveDTO.getCache());
            return;
        }
        //若是IQC则更新IQC的记录缓存
        if(Objects.nonNull(rworkerQualityCacheSaveDTO.getId()) && rworkerQualityCacheSaveDTO.getCategory() == InspectCategoryEnum.IQC_INSPECTION.getCategory()){
            iqcCheckHistoryRepository.updateCache(rworkerQualityCacheSaveDTO.getId(),rworkerQualityCacheSaveDTO.getCache());
            return;
        }
        //Oem质检更新缓存
        if(Objects.nonNull(rworkerQualityCacheSaveDTO.getId()) && rworkerQualityCacheSaveDTO.getCategory() == InspectCategoryEnum.OEM_INSPECTION.getCategory()){
            rbaseOemProxy.updateCache(rworkerQualityCacheSaveDTO.getId(),rworkerQualityCacheSaveDTO.getCache());
            return;
        }
        //手动发起首检或巡检在保存缓存时需要新增一个首检或巡检任务并缓存
        if(Objects.isNull(rworkerQualityCacheSaveDTO.getId())
                && rworkerQualityCacheSaveDTO.getCategory()<=InspectCategoryEnum.IPQC_INSPECTION.getCategory()
                && Objects.nonNull(rworkerQualityCacheSaveDTO.getFaiIpqcTaskSaveInfo())){
            RworkerQualityCacheSaveDTO.FaiIpqcTaskSaveInfo faiIpqcTaskSaveInfo = rworkerQualityCacheSaveDTO.getFaiIpqcTaskSaveInfo();
            InspectTask inspectTask = inspectTaskRepository.findByWorkCellIdAndCategoryAndVarietyIdAndStatusAndDeleted(faiIpqcTaskSaveInfo.getWorkCellId(), rworkerQualityCacheSaveDTO.getCategory(),
                    faiIpqcTaskSaveInfo.getVarietyId(), Boolean.FALSE, Constants.LONG_ZERO).orElse(new InspectTask());
            boolean subWsProductionMode = commonService.subWsProductionMode();
            inspectTask.setCategory(rworkerQualityCacheSaveDTO.getCategory()).setWorkCell(new WorkCell(faiIpqcTaskSaveInfo.getWorkCellId()))
                    .setVarietyId(faiIpqcTaskSaveInfo.getVarietyId()).setWorkSheet(subWsProductionMode?
                            null:new WorkSheet(faiIpqcTaskSaveInfo.getProductWorkSheetId()))
                    .setSubWorkSheet(subWsProductionMode?new SubWorkSheet(faiIpqcTaskSaveInfo.getProductWorkSheetId()):null).setStatus(Boolean.FALSE).setCache(rworkerQualityCacheSaveDTO.getCache()).setDeleted(Constants.LONG_ZERO);
            inspectTaskRepository.save(inspectTask);
        }
    }



    /**
     * 组装下交保存测试数据，并上传至qms
     *
     * @param testDataSaveDto             上传数据
     * @param stepDynamicDataColumnGetDto 下交工序动态测试数据
     * @return void
     * <AUTHOR>
     * @date 2023/2/9
     */
    @Async
    @Override
    public void uploadQmsTestData(TestDataSaveDTO testDataSaveDto, StepDynamicDataColumnGetDTO stepDynamicDataColumnGetDto) {
        if (null == stepDynamicDataColumnGetDto) {
            return;
        }
        testDataSaveDto.setAnalyseCategoryName(stepDynamicDataColumnGetDto.getDynamicDataName()).setAnalyseCategoryCode(stepDynamicDataColumnGetDto.getDynamicDataCode());
        //添加测试数据明细
        List<TestDataDTO> testDataDtoList = Lists.newArrayList();
        this.addTestDataDTO(stepDynamicDataColumnGetDto.getColumnInfoList(), testDataDtoList);
        testDataSaveDto.setTestDataList(testDataDtoList);
        testDataSaveDto.setResult(testDataDtoList.stream().allMatch(TestDataDTO::getTestResult));
        //上传数据至qms
        rbaseQmsProxy.saveInstance(testDataSaveDto);
    }

    /**
     * 添加测试数据
     *
     * @param columnInfoList  下交保存的动态测试数据
     * @param testDataDtoList 上传至qms的数据
     * <AUTHOR>
     * @date 2023/2/9
     */
    public void addTestDataDTO(List<StepDynamicDataColumnGetDTO.ColumnInfo> columnInfoList, List<TestDataDTO> testDataDtoList) {

        if (!net.airuima.rbase.util.ValidateUtils.isValid(columnInfoList)) {
            return;
        }
        columnInfoList.forEach(columnInfo -> {
            if (net.airuima.rbase.util.ValidateUtils.isValid(columnInfo.getColumnInfoList())) {
                this.addTestDataDTO(columnInfo.getColumnInfoList(), testDataDtoList);
            } else if(StringUtils.isNotBlank(columnInfo.getColumnCode()) && !columnInfo.getColumnCode().equals("null")){
                //添加测试数据
                TestDataDTO testDataDto = new TestDataDTO();
                testDataDto.setTestColumnName(columnInfo.getColumnName()).setTestColumn(columnInfo.getColumnCode())
                        .setTestValue(columnInfo.getColumnValue()).setTestResult(columnInfo.getResult());
                //是否存在测试结果存在则上传
                if (!ObjectUtils.isEmpty(columnInfo.getColumnValue()) && "false".equals(columnInfo.getColumnValue())) {
                    testDataDto.setTestResult(Boolean.FALSE);
                }
                //是否为文件
                if (net.airuima.rbase.util.ValidateUtils.isValid(columnInfo.getDocumentDTOList())) {
                    testDataDto.setTestValue(columnInfo.getDocumentDTOList().toString());
                }
                //递归的获取下层级的动态测试数据
                testDataDtoList.add(testDataDto);
            }
        });
    }

    /**
     * 首检巡检抽检终检检测记录保存测试数据上传至qms
     * @param checkHistory 检测历史
     * @param pedigreeStepCheckRule 质检方案
     * @param snCheckItemDtoList 检测项目列表
     */
    @Override
    public void uploadInspectionTestData(CheckHistory checkHistory, PedigreeStepCheckRule pedigreeStepCheckRule, List<RworkerInspectionResultDTO.SnCheckItemDTO> snCheckItemDtoList) {
        if(CollectionUtils.isEmpty(snCheckItemDtoList)){
            return;
        }
        // 获取操作的员工
        StaffDTO staffDTO = rbaseStaffProxy.findByIdAndDeleted(checkHistory.getOperatorId(), Constants.LONG_ZERO);
        // 子工单投产 上传子工单号  否则上传工单号
        String  workSheetSerialNumber = Objects.isNull(checkHistory.getSubWorkSheet()) ? checkHistory.getWorkSheet().getSerialNumber() : checkHistory.getSubWorkSheet().getSerialNumber() ;
        // 获取工单
        WorkSheet workSheet = Objects.isNull(checkHistory.getSubWorkSheet()) ?  checkHistory.getWorkSheet() : checkHistory.getSubWorkSheet().getWorkSheet() ;
        // 根据工单获取组织架构
        OrganizationDTO organizationDto = workSheet.getOrganizationDto();
        // 获取工位
        WorkCell workCell = checkHistory.getWorkCell();
        // 获取产品谱系
        Pedigree pedigree = workSheet.getPedigree();
        List<TestDataSaveDTO> testDataSaveDtoList = Lists.newArrayList();
        for (RworkerInspectionResultDTO.SnCheckItemDTO snCheckItemDTO : snCheckItemDtoList) {
            //获取sn检测项目
            // 获取sn
            String sn = snCheckItemDTO.getSn();
            //测试数据列表
            List<TestDataDTO> testDataDtoList = Lists.newArrayList();
            // 获取检测项目结果列表
            List<RworkerInspectionResultDTO.CheckItemResultDTO> checkItemResultDtoList = Optional.ofNullable(snCheckItemDTO.getCheckItemResultDtoList()).orElse(Lists.newArrayList());
            for (RworkerInspectionResultDTO.CheckItemResultDTO checkItemResultDTO : checkItemResultDtoList) {
                // 获取检测项目结果
                // 根据检测项目结果设置测试数据明细
                TestDataDTO testDataDto = new TestDataDTO();
                testDataDto.setTestColumnName(checkItemResultDTO.getName()).setTestColumn(checkItemResultDTO.getCode())
                        .setTestValue(checkItemResultDTO.getCheckData())
                        .setTestResult(Optional.ofNullable(checkItemResultDTO.getResult()).orElse(Boolean.FALSE));
                testDataDtoList.add(testDataDto);
            }
            Boolean snTestResult = testDataDtoList.stream().map(TestDataDTO::getTestResult).allMatch(Boolean.TRUE::equals);
            // 构建qms测试数据
            TestDataSaveDTO testDataSaveDto = new TestDataSaveDTO();
            testDataSaveDto.setWorkSheetSerialNumber(workSheetSerialNumber)
                    .setSn(sn)
                    .setWorkCellName(Optional.ofNullable(workCell).map(WorkCell::getName).orElse(null))
                    .setWorkCellCode(Optional.ofNullable(workCell).map(WorkCell::getCode).orElse(null))
                    .setOrganizationCode(Optional.ofNullable(organizationDto).map(OrganizationDTO::getCode).orElse(null))
                    .setOrganizationName(Optional.ofNullable(organizationDto).map(OrganizationDTO::getName).orElse(null))
                    .setPedigreeCode(pedigree.getCode())
                    .setPedigreeName(pedigree.getName())
                    .setStaffName(Optional.ofNullable(staffDTO).map(StaffDTO::getName).orElse(null))
                    .setStaffCode(Optional.ofNullable(staffDTO).map(StaffDTO::getCode).orElse(null))
                    .setAnalyseCategoryCode(pedigreeStepCheckRule.getCode())
                    .setAnalyseCategoryName(pedigreeStepCheckRule.getName())
                    .setTestDataList(testDataDtoList)
                    .setResult(snTestResult)
                    .setTestDate(LocalDateTime.now());
            testDataSaveDtoList.add(testDataSaveDto);
        }
        //上传数据至qms
        rbaseQmsProxy.batchSaveInstance(testDataSaveDtoList);
    }

}
