package net.airuima.rworker.service.enviroment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.AbstractDto;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 洁净度等级 Domain
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
@Schema(name = "洁净度等级(CleanlinessGrade)", description = "洁净度等级")
public class CleanlinessGradeDTO extends AbstractDto implements Serializable {

    /**
     * 等级编码
     */
    @Schema(description = "等级编码", required = true)
    private String code;

    /**
     * 等级名称
     */
    @Schema(description = "等级名称", required = true)
    private String name;

    public CleanlinessGradeDTO() {
    }

    public CleanlinessGradeDTO(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public CleanlinessGradeDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getName() {
        return name;
    }

    public CleanlinessGradeDTO setName(String name) {
        this.name = name;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CleanlinessGradeDTO cleanlinessGradeDTO = (CleanlinessGradeDTO) o;
        if (cleanlinessGradeDTO.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), cleanlinessGradeDTO.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }
}
