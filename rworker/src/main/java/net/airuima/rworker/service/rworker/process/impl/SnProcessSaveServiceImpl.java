package net.airuima.rworker.service.rworker.process.impl;

import net.airuima.constant.Constants;
import net.airuima.domain.base.AuditSfIdEntity;
import net.airuima.rbase.constant.*;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.quality.OnlineReworkRule;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.Container;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.domain.procedure.report.StaffPerform;
import net.airuima.rbase.domain.procedure.report.StaffPerformUnqualifiedItem;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.dto.batch.PreContainerDetailInfo;
import net.airuima.rbase.dto.dynamic.StepDynamicDataColumnGetDTO;
import net.airuima.rbase.dto.maintain.MaintainHistoryDTO;
import net.airuima.rbase.dto.ocmes.BakeCycleBakeAgeingSaveRequestDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerBatchStepSaveRequestDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerContainerStepSaveRequestDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerSnStepSaveRequestDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerStepProcessBaseDTO;
import net.airuima.rbase.dto.rworker.process.dto.general.MaterialSaveInfo;
import net.airuima.rbase.proxy.maintain.RbaseMaintainHistoryProxy;
import net.airuima.rbase.proxy.organization.RbaseStaffProxy;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.rbase.repository.base.quality.OnlineReworkRuleRepository;
import net.airuima.rbase.repository.base.quality.UnqualifiedItemRepository;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.rbase.repository.procedure.batch.ContainerDetailRepository;
import net.airuima.rbase.repository.procedure.batch.ContainerRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformUnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkStatusRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.ocmes.BakeCycleBakeAgeingModelService;
import net.airuima.rbase.service.procedure.aps.SubWorkSheetService;
import net.airuima.rbase.service.procedure.aps.api.IProductionPlanService;
import net.airuima.rbase.service.report.api.IWorkSheetStatisticsService;
import net.airuima.rbase.service.report.api.IWorkSheetStepStatisticsService;
import net.airuima.rworker.domain.RworkerCache;
import net.airuima.rworker.service.rworker.cache.IRworkerCacheService;
import net.airuima.rworker.service.rworker.dynamic.IDynamicService;
import net.airuima.rworker.service.rworker.facility.IFacilityService;
import net.airuima.rworker.service.rworker.facility.IWearingPartService;
import net.airuima.rworker.service.rworker.material.IMaterialService;
import net.airuima.rworker.service.rworker.oem.IOemService;
import net.airuima.rworker.service.rworker.process.*;
import net.airuima.rworker.service.rworker.quality.IQualityService;
import net.airuima.rworker.web.rest.rworker.process.dto.RworkerStepProcessGlobalDTO;
import net.airuima.util.*;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.klock.annotation.Klock;
import org.springframework.boot.autoconfigure.klock.model.LockTimeoutStrategy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * SN模式工序保存相关Service
 *
 * <AUTHOR>
 * @date 2023/4/12
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class SnProcessSaveServiceImpl implements ISnProcessSaveService {
    private static final String ERR_MSG = "容器物料汇总数据与对应容器内SN物料汇总数据不一致";
    private static final String CONTAINER_MATERIAL_NOT_MATCHED_SN_MATERIAL = "containerMaterialNotMatchedSnMaterial";


    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private SnWorkDetailRepository snWorkDetailRepository;
    @Autowired
    private SnWorkStatusRepository snWorkStatusRepository;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private UnqualifiedItemRepository unqualifiedItemRepository;
    @Autowired
    private ContainerRepository containerRepository;
    @Autowired
    private RbaseStaffProxy rbaseStaffProxy;
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private WorkCellRepository workCellRepository;
    @Autowired
    private StaffPerformUnqualifiedItemRepository staffPerformUnqualifiedItemRepository;
    @Autowired
    private StaffPerformRepository staffPerformRepository;
    @Autowired
    private IBatchProcessRequestService[] batchProcessRequestServices;
    @Autowired
    private IBatchProcessSaveService[] batchProcessSaveServices;
    @Autowired
    private IMaterialService[] materialServices;
    @Autowired
    private IFacilityService[] facilityServices;
    @Autowired
    private IQualityService[] qualityServices;
    @Autowired
    private IDynamicService[] dynamicServices;
    @Autowired
    private IWearingPartService[] wearingPartServices;
    @Autowired
    private BakeCycleBakeAgeingModelService[] bakeCycleBakeAgeingModelServices;
    @Autowired
    private IProductionPlanService[] productionPlanServices;
    @Autowired
    private IWorkSheetStatisticsService[] workSheetStatisticsServices;
    @Autowired
    private CommonService commonService;
    @Autowired
    private OnlineReworkRuleRepository onlineReworkRuleRepository;
    @Autowired
    private SubWorkSheetService subWorkSheetService;
    @Autowired
    private RbaseMaintainHistoryProxy rbaseMaintainHistoryProxy;
    @Autowired
    private IToDoStepStepValidateService[] toDoStepStepValidateServices;
    @Autowired
    private IRworkerCacheService[] rworkerCacheServices;
    @Autowired
    private IWorkSheetStepStatisticsService[] workSheetStepStatisticsServices;
    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;
    @Autowired
    private IWorkSheetApsService[] workSheetApsServices;
    @Autowired
    private IOemService[] oemServices;

    /**
     * 批量保存 SN工序生产数据
     *
     * @param rworkerSnStepSaveRequestDtoList SN待保存生产工序参数DTO列表
     */
    @Override
    public void createSnStep(List<RworkerSnStepSaveRequestDTO> rworkerSnStepSaveRequestDtoList) {
        Set<RworkerSnStepSaveRequestDTO> rworkerSnStepSaveRequestDTOSet = new HashSet<>();
        Map<String,List<RworkerSnStepSaveRequestDTO>> rworkerSnStepSaveRequestDTOGroup = rworkerSnStepSaveRequestDtoList.stream().collect(Collectors.groupingBy(rworkerSnStepSaveRequestDTO -> rworkerSnStepSaveRequestDTO.getStepId()+ net.airuima.rbase.constant.Constants.UNDERLINE+rworkerSnStepSaveRequestDTO.getProductWorkSheetId()));
        rworkerSnStepSaveRequestDTOGroup.forEach((key,rworkerSnStepSaveRequestDtos)-> rworkerSnStepSaveRequestDTOSet.add(rworkerSnStepSaveRequestDtos.get(Constants.INT_ZERO)));
        rworkerSnStepSaveRequestDtoList = new ArrayList<>(rworkerSnStepSaveRequestDTOSet);
        RworkerStepProcessGlobalDTO rworkerStepProcessGlobalDTO = new RworkerStepProcessGlobalDTO();
        if (rworkerSnStepSaveRequestDtoList.stream().anyMatch(rworkerSnStepSaveRequestDTO -> rworkerSnStepSaveRequestDTO.getUnqualifiedNumber() > Constants.INT_ZERO && rworkerSnStepSaveRequestDTO.getReInspect())) {
            SerialNumberDTO serialNumberDto = new SerialNumberDTO(net.airuima.rbase.constant.Constants.STEP_REINSPECT_SERIAL_NUMBER, null, null);
            rworkerStepProcessGlobalDTO.setStepReinspectSerialNumber(rbaseSerialNumberProxy.generate(serialNumberDto));
        }
        rworkerSnStepSaveRequestDtoList.forEach(rworkerSnStepSaveRequestDTO -> this.createStep(rworkerSnStepSaveRequestDTO, rworkerStepProcessGlobalDTO));
        //保存可能的工序易损件规则对应的最新使用易损件信息
        List<RworkerSnStepSaveRequestDTO.SingleSnSaveInfo> lastSingleSnSaveInfoList = rworkerSnStepSaveRequestDtoList.get(rworkerSnStepSaveRequestDtoList.size() - Constants.INT_ONE).getSingleSnSaveInfoList();
        RworkerSnStepSaveRequestDTO.SingleSnSaveInfo lastSingleSnSaveInfo = lastSingleSnSaveInfoList.get(lastSingleSnSaveInfoList.size() - Constants.INT_ONE);
        if (!CollectionUtils.isEmpty(lastSingleSnSaveInfo.getWearingPartInfoList())) {
            wearingPartServices[0].saveLatestStepWearingPart(lastSingleSnSaveInfo.getWearingPartInfoList());
        }
        //删除可能的生产缓存信息
        rworkerCacheServices[0].deletedCacheByWorkCell(rworkerSnStepSaveRequestDtoList.get(Constants.INT_ZERO).getWorkCellId());
    }

    /**
     * 保存SN工序生产数据
     *
     * @param rworkerSnStepSaveRequestDTO SN待保存生产工序参数DTO
     */
    @Klock(keys = {"#rworkerSnStepSaveRequestDTO.productWorkSheetId", "#rworkerSnStepSaveRequestDTO.stepId"}, waitTime = 60, leaseTime = 60, lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST)
    public void createStep(RworkerSnStepSaveRequestDTO rworkerSnStepSaveRequestDTO, RworkerStepProcessGlobalDTO rworkerStepProcessGlobalDTO) {
        //过滤掉不良数量为空的不良项目数据
        if(!CollectionUtils.isEmpty(rworkerSnStepSaveRequestDTO.getUnqualifiedItemSaveInfoList())){
            rworkerSnStepSaveRequestDTO.setUnqualifiedItemSaveInfoList(rworkerSnStepSaveRequestDTO.getUnqualifiedItemSaveInfoList().stream().filter(unqualifiedItemSaveInfo -> Objects.nonNull(unqualifiedItemSaveInfo.getNumber())).toList());
        }
        if(!CollectionUtils.isEmpty(rworkerSnStepSaveRequestDTO.getContainerSnSaveInfoList())){
            rworkerSnStepSaveRequestDTO.getContainerSnSaveInfoList().forEach(containerSnSaveInfo -> {
                if(!CollectionUtils.isEmpty(containerSnSaveInfo.getUnqualifiedItemSaveInfoList())){
                    containerSnSaveInfo.setUnqualifiedItemSaveInfoList(containerSnSaveInfo.getUnqualifiedItemSaveInfoList().stream().filter(unqualifiedItemSaveInfo -> Objects.nonNull(unqualifiedItemSaveInfo.getNumber())).toList());
                }
                if(!CollectionUtils.isEmpty(containerSnSaveInfo.getSingleSnSaveInfoList())){
                    containerSnSaveInfo.getSingleSnSaveInfoList().forEach(singleSnSaveInfo -> {
                        singleSnSaveInfo.setUnqualifiedItemSaveInfo(Objects.nonNull(singleSnSaveInfo.getUnqualifiedItemSaveInfo()) && Objects.nonNull(singleSnSaveInfo.getUnqualifiedItemSaveInfo().getNumber())?singleSnSaveInfo.getUnqualifiedItemSaveInfo():null);
                    });
                }
            });
        }
        if(!CollectionUtils.isEmpty(rworkerSnStepSaveRequestDTO.getSingleSnSaveInfoList())){
            rworkerSnStepSaveRequestDTO.getSingleSnSaveInfoList().forEach(singleSnSaveInfo -> {
                singleSnSaveInfo.setUnqualifiedItemSaveInfo(Objects.nonNull(singleSnSaveInfo.getUnqualifiedItemSaveInfo()) && Objects.nonNull(singleSnSaveInfo.getUnqualifiedItemSaveInfo().getNumber())?singleSnSaveInfo.getUnqualifiedItemSaveInfo():null);
            });
        }
        //首先验证下交数据
        this.validate(rworkerSnStepSaveRequestDTO);
        //获取系统配置的投产粒度(子工单或者工单)
        boolean subWsProductionMode = commonService.subWsProductionMode();
        SubWorkSheet subWorkSheet = subWsProductionMode ? subWorkSheetRepository.getReferenceById(rworkerSnStepSaveRequestDTO.getProductWorkSheetId()) : new SubWorkSheet();
        WorkSheet workSheet = subWsProductionMode ? subWorkSheet.getWorkSheet() : workSheetRepository.getReferenceById(rworkerSnStepSaveRequestDTO.getProductWorkSheetId());
        WorkFlow workFlowTemp = workSheet.getWorkFlow();
        WorkCell workCell = workCellRepository.getReferenceById(rworkerSnStepSaveRequestDTO.getWorkCellId());
        StaffDTO staffDTO = rbaseStaffProxy.findByIdAndDeleted(rworkerSnStepSaveRequestDTO.getStaffId(),Constants.LONG_ZERO);
        List<WsStep> wsStepList = null;
        //当前下交的Sn是否存在纯单支在线返工的情形
        boolean singleSnOnlineRepair = Boolean.FALSE;
        List<String> snList = rworkerSnStepSaveRequestDTO.getSingleSnSaveInfoList().stream().map(RworkerSnStepSaveRequestDTO.SingleSnSaveInfo::getSn).toList();
        List<SnWorkStatus> snWorkStatusList = snWorkStatusRepository.findBySnInAndDeleted(snList, Constants.LONG_ZERO);
        List<SnWorkDetail> snWorkDetailList = subWsProductionMode ? snWorkDetailRepository.findLatestStepWorkDetailBySnInAndSubWorkSheetIdAndStepIdAndDeleted(snList, rworkerSnStepSaveRequestDTO.getProductWorkSheetId(), rworkerSnStepSaveRequestDTO.getStepId(), Constants.LONG_ZERO)
            : snWorkDetailRepository.findLatestStepWorkDetailBySnInAndWorkSheetIdAndStepIdAndDeleted(snList, rworkerSnStepSaveRequestDTO.getProductWorkSheetId(), rworkerSnStepSaveRequestDTO.getStepId(), Constants.LONG_ZERO);

        //判断是否存在纯单支在线返工且不允许单支在线返工时多个SN下交
        if (!CollectionUtils.isEmpty(snWorkStatusList)) {
            for (SnWorkStatus snWorkStatus : snWorkStatusList) {
                if (subWsProductionMode && null != snWorkStatus && snWorkStatus.getReworkTime() > Constants.INT_ZERO && null != snWorkStatus.getLatestReworkSnWorkDetail() && snWorkStatus.getLatestReworkSnWorkDetail().getSubWorkSheet().getId().equals(subWorkSheet.getId())) {
                    singleSnOnlineRepair = Boolean.TRUE;
                    break;
                } else if (!subWsProductionMode && null != snWorkStatus && snWorkStatus.getReworkTime() > Constants.INT_ZERO && null != snWorkStatus.getLatestReworkSnWorkDetail() && snWorkStatus.getLatestReworkSnWorkDetail().getWorkSheet().getId().equals(workSheet.getId())) {
                    singleSnOnlineRepair = Boolean.TRUE;
                    break;
                }
            }
            if (singleSnOnlineRepair && (snWorkStatusList.size() > Constants.INT_ONE)) {
                throw new ResponseException("error.singleOnlineRepairError", "单支直接在线返工时(无返修单且无返工单时)不允许多支同时一起返工");
            }
        }

        //若Sn已不合格且Sn最近一次的不良SN详情所对应的工单与当前生产工单一致则认为是纯单支在线返工，纯单支在线返工时不更新批量详情数据
        if (singleSnOnlineRepair) {
            wsStepList = commonService.findOnlineSnReworkStep(snWorkStatusList.get(Constants.INT_ZERO).getWorkFlow(), workSheet);
            workFlowTemp = snWorkStatusList.get(Constants.INT_ZERO).getWorkFlow();
        }
        if (!singleSnOnlineRepair) {
            wsStepList = this.getWsSteps(subWorkSheet, workSheet);
        }
        WsStep currWsStep = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(rworkerSnStepSaveRequestDTO.getStepId())).findFirst().orElseThrow(() -> new ResponseException("error.wsStepNotExist", "工单工序快照不存在"));
        WorkFlow workFlow = null != currWsStep.getWorkFlow() ? currWsStep.getWorkFlow() : workFlowTemp;
        Step step = currWsStep.getStep();
        //保存批量详情数据及关联的追溯数据(物料批次、设备等)
        RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO = new RworkerStepProcessBaseDTO(subWsProductionMode, workSheet, subWorkSheet, workCell, currWsStep, step, staffDTO, wsStepList);
        rworkerStepProcessBaseDTO.setWorkFlow(workFlow).setSnStepLatestWorkDetailList(snWorkDetailList).setSnWorkStatusList(snWorkStatusList);
        //验证时间间隔
        BaseResultDTO baseResultDTO = toDoStepStepValidateServices[0].validateSnStepIntervalWhenSave(rworkerSnStepSaveRequestDTO, rworkerStepProcessBaseDTO);
        if (Objects.nonNull(baseResultDTO) && baseResultDTO.getStatus().equals(Constants.KO)) {
            throw new ResponseException(baseResultDTO.getKey(), baseResultDTO.getMessage());
        }
        //获取生产缓存中的工序开始时间
        RworkerCache rworkerCache = rworkerCacheServices[0].findCacheByWorkCell(rworkerSnStepSaveRequestDTO.getWorkCellId());
        if (null != rworkerCache) {
            rworkerSnStepSaveRequestDTO.setStartTime(rworkerCache.getStartTime());
        }
        //当前工序是否为在线调整工序
        boolean currentStepIsOnlineRework = currWsStep.getCategory() ==  ConstantsEnum.STEP_ONLINE_PRE_REWORK_CATEGORY.getCategoryName() || currWsStep.getCategory() == ConstantsEnum.STEP_ONLINE_REWORK_CATEGORY.getCategoryName();
        //当前调整工序是否没有可调整的不良项目(投产数为0)
        boolean currentOnlineReworkStepNoPreUnqualifiedItem;
        //若当前工序为在线返工且前端传过来的Sn对应的SN状态列表里只要有一个Sn的最新不良不属于规则里的不良则认为调整工序投产数为0
        if(currentStepIsOnlineRework && !CollectionUtils.isEmpty(snWorkStatusList)){
           List<UnqualifiedItem> unqualifiedItemList =  onlineReworkRuleRepository.findUnqualifiedItemByStepIdAndDeleted(step.getId(),Constants.LONG_ZERO);
           if(!CollectionUtils.isEmpty(unqualifiedItemList)){
               List<Long> onlineRworkUnqualifiedItemIdList = unqualifiedItemList.stream().map(UnqualifiedItem::getId).toList();
               if(snWorkStatusList.stream().anyMatch(snWorkStatus -> Objects.isNull(snWorkStatus.getLatestUnqualifiedItem()) || !onlineRworkUnqualifiedItemIdList.contains(snWorkStatus.getLatestUnqualifiedItem().getId()))){
                   rworkerSnStepSaveRequestDTO.setNumber(Constants.INT_ZERO).setQualifiedNumber(Constants.INT_ZERO).setUnqualifiedNumber(Constants.INT_ZERO);
                   currentOnlineReworkStepNoPreUnqualifiedItem = Boolean.TRUE;
               } else {
                   currentOnlineReworkStepNoPreUnqualifiedItem = Boolean.FALSE;
               }
           } else {
               currentOnlineReworkStepNoPreUnqualifiedItem = Boolean.FALSE;
           }
        } else {
            currentOnlineReworkStepNoPreUnqualifiedItem = Boolean.FALSE;
        }
        if(!singleSnOnlineRepair && !CollectionUtils.isEmpty(rworkerSnStepSaveRequestDTO.getContainerSnSaveInfoList())){
            rworkerSnStepSaveRequestDTO.setNumber(rworkerSnStepSaveRequestDTO.getContainerSnSaveInfoList().stream().map(RworkerSnStepSaveRequestDTO.ContainerSnSaveInfo::getNumber).reduce(Integer::sum).orElse(rworkerSnStepSaveRequestDTO.getNumber()));
        }else if(!singleSnOnlineRepair && !CollectionUtils.isEmpty(rworkerSnStepSaveRequestDTO.getSingleSnSaveInfoList())){
            rworkerSnStepSaveRequestDTO.setNumber(rworkerSnStepSaveRequestDTO.getSingleSnSaveInfoList().stream().map(RworkerSnStepSaveRequestDTO.SingleSnSaveInfo::getNumber).reduce(Integer::sum).orElse(rworkerSnStepSaveRequestDTO.getNumber()));
        }
        //只有在非纯单支在线返工情况下才可以更新批量详情
        BatchWorkDetail batchWorkDetail = singleSnOnlineRepair ? null : subWsProductionMode ? this.createSubWsWorkDetail(rworkerSnStepSaveRequestDTO, rworkerStepProcessBaseDTO) : this.createWsWorkDetail(rworkerSnStepSaveRequestDTO, rworkerStepProcessBaseDTO);
        //若存在容器则保存容器详情数据及关联的追溯数据(物料批次、设备、易损件等)，然后再保存SN工作详情及关联数据
        if (null != batchWorkDetail && ValidateUtils.isValid(rworkerSnStepSaveRequestDTO.getContainerSnSaveInfoList())) {
            boolean finalSingleSnOnlineRepair1 = singleSnOnlineRepair;
            rworkerSnStepSaveRequestDTO.getContainerSnSaveInfoList().forEach(containerSnSaveInfo -> {
                if(CollectionUtils.isEmpty(containerSnSaveInfo.getSingleSnSaveInfoList())){
                    throw  new ResponseException("error.containerSaveSnIsEmpty","容器投产工序待保存SN参数为空");
                }
                //如果是在线调整工序则需要将上面合格的SN补充道待保存工序里
                if(currentStepIsOnlineRework) {
                    int snWorkStatus = rworkerStepProcessBaseDTO.getWorkSheet().getCategory() != WsEnum.NORMAL_WS.getCategory() ? SnWorkStatusEnum.IN_THE_REPAIR.getStatus() : SnWorkStatusEnum.PUT_INTO_PRODUCTION.getStatus();
                    List<SnWorkStatus> snWorkStatuses = null;
                    ContainerDetail containerDetail = Boolean.TRUE.equals(rworkerStepProcessBaseDTO.getSubWsProductionMode()) ?
                            containerDetailRepository.findTop1ByBatchWorkDetailSubWorkSheetIdAndContainerIdAndDeletedOrderByIdDesc(rworkerStepProcessBaseDTO.getSubWorkSheet().getId(), containerSnSaveInfo.getBindContainerId(), Constants.LONG_ZERO)
                            : containerDetailRepository.findTop1ByBatchWorkDetailWorkSheetIdAndContainerIdAndDeletedOrderByIdDesc(rworkerStepProcessBaseDTO.getWorkSheet().getId(), containerSnSaveInfo.getBindContainerId(), Constants.LONG_ZERO);
                    if (null != containerDetail) {
                        List<SnWorkDetail> snWorkDetails = snWorkDetailRepository.findByContainerDetailIdAndDeleted(containerDetail.getId(), Constants.LONG_ZERO);
                        snWorkStatuses = ValidateUtils.isValid(snWorkDetails) ? snWorkStatusRepository.findByLatestSnWorkDetailIdInAndStatusAndDeleted(snWorkDetails.stream().map(SnWorkDetail::getId).collect(Collectors.toList()), snWorkStatus, net.airuima.rbase.constant.Constants.LONG_ZERO) : null;
                        rworkerStepProcessBaseDTO.setSnStepLatestWorkDetailList(snWorkDetails).setSnWorkStatusList(snWorkStatuses);
                    }
                    if(!CollectionUtils.isEmpty(snWorkStatuses)){
                        List<RworkerSnStepSaveRequestDTO.SingleSnSaveInfo> singleSnSaveInfoList = containerSnSaveInfo.getSingleSnSaveInfoList();
                        snWorkStatuses.forEach(snWorkStatus1 -> {
                            if(rworkerSnStepSaveRequestDTO.getSingleSnSaveInfoList().stream().noneMatch(singleSnSaveInfo -> singleSnSaveInfo.getSn().equals(snWorkStatus1.getSn()))){
                                singleSnSaveInfoList.add(new RworkerSnStepSaveRequestDTO.SingleSnSaveInfo(snWorkStatus1));
                            }
                        });
                        containerSnSaveInfo.setSingleSnSaveInfoList(singleSnSaveInfoList);
                    }
                }
                rworkerStepProcessBaseDTO.setStepReinspectSerialNumber(rworkerStepProcessGlobalDTO.getStepReinspectSerialNumber());
                ContainerDetail containerDetail = this.createContainerDetail(batchWorkDetail, rworkerSnStepSaveRequestDTO, containerSnSaveInfo, rworkerStepProcessBaseDTO);
                containerSnSaveInfo.getSingleSnSaveInfoList().forEach(singleSnSaveInfo -> this.createSnWorkDetail(batchWorkDetail, containerDetail, singleSnSaveInfo, rworkerSnStepSaveRequestDTO, rworkerStepProcessBaseDTO, finalSingleSnOnlineRepair1));
            });
        } else {
            if(CollectionUtils.isEmpty(rworkerSnStepSaveRequestDTO.getSingleSnSaveInfoList())){
                throw  new ResponseException("error.workSheetSaveSnIsEmpty","工单投产工序待保存SN参数为空");
            }
            //若不存在容器则直接按照单支进行保存SN工作详情及关联数据
            boolean finalSingleSnOnlineRepair = singleSnOnlineRepair;
            //如果是在线调整工序则需要将上面合格的SN补充道待保存工序里
            if(currentStepIsOnlineRework){
                int snWorkStatus = rworkerStepProcessBaseDTO.getWorkSheet().getCategory() != WsEnum.NORMAL_WS.getCategory() ? SnWorkStatusEnum.IN_THE_REPAIR.getStatus() : SnWorkStatusEnum.PUT_INTO_PRODUCTION.getStatus();
                List<SnWorkStatus> snWorkStatuses = Boolean.TRUE.equals(rworkerStepProcessBaseDTO.getSubWsProductionMode()) ?
                        snWorkStatusRepository.findBySubWorkSheetIdAndStatusAndDeleted(rworkerStepProcessBaseDTO.getSubWorkSheet().getId(), snWorkStatus, net.airuima.rbase.constant.Constants.LONG_ZERO)
                        : snWorkStatusRepository.findByWorkSheetIdAndStatusAndDeleted(rworkerStepProcessBaseDTO.getWorkSheet().getId(), snWorkStatus, net.airuima.rbase.constant.Constants.LONG_ZERO);
                rworkerStepProcessBaseDTO.setSnStepLatestWorkDetailList( snWorkStatuses.stream().map(SnWorkStatus::getLatestSnWorkDetail).toList()).setSnWorkStatusList(snWorkStatuses);
                List<RworkerSnStepSaveRequestDTO.SingleSnSaveInfo> singleSnSaveInfoList = rworkerSnStepSaveRequestDTO.getSingleSnSaveInfoList();
                snWorkStatuses.forEach(snWorkStatus1 -> {
                    if(rworkerSnStepSaveRequestDTO.getSingleSnSaveInfoList().stream().noneMatch(singleSnSaveInfo -> singleSnSaveInfo.getSn().equals(snWorkStatus1.getSn()))){
                        singleSnSaveInfoList.add(new RworkerSnStepSaveRequestDTO.SingleSnSaveInfo(snWorkStatus1));
                    }
                });
            }
            rworkerStepProcessBaseDTO.setStepReinspectSerialNumber(rworkerStepProcessGlobalDTO.getStepReinspectSerialNumber());
            rworkerSnStepSaveRequestDTO.getSingleSnSaveInfoList().forEach(singleSnSaveInfo ->{
                if(currentOnlineReworkStepNoPreUnqualifiedItem && currentStepIsOnlineRework){
                    singleSnSaveInfo.setQualifiedNumber(Constants.INT_ONE).setUnqualifiedNumber(Constants.INT_ZERO).setUnqualifiedItemSaveInfo(null);
                }
                this.createSnWorkDetail(batchWorkDetail, null, singleSnSaveInfo, rworkerSnStepSaveRequestDTO, rworkerStepProcessBaseDTO, finalSingleSnOnlineRepair);
            });
            //工单 单支 添加抽检 巡检任务
            if (currWsStep.getRequestMode() == ConstantsEnum.WORK_SHEET_REQUEST_MODE.getCategoryName()) {
                qualityServices[0].createBatchInspectTask(subWsProductionMode, subWsProductionMode ? subWorkSheet : null, !subWsProductionMode ? workSheet : null, step);
            }
        }
        //最后一个工序完成后需要更新工单及销售订单相关数据
        if (StringUtils.isBlank(currWsStep.getAfterStepId()) && null != batchWorkDetail && batchWorkDetail.getFinish() == ConstantsEnum.FINISH_STATUS.getCategoryName()) {
            //工序做完是否还存在待处理的任务
            rworkerStepProcessBaseDTO.setInspectTask(qualityServices[0].getTodoInspectTask(subWsProductionMode,subWorkSheet,workSheet));
            BeanUtil.getHighestPrecedenceBean(ISnProcessSaveService.class).calculateWorkSheetAfterAllStepFinished(rworkerStepProcessBaseDTO,batchWorkDetail);
        } else if (null != batchWorkDetail && StringUtils.isNotBlank(currWsStep.getAfterStepId()) && batchWorkDetail.getFinish() == ConstantsEnum.FINISH_STATUS.getCategoryName()
                && batchWorkDetail.getTransferNumber() == Constants.INT_ZERO && currWsStep.getCategory() != StepCategoryEnum.ONLINE_ADJUSTMENT_STEP.getStatus() && currWsStep.getCategory() != StepCategoryEnum.ONLINE_PRELIMINARY_ADJUSTMENT_STEP.getStatus()) {
            MaintainHistoryDTO maintainHistory = subWsProductionMode ?
                    rbaseMaintainHistoryProxy.findTop1BySubWorkSheetIdAndStatusLessThanAndDeleted(subWorkSheet.getId(), MaintainEnum.MAINTAIN_FINISHED_STATUS.getStatus(), Constants.LONG_ZERO).orElse(null)
                    : rbaseMaintainHistoryProxy.findTop1ByWorkSheetIdAndStatusLessThanAndDeleted(workSheet.getId(), MaintainEnum.MAINTAIN_FINISHED_STATUS.getStatus(), Constants.LONG_ZERO).orElse(null);
            if (null == maintainHistory) {
                List<WsStep> childWsStepList = new ArrayList<>();
                commonService.findChildWsStep(wsStepList, childWsStepList, currWsStep);
                if (!CollectionUtils.isEmpty(childWsStepList)) {
                    List<WsStep> onlineAdjustWsSteoList = childWsStepList.stream().filter(wsStep -> wsStep.getCategory() == StepCategoryEnum.ONLINE_ADJUSTMENT_STEP.getStatus() || wsStep.getCategory() == StepCategoryEnum.ONLINE_PRELIMINARY_ADJUSTMENT_STEP.getStatus()).toList();
                    if (CollectionUtils.isEmpty(onlineAdjustWsSteoList)) {
                        BeanUtil.getHighestPrecedenceBean(ISnProcessSaveService.class).calculateWorkSheetAfterAllStepFinished(rworkerStepProcessBaseDTO,batchWorkDetail);
                    }
                }
            }
        }
        //更新生产计划表 工序组
        updateStepGroupProductionPlan(rworkerSnStepSaveRequestDTO, workSheet, step);
        //删除可能的生产缓存信息
        rworkerCacheServices[0].deletedCacheByWorkCell(rworkerSnStepSaveRequestDTO.getWorkCellId());
        //创建工序外协
        oemServices[0].grenadeStepOemOrder(batchWorkDetail);
        //完工上传
        workSheetApsServices[0].wsCompleteUpload(currWsStep,subWorkSheet,workSheet);
    }

    /**
     * 工序完成更新子工单、工单等统计数据
     *
     * @param rworkerStepProcessBaseDTO 工序下交通用参数
     */
    @Override
    public void calculateWorkSheetAfterAllStepFinished(RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO,BatchWorkDetail lastBatchWorkDetail) {
        WorkSheet workSheet = rworkerStepProcessBaseDTO.getWorkSheet();
        int realFinishedNumber;
        //子工单投产粒度时更新子工单和工单的合格数及不合格数
        if (Boolean.TRUE.equals(rworkerStepProcessBaseDTO.getSubWsProductionMode())) {
            SubWorkSheet subWorkSheet = subWorkSheetRepository.getReferenceById(rworkerStepProcessBaseDTO.getSubWorkSheet().getId());
            workSheet = workSheetRepository.getReferenceById(subWorkSheet.getWorkSheet().getId());
            workSheet.setUnqualifiedNumber(workSheet.getUnqualifiedNumber()-subWorkSheet.getUnqualifiedNumber()).setQualifiedNumber(workSheet.getQualifiedNumber()-subWorkSheet.getQualifiedNumber());
            subWorkSheet.setQualifiedNumber(lastBatchWorkDetail.getQualifiedNumber()).setUnqualifiedNumber(subWorkSheet.getNumber()-subWorkSheet.getQualifiedNumber());
            subWorkSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName())
                    .setActualEndDate(LocalDateTime.now());
            workSheet.setUnqualifiedNumber(workSheet.getUnqualifiedNumber()+subWorkSheet.getUnqualifiedNumber()).setQualifiedNumber(workSheet.getQualifiedNumber()+subWorkSheet.getQualifiedNumber());
            if(workSheet.getQualifiedNumber()+workSheet.getUnqualifiedNumber()==workSheet.getNumber()){
                workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName())
                        .setActualEndDate(LocalDateTime.now());
            }
            //存在待检任务 则工单子工单不完成
            if (rworkerStepProcessBaseDTO.getInspectTask()){
                subWorkSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName())
                        .setActualEndDate(null);
                workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName())
                        .setActualEndDate(null);
            }
            subWorkSheetRepository.save(subWorkSheet);
            //更新工单的子工单个数以及完成个数(包括正常、异常)
            subWorkSheetService.updateWorkSheetSubWsNumberInfo(workSheet);
            //更新在制看板数据
            workSheetStepStatisticsServices[0].deleteWorkSheetStepStatisticsInfo(workSheet, List.of(subWorkSheet), rworkerStepProcessBaseDTO.getSubWsProductionMode());
            realFinishedNumber = subWorkSheet.getNumber();
        } else {
            workSheet = workSheetRepository.getReferenceById(workSheet.getId());
            workSheet.setQualifiedNumber(lastBatchWorkDetail.getQualifiedNumber()).setUnqualifiedNumber(workSheet.getNumber() - workSheet.getQualifiedNumber());
            workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName())
                    .setActualEndDate(LocalDateTime.now());
            //存在待检任务 则工单子工单不完成
            if (rworkerStepProcessBaseDTO.getInspectTask()){
                workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName())
                        .setActualEndDate(null);
            }
            realFinishedNumber = workSheet.getNumber();
            //更新在制看板数据
            workSheetStepStatisticsServices[0].deleteWorkSheetStepStatisticsInfo(workSheet, null, rworkerStepProcessBaseDTO.getSubWsProductionMode());
        }
        if (!batchProcessSaveServices[0].existReworkSheetNotFinished(rworkerStepProcessBaseDTO, workSheet) && !rworkerStepProcessBaseDTO.getInspectTask()) {
            workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName()).setActualEndDate(LocalDateTime.now());
        }
        workSheetRepository.save(workSheet);
        //工单类型为返工单时需要更新对应正常单返修合格数、合格数及不合格数数据
        batchProcessSaveServices[0].calculateBatchAfterOnlineReworkSheetFinished(workSheet, workSheet.getQualifiedNumber());
        //正常单需要更新销售订单的完成数量
        batchProcessSaveServices[0].calculateBatchSaleOrderAfterAllStepFinished(workSheet, realFinishedNumber);
        //工单完成-更新工单投料单中的倒冲物料库存-以及线边台账
        if (workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName()) {
            materialServices[0].backFlushMaterialClearInventoryWsFinished(workSheet);
        }
    }


    /**
     * 更新生产计划表 工序组粒度
     *
     * @param rworkerSnStepSaveRequestDTO 保存SN工序生产数据参数
     * @param workSheet                   工单
     * @param step                        工序
     */
    private void updateStepGroupProductionPlan(RworkerSnStepSaveRequestDTO rworkerSnStepSaveRequestDTO, WorkSheet workSheet, Step step) {
        //更新生产计划表 工序组粒度
        Long pedigreeId = Optional.ofNullable(workSheet).map(WorkSheet::getPedigree).map(AuditSfIdEntity::getId).orElse(null);
        Long workLineId = Optional.ofNullable(workSheet).map(WorkSheet::getWorkLine).map(AuditSfIdEntity::getId).orElse(null);
        Long stepGroupId = Optional.ofNullable(step).map(Step::getStepGroup).map(AuditSfIdEntity::getId).orElse(null);
        if (Objects.nonNull(pedigreeId) && Objects.nonNull(stepGroupId)) {
            productionPlanServices[0].updateStepGroupIdActualNumber(pedigreeId, stepGroupId, workLineId, rworkerSnStepSaveRequestDTO.getQualifiedNumber(), OperationEnum.ADD);
        }
    }

    /**
     * 获取生产工单定制工序
     *
     * @param subWorkSheet 子工单
     * @param workSheet    工单
     */
    private List<WsStep> getWsSteps(SubWorkSheet subWorkSheet, WorkSheet workSheet) {
        List<WsStep> wsStepList = null != subWorkSheet.getId() ? wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO) : wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(wsStepList)) {
            wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        }
        return wsStepList;
    }

    /**
     * 验证SN个数
     *
     * @param rworkerSnStepSaveRequestDTO 下交参数
     */
    private void validate(RworkerSnStepSaveRequestDTO rworkerSnStepSaveRequestDTO) {
        if (!CollectionUtils.isEmpty(rworkerSnStepSaveRequestDTO.getSingleSnSaveInfoList()) && rworkerSnStepSaveRequestDTO.getSingleSnSaveInfoList().stream().map(RworkerSnStepSaveRequestDTO.SingleSnSaveInfo::getSn).collect(Collectors.toSet()).size() != rworkerSnStepSaveRequestDTO.getSingleSnSaveInfoList().size()) {
            throw new ResponseException("error.repeatSnNotAccept", "下交SN不可重复");
        }
        if (!CollectionUtils.isEmpty(rworkerSnStepSaveRequestDTO.getSingleSnSaveInfoList()) && rworkerSnStepSaveRequestDTO.getNumber() != rworkerSnStepSaveRequestDTO.getSingleSnSaveInfoList().size()) {
            throw new ResponseException("error.batchSnNumberNotValid", "SN个数与下交总数不一致");
        }
        if (!CollectionUtils.isEmpty(rworkerSnStepSaveRequestDTO.getContainerSnSaveInfoList()) && rworkerSnStepSaveRequestDTO.getContainerSnSaveInfoList().stream().mapToInt(RworkerSnStepSaveRequestDTO.ContainerSnSaveInfo::getNumber).sum() != rworkerSnStepSaveRequestDTO.getNumber()) {
            throw new ResponseException("error.containerProductNumberNotValid", "容器下交投产数量与下交投产总数不一致");
        }
        if (!CollectionUtils.isEmpty(rworkerSnStepSaveRequestDTO.getContainerSnSaveInfoList())) {
            Set<RworkerSnStepSaveRequestDTO.SingleSnSaveInfo> singleSnSaveInfoList = new HashSet<>();
            rworkerSnStepSaveRequestDTO.getContainerSnSaveInfoList().forEach(containerSnSaveInfo ->
                    validSingleSnSaveInfo(singleSnSaveInfoList, containerSnSaveInfo)
            );
            if (singleSnSaveInfoList.stream().map(RworkerSnStepSaveRequestDTO.SingleSnSaveInfo::getSn).collect(Collectors.toSet()).size() != rworkerSnStepSaveRequestDTO.getSingleSnSaveInfoList().size()) {
                throw new ResponseException("error.repeatSnNotAccept", "下交SN不可重复");
            }
            rworkerSnStepSaveRequestDTO.getContainerSnSaveInfoList().forEach(containerSnSaveInfo -> {
                if (containerSnSaveInfo.getSingleSnSaveInfoList().size() != containerSnSaveInfo.getNumber()) {
                    throw new ResponseException("error.containerSnNumberNotValid", "SN个数与容器下交总数不一致");
                }
            });
        }
    }

    /**
     * 验证SN个数
     *
     * @param singleSnSaveInfoList 单支SN生产信息
     * @param containerSnSaveInfo  容器SN生产信息
     */
    private void validSingleSnSaveInfo(Set<RworkerSnStepSaveRequestDTO.SingleSnSaveInfo> singleSnSaveInfoList, RworkerSnStepSaveRequestDTO.ContainerSnSaveInfo containerSnSaveInfo) {
        singleSnSaveInfoList.addAll(containerSnSaveInfo.getSingleSnSaveInfoList());
        //验证各个容器内物料汇总数据
        if (!CollectionUtils.isEmpty(containerSnSaveInfo.getMaterialInfoList())) {
            List<MaterialSaveInfo> singleSnMaterialSaveInfoList = Lists.newArrayList();
            containerSnSaveInfo.getSingleSnSaveInfoList().forEach(singleSnSaveInfo -> singleSnMaterialSaveInfoList.addAll(singleSnSaveInfo.getMaterialInfoList()));
            if (CollectionUtils.isEmpty(singleSnMaterialSaveInfoList)) {
                throw new ResponseException(CONTAINER_MATERIAL_NOT_MATCHED_SN_MATERIAL, ERR_MSG);
            }
            containerSnSaveInfo.getMaterialInfoList().forEach(materialSaveInfo -> {
                if (StringUtils.isNotBlank(materialSaveInfo.getBatch())) {
                    List<MaterialSaveInfo> matchedSnMaterialSaveInfoList = singleSnMaterialSaveInfoList.stream().filter(materialSaveInfoTemp -> materialSaveInfoTemp.getId().equals(materialSaveInfo.getId()) && materialSaveInfoTemp.getBatch().equals(materialSaveInfo.getBatch())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(matchedSnMaterialSaveInfoList)) {
                        throw new ResponseException(CONTAINER_MATERIAL_NOT_MATCHED_SN_MATERIAL, ERR_MSG);
                    }
                    if (matchedSnMaterialSaveInfoList.stream().mapToDouble(MaterialSaveInfo::getNumber).sum() != materialSaveInfo.getNumber()) {
                        throw new ResponseException(CONTAINER_MATERIAL_NOT_MATCHED_SN_MATERIAL, ERR_MSG);
                    }
                }
            });
        }
    }

    /**
     * 保存SN生产数据及关联数据
     *
     * @param batchWorkDetail             批量工序生产详情
     * @param containerDetail             容器生产详情
     * @param singleSnSaveInfo            单支下交工序通用信息
     * @param rworkerSnStepSaveRequestDTO 单支下交请求参数
     * @param rworkerStepProcessBaseDTO   工序下交通用参数
     * @param singleSnOnlineRepair        是否单支在线返工
     */
    @Override
    public void createSnWorkDetail(BatchWorkDetail batchWorkDetail, ContainerDetail containerDetail, RworkerSnStepSaveRequestDTO.SingleSnSaveInfo singleSnSaveInfo,
                                   RworkerSnStepSaveRequestDTO rworkerSnStepSaveRequestDTO,
                                   RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO, Boolean singleSnOnlineRepair) {
        boolean subWsProductionMode = rworkerStepProcessBaseDTO.getSubWsProductionMode();
        SubWorkSheet subWorkSheet = rworkerStepProcessBaseDTO.getSubWorkSheet();
        WorkSheet workSheet = rworkerStepProcessBaseDTO.getWorkSheet();
        Step step = rworkerStepProcessBaseDTO.getStep();
        WsStep wsStep = rworkerStepProcessBaseDTO.getWsStep();
        List<SnWorkDetail> latestSnWorkDetailList = rworkerStepProcessBaseDTO.getSnStepLatestWorkDetailList();
        SnWorkStatus snWorkStatus = snWorkStatusRepository.findBySnAndDeleted(singleSnSaveInfo.getSn(),Constants.LONG_ZERO).orElse(null);
        SnWorkDetail snWorkDetail = null;
        int reworkTime = null != snWorkStatus ? snWorkStatus.getReworkTime() : Constants.INT_ZERO;
        if (!CollectionUtils.isEmpty(latestSnWorkDetailList)) {
            snWorkDetail = latestSnWorkDetailList.stream().filter(latestSnWorkDetail -> latestSnWorkDetail.getSn().equals(singleSnSaveInfo.getSn())
                    && latestSnWorkDetail.getReworkTime() == reworkTime
                    && latestSnWorkDetail.getStep().getId().equals(step.getId())
            ).findFirst().orElse(null);
        }
        if (null != snWorkDetail) {
            throw new ResponseException("error.snStepIsFinished", singleSnSaveInfo.getSn() + "工序已完成");
        }
        UnqualifiedItem unqualifiedItem = null != singleSnSaveInfo.getUnqualifiedItemSaveInfo() ? unqualifiedItemRepository.getReferenceById(singleSnSaveInfo.getUnqualifiedItemSaveInfo().getId()) : null;
        //更新SN工作详情
        snWorkDetail = new SnWorkDetail();
        snWorkDetail.setEndDate(LocalDateTime.now())
                .setOperatorId(rworkerStepProcessBaseDTO.getStaffDTO().getId())
                .setStartDate(rworkerSnStepSaveRequestDTO.getStartTime())
                .setEndDate(LocalDateTime.now())
                .setResult(singleSnSaveInfo.getQualifiedNumber())
                .setSn(singleSnSaveInfo.getSn())
                .setWorkSheet(subWsProductionMode ? null : workSheet)
                .setSubWorkSheet(subWsProductionMode ? subWorkSheet : null)
                .setWorkCell(rworkerStepProcessBaseDTO.getWorkCell())
                .setStep(step)
                .setYsn(StringUtils.isBlank(singleSnSaveInfo.getYsn()) ? null : singleSnSaveInfo.getYsn())
                .setContainerDetail(containerDetail)
                .setUnqualifiedItem(unqualifiedItem)
                .setReworkTime(reworkTime)
                .setWorkHour(ChronoUnit.MINUTES.between(rworkerSnStepSaveRequestDTO.getStartTime(), LocalDateTime.now()))
                .setDeleted(Constants.LONG_ZERO);
        if (Objects.nonNull(singleSnSaveInfo.getStepDynamicDataColumnGetDto())) {
            StepDynamicDataColumnGetDTO stepDynamicDataColumnGetDTO = singleSnSaveInfo.getStepDynamicDataColumnGetDto();
            //补充工序编码和工序名称
            stepDynamicDataColumnGetDTO.setStepName(snWorkDetail.getStep().getName()).setStepCode(snWorkDetail.getStep().getCode());
            snWorkDetail.setStepDynamicDataColumnGetDTO(stepDynamicDataColumnGetDTO);
        }
        snWorkDetailRepository.save(snWorkDetail);
        //更新SN最新生产状态
        if (snWorkStatus == null) {
            snWorkStatus = new SnWorkStatus();
            snWorkStatus.setSn(singleSnSaveInfo.getSn())
                    .setStartDate(LocalDateTime.now())
                    .setReworkTime(Constants.INT_ZERO)
                    .setDeleted(Constants.LONG_ZERO);
        }
        if (StringUtils.isBlank(snWorkStatus.getYsn()) && StringUtils.isNotBlank(singleSnSaveInfo.getYsn())) {
            snWorkStatus.setYsn(singleSnSaveInfo.getYsn());
        }
        //设置初始SN生产状态，后面还会根据不良项目处理类型进一步更新SN生产状态
        if (StringUtils.isNotBlank(wsStep.getAfterStepId())) {
            if (workSheet.getCategory() <= WsEnum.OFFLINE_RE_WS.getCategory() || singleSnOnlineRepair) {
                snWorkStatus.setStatus(SnWorkStatusEnum.IN_THE_REPAIR.getStatus());
            } else {
                snWorkStatus.setStatus(SnWorkStatusEnum.PUT_INTO_PRODUCTION.getStatus());
            }
        } else {
            snWorkStatus.setStatus(SnWorkStatusEnum.QUALIFIED.getStatus());
        }
        snWorkStatus.setSubWorkSheet(subWsProductionMode ? subWorkSheet : null)
                .setWorkSheet(workSheet)
                .setWorkFlow(rworkerStepProcessBaseDTO.getWorkFlow())
                .setReworkTime(snWorkDetail.getResult() != Constants.INT_ONE ? reworkTime + Constants.INT_ONE : reworkTime)
                .setLatestSnWorkDetail(snWorkDetail);
        updateSnWorkStatus(rworkerStepProcessBaseDTO, snWorkStatus, snWorkDetail, unqualifiedItem, rworkerSnStepSaveRequestDTO.getReInspect(), singleSnOnlineRepair);
        //保存SN详情关联的追溯数据(物料批次、设备、不良、易损件等)
        this.updateSnWorkDetailRelationInfo(snWorkDetail, batchWorkDetail, containerDetail, singleSnSaveInfo, rworkerSnStepSaveRequestDTO);
        //保存员工产量
        this.saveSnStaffPerform(batchWorkDetail, containerDetail, snWorkDetail, LocalDate.now());
        if (snWorkStatus.getStatus() == SnWorkStatusEnum.STEP_REINSPECT.getStatus()) {
            qualityServices[0].saveSnProcessReinspectInfo(snWorkStatus, rworkerStepProcessBaseDTO);
        }
        //保存可能存在的维修分析数据
        if (snWorkStatus.getStatus() == SnWorkStatusEnum.MAINTAIN.getStatus()) {
            qualityServices[0].saveSnProcessMaintainInfo(snWorkStatus);
        }
        //保存可能的烘烤温循数据
        if (wsStep.getCategory() >= StepCategoryEnum.PUT_IN_BAKE_STEP.getStatus() && wsStep.getCategory() <= StepCategoryEnum.PULL_OUT_AGEING_STEP.getStatus()) {
            Boolean isPutOutStep = wsStep.getCategory() % Constants.INT_TWO == Constants.INT_ZERO;
            BakeCycleBakeAgeingSaveRequestDTO bakeCycleBakeAgeingSaveRequestDTO = new BakeCycleBakeAgeingSaveRequestDTO(singleSnSaveInfo, rworkerStepProcessBaseDTO, (isPutOutStep && snWorkDetail.getResult() == Constants.INT_ZERO) ? reworkTime : snWorkStatus.getReworkTime());
            //如果当前工序是取出的话，则找到上面最近一个放入工序
            if (isPutOutStep) {
                if (StringUtils.isBlank(wsStep.getPreStepId())) {
                    throw new ResponseException("error.BatchWorkDetailNull", "未检测放入工序记录");
                }
                WsStep latestWsStep = rworkerStepProcessBaseDTO.getWsStepList().stream().filter(wsStep1 -> wsStep.getPreStepId().contains(String.valueOf(wsStep1.getStep().getId())) && wsStep1.getCategory() == wsStep.getCategory() - 1).findFirst().orElse(null);
                if (Objects.isNull(latestWsStep)) {
                    throw new ResponseException("error.BatchWorkDetailNull", "未检测放入工序记录");
                }
                bakeCycleBakeAgeingSaveRequestDTO.setPutInStepId(latestWsStep.getStep().getId());
            }
            bakeCycleBakeAgeingModelServices[0].saveBakeCycleBakeAgeingHistoryInfo(bakeCycleBakeAgeingSaveRequestDTO);
        }
    }

    /**
     * 保存单支生产状态
     *
     * @param rworkerStepProcessBaseDTO 工序下交通用信息
     * @param snWorkStatus              单支生产状态
     * @param snWorkDetail              单支生产详情
     * @param unqualifiedItem           不良项目
     * @param stepReinspect             是否复检
     * @param singleSnOnlineRepair      是否为单支在线返
     */
    private void updateSnWorkStatus(RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO, SnWorkStatus snWorkStatus,
                                    SnWorkDetail snWorkDetail, UnqualifiedItem unqualifiedItem, boolean stepReinspect, boolean singleSnOnlineRepair) {
        WorkSheet workSheet = rworkerStepProcessBaseDTO.getWorkSheet();
        SubWorkSheet subWorkSheet = rworkerStepProcessBaseDTO.getSubWorkSheet();
        //如果当前SN工作详情不合格且不良项目不为空时需要更新SN生产状态
        if (snWorkDetail.getResult() == Constants.INT_ZERO && null != unqualifiedItem) {
            snWorkStatus.setLatestUnqualifiedItem(unqualifiedItem).setLatestReworkSnWorkDetail(snWorkDetail).setIsUpdateBatchWorkDetail(Boolean.TRUE);
            //不良复检需将状态改为复检状态
            if (stepReinspect && FuncKeyUtil.checkApi("QReinspection")) {
                snWorkStatus.setStatus(SnWorkStatusEnum.STEP_REINSPECT.getStatus());
                //更新工单、子工单不合格数量
                updateWorkSheetUnqualifiedNumber(subWorkSheet, workSheet, singleSnOnlineRepair);
            } else if (unqualifiedItem.getDealWay() == ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_SCRAP.getCategoryName()) {
                //不良项目处理方式为报废时则状态改为报废
                snWorkStatus.setStatus(SnWorkStatusEnum.SCRAP.getStatus());
            } else if (unqualifiedItem.getDealWay() == ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_MAINTAIN_ANALYSE.getCategoryName()) {
                //不良项目处理方式为维修分析时则状态改为维修分析
                snWorkStatus.setStatus(SnWorkStatusEnum.MAINTAIN.getStatus());
                //更新工单、子工单不合格数量
                updateWorkSheetUnqualifiedNumber(subWorkSheet, workSheet, singleSnOnlineRepair);
            } else if (unqualifiedItem.getDealWay() == ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_ONLINE_REWORK.getCategoryName()) {
                //处理在线返修类型的SN不良
                this.dealWithUnqualifiedSnWhenOnlineRework(rworkerStepProcessBaseDTO, snWorkStatus, singleSnOnlineRepair);
            } else if (unqualifiedItem.getDealWay() == ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_ONLINE_ADJUSTMENT.getCategoryName()) {
                //处理在线调整类型的SN不良
                this.dealWithUnqualifiedSnWhenOnlineAdjustment(rworkerStepProcessBaseDTO, snWorkStatus);
            }
        }
        if (snWorkStatus.getStatus() == SnWorkStatusEnum.SCRAP.getStatus() || snWorkStatus.getStatus() == SnWorkStatusEnum.QUALIFIED.getStatus()) {
            snWorkStatus.setEndDate(LocalDateTime.now()).setIsUpdateBatchWorkDetail(Boolean.TRUE);
        }
        //SN合格完成最后一个工序时更新子工单和工单的合格数
        if (snWorkStatus.getStatus() == SnWorkStatusEnum.QUALIFIED.getStatus()) {
            updateWorkSheetQualifiedNumber(subWorkSheet, workSheet, singleSnOnlineRepair);
            //更新生产计划表 生产线
            updateProductionPlanAndWorkSheetStatistics(subWorkSheet, workSheet);
        }

        if (snWorkStatus.getStatus() == SnWorkStatusEnum.SCRAP.getStatus()) {
            //更新工单、子工单不合格数量
            updateWorkSheetUnqualifiedNumber(subWorkSheet, workSheet, singleSnOnlineRepair);
            // 更新工单统计表
            if (Objects.nonNull(workSheet) && null != workSheet.getId()) {
                workSheetStatisticsServices[0].updateWorkSheetNumber(workSheet.getId(), LocalDate.now(), Constants.INT_ZERO, Constants.INT_ONE, OperationEnum.ADD);
            }
            if (Objects.nonNull(subWorkSheet) && null != subWorkSheet.getId()) {
                workSheetStatisticsServices[0].updateWorkSheetNumber(subWorkSheet.getWorkSheet().getId(), LocalDate.now(), Constants.INT_ZERO, Constants.INT_ONE, OperationEnum.ADD);
            }
        }
        snWorkStatusRepository.save(snWorkStatus);
    }

    /**
     * 不良项目处理类型为在线返修时的处理逻辑
     *
     * @param rworkerStepProcessBaseDTO 工序下交通用信息
     * @param snWorkStatus              sn生产状态
     */
    private void dealWithUnqualifiedSnWhenOnlineRework(RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO, SnWorkStatus snWorkStatus, boolean singleSnOnlineRepair) {
        //若不良项目没有不良组别则sn生产状态改为报废
        if (null == snWorkStatus.getLatestUnqualifiedItem().getUnqualifiedGroup()) {
            snWorkStatus.setStatus(SnWorkStatusEnum.SCRAP.getStatus());
            return;
        }
        //若没有配置单支不良在线返工规则则也认为报废
        WorkFlow reWorkFlow = commonService.findPedigreeReworkWorkFlow(rworkerStepProcessBaseDTO.getWorkSheet().getPedigree(), snWorkStatus.getLatestUnqualifiedItem().getUnqualifiedGroup().getId(), rworkerStepProcessBaseDTO.getWorkSheet().getClientId());
        if (null == reWorkFlow) {
            snWorkStatus.setStatus(SnWorkStatusEnum.SCRAP.getStatus());
            return;
        }
        snWorkStatus.setWorkFlow(reWorkFlow).setStatus(SnWorkStatusEnum.IN_THE_REPAIR.getStatus()).setReworkStartDate(null == snWorkStatus.getReworkStartDate() ? LocalDateTime.now() : snWorkStatus.getReworkStartDate());
        //更新工单、子工单不合格数量
        updateWorkSheetUnqualifiedNumber(rworkerStepProcessBaseDTO.getSubWorkSheet(), rworkerStepProcessBaseDTO.getWorkSheet(), singleSnOnlineRepair);
    }

    /**
     * 不良项目处理类型为在线调整时的处理逻辑
     *
     * @param rworkerStepProcessBaseDTO 工序下交通用信息
     * @param snWorkStatus              SN生产状态
     */
    private void dealWithUnqualifiedSnWhenOnlineAdjustment(RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO, SnWorkStatus snWorkStatus) {
        List<OnlineReworkRule> onlineReworkRuleList = onlineReworkRuleRepository.findByUnqualifiedItemIdAndDeleted(snWorkStatus.getLatestUnqualifiedItem().getId(), Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(onlineReworkRuleList) || StringUtils.isEmpty(rworkerStepProcessBaseDTO.getWsStep().getAfterStepId())) {
            snWorkStatus.setStatus(SnWorkStatusEnum.SCRAP.getStatus());
            return;
        }
        List<WsStep> nextWsStepList = rworkerStepProcessBaseDTO.getWsStepList().stream().filter(wsStep -> StringUtils.isNotBlank(wsStep.getPreStepId()) && wsStep.getPreStepId().contains(rworkerStepProcessBaseDTO.getStep().getId().toString())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nextWsStepList)) {
            snWorkStatus.setStatus(SnWorkStatusEnum.SCRAP.getStatus());
            return;
        }
        //下个工序请求模式为单支但是下工序没有调整对应不良的规则则认为报废
        if (nextWsStepList.stream().anyMatch(wsStep -> wsStep.getRequestMode() == ConstantsEnum.SN_REQUEST_MODE.getCategoryName()) && onlineReworkRuleList.stream().noneMatch(onlineReworkRule -> rworkerStepProcessBaseDTO.getWsStep().getAfterStepId().contains(onlineReworkRule.getStep().getId().toString()))) {
            snWorkStatus.setStatus(SnWorkStatusEnum.SCRAP.getStatus());
            return;
        }
        //若下工序请求模式为非单支则判断后续所有工序中是否存在对应调整该不良的工序，若不存在则认为报废
        List<WsStep> childWsStepList = Lists.newArrayList();
        commonService.findChildWsStep(rworkerStepProcessBaseDTO.getWsStepList(), childWsStepList, rworkerStepProcessBaseDTO.getWsStep());
        if (onlineReworkRuleList.stream().noneMatch(onlineReworkRule -> childWsStepList.stream().anyMatch(wsStep -> onlineReworkRule.getStep().getId().equals(wsStep.getStep().getId())))) {
            snWorkStatus.setStatus(SnWorkStatusEnum.SCRAP.getStatus());
            return;
        }
        snWorkStatus.setReworkTime(snWorkStatus.getReworkTime() - Constants.INT_ONE);
    }

    /**
     * 更新产线粒度生产计划表和工单统计表
     *
     * @param subWorkSheet 子工单
     * @param workSheet    工单
     */
    private void updateProductionPlanAndWorkSheetStatistics(SubWorkSheet subWorkSheet, WorkSheet workSheet) {
        // 产品谱系id
        Long pedigreeId = null;
        // 产线id
        Long workLineId = null;
        if (Objects.nonNull(workSheet)) {
            pedigreeId = Optional.ofNullable(workSheet).map(WorkSheet::getPedigree).map(AuditSfIdEntity::getId).orElse(null);
            workLineId = Optional.ofNullable(workSheet).map(WorkSheet::getWorkLine).map(AuditSfIdEntity::getId).orElse(null);
        }
        if (Objects.nonNull(subWorkSheet)) {
            workSheet = Optional.ofNullable(subWorkSheet).map(SubWorkSheet::getWorkSheet).orElse(null);
            pedigreeId = Optional.ofNullable(workSheet).map(WorkSheet::getPedigree).map(AuditSfIdEntity::getId).orElse(null);
            workLineId = Optional.ofNullable(workSheet).map(WorkSheet::getWorkLine).map(AuditSfIdEntity::getId).orElse(null);
        }
        if (Objects.nonNull(pedigreeId) && Objects.nonNull(workLineId)) {
            productionPlanServices[0].updateWorkLineActualNumber(pedigreeId, workLineId, Constants.INT_ONE, OperationEnum.ADD);
        }
        // 更新工单统计表
        if (Objects.nonNull(workSheet)) {
            workSheetStatisticsServices[0].updateWorkSheetNumber(workSheet.getId(), LocalDate.now(), Constants.INT_ONE, Constants.INT_ZERO, OperationEnum.ADD);
        }
    }

    /**
     * 更新工单、子工单不合格数量
     *
     * @param subWorkSheet         子工单
     * @param workSheet            工单
     * @param singleSnOnlineRepair 是否单支在线返工
     */
    private void updateWorkSheetUnqualifiedNumber(SubWorkSheet subWorkSheet, WorkSheet workSheet, boolean singleSnOnlineRepair) {
        if (null != subWorkSheet && null != subWorkSheet.getId() && !singleSnOnlineRepair) {
            subWorkSheet.setUnqualifiedNumber(subWorkSheet.getUnqualifiedNumber() + Constants.INT_ONE);
            if (subWorkSheet.getUnqualifiedNumber() + subWorkSheet.getQualifiedNumber() == subWorkSheet.getNumber()) {
                subWorkSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName());
                subWorkSheet.setActualEndDate(LocalDateTime.now());
            }
            subWorkSheetRepository.save(subWorkSheet);
        }
        if (null != workSheet && null != workSheet.getId() && !singleSnOnlineRepair) {
            workSheet.setUnqualifiedNumber(workSheet.getUnqualifiedNumber() + Constants.INT_ONE);
            if (workSheet.getUnqualifiedNumber() + workSheet.getQualifiedNumber() == workSheet.getNumber()) {
                workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName());
                workSheet.setActualEndDate(LocalDateTime.now());
            }
            workSheetRepository.save(workSheet);
        }
    }

    /**
     * 更新工单、子工单合格数量
     *
     * @param subWorkSheet         子工单
     * @param workSheet            工单
     * @param singleSnOnlineRepair 是否单支在线返工
     */
    private void updateWorkSheetQualifiedNumber(SubWorkSheet subWorkSheet, WorkSheet workSheet, boolean singleSnOnlineRepair) {
        //纯单支在线返工时不更新子工单的合格数
        if (null != subWorkSheet && !singleSnOnlineRepair) {
            subWorkSheet.setQualifiedNumber(subWorkSheet.getQualifiedNumber() + Constants.INT_ONE);
            if (subWorkSheet.getUnqualifiedNumber() + subWorkSheet.getQualifiedNumber() == subWorkSheet.getNumber()) {
                subWorkSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName());
                subWorkSheet.setActualEndDate(LocalDateTime.now());
            }
            subWorkSheetRepository.save(subWorkSheet);
        }
        //若纯单支在线返工时需要更新合格数、返工合格数、不合格数
        if (null != workSheet) {
            workSheet.setQualifiedNumber(workSheet.getQualifiedNumber() + Constants.INT_ONE);
            workSheet.setReworkQualifiedNumber(workSheet.getReworkQualifiedNumber() + (singleSnOnlineRepair ? Constants.INT_ONE : Constants.INT_ZERO));
            workSheet.setUnqualifiedNumber(workSheet.getUnqualifiedNumber() - (singleSnOnlineRepair ? Constants.INT_ONE : Constants.INT_ZERO));
            if (workSheet.getUnqualifiedNumber() + workSheet.getQualifiedNumber() == workSheet.getNumber()) {
                workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName());
                workSheet.setActualEndDate(LocalDateTime.now());
            }
            workSheetRepository.save(workSheet);
        }
    }

    /**
     * 保存关联数据
     *
     * @param snWorkDetail                SN工作详情
     * @param singleSnSaveInfo            SN待保存生产参数
     * @param rworkerSnStepSaveRequestDTO 请求参数
     */
    private void updateSnWorkDetailRelationInfo(SnWorkDetail snWorkDetail, BatchWorkDetail batchWorkDetail, ContainerDetail containerDetail, RworkerSnStepSaveRequestDTO.SingleSnSaveInfo singleSnSaveInfo, RworkerSnStepSaveRequestDTO rworkerSnStepSaveRequestDTO) {
        //保存SN详情物料信息
        if (!CollectionUtils.isEmpty(singleSnSaveInfo.getMaterialInfoList())) {
            materialServices[0].saveSnWorkDetailMaterialInfo(snWorkDetail, singleSnSaveInfo.getMaterialInfoList());
        }
        //保存SN详情设备信息
        if (!CollectionUtils.isEmpty(rworkerSnStepSaveRequestDTO.getFacilityIdList())) {
            facilityServices[0].saveSnWorkDetailFacilityInfo(snWorkDetail, rworkerSnStepSaveRequestDTO.getFacilityIdList());
        }
        //保存sn不良详情
        if (snWorkDetail.getResult() == Constants.INT_ZERO && !ObjectUtils.isEmpty(singleSnSaveInfo.getUnqualifiedItemSaveInfo())) {
            qualityServices[0].saveSnWorkDetailUnqualifiedItem(snWorkDetail, singleSnSaveInfo.getUnqualifiedItemSaveInfo());
        }
        //保存工序动态数据
        if (null != singleSnSaveInfo.getStepDynamicDataColumnGetDto()) {
            dynamicServices[0].saveSnWorkDetailStepDynamicDataInfo(snWorkDetail, singleSnSaveInfo.getStepDynamicDataColumnGetDto());
        }
        //保存易损件信息
        if (!CollectionUtils.isEmpty(singleSnSaveInfo.getWearingPartInfoList())) {
            wearingPartServices[0].saveSnWorkDetailWearingPart(snWorkDetail, batchWorkDetail, containerDetail, singleSnSaveInfo.getWearingPartInfoList(), rworkerSnStepSaveRequestDTO);
        }
    }

    /**
     * 保存单支员工产量数据
     *
     * @param batchWorkDetail 批量详情
     * @param containerDetail 容器详情
     * @param snWorkDetail    SN详情
     * @param workDate        记录日期
     */
    private void saveSnStaffPerform(BatchWorkDetail batchWorkDetail,
                                    ContainerDetail containerDetail, SnWorkDetail snWorkDetail, LocalDate workDate) {
        //保存员工产量信息
        StaffPerform staffPerform = new StaffPerform();
        staffPerform.setSnWorkDetailId(snWorkDetail.getId())
                .setBatchWorkDetailId(null != batchWorkDetail ? batchWorkDetail.getId() : null)
                .setContainerDetailId(null != containerDetail ? containerDetail.getId() : null)
                .setStaffId(snWorkDetail.getOperatorId())
                .setStep(snWorkDetail.getStep())
                .setSubWorkSheet(snWorkDetail.getSubWorkSheet())
                .setWorkSheet(snWorkDetail.getWorkSheet())
                .setWorkCell(snWorkDetail.getWorkCell())
                .setRecordDate(workDate)
                .setRecordTime(LocalDateTime.now())
                .setWorkHour(staffPerform.getWorkHour() + snWorkDetail.getWorkHour())
                .setInputNumber(Constants.INT_ONE)
                .setQualifiedNumber(snWorkDetail.getResult())
                .setUnqualifiedNumber(Constants.INT_ONE - snWorkDetail.getResult()).setDeleted(Constants.LONG_ZERO);
        staffPerformRepository.save(staffPerform);
        //保存员工不良明细信息
        if (snWorkDetail.getResult() == Constants.INT_ZERO && null != snWorkDetail.getUnqualifiedItem()) {
            StaffPerformUnqualifiedItem staffPerformUnqualifiedItem = new StaffPerformUnqualifiedItem();
            staffPerformUnqualifiedItem.setStaffPerform(staffPerform).setRecordDate(staffPerform.getRecordDate())
                    .setRecordTime(staffPerform.getRecordTime())
                    .setUnqualifiedItem(snWorkDetail.getUnqualifiedItem())
                    .setNumber(Constants.INT_ONE).setDeleted(Constants.LONG_ZERO);
            staffPerformUnqualifiedItemRepository.save(staffPerformUnqualifiedItem);
        }
    }

    /**
     * 保存子工单批量详情数据
     *
     * @param rworkerSnStepSaveRequestDTO SN待保存参数DTO
     * @param rworkerStepProcessBaseDTO   基础信息
     * @return net.airuima.domain.procedure.batch.BatchWorkDetail 保存完后的批量详情
     */
    private BatchWorkDetail createSubWsWorkDetail(RworkerSnStepSaveRequestDTO rworkerSnStepSaveRequestDTO, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        SubWorkSheet subWorkSheet = rworkerStepProcessBaseDTO.getSubWorkSheet();
        WorkSheet workSheet = rworkerStepProcessBaseDTO.getWorkSheet();
        WsStep wsStep = rworkerStepProcessBaseDTO.getWsStep();
        BatchWorkDetail batchWorkDetail = batchWorkDetailRepository.findByStepIdAndSubWorkSheetIdAndDeleted(wsStep.getStep().getId(), subWorkSheet.getId(), Constants.LONG_ZERO).orElse(new BatchWorkDetail());
        if (batchWorkDetail.getFinish() == ConstantsEnum.FINISH_STATUS.getCategoryName()) {
            throw new ResponseException("error.stepIsFinished", "工单工序已完成");
        }
        if (batchWorkDetail.getInputNumber() + rworkerSnStepSaveRequestDTO.getNumber() > subWorkSheet.getNumber()) {
            throw new ResponseException("error.stepInputNumberOverWorkSheetNumber", "工单工序投产数已超过工单批量");
        }
        //是否为在线调整类型工序
        boolean onlineReworkStep = wsStep.getCategory() == ConstantsEnum.STEP_ONLINE_PRE_REWORK_CATEGORY.getCategoryName() || wsStep.getCategory() == ConstantsEnum.STEP_ONLINE_REWORK_CATEGORY.getCategoryName();
        //待流转数量
        int preStepTransferNumber = Constants.NEGATIVE_ONE;
        //若为第一个工序则直接设置前置工序完成且待投产总数为子工单数量
        preStepTransferNumber = getPreStepTransferNumber(subWorkSheet, wsStep, onlineReworkStep, preStepTransferNumber,rworkerStepProcessBaseDTO);
        //记录子工单和工单的实际开工时间以及更改工单状态为投产中
        if (null == subWorkSheet.getActualStartDate()) {
            subWorkSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName()).setActualStartDate(LocalDateTime.now());
            subWorkSheetRepository.save(subWorkSheet);
        }
        if (null == workSheet.getActualStartDate()) {
            workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName()).setActualStartDate(LocalDateTime.now());
            workSheetRepository.save(workSheet);
        }
        //更新批量详情数据
        BatchWorkDetail saveBatchWorkDetail = batchProcessSaveServices[0].saveBatchWorkDetail(MapperUtils.map(rworkerSnStepSaveRequestDTO, RworkerBatchStepSaveRequestDTO.class), rworkerStepProcessBaseDTO, batchWorkDetail, preStepTransferNumber, preStepTransferNumber, onlineReworkStep);
        BatchProcessSaveServiceImpl batchProcessSaveService = BeanUtil.getBean(BatchProcessSaveServiceImpl.class);
        //保存批量详情关联的追溯数据(物料批次、设备不良等)
        RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDto = MapperUtils.map(rworkerSnStepSaveRequestDTO, RworkerBatchStepSaveRequestDTO.class);
        rworkerBatchStepSaveRequestDto.setUnqualifiedItemInfoList(rworkerSnStepSaveRequestDTO.getUnqualifiedItemSaveInfoList());
        batchProcessSaveService.updateBatchWorkDetailRelationInfoBase(saveBatchWorkDetail, rworkerBatchStepSaveRequestDto, rworkerStepProcessBaseDTO);
        return saveBatchWorkDetail;
    }

    /**
     * 获取待流转数量
     *
     * @param subWorkSheet          子工单
     * @param wsStep                生产工单定制工序
     * @param onlineReworkStep      是否为在线调整类型工序
     * @param preStepTransferNumber 待流转数量
     * @return int 待流转数量
     */
    private int getPreStepTransferNumber(SubWorkSheet subWorkSheet, WsStep wsStep, boolean onlineReworkStep, int preStepTransferNumber,RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        //是否前置工序都已完成
        boolean allPreStepFinished;
        if (StringUtils.isBlank(wsStep.getPreStepId())) {
            preStepTransferNumber = subWorkSheet.getNumber();
        } else {
            //若当前投产工序存在前置工序则需要判断所有前置是否完成，若都已完成则获取当前工序待投产总数
            List<Long> preStepIdList = Arrays.stream(wsStep.getPreStepId().split(net.airuima.rbase.constant.Constants.STR_COMMA)).map(Long::valueOf).collect(Collectors.toList());
            List<BatchWorkDetail> preFinishedBatchWorkDetailList = batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdInAndFinishAndDeleted(subWorkSheet.getId(), preStepIdList, ConstantsEnum.FINISH_STATUS.getCategoryName(), Constants.LONG_ZERO);
            allPreStepFinished = ValidateUtils.isValid(preFinishedBatchWorkDetailList) && preFinishedBatchWorkDetailList.size() == preStepIdList.size();
            if (allPreStepFinished) {
                if (onlineReworkStep) {
                    preStepTransferNumber = batchProcessRequestServices[0].findOnlineReworkStepTransferNumber(subWorkSheet.getId(), wsStep.getStep().getId(), Boolean.TRUE,rworkerStepProcessBaseDTO);
                } else {
                    preStepTransferNumber = StringUtils.isBlank(wsStep.getPreStepId()) ? subWorkSheet.getNumber() : preFinishedBatchWorkDetailList.stream().mapToInt(BatchWorkDetail::getTransferNumber).min().getAsInt();
                }
                rworkerStepProcessBaseDTO.setRealPreStepTransferNumber(StringUtils.isBlank(wsStep.getPreStepId()) ? subWorkSheet.getNumber() : preFinishedBatchWorkDetailList.stream().mapToInt(BatchWorkDetail::getTransferNumber).min().getAsInt());
            }
        }
        return preStepTransferNumber;
    }


    /**
     * 保存工单批量详情数据
     *
     * @param rworkerSnStepSaveRequestDTO SN待保存参数DTO
     * @param rworkerStepProcessBaseDTO   基础信息
     * @return net.airuima.domain.procedure.batch.BatchWorkDetail 保存完后的批量详情
     */
    private BatchWorkDetail createWsWorkDetail(RworkerSnStepSaveRequestDTO rworkerSnStepSaveRequestDTO, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        WorkSheet workSheet = rworkerStepProcessBaseDTO.getWorkSheet();
        WsStep wsStep = rworkerStepProcessBaseDTO.getWsStep();
        BatchWorkDetail batchWorkDetail = batchWorkDetailRepository.findByStepIdAndWorkSheetIdAndDeleted(wsStep.getStep().getId(), workSheet.getId(), Constants.LONG_ZERO).orElse(new BatchWorkDetail());
        //验证批量详情数据
        if (batchWorkDetail.getFinish() == ConstantsEnum.FINISH_STATUS.getCategoryName()) {
            throw new ResponseException("error.stepIsFinished", "工单工序已完成");
        }
        if (batchWorkDetail.getInputNumber() + rworkerSnStepSaveRequestDTO.getNumber() > workSheet.getNumber()) {
            throw new ResponseException("error.stepInputNumberOverWorkSheetNumber", "工单工序投产数已超过工单批量");
        }
        //是否为在线调整类型工序
        boolean onlineReworkStep = wsStep.getCategory() == ConstantsEnum.STEP_ONLINE_PRE_REWORK_CATEGORY.getCategoryName() || wsStep.getCategory() == ConstantsEnum.STEP_ONLINE_REWORK_CATEGORY.getCategoryName();
        //待流转数量
        int preStepTransferNumber = Constants.NEGATIVE_ONE;
        //前置工序合格数
        int preStepQualifiedNumber = Constants.INT_ZERO;
        //若为第一个工序则直接设置前置工序完成且待投产总数为子工单数量
        preStepTransferNumber = getWorkSheetPreStepTransferNumber(workSheet, wsStep, onlineReworkStep, preStepTransferNumber,rworkerStepProcessBaseDTO);
        List<Long> preStepIdList = StringUtils.isNotBlank(wsStep.getPreStepId()) ? Arrays.stream(wsStep.getPreStepId().split(net.airuima.rbase.constant.Constants.STR_COMMA)).map(Long::valueOf).collect(Collectors.toList()) : Lists.newArrayList();
        if (!CollectionUtils.isEmpty(preStepIdList)) {
            List<BatchWorkDetail> preBatchWorkDetailList = batchWorkDetailRepository.findByWorkSheetIdAndStepIdInAndDeleted(workSheet.getId(), preStepIdList, Constants.LONG_ZERO);
            preStepQualifiedNumber = preBatchWorkDetailList.stream().mapToInt(BatchWorkDetail::getTransferNumber).min().getAsInt();
            ;
        }
        //记录工单的实际开工时间以及更改工单状态为投产中
        if (null == workSheet.getActualStartDate()) {
            workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName()).setActualStartDate(LocalDateTime.now());
            workSheetRepository.save(workSheet);
        }
        //更新批量详情数据
        BatchWorkDetail saveBatchWorkDetail = batchProcessSaveServices[0].saveBatchWorkDetail(MapperUtils.map(rworkerSnStepSaveRequestDTO, RworkerBatchStepSaveRequestDTO.class), rworkerStepProcessBaseDTO, batchWorkDetail, preStepTransferNumber, preStepQualifiedNumber, onlineReworkStep);
        BatchProcessSaveServiceImpl batchProcessSaveService = BeanUtil.getBean(BatchProcessSaveServiceImpl.class);
        //保存批量详情关联的追溯数据(物料批次、设备不良等)
        RworkerBatchStepSaveRequestDTO rworkerBatchStepSaveRequestDto = MapperUtils.map(rworkerSnStepSaveRequestDTO, RworkerBatchStepSaveRequestDTO.class);
        rworkerBatchStepSaveRequestDto.setUnqualifiedItemInfoList(rworkerSnStepSaveRequestDTO.getUnqualifiedItemSaveInfoList());
        batchProcessSaveService.updateBatchWorkDetailRelationInfoBase(saveBatchWorkDetail, rworkerBatchStepSaveRequestDto, rworkerStepProcessBaseDTO);
        return saveBatchWorkDetail;
    }

    /**
     * 获取工单待流转数量
     *
     * @param workSheet             工单
     * @param wsStep                生产工单定制工序
     * @param onlineReworkStep      是否为在线调整类型工序
     * @param preStepTransferNumber 待流转数量
     * @return int 待流转数量
     */
    private int getWorkSheetPreStepTransferNumber(WorkSheet workSheet, WsStep wsStep, boolean onlineReworkStep, int preStepTransferNumber,RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        //是否前置工序都已完成
        boolean allPreStepFinished;
        if (StringUtils.isBlank(wsStep.getPreStepId())) {
            preStepTransferNumber = workSheet.getNumber();
        } else {
            //若当前投产工序存在前置工序则需要判断所有前置是否完成，若都已完成则获取当前工序待投产总数
            List<Long> preStepIdList = Arrays.stream(wsStep.getPreStepId().split(net.airuima.rbase.constant.Constants.STR_COMMA)).map(Long::valueOf).collect(Collectors.toList());
            List<BatchWorkDetail> preFinishedBatchWorkDetailList = batchWorkDetailRepository.findByWorkSheetIdAndStepIdInAndFinishAndDeleted(workSheet.getId(), preStepIdList, ConstantsEnum.FINISH_STATUS.getCategoryName(), Constants.LONG_ZERO);
            allPreStepFinished = ValidateUtils.isValid(preFinishedBatchWorkDetailList) && preFinishedBatchWorkDetailList.size() == preStepIdList.size();
            if (allPreStepFinished) {
                if (onlineReworkStep) {
                    preStepTransferNumber = batchProcessRequestServices[0].findOnlineReworkStepTransferNumber(workSheet.getId(), wsStep.getStep().getId(), Boolean.FALSE,rworkerStepProcessBaseDTO);
                } else {
                    preStepTransferNumber = StringUtils.isBlank(wsStep.getPreStepId()) ? workSheet.getNumber() : preFinishedBatchWorkDetailList.stream().mapToInt(BatchWorkDetail::getTransferNumber).min().getAsInt();
                }
                rworkerStepProcessBaseDTO.setRealPreStepTransferNumber(StringUtils.isBlank(wsStep.getPreStepId()) ? workSheet.getNumber() : preFinishedBatchWorkDetailList.stream().mapToInt(BatchWorkDetail::getTransferNumber).min().getAsInt());
            }
        }
        return preStepTransferNumber;
    }

    /**
     * 保存容器详情数据
     *
     * @param batchWorkDetail             批量详情数据
     * @param rworkerSnStepSaveRequestDTO SN待保存工序参数DTO
     * @param containerSnSaveInfo         容器待保存工序参数DTO
     * @param containerStepSaveBaseInfo   通用参数
     * @return net.airuima.domain.procedure.batch.ContainerDetail 容器详情
     */
    private ContainerDetail createContainerDetail(BatchWorkDetail batchWorkDetail, RworkerSnStepSaveRequestDTO rworkerSnStepSaveRequestDTO,
                                                  RworkerSnStepSaveRequestDTO.ContainerSnSaveInfo containerSnSaveInfo, RworkerStepProcessBaseDTO containerStepSaveBaseInfo) {
        //获取容器详情前置数据ID，方便后面批量删除回退数据
        //获取请求的容器未解绑的容器详情
        List<ContainerDetail> preContainerDetailList = null;
        if (!CollectionUtils.isEmpty(containerSnSaveInfo.getRequestContainerInfoList())) {
            List<Long> requestContainerIdList = containerSnSaveInfo.getRequestContainerInfoList().stream().map(RworkerContainerStepSaveRequestDTO.BingContainerInfo.RequestContainerInfo::getId).collect(Collectors.toList());
            if (Boolean.TRUE.equals(containerStepSaveBaseInfo.getSubWsProductionMode())) {
                preContainerDetailList = containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndContainerIdInAndStatusAndDeletedOrderByIdDesc(
                        containerStepSaveBaseInfo.getSubWorkSheet().getId(), requestContainerIdList, Constants.INT_ONE, Constants.LONG_ZERO);
            } else {
                preContainerDetailList = containerDetailRepository.findByBatchWorkDetailWorkSheetIdAndContainerIdInAndStatusAndDeletedOrderByIdDesc(
                        containerStepSaveBaseInfo.getWorkSheet().getId(), requestContainerIdList, Constants.INT_ONE, Constants.LONG_ZERO);
            }
        }
        if (ValidateUtils.isValid(preContainerDetailList)) {
            preContainerDetailList = preContainerDetailList.stream().filter(containerDetail -> !containerDetail.getBatchWorkDetail().getId().equals(batchWorkDetail.getId())).collect(Collectors.toList());
        }
        Container bindContainer = containerRepository.getReferenceById(containerSnSaveInfo.getBindContainerId());
        ContainerDetail containerDetail = containerDetailRepository.findTop1ByBatchWorkDetailIdAndContainerIdAndStatusAndDeletedOrderByIdDesc(batchWorkDetail.getId(),
                bindContainer.getId(), Constants.INT_ONE, Constants.LONG_ZERO).orElseGet(ContainerDetail::new);
        //更新前置容情详情剩余待流转数量以及获取各个前置容情详情分别下交到当前容情详情数量信息
        ContainerProcessSaveServiceImpl containerProcessSaveService = BeanUtil.getBean(ContainerProcessSaveServiceImpl.class);
        List<PreContainerDetailInfo> preContainerDetailInfoList = containerProcessSaveService.updatePreContainerDetailInfo(bindContainer, containerSnSaveInfo.getRequestContainerInfoList(), preContainerDetailList, containerDetail.getPreContainerDetailInfoList());
        // 获取前置容器详情编码列表并进行保存至当前容器想起
        Set<String> preContainerCodeList = StringUtils.isBlank(containerDetail.getPreContainerCodeList()) ? new HashSet<>() : new HashSet<>(Arrays.asList(containerDetail.getPreContainerCodeList().split(net.airuima.rbase.constant.Constants.STR_SEMICOLON)));
        if (ValidateUtils.isValid(preContainerDetailList)) {
            preContainerCodeList.addAll(preContainerDetailList.stream().map(ContainerDetail::getContainerCode).toList());
        }
        containerDetail.setBatchWorkDetail(batchWorkDetail);
        containerDetail.setBindTime(containerDetail.getId() != null ? containerDetail.getBindTime() : LocalDateTime.now());
        containerDetail.setStaffId(containerStepSaveBaseInfo.getStaffDTO().getId());
        containerDetail.setRecordDate(LocalDateTime.now());
        containerDetail.setContainer(bindContainer);
        containerDetail.setContainerCode(bindContainer.getCode());
        containerDetail.setStatus(ConstantsEnum.BINDING.getCategoryName());
        containerDetail.setInputNumber(containerDetail.getInputNumber() + containerSnSaveInfo.getNumber());
        containerDetail.setQualifiedNumber(containerDetail.getQualifiedNumber() + containerSnSaveInfo.getQualifiedNumber());
        containerDetail.setUnqualifiedNumber(containerDetail.getUnqualifiedNumber() + containerSnSaveInfo.getUnqualifiedNumber());
        containerDetail.setWorkCell(containerStepSaveBaseInfo.getWorkCell()).setStartTime(containerDetail.getId() != null ? containerDetail.getStartTime() : rworkerSnStepSaveRequestDTO.getStartTime());
        containerDetail.setPreContainerDetailInfoList(ValidateUtils.isValid(preContainerDetailInfoList) ? preContainerDetailInfoList : null);
        containerDetail.setPreContainerCodeList(ValidateUtils.isValid(preContainerCodeList) ? String.join(net.airuima.rbase.constant.Constants.STR_SEMICOLON, preContainerCodeList) : null);
        containerDetail.setTransferNumber(containerDetail.getTransferNumber() + containerSnSaveInfo.getQualifiedNumber());
        //若当前工序为最后一道工序解绑当前容器或者下个工序的请求模式为单支时则也直接解绑容器 || 下道工序为外协工序类型容器解绑
        if (StringUtils.isBlank(containerStepSaveBaseInfo.getWsStep().getAfterStepId())
                || containerStepSaveBaseInfo.getWsStepList().stream().anyMatch(wsStep -> containerStepSaveBaseInfo.getWsStep().getAfterStepId().contains(wsStep.getStep().getId().toString()) && wsStep.getRequestMode() == ConstantsEnum.SN_REQUEST_MODE.getCategoryName())
                || containerStepSaveBaseInfo.getWsStepList().stream().anyMatch(wsStep -> containerStepSaveBaseInfo.getWsStep().getAfterStepId().contains(wsStep.getStep().getId().toString()) && wsStep.getCategory() == StepCategoryEnum.OEM_STEP.getStatus())
        ) {
            containerDetail.setTransferNumber(Constants.INT_ZERO).setStatus(ConstantsEnum.UNBIND.getCategoryName()).setUnbindTime(LocalDateTime.now());
        }
        RworkerContainerStepSaveRequestDTO.BingContainerInfo bingContainerInfo = MapperUtils.map(containerSnSaveInfo, RworkerContainerStepSaveRequestDTO.BingContainerInfo.class);
        //动态数据参数不为空则需要更新动态数据
        if (Objects.nonNull(bingContainerInfo.getStepDynamicDataColumnGetDto())) {
            StepDynamicDataColumnGetDTO stepDynamicDataColumnGetDto = bingContainerInfo.getStepDynamicDataColumnGetDto();
            //补充工序编码和工序名称
            stepDynamicDataColumnGetDto.setStepName(containerDetail.getBatchWorkDetail().getStep().getName()).setStepCode(containerDetail.getBatchWorkDetail().getStep().getCode());
            containerDetail.setStepDynamicDataColumnGetDTO(stepDynamicDataColumnGetDto);
        }
        containerDetailRepository.save(containerDetail);
        //保存容情详情关联的追溯数据(物料批次、设备不良、易损件等)
        bingContainerInfo.setUnqualifiedItemInfoList(containerSnSaveInfo.getUnqualifiedItemSaveInfoList());
        RworkerContainerStepSaveRequestDTO rworkerContainerStepSaveRequestDto = MapperUtils.map(rworkerSnStepSaveRequestDTO, RworkerContainerStepSaveRequestDTO.class);
        rworkerContainerStepSaveRequestDto.setUnqualifiedItemInfoList(rworkerSnStepSaveRequestDTO.getUnqualifiedItemSaveInfoList());
        containerProcessSaveService.updateContainerWorkDetailRelationInfo(containerDetail, bingContainerInfo, rworkerContainerStepSaveRequestDto);
        return containerDetail;
    }

}
