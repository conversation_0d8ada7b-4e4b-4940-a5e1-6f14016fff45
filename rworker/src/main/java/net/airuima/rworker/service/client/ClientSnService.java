package net.airuima.rworker.service.client;

import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.*;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeConfig;
import net.airuima.rbase.domain.base.pedigree.PedigreeSnReuseConfig;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepMaterialRule;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.process.WorkFlowStep;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.rbase.domain.procedure.batch.*;
import net.airuima.rbase.domain.procedure.material.WsMaterialBatch;
import net.airuima.rbase.domain.procedure.material.WsWorkCellMaterialBatch;
import net.airuima.rbase.domain.procedure.report.StaffPerform;
import net.airuima.rbase.domain.procedure.report.StaffPerformUnqualifiedItem;
import net.airuima.rbase.domain.procedure.single.*;
import net.airuima.rbase.dto.batch.MaterialBatchInfo;
import net.airuima.rbase.dto.client.*;
import net.airuima.rbase.dto.ocmes.BakeCycleBakeAgeingSaveDTO;
import net.airuima.rbase.dto.ocmes.plugin.SnReplaceDTO;
import net.airuima.rbase.dto.process.StepDTO;
import net.airuima.rbase.dto.single.SnWorkStatusDTO;
import net.airuima.rbase.proxy.optical.RbaseBakeCycleBakeAgeingProxy;
import net.airuima.rbase.proxy.organization.RbaseSupplierProxy;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.repository.base.process.WorkFlowStepRepository;
import net.airuima.rbase.repository.base.quality.UnqualifiedItemRepository;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.repository.base.scene.WorkCellStepRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WsReworkRepository;
import net.airuima.rbase.repository.procedure.batch.*;
import net.airuima.rbase.repository.procedure.material.WsMaterialBatchRepository;
import net.airuima.rbase.repository.procedure.material.WsWorkCellMaterialBatchRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformUnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.single.*;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.ocmes.BakeCycleBakeAgeingModelService;
import net.airuima.rbase.service.procedure.aps.SubWorkSheetService;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.rbase.util.ToolUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rbase.web.rest.error.SaveRepeatException;
import net.airuima.rworker.service.client.api.IClientSnService;
import net.airuima.util.BeanUtil;
import net.airuima.util.RedisUtils;
import net.airuima.util.ResponseException;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.klock.annotation.Klock;
import org.springframework.boot.autoconfigure.klock.model.LockTimeoutStrategy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * RWorkSN相关Service
 *
 * <AUTHOR>
 * @date 2020/12/29
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class ClientSnService implements IClientSnService {
    private static final String OC_MES = "ocMes";
    private static final String BAKE_CYCLE_BAKE_AGEING_MODEL = "BakeCycleBakeAgeingModel";
    @Autowired
    private WsWorkCellMaterialBatchRepository wsWorkCellMaterialBatchRepository;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private SnWorkStatusRepository snWorkStatusRepository;
    @Autowired
    private WorkCellRepository workCellRepository;
    @Autowired
    private WorkCellStepRepository workCellStepRepository;
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private SnWorkDetailRepository snWorkDetailRepository;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private WsMaterialRepository wsMaterialRepository;
    @Autowired
    private UnqualifiedItemRepository unqualifiedItemRepository;
    @Autowired
    private BatchWorkDetailFacilityRepository batchWorkDetailFacilityRepository;
    @Autowired
    private BatchWorkDetailMaterialBatchRepository batchWorkDetailMaterialBatchRepository;
    @Autowired
    private SnWorkDetailFacilityRepository snWorkDetailFacilityRepository;
    @Autowired
    private SnWorkDetailMaterialBatchRepository snWorkDetailMaterialBatchRepository;
    @Autowired
    private WsMaterialBatchRepository wsMaterialBatchRepository;
    @Autowired
    private SnUnqualifiedItemRepository snUnqualifiedItemRepository;
    @Autowired
    private WsStepUnqualifiedItemRepository wsStepUnqualifiedItemRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private WorkFlowStepRepository workFlowStepRepository;
    @Autowired
    private StepRepository stepRepository;
    @Autowired
    private ClientStepService clientStepService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private SnReworkRepository snReworkRepository;
    @Autowired
    private WsReworkRepository wsReworkRepository;
    @Autowired
    private RbaseSupplierProxy rbaseSupplierProxy;
    @Autowired
    private StaffPerformRepository staffPerformRepository;
    @Autowired
    private WorkFlowRepository workFlowRepository;
    @Autowired
    private StaffPerformUnqualifiedItemRepository staffPerformUnqualifiedItemRepository;
    @Autowired
    private RbaseBakeCycleBakeAgeingProxy rbaseBakeCycleBakeAgeingProxy;
    @Autowired
    private SubWorkSheetService subWorkSheetService;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private BakeCycleBakeAgeingModelService[] bakeCycleBakeAgeingModelServices;


    /**
     * 获取待做工序并验证参数是否合规
     *
     * @param clientSnDto 单支SN请求工序参数
     * @return net.airuima.dto.client.ClientGetSnStepInfoDTO 请求单支返回工序信息
     * <AUTHOR>
     * @date 2021-03-16
     **/
    @Override
    @Transactional(readOnly = true)
    public ClientGetSnStepInfoDTO getStepInfo(ClientSnDTO clientSnDto) {
        Optional<WorkCell> workCellOptional = StringUtils.isNotBlank(clientSnDto.getWorkCellIp()) ? workCellRepository.findByIpAndDeleted(clientSnDto.getWorkCellIp(), Constants.LONG_ZERO) : workCellRepository.findByIdAndDeleted(clientSnDto.getWorkCellId(), Constants.LONG_ZERO);
        //验证工位是否存在
        if (!workCellOptional.isPresent()) {
            return new ClientGetSnStepInfoDTO<>(new BaseClientDTO(Constants.KO, "工位不存在!"));
        }
        //验证工位是否绑定工序
        List<Step> stepList = workCellStepRepository.findByWorkCellId(clientSnDto.getWorkCellId(), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(stepList)) {
            return new ClientGetSnStepInfoDTO<>(new BaseClientDTO(Constants.KO, "工位未绑定工序!"));
        }
        SnWorkStatus snWorkStatus = snWorkStatusRepository.findBySnAndDeleted(clientSnDto.getSn(), Constants.LONG_ZERO).orElse(null);
        if (null == clientSnDto.getSubWsId() && null == snWorkStatus) {
            return new ClientGetSnStepInfoDTO<>(new BaseClientDTO(Constants.KO, "SN未绑定任何工单!"));
        }
        SubWorkSheet subWorkSheet = null != clientSnDto.getSubWsId() ? subWorkSheetRepository.findByIdAndDeleted(clientSnDto.getSubWsId(), Constants.LONG_ZERO).orElse(null) : null;
        if (null != clientSnDto.getSubWsId() && null == subWorkSheet) {
            return new ClientGetSnStepInfoDTO<>(new BaseClientDTO(Constants.KO, "当前子工单不存在!"));
        }
        if (null == clientSnDto.getSubWsId() && null != snWorkStatus) {
            subWorkSheet = snWorkStatus.getSubWorkSheet();
        }
        if (null != snWorkStatus && snWorkStatus.getStatus() == SnWorkStatusEnum.MAINTAIN.getStatus()) {
            return new ClientGetSnStepInfoDTO<>(new BaseClientDTO(Constants.KO, "SN维修分析中!"));
        }
        if (null != snWorkStatus && snWorkStatus.getStatus() == SnWorkStatusEnum.IN_THE_REPAIR.getStatus()) {
            //验证当前sn是否存在返工单
            Optional<SnRework> snReworkOptional = snReworkRepository.findTop1BySnWorkStatusIdAndStatusAndDeletedOrderByIdDesc(snWorkStatus.getId(), ConstantsEnum.BINDING.getCategoryName(), Constants.LONG_ZERO);
            if (snReworkOptional.isPresent()) {
                //因为返工单只会生成一个返工子工单，所有getOne
                List<SubWorkSheet> subWorkSheets = subWorkSheetRepository.findByWorkSheetIdAndDeleted(snReworkOptional.get().getWsRework().getReworkWorkSheet().getId(), Constants.LONG_ZERO);
                if (ValidateUtils.isValid(subWorkSheets)) {
                    subWorkSheet = subWorkSheets.get(Constants.INT_ZERO);
                }
            }
        }
        WorkSheet workSheet = subWorkSheet.getWorkSheet();
        //先查找子工单定制工序，如果不存在查找总工单定制工序
        List<WsStep> wsStepList = wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(wsStepList)) {
            wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(subWorkSheet.getWorkSheet().getId(), Constants.LONG_ZERO);
        }
        if (!ValidateUtils.isValid(wsStepList)) {
            return new ClientGetSnStepInfoDTO<>(new BaseClientDTO(Constants.KO, "当前工单没有定制工序!"));
        }

        //检查工单状态是否可以投产（子工单、总工单暂停、停止）
        BaseClientDTO baseClientDto = clientStepService.checkSubWs(subWorkSheet);
        if (Constants.KO.equals(baseClientDto.getStatus())) {
            //纯单支在线返修不受子工单完成结果影响，即子工单完成了但是在线返修的SN继续返修
            return new ClientGetSnStepInfoDTO<>(baseClientDto);
        }
        //返修单时需要检查当前SN是否处在正常工单投产中，只有不合格、返修中及正常合格SN才可以进行返修
        if (null != snWorkStatus && subWorkSheet.getWorkSheet().getCategory() <= Constants.INT_ZERO
                && !snWorkStatus.getSubWorkSheet().getId().equals(subWorkSheet.getId())
                && snWorkStatus.getStatus() < Constants.INT_TWO) {
            return new ClientGetSnStepInfoDTO<>(new BaseClientDTO(Constants.KO, "当前SN正在投产中!"));
        }
        //工单不允许SN混单
        if (workSheet.getCategory() > Constants.INT_ZERO && null != snWorkStatus && !snWorkStatus.getSubWorkSheet().getId().equals(subWorkSheet.getId())) {
            return new ClientGetSnStepInfoDTO<>(new BaseClientDTO(Constants.KO, "当前SN[" + snWorkStatus.getSn() + "]不属于当前子工单[" + subWorkSheet.getSerialNumber() + "],而属于[" + snWorkStatus.getSubWorkSheet().getSerialNumber() + "!"));
        }
        //单支离线返修单返修或者在线返修单返修需要判断SN要么已完成、要么不合格、要么处于返修中
        if (workSheet.getCategory() <= Constants.INT_ZERO) {
            if (null == snWorkStatus) {
                return new ClientGetSnStepInfoDTO<>(new BaseClientDTO(Constants.KO, "当前SN从未在系统中投产过!"));
            }
            if (!snWorkStatus.getSubWorkSheet().getId().equals(subWorkSheet.getId()) && snWorkStatus.getStatus() < Constants.INT_TWO) {
                return new ClientGetSnStepInfoDTO<>(new BaseClientDTO(Constants.KO, "当前SN未完成正常投产，不可进行返修!"));
            }
        }
        return this.getStepInfo(subWorkSheet, clientSnDto, snWorkStatus, wsStepList, stepList);
    }

    /**
     * 获取待做工序并验证参数是否合规
     *
     * @param subWorkSheet 当前投产子工单
     * @param clientSnDto  请求参数
     * @param snWorkStatus sn生产状态
     * @param wsStepList   定制工序列表
     * @param stepList     工位绑定的工序列表
     * @return ClientGetSnStepAdaptInfoDTO
     * <AUTHOR>
     * @date 2021-03-16
     **/
    @Transactional(readOnly = true)
    public ClientGetSnStepInfoDTO getStepInfo(SubWorkSheet subWorkSheet, ClientSnDTO clientSnDto, SnWorkStatus snWorkStatus,
                                              List<WsStep> wsStepList, List<Step> stepList) {
        // 正常单SN报废后禁止在任何正常单里往下流转
        if (null != snWorkStatus && subWorkSheet.getWorkSheet().getCategory() > Constants.INT_ZERO
                && (snWorkStatus.getStatus() == SnWorkStatusEnum.SCRAP.getStatus() || snWorkStatus.getStatus() == SnWorkStatusEnum.RETURN_STOCK.getStatus())) {
            String status = snWorkStatus.getStatus() == SnWorkStatusEnum.SCRAP.getStatus() ? "报废" : "退库";
            return new ClientGetSnStepInfoDTO<>(new BaseClientDTO(Constants.KO, "当前SN已" + status + "禁止投产!"));
        }
        // 任何工单的SN报废后禁止继续在当前工单投产
        if (null != snWorkStatus
                && snWorkStatus.getSubWorkSheet().getId().equals(subWorkSheet.getId())
                && (snWorkStatus.getStatus() == SnWorkStatusEnum.SCRAP.getStatus() || snWorkStatus.getStatus() == SnWorkStatusEnum.RETURN_STOCK.getStatus())) {
            String status = snWorkStatus.getStatus() == SnWorkStatusEnum.SCRAP.getStatus() ? "报废" : "退库";
            return new ClientGetSnStepInfoDTO<>(new BaseClientDTO(Constants.KO, "当前SN已" + status + "禁止投产!"));
        }
        List<WorkFlowStep> workFlowStepList = workFlowStepRepository.findStepByWorkFlowIdAndDeleted(subWorkSheet.getWorkSheet().getWorkFlow().getId(), Constants.LONG_ZERO);
        if (null != snWorkStatus && null != snWorkStatus.getWorkFlow()) {
            workFlowStepList = workFlowStepRepository.findStepByWorkFlowIdAndDeleted(snWorkStatus.getWorkFlow().getId(), Constants.LONG_ZERO);
        }
        boolean snBindRework = false;
        //当sn存在验证是否为通过在线返修单返修sn
        if (null != snWorkStatus) {
            Optional<SnRework> snReworkOptional = snReworkRepository.findTop1BySnWorkStatusIdAndStatusAndDeletedOrderByIdDesc(snWorkStatus.getId(), ConstantsEnum.BINDING.getCategoryName(), Constants.LONG_ZERO);
            snBindRework = snReworkOptional.isPresent();
        }
        StepDTO nextToDoStepDto = null;
        //当前SN若从未投产、正常工单投产中或者离线返修单返修则根据定制工序获取后续待做工序列表
        if (Boolean.TRUE.equals(null == snWorkStatus ||
                snWorkStatus.getReworkTime() == Constants.INT_ZERO || snBindRework) ||
                !Objects.requireNonNull(snWorkStatus).getLatestReworkSnWorkDetail().getSubWorkSheet().getId().equals(subWorkSheet.getId())) {
            // 未绑定工序集合
            List<Step> noBindStepList = new ArrayList<Step>();
            nextToDoStepDto = this.findToDoStepWithWsStep(clientSnDto.getSn(), wsStepList, stepList, snWorkStatus, subWorkSheet, noBindStepList);
            if (!CollectionUtils.isEmpty(noBindStepList)) {
                String noBindStepListMsg = noBindStepList.stream().map(i -> i.getName() + "(" + i.getCode() + ")").collect(Collectors.joining(","));
                return new ClientGetSnStepInfoDTO<>(new BaseClientDTO(Constants.KO, "当前工位未绑定下一步工序：" + noBindStepListMsg));
            }
        }
        //若单支直接在线返修则以返修工艺路线进行获取下个待做工序
        if (null != snWorkStatus &&
                snWorkStatus.getReworkTime() > Constants.INT_ZERO &&
                !snBindRework &&
                snWorkStatus.getLatestReworkSnWorkDetail().getSubWorkSheet().getId().equals(subWorkSheet.getId())) {
            nextToDoStepDto = this.findToDoStepWithWorkFlowStep(clientSnDto.getSn(), workFlowStepList, stepList, snWorkStatus, subWorkSheet);
        }
        if (null == nextToDoStepDto) {
            return new ClientGetSnStepInfoDTO<>(new BaseClientDTO(Constants.KO, "当前SN已完成!"));
        }
        BatchWorkDetail batchWorkDetail = batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), nextToDoStepDto.getId(), Constants.LONG_ZERO).orElse(null);
        if (null != batchWorkDetail && (batchWorkDetail.getInputNumber()) > subWorkSheet.getNumber()) {
            return new ClientGetSnStepInfoDTO<>(new BaseClientDTO(Constants.KO, "当前工序投入数已超过工单投产数!"));
        }
        WorkCell workCell = workCellRepository.getReferenceById(clientSnDto.getWorkCellId());
        ClientGetSnStepInfoDTO<Object> clientGetSnStepAdaptInfoDto = new ClientGetSnStepInfoDTO<>();
        Step currStep = stepRepository.getReferenceById(nextToDoStepDto.getId());
        //获取工序不良项目,优先获取产品谱系配置的工序不良项目，最后才获取工序默认配置的不良项目
        clientGetSnStepAdaptInfoDto.setUnqualifiedItemInfoList(clientStepService.getUnqualifiedItemInfo(subWorkSheet, currStep, Constants.INT_ONE));
        //获取定制流程(纯单支在线返修的SN以定义的在线返修工艺路线作为返修流程)
        if (null != snWorkStatus &&
                snWorkStatus.getReworkTime() > Constants.INT_ZERO && !snBindRework &&
                snWorkStatus.getLatestSnWorkDetail().getSubWorkSheet().getId().equals(subWorkSheet.getId())) {
            List<StepDTO> StepDtoList = findWsStepList(workFlowStepList, subWorkSheet);
            clientGetSnStepAdaptInfoDto.setWsStepInfoList(StepDtoList.stream().map(ClientGetStepInfoDTO.WsStepInfo::new).collect(Collectors.toList()));
        } else {
            clientGetSnStepAdaptInfoDto.setWsStepInfoList(wsStepList.stream().map(ClientGetSnStepInfoDTO.WsStepInfo::new).collect(Collectors.toList()));
        }
        //获取工位工序绑定的设备信息
        clientGetSnStepAdaptInfoDto.setEquipmentInfoList(clientStepService.getEquipmentIfo(clientSnDto.getWorkCellId(), currStep.getId()));
        //获取工序需要上料的物料集合
        if (nextToDoStepDto.getIsControlMaterial()) {
            clientGetSnStepAdaptInfoDto.setMaterialInfoList(clientStepService.getMaterialInfo(subWorkSheet, workCell, currStep, Constants.INT_ONE));
        }
        //获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = commonService.findSnapshotWorkFlow(subWorkSheet.getWorkSheet(), subWorkSheet,currStep);
        clientGetSnStepAdaptInfoDto.setClientStepSubWsInfoDto(new ClientStepSubWsInfoDTO(subWorkSheet))
                .setReworkTime(null != snWorkStatus ? snWorkStatus.getReworkTime() : Constants.INT_ZERO)
                .setWorkFlowId(snWorkStatus == null || !snWorkStatus.getSubWorkSheet().getId().equals(subWorkSheet.getId()) ? snapshotWorkFlow.getId() : snWorkStatus.getWorkFlow().getId())
                .setSn(clientSnDto.getSn())
                .setWsId(subWorkSheet.getWorkSheet().getId())
                .setSubWsId(subWorkSheet.getId())
                .setWorkCellId(clientSnDto.getWorkCellId())
                .setControlMode(nextToDoStepDto.getControlMode())
                .setRequestMode(nextToDoStepDto.getRequestMode())
                .setIsBindContainer(nextToDoStepDto.getIsBindContainer())
                .setIsControlMaterial(nextToDoStepDto.getIsControlMaterial())
                .setStepId(nextToDoStepDto.getId())
                .setStepCode(nextToDoStepDto.getCode())
                .setStepName(nextToDoStepDto.getName())
                .setStepCategory(nextToDoStepDto.getCategory())
                .setPedigreeName(subWorkSheet.getWorkSheet().getPedigree().getName())
                .setPedigreeCode(subWorkSheet.getWorkSheet().getPedigree().getCode())
                .setPedigreeSpecification(subWorkSheet.getWorkSheet().getPedigree().getSpecification()).setXsrfToken(ToolUtils.generateUuId());
        //获取烘烤温循老化配置信息(如果获取当前配置不是最后则需要验证返回参数状态，是否为ko)
        return bakeCycleBakeAgeingModelServices[0].queryBakeCycleBakeAgeingConfig(clientGetSnStepAdaptInfoDto);
    }

    /**
     * 获取单支返修单的工艺路线工序配置
     *
     * @param workFlowStepList 工艺路线工序关系列表
     * @param subWorkSheet     子工单
     * @return List<StepDTO>
     * <AUTHOR>
     * @date 2022/4/6
     */
    private List<StepDTO> findWsStepList(List<WorkFlowStep> workFlowStepList, SubWorkSheet subWorkSheet) {
        return workFlowStepList.stream().map(workFlowStep -> {
            StepDTO stepDTO = commonService.findPedigreeStepConfig(subWorkSheet.getWorkSheet().getClientId(),subWorkSheet.getWorkSheet().getPedigree(), workFlowStep.getWorkFlow(), workFlowStep.getStep());
            if (null == stepDTO) {
                throw new ResponseException("error.stepConfigNotExist", "工序(" + workFlowStep.getStep().getCode() + ")请求模式配置不存在");
            }
            return stepDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 根据工单定制工序获取当前SN待做工序信息
     *
     * @param sn             当前SN
     * @param wsStepList     工单定制工序
     * @param stepList       工位工序列表
     * @param snWorkStatus   SN生产状态
     * @param subWorkSheet   子工单
     * @param noBindStepList 未绑定工具集合
     * @return StepDTO
     * <AUTHOR>
     * @date 2021-05-23
     **/
    public StepDTO findToDoStepWithWsStep(String sn, List<WsStep> wsStepList, List<Step> stepList, SnWorkStatus snWorkStatus, SubWorkSheet subWorkSheet, List<Step> noBindStepList) {
        List<Step> todoStepList = null;
        if (null == snWorkStatus || !snWorkStatus.getSubWorkSheet().getId().equals(subWorkSheet.getId())) {
            todoStepList = wsStepList.stream().filter(wsStep -> StringUtils.isBlank(wsStep.getPreStepId())).map(WsStep::getStep).collect(Collectors.toList());
        } else {
            WsStep wsStep = wsStepList.stream().filter(wsStep1 -> wsStep1.getStep().getId().equals(snWorkStatus.getLatestSnWorkDetail().getStep().getId())).findFirst().orElse(null);
            if (null != wsStep && StringUtils.isNotBlank(wsStep.getAfterStepId())) {
                todoStepList = wsStepList.stream().map(WsStep::getStep).filter(step -> wsStep.getAfterStepId().contains(step.getId().toString())).collect(Collectors.toList());
            }
        }
        if (!ValidateUtils.isValid(todoStepList)) {
            return null;
        }
        StepDTO nextTodoStepDto = null;
        for (Step toDoStep : todoStepList) {
            if (stepList.stream().anyMatch(step -> step.getId().equals(toDoStep.getId()))) {
                Optional<SnWorkDetail> snWorkDetailOptional = snWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndSnAndReworkTimeAndDeleted(subWorkSheet.getId(), toDoStep.getId(), sn, null != snWorkStatus ? snWorkStatus.getReworkTime() : Constants.INT_ZERO, Constants.LONG_ZERO);
                if (!snWorkDetailOptional.isPresent()) {
                    WsStep currWsStep = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(toDoStep.getId())).findFirst().orElseThrow(() -> new ResponseException("error.wsStepNotExist", "生产工单定制工序不存在"));
                    nextTodoStepDto = new StepDTO(currWsStep);
                    return nextTodoStepDto;
                }
            } else {
                //当前工位工序中不包括下一步待做工序
                noBindStepList.add(toDoStep);
            }
        }
        return nextTodoStepDto;
    }

    /**
     * 根据返修工艺路线获取当前SN待做工序信息
     *
     * @param sn               当前SN
     * @param workFlowStepList 返修工艺路线
     * @param stepList         工位工序列表
     * @param snWorkStatus     SN生产状态
     * @param subWorkSheet     子工单
     * @return StepDTO
     * <AUTHOR>
     * @date 2021-05-23
     **/
    public StepDTO findToDoStepWithWorkFlowStep(String sn, List<WorkFlowStep> workFlowStepList, List<Step> stepList, SnWorkStatus snWorkStatus, SubWorkSheet subWorkSheet) {
        List<Step> todoStepList = null;
        if (snWorkStatus.getLatestSnWorkDetail().getReworkTime() != snWorkStatus.getReworkTime()) {
            todoStepList = workFlowStepList.stream().filter(workFlowStep -> StringUtils.isBlank(workFlowStep.getPreStepId())).map(WorkFlowStep::getStep).collect(Collectors.toList());
        } else {
            WorkFlowStep workFlowStep = workFlowStepList.stream().filter(workFlowStep1 -> workFlowStep1.getStep().getId().equals(snWorkStatus.getLatestSnWorkDetail().getStep().getId())).findFirst().orElse(null);
            if (null != workFlowStep && StringUtils.isNotBlank(workFlowStep.getAfterStepId())) {
                todoStepList = workFlowStepList.stream().map(WorkFlowStep::getStep).filter(step -> workFlowStep.getAfterStepId().contains(step.getId().toString())).collect(Collectors.toList());
            }
        }
        if (!ValidateUtils.isValid(todoStepList)) {
            return null;
        }
        for (Step toDoStep : todoStepList) {
            if (stepList.stream().anyMatch(step -> step.getId().equals(toDoStep.getId()))) {
                Optional<SnWorkDetail> snWorkDetailOptional = snWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndSnAndReworkTimeAndDeleted(subWorkSheet.getId(), toDoStep.getId(), sn, snWorkStatus.getReworkTime(), Constants.LONG_ZERO);
                if (!snWorkDetailOptional.isPresent()) {
                    StepDTO stepDto = commonService.findPedigreeStepConfig(subWorkSheet.getWorkSheet().getClientId(),subWorkSheet.getWorkSheet().getPedigree(), workFlowStepList.get(Constants.INT_ZERO).getWorkFlow(), toDoStep);
                    if (null == stepDto) {
                        throw new ResponseException("error.stepConfigNotExist", "工序(" + toDoStep.getCode() + ")请求模式配置不存在");
                    }
                    stepDto.setCode(toDoStep.getCode())
                            .setName(toDoStep.getName());
                    return stepDto;
                }
            }
        }
        return null;
    }


    /**
     * 保存单支工序详情相关信息
     *
     * @param clientSaveSnStepInfoDto 请求参数
     * @return BaseClientDTO 返回的参数
     * <AUTHOR>
     * @date 2021-03-18
     **/
    @Transactional(rollbackFor = Exception.class)
    @Klock(keys = {"#clientSaveSnStepInfoDto.subWsId", "#clientSaveSnStepInfoDto.stepId"}, waitTime = 60, leaseTime = 60, lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST)
    public BaseClientDTO saveStepInfo(ClientSaveSnStepInfoDTO clientSaveSnStepInfoDto) {
        //根据token获取redis里面的记录，防止重复提交
        if (StringUtils.isNotBlank(clientSaveSnStepInfoDto.getXsrfToken()) && null != redisUtils.get(clientSaveSnStepInfoDto.getXsrfToken())) {
            return new BaseClientDTO(Constants.KO, "当前生产数据已保存,请勿重复提交!");
        }
        // 是否需要更新批量详情的数据(如果为在线返修不换工单时不更新，不然会导致批量详情数据超过工单投产数)
        boolean isUpdateWsdInfo = true;
        SnWorkStatus snWorkStatus = snWorkStatusRepository.findBySnAndDeleted(clientSaveSnStepInfoDto.getSn(), Constants.LONG_ZERO).orElse(null);
        SubWorkSheet subWorkSheet = subWorkSheetRepository.getReferenceById(clientSaveSnStepInfoDto.getSubWsId());
        Step step = stepRepository.getReferenceById(clientSaveSnStepInfoDto.getStepId());
        WorkCell workCell = workCellRepository.getReferenceById(clientSaveSnStepInfoDto.getWorkCellId());
        Optional<SnWorkDetail> snWorkDetailOptional = snWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndSnAndReworkTimeAndDeleted(clientSaveSnStepInfoDto.getSubWsId(), clientSaveSnStepInfoDto.getStepId(), clientSaveSnStepInfoDto.getSn(), clientSaveSnStepInfoDto.getReworkTime(), Constants.LONG_ZERO);
        if (snWorkDetailOptional.isPresent()) {
            return new BaseClientDTO(Constants.KO, "当前工序已完成!");
        }
        Boolean snUnBindRework = true;
        //当sn存在验证是否为通过在线返修单返修sn(初次单支)
        if (null != snWorkStatus) {
            Optional<SnRework> snReworkOptional = snReworkRepository.findTop1BySnWorkStatusIdAndStatusAndDeletedOrderByIdDesc(snWorkStatus.getId(), ConstantsEnum.BINDING.getCategoryName(), Constants.LONG_ZERO);
            if (snReworkOptional.isPresent()) {
                if (!snReworkOptional.get().getWsRework().getReworkWorkSheet().getId().equals(subWorkSheet.getWorkSheet().getId())) {
                    return new BaseClientDTO(Constants.KO, "当前sn[" + snWorkStatus.getSn() + "]绑定返修工单[" + snReworkOptional.get().getWsRework().getReworkWorkSheet().getSerialNumber() + "]与请求工单[" + subWorkSheet.getWorkSheet().getSerialNumber() + "]不一致");
                }
                snUnBindRework = false;
            }
        }
        //验证物料库存是否足够
        if (clientStepService.validateIsCheckWsReceiveMaterial(subWorkSheet.getWorkSheet()) && ValidateUtils.isValid(clientSaveSnStepInfoDto.getMaterialBatchInfoList())) {
            BaseClientDTO baseClientDTO = clientStepService.validateMaterialInventory(workCell, subWorkSheet.getWorkSheet(), step, Constants.INT_ONE, clientSaveSnStepInfoDto.getMaterialBatchInfoList().stream().map(MaterialBatchInfo::new).collect(Collectors.toList()));
            if (baseClientDTO.getStatus().equals(Constants.KO)) {
                return baseClientDTO;
            }
        }
        //如果不是按照在线返修单批量返修，即直接按照单支在线返修的时候不更新工单详情的数据
        if (Boolean.TRUE.equals(null != snWorkStatus && null != snWorkStatus.getLatestReworkSnWorkDetail()
                && snUnBindRework)
                && subWorkSheet.getId().equals(snWorkStatus.getLatestReworkSnWorkDetail().getSubWorkSheet().getId())) {
            isUpdateWsdInfo = false;
        }
        //判断批量详情的投产数是否超过工单数量
        if (isUpdateWsdInfo) {
            BatchWorkDetail batchWorkDetail = batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), step.getId(), Constants.LONG_ZERO).orElse(null);
            if (null != batchWorkDetail && batchWorkDetail.getInputNumber() + Constants.INT_ONE > subWorkSheet.getNumber()) {
                return new BaseClientDTO(Constants.KO, "当前工序投产数已超过工单数量!");
            }
        }
        WsStep wsStep = wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getWorkSheet().getId(), step.getId(), Constants.LONG_ZERO).orElse(null);
        //验证烘烤温循老化
        BaseClientDTO baseClientDto = null;
        if(Objects.nonNull(clientSaveSnStepInfoDto.getBakeHistoryInfo())){
            baseClientDto = rbaseBakeCycleBakeAgeingProxy.validateBakeStepInfo(new BakeCycleBakeAgeingSaveDTO(clientSaveSnStepInfoDto).setSubWorkSheet(subWorkSheet).setWsStep(wsStep).setStep(step));
        }
        if(Objects.isNull(baseClientDto) && Objects.nonNull(clientSaveSnStepInfoDto.getCycleBakeHistoryInfo())){
            baseClientDto = rbaseBakeCycleBakeAgeingProxy.validateCycleBakeStepInfo(new BakeCycleBakeAgeingSaveDTO(clientSaveSnStepInfoDto).setSubWorkSheet(subWorkSheet).setWsStep(wsStep).setStep(step));
        }
        if(Objects.isNull(baseClientDto) && Objects.nonNull(clientSaveSnStepInfoDto.getAgeingHistoryInfo())){
            baseClientDto = rbaseBakeCycleBakeAgeingProxy.validateAgingStepInfo(new BakeCycleBakeAgeingSaveDTO(clientSaveSnStepInfoDto).setSubWorkSheet(subWorkSheet).setWsStep(wsStep).setStep(step));
        }
        if (Constants.KO.equals(baseClientDto.getStatus())) {
            return baseClientDto;
        }
        IClientSnService clientSnService = BeanUtil.getHighestPrecedenceBean(IClientSnService.class);
        BaseClientDTO baseClientDTO = clientSnService.saveStepInfo(clientSaveSnStepInfoDto, snWorkStatus, subWorkSheet, workCell, step, isUpdateWsdInfo, snUnBindRework);
        clientSnService.afterStepSnSaved(clientSaveSnStepInfoDto);
        return baseClientDTO;
    }

    /**
     * 保存单支工序详情相关信息
     *
     * @param clientSaveSnStepInfoDto 请求参数
     * @param snWorkStatus            SN生产状态
     * @param subWorkSheet            子工单
     * @param workCell                工位
     * @param step                    工序
     * @param isUpdateWsdInfo         是否更新工单详情
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO 结果信息
     * <AUTHOR>
     * @date 2021-03-18
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseClientDTO saveStepInfo(ClientSaveSnStepInfoDTO clientSaveSnStepInfoDto, SnWorkStatus snWorkStatus,
                                      SubWorkSheet subWorkSheet, WorkCell workCell, Step step, boolean isUpdateWsdInfo, boolean snUnBindRework) {
        WsStep wsStep = wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getWorkSheet().getId(), step.getId(), Constants.LONG_ZERO).orElse(null);
        UnqualifiedItem unqualifiedItem = null != clientSaveSnStepInfoDto.getUnqualifiedItemId() ? unqualifiedItemRepository.getReferenceById(clientSaveSnStepInfoDto.getUnqualifiedItemId()) : null;
        WorkSheet workSheet = subWorkSheet.getWorkSheet();
        if (clientSaveSnStepInfoDto.getResult() == Constants.INT_ZERO && null == unqualifiedItem) {
            return new BaseClientDTO(Constants.KO, "当前SN不良项目不存在!");
        }
        // 保存sn状态以及sn工作详情
        SnWorkDetail snWorkDetail = new SnWorkDetail();
        if (snWorkStatus == null) {
            snWorkStatus = new SnWorkStatus();
        }
        IClientSnService clientSnService = BeanUtil.getHighestPrecedenceBean(IClientSnService.class);
        clientSnService.saveSnWorkStatusAndSnWorkDetail(clientSaveSnStepInfoDto, snWorkDetail, snWorkStatus, wsStep, snUnBindRework);
        //总工单的第一个工序录入时作为该工单的实际开工时间
        if (null == workSheet.getActualStartDate()) {
            workSheet.setActualStartDate(LocalDateTime.now());
            workSheetRepository.save(workSheet);
        }
        //子工单的第一个工序录入时作为该工单的实际开工时间
        if (null == subWorkSheet.getActualStartDate()) {
            subWorkSheet.setActualStartDate(LocalDateTime.now());
            subWorkSheetRepository.save(subWorkSheet);
        }
        //更新批量详情数据
        BatchWorkDetail batchWorkDetail = isUpdateWsdInfo ? this.updateBatchWsdInfo(snWorkDetail, wsStep) : null;
        //保存批量详情设备及单支详情设备信息
        this.saveWorkDetailEquipment(batchWorkDetail, snWorkDetail, clientSaveSnStepInfoDto);
        //更新批量详情及单支详情物料批次信息
        this.saveWorkDetailMaterialBatch(batchWorkDetail, snWorkDetail, clientSaveSnStepInfoDto);
        //更新单支与批量详情不良信息
        this.saveWorkDetailUnqualifiedItem(snWorkDetail, unqualifiedItem, isUpdateWsdInfo);
        //保存单支与员工产量数据
        this.saveSnStaffPerformReport(batchWorkDetail, snWorkDetail, clientSaveSnStepInfoDto);
        //对于需要核验领料的工单进行库存扣数
        if (Boolean.TRUE.equals(clientStepService.validateIsCheckWsReceiveMaterial(workSheet)) && ValidateUtils.isValid(clientSaveSnStepInfoDto.getMaterialBatchInfoList())) {
            this.subtractOnlineInventory(workSheet, workCell, step, clientSaveSnStepInfoDto.getMaterialBatchInfoList(), Constants.INT_ONE);
        }
        //SN工序全部完成时或者报废时更新子工单、总工单数据
        if (snWorkStatus.getStatus() == Constants.INT_FOUR || snWorkStatus.getStatus() == Constants.INT_FIVE || snWorkStatus.getStatus() == Constants.INT_SIX) {
            this.updateWorkSheetInfo(snWorkStatus);
        }
        //判断工序良率是否需要预警
        if (clientSaveSnStepInfoDto.getResult() == Constants.INT_ZERO && null != unqualifiedItem && null != batchWorkDetail) {
            clientStepService.saveStepWaring(batchWorkDetail);
        }
        //生产数据保存完后再一次验证是否存在相同重复提交的key,若存在则事务回滚
        if (StringUtils.isNotBlank(clientSaveSnStepInfoDto.getXsrfToken())) {
            if (null == redisUtils.get(clientSaveSnStepInfoDto.getXsrfToken())) {
                redisUtils.set(clientSaveSnStepInfoDto.getXsrfToken(), clientSaveSnStepInfoDto.getXsrfToken(), 5 * 60 * 60);
            } else {
                throw new SaveRepeatException();
            }
        }
        //保存烘烤温循老化历史数据
        BaseClientDTO baseClientDTO = new BaseClientDTO(Constants.OK);
        //保存烘烤温循老化历史数据
        if(Objects.nonNull(clientSaveSnStepInfoDto.getBakeHistoryInfo())){
            baseClientDTO =  bakeCycleBakeAgeingModelServices[0].saveBakeHistoryInfo(new BakeCycleBakeAgeingSaveDTO(clientSaveSnStepInfoDto).setSubWorkSheet(subWorkSheet).setWsStep(wsStep).setStep(step));
        }
        if(Objects.nonNull(clientSaveSnStepInfoDto.getCycleBakeHistoryInfo())){
            baseClientDTO =  bakeCycleBakeAgeingModelServices[0].saveCycleBakeHistoryInfo(new BakeCycleBakeAgeingSaveDTO(clientSaveSnStepInfoDto).setSubWorkSheet(subWorkSheet).setWsStep(wsStep).setStep(step));
        }
        if(Objects.nonNull(clientSaveSnStepInfoDto.getAgeingHistoryInfo())){
            baseClientDTO =  bakeCycleBakeAgeingModelServices[0].saveAgeingHistoryInfo(new BakeCycleBakeAgeingSaveDTO(clientSaveSnStepInfoDto).setSubWorkSheet(subWorkSheet).setWsStep(wsStep).setStep(step));
        }
        return baseClientDTO;
    }

    /**
     * 保存sn状态以及sn工作详情
     *
     * @param clientSaveSnStepInfoDto SN工序请求参数
     * @param snWorkDetail            sn工作详情
     * @param snWorkStatus            sn状态
     * @param wsStep                  当前工序快照
     * @param snUnBindRework          当sn存在验证是否为通过在线返修单返修sn(初次单支)
     */
    @Override
    public void saveSnWorkStatusAndSnWorkDetail(ClientSaveSnStepInfoDTO clientSaveSnStepInfoDto, SnWorkDetail snWorkDetail,
                                                SnWorkStatus snWorkStatus, WsStep wsStep, boolean snUnBindRework) {
        WorkFlow workFlow = workFlowRepository.getReferenceById(clientSaveSnStepInfoDto.getWorkFlowId());
        SubWorkSheet subWorkSheet = subWorkSheetRepository.getReferenceById(clientSaveSnStepInfoDto.getSubWsId());
        Step step = stepRepository.getReferenceById(clientSaveSnStepInfoDto.getStepId());
        WorkCell workCell = workCellRepository.getReferenceById(clientSaveSnStepInfoDto.getWorkCellId());
        UnqualifiedItem unqualifiedItem = null != clientSaveSnStepInfoDto.getUnqualifiedItemId() ? unqualifiedItemRepository.getReferenceById(clientSaveSnStepInfoDto.getUnqualifiedItemId()) : null;
        //保存SN工序详情信息
        snWorkDetail.setEndDate(LocalDateTime.now())
                .setOperatorId(clientSaveSnStepInfoDto.getStaffId())
                .setStartDate(clientSaveSnStepInfoDto.getStartTime())
                .setResult(clientSaveSnStepInfoDto.getResult())
                .setSn(clientSaveSnStepInfoDto.getSn())
                .setSubWorkSheet(subWorkSheet)
                .setWorkCell(workCell)
                .setStep(step)
                .setUnqualifiedItem(unqualifiedItem)
                .setReworkTime(clientSaveSnStepInfoDto.getReworkTime())
                .setWorkHour(ChronoUnit.MINUTES.between(clientSaveSnStepInfoDto.getStartTime(), LocalDateTime.now()))
                .setDeleted(Constants.LONG_ZERO);
        snWorkDetailRepository.save(snWorkDetail);
        //新增或更新SN生产状态信息
        if (snWorkStatus.getId() == null) {
            snWorkStatus.setStartDate(LocalDateTime.now())
                    .setReworkTime(Constants.INT_ZERO)
                    .setDeleted(Constants.LONG_ZERO);
        }
        //对于直接在线返修的SN需要判断返修工艺路线里是否还有后续工序，若无则完成，否则为反修中
        if (null != snWorkStatus.getId() &&
                snWorkStatus.getReworkTime() > Constants.INT_ZERO &&
                snUnBindRework &&
                snWorkStatus.getLatestSnWorkDetail().getSubWorkSheet().getId().equals(subWorkSheet.getId())) {
            Optional<WorkFlowStep> workFlowStepOptional = workFlowStepRepository.findByWorkFlowIdAndStepIdAndDeleted(workFlow.getId(), step.getId(), Constants.LONG_ZERO);
            if (workFlowStepOptional.isPresent() && StringUtils.isBlank(workFlowStepOptional.get().getAfterStepId())) {
                snWorkStatus.setStatus(Constants.INT_FOUR);
            } else {
                snWorkStatus.setStatus(Constants.INT_THREE);
            }
        } else {
            snWorkStatus.setStatus(StringUtils.isNotBlank(wsStep.getAfterStepId()) ? subWorkSheet.getWorkSheet().getCategory() <= Constants.INT_ZERO ? Constants.INT_THREE : Constants.INT_ONE : Constants.INT_FOUR);
        }
        snWorkStatus.setSn(clientSaveSnStepInfoDto.getSn())
                .setSubWorkSheet(subWorkSheet)
                .setWorkFlow(workFlow)
                .setReworkTime(snWorkDetail.getResult() != Constants.INT_ONE ? clientSaveSnStepInfoDto.getReworkTime() + Constants.INT_ONE : clientSaveSnStepInfoDto.getReworkTime())
                .setLatestSnWorkDetail(snWorkDetail);
        //SN发生不良时若不良项目处理方式为报废则直接报废，否则需要查看有无定义在线返修流程框图，没定义则认为报废
        if (snWorkDetail.getResult() == Constants.INT_ZERO) {
            assert unqualifiedItem != null;
            if (unqualifiedItem.getDealWay() == Constants.INT_TWO) {
                snWorkStatus.setStatus(Constants.INT_FIVE);
            }
            if (unqualifiedItem.getDealWay() == Constants.INT_THREE) {
                snWorkStatus.setStatus(Constants.INT_SIX);
            } else {
                WorkFlow reWorkFlow = commonService.findPedigreeReworkWorkFlow(subWorkSheet.getWorkSheet().getPedigree(), unqualifiedItem.getUnqualifiedGroup().getId(), subWorkSheet.getWorkSheet().getClientId());
                if (unqualifiedItem.getDealWay() == Constants.INT_ZERO && null != reWorkFlow) {
                    snWorkStatus.setWorkFlow(reWorkFlow).setStatus(Constants.INT_THREE).setReworkStartDate(LocalDateTime.now());
                } else {
                    snWorkStatus.setStatus(Constants.INT_FIVE);
                }
            }
            snWorkStatus.setLatestReworkSnWorkDetail(snWorkDetail).setLatestUnqualifiedItem(unqualifiedItem);
        }
        snWorkStatus.setEndDate(snWorkStatus.getStatus() == Constants.INT_FOUR || snWorkStatus.getStatus() == Constants.INT_FIVE ? LocalDateTime.now() : null);
        snWorkStatusRepository.save(snWorkStatus);
    }

    /**
     * 保存纯单支员工产量数据
     *
     * @param batchWorkDetail         批次详情
     * @param snWorkDetail            sn详情
     * @param clientSaveSnStepInfoDto 单支下交数据
     * @return void
     * <AUTHOR>
     * @date 2022/12/5
     */
    private void saveSnStaffPerformReport(BatchWorkDetail batchWorkDetail, SnWorkDetail snWorkDetail, ClientSaveSnStepInfoDTO clientSaveSnStepInfoDto) {
        //获取当前记录日期
        LocalDate recordDate = commonService.staffWorkRecordDate();
        //保存员工产量信息
        StaffPerform staffPerform = new StaffPerform();
        staffPerform
                .setBatchWorkDetailId(batchWorkDetail != null ? batchWorkDetail.getId() : null)
                .setContainerDetailId(null)
                .setSnWorkDetailId(snWorkDetail.getId())
                .setStaffId(clientSaveSnStepInfoDto.getStaffId())
                .setStep(stepRepository.getReferenceById(clientSaveSnStepInfoDto.getStepId()))
                .setSubWorkSheet(snWorkDetail.getSubWorkSheet())
                .setWorkSheet(snWorkDetail.getSubWorkSheet().getWorkSheet())
                .setWorkCell(workCellRepository.getReferenceById(clientSaveSnStepInfoDto.getWorkCellId()))
                .setRecordDate(recordDate)
                .setRecordTime(LocalDateTime.now())
                .setInputNumber(Constants.INT_ONE)
                .setWorkHour(staffPerform.getWorkHour()+snWorkDetail.getWorkHour())
                .setQualifiedNumber(clientSaveSnStepInfoDto.getResult() == Constants.INT_ONE ? Constants.INT_ONE : Constants.INT_ZERO)
                .setUnqualifiedNumber(clientSaveSnStepInfoDto.getResult() == Constants.INT_ONE ? Constants.INT_ZERO : Constants.INT_ONE).setDeleted(Constants.LONG_ZERO);
        staffPerformRepository.save(staffPerform);

        //若有不良则更新员工不良明细信息
        if (clientSaveSnStepInfoDto.getResult() == Constants.INT_ZERO && clientSaveSnStepInfoDto.getUnqualifiedItemId() != null) {

            StaffPerformUnqualifiedItem staffPerformUnqualifiedItem = new StaffPerformUnqualifiedItem();
            staffPerformUnqualifiedItem.setStaffPerform(staffPerform).setRecordDate(staffPerform.getRecordDate())
                    .setRecordTime(staffPerform.getRecordTime())
                    .setUnqualifiedItem(unqualifiedItemRepository.getReferenceById(clientSaveSnStepInfoDto.getUnqualifiedItemId()))
                    .setNumber(Constants.INT_ONE).setDeleted(Constants.LONG_ZERO);
            staffPerformUnqualifiedItemRepository.save(staffPerformUnqualifiedItem);
        }
    }

    /**
     * 验证SN复用是否合法
     *
     * @param workSheet    总工单
     * @param snWorkStatus sn生产状态
     * @return ClientGetSnStepInfoDTO
     * <AUTHOR>
     * @date 2021-05-20
     **/
    @Transactional(readOnly = true)
    public BaseClientDTO validateSnReUse(WorkSheet workSheet, SnWorkStatus snWorkStatus) {
        //优先检查当前扫描的SN前段工单的型号物料编码是否属于当前工单的投料单中的物料
        List<WsMaterial> wsMaterialList = wsMaterialRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        WorkSheet reUseWorkSheet = snWorkStatus.getSubWorkSheet().getWorkSheet();
        if (wsMaterialList.stream().anyMatch(wsMaterial -> wsMaterial.getMaterialId().equals(reUseWorkSheet.getPedigree().getMaterialId()))) {
            return new BaseClientDTO(Constants.OK);
        }
        List<PedigreeSnReuseConfig> pedigreeSnReuseConfigs = commonService.findPedigreeSnReuseConfig(workSheet.getPedigree());
        if (!ValidateUtils.isValid(pedigreeSnReuseConfigs)) {
            return new BaseClientDTO(Constants.KO, "当前SN不可复用!");
        }
        List<Pedigree> reUsePedigrees = Lists.newArrayList();
        commonService.findParentPedigreeGroupByLevel(snWorkStatus.getSubWorkSheet().getWorkSheet().getPedigree()).forEach((key, value) -> reUsePedigrees.addAll(value));
        if (pedigreeSnReuseConfigs.stream().noneMatch(pedigreeReuseConfig -> reUsePedigrees.stream().anyMatch(reUsePedigree -> reUsePedigree.getId().equals(pedigreeReuseConfig.getReusePedigree().getId())))) {
            return new BaseClientDTO(Constants.KO, "当前SN不可复用!");
        }
        return new BaseClientDTO(Constants.OK);
    }


    /**
     * SN工序全部完成时或者报废时更新子工单、总工单数据
     * 生产状态: 0-待投产;1-投产中;2-待返修;3-返修中;4-合格;5-报废
     *
     * @param snWorkStatus SN生产状态
     * <AUTHOR>
     * @date 2021-03-18
     **/
    @Transactional(rollbackFor = Exception.class)
    public void updateWorkSheetInfo(SnWorkStatus snWorkStatus) {
        SubWorkSheet subWorkSheet = snWorkStatus.getSubWorkSheet();
        WorkSheet workSheet = subWorkSheet.getWorkSheet();
        //报废或者维修分析都是不在走当前工单，需要计算不合格数
        if (snWorkStatus.getStatus() == Constants.INT_FIVE || snWorkStatus.getStatus() == Constants.INT_SIX) {
            subWorkSheet.setUnqualifiedNumber(subWorkSheet.getUnqualifiedNumber() + Constants.INT_ONE);
            workSheet.setUnqualifiedNumber(workSheet.getUnqualifiedNumber() + Constants.INT_ONE);
        }
        // 获取 所有子工单的不良数
        Long unqualifiedNumber = wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
        if (unqualifiedNumber != null && subWorkSheet.getUnqualifiedNumber() != unqualifiedNumber && unqualifiedNumber > subWorkSheet.getUnqualifiedNumber()) {
            int difference = unqualifiedNumber.intValue() - subWorkSheet.getUnqualifiedNumber();
            subWorkSheet.setUnqualifiedNumber(subWorkSheet.getUnqualifiedNumber() + difference);
            workSheet.setUnqualifiedNumber(workSheet.getUnqualifiedNumber() + difference);
        }
        //纯单支在线返修合格不更新子工单合格数，需要更新总工单合格数
        if (snWorkStatus.getReworkTime() > Constants.INT_ZERO && snWorkStatus.getLatestReworkSnWorkDetail().getSubWorkSheet().getId().equals(subWorkSheet.getId())) {
            if (snWorkStatus.getStatus() == Constants.INT_FOUR) {
                workSheet.setQualifiedNumber(workSheet.getQualifiedNumber() + Constants.INT_ONE);
                workSheet.setReworkQualifiedNumber(workSheet.getReworkQualifiedNumber() + Constants.INT_ONE);
            }
        } else {
            if (snWorkStatus.getStatus() == Constants.INT_FOUR) {
                workSheet.setQualifiedNumber(workSheet.getQualifiedNumber() + Constants.INT_ONE);
                subWorkSheet.setQualifiedNumber(subWorkSheet.getQualifiedNumber() + Constants.INT_ONE);
            }
            if (subWorkSheet.getQualifiedNumber() + subWorkSheet.getUnqualifiedNumber() == subWorkSheet.getNumber()) {
                subWorkSheet.setStatus(Constants.FINISH).setActualEndDate(LocalDateTime.now());
            }
        }
        if (workSheet.getQualifiedNumber() + workSheet.getUnqualifiedNumber() == workSheet.getNumber()) {
            workSheet.setStatus(Constants.FINISH).setActualEndDate(LocalDateTime.now());
        }
        if (subWorkSheet.getQualifiedNumber() + subWorkSheet.getUnqualifiedNumber() == subWorkSheet.getNumber()) {
            subWorkSheet.setStatus(Constants.FINISH).setActualEndDate(LocalDateTime.now());
            //如果当前工单为在线返修单则完成后需要更新对应的原始正常单数据
            if (workSheet.getCategory() == Constants.NEGATIVE_ONE) {
                Optional<WsRework> wsReworkOptional = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
                wsReworkOptional.ifPresent(wsRework -> {
                    WorkSheet originalWorkSheet = wsRework.getOriginalWorkSheet();
                    originalWorkSheet.setQualifiedNumber(originalWorkSheet.getQualifiedNumber() + subWorkSheet.getQualifiedNumber());
                    originalWorkSheet.setUnqualifiedNumber(originalWorkSheet.getUnqualifiedNumber() - subWorkSheet.getQualifiedNumber());
                    originalWorkSheet.setReworkQualifiedNumber(originalWorkSheet.getReworkQualifiedNumber() + subWorkSheet.getQualifiedNumber());
                    if (originalWorkSheet.getQualifiedNumber() + originalWorkSheet.getUnqualifiedNumber() == originalWorkSheet.getNumber()) {
                        originalWorkSheet.setStatus(Constants.FINISH).setActualEndDate(LocalDateTime.now());
                    }
                    workSheetRepository.save(originalWorkSheet);
                });
            }
        }
        workSheetRepository.save(workSheet);
        subWorkSheetRepository.save(subWorkSheet);
        //更新工单的子工单个数以及完成个数(包括正常、异常)
        subWorkSheetService.updateWorkSheetSubWsNumberInfo(workSheet);
    }

    /**
     * 更新单支与批量详情不良信息
     *
     * @param snWorkDetail    SN工作详情
     * @param unqualifiedItem 不良项目
     * @return void
     * <AUTHOR>
     * @date 2021-03-18
     **/
    @Transactional(rollbackFor = Exception.class)
    public void saveWorkDetailUnqualifiedItem(SnWorkDetail snWorkDetail, UnqualifiedItem unqualifiedItem, boolean isUpdateWsdInfo) {
        if (snWorkDetail.getResult() == Constants.INT_ZERO && null != unqualifiedItem) {
            SnUnqualifiedItem snUnqualifiedItem = new SnUnqualifiedItem();
            snUnqualifiedItem.setSn(snWorkDetail.getSn()).setStep(snWorkDetail.getStep()).setSubWorkSheet(snWorkDetail.getSubWorkSheet()).setUnqualifiedItem(unqualifiedItem).setDeleted(Constants.LONG_ZERO);
            snUnqualifiedItemRepository.save(snUnqualifiedItem);
            if (isUpdateWsdInfo) {
                List<WsStepUnqualifiedItem> wsStepUnqualifiedItemList = wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndDeleted(snWorkDetail.getSubWorkSheet().getId(), snWorkDetail.getStep().getId(), Constants.LONG_ZERO);
                WsStepUnqualifiedItem wsStepUnqualifiedItem = wsStepUnqualifiedItemList.stream().filter(wsStepUnqualifiedItemTemp -> wsStepUnqualifiedItemTemp.getUnqualifiedItem().getId()
                        .equals(unqualifiedItem.getId())).findAny().orElseGet(WsStepUnqualifiedItem::new);
                wsStepUnqualifiedItem.setStep(snWorkDetail.getStep())
                        .setSubWorkSheet(snWorkDetail.getSubWorkSheet())
                        .setUnqualifiedItem(unqualifiedItem)
                        .setOperatorId(snWorkDetail.getOperatorId())
                        .setNumber(wsStepUnqualifiedItem.getNumber() + Constants.INT_ONE)
                        .setRecordDate(LocalDate.now())
                        .setFlag(false);
                wsStepUnqualifiedItemRepository.save(wsStepUnqualifiedItem);
            }
        }
    }

    /**
     * 更新批量的工序生产信息
     *
     * @param snWorkDetail SN工作详情
     * @param wsStep       当前定制工序
     * @return void
     * <AUTHOR>
     * @date 2021-03-18
     **/
    @Transactional(rollbackFor = Exception.class)
    public BatchWorkDetail updateBatchWsdInfo(SnWorkDetail snWorkDetail, WsStep wsStep) {
        int qualifiedNumber = snWorkDetail.getResult() == Constants.INT_ONE ? Constants.INT_ONE : Constants.INT_ZERO;
        int unQualifiedNumber = snWorkDetail.getResult() == Constants.INT_ZERO ? Constants.INT_ONE : Constants.INT_ZERO;
        BatchWorkDetail batchWorkDetail = batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(snWorkDetail.getSubWorkSheet().getId(), snWorkDetail.getStep().getId(), Constants.LONG_ZERO).orElse(null);
        if (null == batchWorkDetail) {
            batchWorkDetail = new BatchWorkDetail();
            batchWorkDetail.setSubWorkSheet(snWorkDetail.getSubWorkSheet())
                    .setFinish(Constants.INT_ZERO)
                    .setStartDate(snWorkDetail.getStartDate())
                    .setStep(snWorkDetail.getStep())
                    .setOperatorId(snWorkDetail.getOperatorId())
                    .setWorkCell(snWorkDetail.getWorkCell())
                    .setDeleted(Constants.LONG_ZERO);
        }
        batchWorkDetail.setFinishNumber(batchWorkDetail.getFinishNumber() + Constants.INT_ONE)
                .setInputNumber(batchWorkDetail.getInputNumber() + Constants.INT_ONE)
                .setUnqualifiedNumber(batchWorkDetail.getUnqualifiedNumber() + unQualifiedNumber)
                .setQualifiedNumber(batchWorkDetail.getQualifiedNumber() + qualifiedNumber)
                .setEffectNumber(batchWorkDetail.getEffectNumber() + Constants.INT_ONE)
                .setTransferNumber(snWorkDetail.getResult() == Constants.INT_ONE ? batchWorkDetail.getTransferNumber() + Constants.INT_ONE : batchWorkDetail.getTransferNumber())
                .setWorkHour(batchWorkDetail.getWorkHour() + snWorkDetail.getWorkHour());
        //第一个工序若投产数和工单数量一致则认为完成，否则都需要判断前置工序完成且往下流转数和当前工序投入数相等时才认为完成
        if (StringUtils.isBlank(wsStep.getPreStepId()) && batchWorkDetail.getInputNumber() == snWorkDetail.getSubWorkSheet().getNumber()) {
            batchWorkDetail.setFinish(Constants.INT_ONE);
        } else if (StringUtils.isNotBlank(wsStep.getPreStepId())) {
            final int inputNumber = batchWorkDetail.getInputNumber();
            List<Long> preStepIds = Arrays.stream(wsStep.getPreStepId().split(Constants.STR_COMMA)).map(Long::parseLong).collect(Collectors.toList());
            List<BatchWorkDetail> preBatchWorkDetails = batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdInAndDeleted(snWorkDetail.getSubWorkSheet().getId(), preStepIds, Constants.LONG_ZERO);
            boolean isNotFinish = preBatchWorkDetails.stream()
                    .anyMatch(preBatchWorkDetail -> preBatchWorkDetail.getFinish() == Constants.INT_ZERO || preBatchWorkDetail.getTransferNumber() != inputNumber);
            batchWorkDetail.setFinish(isNotFinish ? Constants.INT_ZERO : Constants.INT_ONE);
        }
        batchWorkDetail.setEndDate(batchWorkDetail.getFinish() == Constants.INT_ONE ? LocalDateTime.now() : null);
        batchWorkDetailRepository.save(batchWorkDetail);
        if(batchWorkDetail.getFinish() == ConstantsEnum.FINISH_STATUS.getCategoryName()){
            SubWorkSheet subWorkSheet = snWorkDetail.getSubWorkSheet();
            Long stepCompNumber = batchWorkDetailRepository.countBySubWorkSheetIdAndFinishAndDeleted(subWorkSheet.getId(),ConstantsEnum.FINISH_STATUS.getCategoryName(), net.airuima.constant.Constants.LONG_ZERO);
            subWorkSheet.setStepCompNumber(Objects.nonNull(stepCompNumber)?stepCompNumber.intValue(): net.airuima.constant.Constants.INT_ZERO);
            subWorkSheetRepository.save(subWorkSheet);
        }
        return batchWorkDetail;
    }


    /**
     * 更新批量详情及单支详情物料批次信息
     *
     * @param batchWorkDetail         工单详情
     * @param snWorkDetail            SN工作详情
     * @param clientSaveSnStepInfoDto 请求参数
     * <AUTHOR>
     * @date 2021-03-18
     **/
    @Transactional(rollbackFor = Exception.class)
    public void saveWorkDetailMaterialBatch(BatchWorkDetail batchWorkDetail, SnWorkDetail snWorkDetail, ClientSaveSnStepInfoDTO clientSaveSnStepInfoDto) {
        if (ValidateUtils.isValid(clientSaveSnStepInfoDto.getMaterialBatchInfoList())) {
            //更新批量详情物料批次信息
            if (null != batchWorkDetail) {
                clientSaveSnStepInfoDto.getMaterialBatchInfoList().forEach(materialBatchInfo -> {
                    Optional<BatchWorkDetailMaterialBatch> batchWorkDetailMaterialBatchOptional = Optional.empty();
                    if (materialBatchInfo.getMaterialId() != null && StringUtils.isNotBlank(materialBatchInfo.getMaterialBatch())) {
                        batchWorkDetailMaterialBatchOptional = batchWorkDetailMaterialBatchRepository.findByBatchWorkDetailIdAndMaterialIdAndMaterialBatchAndDeleted(batchWorkDetail.getId(), materialBatchInfo.getMaterialId(), materialBatchInfo.getMaterialBatch(), Constants.LONG_ZERO);
                    }
                    if (!batchWorkDetailMaterialBatchOptional.isPresent() && materialBatchInfo.getMaterialId() != null && StringUtils.isBlank(materialBatchInfo.getMaterialBatch())) {
                        batchWorkDetailMaterialBatchOptional = batchWorkDetailMaterialBatchRepository.findByBatchWorkDetailIdAndMaterialIdAndDeleted(batchWorkDetail.getId(), materialBatchInfo.getMaterialId(), Constants.LONG_ZERO);
                    }
                    if (!batchWorkDetailMaterialBatchOptional.isPresent() && materialBatchInfo.getMaterialId() == null && StringUtils.isNotBlank(materialBatchInfo.getMaterialBatch())) {
                        batchWorkDetailMaterialBatchOptional = batchWorkDetailMaterialBatchRepository.findByBatchWorkDetailIdAndMaterialBatchAndDeleted(batchWorkDetail.getId(), materialBatchInfo.getMaterialBatch(), Constants.LONG_ZERO);
                    }
                    if (!batchWorkDetailMaterialBatchOptional.isPresent()) {
                        BatchWorkDetailMaterialBatch batchWorkDetailMaterialBatch = new BatchWorkDetailMaterialBatch();
                        batchWorkDetailMaterialBatch.setBatchWorkDetail(batchWorkDetail)
                                .setMaterialId(materialBatchInfo.getMaterialId())
                                .setMaterialBatch(materialBatchInfo.getMaterialBatch())
                                .setSupplierId(ValidateUtils.isValid(materialBatchInfo.getSupplierCode()) ? rbaseSupplierProxy.findByCodeAndDeleted(materialBatchInfo.getSupplierCode(),Constants.LONG_ZERO).getId() : null)
                                .setSerial(materialBatchInfo.getSerial()).setDeleted(Constants.LONG_ZERO);
                        batchWorkDetailMaterialBatchRepository.save(batchWorkDetailMaterialBatch);
                    }
                });
            }
            //更新单支详情物料批次信息
            if (null != snWorkDetail) {
                clientSaveSnStepInfoDto.getMaterialBatchInfoList().forEach(materialBatchInfo -> {
                    SnWorkDetailMaterialBatch snWorkDetailMaterialBatch = new SnWorkDetailMaterialBatch();
                    snWorkDetailMaterialBatch.setMaterialBatch(materialBatchInfo.getMaterialBatch())
                            .setMaterialId(materialBatchInfo.getMaterialId())
                            .setSupplierId(ValidateUtils.isValid(materialBatchInfo.getSupplierCode()) ? rbaseSupplierProxy.findByCodeAndDeleted(materialBatchInfo.getSupplierCode(),Constants.LONG_ZERO).getId() : null)
                            .setSnWorkDetail(snWorkDetail).setDeleted(Constants.LONG_ZERO);
                    snWorkDetailMaterialBatchRepository.save(snWorkDetailMaterialBatch);
                });
            }
        }
    }

    /**
     * 保存批量详情设备及单支详情设备信息
     *
     * @param batchWorkDetail         批量工作详情
     * @param snWorkDetail            SN工作详情
     * @param clientSaveSnStepInfoDto 请求参数
     * <AUTHOR>
     * @date 2021-03-18
     **/
    @Transactional(rollbackFor = Exception.class)
    public void saveWorkDetailEquipment(BatchWorkDetail batchWorkDetail, SnWorkDetail snWorkDetail, ClientSaveSnStepInfoDTO clientSaveSnStepInfoDto) {
        if (ValidateUtils.isValid(clientSaveSnStepInfoDto.getEquipmentInfoList())) {
            if (null != batchWorkDetail) {
                clientSaveSnStepInfoDto.getEquipmentInfoList().forEach(equipmentInfo -> {
                    BatchWorkDetailFacility batchWorkDetailFacility = batchWorkDetailFacilityRepository.findByBatchWorkDetailIdAndFacilityIdAndDeleted(batchWorkDetail.getId(), equipmentInfo.getEquipmentId(), Constants.LONG_ZERO).orElseGet(BatchWorkDetailFacility::new);
                    if (null == batchWorkDetailFacility.getId()) {
                        batchWorkDetailFacility.setFacilityId(equipmentInfo.getEquipmentId());
                        batchWorkDetailFacility.setBatchWorkDetail(batchWorkDetail).setDeleted(Constants.LONG_ZERO);
                        batchWorkDetailFacilityRepository.save(batchWorkDetailFacility);
                    }
                });
            }
            if (null != snWorkDetail) {
                clientSaveSnStepInfoDto.getEquipmentInfoList().forEach(equipmentInfo -> {
                    SnWorkDetailFacility snWorkDetailFacility = new SnWorkDetailFacility();
                    snWorkDetailFacility.setSnWorkDetail(snWorkDetail)
                            .setFacilityId(equipmentInfo.getEquipmentId()).setDeleted(Constants.LONG_ZERO);
                    snWorkDetailFacilityRepository.save(snWorkDetailFacility);
                });
            }
        }
    }

    /**
     * 扣除工单物料在线库存
     *
     * @param workSheet             总工单
     * @param step                  工序
     * @param materialBatchInfoList 物料批次列表
     * @param number                完成数量
     * <AUTHOR>
     * @date 2021-03-18
     **/
    @Transactional(rollbackFor = Exception.class)
    public void subtractOnlineInventory(WorkSheet workSheet, WorkCell workCell, Step step, List<ClientSaveSnStepInfoDTO.MaterialBatchInfo> materialBatchInfoList, int number) {
        //获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = commonService.findSnapshotWorkFlow(workSheet, null,step);
        //获取当前工序需要扣料的配置信息
        List<PedigreeStepMaterialRule> pedigreeStepMaterialRuleList = commonService.findPedigreeStepMaterialRule(workSheet.getClientId(),workSheet.getPedigree().getId(), snapshotWorkFlow.getId(), step.getId()).stream().filter(PedigreeStepMaterialRule::getIsDeduct).collect(Collectors.toList());
        if (ValidateUtils.isValid(pedigreeStepMaterialRuleList)) {
            //工序生产过程物料库存管控级别(0:不管控物料库存;1:总工单物料库存;2:工位物料库存)
            int materialControlLevel = commonService.getMaterialControlLevel();
            pedigreeStepMaterialRuleList.forEach(pedigreeStepMaterialRule -> {
                AtomicReference<Double> reduceNumber = new AtomicReference<>(NumberUtils.multiply(number, pedigreeStepMaterialRule.getProportion()).doubleValue());
                Optional<WsMaterial> wsMaterialOptional = wsMaterialRepository.findByWorkSheetIdAndMaterialIdAndDeleted(workSheet.getId(), pedigreeStepMaterialRule.getMaterialId(), Constants.LONG_ZERO);
                wsMaterialOptional.ifPresent(wsMaterial -> {
                    //获取包括替换料在内的投料单信息
                    List<WsMaterial> wsMaterialList = wsMaterialRepository.findByWorkSheetIdAndOriginMaterialIdAndDeleted(workSheet.getId(), wsMaterialOptional.get().getOriginMaterialId(), Constants.LONG_ZERO);
                    //若未配置物料库存管控级别或者级别为工位物料库存
                    if (materialControlLevel == Constants.INT_TWO) {
                        //获取当前工位对应的包括替换料在内的库存数据
                        List<WsWorkCellMaterialBatch> wsWorkCellMaterialBatchList = wsWorkCellMaterialBatchRepository.findByWorkSheetIdAndWorkCellIdAndMaterialIdInAndLeftNumberGreaterThanAndDeleted(workSheet.getId(), workCell.getId(),
                                wsMaterialList.stream().map(WsMaterial::getMaterialId).collect(Collectors.toList()), Constants.DOUBLE_ZERRO, Constants.LONG_ZERO);
                        if (ValidateUtils.isValid(wsWorkCellMaterialBatchList)) {
                            //将库存数据按照从小到大的顺序排序
                            List<WsWorkCellMaterialBatch> sortedWsWorkCellMaterialBatchList = wsWorkCellMaterialBatchList.stream().sorted(Comparator.comparing(WsWorkCellMaterialBatch::getLeftNumber)).collect(Collectors.toList());
                            sortedWsWorkCellMaterialBatchList.forEach(wsWorkCellMaterialBatch -> {
                                if (wsWorkCellMaterialBatch.getLeftNumber() > reduceNumber.get()) {
                                    wsWorkCellMaterialBatch.setLeftNumber(NumberUtils.subtract(wsWorkCellMaterialBatch.getLeftNumber(), reduceNumber.get()).doubleValue());
                                    reduceNumber.set(Constants.DOUBLE_ZERRO);
                                } else {
                                    reduceNumber.set(NumberUtils.subtract(reduceNumber.get(), wsWorkCellMaterialBatch.getLeftNumber()).doubleValue());
                                    wsWorkCellMaterialBatch.setLeftNumber(Constants.DOUBLE_ZERRO);
                                }
                                wsWorkCellMaterialBatchRepository.save(wsWorkCellMaterialBatch);
                            });
                        }
                    } else if (materialControlLevel == Constants.INT_ONE) {
                        //物料库存管控级别或者级别为总工单物料库存
                        List<WsMaterialBatch> wsMaterialBatchList = wsMaterialBatchRepository.findByWorkSheetIdAndMaterialIdInAndLeftNumberIsGreaterThanAndDeleted(workSheet.getId(), wsMaterialList.stream().map(WsMaterial::getMaterialId).collect(Collectors.toList()), Constants.DOUBLE_ZERRO, Constants.LONG_ZERO);
                        if (ValidateUtils.isValid(wsMaterialBatchList)) {
                            List<WsMaterialBatch> sortedWsMaterialBatchList = wsMaterialBatchList.stream().sorted(Comparator.comparing(WsMaterialBatch::getLeftNumber)).collect(Collectors.toList());
                            //获取扫描的批次与工单批次库存匹配的集合数据,扣减员工扫描的物料批次的剩余库存
                            List<WsMaterialBatch> matchedWsMaterialBatchList = sortedWsMaterialBatchList.stream().filter(wsMaterialBatch -> materialBatchInfoList.stream().anyMatch(materialBatchInfo -> materialBatchInfo.getMaterialId().equals(wsMaterialBatch.getMaterialId()) && materialBatchInfo.getMaterialBatch().equals(wsMaterialBatch.getBatch()))).collect(Collectors.toList());
                            matchedWsMaterialBatchList.forEach(wsMaterialBatch -> {
                                if (wsMaterialBatch.getLeftNumber() > reduceNumber.get()) {
                                    wsMaterialBatch.setLeftNumber(NumberUtils.subtract(wsMaterialBatch.getLeftNumber(), reduceNumber.get()).doubleValue());
                                    reduceNumber.set(Constants.DOUBLE_ZERRO);
                                } else {
                                    reduceNumber.set(NumberUtils.subtract(reduceNumber.get(), wsMaterialBatch.getLeftNumber()).doubleValue());
                                    wsMaterialBatch.setLeftNumber(Constants.DOUBLE_ZERRO);
                                }
                                wsMaterialBatchRepository.save(wsMaterialBatch);
                            });
                        }
                    }
                });
            });
        }
    }

    /**
     * 非纯单支模式下校验SN是否合法
     *
     * @param clientSnDto SN请求工序参数
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO 结果信息
     **/
    @Override
    @Transactional(readOnly = true)
    public BaseClientDTO verification(ClientSnDTO clientSnDto) {
        Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findByIdAndDeleted(clientSnDto.getSubWsId(), Constants.LONG_ZERO);
        if (!subWorkSheetOptional.isPresent()) {
            return new BaseClientDTO(Constants.KO, "子工单不存在!");
        }
        Optional<Step> stepOptional = stepRepository.findByIdAndDeleted(clientSnDto.getStepId(), Constants.LONG_ZERO);
        if (!stepOptional.isPresent()) {
            return new BaseClientDTO(Constants.KO, "工序不存在!");
        }
        SubWorkSheet subWorkSheet = subWorkSheetOptional.get();
        WorkSheet workSheet = subWorkSheet.getWorkSheet();
        Step step = stepOptional.get();
        SnWorkStatus snWorkStatus = snWorkStatusRepository.findBySnAndDeleted(clientSnDto.getSn(), Constants.LONG_ZERO).orElse(null);
        //查找对应工单定制流程(后续存在转工艺)
        List<WsStep> wsStepList = wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(wsStepList)) {
            wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(subWorkSheet.getWorkSheet().getId(), Constants.LONG_ZERO);
        }
        WsStep currWsStep = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(stepOptional.get().getId())).collect(Collectors.toList()).get(Constants.INT_ZERO);

        //判断当前SN是否为复用SN
        boolean isReUseSn = false;
        //当前工单为正常单且当前工单ID与SN状态对应的工单ID不一致时需要检查当前工单第一个工序是否为复用SN关联工序
        if (workSheet.getCategory() > Constants.INT_ZERO && null != snWorkStatus
                && snWorkStatus.getSubWorkSheet().getWorkSheet().getCategory() > Constants.INT_ZERO
                && !snWorkStatus.getSubWorkSheet().getWorkSheet().getId().equals(workSheet.getId())) {
            PedigreeConfig pedigreeConfig = commonService.findPedigreeConfig(workSheet.getPedigree());
            if (null != pedigreeConfig && pedigreeConfig.getIsReuseSN()) {
                isReUseSn = true;
            }
        }
        //对于复用SN转工单投产的需要验证前段工单的工序是否都已全部完成，否则禁止转单生产
        if (isReUseSn && snWorkStatus.getStatus() <= Constants.INT_ONE) {
            return new BaseClientDTO(Constants.KO, "当前SN尚未完成禁止转工单投产!");
        }
        if (isReUseSn) {
            BaseClientDTO baseClientDto = this.validateSnReUse(workSheet, snWorkStatus);
            if (Constants.KO.equals(baseClientDto.getStatus())) {
                return new ClientGetSnStepInfoDTO<>(baseClientDto);
            }
        }
        if (snWorkStatus != null) {
            if (!isReUseSn &&  subWorkSheet.getWorkSheet().getCategory() != WsEnum.OFFLINE_RE_WS.getCategory() && snWorkStatus.getStatus() == SnWorkStatusEnum.QUALIFIED.getStatus()) {
                return new BaseClientDTO(Constants.KO, "SN(" + clientSnDto.getSn() + ")已完成!");
            }
            if (snWorkStatus.getStatus() == SnWorkStatusEnum.SCRAP.getStatus()) {
                return new BaseClientDTO(Constants.KO, "SN(" + clientSnDto.getSn() + ")已报废!");
            }
            //查找当前工序对应的SN详情是否存在
            Optional<SnWorkDetail> snWorkDetailOptional = snWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndSnAndReworkTimeAndDeleted(subWorkSheet.getId(), step.getId(), clientSnDto.getSn(), snWorkStatus.getReworkTime(), Constants.LONG_ZERO);
            if (snWorkDetailOptional.isPresent()) {
                return new BaseClientDTO(Constants.KO, "当前工序(" + step.getCode() + ")SN(" + clientSnDto.getSn() + ")已完成!");
            }
        }

        //获取当前sn的最新一条详情记录
        Optional<SnWorkDetail> snWorkDetailOptional = snWorkDetailRepository.findTop1BySubWorkSheetIdAndSnAndDeletedOrderByIdDesc(subWorkSheet.getId(), clientSnDto.getSn(), Constants.LONG_ZERO);
        BaseClientDTO baseClientDto = new BaseClientDTO();
        //在线返修单
        if (subWorkSheet.getWorkSheet().getCategory() == WsEnum.ONLINE_RE_WS.getCategory()) {
            baseClientDto = validDateOnlineReWs(subWorkSheet, clientSnDto, wsStepList, step);
        }

        //正常单
        if (subWorkSheet.getWorkSheet().getCategory() == WsEnum.NORMAL_WS.getCategory()) {
            baseClientDto = validDateNormalWs(isReUseSn, subWorkSheet, clientSnDto, snWorkStatus, wsStepList,currWsStep);
        }
        //离线单
        if (subWorkSheet.getWorkSheet().getCategory() == WsEnum.OFFLINE_RE_WS.getCategory() && snWorkStatus != null) {
            //第一道工序只要不是投产中和返修中, 都可以录入
            if(StringUtils.isBlank(currWsStep.getPreStepId()) && ((snWorkStatus.getStatus() == SnWorkStatusEnum.IN_THE_REPAIR.getStatus()) || (snWorkStatus.getStatus() == SnWorkStatusEnum.PUT_INTO_PRODUCTION.getStatus()))){
                return new BaseClientDTO(Constants.KO, "SN(" + clientSnDto.getSn() + ")投产中!");
            }else {
                if (snWorkStatus.getSubWorkSheet().getId().equals(subWorkSheet.getId()) && snWorkStatus.getStatus() != SnWorkStatusEnum.IN_THE_REPAIR.getStatus()){
                    return new BaseClientDTO(Constants.KO, "SN(" + clientSnDto.getSn() + ")状态不是返修中!");
                }
            }
        }
        return baseClientDto.getStatus().equals(Constants.KO) ? baseClientDto : baseClientDto.setMessage(snWorkDetailOptional.map(snWorkDetail -> snWorkDetail.getSubWorkSheet().getSerialNumber()).orElse(null));
    }

    /**
     * 验证当前sn 是否属于当前子工单
     *
     * @param subWorkSheet 子工单
     * @param clientSnDto  sn
     * @param wsStepList   工序快照列表
     * @param step         当前工序
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO
     * <AUTHOR>
     * @date 2022/11/18
     */
    public BaseClientDTO validDateOnlineReWs(SubWorkSheet subWorkSheet, ClientSnDTO clientSnDto, List<WsStep> wsStepList, Step step) {

        SnWorkStatus snWorkStatus = snWorkStatusRepository.findBySnAndDeleted(clientSnDto.getSn(), Constants.LONG_ZERO).orElse(null);
        AtomicBoolean hasSnControlMode = new AtomicBoolean(false);
        //判断当前在线反是以容器返修且未输入sn,放行
        if (ObjectUtils.isEmpty(snWorkStatus)) {
            List<SnRework> snReworkList = snReworkRepository.findByWsReworkReworkWorkSheetIdAndDeletedAndContainerIsNotNull(subWorkSheet.getWorkSheet().getId(), Constants.LONG_ZERO);
            hasSnControlMode(step.getId(),step.getId(), wsStepList, hasSnControlMode);
            if (ValidateUtils.isValid(snReworkList) && hasSnControlMode.get()) {
                return new BaseClientDTO(Constants.OK, clientSnDto.getSn());
            }
            if (snWorkStatus == null) {
                return new BaseClientDTO(Constants.KO, "SN(" + clientSnDto.getSn() + ")不存在");
            }
        }
        //判断当前sn是否属于当前工单
        Optional<SnRework> snReworkOptional = snReworkRepository.findTop1BySnWorkStatusIdAndDeletedOrderByIdDesc(snWorkStatus.getId(), Constants.LONG_ZERO);
        List<SnRework> snReworkList = snReworkRepository.findByWsReworkReworkWorkSheetIdAndDeletedAndContainerIsNotNull(subWorkSheet.getWorkSheet().getId(), Constants.LONG_ZERO);
        if (!snReworkOptional.isPresent()) {
            if (!ValidateUtils.isValid(snReworkList)) {
                return new BaseClientDTO(Constants.KO, "SN(" + clientSnDto.getSn() + ")不存在返修记录!");
            } else {
                if (!snWorkStatus.getSubWorkSheet().getId().equals(subWorkSheet.getId())) {
                    return new BaseClientDTO(Constants.KO, "SN(" + clientSnDto.getSn() + ")不属于当前在线反修工单!");
                }
            }
        } else {
            SnRework snRework = snReworkOptional.get();
            if (!snRework.getWsRework().getReworkWorkSheet().getId().equals(subWorkSheet.getWorkSheet().getId())) {
                return new BaseClientDTO(Constants.KO, "SN(" + clientSnDto.getSn() + ")不属于当前在线反修工单!");
            }
        }

        return new BaseClientDTO(Constants.OK);
    }

    /**
     * 验证当前sn 是否属于当前子工单
     *
     * @param subWorkSheet 子工单
     * @param clientSnDto  sn
     * @param snWorkStatus sn状态
     * @param currWsStep   当前工序快照
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO
     * <AUTHOR>
     * @date 2022/11/18
     */
    public BaseClientDTO validDateNormalWs(boolean isSnReUse, SubWorkSheet subWorkSheet, ClientSnDTO clientSnDto, SnWorkStatus snWorkStatus, List<WsStep> wsStepList,WsStep currWsStep) {
        if (!isSnReUse && snWorkStatus != null && !subWorkSheet.getId().equals(snWorkStatus.getSubWorkSheet().getId())) {
            return new BaseClientDTO(Constants.KO, "SN(" + clientSnDto.getSn() + ")不属于当前工单!");
        }
        if (!isSnReUse && snWorkStatus != null && currWsStep.getCategory() == StepCategoryEnum.NORMAL_STEP.getStatus() && snWorkStatus.getStatus() != SnWorkStatusEnum.PUT_INTO_PRODUCTION.ordinal()) {
            return new BaseClientDTO(Constants.KO, "正常工序SN(" + clientSnDto.getSn() + ")状态不是投产中!");
        }
        if (Objects.isNull(snWorkStatus)){
            AtomicBoolean hasSnControlMode = new AtomicBoolean(false);
            hasSnControlMode(currWsStep.getStep().getId(),currWsStep.getStep().getId(), wsStepList, hasSnControlMode);
            if (hasSnControlMode.get()){
                return new BaseClientDTO(Constants.KO,"非首道单支工序禁止绑定SN（不属于当前工单）");
            }
        }
        return new BaseClientDTO(Constants.OK);
    }

    /**
     * 判断当前工序之前是否存在SN管控模式的工序
     *
     * @param currentStepId     传入的当前工序参照
     * @param stepId           当前工序ID
     * @param wsStepList       定制流程集合
     * @param hasSnControlMode 是否存在SN管控模式工序
     * @return void
     * <AUTHOR>
     * @date 2021-05-24
     **/
    @Transactional(readOnly = true)
    public void hasSnControlMode(Long currentStepId,Long stepId, List<WsStep> wsStepList, AtomicBoolean hasSnControlMode) {
        //判断当前工序的管控模式是否是单支模式
        if (wsStepList.stream().anyMatch(wsStep -> wsStep.getStep().getId().equals(stepId) && wsStep.getControlMode() == Constants.INT_ONE) && !stepId.equals(currentStepId)) {
            hasSnControlMode.set(true);
            return;
        }
        //获得当前工序
        WsStep currWsStep = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(stepId)).collect(Collectors.toList()).get(Constants.INT_ZERO);
        String preStepIds = currWsStep.getPreStepId();
        if (ValidateUtils.isValid(preStepIds)) {
            String[] preStepIdArray = preStepIds.split(Constants.STR_COMMA);
            for (String preStepId : preStepIdArray) {
                hasSnControlMode(currentStepId,Long.parseLong(preStepId), wsStepList, hasSnControlMode);
            }
        }
    }

    /**
     * 获取当前sn 最新状态
     *
     * @param sn
     * @return SnWorkStatusDTO
     * <AUTHOR>
     * @date 2022/10/17
     */
    @FetchMethod
    @Transactional(readOnly = true)
    public SnWorkStatusDTO getSnWorkStatusInfo(String sn) {

        SnWorkStatus snWorkStatus = snWorkStatusRepository.findBySnAndDeleted(sn, Constants.LONG_ZERO).orElse(null);
        if (null == snWorkStatus) {
            return new SnWorkStatusDTO(Constants.KO, "SN不存在!");
        }
        WorkSheet workSheet = snWorkStatus.getSubWorkSheet().getWorkSheet();
        Optional<SnWorkDetail> snWorkDetailOptional = snWorkDetailRepository.findTop1BySnAndDeletedOrderByIdDesc(sn, Constants.LONG_ZERO);
        SnWorkStatusDTO snWorkStatusDto = new SnWorkStatusDTO();
        if (snWorkDetailOptional.isPresent()) {
            workSheet = snWorkDetailOptional.get().getSubWorkSheet().getWorkSheet();
            snWorkStatusDto.setSubWsSerialNumber(snWorkDetailOptional.get().getSubWorkSheet().getSerialNumber());
        } else {
            snWorkStatusDto.setSubWsSerialNumber(snWorkStatus.getSubWorkSheet().getSerialNumber());
        }
        snWorkStatusDto.setWsSerialNumber(workSheet.getSerialNumber())
                .setMaterialCode(workSheet.getPedigree().getMaterialDto().getCode())
                .setMaterialDescription(workSheet.getPedigree().getMaterialDto().getDescription())
                .setMaterialSpecification(workSheet.getPedigree().getMaterialDto().getSpecification())
                .setOrgCode(workSheet.getOrganizationDto() != null ? workSheet.getOrganizationDto().getCode() : null)
                .setWorkLineCode(workSheet.getWorkLine() != null ? workSheet.getWorkLine().getCode() : null)
                .setPedigreeCode(workSheet.getPedigree().getCode())
                .setClientCode(Objects.nonNull(workSheet.getClientDTO()) ? workSheet.getClientDTO().getCode() : null)
                .setClientName(Objects.nonNull(workSheet.getClientDTO()) ? workSheet.getClientDTO().getName() : null)
                .setSnStatus(snWorkStatus.getStatus()).setStatus(Constants.OK);
        return snWorkStatusDto;
    }

    /**
     * 替换sn标签
     *
     * @param snReplaceDtoList 替换信息列表
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO
     * <AUTHOR>
     * @date 2023/2/23
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseClientDTO snReplace(List<SnReplaceDTO> snReplaceDtoList) {
        if (!ValidateUtils.isValid(snReplaceDtoList)) {
            return new BaseClientDTO(Constants.KO, "替换信息不存在");
        }
        //去重
        List<SnReplaceDTO> snReplaceList = snReplaceDtoList.stream().distinct().collect(Collectors.toList());
        //获取原始标以及替换标
        List<String> replaceSnList = snReplaceList.stream().map(SnReplaceDTO::getReplaceSn).distinct().collect(Collectors.toList());
        List<String> originalSnList = snReplaceList.stream().map(SnReplaceDTO::getOriginalSn).distinct().collect(Collectors.toList());

        if (replaceSnList.size() != originalSnList.size()) {
            return new BaseClientDTO(Constants.KO, "原标与替换标不能一一对应");
        }

        List<SnWorkStatus> originalSnWorkStatuses = snWorkStatusRepository.findBySnInAndDeleted(originalSnList, Constants.LONG_ZERO);
        //原始标必须都存在
        if (!ValidateUtils.isValid(originalSnWorkStatuses) || (ValidateUtils.isValid(originalSnWorkStatuses) && originalSnWorkStatuses.size() != originalSnList.size())) {
            if (ValidateUtils.isValid(originalSnWorkStatuses)) {
                originalSnList = originalSnList.stream().filter(origSn -> !originalSnWorkStatuses.stream().anyMatch(snWorkStatus -> snWorkStatus.getSn().equals(origSn))).collect(Collectors.toList());
            }
            return new BaseClientDTO(Constants.KO, "原标签" + originalSnList.toString() + "不存在");
        }

        List<SnWorkStatus> replaceSnWorkStatuses = snWorkStatusRepository.findBySnInAndDeleted(replaceSnList, Constants.LONG_ZERO);
        //替换标必须是新标
        if (ValidateUtils.isValid(replaceSnWorkStatuses)) {
            return new BaseClientDTO(Constants.KO, "替换标签" + replaceSnWorkStatuses.stream().map(SnWorkStatus::getSn).collect(Collectors.toList()).toString() + "已存在");
        }
        snReplaceList.stream().forEach(snReplaceDto -> {
            Optional<SnWorkStatus> snWorkStatusOptional = originalSnWorkStatuses.stream().filter(snWorkStatus -> snWorkStatus.getSn().equals(snReplaceDto.getOriginalSn())).findFirst();
            if (snWorkStatusOptional.isPresent()) {
                SnWorkStatus snWorkStatus = snWorkStatusOptional.get();
                snWorkStatus.setSn(snReplaceDto.getReplaceSn());
                //修改SN详情
                snWorkDetailRepository.snWorkDetailReplaceSn(snReplaceDto.getOriginalSn(), snReplaceDto.getReplaceSn());
                //修改sn不良项目
                snUnqualifiedItemRepository.snUnqualifiedReplaceSn(snReplaceDto.getOriginalSn(), snReplaceDto.getReplaceSn());
                snWorkStatusRepository.save(snWorkStatus);
            }
        });
        //修改烘烤历史记录SN
        bakeCycleBakeAgeingModelServices[0].replaceBakeHistorySn(snReplaceList);
        //修改温循历史记录SN
        bakeCycleBakeAgeingModelServices[0].replaceCycleBakeHistorySn(snReplaceList);
        //修改老化历史记录SN
        bakeCycleBakeAgeingModelServices[0].replaceAgeingHistorySn(snReplaceList);
        return new BaseClientDTO(Constants.OK);
    }

    /**
     * 工序保存完后后续操作扩展接口
     *
     * @param clientSaveSnStepInfoDto 保存工序请求参数
     */
    @Override
    public void afterStepSnSaved(ClientSaveSnStepInfoDTO clientSaveSnStepInfoDto) {
        IClientSnService.super.afterStepSnSaved(clientSaveSnStepInfoDto);
    }
}
