package net.airuima.rworker.service.client;

import com.google.common.util.concurrent.AtomicDouble;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.WipLedgerOperationEnum;
import net.airuima.rbase.constant.WsEnum;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepMaterialRule;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.rbase.domain.procedure.batch.WsMaterial;
import net.airuima.rbase.domain.procedure.material.WsMaterialBatch;
import net.airuima.rbase.domain.procedure.material.WsWorkCellMaterialBatch;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.client.ClientMaterialDTO;
import net.airuima.rbase.dto.client.ClientWsMaterialDTO;
import net.airuima.rbase.dto.pedigree.SnRuleDTO;
import net.airuima.rbase.proxy.bom.RbaseMaterialProxy;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WsReworkRepository;
import net.airuima.rbase.repository.procedure.batch.WsMaterialRepository;
import net.airuima.rbase.repository.procedure.material.WsMaterialBatchRepository;
import net.airuima.rbase.repository.procedure.material.WsWorkCellMaterialBatchRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.procedure.material.WsCheckMaterialDetailService;
import net.airuima.rbase.service.wip.PIWipLedgerService;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.rbase.util.ValidateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.klock.annotation.Klock;
import org.springframework.boot.autoconfigure.klock.model.LockTimeoutStrategy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * RWork物料相关Resource
 *
 * <AUTHOR>
 * @date 2020/12/28
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ClientMaterialService {

    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private WorkCellRepository workCellRepository;
    @Autowired
    private StepRepository stepRepository;
    @Autowired
    private RbaseMaterialProxy rbaseMaterialProxy;
    @Autowired
    private WsMaterialRepository wsMaterialRepository;
    @Autowired
    private WsMaterialBatchRepository wsMaterialBatchRepository;
    @Autowired
    private WsWorkCellMaterialBatchRepository wsWorkCellMaterialBatchRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private WsCheckMaterialDetailService wsCheckMaterialDetailService;
    @Autowired
    private PIWipLedgerService wipLedgerServices;
    @Autowired
    private WsReworkRepository wsReworkRepository;

    /**
     * 保存上料信息
     *
     * @param clientMaterialDto 物料信息DTO
     * @return BaseClientDTO
     */
    @Transactional(rollbackFor = Exception.class)
    @Klock(keys = {"#clientMaterialDto.workSheetId", "#clientMaterialDto.workCellId"}, waitTime = 60, leaseTime = 60, lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST)
    public BaseClientDTO saveMaterialInfo(ClientMaterialDTO clientMaterialDto) {
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findByIdAndDeleted(clientMaterialDto.getWorkSheetId(), Constants.LONG_ZERO);
        if (!workSheetOptional.isPresent()) {
            return new BaseClientDTO(Constants.KO, "总工单工单不存在!");
        }
        Optional<WorkCell> workCellOptional = workCellRepository.findByIdAndDeleted(clientMaterialDto.getWorkCellId(), Constants.LONG_ZERO);
        if (!workCellOptional.isPresent()) {
            return new BaseClientDTO(Constants.KO, "工位不能为空!");
        }
        Optional<Step> stepOptional = stepRepository.findByIdAndDeleted(clientMaterialDto.getStepId(), Constants.LONG_ZERO);
        if (!stepOptional.isPresent()) {
            return new BaseClientDTO(Constants.KO, "工序不能为空!");
        }
        WorkSheet workSheet = workSheetOptional.get();
        WorkCell workCell = workCellOptional.get();
        Step step = stepOptional.get();
        //获取定制工序中工艺路线
        WorkFlow snapshotWorkFlow = commonService.findSnapshotWorkFlow(workSheet, null,step);
        //查找型号工序用例比例
        List<PedigreeStepMaterialRule> pedigreeStepMaterialRuleList = commonService.findPedigreeStepMaterialRule(workSheet.getClientId(), workSheet.getPedigree().getId(), snapshotWorkFlow.getId(), step.getId());
        if (!ValidateUtils.isValid(pedigreeStepMaterialRuleList)) {
            return new BaseClientDTO(Constants.KO, "产品谱系工序上料规则不存在!");
        }
        //检查物料是否合法
        BaseClientDTO baseClientDto = checkMaterial(clientMaterialDto, workSheet, pedigreeStepMaterialRuleList);
        if (Constants.KO.equals(baseClientDto.getStatus())) {
            return baseClientDto;
        }
        //保存上料信息
        saveMaterial(clientMaterialDto, workSheet, workCell, pedigreeStepMaterialRuleList);
        return baseClientDto;
    }

    /**
     * 检查上料是否合规
     *
     * @param clientMaterialDto            上料信息集合
     * @param workSheet                    总工单
     * @param pedigreeStepMaterialRuleList 上料规则
     * @return BaseClientDTO
     * <AUTHOR>
     * @date 2021-01-20
     **/
    @Transactional(rollbackFor = Exception.class)
    public BaseClientDTO checkMaterial(ClientMaterialDTO clientMaterialDto,
                                       WorkSheet workSheet,
                                       List<PedigreeStepMaterialRule> pedigreeStepMaterialRuleList) {
        WorkSheet orgWorkSheet = workSheet;
        if (workSheet.getCategory() == WsEnum.ONLINE_RE_WS.getCategory()) {
            WsRework wsRework = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO).orElse(null);
            if (null != wsRework) {
                orgWorkSheet = wsRework.getOriginalWorkSheet();
            }
        }
        //不存在的物料集合
        List<String> notExistMaterialList = new ArrayList<>();
        //不存在的投料单物料集合
        List<String> notExistWmList = new ArrayList<>();
        //不匹配工序的物料集合
        List<String> notMatchingMaterialList = new ArrayList<>();
        //总工单库存不足的物料集合
        List<String> notEnoughInventoryMaterialList = new ArrayList<>();
        // 物料管控粒度不匹配
        List<String> notMatchingControlMaterialGranularityList = new ArrayList<>();
        // 批次号/序列号不符合规则
        List<String> notMatchingSNRuleList = new ArrayList<>();
        WorkSheet finalOrgWorkSheet = orgWorkSheet;
        clientMaterialDto.getMaterialDetailDtoList().forEach(clientMaterialDetailDto -> {
            MaterialDTO materialDto = rbaseMaterialProxy.findByCodeAndDeleted(clientMaterialDetailDto.getMaterialCode(),Constants.LONG_ZERO).orElse(null);
            //物料不存在
            if (materialDto == null) {
                notExistMaterialList.add(clientMaterialDetailDto.getMaterialCode());
                return;
            }
            clientMaterialDetailDto.setMaterialId(materialDto.getId());
            //查找投料单
            Optional<WsMaterial> wsMaterialOptional = wsMaterialRepository.findByWorkSheetIdAndMaterialIdAndDeleted(
                    workSheet.getId(), materialDto.getId(), Constants.LONG_ZERO);
            if (!wsMaterialOptional.isPresent()) {
                notExistWmList.add(materialDto.getCode());
                return;
            }
            //判断物料是否为配置的工序所用物料编码
            boolean isCurrProcessStepMaterial = pedigreeStepMaterialRuleList.stream()
                    .anyMatch(pedigreeStepMaterialRule -> pedigreeStepMaterialRule.getMaterialId().equals(wsMaterialOptional.get().getOriginMaterialId()));
            if (!isCurrProcessStepMaterial) {
                notMatchingMaterialList.add(materialDto.getCode());
                return;
            }
            //判断总工单所领的物料是否足够
            List<WsMaterial> wsMaterialList = wsMaterialRepository.findByWorkSheetIdAndOriginMaterialIdAndDeleted(workSheet.getId(), wsMaterialOptional.get().getOriginMaterialId(), Constants.LONG_ZERO);
            BigDecimal sumLeftNumber = wsMaterialBatchRepository.sumLeftNumberByWorkSheetIdAndMaterialIdInAndDeleted(finalOrgWorkSheet.getId(), wsMaterialList.stream().map(WsMaterial::getMaterialId).collect(Collectors.toList()), Constants.LONG_ZERO);
            if (null == sumLeftNumber || sumLeftNumber.doubleValue() < clientMaterialDetailDto.getNumber()) {
                notEnoughInventoryMaterialList.add(materialDto.getCode());
                return;
            }
            // 物料管控粒度
            Optional<PedigreeStepMaterialRule> pedigreeStepMaterialRuleOptional = pedigreeStepMaterialRuleList.stream().filter(pedigreeStepMaterialRule -> pedigreeStepMaterialRule.getMaterialId().equals(wsMaterialOptional.get().getOriginMaterialId())).findAny();
            if (pedigreeStepMaterialRuleOptional.isPresent()) {
                if (clientMaterialDetailDto.getControlMaterialGranularity() != pedigreeStepMaterialRuleOptional.get().getControlMaterialGranularity()) {
                    notMatchingControlMaterialGranularityList.add(materialDto.getCode());
                    return;
                }

                // 批次号/序列号规则是否符合
                List<List<SnRuleDTO>> serialNumberRule = pedigreeStepMaterialRuleOptional.get().getSerialNumberRule();
                Boolean notMatch = snNotMatchingSnRule(serialNumberRule, clientMaterialDetailDto.getSn());
                if (notMatch) {
                    notMatchingSNRuleList.add(materialDto.getCode());
                    return;
                }
            }
        });
        return dealMessage(
                notExistMaterialList,
                notExistWmList,
                notMatchingMaterialList,
                notEnoughInventoryMaterialList,
                notMatchingControlMaterialGranularityList,
                notMatchingSNRuleList);
    }

    /**
     * 序列号不符合规则
     *
     * @param serialNumberRule 序列号规则集合
     * @param sn               序列号
     * @return 序列号不符合规则
     */
    private Boolean snNotMatchingSnRule(List<List<SnRuleDTO>> serialNumberRule, String sn) {
        if (ValidateUtils.isValid(serialNumberRule) || StringUtils.isEmpty(sn)) {
            return false;
        }
        // 多组校验，【批次号/序列号】符合其中一个分组条件即可。
        // 在单个分组中，多个需要同时满足条件。
        for (List<SnRuleDTO> snRuleDtoList : serialNumberRule) {
            if (Boolean.TRUE.equals(existSnRuleListMatch(sn, snRuleDtoList))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 每个分组存在不符合规则
     *
     * @param sn            序列号
     * @param snRuleDtoList 单个分组的正则
     * @return 每个分组存在不符合规则
     */
    private Boolean existSnRuleListMatch(String sn, List<SnRuleDTO> snRuleDtoList) {
        for (SnRuleDTO snRuleDTO : snRuleDtoList) {
            String regular = snRuleDTO.getRegular();
            if (!Pattern.compile(regular).matcher(sn).matches()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 处理上料错误的消息
     *
     * @param notExistMaterialList           不存在的物料集合
     * @param notExistWmList                 不存在的投料单物料集合
     * @param notMatchingMaterialList        不匹配工序的物料集合
     * @param notEnoughInventoryMaterialList 总工单物料库存不足集合
     */
    public BaseClientDTO dealMessage(List<String> notExistMaterialList,
                                     List<String> notExistWmList,
                                     List<String> notMatchingMaterialList,
                                     List<String> notEnoughInventoryMaterialList,
                                     List<String> notMatchingControlMaterialGranularityList,
                                     List<String> notMatchingSNRuleList) {
        BaseClientDTO baseClientDto = new BaseClientDTO();
        StringBuilder message = new StringBuilder();
        if (ValidateUtils.isAnyValid(
                notExistMaterialList,
                notExistWmList,
                notMatchingMaterialList)) {
            baseClientDto.setStatus(Constants.KO);
            message.append(Constants.STATUS_KO_COLON);
        } else {
            baseClientDto.setStatus(Constants.OK);
            return baseClientDto;
        }
        if (ValidateUtils.isValid(notExistMaterialList)) {
            message.append("物料编码");
            message.append(String.join(Constants.STR_COMMA, notExistMaterialList));
            message.append("物料不存在!;");
        }
        if (ValidateUtils.isValid(notExistWmList)) {
            message.append("物料编码");
            message.append(String.join(Constants.STR_COMMA, notExistWmList));
            message.append("不在当前工单的投料单中!;");
        }
        if (ValidateUtils.isValid(notMatchingMaterialList)) {
            message.append("物料编码");
            message.append(String.join(Constants.STR_COMMA, notMatchingMaterialList));
            message.append("不是当前工序所用物料!;");
        }
        if (ValidateUtils.isValid(notEnoughInventoryMaterialList)) {
            message.append("物料编码");
            message.append(String.join(Constants.STR_COMMA, notEnoughInventoryMaterialList));
            message.append("总工单库存不足!;");
        }
        if (ValidateUtils.isValid(notMatchingControlMaterialGranularityList)) {
            message.append("物料编码");
            message.append(String.join(Constants.STR_COMMA, notMatchingControlMaterialGranularityList));
            message.append("物料管控粒度不匹配!;");
        }
        if (ValidateUtils.isValid(notMatchingSNRuleList)) {
            message.append("物料编码");
            message.append(String.join(Constants.STR_COMMA, notMatchingSNRuleList));
            message.append("批次号/序列号规则不符!;");
        }
        baseClientDto.setMessage(message.toString());
        return baseClientDto;
    }

    /**
     * 保存上料记录
     *
     * @param clientMaterialDto 上料信息DTO
     * @param workSheet         总工单
     * @param workCell          工位
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveMaterial(ClientMaterialDTO clientMaterialDto,
                             WorkSheet workSheet,
                             WorkCell workCell,
                             List<PedigreeStepMaterialRule> pedigreeStepMaterialRuleList) {
        if (workSheet.getCategory() == WsEnum.ONLINE_RE_WS.getCategory()) {
            WsRework wsRework = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO).orElse(null);
            if (null != wsRework) {
                workSheet = wsRework.getOriginalWorkSheet();
            }
        }
        //获取总工单物料剩余数量大于0的记录
        List<WsMaterialBatch> wsMaterialBatchList = wsMaterialBatchRepository.findByWorkSheetIdAndLeftNumberIsGreaterThanAndDeleted(workSheet.getId(), Constants.DOUBLE_ZERRO, Constants.LONG_ZERO);
        //总工单领料记录按照物料ID进行分组
        Map<Long, List<WsMaterialBatch>> wsMaterialBatchLeftCollect = wsMaterialBatchList.stream().collect(Collectors.groupingBy(WsMaterialBatch::getMaterialId));
        //工位上料数据按照物料ID进行分组
        Map<Long, List<ClientMaterialDTO.ClientMaterialDetailDTO>> increaseMaterialCollect = clientMaterialDto.getMaterialDetailDtoList().stream().collect(Collectors.groupingBy(ClientMaterialDTO.ClientMaterialDetailDTO::getMaterialId));
        //1.先扣减总工单领料的剩余数量
        this.deductionWsMaterialInventory(workSheet, increaseMaterialCollect, pedigreeStepMaterialRuleList, wsMaterialBatchLeftCollect);
        //2.更新工单工位上料的数量信息
        this.updateWsWorkCellMaterialBatchInventory(increaseMaterialCollect, workSheet, workCell);
    }

    /**
     * 扣减总工单领料的剩余数量
     *
     * @param workSheet                    总工单
     * @param increaseMaterialCollect      需要上料的物料集合
     * @param pedigreeStepMaterialRuleList 上料规则
     * @param wsMaterialBatchLeftCollect   工单剩余物料集合
     * <AUTHOR>
     * @date 2021-01-20
     **/
    public void deductionWsMaterialInventory(WorkSheet workSheet, Map<Long, List<ClientMaterialDTO.ClientMaterialDetailDTO>> increaseMaterialCollect,
                                             List<PedigreeStepMaterialRule> pedigreeStepMaterialRuleList,
                                             Map<Long, List<WsMaterialBatch>> wsMaterialBatchLeftCollect) {
        increaseMaterialCollect.forEach((key, value) -> {
            Optional<WsMaterial> wsMaterialOptional = wsMaterialRepository.findByWorkSheetIdAndMaterialIdAndDeleted(workSheet.getId(), key, Constants.LONG_ZERO);
            if (wsMaterialOptional.isPresent()) {
                WsMaterial wsMaterial = wsMaterialOptional.get();
                Optional<PedigreeStepMaterialRule> pedigreeStepMaterialRuleOptional = pedigreeStepMaterialRuleList.stream().filter(pedigreeStepMaterialRule -> pedigreeStepMaterialRule.getMaterialId().equals(wsMaterial.getOriginMaterialId())).findAny();
                if (pedigreeStepMaterialRuleOptional.isPresent()) {
                    PedigreeStepMaterialRule pedigreeStepMaterialRule = pedigreeStepMaterialRuleOptional.get();
                    //配置的上料规则需要扣数则扣除工单剩余物料数量
                    if (pedigreeStepMaterialRule.getIsDeduct()) {
                        value.forEach(increaseMaterialTemp -> {
                            AtomicDouble leftNumber = new AtomicDouble(increaseMaterialTemp.getNumber());
                            //当前物料需要核批次且批次不为空
                            if (pedigreeStepMaterialRule.getIsCheckMaterialBatch()) {
                                if (ValidateUtils.isValid(increaseMaterialTemp.getMaterialBatch())) {
                                    if (wsMaterialBatchLeftCollect.get(key) != null) {
                                        wsMaterialBatchLeftCollect.get(key).forEach(wsMaterialBatch -> {
                                            if (ValidateUtils.isValid(wsMaterialBatch.getBatch()) && wsMaterialBatch.getBatch().equals(increaseMaterialTemp.getMaterialBatch())) {
                                                this.deductionNumber(wsMaterialBatch, leftNumber);
                                            }
                                        });
                                    }
                                } else {
                                    ////需要核批次但批次为空，默认按无批次扣数
                                    if (wsMaterialBatchLeftCollect.get(key) != null) {
                                        wsMaterialBatchLeftCollect.get(key).forEach(wsMaterialBatch -> this.deductionNumber(wsMaterialBatch, leftNumber));
                                    }
                                }
                            } else {
                                //不需要核批次直接扣数
                                if (wsMaterialBatchLeftCollect.get(key) != null) {
                                    wsMaterialBatchLeftCollect.get(key).forEach(wsMaterialBatch -> this.deductionNumber(wsMaterialBatch, leftNumber));
                                }
                            }
                        });
                    }
                }
            }
        });
    }

    /**
     * 更新工单工位上料的数量信息
     *
     * @param increaseMaterialCollect 需要上料的物料集合
     * @param workSheet               总工单
     * @param workCell                工位
     * <AUTHOR>
     * @date 2021-01-20
     **/
    public void updateWsWorkCellMaterialBatchInventory(Map<Long, List<ClientMaterialDTO.ClientMaterialDetailDTO>> increaseMaterialCollect,
                                                       WorkSheet workSheet, WorkCell workCell) {
        increaseMaterialCollect.forEach((key, value) -> {
            //1.若上料有物料批次号则更新工单物料批次的剩余库存
            value.forEach(increaseMaterialTemp -> {
                if (ValidateUtils.isValid(increaseMaterialTemp.getMaterialBatch())) {
                    WsWorkCellMaterialBatch wsWorkCellMaterialBatchTemp = wsWorkCellMaterialBatchRepository.findByWorkSheetIdAndWorkCellIdAndMaterialIdAndBatchAndDeleted(workSheet.getId(), workCell.getId(),
                            key, increaseMaterialTemp.getMaterialBatch(), Constants.LONG_ZERO).orElseGet(WsWorkCellMaterialBatch::new);
                    wsWorkCellMaterialBatchTemp.setWorkSheet(workSheet)
                            .setWorkCell(workCell)
                            .setMaterialId(key)
                            .setBatch(increaseMaterialTemp.getMaterialBatch())
                            .setNumber(NumberUtils.add(wsWorkCellMaterialBatchTemp.getNumber(), increaseMaterialTemp.getNumber()).doubleValue())
                            .setLeftNumber(NumberUtils.add(wsWorkCellMaterialBatchTemp.getLeftNumber(), increaseMaterialTemp.getNumber()).doubleValue());
                    wsWorkCellMaterialBatchRepository.save(wsWorkCellMaterialBatchTemp);
                } else {
                    //2.若上料无物料批次号则更新工单物料的剩余库存
                    WsWorkCellMaterialBatch wsWorkCellMaterialBatch = wsWorkCellMaterialBatchRepository.findByWorkSheetIdAndWorkCellIdAndMaterialIdAndDeleted(workSheet.getId(),
                            workCell.getId(), key, Constants.LONG_ZERO).orElseGet(WsWorkCellMaterialBatch::new);
                    wsWorkCellMaterialBatch.setWorkSheet(workSheet)
                            .setWorkCell(workCell)
                            .setMaterialId(key);
                    wsWorkCellMaterialBatch.setNumber(NumberUtils.add(wsWorkCellMaterialBatch.getNumber(), increaseMaterialTemp.getNumber()).doubleValue())
                            .setLeftNumber(NumberUtils.add(wsWorkCellMaterialBatch.getLeftNumber(), increaseMaterialTemp.getNumber()).doubleValue());
                    wsWorkCellMaterialBatchRepository.save(wsWorkCellMaterialBatch);
                }
            });
        });
    }

    /**
     * 上料扣减工单领料剩余数量
     *
     * @param wsMaterialBatch 工单领料记录
     * @param leftNumber      上料剩余数量
     * <AUTHOR>
     * @date 2021-01-20
     **/
    public void deductionNumber(WsMaterialBatch wsMaterialBatch, AtomicDouble leftNumber) {
        double currLeftNumber = NumberUtils.subtract(
                wsMaterialBatch.getLeftNumber(), leftNumber.get()).doubleValue();
        if (currLeftNumber > Constants.INT_ZERO) {
            wipLedgerServices.processWarehouseAndSaveLedger(wsMaterialBatch.getWorkSheet().getWorkLine(), wsMaterialBatch, -leftNumber.get(), WipLedgerOperationEnum.WIP_LEDGER_OPERATION_WORK_SHEET_DELEVER.getCategory(), null);
            leftNumber.set(Constants.INT_ZERO);
            wsMaterialBatch.setLeftNumber(currLeftNumber);
        } else {
            leftNumber.set(NumberUtils.subtract(leftNumber.get(), wsMaterialBatch.getLeftNumber()).doubleValue());
            wipLedgerServices.processWarehouseAndSaveLedger(wsMaterialBatch.getWorkSheet().getWorkLine(), wsMaterialBatch, -wsMaterialBatch.getLeftNumber(), WipLedgerOperationEnum.WIP_LEDGER_OPERATION_WORK_SHEET_DELEVER.getCategory(), null);
            wsMaterialBatch.setLeftNumber(Constants.INT_ZERO);
        }
        wsMaterialBatchRepository.save(wsMaterialBatch);
    }

    /**
     * 通过工单id 获取上料规则
     * @param workSheetId 工单id
     * @return ClientWsMaterialDTO
     */
    public ClientWsMaterialDTO findWsMaterialByWsId(Long workSheetId) {

        List<WsMaterial> wsMaterialList = wsMaterialRepository.findByWorkSheetIdAndDeleted(workSheetId, Constants.LONG_ZERO);

        if (ValidateUtils.isValid(wsMaterialList)){
            return new ClientWsMaterialDTO(Constants.OK,wsMaterialList);
        }
        return new ClientWsMaterialDTO(Constants.KO,"工单投料单不存在");
    }

}
