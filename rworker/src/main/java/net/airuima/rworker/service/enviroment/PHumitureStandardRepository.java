package net.airuima.rworker.service.enviroment;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rworker.service.enviroment.dto.HumitureStandardDTO;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class PHumitureStandardRepository {

    @BeanDefine("humitureStandardRepository")
    public Optional<HumitureStandardDTO> findByAreaIdAndDeleted(Long areaId, Long delete) {
        return Optional.empty();
    }
}
