package net.airuima.rworker.service.rworker.process;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.dto.rworker.process.dto.RworkerContainerStepSaveRequestDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerStepProcessBaseDTO;
import net.airuima.rworker.web.rest.rworker.process.dto.RworkerStepProcessGlobalDTO;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * Rworker容器模式生产过程下交相关Service
 * <AUTHOR>
 * @date 2023/2/2
 */
@FuncDefault
public interface IContainerProcessSaveService {

    /**
     * 保存当前生产工序容器详情及更新前置工序容器详情信息
     *
     * @param batchWorkDetail                    当前生产工序的批量详情
     * @param rworkerContainerStepSaveRequestDTO 工序保存请求参数DTO
     * @param containerStepSaveBaseInfo          工序生产过程公用基础数据
     */
    @FuncInterceptor(value = "Container")
    default void createContainerWorkDetail(BatchWorkDetail batchWorkDetail, RworkerContainerStepSaveRequestDTO rworkerContainerStepSaveRequestDTO, RworkerStepProcessBaseDTO containerStepSaveBaseInfo) {

    }

    /**
     * 验证批量详情数据
     *
     * @param batchWorkDetail           批量生产详情
     * @param rworkerContainerStepSaveRequestDTO  工序保存请求参数DTO
     * @param containerStepSaveBaseInfo 工序生产过程公用基础数据
     * <AUTHOR>
     * @date 2023/4/7
     **/
    @FuncInterceptor(value = "Container")
    default void validBatchWorkDetail(BatchWorkDetail batchWorkDetail, RworkerContainerStepSaveRequestDTO rworkerContainerStepSaveRequestDTO, RworkerStepProcessBaseDTO containerStepSaveBaseInfo) {

    }

    /**
     * 工单粒度时保存工单批量详情数据
     *
     * @param rworkerContainerStepSaveRequestDTO 容器工序保存参数DTO
     * @param containerStepSaveBaseInfo          工序生产过程通用基础数据
     * @return net.airuima.rbase.domain.procedure.batch.BatchWorkDetail 批量工序生产详情
     */
    @FuncInterceptor(value = "Container")
    default BatchWorkDetail createWsWorkDetail(RworkerContainerStepSaveRequestDTO rworkerContainerStepSaveRequestDTO, RworkerStepProcessBaseDTO containerStepSaveBaseInfo) {
        return null;
    }
}
