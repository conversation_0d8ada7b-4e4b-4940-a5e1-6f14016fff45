package net.airuima.rworker.dto.client.base;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.util.ValidateUtils;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * RWork相关接口基础信息DTO
 *
 * <AUTHOR>
 * @date 2020/12/28
 */
@Schema(description = "结果信息DTO")
public class BaseClientDTO {

    /**
     * 结果返回状态
     */
    @Schema(description = "OK/NG")
    private String status;

    /**
     * 结果返回消息
     */
    @Schema(description = "异常提醒信息")
    private String message;

    public BaseClientDTO() {
    }

    public BaseClientDTO(String status) {
        this.status = status;
    }

    public BaseClientDTO(String status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getStatus() {
        if (!ValidateUtils.isValid(status)) {
            return Constants.OK;
        }
        return status;
    }

    public BaseClientDTO setStatus(String status) {
        this.status = status;
        return this;
    }

    public String getMessage() {
        return message;
    }

    public BaseClientDTO setMessage(String message) {
        this.message = message;
        return this;
    }
}
