package net.airuima.rworker.dto.client;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.base.scene.WorkLine;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * RWork生产线相关接口DTO
 *
 * <AUTHOR>
 * @date 2020/12/28
 */
@Schema(description = "Rwork获取生产线返回数据")
public class ClientWorkLineDTO {

    /**
     * 生产线ID
     */
    @Schema(description = "生产线ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long workLineId;

    /**
     * 生产线名称
     */
    @Schema(description = "生产线名称")
    private String workLineName;

    /**
     * 生产线编码
     */
    @Schema(description = "生产线编码")
    private String workLineCode;

    public ClientWorkLineDTO() {
    }

    public ClientWorkLineDTO(WorkLine workLine) {
        this.workLineId = workLine.getId();
        workLineName = workLine.getName();
        workLineCode = workLine.getCode();
    }

    public String getWorkLineName() {
        return workLineName;
    }

    public ClientWorkLineDTO setWorkLineName(String workLineName) {
        this.workLineName = workLineName;
        return this;
    }

    public String getWorkLineCode() {
        return workLineCode;
    }

    public ClientWorkLineDTO setWorkLineCode(String workLineCode) {
        this.workLineCode = workLineCode;
        return this;
    }

    public Long getWorkLineId() {
        return workLineId;
    }

    public ClientWorkLineDTO setWorkLineId(Long workLineId) {
        this.workLineId = workLineId;
        return this;
    }
}
