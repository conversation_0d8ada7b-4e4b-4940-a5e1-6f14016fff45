package net.airuima.rworker.dto.client;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-05-23
 */
@Schema(description = "烘烤放入工序参数")
public class BakeConfigInfoDTO implements Serializable {
    /**
     * 时长范围
     */
    @Schema(description = "时长范围")
    private String durationRange;

    /**
     * 温度范围
     */
    @Schema(description = "温度范围")
    private String temperatureRange;

    /**
     * 烘烤条件编码
     */
    @Schema(description = "烘烤条件编码")
    private String bakeGroupCode;

    /**
     * 烘烤条件名称
     */
    @Schema(description = "烘烤条件名称")
    private String bakeGroupName;

    public String getBakeGroupCode() {
        return bakeGroupCode;
    }

    public BakeConfigInfoDTO setBakeGroupCode(String bakeGroupCode) {
        this.bakeGroupCode = bakeGroupCode;
        return this;
    }

    public String getBakeGroupName() {
        return bakeGroupName;
    }

    public BakeConfigInfoDTO setBakeGroupName(String bakeGroupName) {
        this.bakeGroupName = bakeGroupName;
        return this;
    }

    public String getDurationRange() {
        return durationRange;
    }

    public BakeConfigInfoDTO setDurationRange(String durationRange) {
        this.durationRange = durationRange;
        return this;
    }

    public String getTemperatureRange() {
        return temperatureRange;
    }

    public BakeConfigInfoDTO setTemperatureRange(String temperatureRange) {
        this.temperatureRange = temperatureRange;
        return this;
    }
}
