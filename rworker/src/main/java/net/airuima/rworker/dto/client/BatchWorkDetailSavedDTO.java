package net.airuima.rworker.dto.client;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.WsStep;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * 批次详情保存参数
 *
 * <AUTHOR>
 * @date 2023/2/3
 */
@Schema(description = "批次详情保存参数")
public class BatchWorkDetailSavedDTO {

    /**
     * 待保存的工序信息
     */
    @Schema(description = "待保存的工序信息")
    private ClientSaveStepInfoDTO clientSaveStepInfoDto;

    /**
     * 子工单
     */
    @Schema(description = "子工单")
    private SubWorkSheet subWorkSheet;

    /**
     * 当前定制工序
     */
    @Schema(description = "当前定制工序")
    private WsStep wsStep;

    /**
     * 工位
     */
    @Schema(description = "工位")
    private WorkCell workCell;


    /**
     * 前置工序是否完成
     */
    @Schema(description = "前置工序是否完成")
    private boolean preStepIsFinish;

    /**
     * 批次工作详情
     */
    @Schema(description = "批次工作详情")
    private BatchWorkDetail batchWorkDetail;

    public BatchWorkDetailSavedDTO() {
        
    }

    public BatchWorkDetailSavedDTO(ClientSaveStepInfoDTO clientSaveStepInfoDto, SubWorkSheet subWorkSheet, WsStep wsStep, WorkCell workCell, boolean preStepIsFinish, BatchWorkDetail batchWorkDetail) {
        this.clientSaveStepInfoDto = clientSaveStepInfoDto;
        this.subWorkSheet = subWorkSheet;
        this.wsStep = wsStep;
        this.workCell = workCell;
        this.preStepIsFinish = preStepIsFinish;
        this.batchWorkDetail = batchWorkDetail;
    }

    public ClientSaveStepInfoDTO getClientSaveStepInfoDto() {
        return clientSaveStepInfoDto;
    }

    public BatchWorkDetailSavedDTO setClientSaveStepInfoDto(ClientSaveStepInfoDTO clientSaveStepInfoDto) {
        this.clientSaveStepInfoDto = clientSaveStepInfoDto;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public BatchWorkDetailSavedDTO setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public WsStep getWsStep() {
        return wsStep;
    }

    public BatchWorkDetailSavedDTO setWsStep(WsStep wsStep) {
        this.wsStep = wsStep;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public BatchWorkDetailSavedDTO setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public boolean getPreStepIsFinish() {
        return preStepIsFinish;
    }

    public BatchWorkDetailSavedDTO setPreStepIsFinish(boolean preStepIsFinish) {
        this.preStepIsFinish = preStepIsFinish;
        return this;
    }

    public BatchWorkDetail getBatchWorkDetail() {
        return batchWorkDetail;
    }

    public BatchWorkDetailSavedDTO setBatchWorkDetail(BatchWorkDetail batchWorkDetail) {
        this.batchWorkDetail = batchWorkDetail;
        return this;
    }
}
