package net.airuima.rworker.web.rest.rworker.process.dto.general;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 通用工序不合格项目信息
 * <AUTHOR>
 * @date 2023/4/6
 */
@Schema(description = "工序不合格项目信息")
public class UnqualifiedItemGetInfo implements Serializable {

    /**
     * 不合格项目id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "不合格项目id")
    private Long id;

    /**
     * 不合格项目代码
     */
    @Schema(description = "不合格项目代码")
    private String code;

    /**
     * 不合格项目名称
     */
    @Schema(description = "不合格项目名称")
    private String name;

    public UnqualifiedItemGetInfo() {

    }

    public UnqualifiedItemGetInfo(UnqualifiedItem unqualifiedItem) {
        this.id = unqualifiedItem.getId();
        this.code = unqualifiedItem.getCode();
        this.name = unqualifiedItem.getName();
    }

    public Long getId() {
        return id;
    }

    public UnqualifiedItemGetInfo setId(Long id) {
        this.id = id;
        return this;
    }

    public String getCode() {
        return code;
    }

    public UnqualifiedItemGetInfo setCode(String code) {
        this.code = code;
        return this;
    }

    public String getName() {
        return name;
    }

    public UnqualifiedItemGetInfo setName(String name) {
        this.name = name;
        return this;
    }
}
