package net.airuima.rworker.web.rest.rworker.process.dto.general;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.document.DocumentDTO;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 工序SOP信息
 * <AUTHOR>
 * @date 2023/4/6
 */
@Schema(description = "工序SOP信息")
public class ProcessDocumentGetInfo implements Serializable {

    /**
     * 文件ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "文件ID")
    private Long documentId;

    /**
     * 调用服务名
     */
    @Schema(description = "调用服务名")
    private String serviceName;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 文件类型，0：图片，1：文档，2：视频，3：压缩包，4：音频，5：可执行文件
     */
    @Schema(description = "文件类型，0：图片，1：文档，2：视频，3：压缩包，4：音频，5：可执行文件")
    private Integer category;

    /**
     * SOP路径
     */
    @Schema(description = "SOP路径")
    private String path;

    public ProcessDocumentGetInfo() {

    }

    public ProcessDocumentGetInfo(DocumentDTO documentDTO) {
        this.documentId = documentDTO.getId();
        this.serviceName = documentDTO.getServiceName();
        this.category = documentDTO.getCategory();
        this.path = documentDTO.getPath();
        this.name = documentDTO.getName();
    }

    public Long getDocumentId() {
        return documentId;
    }

    public ProcessDocumentGetInfo setDocumentId(Long documentId) {
        this.documentId = documentId;
        return this;
    }

    public String getServiceName() {
        return serviceName;
    }

    public ProcessDocumentGetInfo setServiceName(String serviceName) {
        this.serviceName = serviceName;
        return this;
    }

    public String getName() {
        return name;
    }

    public ProcessDocumentGetInfo setName(String name) {
        this.name = name;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public ProcessDocumentGetInfo setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public String getPath() {
        return path;
    }

    public ProcessDocumentGetInfo setPath(String path) {
        this.path = path;
        return this;
    }
}
