package net.airuima.rworker.web.rest.rworker.process.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 验证待绑定容器合规性参数DTO
 * <AUTHOR>
 * @date 2023/1/31
 */
@Schema(description = "验证待绑定容器合规性参数DTO")
public class RworkerBindContainerValidateRequestDTO implements Serializable {
    /**
     * 需验证的待绑定容器编码
     */
    @Schema(description = "需验证的待绑定容器编码")
    private String bindContainerCode;

    /**
     * 请求工序生产的容器编码列表
     */
    @Schema(description = "请求工序生产的容器编码列表")
    private List<String> stepRequestContainerCodeList;

    public String getBindContainerCode() {
        return bindContainerCode;
    }

    /**
     * 工单id或子工单id
     */
    @Schema(description = "工单id或子工单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long productWorkSheetId;

    public Long getProductWorkSheetId() {
        return productWorkSheetId;
    }

    public RworkerBindContainerValidateRequestDTO setProductWorkSheetId(Long productWorkSheetId) {
        this.productWorkSheetId = productWorkSheetId;
        return this;
    }

    public RworkerBindContainerValidateRequestDTO setBindContainerCode(String bindContainerCode) {
        this.bindContainerCode = bindContainerCode;
        return this;
    }

    public List<String> getStepRequestContainerCodeList() {
        return stepRequestContainerCodeList;
    }

    public RworkerBindContainerValidateRequestDTO setStepRequestContainerCodeList(List<String> stepRequestContainerCodeList) {
        this.stepRequestContainerCodeList = stepRequestContainerCodeList;
        return this;
    }
}
