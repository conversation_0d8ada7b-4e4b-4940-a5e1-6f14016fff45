package net.airuima.rworker.web.rest.rworker.process.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * RWorker获取易损件相关信息DTO
 *
 * <AUTHOR>
 * @date 2021/6/23
 */
@Schema(description = "RWorker获取易损件预警相关信息DTO")
public class RworkerValidWearingPartInfoDTO implements Serializable {
    /**
     * 易损件ID
     **/
    @Schema(description = "易损件ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 易损件编码
     */
    @Schema(description = "易损件编码")
    private String code;

    /**
     * 易损件名称
     */
    @Schema(description = "易损件名称")
    private String name;

    /**
     * 易损件重置方式（0:手动，1:自动）
     */
    @Schema(description = "易损件重置方式（0:手动，1:自动）")
    private Integer resetWay;

    /**
     * 易损件种类ID
     */
    @Schema(description = "易损件种类ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long wearingPartGroupId;

    /**
     * 预警 管控类型 0：次数；1：时长；2：有效期；3：时长+次数；4：时长+有效期；5：次数+有效期；6：时长+次数+有效期
     */
    @Schema(description = "预警 管控类型 0：次数；1：时长；2：有效期；3：时长+次数；4：时长+有效期；5：次数+有效期；6：时长+次数+有效期")
    private Integer controlCategory;

    /**
     * 预警 剩余次数
     */
    @Schema(description = "预警 剩余次数")
    private Integer residueNumber;

    /**
     * 预警 剩余时间(秒)
     */
    @Schema(description = "预警 剩余时间(秒)")
    private Integer residueTime;

    /**
     * 预警 剩余天数
     */
    @Schema(description = "预警 剩余天数")
    private String residueDay;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public RworkerValidWearingPartInfoDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public Integer getResetWay() {
        return resetWay;
    }

    public RworkerValidWearingPartInfoDTO setResetWay(Integer resetWay) {
        this.resetWay = resetWay;
        return this;
    }

    public Long getWearingPartGroupId() {
        return wearingPartGroupId;
    }

    public RworkerValidWearingPartInfoDTO setWearingPartGroupId(Long wearingPartGroupId) {
        this.wearingPartGroupId = wearingPartGroupId;
        return this;
    }

    public Integer getControlCategory() {
        return controlCategory;
    }

    public RworkerValidWearingPartInfoDTO setControlCategory(Integer controlCategory) {
        this.controlCategory = controlCategory;
        return this;
    }

    public Integer getResidueNumber() {
        return residueNumber;
    }

    public RworkerValidWearingPartInfoDTO setResidueNumber(Integer residueNumber) {
        this.residueNumber = residueNumber;
        return this;
    }

    public Integer getResidueTime() {
        return residueTime;
    }

    public RworkerValidWearingPartInfoDTO setResidueTime(Integer residueTime) {
        this.residueTime = residueTime;
        return this;
    }

    public String getResidueDay() {
        return residueDay;
    }

    public RworkerValidWearingPartInfoDTO setResidueDay(String residueDay) {
        this.residueDay = residueDay;
        return this;
    }
}
