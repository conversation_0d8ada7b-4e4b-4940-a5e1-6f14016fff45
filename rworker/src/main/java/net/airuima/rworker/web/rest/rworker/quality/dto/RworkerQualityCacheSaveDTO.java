package net.airuima.rworker.web.rest.rworker.quality.dto;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 * 保存质检表单缓存DTO
 * <AUTHOR>
 */
public class RworkerQualityCacheSaveDTO {

    /**
     * 质检类型(0:首检;1:巡检;2:末检;3:终检;4:抽检;5:来料检;6:外协入库检)
     */
    private Integer category;

    /**
     * 质检任务ID
     */
    private Long id;

    /**
     * 质检缓存
     */
    private String cache;

    /**
     * 手动发起首检/巡检时待新增质检任务参数
     */
    private FaiIpqcTaskSaveInfo faiIpqcTaskSaveInfo;

    /**
     * 首检/巡检时待新增质检任务参数
     */
    public static class FaiIpqcTaskSaveInfo{
        /**
         * 投产工单ID
         */
        private Long productWorkSheetId;


        /**
         * 工位ID
         */
        private Long workCellId;


        /**
         * 项目类型ID
         */
        private Long varietyId;

        public Long getProductWorkSheetId() {
            return productWorkSheetId;
        }

        public FaiIpqcTaskSaveInfo setProductWorkSheetId(Long productWorkSheetId) {
            this.productWorkSheetId = productWorkSheetId;
            return this;
        }

        public Long getWorkCellId() {
            return workCellId;
        }

        public FaiIpqcTaskSaveInfo setWorkCellId(Long workCellId) {
            this.workCellId = workCellId;
            return this;
        }

        public Long getVarietyId() {
            return varietyId;
        }

        public FaiIpqcTaskSaveInfo setVarietyId(Long varietyId) {
            this.varietyId = varietyId;
            return this;
        }
    }

    public Integer getCategory() {
        return category;
    }

    public RworkerQualityCacheSaveDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public Long getId() {
        return id;
    }

    public RworkerQualityCacheSaveDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getCache() {
        return cache;
    }

    public RworkerQualityCacheSaveDTO setCache(String cache) {
        this.cache = cache;
        return this;
    }

    public FaiIpqcTaskSaveInfo getFaiIpqcTaskSaveInfo() {
        return faiIpqcTaskSaveInfo;
    }

    public RworkerQualityCacheSaveDTO setFaiIpqcTaskSaveInfo(FaiIpqcTaskSaveInfo faiIpqcTaskSaveInfo) {
        this.faiIpqcTaskSaveInfo = faiIpqcTaskSaveInfo;
        return this;
    }
}
