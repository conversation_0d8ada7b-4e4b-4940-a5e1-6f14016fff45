package net.airuima.rworker.web.rest.rworker.process.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 基础警告信息DTO
 * <AUTHOR>
 * @date 2023/9/13
 */
public class BaseWaringDTO implements Serializable {
    /**
     * 警告国际化key
     */
    @Schema(description = "警告国际化key")
    @JsonIgnore
    private String key;

    /**
     * 警告信息
     */
    @Schema(description = "警告信息")
    @JsonIgnore
    private String message;

    public BaseWaringDTO() {

    }

    public BaseWaringDTO(String key, String message) {
        this.key = key;
        this.message = message;
    }

    public String getKey() {
        return key;
    }

    public BaseWaringDTO setKey(String key) {
        this.key = key;
        return this;
    }

    public String getMessage() {
        return message;
    }

    public BaseWaringDTO setMessage(String message) {
        this.message = message;
        return this;
    }
}
