package net.airuima.rworker.web.rest.rworker.process.dto.general;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 易损件规则信息
 * <AUTHOR>
 * @date 2023/4/6
 */
@Schema(description = "易损件规则信息")
public class WearingPartGroupGetInfo implements Serializable {

    /**
     * 易损件类型名称
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "易损件类型id")
    private Long id;

    /**
     * 易损件类型名称
     */
    @Schema(description = "易损件类型名称")
    private String name;

    /**
     * 易损件类型编码
     */
    @Schema(description = "易损件类型编码")
    private String code;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer number;

    /**
     * 易损件信息集合
     */
    @Schema(description = "易损件信息集合")
    private List<WearingPartInfo> wearingPartInfoList;

    /**
     * 替换易损件列表规则信息
     */
    private List<WearingPartGroupGetInfo> wearingPartGroupExchangeInfoList;

    public WearingPartGroupGetInfo() {

    }

    public WearingPartGroupGetInfo(Long id, String name, String code, Integer number) {
        this.id = id;
        this.name = name;
        this.code = code;
        this.number = number;
    }

    public WearingPartGroupGetInfo(Long id, String name, String code) {
        this.id = id;
        this.name = name;
        this.code = code;
    }

    public Long getId() {
        return id;
    }

    public WearingPartGroupGetInfo setId(Long id) {
        this.id = id;
        return this;
    }

    public String getName() {
        return name;
    }

    public WearingPartGroupGetInfo setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public WearingPartGroupGetInfo setCode(String code) {
        this.code = code;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public WearingPartGroupGetInfo setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public List<WearingPartInfo> getWearingPartInfoList() {
        return wearingPartInfoList;
    }

    public WearingPartGroupGetInfo setWearingPartInfoList(List<WearingPartInfo> wearingPartInfoList) {
        this.wearingPartInfoList = wearingPartInfoList;
        return this;
    }

    public List<WearingPartGroupGetInfo> getWearingPartGroupExchangeInfoList() {
        return wearingPartGroupExchangeInfoList;
    }

    public WearingPartGroupGetInfo setWearingPartGroupExchangeInfoList(List<WearingPartGroupGetInfo> wearingPartGroupExchangeInfoList) {
        this.wearingPartGroupExchangeInfoList = wearingPartGroupExchangeInfoList;
        return this;
    }

    /**
     * 易损件信息
     */
    @Schema(description = "易损件信息")
    public static class WearingPartInfo implements Serializable{
        /**
         * ID
         */
        @Schema(description = "ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 易损件名称
         */
        @Schema(description = "易损件名称")
        private String name;


        /**
         * 易损件编码
         */
        @Schema(description = "易损件编码")
        private String code;

        /**
         * 易损件使用类型（0：次数；1：时长；2：有效期；3：时长+次数；4：时长+有效期；5：次数+有效期；6：时长+次数+有效期）
         */
        @Schema(description = "易损件使用类型（0：次数；1：时长；2：有效期；3：时长+次数；4：时长+有效期；5：次数+有效期；6：时长+次数+有效期）")
        private Integer category;

        /**
         * 最大重置次数
         */
        @Schema(description = "最大重置次数")
        private Integer maxResetNumber;

        /**
         * 累计重置次数
         */
        @Schema(description = "累计重置次数")
        private Integer accumulateResetNumber;

        /**
         * 剩余重置次数
         */
        @Schema(description = "剩余重置次数")
        private Integer availableResetNumber;

        /**
         * 最大使用次数
         */
        @Schema(description = "最大使用次数")
        private Integer maxUseNumber;

        /**
         * 累计使用次数
         */
        @Schema(description = "累计使用次数")
        private Integer accumulateUseNumber;

        /**
         * 剩余使用次数
         */
        @Schema(description = "剩余使用次数")
        private Integer availableUseNumber;

        /**
         * 最大使用时长(秒为单位)
         */
        @Schema(description = "最大使用时长(秒为单位)")
        private Integer maxUseTime;

        /**
         * 累计使用时长(秒为单位)
         */
        @Schema(description = "累计使用时长(秒为单位)")
        private Integer accumulateUseTime;

        /**
         * 剩余使用时长(秒为单位)
         */
        @Schema(description = "剩余使用时长(秒为单位)")
        private Integer availableUseTime;

        /**
         * 易损件使用状态（0可用，1在用，2超期，3报废）
         */
        @Schema(description = "易损件使用状态（0可用，1在用，2超期，3报废）")
        private Integer status;

        /**
         * 易损件重置方式（0:手动，1:自动）
         */
        @Schema(description = "易损件重置方式（0:手动，1:自动）")
        private Integer resetWay;

        /**
         * 失效期
         */
        @Schema(description = "失效期")
        private LocalDateTime expireDate;

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public Integer getCategory() {
            return category;
        }

        public void setCategory(Integer category) {
            this.category = category;
        }

        public Integer getMaxResetNumber() {
            return maxResetNumber;
        }

        public void setMaxResetNumber(Integer maxResetNumber) {
            this.maxResetNumber = maxResetNumber;
        }

        public Integer getAccumulateResetNumber() {
            return accumulateResetNumber;
        }

        public void setAccumulateResetNumber(Integer accumulateResetNumber) {
            this.accumulateResetNumber = accumulateResetNumber;
        }

        public Integer getMaxUseNumber() {
            return maxUseNumber;
        }

        public void setMaxUseNumber(Integer maxUseNumber) {
            this.maxUseNumber = maxUseNumber;
        }

        public Integer getAccumulateUseNumber() {
            return accumulateUseNumber;
        }

        public void setAccumulateUseNumber(Integer accumulateUseNumber) {
            this.accumulateUseNumber = accumulateUseNumber;
        }

        public Integer getMaxUseTime() {
            return maxUseTime;
        }

        public void setMaxUseTime(Integer maxUseTime) {
            this.maxUseTime = maxUseTime;
        }

        public Integer getAccumulateUseTime() {
            return accumulateUseTime;
        }

        public void setAccumulateUseTime(Integer accumulateUseTime) {
            this.accumulateUseTime = accumulateUseTime;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public Integer getResetWay() {
            return resetWay;
        }

        public void setResetWay(Integer resetWay) {
            this.resetWay = resetWay;
        }

        public LocalDateTime getExpireDate() {
            return expireDate;
        }

        public void setExpireDate(LocalDateTime expireDate) {
            this.expireDate = expireDate;
        }

        public Integer getAvailableResetNumber() {
            return this.maxResetNumber - this.accumulateResetNumber;
        }

        public void setAvailableResetNumber(Integer availableResetNumber) {
            this.availableResetNumber = availableResetNumber;
        }

        public Integer getAvailableUseNumber() {
            return this.maxUseNumber - this.accumulateUseNumber;
        }

        public void setAvailableUseNumber(Integer availableUseNumber) {
            this.availableUseNumber = availableUseNumber;
        }

        public Integer getAvailableUseTime() {
            return this.maxUseTime - this.accumulateUseTime;
        }

        public void setAvailableUseTime(Integer availableUseTime) {
            this.availableUseTime = availableUseTime;
        }
    }
}
