package net.airuima.rworker.web.rest.rworker.person;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import net.airuima.config.annotation.AppKey;
import net.airuima.rbase.dto.rworker.person.dto.StaffLoginDTO;
import net.airuima.rbase.dto.rworker.person.dto.StaffLoginGetDTO;
import net.airuima.rworker.service.rworker.person.IStaffService;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 * Rworker请求人员相关的Resource
 *
 * <AUTHOR>
 * @date 2022/12/19
 */
@Tag(name = "RWorker-Web员工相关Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/rworker/staff")
public class RworkerStaffResource {
    @Autowired
    private IStaffService[] staffServices;

    /**
     * 员工请求登陆Rworker
     * @param staffRequestDto 登陆请求参数
     * @param httpServletRequest httpServletRequest
     * @return RworkerStaffGetDTO
     */
    @Operation(summary= "员工登陆Rworker")
    @PreAuthorize("@sc.checkSecurity()")
    @PostMapping("/login")
    public ResponseEntity<ResponseData<StaffLoginGetDTO>> login(@RequestBody StaffLoginDTO staffRequestDto, HttpServletRequest httpServletRequest) {
        try {
            StaffLoginGetDTO staffLoginGetDTO = staffServices[0].login(staffRequestDto,httpServletRequest);
            return ResponseData.ok(staffLoginGetDTO);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }
}
