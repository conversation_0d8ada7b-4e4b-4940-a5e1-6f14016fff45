package net.airuima.rworker.web.rest.rworker.process.dto.general;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 工序保存对应不良项目信息
 * <AUTHOR>
 * @date 2023/4/10
 */
@Schema(description = "工序保存对应不良项目信息")
public class UnqualifiedItemSaveInfo implements Serializable {

    /**
     * 不良项目id
     */
    @Schema(description = "不良项目ID")
    private Long id;

    /**
     * 不良项目数量
     */
    @Schema(description = "不良项目数量")
    private int number;

    public UnqualifiedItemSaveInfo(Long id, int number) {
        this.id = id;
        this.number = number;
    }

    public Long getId() {
        return id;
    }

    public UnqualifiedItemSaveInfo setId(Long id) {
        this.id = id;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public UnqualifiedItemSaveInfo setNumber(int number) {
        this.number = number;
        return this;
    }
}
