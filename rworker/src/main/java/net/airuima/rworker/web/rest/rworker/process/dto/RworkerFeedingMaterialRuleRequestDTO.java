package net.airuima.rworker.web.rest.rworker.process.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * Rworker请求获取上料规则信息参数DTO
 *
 * <AUTHOR>
 * @date 2023/2/10
 */
@Schema(description = "Rworker请求获取上料规则信息参数DTO")
public class RworkerFeedingMaterialRuleRequestDTO implements Serializable {

    /**
     * 请求获取上料规则工单DTO列表
     */
    @Schema(description = "请求获取上料规则工单DTO列表")
    private List<WorkSheetInfo> workSheetInfoList;

    /**
     * 工位ID
     */
    @Schema(description = "工位ID")
    private Long workCellId;

    /**
     * 工序ID
     */
    @Schema(description = "工序ID")
    private Long stepId;


    @Schema(description = "请求获取上料规则工单DTO")
    public static class WorkSheetInfo implements Serializable{
        /**
         * 工单ID
         */
        @Schema(description = "工单ID")
        private Long workSheetId;

        /**
         * 是否为在线返工单、纯单支返工或转工艺情形
         */
        @Schema(description = "是否为在线返工单、纯单支返工或转工艺情形")
        private Boolean onlineMaintainOrTransferWorkFlow = Boolean.FALSE;


        /**
         * 投产工单id
         */
        @Schema(description = "投产工单id")
        private Long productWorkSheetId;

        /**
         * 工艺路线ID
         */
        @Schema(description = "工艺路线ID")
        private Long workFlowId;

        /**
         * 投产数
         */
        @Schema(description = "投产数")
        private Integer number;

        public Long getWorkSheetId() {
            return workSheetId;
        }

        public WorkSheetInfo setWorkSheetId(Long workSheetId) {
            this.workSheetId = workSheetId;
            return this;
        }

        public Boolean getOnlineMaintainOrTransferWorkFlow() {
            return onlineMaintainOrTransferWorkFlow;
        }

        public WorkSheetInfo setOnlineMaintainOrTransferWorkFlow(Boolean onlineMaintainOrTransferWorkFlow) {
            this.onlineMaintainOrTransferWorkFlow = onlineMaintainOrTransferWorkFlow;
            return this;
        }

        public Integer getNumber() {
            return number;
        }

        public WorkSheetInfo setNumber(Integer number) {
            this.number = number;
            return this;
        }

        public Long getProductWorkSheetId() {
            return productWorkSheetId;
        }

        public WorkSheetInfo setProductWorkSheetId(Long productWorkSheetId) {
            this.productWorkSheetId = productWorkSheetId;
            return this;
        }

        public Long getWorkFlowId() {
            return workFlowId;
        }

        public WorkSheetInfo setWorkFlowId(Long workFlowId) {
            this.workFlowId = workFlowId;
            return this;
        }
    }

    public List<WorkSheetInfo> getWorkSheetInfoList() {
        return workSheetInfoList;
    }

    public RworkerFeedingMaterialRuleRequestDTO setWorkSheetInfoList(List<WorkSheetInfo> workSheetInfoList) {
        this.workSheetInfoList = workSheetInfoList;
        return this;
    }

    public Long getWorkCellId() {
        return workCellId;
    }

    public RworkerFeedingMaterialRuleRequestDTO setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
        return this;
    }

    public Long getStepId() {
        return stepId;
    }

    public RworkerFeedingMaterialRuleRequestDTO setStepId(Long stepId) {
        this.stepId = stepId;
        return this;
    }

}
