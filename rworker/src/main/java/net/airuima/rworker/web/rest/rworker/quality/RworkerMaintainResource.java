package net.airuima.rworker.web.rest.rworker.quality;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.rbase.dto.rworker.maintain.dto.MaintainAnalyseDTO;
import net.airuima.rbase.dto.rworker.maintain.dto.MaintainHistoryInfoDTO;
import net.airuima.rbase.dto.rworker.maintain.dto.MaintainHistorySaveDTO;
import net.airuima.rbase.proxy.maintain.RbaseIMaintainServiceProxy;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/3/23
 */
@Tag(name = "RWorker维修分析相关Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/rworker/maintain")
public class RworkerMaintainResource {

    @Autowired
    private RbaseIMaintainServiceProxy rbaseIMaintainServiceProxy;

    /**
     * 获取维修分析信息
     *
     * @param maintainAnalyseDTO RWorker请求维修分析参数DTO
     * @return org.springframework.http.ResponseEntity<net.airuima.web.rest.procedure.maintaincase.dto.MaintainHistoryInfoDTO>
     */
    @Operation(summary= "获取维修分析信息")
    @PreAuthorize("@sc.checkSecurity()")
    @PostMapping("/contions")
    public ResponseEntity<ResponseData<MaintainHistoryInfoDTO>> getMaintainHistoryInfo(@RequestBody MaintainAnalyseDTO maintainAnalyseDTO) {
        try {
            return ResponseData.ok(rbaseIMaintainServiceProxy.getMaintainAnalyseInfo(maintainAnalyseDTO));
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 保存维修历史记录
     *
     * @param maintainHistorySaveDto Rworker保存维修分析记录信息
     * @return org.springframework.http.ResponseEntity<net.airuima.rbase.dto.client.base.BaseClientDTO>
     */
    @Operation(summary= "保存维修历史记录")
    @PreAuthorize("@sc.checkSecurity()")
    @PreventRepeatSubmit
    @PostMapping("/custom")
    public ResponseEntity<ResponseData<Void>> saveMaintainHistoryInfo(@RequestBody MaintainHistorySaveDTO maintainHistorySaveDto) {
        try {
            rbaseIMaintainServiceProxy.saveMaintainAnalyseInfo(maintainHistorySaveDto);
            return ResponseData.ok();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

}
