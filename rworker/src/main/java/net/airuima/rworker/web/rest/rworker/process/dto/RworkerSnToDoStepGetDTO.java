package net.airuima.rworker.web.rest.rworker.process.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.constant.Constants;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.dto.client.AgeingConfigInfoDTO;
import net.airuima.rbase.dto.client.BakeConfigInfoDTO;
import net.airuima.rbase.dto.client.CycleBakeConfigInfoDTO;
import net.airuima.rbase.dto.dynamic.StepDynamicDataGetDTO;
import net.airuima.rbase.dto.rworker.process.dto.BaseWaringDTO;
import net.airuima.rbase.dto.rworker.process.dto.general.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * Rworker-Web请求返回SN待做工序信息DTO
 * <AUTHOR>
 * @date 2023/4/6
 */
@Schema(description = "Rworker-Web请求返回SN待做工序信息DTO")
public class RworkerSnToDoStepGetDTO extends BaseWaringDTO implements Serializable {

    /**
     * 待生产流转SN
     */
    @Schema(description = "待生产流转SN")
    private String sn;

    /**
     * 内部SN对应的YSN
     */
    @Schema(description = "内部SN对应的YSN")
    private String ysn;

    /**
     * 工艺路线ID
     */
    @Schema(description = "工艺路线ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long workFlowId;


    /**
     * 是否为在线返工单、纯单支返工或转工艺情形
     */
    @Schema(description = "是否为在线返工单、纯单支返工或转工艺情形")
    private Boolean onlineMaintainOrTransferWorkFlow = Boolean.FALSE;


    /**
     * 工序id
     */
    @Schema(description = "工序ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 工序名称
     */
    @Schema(description = "工序名称")
    private String name;

    /**
     * 工序编码
     */
    @Schema(description = "工序编码")
    private String code;

    /**
     * 工序类型
     */
    @Schema(description = "工序类型")
    private Integer category;

    /**
     * 当前工序投产数
     */
    @Schema(description = "工序投产数")
    private Integer number;

    /**
     * 工序开始时间
     */
    @Schema(description = "工序开始时间")
    private LocalDateTime startTime;

    /**
     * 生产模式(0:批量;1:单支)
     */
    @Schema(description = "生产模式")
    private Integer controlMode;

    /**
     * 是否绑定容器(false:否;true:是)
     */
    @Schema(description = "是否绑定容器")
    private Boolean bindContainer;

    /**
     * 是否上料(true:是;false:否)
     */
    @Schema(description = "是否上料")
    private Boolean isFeedingMaterial;

    /**
     * 物料库存管控级别(0:不管控物料库存;1:总工单物料库存;2:工位物料库存)
     */
    @Schema(description = "物料库存管控级别(0:不管控物料库存;1:总工单物料库存;2:工位物料库存)")
    private Integer materialControlLevel;

    /**
     * 工序技术指标
     */
    @Schema(description = "工序技术指标")
    private String specification;

    /**
     * 工序SOP信息列表
     */
    @Schema(description = "工序SOP信息列表")
    private List<ProcessDocumentGetInfo> stepSopInfoList;

    /**
     * 产品工艺图信息列表
     */
    @Schema(description = "产品工艺图信息列表")
    private List<ProcessDocumentGetInfo> productsCraftInfoList;

    /**
     * 工序不合格项目列表
     */
    @Schema(description = "工序不合格项目列表")
    private List<UnqualifiedItemGetInfo> unqualifiedItemInfoList;

    /**
     * 工序设备列表
     */
    @Schema(description = "工序设备列表")
    private List<FacilityGetInfo> facilityInfoList;

    /**
     * 工单工艺快照列表
     */
    @Schema(description = "工单工艺快照列表")
    private List<WsStepGetInfo> wsStepInfoList;

    /**
     * 工单BOM清单列表
     */
    @Schema(description = "工单BOM清单列表")
    private List<BomMaterialGetInfo> bomMaterialInfoList;


    /**
     * 待投产工单信息
     */
    @Schema(description = "待投产工单信息")
    private RworkerToDoWsGetDTO workSheetInfo;


    /**
     * 动态数据表单DTO
     */
    @Schema(description = "动态数据表单DTO")
    private StepDynamicDataGetDTO stepDynamicDataGetDto;

    /**
     * 可见工序动态数据表单
     */
    @Schema(description = "可见工序动态数据表单")
    private List<StepDynamicDataGetDTO> stepDynamicDataGetVisibleDtoList;

    /**
     * 易损件规则信息
     */
    @Schema(description = "易损件规则信息")
    private List<WearingPartGroupGetInfo> wearingPartGroupInfoList;

    /**
     * 烘烤配置参数详情
     */
    @Schema(description = "烘烤配置参数详情")
    private BakeConfigInfoDTO bakeConfigInfo;

    /**
     * 温循配置参数详情
     */
    @Schema(description = "温循配置参数详情")
    private CycleBakeConfigInfoDTO cycleBakeConfigInfo;

    /**
     * 老化配置参数详情
     */
    @Schema(description = "老化配置参数详情")
    private AgeingConfigInfoDTO ageingConfigInfo;

    public RworkerSnToDoStepGetDTO() {

    }

    public RworkerSnToDoStepGetDTO(WsStep wsStep) {
        this.id = wsStep.getStep().getId();
        this.code = wsStep.getStep().getCode();
        this.name = wsStep.getStep().getName();
        this.category = wsStep.getCategory();
        this.number = Constants.INT_ONE;
        this.controlMode = wsStep.getControlMode();
        this.bindContainer = wsStep.getIsBindContainer();
        this.startTime = LocalDateTime.now();
    }

    public String getSn() {
        return sn;
    }

    public RworkerSnToDoStepGetDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public String getYsn() {
        return ysn;
    }

    public RworkerSnToDoStepGetDTO setYsn(String ysn) {
        this.ysn = ysn;
        return this;
    }

    public Long getId() {
        return id;
    }

    public RworkerSnToDoStepGetDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getWorkFlowId() {
        return workFlowId;
    }

    public RworkerSnToDoStepGetDTO setWorkFlowId(Long workFlowId) {
        this.workFlowId = workFlowId;
        return this;
    }


    public Boolean getOnlineMaintainOrTransferWorkFlow() {
        return onlineMaintainOrTransferWorkFlow;
    }

    public RworkerSnToDoStepGetDTO setOnlineMaintainOrTransferWorkFlow(Boolean onlineMaintainOrTransferWorkFlow) {
        this.onlineMaintainOrTransferWorkFlow = onlineMaintainOrTransferWorkFlow;
        return this;
    }

    public String getName() {
        return name;
    }

    public RworkerSnToDoStepGetDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public RworkerSnToDoStepGetDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public RworkerSnToDoStepGetDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public RworkerSnToDoStepGetDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public RworkerSnToDoStepGetDTO setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
        return this;
    }

    public Integer getControlMode() {
        return controlMode;
    }

    public RworkerSnToDoStepGetDTO setControlMode(Integer controlMode) {
        this.controlMode = controlMode;
        return this;
    }

    public Boolean getBindContainer() {
        return bindContainer;
    }

    public RworkerSnToDoStepGetDTO setBindContainer(Boolean bindContainer) {
        this.bindContainer = bindContainer;
        return this;
    }

    public Boolean getIsFeedingMaterial() {
        return isFeedingMaterial;
    }

    public RworkerSnToDoStepGetDTO setIsFeedingMaterial(Boolean feedingMaterial) {
        isFeedingMaterial = feedingMaterial;
        return this;
    }

    public Integer getMaterialControlLevel() {
        return materialControlLevel;
    }

    public RworkerSnToDoStepGetDTO setMaterialControlLevel(Integer materialControlLevel) {
        this.materialControlLevel = materialControlLevel;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public RworkerSnToDoStepGetDTO setSpecification(String specification) {
        this.specification = specification;
        return this;
    }

    public List<ProcessDocumentGetInfo> getStepSopInfoList() {
        return stepSopInfoList;
    }

    public RworkerSnToDoStepGetDTO setStepSopInfoList(List<ProcessDocumentGetInfo> stepSopInfoList) {
        this.stepSopInfoList = stepSopInfoList;
        return this;
    }

    public List<ProcessDocumentGetInfo> getProductsCraftInfoList() {
        return productsCraftInfoList;
    }

    public RworkerSnToDoStepGetDTO setProductsCraftInfoList(List<ProcessDocumentGetInfo> productsCraftInfoList) {
        this.productsCraftInfoList = productsCraftInfoList;
        return this;
    }

    public List<UnqualifiedItemGetInfo> getUnqualifiedItemInfoList() {
        return unqualifiedItemInfoList;
    }

    public RworkerSnToDoStepGetDTO setUnqualifiedItemInfoList(List<UnqualifiedItemGetInfo> unqualifiedItemInfoList) {
        this.unqualifiedItemInfoList = unqualifiedItemInfoList;
        return this;
    }

    public List<FacilityGetInfo> getFacilityInfoList() {
        return facilityInfoList;
    }

    public RworkerSnToDoStepGetDTO setFacilityInfoList(List<FacilityGetInfo> facilityInfoList) {
        this.facilityInfoList = facilityInfoList;
        return this;
    }

    public List<WsStepGetInfo> getWsStepInfoList() {
        return wsStepInfoList;
    }

    public RworkerSnToDoStepGetDTO setWsStepInfoList(List<WsStepGetInfo> wsStepInfoList) {
        this.wsStepInfoList = wsStepInfoList;
        return this;
    }

    public List<BomMaterialGetInfo> getBomMaterialInfoList() {
        return bomMaterialInfoList;
    }

    public RworkerSnToDoStepGetDTO setBomMaterialInfoList(List<BomMaterialGetInfo> bomMaterialInfoList) {
        this.bomMaterialInfoList = bomMaterialInfoList;
        return this;
    }

    public RworkerToDoWsGetDTO getWorkSheetInfo() {
        return workSheetInfo;
    }

    public RworkerSnToDoStepGetDTO setWorkSheetInfo(RworkerToDoWsGetDTO workSheetInfo) {
        this.workSheetInfo = workSheetInfo;
        return this;
    }

    public StepDynamicDataGetDTO getStepDynamicDataGetDto() {
        return stepDynamicDataGetDto;
    }

    public RworkerSnToDoStepGetDTO setStepDynamicDataGetDto(StepDynamicDataGetDTO stepDynamicDataGetDto) {
        this.stepDynamicDataGetDto = stepDynamicDataGetDto;
        return this;
    }


    public List<StepDynamicDataGetDTO> getStepDynamicDataGetVisibleDtoList() {
        return stepDynamicDataGetVisibleDtoList;
    }

    public RworkerSnToDoStepGetDTO setStepDynamicDataGetVisibleDtoList(List<StepDynamicDataGetDTO> stepDynamicDataGetVisibleDtoList) {
        this.stepDynamicDataGetVisibleDtoList = stepDynamicDataGetVisibleDtoList;
        return this;
    }

    public List<WearingPartGroupGetInfo> getWearingPartGroupInfoList() {
        return wearingPartGroupInfoList;
    }

    public RworkerSnToDoStepGetDTO setWearingPartGroupInfoList(List<WearingPartGroupGetInfo> wearingPartGroupInfoList) {
        this.wearingPartGroupInfoList = wearingPartGroupInfoList;
        return this;
    }

    public BakeConfigInfoDTO getBakeConfigInfo() {
        return bakeConfigInfo;
    }

    public RworkerSnToDoStepGetDTO setBakeConfigInfo(BakeConfigInfoDTO bakeConfigInfo) {
        this.bakeConfigInfo = bakeConfigInfo;
        return this;
    }

    public CycleBakeConfigInfoDTO getCycleBakeConfigInfo() {
        return cycleBakeConfigInfo;
    }

    public RworkerSnToDoStepGetDTO setCycleBakeConfigInfo(CycleBakeConfigInfoDTO cycleBakeConfigInfo) {
        this.cycleBakeConfigInfo = cycleBakeConfigInfo;
        return this;
    }

    public AgeingConfigInfoDTO getAgeingConfigInfo() {
        return ageingConfigInfo;
    }

    public RworkerSnToDoStepGetDTO setAgeingConfigInfo(AgeingConfigInfoDTO ageingConfigInfo) {
        this.ageingConfigInfo = ageingConfigInfo;
        return this;
    }
}
