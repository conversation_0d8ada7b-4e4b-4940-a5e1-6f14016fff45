package net.airuima.rworker.web.rest.rworker.process.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 * 当前工位待做工单DTO
 * <AUTHOR>
 * @date 2022/12/21
 */
@Schema(description = "Rworker-Web请求返回待做工单列表信息DTO")
public class RworkerToDoWsGetDTO implements Serializable {

    /**
     * 投产工单id
     */
    @Schema(description = "投产工单ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long productWorkSheetId;

    /**
     * 工单ID
     */
    @Schema(description = "工单ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long workSheetId;

    /**
     * 工单号
     */
    @Schema(description = "工单号")
    private String serialNumber;

    /**
     * 工单数量
     */
    @Schema(description = "工单数量")
    private Integer number;

    /**
     * 工单类型
     */
    @Schema(description = "工单类型")
    private Integer category;

    /**
     * 优先级
     */
    @Schema(description = "优先级")
    private Integer priority;

    /**
     * 计划完工日期
     */
    @Schema(description = "计划完工日期")
    private LocalDateTime planEndDate;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 投产粒度(0:子工单;1:工单)
     */
    @Schema(description = "投产粒度(0:子工单;1:工单)")
    private Integer mode;

    /**
     * 工单所属产品谱系信息
     */
    @Schema(description = "工单所属产品谱系信息")
    private PedigreeInfo pedigreeInfo;

    /**
     * 工单所属组织架构信息
     */
    @Schema(description = "工单所属组织架构信息")
    private OrganizationInfo organizationInfo;

    /**
     * 工单所属生产线信息
     */
    @Schema(description = "工单所属生产线信息")
    private WorkLineInfo workLineInfo;

    /**
     * 工单所属客户信息
     */
    @Schema(description = "工单所属客户信息")
    private ClientInfo clientInfo;

    public RworkerToDoWsGetDTO() {

    }

    public RworkerToDoWsGetDTO(WorkSheet workSheet) {
        this.productWorkSheetId = workSheet.getId();
        this.workSheetId = workSheet.getId();
        this.category = workSheet.getCategory();
        this.mode = Constants.INT_ONE;
        this.number = workSheet.getNumber();
        this.serialNumber = workSheet.getSerialNumber();
        this.status = workSheet.getStatus();
        this.pedigreeInfo = new PedigreeInfo(workSheet);
        this.organizationInfo = new OrganizationInfo(workSheet);
        this.workLineInfo = new WorkLineInfo(workSheet);
        this.clientInfo = new ClientInfo(workSheet);
        this.priority = workSheet.getPriority();
        this.planEndDate = workSheet.getPlanEndDate();
    }

    public RworkerToDoWsGetDTO(SubWorkSheet subWorkSheet) {
        this.productWorkSheetId = subWorkSheet.getId();
        this.workSheetId = subWorkSheet.getWorkSheet().getId();
        this.category = subWorkSheet.getWorkSheet().getCategory();
        this.mode = Constants.INT_ZERO;
        this.number = subWorkSheet.getNumber();
        this.serialNumber = subWorkSheet.getSerialNumber();
        this.status = subWorkSheet.getStatus();
        this.pedigreeInfo = new PedigreeInfo(subWorkSheet.getWorkSheet());
        this.organizationInfo = new OrganizationInfo(subWorkSheet.getWorkSheet());
        this.workLineInfo = new WorkLineInfo(subWorkSheet);
        this.clientInfo = new ClientInfo(subWorkSheet.getWorkSheet());
        this.priority = subWorkSheet.getPriority();
        this.planEndDate = subWorkSheet.getPlanEndDate();
    }

    public Long getProductWorkSheetId() {
        return productWorkSheetId;
    }

    public RworkerToDoWsGetDTO setProductWorkSheetId(Long productWorkSheetId) {
        this.productWorkSheetId = productWorkSheetId;
        return this;
    }

    public Long getWorkSheetId() {
        return workSheetId;
    }

    public RworkerToDoWsGetDTO setWorkSheetId(Long workSheetId) {
        this.workSheetId = workSheetId;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public RworkerToDoWsGetDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public RworkerToDoWsGetDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public RworkerToDoWsGetDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public Integer getPriority() {
        return priority;
    }

    public RworkerToDoWsGetDTO setPriority(Integer priority) {
        this.priority = priority;
        return this;
    }

    public LocalDateTime getPlanEndDate() {
        return planEndDate;
    }

    public RworkerToDoWsGetDTO setPlanEndDate(LocalDateTime planEndDate) {
        this.planEndDate = planEndDate;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public RworkerToDoWsGetDTO setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public Integer getMode() {
        return mode;
    }

    public RworkerToDoWsGetDTO setMode(Integer mode) {
        this.mode = mode;
        return this;
    }

    public PedigreeInfo getPedigreeInfo() {
        return pedigreeInfo;
    }

    public RworkerToDoWsGetDTO setPedigreeInfo(PedigreeInfo pedigreeInfo) {
        this.pedigreeInfo = pedigreeInfo;
        return this;
    }

    public OrganizationInfo getOrganizationInfo() {
        return organizationInfo;
    }

    public RworkerToDoWsGetDTO setOrganizationInfo(OrganizationInfo organizationInfo) {
        this.organizationInfo = organizationInfo;
        return this;
    }

    public WorkLineInfo getWorkLineInfo() {
        return workLineInfo;
    }

    public RworkerToDoWsGetDTO setWorkLineInfo(WorkLineInfo workLineInfo) {
        this.workLineInfo = workLineInfo;
        return this;
    }

    public ClientInfo getClientInfo() {
        return clientInfo;
    }

    public RworkerToDoWsGetDTO setClientInfo(ClientInfo clientInfo) {
        this.clientInfo = clientInfo;
        return this;
    }

    @Schema(description = "产品谱系信息")
    public static class PedigreeInfo implements Serializable{
        /**
         * 产品谱系id
         */
        @Schema(description = "产品谱系ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 产品谱系名称
         */
        @Schema(description = "产品谱系名称")
        private String name;

        /**
         * 产品谱系编码
         */
        @Schema(description = "产品谱系编码")
        private String code;

        /**
         * 产品谱系型号规格
         */
        @Schema(description = "产品谱系规格型号")
        private String specification;

        public PedigreeInfo() {

        }

        public PedigreeInfo(WorkSheet workSheet){
            this.id = workSheet.getPedigree().getId();
            this.name = workSheet.getPedigree().getName();
            this.code = workSheet.getPedigree().getCode();
            this.specification = workSheet.getPedigree().getSpecification();
        }

        public Long getId() {
            return id;
        }

        public PedigreeInfo setId(Long id) {
            this.id = id;
            return this;
        }

        public String getName() {
            return name;
        }

        public PedigreeInfo setName(String name) {
            this.name = name;
            return this;
        }

        public String getCode() {
            return code;
        }

        public PedigreeInfo setCode(String code) {
            this.code = code;
            return this;
        }

        public String getSpecification() {
            return specification;
        }

        public PedigreeInfo setSpecification(String specification) {
            this.specification = specification;
            return this;
        }
    }

    @Schema(description = "组织架构信息")
    public static class OrganizationInfo implements Serializable{
        /**
         * 组织架构ID
         */
        @Schema(description = "组织架构ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 组织架构名称
         */
        @Schema(description = "组织架构名称")
        private String name;

        /**
         * 组织架构编码
         */
        @Schema(description = "组织架构编码")
        private String code;

        public OrganizationInfo() {

        }

        public OrganizationInfo(WorkSheet workSheet) {
            this.id = workSheet.getOrganizationId();
            this.code = workSheet.getOrganizationDto().getCode();
            this.name = workSheet.getOrganizationDto().getName();;

        }

        public Long getId() {
            return id;
        }

        public OrganizationInfo setId(Long id) {
            this.id = id;
            return this;
        }

        public String getName() {
            return name;
        }

        public OrganizationInfo setName(String name) {
            this.name = name;
            return this;
        }

        public String getCode() {
            return code;
        }

        public OrganizationInfo setCode(String code) {
            this.code = code;
            return this;
        }
    }

    @Schema(description = "生产线信息")
    public static class WorkLineInfo implements Serializable{
        /**
         * 生产线ID
         */
        @Schema(description = "生产线ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 生产线名称
         */
        @Schema(description = "生产线名称")
        private String name;

        /**
         * 生产线编码
         */
        @Schema(description = "生产线编码")
        private String code;

        public WorkLineInfo() {

        }

        public WorkLineInfo(WorkSheet workSheet) {
            this.id = workSheet.getWorkLine().getId();
            this.code = workSheet.getWorkLine().getCode();
            this.name = workSheet.getWorkLine().getName();
        }

        public WorkLineInfo(SubWorkSheet subWorkSheet) {
            this.id = subWorkSheet.getWorkLine().getId();
            this.code = subWorkSheet.getWorkLine().getCode();
            this.name = subWorkSheet.getWorkLine().getName();
        }

        public Long getId() {
            return id;
        }

        public WorkLineInfo setId(Long id) {
            this.id = id;
            return this;
        }

        public String getName() {
            return name;
        }

        public WorkLineInfo setName(String name) {
            this.name = name;
            return this;
        }

        public String getCode() {
            return code;
        }

        public WorkLineInfo setCode(String code) {
            this.code = code;
            return this;
        }
    }

    @Schema(description = "客户信息")
    public static class ClientInfo implements Serializable{
        /**
         * 客户ID
         */
        @Schema(description = "客户ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 客户名称
         */
        @Schema(description = "客户名称")
        private String name;

        /**
         * 客户编码
         */
        @Schema(description = "客户编码")
        private String code;

        public ClientInfo() {

        }

        public ClientInfo(WorkSheet workSheet) {
            this.id = workSheet.getClientId();
            this.name = workSheet.getClientDTO().getName();
            this.code = workSheet.getClientDTO().getCode();
        }

        public Long getId() {
            return id;
        }

        public ClientInfo setId(Long id) {
            this.id = id;
            return this;
        }

        public String getName() {
            return name;
        }

        public ClientInfo setName(String name) {
            this.name = name;
            return this;
        }

        public String getCode() {
            return code;
        }

        public ClientInfo setCode(String code) {
            this.code = code;
            return this;
        }
    }

}
