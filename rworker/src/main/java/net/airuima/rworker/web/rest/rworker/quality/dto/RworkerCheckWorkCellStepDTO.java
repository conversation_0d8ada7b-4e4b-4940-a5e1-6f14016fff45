package net.airuima.rworker.web.rest.rworker.quality.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/2/22
 */
@Schema(description = "Rworker-Web请求首检工位绑定工序请求参数DTO")
public class RworkerCheckWorkCellStepDTO implements Serializable {

    /**
     * 投产工单编码
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "投产工单编码")
    private String serialNumber;

    /**
     * 工位ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "工位ID")
    private Long workCellId;

    public String getSerialNumber() {
        return serialNumber;
    }

    public RworkerCheckWorkCellStepDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public Long getWorkCellId() {
        return workCellId;
    }

    public RworkerCheckWorkCellStepDTO setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
        return this;
    }

}
