package net.airuima.rworker.web.rest.rworker.material.dto;

import net.airuima.rbase.domain.base.pedigree.PedigreeStepMaterialRule;
import net.airuima.rbase.domain.procedure.batch.WsMaterial;
import net.airuima.rbase.domain.procedure.material.WsMaterialBatch;
import net.airuima.rbase.domain.procedure.material.WsWorkCellMaterialBatch;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 工序上料规则通用数据信息，避免反复查询
 * <AUTHOR>
 * @date 2023/3/21
 */
public class RworkerFeedingMaterialBaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 产品谱系工序上料规则
     */
    private List<PedigreeStepMaterialRule> pedigreeStepMaterialRuleList;

    /**
     * 工单投料单
     */
    private List<WsMaterial> wsMaterialList;

    /**
     * 工单物料批次库存
     */
    private List<WsMaterialBatch> wsMaterialBatchList;

    /**
     * 工单工位物料批次库存
     */
    private List<WsWorkCellMaterialBatch> wsWorkCellMaterialBatchList;

    public List<PedigreeStepMaterialRule> getPedigreeStepMaterialRuleList() {
        return pedigreeStepMaterialRuleList;
    }

    public RworkerFeedingMaterialBaseDTO setPedigreeStepMaterialRuleList(List<PedigreeStepMaterialRule> pedigreeStepMaterialRuleList) {
        this.pedigreeStepMaterialRuleList = pedigreeStepMaterialRuleList;
        return this;
    }

    public List<WsMaterial> getWsMaterialList() {
        return wsMaterialList;
    }

    public RworkerFeedingMaterialBaseDTO setWsMaterialList(List<WsMaterial> wsMaterialList) {
        this.wsMaterialList = wsMaterialList;
        return this;
    }

    public List<WsMaterialBatch> getWsMaterialBatchList() {
        return wsMaterialBatchList;
    }

    public RworkerFeedingMaterialBaseDTO setWsMaterialBatchList(List<WsMaterialBatch> wsMaterialBatchList) {
        this.wsMaterialBatchList = wsMaterialBatchList;
        return this;
    }

    public List<WsWorkCellMaterialBatch> getWsWorkCellMaterialBatchList() {
        return wsWorkCellMaterialBatchList;
    }

    public RworkerFeedingMaterialBaseDTO setWsWorkCellMaterialBatchList(List<WsWorkCellMaterialBatch> wsWorkCellMaterialBatchList) {
        this.wsWorkCellMaterialBatchList = wsWorkCellMaterialBatchList;
        return this;
    }

    @Override
    public String toString() {
        return super.toString();
    }
}
