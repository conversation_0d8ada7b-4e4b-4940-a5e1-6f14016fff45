package net.airuima.rworker.web.rest.client;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AppKey;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.dto.client.*;
import net.airuima.rbase.dto.standardpart.StandardPartDTO;
import net.airuima.rbase.proxy.calibrate.RbaseCalibrateCheckResultProxy;
import net.airuima.rbase.proxy.standardpart.RbaseStandardPartProxy;
import net.airuima.rworker.service.client.ClientQualityService;
import net.airuima.rworker.service.client.api.IClientQualityService;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseContent;
import net.airuima.web.rest.errors.BadRequestAlertException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-03-23
 */
@Tag(name = "Rworker-Client质量相关Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/client/qualities")
public class ClientQualityResource {
    private static final String ERR_MSG = "参数输入有误！";
    private final ClientQualityService clientQualityService;
    @Autowired
    private RbaseCalibrateCheckResultProxy rbaseCalibrateCheckResultProxy;
    @Autowired
    private  IClientQualityService[] clientQualityServices;
    @Autowired
    private RbaseStandardPartProxy rbaseStandardPartProxy;

    public ClientQualityResource(ClientQualityService clientQualityService) {
        this.clientQualityService = clientQualityService;
    }

    /**
     * 通过工位、子工单验证首检与QC抽检
     *
     * @param clientGetCheckInfoDto Rworker请求参数
     * @return ResponseEntity<ResponseContent < ClientGetCheckInfoDTO>>
     * <AUTHOR>
     * @date 2021-03-23
     **/
    @Operation(summary= "通过工位、子工单验证是否需要首检与巡检")
    @PostMapping("/check-process-quality")
    public ResponseEntity<ResponseContent<ClientGetCheckInfoDTO>> checkProcessQualityInfo(@RequestBody ClientGetCheckInfoDTO clientGetCheckInfoDto) {
        try {
            return ResponseContent.isOk(clientQualityServices[0].checkProcessInspection(clientGetCheckInfoDto));
        } catch (BadRequestAlertException e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(e.getEntityName(), e.getErrorKey(), e.getTitle())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
        }
    }

    /**
     * 通过检测工位、子工单获取可检测的生产工位及对应工序列表
     *
     * @param clientGetCheckInfoDto Rworker请求参数
     * @return ResponseEntity<ResponseContent < ClientGetCheckInfoDTO>>
     * <AUTHOR>
     * @date 2021-03-23
     **/
    @Operation(summary= "通过检测工位、子工单获取可检测的生产工位及对应工序列表")
    @PostMapping("/get-work-cell-step-info")
    public ResponseEntity<ResponseContent<ClientGetCheckInfoDTO>> getToInspectedWorkCellAndStepInfo(@RequestBody ClientGetCheckInfoDTO clientGetCheckInfoDto) {
        try {
            return ResponseContent.isOk(clientQualityServices[0].getToInspectedWorkCellAndStepInfo(clientGetCheckInfoDto));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
        }
    }

    /**
     * 通过子工单及工序获取检测规则及检测项目信息
     *
     * @param clientGetCheckInfoDto Rworker请求参数
     * @return ResponseEntity<ResponseContent < ClientGetCheckInfoDTO>>
     * <AUTHOR>
     * @date 2021-03-23
     **/
    @Operation(summary= "通过子工单及工序获取检测规则及检测项目信息")
    @PostMapping("/get-check-rule-info")
    public ResponseEntity<ResponseContent<ClientGetCheckInfoDTO>> getCheckRuleInfo(@RequestBody ClientGetCheckInfoDTO clientGetCheckInfoDto) {
        try {
            return ResponseContent.isOk(clientQualityService.getCheckRuleInfo(clientGetCheckInfoDto));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
        }
    }

    /**
     * 保存检测结果数据
     *
     * @param clientSaveCheckInfoDTO Rworker请求参数
     * @return ResponseEntity<ResponseContent < ClientSaveCheckInfoDTO>>
     * <AUTHOR>
     * @date 2021-03-23
     **/
    @Operation(summary= "保存首检或QC检测结果数据")
    @PostMapping("/save-check-result-info")
    public ResponseEntity<ResponseContent<ClientSaveCheckInfoDTO>> saveCheckRuleInfo(@RequestBody ClientSaveCheckInfoDTO clientSaveCheckInfoDTO) {
        try {
            return ResponseContent.isOk(clientQualityService.saveCheckInfo(clientSaveCheckInfoDTO));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
        }
    }

    /**
     * 保存提前预警信息
     *
     * @param clientSaveUnqualifiedEventDto 提前预警参数
     * @return ResponseEntity<ResponseContent < BaseClientDTO>>
     * <AUTHOR>
     * @date 2021-06-13
     **/
    @Operation(summary= "保存提前预警信息")
    @PostMapping("/unqualified-event")
    public ResponseEntity<ResponseContent<BaseClientDTO>> saveUnqualifiedEvent(@RequestBody ClientSaveUnqualifiedEventDTO clientSaveUnqualifiedEventDto) {
        try {
            return ResponseContent.isOk(clientQualityService.saveUnqualifiedEvent(clientSaveUnqualifiedEventDto));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
        }
    }

    /**
     * 保存标准件测试数据及明细
     *
     * @param clientStandardPartCheckResultSaveDtoList 标准件测试数据参数列表DTO
     * @return BaseClientDTO
     */
    @Operation(summary= "保存标准件测试数据")
    @PostMapping("/standard-part-check-result")
    public ResponseEntity<ResponseContent<BaseClientDTO>> standardPartCheckResult(@RequestBody @Valid List<ClientStandardPartCheckResultSaveDTO> clientStandardPartCheckResultSaveDtoList, BindingResult bindingResult) {
        try {
            if (bindingResult.hasErrors()) {
                String messages = bindingResult.getAllErrors().stream().map(ObjectError::getDefaultMessage).reduce((m1, m2) -> m1 + "；" + m2).orElse(ERR_MSG);
                return ResponseContent.isOk(new BaseClientDTO(Constants.KO, messages));
            }
            return ResponseContent.isOk(clientQualityServices[0].saveStandardPartCheckResultDetail(clientStandardPartCheckResultSaveDtoList));
        } catch (Exception e) {
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
        }
    }

    /**
     * 保存台位GRR测试数据
     *
     * @param clientGrrHistorySaveDtoList 台位GRR测试数据参数列表DTO
     * @return BaseClientDTO
     */
    @Operation(summary= "保存台位GRR测试数据")
    @PostMapping("/work-cell-grr-history")
    public ResponseEntity<ResponseContent<BaseClientDTO>> workCellGrrHistory(@RequestBody @Valid List<ClientGrrHistorySaveDTO> clientGrrHistorySaveDtoList, BindingResult bindingResult) {
        try {
            if (bindingResult.hasErrors()) {
                String messages = bindingResult.getAllErrors().stream().map(ObjectError::getDefaultMessage).reduce((m1, m2) -> m1 + "；" + m2).orElse(ERR_MSG);
                return ResponseContent.isOk(new BaseClientDTO(Constants.KO, messages));
            }
            return ResponseContent.isOk(clientQualityServices[0].saveGrrHistory(clientGrrHistorySaveDtoList));
        } catch (Exception e) {
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
        }
    }

    /**
     * 保存台位校准数据
     *
     * @param clientCalibrateCheckResultSaveDTOList 台位校准数据参数列表DTO
     * @return BaseClientDTO
     */
    @Operation(summary= "保存台位校准数据")
    @PostMapping("/calibrate-check-result")
    public ResponseEntity<ResponseContent<BaseClientDTO>> calibrateCheckResult(@RequestBody @Valid List<ClientCalibrateCheckResultSaveDTO> clientCalibrateCheckResultSaveDTOList, BindingResult bindingResult) {
        try {
            if (bindingResult.hasErrors()) {
                String messages = bindingResult.getAllErrors().stream().map(ObjectError::getDefaultMessage).reduce((m1, m2) -> m1 + "；" + m2).orElse(ERR_MSG);
                return ResponseContent.isOk(new BaseClientDTO(Constants.KO, messages));
            }
            return ResponseContent.isOk(rbaseCalibrateCheckResultProxy.saveCalibrateCheckResult(clientCalibrateCheckResultSaveDTOList));
        } catch (Exception e) {
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
        }
    }

    /**
     * 通过标准件SN获取标准件信息
     *
     * @param sn 标准件SN
     * @return StandardPartGetDTO
     */
    @Operation(summary= "通过标准件SN获取标准件信息")
    @GetMapping("/standard-part")
    public ResponseEntity<ResponseContent<ClientStandardPartGetDTO>> standardPartBySn(@RequestParam("sn") String sn) {
        try {
            Optional<StandardPartDTO> standardPartOptional = rbaseStandardPartProxy.findBySnAndDeleted(sn, Constants.LONG_ZERO);
            if (!standardPartOptional.isPresent()) {
                return ResponseContent.isOk(new ClientStandardPartGetDTO(new BaseClientDTO(Constants.KO, "标准件不存在")));
            }
            return ResponseContent.isOk(new ClientStandardPartGetDTO(standardPartOptional.get()));
        } catch (Exception e) {
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
        }
    }

    /**
     * 通过台位编码获取最新比对数据
     * @param workCellCode 台位编码
     * @return ClientCalibrateLatestResultGetDTO
     */
    @Operation(summary= "通过台位编码获取最新比对数据")
    @GetMapping("/calibrate-check-result/{workCellCode}")
    public ResponseEntity<ResponseContent<ClientCalibrateLatestResultGetDTO>> latestCalibrateResult(@PathVariable("workCellCode") String workCellCode) {
        try{
            return ResponseContent.isOk(clientQualityService.findLatestCalibrateResultByWorkCellCode(workCellCode));
        }catch (Exception e){
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
        }
    }

    /**
     * 获取指定子工单工序待处理的预警事件
     * @param clientUnqualifiedEventGetDto 指定预警信息
     * <AUTHOR>
     * @date  2022/12/1
     * @return ClientUnqualifiedEventDTO
     */
    @Operation(summary= "获取指定子工单工序待处理的预警事件")
    @PostMapping("/get-unqualified-event")
    public ResponseEntity<ResponseContent<ClientUnqualifiedEventDTO>> getUnqualifiedEventInfo(@RequestBody ClientUnqualifiedEventGetDTO clientUnqualifiedEventGetDto){
        try {
            return ResponseContent.isOk(clientQualityService.getUnqualifiedEventInfo(clientUnqualifiedEventGetDto));
        }catch (Exception e){
            e.printStackTrace();
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
         }

    }

}

