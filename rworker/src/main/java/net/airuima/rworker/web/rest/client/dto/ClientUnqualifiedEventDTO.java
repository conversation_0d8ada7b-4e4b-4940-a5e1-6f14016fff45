package net.airuima.rworker.web.rest.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.domain.procedure.quality.UnqualifiedEvent;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/1
 */
@Schema(description = "返回Rworker预警事件详情")
public class ClientUnqualifiedEventDTO extends BaseClientDTO {

    @Schema(description = "预警事件详情列表")
    List<UnqualifiedEventInfo> unqualifiedEventInfoList;

    public ClientUnqualifiedEventDTO() {
    }

    public ClientUnqualifiedEventDTO(String status, List<UnqualifiedEventInfo> unqualifiedEventInfoList) {
        super(status);
        this.unqualifiedEventInfoList = unqualifiedEventInfoList;
    }

    public ClientUnqualifiedEventDTO(String status, String message) {
        super(status, message);
    }

    public List<UnqualifiedEventInfo> getUnqualifiedEventInfoList() {
        return unqualifiedEventInfoList;
    }

    public ClientUnqualifiedEventDTO setUnqualifiedEventInfoList(List<UnqualifiedEventInfo> unqualifiedEventInfoList) {
        this.unqualifiedEventInfoList = unqualifiedEventInfoList;
        return this;
    }

    @Schema(description = "预警事件详情")
    public static class UnqualifiedEventInfo{

        /**
         *  事件类型 0:警告1:停线
         */
        @Schema(description = "事件类型0:警告1:停线")
        private Integer eventType;

        /**
         * 预警原因类型 0：生产不良数量超标；1：工序良率不达标
         */
        @Schema(description = "预警原因类型 0：生产不良数量超标；1：工序良率不达标")
        private Integer warningReasonType;

        /**
         * 预警单号
         */
        @Schema(description = "预警单号")
        private String warningSerialNumber;

        /**
         * 子工单
         */
        @Schema(description = "子工单")
        private String subWsSerialNumber;

        /**
         * 不良项目编码
         */
        @Schema(description = "不良项目编码")
        private String unqualifiedItemCode;

        /**
         * 不良项目名称
         */
        @Schema(description = "不良项目名称")
        private String unqualifiedItemName;

        /**
         * 产品代码编码
         */
        @Schema(description = "产品代码编码")
        private String pedigreeCode;

        /**
         * 产品代码名称
         */
        @Schema(description = "产品代码名称")
        private String pedigreeName;

        /**
         * 不良工序编码
         */
        @Schema(description = "不良工序编码")
        private String stepCode;

        /**
         * 不良工序名称
         */
        @Schema(description = "不良工序名称")
        private String stepName;

        /**
         * 工位编码
         */
        @Schema(description = "工位编码")
        private String workCellCode;

        /**
         * 工位名称
         */
        @Schema(description = "工位名称")
        private String workCellName;

        /**
         * 预警时间
         */
        @Schema(description = "预警时间")
        private LocalDateTime recordTime;

        /**
         * 责任人工号
         */
        @Schema(description = "责任人工号")
        private String ownerStaffCode;

        /**
         * 责任人名称
         */
        @Schema(description = "责任人名称")
        private String ownerStaffName;

        public UnqualifiedEventInfo() {
        }

        public UnqualifiedEventInfo(UnqualifiedEvent unqualifiedEvent) {
            this.eventType = unqualifiedEvent.getEventType();
            this.warningReasonType = unqualifiedEvent.getReasonType();
            this.warningSerialNumber = unqualifiedEvent.getSerialNumber();
            this.subWsSerialNumber = unqualifiedEvent.getSubWorkSheet().getSerialNumber();
            this.unqualifiedItemCode = ObjectUtils.isEmpty(unqualifiedEvent.getUnqualifiedItem())?null:unqualifiedEvent.getUnqualifiedItem().getCode();
            this.unqualifiedItemName = ObjectUtils.isEmpty(unqualifiedEvent.getUnqualifiedItem())?null:unqualifiedEvent.getUnqualifiedItem().getName();
            this.pedigreeCode = unqualifiedEvent.getSubWorkSheet().getWorkSheet().getPedigree().getCode();
            this.pedigreeName = unqualifiedEvent.getSubWorkSheet().getWorkSheet().getPedigree().getName();
            this.stepCode = unqualifiedEvent.getStep().getCode();
            this.stepName = unqualifiedEvent.getStep().getName();
            this.workCellCode = unqualifiedEvent.getWorkCell().getCode();
            this.workCellName = unqualifiedEvent.getWorkCell().getName();
            this.recordTime = unqualifiedEvent.getRecordTime();
            this.ownerStaffCode = unqualifiedEvent.getOwnerDto().getCode();
            this.ownerStaffName = unqualifiedEvent.getOwnerDto().getName();
        }

        public Integer getEventType() {
            return eventType;
        }

        public UnqualifiedEventInfo setEventType(Integer eventType) {
            this.eventType = eventType;
            return this;
        }

        public Integer getWarningReasonType() {
            return warningReasonType;
        }

        public UnqualifiedEventInfo setWarningReasonType(Integer warningReasonType) {
            this.warningReasonType = warningReasonType;
            return this;
        }

        public String getWarningSerialNumber() {
            return warningSerialNumber;
        }

        public UnqualifiedEventInfo setWarningSerialNumber(String warningSerialNumber) {
            this.warningSerialNumber = warningSerialNumber;
            return this;
        }

        public String getSubWsSerialNumber() {
            return subWsSerialNumber;
        }

        public UnqualifiedEventInfo setSubWsSerialNumber(String subWsSerialNumber) {
            this.subWsSerialNumber = subWsSerialNumber;
            return this;
        }

        public String getUnqualifiedItemCode() {
            return unqualifiedItemCode;
        }

        public UnqualifiedEventInfo setUnqualifiedItemCode(String unqualifiedItemCode) {
            this.unqualifiedItemCode = unqualifiedItemCode;
            return this;
        }

        public String getUnqualifiedItemName() {
            return unqualifiedItemName;
        }

        public UnqualifiedEventInfo setUnqualifiedItemName(String unqualifiedItemName) {
            this.unqualifiedItemName = unqualifiedItemName;
            return this;
        }

        public String getPedigreeCode() {
            return pedigreeCode;
        }

        public UnqualifiedEventInfo setPedigreeCode(String pedigreeCode) {
            this.pedigreeCode = pedigreeCode;
            return this;
        }

        public String getPedigreeName() {
            return pedigreeName;
        }

        public UnqualifiedEventInfo setPedigreeName(String pedigreeName) {
            this.pedigreeName = pedigreeName;
            return this;
        }

        public String getStepCode() {
            return stepCode;
        }

        public UnqualifiedEventInfo setStepCode(String stepCode) {
            this.stepCode = stepCode;
            return this;
        }

        public String getStepName() {
            return stepName;
        }

        public UnqualifiedEventInfo setStepName(String stepName) {
            this.stepName = stepName;
            return this;
        }

        public String getWorkCellCode() {
            return workCellCode;
        }

        public UnqualifiedEventInfo setWorkCellCode(String workCellCode) {
            this.workCellCode = workCellCode;
            return this;
        }

        public String getWorkCellName() {
            return workCellName;
        }

        public UnqualifiedEventInfo setWorkCellName(String workCellName) {
            this.workCellName = workCellName;
            return this;
        }

        public LocalDateTime getRecordTime() {
            return recordTime;
        }

        public UnqualifiedEventInfo setRecordTime(LocalDateTime recordTime) {
            this.recordTime = recordTime;
            return this;
        }

        public String getOwnerStaffCode() {
            return ownerStaffCode;
        }

        public UnqualifiedEventInfo setOwnerStaffCode(String ownerStaffCode) {
            this.ownerStaffCode = ownerStaffCode;
            return this;
        }

        public String getOwnerStaffName() {
            return ownerStaffName;
        }

        public UnqualifiedEventInfo setOwnerStaffName(String ownerStaffName) {
            this.ownerStaffName = ownerStaffName;
            return this;
        }
    }
}
