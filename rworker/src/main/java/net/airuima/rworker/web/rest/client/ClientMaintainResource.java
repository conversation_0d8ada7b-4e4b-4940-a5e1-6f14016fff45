package net.airuima.rworker.web.rest.client;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.dto.client.ClientWorkFlowStepDTO;
import net.airuima.rbase.dto.client.GetMaintainAnalyseDTO;
import net.airuima.rbase.dto.client.MaintainAnalyseInfoDTO;
import net.airuima.rbase.dto.client.SaveMaterialAnalyseDTO;
import net.airuima.rworker.service.client.ClientMaintainService;
import net.airuima.rworker.service.client.api.IClientMaintainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/10/20
 */
@Tag(name = "Rworker-Client维修分析相关Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/client/maintain")
public class ClientMaintainResource {

    @Autowired
    private ClientMaintainService clientMaintainService;
    
    @Autowired
    private IClientMaintainService[] clientMaintainServices;

    /**
     * 获取维修分析信息
     * @param getMaintainAnalyseDto
     * <AUTHOR>
     * @date  2022/9/30
     * @return MaintainAnalyseInfoDTO
     */
    @PostMapping("/get-maintain-analyse-info")
    public ResponseEntity<MaintainAnalyseInfoDTO> getMaintainAnalyseInfo(@RequestBody GetMaintainAnalyseDTO getMaintainAnalyseDto){
        try{
            return ResponseEntity.ok(clientMaintainServices[0].getMaintainAnalyseInfo(getMaintainAnalyseDto));
        }catch (Exception e){
            e.printStackTrace();
            return ResponseEntity.ok(new MaintainAnalyseInfoDTO(new BaseClientDTO(Constants.KO,e.toString())));
        }
    }

    /**
     * 保存维修历史记录
     * @param saveMaterialAnalyseDTOList 维修历史信息
     * <AUTHOR>
     * @date  2022/10/11
     * @return ResponseEntity<BaseClientDTO>
     */
    @PostMapping("/save-maintain-analyse-info")
    public ResponseEntity<BaseClientDTO> saveMaintainAnalyseInfo(@RequestBody List<SaveMaterialAnalyseDTO> saveMaterialAnalyseDTOList){
        try{
            return ResponseEntity.ok(clientMaintainService.saveMaintainAnalyseInfo(saveMaterialAnalyseDTOList));
        }catch (Exception e){
            e.printStackTrace();
            return ResponseEntity.ok(new BaseClientDTO(Constants.KO,e.toString()));
        }
    }

    /**
     * 获取当前 sn（容器）选择的维修方案对应的 工艺路线信息
     * @param text sn（容器）
     * @param maintainCaseCode 维修方案编码
     * <AUTHOR>
     * @date  2022/10/20
     * @return ClientWorkFlowStepDTO
     */
    @GetMapping("/workflow")
    public ResponseEntity<ClientWorkFlowStepDTO> findMaintainCaseWorkFlowInfo(@RequestParam("text") String text, @RequestParam("maintainCaseCode") String maintainCaseCode){
        return ResponseEntity.ok(clientMaintainService.findMaintainCaseWorkFlowInfo(text, maintainCaseCode));
    }
}
