package net.airuima.rworker.web.rest.client;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.rbase.dto.client.ClientPedigreeConfigDTO;
import net.airuima.rworker.service.client.ClientPedigreeService;
import net.airuima.util.ResponseContent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/7/4
 */
@Tag(name = "Rworker-Client谱系相关Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/client/pedigrees")
public class ClientPedigreeResource {

    @Autowired
    private ClientPedigreeService clientPedigreeService;

    /**
     * 通过产品谱系编码 获取  产品谱系配置
     *
     * @param pedigreeCode 产品谱系编码
     * @return
     */
    @Operation(summary= "通过产品谱系编码 获取  产品谱系配置")
    @GetMapping("/config")
    public ResponseEntity<ResponseContent<ClientPedigreeConfigDTO>> getPedigreeConfigInfo(@RequestParam(value = "pedigreeCode") String pedigreeCode){
        try {
            return ResponseContent.isOk(clientPedigreeService.getPedigreeConfigInfo(pedigreeCode));
        }catch (Exception e){
            e.printStackTrace();
            return ResponseContent.badRequest().message(e.getMessage()).isBadRequestBuild();
        }
    }
}
