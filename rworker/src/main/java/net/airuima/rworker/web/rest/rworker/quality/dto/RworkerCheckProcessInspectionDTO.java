package net.airuima.rworker.web.rest.rworker.quality.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/4/28
 */
@Schema(description = "Rworker-Web请求验证首检巡检 参数DTO")
public class RworkerCheckProcessInspectionDTO {

    /**
     * 是否开班：true：开班 ：false：非开班
     */
    @Schema(description = "true：开班 ：false：非开班")
    private boolean firstTimeWork=false;

    /**
     * 投产工单编码
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "投产工单编码")
    private String serialNumber;

    /**
     * 工位ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "工位ID")
    private Long workCellId;

    public boolean getFirstTimeWork() {
        return firstTimeWork;
    }

    public RworkerCheckProcessInspectionDTO setFirstTimeWork(boolean firstTimeWork) {
        this.firstTimeWork = firstTimeWork;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public RworkerCheckProcessInspectionDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public Long getWorkCellId() {
        return workCellId;
    }

    public RworkerCheckProcessInspectionDTO setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
        return this;
    }
}
