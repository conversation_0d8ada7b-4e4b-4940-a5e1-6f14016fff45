package net.airuima.rworker.web.rest.rworker.process.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.client.AgeingHistoryInfoDTO;
import net.airuima.rbase.dto.client.BakeHistoryInfoDTO;
import net.airuima.rbase.dto.client.CycleBakeHistoryInfoDTO;
import net.airuima.rbase.dto.dynamic.StepDynamicDataColumnGetDTO;
import net.airuima.rbase.dto.rworker.process.dto.general.MaterialSaveInfo;
import net.airuima.rbase.dto.rworker.process.dto.general.UnqualifiedItemSaveInfo;
import net.airuima.rbase.dto.rworker.process.dto.general.WearingPartSaveInfo;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * Rworker-Web保存SN工序参数DTO
 *
 * <AUTHOR>
 * @date 2023/4/10
 */
@Schema(description = "Rworker-Web保存SN工序生产数据参数DTO")
public class RworkerSnStepSaveRequestDTO implements Serializable {

    /**
     * 投产工单id
     */
    @Schema(description = "投产工单id", required = true)
    private Long productWorkSheetId;

    /**
     * 工位id
     */
    @Schema(description = "工位ID", required = true)
    private Long workCellId;

    /**
     * 当前操作人id
     */
    @Schema(description = "操作人ID")
    private Long staffId;

    /**
     * 工序Id
     */
    @Schema(description = "工序Id")
    private Long stepId;

    /**
     * 工序开始时间
     */
    @Schema(description = "工序开始时间")
    private LocalDateTime startTime;

    /**
     * 工序完成日期
     */
    @Schema(description = "工序完成时间")
    private LocalDateTime endTime;

    /**
     * 工单工序投产总数
     */
    @Schema(description = "工单工序投产总数")
    private int number;

    /**
     * 工单工序合格总数
     */
    @Schema(description = "工单工序合格总数")
    private int qualifiedNumber;

    /**
     * 工单工序不合格总数
     */
    @Schema(description = "工单工序不合格总数")
    private int unqualifiedNumber;

    /**
     * 设备ID列表
     */
    @Schema(description = "设备ID列表")
    private List<Long> facilityIdList;

    /**
     * 工单工序不良项目汇总信息列表
     */
    @Schema(description = "工单工序不良项目汇总信息列表")
    private List<UnqualifiedItemSaveInfo> unqualifiedItemSaveInfoList;


    /**
     * 工单工序生产物料汇总信息列表
     */
    @Schema(description = "工单工序生产物料汇总信息列表")
    private List<MaterialSaveInfo> materialInfoList;


    /**
     * 容器SN生产信息列表
     */
    @Schema(description = "容器SN生产信息列表")
    private List<ContainerSnSaveInfo> containerSnSaveInfoList;

    /**
     * 工单SN生产信息列表
     */
    @Schema(description = "工单SN生产信息列表")
    private List<SingleSnSaveInfo> singleSnSaveInfoList;

    public Long getWorkCellId() {
        return workCellId;
    }

    public RworkerSnStepSaveRequestDTO setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
        return this;
    }

    public Long getProductWorkSheetId() {
        return productWorkSheetId;
    }

    public RworkerSnStepSaveRequestDTO setProductWorkSheetId(Long productWorkSheetId) {
        this.productWorkSheetId = productWorkSheetId;
        return this;
    }

    public Long getStaffId() {
        return staffId;
    }

    public RworkerSnStepSaveRequestDTO setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public Long getStepId() {
        return stepId;
    }

    public RworkerSnStepSaveRequestDTO setStepId(Long stepId) {
        this.stepId = stepId;
        return this;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public RworkerSnStepSaveRequestDTO setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
        return this;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public RworkerSnStepSaveRequestDTO setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public RworkerSnStepSaveRequestDTO setNumber(int number) {
        this.number = number;
        return this;
    }

    public int getQualifiedNumber() {
        return qualifiedNumber;
    }

    public RworkerSnStepSaveRequestDTO setQualifiedNumber(int qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public int getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public RworkerSnStepSaveRequestDTO setUnqualifiedNumber(int unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public List<Long> getFacilityIdList() {
        return facilityIdList;
    }

    public RworkerSnStepSaveRequestDTO setFacilityIdList(List<Long> facilityIdList) {
        this.facilityIdList = facilityIdList;
        return this;
    }

    public List<MaterialSaveInfo> getMaterialInfoList() {
        return materialInfoList;
    }

    public RworkerSnStepSaveRequestDTO setMaterialInfoList(List<MaterialSaveInfo> materialInfoList) {
        this.materialInfoList = materialInfoList;
        return this;
    }

    public List<UnqualifiedItemSaveInfo> getUnqualifiedItemSaveInfoList() {
        return unqualifiedItemSaveInfoList;
    }

    public RworkerSnStepSaveRequestDTO setUnqualifiedItemSaveInfoList(List<UnqualifiedItemSaveInfo> unqualifiedItemSaveInfoList) {
        this.unqualifiedItemSaveInfoList = unqualifiedItemSaveInfoList;
        return this;
    }

    public List<ContainerSnSaveInfo> getContainerSnSaveInfoList() {
        return containerSnSaveInfoList;
    }

    public RworkerSnStepSaveRequestDTO setContainerSnSaveInfoList(List<ContainerSnSaveInfo> containerSnSaveInfoList) {
        this.containerSnSaveInfoList = containerSnSaveInfoList;
        return this;
    }

    public List<SingleSnSaveInfo> getSingleSnSaveInfoList() {
        return singleSnSaveInfoList;
    }

    public RworkerSnStepSaveRequestDTO setSingleSnSaveInfoList(List<SingleSnSaveInfo> singleSnSaveInfoList) {
        this.singleSnSaveInfoList = singleSnSaveInfoList;
        return this;
    }

    /**
     * 单支SN生产信息
     */
    @Schema(description = "单支SN生产信息")
    public static class SingleSnSaveInfo implements Serializable{

        /**
         * 投产SN
         */
        @Schema(description = "投产SN")
        private String sn;

        /**
         * YSN
         */
        @Schema(description = "YSN")
        private String ysn;

        /**
         * SN工序投产数
         */
        @Schema(description = "SN工序投产数(默认1)")
        private int number;

        /**
         * SN工序合格数量
         */
        @Schema(description = "SN工序合格数量(1或者0)")
        private int qualifiedNumber;

        /**
         * SN工序不合格数量
         */
        @Schema(description = "SN工序不合格数量(1或0)")
        private int unqualifiedNumber;

        /**
         * SN工序不良信息
         */
        @Schema(description = "SN工序不良信息")
        private UnqualifiedItemSaveInfo unqualifiedItemSaveInfo;

        /**
         * SN生产物料信息列表
         */
        @Schema(description = "SN生产物料信息列表")
        private List<MaterialSaveInfo> materialInfoList;


        /**
         * SN工序易损件信息列表
         */
        @Schema(description = "SN工序易损件信息列表")
        private List<WearingPartSaveInfo> wearingPartInfoList;

        /**
         * SN动态数据信息
         */
        @Schema(description = "SN动态数据信息")
        private StepDynamicDataColumnGetDTO stepDynamicDataColumnGetDto;

        /**
         * SN烘烤信息
         */
        @Schema(description = "SN烘烤信息")
        private BakeHistoryInfoDTO bakeHistoryInfo;

        /**
         * SN温循信息
         */
        @Schema(description = "SN温循信息")
        private CycleBakeHistoryInfoDTO cycleBakeHistoryInfo;

        /**
         * SN老化信息
         */
        @Schema(description = "SN老化信息")
        private AgeingHistoryInfoDTO ageingHistoryInfo;

        public String getSn() {
            return sn;
        }

        public SingleSnSaveInfo setSn(String sn) {
            this.sn = sn;
            return this;
        }

        public String getYsn() {
            return ysn;
        }

        public SingleSnSaveInfo setYsn(String ysn) {
            this.ysn = ysn;
            return this;
        }

        public int getNumber() {
            return number;
        }

        public SingleSnSaveInfo setNumber(int number) {
            this.number = number;
            return this;
        }

        public int getQualifiedNumber() {
            return qualifiedNumber;
        }

        public SingleSnSaveInfo setQualifiedNumber(int qualifiedNumber) {
            this.qualifiedNumber = qualifiedNumber;
            return this;
        }

        public int getUnqualifiedNumber() {
            return unqualifiedNumber;
        }

        public SingleSnSaveInfo setUnqualifiedNumber(int unqualifiedNumber) {
            this.unqualifiedNumber = unqualifiedNumber;
            return this;
        }

        public UnqualifiedItemSaveInfo getUnqualifiedItemSaveInfo() {
            return unqualifiedItemSaveInfo;
        }

        public SingleSnSaveInfo setUnqualifiedItemSaveInfo(UnqualifiedItemSaveInfo unqualifiedItemSaveInfo) {
            this.unqualifiedItemSaveInfo = unqualifiedItemSaveInfo;
            return this;
        }

        public List<MaterialSaveInfo> getMaterialInfoList() {
            return materialInfoList;
        }

        public SingleSnSaveInfo setMaterialInfoList(List<MaterialSaveInfo> materialInfoList) {
            this.materialInfoList = materialInfoList;
            return this;
        }

        public List<WearingPartSaveInfo> getWearingPartInfoList() {
            return wearingPartInfoList;
        }

        public SingleSnSaveInfo setWearingPartInfoList(List<WearingPartSaveInfo> wearingPartInfoList) {
            this.wearingPartInfoList = wearingPartInfoList;
            return this;
        }

        public StepDynamicDataColumnGetDTO getStepDynamicDataColumnGetDto() {
            return stepDynamicDataColumnGetDto;
        }

        public SingleSnSaveInfo setStepDynamicDataColumnGetDto(StepDynamicDataColumnGetDTO stepDynamicDataColumnGetDto) {
            this.stepDynamicDataColumnGetDto = stepDynamicDataColumnGetDto;
            return this;
        }

        public BakeHistoryInfoDTO getBakeHistoryInfo() {
            return bakeHistoryInfo;
        }

        public SingleSnSaveInfo setBakeHistoryInfo(BakeHistoryInfoDTO bakeHistoryInfo) {
            this.bakeHistoryInfo = bakeHistoryInfo;
            return this;
        }

        public CycleBakeHistoryInfoDTO getCycleBakeHistoryInfo() {
            return cycleBakeHistoryInfo;
        }

        public SingleSnSaveInfo setCycleBakeHistoryInfo(CycleBakeHistoryInfoDTO cycleBakeHistoryInfo) {
            this.cycleBakeHistoryInfo = cycleBakeHistoryInfo;
            return this;
        }

        public AgeingHistoryInfoDTO getAgeingHistoryInfo() {
            return ageingHistoryInfo;
        }

        public SingleSnSaveInfo setAgeingHistoryInfo(AgeingHistoryInfoDTO ageingHistoryInfo) {
            this.ageingHistoryInfo = ageingHistoryInfo;
            return this;
        }
    }

    /**
     * 容器SN生产信息
     */
    @Schema(description = "容器SN生产信息")
    public static class ContainerSnSaveInfo implements Serializable{
        /**
         * 新绑定的容器ID
         */
        @Schema(description = "新绑定的容器ID")
        private Long bindContainerId;

        /**
         * 请求的容器Id列表
         */
        @Schema(description = "请求的容器Id列表")
        private List<RworkerContainerStepSaveRequestDTO.BingContainerInfo.RequestContainerInfo> requestContainerInfoList;

        /**
         * 容器工序投产总数
         */
        @Schema(description = "容器工序投产总数")
        private Integer number;

        /**
         * 容器工序合格总数
         */
        @Schema(description = "容器工序合格总数")
        private Integer qualifiedNumber;

        /**
         * 容器工序不合格总数
         */
        @Schema(description = "容器工序不合格总数")
        private Integer unqualifiedNumber;

        /**
         * 容器工序不良汇总信息列表
         */
        @Schema(description = "容器工序不良汇总信息列表")
        private List<UnqualifiedItemSaveInfo> unqualifiedItemSaveInfoList;

        /**
         * 容器生产物料汇总信息列表
         */
        @Schema(description = "容器生产物料汇总信息列表")
        private List<MaterialSaveInfo> materialInfoList;

        /**
         * 新绑定容器内SN信息列表
         */
        @Schema(description = "新绑定容器内SN信息列表")
        private List<SingleSnSaveInfo> singleSnSaveInfoList;

        public Integer getNumber() {
            return number;
        }

        public ContainerSnSaveInfo setNumber(Integer number) {
            this.number = number;
            return this;
        }

        public Integer getQualifiedNumber() {
            return qualifiedNumber;
        }

        public ContainerSnSaveInfo setQualifiedNumber(Integer qualifiedNumber) {
            this.qualifiedNumber = qualifiedNumber;
            return this;
        }

        public Integer getUnqualifiedNumber() {
            return unqualifiedNumber;
        }

        public ContainerSnSaveInfo setUnqualifiedNumber(Integer unqualifiedNumber) {
            this.unqualifiedNumber = unqualifiedNumber;
            return this;
        }

        public List<UnqualifiedItemSaveInfo> getUnqualifiedItemSaveInfoList() {
            return unqualifiedItemSaveInfoList;
        }

        public ContainerSnSaveInfo setUnqualifiedItemSaveInfoList(List<UnqualifiedItemSaveInfo> unqualifiedItemSaveInfoList) {
            this.unqualifiedItemSaveInfoList = unqualifiedItemSaveInfoList;
            return this;
        }

        public List<MaterialSaveInfo> getMaterialInfoList() {
            return materialInfoList;
        }

        public ContainerSnSaveInfo setMaterialInfoList(List<MaterialSaveInfo> materialInfoList) {
            this.materialInfoList = materialInfoList;
            return this;
        }

        public Long getBindContainerId() {
            return bindContainerId;
        }

        public ContainerSnSaveInfo setBindContainerId(Long bindContainerId) {
            this.bindContainerId = bindContainerId;
            return this;
        }

        public List<RworkerContainerStepSaveRequestDTO.BingContainerInfo.RequestContainerInfo> getRequestContainerInfoList() {
            return requestContainerInfoList;
        }

        public ContainerSnSaveInfo setRequestContainerInfoList(List<RworkerContainerStepSaveRequestDTO.BingContainerInfo.RequestContainerInfo> requestContainerInfoList) {
            this.requestContainerInfoList = requestContainerInfoList;
            return this;
        }

        public List<SingleSnSaveInfo> getSingleSnSaveInfoList() {
            return singleSnSaveInfoList;
        }

        public ContainerSnSaveInfo setSingleSnSaveInfoList(List<SingleSnSaveInfo> singleSnSaveInfoList) {
            this.singleSnSaveInfoList = singleSnSaveInfoList;
            return this;
        }
    }
}
