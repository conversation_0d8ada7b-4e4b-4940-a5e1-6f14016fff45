package net.airuima.rworker.web.rest.client.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/5
 */
@Schema(description = "Rworker请求获取工序工序报表信息DTO")
public class ClientStaffPerformReportGetDTO {

    /**
     * 工序id
     */
    @Schema(description = "工序id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long stepId;

    /**
     * 工位id
     */
    @Schema(description = "工位id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long workCellId;

    /**
     * 时间段列表
     */
    @Schema(description = "时间段列表")
    private List<TimePeriodInfo> timePeriodInfos;

    public Long getStepId() {
        return stepId;
    }

    public ClientStaffPerformReportGetDTO setStepId(Long stepId) {
        this.stepId = stepId;
        return this;
    }

    public Long getWorkCellId() {
        return workCellId;
    }

    public ClientStaffPerformReportGetDTO setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
        return this;
    }

    public List<TimePeriodInfo> getTimePeriodInfos() {
        return timePeriodInfos;
    }

    public ClientStaffPerformReportGetDTO setTimePeriodInfos(List<TimePeriodInfo> timePeriodInfos) {
        this.timePeriodInfos = timePeriodInfos;
        return this;
    }

    @Schema(description = "时间段")
    public static class TimePeriodInfo{
        @Schema(description = "开始日期")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
        private LocalDateTime startTime;

        @Schema(description = "结束日期")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
        private LocalDateTime endTime;

        public LocalDateTime getStartTime() {
            return startTime;
        }

        public TimePeriodInfo setStartTime(LocalDateTime startTime) {
            this.startTime = startTime;
            return this;
        }

        public LocalDateTime getEndTime() {
            return endTime;
        }

        public TimePeriodInfo setEndTime(LocalDateTime endTime) {
            this.endTime = endTime;
            return this;
        }
    }
}
