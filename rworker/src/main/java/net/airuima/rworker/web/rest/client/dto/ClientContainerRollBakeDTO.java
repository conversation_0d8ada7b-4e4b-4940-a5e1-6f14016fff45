package net.airuima.rworker.web.rest.client.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/11/28
 */
public class ClientContainerRollBakeDTO extends BaseClientDTO {

    @Schema(description = "当前容器详情id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long containerDetailId;

    @Schema(description = "被占用容器详情列表")
    private List<ClientContainerDetailInfo> clientContainerDetailInfoList;

    public ClientContainerRollBakeDTO() {
    }

    public ClientContainerRollBakeDTO(String status, String message) {
        super(status, message);
    }

    public ClientContainerRollBakeDTO(String status, String message,Long containerDetailId) {
        super(status, message);
        this.containerDetailId = containerDetailId;
    }

    public ClientContainerRollBakeDTO(String status, String message, Long containerDetailId,List<ClientContainerDetailInfo> clientContainerDetailInfoList) {
        super(status, message);
        this.containerDetailId = containerDetailId;
        this.clientContainerDetailInfoList = clientContainerDetailInfoList;
    }

    public Long getContainerDetailId() {
        return containerDetailId;
    }

    public ClientContainerRollBakeDTO setContainerDetailId(Long containerDetailId) {
        this.containerDetailId = containerDetailId;
        return this;
    }

    public List<ClientContainerDetailInfo> getClientContainerDetailInfoList() {
        return clientContainerDetailInfoList;
    }

    public ClientContainerRollBakeDTO setClientContainerDetailInfoList(List<ClientContainerDetailInfo> clientContainerDetailInfoList) {
        this.clientContainerDetailInfoList = clientContainerDetailInfoList;
        return this;
    }

    @Schema(description = "被占用容器详情信息")
    public static class ClientContainerDetailInfo{
        @JsonSerialize(using = ToStringSerializer.class)
        @Schema(description = "被占用容器id")
        private Long containerDetailId;

        @Schema(description = "容器编码")
        private String containerCode;

        public ClientContainerDetailInfo() {
        }

        public ClientContainerDetailInfo(ContainerDetail containerDetail) {
            this.containerDetailId = containerDetail.getId();
            this.containerCode = containerDetail.getContainer().getCode();
        }

        public Long getContainerDetailId() {
            return containerDetailId;
        }

        public ClientContainerDetailInfo setContainerDetailId(Long containerDetailId) {
            this.containerDetailId = containerDetailId;
            return this;
        }

        public String getContainerCode() {
            return containerCode;
        }

        public ClientContainerDetailInfo setContainerCode(String containerCode) {
            this.containerCode = containerCode;
            return this;
        }
    }
}
