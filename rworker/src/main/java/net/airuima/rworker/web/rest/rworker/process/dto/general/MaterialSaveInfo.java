package net.airuima.rworker.web.rest.rworker.process.dto.general;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 工序保存时对应物料信息
 * <AUTHOR>
 * @date 2023/4/10
 */
@Schema(description = "工序物料信息")
public class MaterialSaveInfo implements Serializable {
    /**
     * 物料id
     */
    @Schema(description = "物料ID")
    private Long id;

    /**
     * 物料批次
     */
    @Schema(description = "物料批次")
    private String batch;

    /**
     * 是否核物料批次(true:是;false:否)
     */
    @Schema(description = "是否核物料批次(true:核对物料批次;false:不核对物料批次)")
    private boolean isCheckMaterialBatch;

    /**
     * 是否扣除库存(true:是;false:否)
     */
    @Schema(description = "是否扣除库存(true:是;false:否)")
    private boolean isDeduct;

    /**
     * 上料数量
     */
    @Schema(description = "上料数量")
    private double number;

    /**
     * 上料类型(0:单只序列号;1:批次号)
     */
    @Schema(description = "上料类型(0:单只序列号;1:物料批次)")
    private int granularity=1;

    public Long getId() {
        return id;
    }

    public MaterialSaveInfo setId(Long id) {
        this.id = id;
        return this;
    }

    public String getBatch() {
        return batch;
    }

    public MaterialSaveInfo setBatch(String batch) {
        this.batch = batch;
        return this;
    }

    public boolean getIsCheckMaterialBatch() {
        return isCheckMaterialBatch;
    }

    public MaterialSaveInfo setIsCheckMaterialBatch(boolean checkMaterialBatch) {
        this.isCheckMaterialBatch = checkMaterialBatch;
        return this;
    }

    public boolean getIsDeduct() {
        return isDeduct;
    }

    public MaterialSaveInfo setIsDeduct(boolean deduct) {
        this.isDeduct = deduct;
        return this;
    }

    public double getNumber() {
        return number;
    }

    public MaterialSaveInfo setNumber(double number) {
        this.number = number;
        return this;
    }

    public int getGranularity() {
        return granularity;
    }

    public MaterialSaveInfo setGranularity(int granularity) {
        this.granularity = granularity;
        return this;
    }
}
