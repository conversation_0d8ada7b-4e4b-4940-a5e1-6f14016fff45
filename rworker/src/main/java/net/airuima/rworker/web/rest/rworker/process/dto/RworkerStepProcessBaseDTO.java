package net.airuima.rworker.web.rest.rworker.process.dto;


import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.dto.maintain.MaintainHistoryDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.rbase.dto.rworker.process.dto.general.FacilityGetInfo;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 工序生产过程相关参数类(主要为后续保存或者请求待做工序信息参数服务，避免反复查询)
 * <AUTHOR>
 * @date 2023/2/7
 */
public class RworkerStepProcessBaseDTO implements Serializable {

    /**
     *  投产粒度： true:子工单粒度，false:工单粒度
     */
    private Boolean subWsProductionMode;

    /**
     * 工艺路线
     */
    private WorkFlow workFlow;

    /**
     * 工单
     */
    private WorkSheet workSheet;

    /**
     * 子工单
     */
    private SubWorkSheet subWorkSheet;

    /**
     * 工单工序快照列表
     */
    private List<WsStep> wsStepList;

    /**
     * 工位
     */
    private WorkCell workCell;

    /**
     * 工单工序快照
     */
    private WsStep wsStep;

    /**
     * 工序
     */
    private Step step;

    /**
     * 员工
     */
    private StaffDTO staffDTO;

    /**
     * 请求对象对应最新维修历史
     */
    private MaintainHistoryDTO maintainHistory;

    /**
     * 工位工序设备列表
     */
    private List<FacilityGetInfo> facilityGetInfoList;

    /**
     * SN生产状态列表
     */
    private List<SnWorkStatus> snWorkStatusList;

    /**
     * SN当前工序最新记录列表
     */
    private List<SnWorkDetail> snStepLatestWorkDetailList;

    public RworkerStepProcessBaseDTO() {

    }

    public RworkerStepProcessBaseDTO(Boolean subWsProductionMode, WorkSheet workSheet, SubWorkSheet subWorkSheet, WorkCell workCell, WsStep wsStep, Step step, StaffDTO staffDTO,List<WsStep> wsStepList) {
        this.subWsProductionMode = subWsProductionMode;
        this.workSheet = workSheet;
        this.subWorkSheet = subWorkSheet;
        this.wsStepList = wsStepList;
        this.workCell = workCell;
        this.wsStep = wsStep;
        this.step = step;
        this.staffDTO = staffDTO;
    }

    public Boolean getSubWsProductionMode() {
        return subWsProductionMode;
    }

    public RworkerStepProcessBaseDTO setSubWsProductionMode(Boolean subWsProductionMode) {
        this.subWsProductionMode = subWsProductionMode;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public RworkerStepProcessBaseDTO setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return Objects.nonNull(workSheet) && Objects.nonNull(workSheet.getId()) ? workSheet:null;
    }

    public RworkerStepProcessBaseDTO setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return Objects.nonNull(subWorkSheet) && Objects.nonNull(subWorkSheet.getId()) ? subWorkSheet:null;
    }

    public RworkerStepProcessBaseDTO setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public List<WsStep> getWsStepList() {
        return wsStepList;
    }

    public RworkerStepProcessBaseDTO setWsStepList(List<WsStep> wsStepList) {
        this.wsStepList = wsStepList;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public RworkerStepProcessBaseDTO setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public WsStep getWsStep() {
        return wsStep;
    }

    public RworkerStepProcessBaseDTO setWsStep(WsStep wsStep) {
        this.wsStep = wsStep;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public RworkerStepProcessBaseDTO setStep(Step step) {
        this.step = step;
        return this;
    }

    public StaffDTO getStaffDTO() {
        return staffDTO;
    }

    public RworkerStepProcessBaseDTO setStaffDTO(StaffDTO staffDTO) {
        this.staffDTO = staffDTO;
        return this;
    }

    public MaintainHistoryDTO getMaintainHistory() {
        return maintainHistory;
    }

    public RworkerStepProcessBaseDTO setMaintainHistory(MaintainHistoryDTO maintainHistory) {
        this.maintainHistory = maintainHistory;
        return this;
    }

    public List<FacilityGetInfo> getFacilityGetInfoList() {
        return facilityGetInfoList;
    }

    public RworkerStepProcessBaseDTO setFacilityGetInfoList(List<FacilityGetInfo> facilityGetInfoList) {
        this.facilityGetInfoList = facilityGetInfoList;
        return this;
    }

    public List<SnWorkStatus> getSnWorkStatusList() {
        return snWorkStatusList;
    }

    public RworkerStepProcessBaseDTO setSnWorkStatusList(List<SnWorkStatus> snWorkStatusList) {
        this.snWorkStatusList = snWorkStatusList;
        return this;
    }

    public List<SnWorkDetail> getSnStepLatestWorkDetailList() {
        return snStepLatestWorkDetailList;
    }

    public RworkerStepProcessBaseDTO setSnStepLatestWorkDetailList(List<SnWorkDetail> snStepLatestWorkDetailList) {
        this.snStepLatestWorkDetailList = snStepLatestWorkDetailList;
        return this;
    }
}
