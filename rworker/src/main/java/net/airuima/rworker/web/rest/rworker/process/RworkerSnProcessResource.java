package net.airuima.rworker.web.rest.rworker.process;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.constant.Constants;
import net.airuima.rbase.dto.rworker.process.dto.RworkerBindSnValidateRequestDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerSnStepSaveRequestDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerSnToDoStepGetDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerSnTodoRequestDTO;
import net.airuima.rworker.service.rworker.cache.IRworkerCacheService;
import net.airuima.rworker.service.rworker.process.ISnProcessRequestService;
import net.airuima.rworker.service.rworker.process.ISnProcessSaveService;
import net.airuima.rworker.web.rest.rworker.process.dto.RworkerBindSnValidateGetDTO;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/4/4
 */
@Tag(name = "RWorker-Web生产过程单支模式相关Resource")
@RestController
@RequestMapping("/api/rworker/sn/processes")
public class RworkerSnProcessResource {
    private static final String ERROR = "error";
    @Autowired
    private ISnProcessRequestService[] snProcessRequestServices;
    @Autowired
    private ISnProcessSaveService[] snProcessSaveServices;
    @Autowired
    private IRworkerCacheService[] rworkerCacheServices;


    /**
     * 通过投产粒度工单ID及SN验证SN是否合规
     *
     * @param rworkerBindSnValidateRequestDTO 验证参数
     * @return org.springframework.http.ResponseEntity<java.lang.Void>
     */
    @Operation(summary = "通过投产粒度工单ID及SN验证SN是否合规")
    @PostMapping("/validate")
    public ResponseEntity<ResponseData<RworkerBindSnValidateGetDTO>> validate(@RequestBody RworkerBindSnValidateRequestDTO rworkerBindSnValidateRequestDTO) {
        try {
            return ResponseData.ok(snProcessRequestServices[0].validate(rworkerBindSnValidateRequestDTO));
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            return ResponseData.error(e);
        }
    }

    /**
     * 获取SN请求模式下的待做工序信息
     *
     * @param rworkerSnTodoRequestDTO 请求参数DTO
     * @return org.springframework.http.ResponseEntity<net.airuima.web.rest.rworker.process.dto.RworkerSnToDoStepGetDTO> SN待投产工序生产信息DTO
     */
    @Operation(summary = "获取SN请求模式下的待做工序信息")
    @PreAuthorize("@sc.checkSecurity()")
    @PostMapping("/todo/")
    public ResponseEntity<ResponseData<RworkerSnToDoStepGetDTO>> snTodoStep(@RequestBody RworkerSnTodoRequestDTO rworkerSnTodoRequestDTO) {
        try {
            RworkerSnToDoStepGetDTO rworkerSnToDoStepGetDTO = snProcessRequestServices[0].snTodoStep(rworkerSnTodoRequestDTO);
            if(StringUtils.isNotBlank(rworkerSnToDoStepGetDTO.getKey()) && StringUtils.isNotBlank(rworkerSnToDoStepGetDTO.getMessage())){
                return ResponseData.error(rworkerSnToDoStepGetDTO.getKey(),rworkerSnToDoStepGetDTO.getMessage());
            }
            return ResponseData.ok(rworkerSnToDoStepGetDTO);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            return ResponseData.error(e);
        }
    }

    /**
     * 保存SN模式下的工序生产信息
     *
     * @param rworkerSnStepSaveRequestDTOList SN待保存生产数据DTO
     */
    @Operation(summary = "保存SN模式下的工序生产信息")
    @PreAuthorize("@sc.checkSecurity()")
    @FuncInterceptor("Single")
    @PreventRepeatSubmit(expireTime = 300)
    @PostMapping("/create")
    public ResponseEntity<ResponseData<Void>> createSnStep(@RequestBody List<RworkerSnStepSaveRequestDTO> rworkerSnStepSaveRequestDTOList) {
        try {
            snProcessSaveServices[0].createSnStep(rworkerSnStepSaveRequestDTOList);
            return ResponseData.ok();
        } catch (ResponseException e) {
            //如果工序已完成则需要将缓存按照工位进行删除
            if(e.getErrorKey().equals("error.stepIsFinished")){
                rworkerCacheServices[0].deletedCacheByWorkCell(rworkerSnStepSaveRequestDTOList.get(Constants.INT_ZERO).getWorkCellId());
            }
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }
}
