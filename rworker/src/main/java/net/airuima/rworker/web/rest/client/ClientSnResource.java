package net.airuima.rworker.web.rest.client;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.dto.client.ClientGetSnStepInfoDTO;
import net.airuima.rbase.dto.client.ClientSaveSnStepInfoDTO;
import net.airuima.rbase.dto.client.ClientSnDTO;
import net.airuima.rbase.dto.ocmes.plugin.SnReplaceDTO;
import net.airuima.rbase.dto.single.SnWorkStatusDTO;
import net.airuima.rbase.web.rest.error.SaveRepeatException;
import net.airuima.rworker.service.client.ClientSnService;
import net.airuima.rworker.service.client.api.IClientSnService;
import net.airuima.util.HeaderUtil;
import net.airuima.util.RedisUtils;
import net.airuima.util.ResponseContent;
import net.airuima.util.ResponseData;
import net.airuima.web.rest.errors.BadRequestAlertException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * RWorkSN相关Resource
 *
 * <AUTHOR>
 * @date 2020/12/29
 */
@Tag(name = "Rworker-Client单支相关Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/client/sn")
public class ClientSnResource {

    private final ClientSnService clientSnService;
    private final RedisUtils redisUtils;
    @Autowired
    private IClientSnService[] clientSnServices;

    public ClientSnResource(ClientSnService clientSnService, RedisUtils redisUtils) {
        this.clientSnService = clientSnService;
        this.redisUtils = redisUtils;
    }



    /***
     * 获取单支工序信息
     *
     * @param clientSnDto 请求传过来的SN信息
     * @return BaseClientDTO
     */
    @Operation(summary= "获取单支工序信息")
    @PostMapping("/get-step-info")
    public ResponseEntity<ResponseContent<ClientGetSnStepInfoDTO>> getStepInfo(@RequestBody ClientSnDTO clientSnDto) {
        try {
            return ResponseContent.isOk(clientSnServices[0].getStepInfo(clientSnDto));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseContent.badRequest().message(e.getMessage()).isBadRequestBuild();
        }
    }


    /**
     * 保存单支工序信息
     * <AUTHOR>
     * @param clientSaveSnStepInfoDto
     * @return ResponseEntity<ResponseContent<BaseClientDTO>>
     * @date 2021-03-18
     **/
    @Operation(summary= "保存单支工序信息")
    @PostMapping("/save-step-info")
    public ResponseEntity<ResponseContent<BaseClientDTO>> saveStepInfo(@RequestBody ClientSaveSnStepInfoDTO clientSaveSnStepInfoDto) {
        try {
            return ResponseContent.isOk(clientSnService.saveStepInfo(clientSaveSnStepInfoDto));
        } catch (SaveRepeatException e){
            return ResponseContent.isOk(new BaseClientDTO(Constants.KO, "当前生产数据已保存,请勿重复提交!"));
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(e.getEntityName(), e.getErrorKey(), e.getTitle())).build();
        } catch (Exception e) {
            e.printStackTrace();
            if(StringUtils.isNotBlank(clientSaveSnStepInfoDto.getXsrfToken())){
                redisUtils.del(clientSaveSnStepInfoDto.getXsrfToken());
            }
            return ResponseContent.badRequest().message(e.getMessage()).isBadRequestBuild();
        }
    }


    /***
     * 校验SN是否合法
     *
     * @param clientSnDto 请求传过来的SN信息
     * @return
     */
    @Operation(summary= "校验SN是否合法")
    @PostMapping("/verification")
    public ResponseEntity<ResponseContent<BaseClientDTO>> verification(@RequestBody ClientSnDTO clientSnDto) {
        try {
            return ResponseContent.isOk(clientSnServices[0].verification(clientSnDto));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
        }
    }


    /**
     * 获取当前sn 最新状态信息
     * @param sn
     * <AUTHOR>
     * @date  2022/10/17
     * @return SnWorkStatusDTO
     */
    @GetMapping("/status/{sn}")
    public ResponseEntity<SnWorkStatusDTO> currentSnInfo(@PathVariable("sn") String sn){
        try{
            SnWorkStatusDTO snWorkStatusDto = clientSnService.getSnWorkStatusInfo(sn);
            return ResponseEntity.ok(snWorkStatusDto);
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 替换sn操作过程数据历史标签
     * @param snReplaceDtoList 替换信息列表
     * <AUTHOR>
     * @date  2023/2/23
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO
     */
    @Operation(summary = "替换sn操作过程数据历史标签")
    @PostMapping("/replace")
    public ResponseEntity<ResponseData<BaseClientDTO>> snReplace(@RequestBody List<SnReplaceDTO> snReplaceDtoList){
        try {
            return ResponseData.ok(clientSnService.snReplace(snReplaceDtoList));
        }catch (Exception e){
            e.printStackTrace();
            return ResponseData.ok(new BaseClientDTO(Constants.KO,e.getMessage()));
        }
    }

}
