package net.airuima.rworker.web.rest.rworker.material;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.rworker.material.dto.RworkerMaterialSaveRequestDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerFeedingMaterialRuleGetDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerFeedingMaterialRuleRequestDTO;
import net.airuima.rworker.service.rworker.material.IMaterialService;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/1/29
 */
@Tag(name = "RWorker-Web生产物料相关Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/rworker/material")
public class RworkerMaterialResource {

    @Autowired
    private IMaterialService[] materialServices;

    /**
     * 获取工单工序上料规则信息
     *
     * @param rworkerFeedingMaterialRuleRequestDTO 工单工序上料规则请求参数
     * @return List<RworkerFeedingMaterialRuleGetDTO>
     */
    @Operation(summary = "获取工单工序上料规则信息")
    @PreAuthorize("@sc.checkSecurity()")
    @PostMapping("/feeding-material-rule")
    public ResponseEntity<ResponseData<List<RworkerFeedingMaterialRuleGetDTO>>> findFeedingMaterialRuleInfo(@RequestBody RworkerFeedingMaterialRuleRequestDTO rworkerFeedingMaterialRuleRequestDTO) {
        try {
            List<RworkerFeedingMaterialRuleGetDTO> rworkerFeedingMaterialRuleGetDTOList = materialServices[0].findStepMaterialInfo(rworkerFeedingMaterialRuleRequestDTO);
            return ResponseData.ok(rworkerFeedingMaterialRuleGetDTOList);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 保存Rworker工位上料信息
     *
     * @param rworkerMaterialSaveRequestDTOList 保存Rworker工位上料信息参数
     */
    @Operation(summary = "保存Rworker工位上料信息")
    @FuncInterceptor("WorksheetMaterial && WsMaterialBatch && WsMaterialBatchNumber && WsWorkCellMaterialBatchNumber")
    @PreAuthorize("@sc.checkSecurity()")
    @PreventRepeatSubmit
    @PostMapping("/create")
    public ResponseEntity<ResponseData<Void>> createFeedingMaterialInfo(@RequestBody List<RworkerMaterialSaveRequestDTO> rworkerMaterialSaveRequestDTOList) {
        try {
            materialServices[0].saveFeedingMaterialInfo(rworkerMaterialSaveRequestDTOList);
            return ResponseData.ok();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 验证Rworker上料的物料编码信息（一般只针对返工单、纯单支在线返及转工艺工单投产时物料不验证BOM及上料规则的情况）
     * @param materialCode 物料编码
     * @return 物料基础信息
     */
    @Operation(summary = "验证Rworker上料的物料编码信息（一般只针对返工单、纯单支在线返及转工艺工单投产时物料不验证BOM及上料规则的情况）")
    @PreAuthorize("@sc.checkSecurity()")
    @PostMapping("/validate/{materialCode}")
    public ResponseEntity<ResponseData<MaterialDTO>> validateMaterial(@PathVariable("materialCode") String materialCode) {
        try {
            return ResponseData.ok( materialServices[0].validateMaterial(materialCode));
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 验证单支序列号唯一性
     * @param serialNumbers 单支序列号集合
     */
    @Operation(summary = "验证Rworker上料的单支序列号唯一性")
    @PreAuthorize("@sc.checkSecurity()")
    @PostMapping("/single/serial-number/validate")
    public ResponseEntity<ResponseData<Void>> validateSingleSerialNumber(@RequestBody List<String> serialNumbers){
        try {
            materialServices[0].validateSingleSerialNumber(serialNumbers);
            return ResponseData.ok();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }
}
