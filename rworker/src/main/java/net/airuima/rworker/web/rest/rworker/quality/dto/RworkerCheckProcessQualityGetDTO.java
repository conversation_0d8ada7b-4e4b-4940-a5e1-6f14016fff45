package net.airuima.rworker.web.rest.rworker.quality.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/2/6
 */
@Schema(description = "Rworker首检-PQC检返回结果DTO")
public class RworkerCheckProcessQualityGetDTO implements Serializable {

    /**
     * 检查结果，false：不需要首检，true，需要首检
     */
    @Schema(description = "检查结果，false：不需要首检，true，需要首检")
    private Boolean result;

    /**
     * 类型(0,首检;1,巡检)
     */
    @Schema(description = "类型(0,首检;1,巡检)")
    private Integer category;

    /**
     * 投产工单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "投产工单ID")
    private Long productWorkSheetId;

    /**
     * 投产工单编码
     */
    @Schema(description = "投产工单编码")
    private String serialNumber;

    /**
     * 检测项目类型
     */
    @Schema(description = "检测项目类型")
    private Integer variety;

    /**
     * 检查结果提示信息
     */
    @Schema(description = "检查结果提示信息")
    private String checkResultMessage;

    /**
     * 下次待检时间
     */
    @Schema(description = "下次待检时间")
    private LocalDateTime nextCheckDate;

    public RworkerCheckProcessQualityGetDTO() {
    }

    public RworkerCheckProcessQualityGetDTO(Boolean result) {
        this.result = result;
    }

    public Boolean getResult() {
        return result;
    }

    public RworkerCheckProcessQualityGetDTO setResult(Boolean result) {
        this.result = result;
        return this;
    }

    public String getCheckResultMessage() {
        return checkResultMessage;
    }

    public RworkerCheckProcessQualityGetDTO setCheckResultMessage(String checkResultMessage) {
        this.checkResultMessage = checkResultMessage;
        return this;
    }

    public LocalDateTime getNextCheckDate() {
        return nextCheckDate;
    }

    public RworkerCheckProcessQualityGetDTO setNextCheckDate(LocalDateTime nextCheckDate) {
        this.nextCheckDate = nextCheckDate;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public RworkerCheckProcessQualityGetDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public Integer getVariety() {
        return variety;
    }

    public RworkerCheckProcessQualityGetDTO setVariety(Integer variety) {
        this.variety = variety;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public RworkerCheckProcessQualityGetDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public Long getProductWorkSheetId() {
        return productWorkSheetId;
    }

    public RworkerCheckProcessQualityGetDTO setProductWorkSheetId(Long productWorkSheetId) {
        this.productWorkSheetId = productWorkSheetId;
        return this;
    }
}
