package net.airuima.rworker.web.rest.rworker.quality.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Rworker质检放行DTO")
public class RworkerCancelTaskDTO {

    /**
     * 任务id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "任务id")
    private Long taskId;
    /**
     * 员工id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "员工id")
    private Long staffId;

    public Long getTaskId() {
        return taskId;
    }

    public RworkerCancelTaskDTO setTaskId(Long taskId) {
        this.taskId = taskId;
        return this;
    }

    public Long getStaffId() {
        return staffId;
    }

    public RworkerCancelTaskDTO setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }
}
