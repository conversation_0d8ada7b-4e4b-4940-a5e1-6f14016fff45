package net.airuima.rworker.web.rest.rworker.process.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * SN请求工序生产参数DTO
 * <AUTHOR>
 * @date 2023/4/6
 */
@Schema(description = "SN请求工序生产参数DTO")
public class RworkerSnTodoRequestDTO implements Serializable {

    /**
     * 员工ID
     */
    @Schema(description = "员工ID", required = true)
    private Long staffId;

    /**
     * 工位id
     */
    @Schema(description = "工位ID", required = true)
    private Long workCellId;

    /**
     * 请求生产的SN
     */
    @Schema(description = "请求生产的SN", required = true)
    private String sn;

    public Long getStaffId() {
        return staffId;
    }

    public RworkerSnTodoRequestDTO setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public Long getWorkCellId() {
        return workCellId;
    }

    public RworkerSnTodoRequestDTO setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public RworkerSnTodoRequestDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }
}
