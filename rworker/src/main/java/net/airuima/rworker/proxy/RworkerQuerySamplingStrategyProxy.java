package net.airuima.rworker.proxy;

import net.airuima.config.bean.BeanDefine;
import net.airuima.config.bean.ObjectField;
import net.airuima.rbase.dto.qms.SampleCaseDTO;
import org.springframework.stereotype.Component;

@Component
public class RworkerQuerySamplingStrategyProxy {

    @BeanDefine(value = "querySamplingStrategyServiceImpl",funcKey = "FAI || IPQC || PQC || FQC || IQC || LQC")
    public Integer getSampleResult(@ObjectField("net.airuima.qms.domain.base.SampleCase") SampleCaseDTO sampleCase,
                                   Integer number) {
        return null;
    }

}
