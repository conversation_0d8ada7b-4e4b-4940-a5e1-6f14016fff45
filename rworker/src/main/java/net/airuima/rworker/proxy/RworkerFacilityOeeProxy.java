package net.airuima.rworker.proxy;

import net.airuima.config.bean.BeanDefine;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RworkerFacilityOeeProxy {

    /**
     * rworker生产时更新oee产量等数据
     *
     */
    @BeanDefine(value = "facilityOeeService",funcKey = "FacilityOee")
    public void updateFacilityOeeProductionByRworker(Long workCellId, List<Long> facilityIds, Integer finishNumber, Integer qualifiedNumber, Integer unqualifiedNumber, LocalDate recordDate){

    }
}
