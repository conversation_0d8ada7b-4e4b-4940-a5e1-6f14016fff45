package net.airuima.rworker.proxy;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rworker.dto.client.GrrLedgerDTO;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RworkerGrrLedgerProxy {

    @BeanDefine(value = "grrLedgerRepository",funcKey = "GRR")
    public Optional<GrrLedgerDTO> findByWorkCellCodeAndDeleted(String workCellCode, Long deleted){
        return Optional.empty();
    }
}
