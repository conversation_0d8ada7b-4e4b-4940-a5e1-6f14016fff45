package net.airuima.rworker.domain;

import io.hypersistence.utils.hibernate.type.json.JsonType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Table;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.dto.rworker.cache.dto.RworkerCacheSaveRequestDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.*;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * Rworker缓存实体类Domain
 * <AUTHOR>
 * @date 2023/11/15
 */
@Schema(name = "Rworker缓存实体类", description = "Rworker缓存实体类")
@Entity
@Table(name = "procedure_rworker_cache",uniqueConstraints = {
        @UniqueConstraint(name = "procedure_rworker_cache_uuid_unique", columnNames = {"uuid", "deleted"}),
        @UniqueConstraint(name = "procedure_rworker_cache_work_cell_unique", columnNames = {"work_cell_id", "deleted"})
})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "rworkerCacheEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class RworkerCache extends CustomBaseEntity implements Serializable {

    /**
     * 缓存唯一标识
     */
    @NotNull
    @Column(name = "uuid")
    @Schema(description = "缓存唯一标识")
    private String uuid;


    /**
     * 请求工序对象类型(0:子工单号;1:工单号；2:容器号；3:SN号)
     */
    @Schema(description = "请求工序对象类型(0:子工单号;1:工单号；2:容器号；3:SN号)")
    @Column(name = "category")
    private int category;

    /**
     * 生产工位
     */
    @NotNull
    @ManyToOne
    @Schema(description = "生产工位")
    @JoinColumn(name = "work_cell_id")
    private WorkCell workCell;

    /**
     * 生产工序
     */
    @NotNull
    @ManyToOne
    @Schema(description = "生产工序")
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 工序开始时间
     */
    @NotNull
    @Column(name = "start_time")
    @Schema(description = "工序开始时间")
    private LocalDateTime startTime;

    /**
     * 缓存失效日期
     */
    @Column(name = "expire_date")
    @Schema(description = "缓存失效日期")
    private LocalDate expireDate;

    /**
     * 请求工序对象列表(如工单号、子工单号、容器号或SN号)
     */
    @NotNull
    @Schema(description = "请求工序对象列表(如工单号、子工单号、容器号或SN号)")
    @Type(JsonType.class)
    @Column(name = "serialNumber", nullable = false)
    private List<String> serialNumber;

    /**
     * 缓存具体内容
     */
    @NotNull
    @Schema(description = "缓存具体内容")
    @Column(name = "cache")
    private String cache;

    public RworkerCache(){

    }

    public RworkerCache(RworkerCacheSaveRequestDTO rworkerCacheSaveRequestDTO){
        this.uuid = rworkerCacheSaveRequestDTO.getUuid();
        this.cache = rworkerCacheSaveRequestDTO.getCache();
        this.category = rworkerCacheSaveRequestDTO.getCategory();
        this.startTime = LocalDateTime.now();
        this.serialNumber = rworkerCacheSaveRequestDTO.getSerialNumber();
    }
    public String getUuid() {
        return uuid;
    }

    public RworkerCache setUuid(String uuid) {
        this.uuid = uuid;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public RworkerCache setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public RworkerCache setStep(Step step) {
        this.step = step;
        return this;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public RworkerCache setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
        return this;
    }

    public LocalDate getExpireDate() {
        return expireDate;
    }

    public RworkerCache setExpireDate(LocalDate expireDate) {
        this.expireDate = expireDate;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public RworkerCache setCategory(int category) {
        this.category = category;
        return this;
    }

    public List<String> getSerialNumber() {
        return serialNumber;
    }

    public RworkerCache setSerialNumber(List<String> serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public String getCache() {
        return cache;
    }

    public RworkerCache setCache(String cache) {
        this.cache = cache;
        return this;
    }
}
