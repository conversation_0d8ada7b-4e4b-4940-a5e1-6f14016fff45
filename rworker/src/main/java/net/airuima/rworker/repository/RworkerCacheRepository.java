package net.airuima.rworker.repository;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.rworker.domain.RworkerCache;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.Optional;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * Rworker缓存Repository
 * <AUTHOR>
 * @date 2023/11/15
 */
@Repository
public interface RworkerCacheRepository extends LogicDeleteableRepository<RworkerCache>,
        EntityGraphJpaSpecificationExecutor<RworkerCache>, EntityGraphJpaRepository<RworkerCache, Long> {

    /**
     * 通过工位主键ID以及逻辑删除获取唯一缓存记录
     * @param workCellId 工位主键ID
     * @param deleted   逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.cache.RworkerCache> 缓存记录
     * <AUTHOR>
     * @date 2023/11/15
     */
    @DataFilter(isSkip = true)
    Optional<RworkerCache> findByWorkCellIdAndDeleted(Long workCellId,Long deleted);

    /**
     * 通过识别码以及逻辑删除获取唯一缓存记录
     * @param uuid 缓存识别码
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.cache.RworkerCache> 唯一缓存记录
     * <AUTHOR>
     * @date 2023/11/15
     */
    @DataFilter(isSkip = true)
    Optional<RworkerCache> findByUuidAndDeleted(String uuid,Long deleted);

    /**
     * 通过请求工序对象类型以及序列号获取唯一记录
     * @param category 请求工序对象类型(0:子工单号;1:工单号；2:容器号；3:SN号)
     * @param serialNumber 请求工序对象序列号
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.cache.RworkerCache> 唯一缓存记录
     */
    @DataFilter(isSkip = true)
    @EntityGraph("rworkerCacheEntityGraph")
    @Query(value = "SELECT rc FROM RworkerCache rc WHERE rc.category = :category and function('JSON_CONTAINS', rc.serialNumber, :serialNumber)=true  and rc.deleted=0")
    Optional<RworkerCache> findByCategoryAndSerialNumber(@Param("category") Integer category,@Param("serialNumber") String serialNumber);

    /**
     * 通过请求工序对象序列号以及工序主键ID删除缓存
     * @param serialNumber 请求工序对象序列号
     * @param category 请求工序对象类型(0:子工单号;1:工单号；2:容器号；3:SN号)
     * @param stepId 工序ID
     */
    @Modifying
    @Query(value = "DELETE FROM RworkerCache rc WHERE rc.step.id = :stepId and rc.category = :category and function('JSON_CONTAINS', rc.serialNumber, :serialNumber)=true")
    void deleteByCategoryAndSerialNumberAndStepId(@Param("category") Integer category,@Param("serialNumber") String serialNumber,@Param("stepId") Long stepId);

    /**
     * 通过工位ID删除缓存
     * @param workCellId 工位ID
     */
    @Modifying
    @Query("delete from RworkerCache where workCell.id = ?1")
    void deleteByWorkCellId(Long workCellId);

    /**
     * 删除过期的缓存
     * @param localDate 当前日期
     */
    @Modifying
    @Query("delete from RworkerCache rc where rc.expireDate<?1")
    void deleteExpiredCache(LocalDate localDate);
}
