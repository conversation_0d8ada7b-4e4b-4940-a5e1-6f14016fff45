package net.airuima.humiture.service.procedure;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.humiture.constant.HumitureEnum;
import net.airuima.humiture.domain.base.HumitureStandard;
import net.airuima.humiture.domain.procedure.HumitureCheckHistory;
import net.airuima.humiture.dto.RworkerHumitureSaveRequestDTO;
import net.airuima.humiture.repository.base.HumitureStandardRepository;
import net.airuima.humiture.repository.procedure.HumitureCheckHistoryRepository;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.scene.OrganizationArea;
import net.airuima.rbase.repository.base.scene.OrganizationAreaRepository;
import net.airuima.rbase.util.ToolUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 温湿度检测历史Service
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class HumitureCheckHistoryService extends CommonJpaService<HumitureCheckHistory> {

    private final String HUMITURE_CHECK_HISTORY_ENRITY_GRAPH = "humitureCheckHistoryEntityGraph";
    @Autowired
    private HumitureCheckHistoryRepository humitureCheckHistoryRepository;
    @Autowired
    private HumitureStandardRepository humitureStandardRepository;
    @Autowired
    private LatestHumitureCheckResultService latestHumitureCheckResultService;
    @Autowired
    private OrganizationAreaRepository organizationAreaRepository;

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<HumitureCheckHistory> find(Specification<HumitureCheckHistory> spec, Pageable pageable) {
        return humitureCheckHistoryRepository.findAll(spec, pageable, new NamedEntityGraph(HUMITURE_CHECK_HISTORY_ENRITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public List<HumitureCheckHistory> find(Specification<HumitureCheckHistory> spec) {
        return humitureCheckHistoryRepository.findAll(spec, new NamedEntityGraph(HUMITURE_CHECK_HISTORY_ENRITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<HumitureCheckHistory> findAll(Pageable pageable) {
        return humitureCheckHistoryRepository.findAll(pageable, new NamedEntityGraph(HUMITURE_CHECK_HISTORY_ENRITY_GRAPH));
    }

    /**
     * 新增温湿度检测历史
     *
     * @param entity 温湿度检测历史实体
     * <AUTHOR>
     * @date 2022-06-24
     **/
    public void create(HumitureCheckHistory entity) {
        //1. 获取温湿度检测标准
        Optional<HumitureStandard> humitureStandardOptional = humitureStandardRepository.findByAreaIdAndDeleted(entity.getArea().getId(), Constants.LONG_ZERO);
        if (humitureStandardOptional.isEmpty()) {
            throw new ResponseException("error.HumitureStandardNotExist", "温湿度检测标准不存在");
        }
        HumitureStandard humitureStandard = humitureStandardOptional.get();
        // 温度检测范围
        String temperatureRange = humitureStandard.getTemperatureRange();
        // 湿度检测范围
        String humidityRange = humitureStandard.getHumidityRange();
        // 温度停线范围
        List<String> temperatureStopLineRangeList = humitureStandard.getTemperatureStopLineRangeList();
        // 湿度停线范围
        List<String> humidityStopLineRangeList = humitureStandard.getHumidityStopLineRangeList();

        //2. 温湿度检测
        if (ToolUtils.compareInterval(temperatureRange, String.valueOf(entity.getTemperature())) && ToolUtils.compareInterval(humidityRange, String.valueOf(entity.getHumidity()))) {
            //合格
            entity.setResult(Constants.INT_ONE);
        } else {
            // 停线
            boolean temperatureStopLine = CollectionUtils.isEmpty(temperatureStopLineRangeList) ? Boolean.FALSE:temperatureStopLineRangeList.stream().anyMatch(temperatureStopLineRange ->
                    ToolUtils.compareInterval(temperatureStopLineRange, String.valueOf(entity.getTemperature())));
            boolean humidityStopLine = CollectionUtils.isEmpty(humidityStopLineRangeList) ? Boolean.FALSE:humidityStopLineRangeList.stream().anyMatch(humidityStopLineRange ->
                    ToolUtils.compareInterval(humidityStopLineRange, String.valueOf(entity.getHumidity())));

            if (temperatureStopLine || humidityStopLine) {
                entity.setResult(HumitureEnum.STOPPED.getCategory());
            } else {
                // 预警
                entity.setResult(HumitureEnum.WARNING.getCategory());
            }
        }
        entity.setRecordDate(LocalDateTime.now());
        //3. 检测结果更新
        humitureCheckHistoryRepository.save(entity);
        //4. 更新最新环境检测结果表
        latestHumitureCheckResultService.updateFinal(entity);
    }

    /**
     * 保存Rworker上传的温湿度数据
     * @param rworkerHumitureSaveRequestDTOList Rworker保存温湿度参数
     */
    public void custom(List<RworkerHumitureSaveRequestDTO> rworkerHumitureSaveRequestDTOList) {
        rworkerHumitureSaveRequestDTOList.forEach(humitureSaveRequestDTO -> {
            if (StringUtils.isBlank(humitureSaveRequestDTO.getAreaCode())) {
                return;
            }
            if (Objects.isNull(humitureSaveRequestDTO.getTemperature()) || Objects.isNull(humitureSaveRequestDTO.getHumidity())) {
                return;
            }
            OrganizationArea organizationArea = organizationAreaRepository.findByCodeAndDeleted(humitureSaveRequestDTO.getAreaCode(), Constants.LONG_ZERO).orElse(null);
            if (Objects.isNull(organizationArea)) {
                return;
            }
            HumitureStandard humitureStandard = humitureStandardRepository.findByAreaIdAndDeleted(organizationArea.getId(), Constants.LONG_ZERO).orElse(null);
            if (Objects.isNull(humitureStandard)) {
                return;
            }
            HumitureCheckHistory entity = new HumitureCheckHistory();
            entity.setArea(organizationArea).setHumidity(humitureSaveRequestDTO.getHumidity()).setTemperature(humitureSaveRequestDTO.getTemperature()).setRecordDate(LocalDateTime.now());
            this.create(entity);
        });
    }
}
