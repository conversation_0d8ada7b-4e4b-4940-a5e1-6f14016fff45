package net.airuima.humiture.service.procedure.api;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.humiture.domain.procedure.HumitureCheckHistory;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 温湿度检测历史扩展接口
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@FuncDefault
public interface IHumitureCheckHistoryService {

    /**
     * 温湿度检测历史新增接口
     * 
     * @param entity 实体信息
     */
    default void createHumitureCheckHistory(HumitureCheckHistory entity){
       
    }
}
