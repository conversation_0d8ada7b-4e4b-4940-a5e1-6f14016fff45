package net.airuima.humiture.web.rest.base;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.humiture.domain.base.HumitureStandard;
import net.airuima.humiture.service.base.HumitureStandardService;
import net.airuima.rbase.constant.Constants;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 温湿度检测标准Resource
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Tag(name = "温湿度检测标准Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/humiture-standards")
@AuthorityRegion("环境管控")
@FuncInterceptor("EnvHumiture")
public class HumitureStandardResource extends ProtectBaseResource<HumitureStandard> {

    private final HumitureStandardService humitureStandardService;

    public HumitureStandardResource(HumitureStandardService humitureStandardService) {
        this.humitureStandardService = humitureStandardService;
        this.mapUri = "/api/humiture-standards";
    }

    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览温湿度检测标准";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建温湿度检测标准";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改温湿度检测标准";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除温湿度检测标准";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_IMPORT)) {
            return "导入温湿度检测标准";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出温湿度检测标准";
        }
        return "";
    }

}
