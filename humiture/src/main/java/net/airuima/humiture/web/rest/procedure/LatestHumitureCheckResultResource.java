package net.airuima.humiture.web.rest.procedure;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.humiture.domain.procedure.LatestHumitureCheckResult;
import net.airuima.humiture.service.procedure.LatestHumitureCheckResultService;
import net.airuima.rbase.constant.Constants;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 最新温湿度检测结果Resource
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Tag(name = "最新温湿度检测结果Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/latest-humiture-check-results")
@AuthorityRegion("环境管控")
@FuncInterceptor("EnvCleanliness || EnvHumiture")
@AuthSkip("ICUD")
public class LatestHumitureCheckResultResource extends ProtectBaseResource<LatestHumitureCheckResult> {

    private final LatestHumitureCheckResultService latestHumitureCheckResultService;

    public LatestHumitureCheckResultResource(LatestHumitureCheckResultService latestHumitureCheckResultService) {
        this.latestHumitureCheckResultService = latestHumitureCheckResultService;
        this.mapUri = "/api/latest-humiture-check-results";
    }

    /**
     * Rworker验证工位区域温湿度合规性
     * @param workCellId 工位ID
     */
    @PreAuthorize("@sc.checkSecurity()")
    @PostMapping("/rworker/validate/{workCellId}")
    public ResponseEntity<ResponseData<Void>> validate(@PathVariable("workCellId") Long workCellId){
        try{
            latestHumitureCheckResultService.validate(workCellId);
        }catch (ResponseException e){
            return ResponseData.error(e.getErrorKey(),e.getErrorMessage());
        }
        return ResponseData.ok();
    }

    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览最新温湿度检测结果";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建最新温湿度检测结果";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改最新温湿度检测结果";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除最新温湿度检测结果";
        }
        return "";
    }

}
