package net.airuima.humiture.domain.base;

import io.hypersistence.utils.hibernate.type.json.JsonType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Table;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.scene.OrganizationArea;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.*;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 温湿度检测标准Domain
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Schema(name = "温湿度检测标准(HumitureStandard)", description = "温湿度检测标准")
@Entity
@Table(name = "base_humiture_standard", uniqueConstraints = {
        @UniqueConstraint(name = "base_humiture_standard_unique", columnNames = {"area_id", "deleted"})
})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "温湿度检测标准数据")
@NamedEntityGraph(name = "humitureStandardEntityGraph",attributeNodes = {@NamedAttributeNode("area")})
public class HumitureStandard extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门区域ID
     */
    @NotNull
    @ManyToOne
    @Schema(description = "部门区域ID")
    @JoinColumn(name = "area_id", nullable = false)
    private OrganizationArea area;

    /**
     * 温度检测范围
     */
    @NotNull
    @Schema(description = "温度检测范围", required = true)
    @Column(name = "temperature_range", nullable = false)
    private String temperatureRange;

    /**
     * 湿度检测范围
     */
    @NotNull
    @Schema(description = "湿度检测范围", required = true)
    @Column(name = "humidity_range", nullable = false)
    private String humidityRange;

    /**
     * 温度停线范围
     */
    @Schema(description = "温度停线范围")
    @Column(name = "temperature_stop_line_range")
    @Type(JsonType.class)
    private List<String> temperatureStopLineRangeList;

    /**
     * 湿度停线范围
     */
    @Schema(description = "湿度停线范围")
    @Column(name = "humidity_stop_line_range")
    @Type(JsonType.class)
    private List<String> humidityStopLineRangeList;

    /**
     * 温湿度周期数
     */
    @Schema(description = "温湿度周期数", required = true)
    @Column(name = "period_number", nullable = false)
    private int periodNumber;

    /**
     * 温湿度周期单位(0:小时，1:天，2:周，3:月，4:年)
     */
    @Schema(description = "温湿度周期单位(0:小时，1:天，2:周，3:月，4:年)", required = true)
    @Column(name = "period_unit", nullable = false)
    private int periodUnit;

    /**
     * 提醒数值
     */
    @Schema(description = "提醒周期")
    @Column(name = "warn_duration", nullable = false)
    private int warnDuration;

    /**
     * 提醒周期
     */
    @Schema(description = "提醒周期单位(0:小时，1:天，2:周，3:月，4:年)")
    @Column(name = "warn_unit", nullable = false)
    private int warnUnit;

    public String getTemperatureRange() {
        return temperatureRange;
    }

    public HumitureStandard setTemperatureRange(String temperatureRange) {
        this.temperatureRange = temperatureRange;
        return this;
    }

    public OrganizationArea getArea() {
        return area;
    }

    public HumitureStandard setArea(OrganizationArea area) {
        this.area = area;
        return this;
    }

    public String getHumidityRange() {
        return humidityRange;
    }

    public HumitureStandard setHumidityRange(String humidityRange) {
        this.humidityRange = humidityRange;
        return this;
    }

    public int getPeriodNumber() {
        return periodNumber;
    }

    public HumitureStandard setPeriodNumber(int periodNumber) {
        this.periodNumber = periodNumber;
        return this;
    }

    public int getPeriodUnit() {
        return periodUnit;
    }

    public HumitureStandard setPeriodUnit(int periodUnit) {
        this.periodUnit = periodUnit;
        return this;
    }

    public int getWarnDuration() {
        return warnDuration;
    }

    public HumitureStandard setWarnDuration(int warnDuration) {
        this.warnDuration = warnDuration;
        return this;
    }

    public int getWarnUnit() {
        return warnUnit;
    }

    public HumitureStandard setWarnUnit(int warnUnit) {
        this.warnUnit = warnUnit;
        return this;
    }

    public List<String> getTemperatureStopLineRangeList() {
        return temperatureStopLineRangeList;
    }

    public HumitureStandard setTemperatureStopLineRangeList(List<String> temperatureStopLineRangeList) {
        this.temperatureStopLineRangeList = temperatureStopLineRangeList;
        return this;
    }

    public List<String> getHumidityStopLineRangeList() {
        return humidityStopLineRangeList;
    }

    public HumitureStandard setHumidityStopLineRangeList(List<String> humidityStopLineRangeList) {
        this.humidityStopLineRangeList = humidityStopLineRangeList;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        HumitureStandard humitureStandard = (HumitureStandard) o;
        if (humitureStandard.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), humitureStandard.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
