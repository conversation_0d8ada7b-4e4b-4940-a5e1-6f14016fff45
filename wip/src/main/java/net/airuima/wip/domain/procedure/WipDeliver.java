package net.airuima.wip.domain.procedure;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.document.DocumentDTO;
import net.airuima.rbase.dto.organization.OrganizationDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.wip.domain.base.WipWarehouse;
import org.apache.commons.compress.utils.Lists;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边仓出库表（工序上料，自动扣料）Domain
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Schema(name = "线边仓出库表（工序上料，自动扣料）(WipDeliver)", description = "线边仓出库表（工序上料，自动扣料）")
@Entity
@Table(name = "procedure_wip_deliver")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@FetchEntity
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "wipDeliverEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "wipWarehouse",subgraph = "wipWarehouseEntityGraph"),
        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "wipWarehouseEntityGraph", attributeNodes = {
                        @NamedAttributeNode(value = "workLine")}),
                @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree")})})
public class WipDeliver extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 线边仓
     */
    @NotNull
    @Schema(description = "线边仓", required = true)
    @ManyToOne
    @JoinColumn(name = "warehouse_id", nullable = false)
    private WipWarehouse wipWarehouse;

    /**
     * 入库(原始)批次
     */
    @Schema(description = "入库(原始)批次")
    @Column(name = "origin_batch")
    private String originBatch;

    /**
     * 出库（领用）批次
     */
    @Schema(description = "出库（领用）批次")
    @Column(name = "deliver_batch")
    private String deliverBatch;


    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    @Column(name = "organization_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long organizationId;

    /**
     * 部门DTO
     */
    @Transient
    @Schema(description = "部门DTO")
    @FetchField(mapUri = "/api/organizations", serviceId = "mom", paramKey = "organizationId", tableName = "organization")
    private OrganizationDTO organizationDto = new OrganizationDTO();

    /**
     * 领用人id
     */
    @Schema(description = "领用人id")
    @Column(name = "receiver_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long receiverId;

    /**
     * 领用人DTO
     */
    @Schema(description = "领用人DTO")
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "receiverId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "receiver_id")
    @Transient
    private StaffDTO receiverDto = new StaffDTO();

    /**
     * 领用用途
     */
    @Schema(description = "领用用途")
    @Column(name = "purposes")
    private String purposes;


    /**
     * 物料id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "物料id")
    @Column(name = "material_id")
    private Long materialId;

    /**
     * 物料DTO
     */
    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "materialId")
    @Transient
    private MaterialDTO materialDto = new MaterialDTO();

    /**
     * 关联工单
     */
    @Schema(description = "关联工单")
    @ManyToOne
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;

    /**
     * 出库数量
     */
    @NotNull
    @Schema(description = "出库数量", required = true)
    @Column(name = "number", nullable = false)
    private BigDecimal number;

    /**
     * 出库人id
     */
    @Schema(description = "出库人id", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "operator_id", nullable = false)
    private Long operatorId;

    /**
     * 出库人DTO
     */
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "operatorId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "operator_id")
    @Transient
    private StaffDTO operatorDto = new StaffDTO();

    /**
     * 出库时间
     */
    @Schema(description = "出库时间", required = false)
    @Column(name = "record_date", nullable = true)
    private LocalDateTime recordDate;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    @Column(name = "note")
    private String note;

    /**
     * 退料数量
     */
    @Schema(description = "退料数量")
    @Column(name = "return_number")
    private BigDecimal returnNumber;

    /**
     * 线边出库关联文件信息
     */
    @Transient
    private List<DocumentDTO> documentDtoList = Lists.newArrayList();

    public String getDeliverBatch() {
        return deliverBatch;
    }

    public WipDeliver setDeliverBatch(String deliverBatch) {
        this.deliverBatch = deliverBatch;
        return this;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public WipDeliver setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
        return this;
    }

    public OrganizationDTO getOrganizationDto() {
        return organizationDto;
    }

    public WipDeliver setOrganizationDto(OrganizationDTO organizationDto) {
        this.organizationDto = organizationDto;
        return this;
    }

    public Long getReceiverId() {
        return receiverId;
    }

    public WipDeliver setReceiverId(Long receiverId) {
        this.receiverId = receiverId;
        return this;
    }

    public StaffDTO getReceiverDto() {
        return receiverDto;
    }

    public WipDeliver setReceiverDto(StaffDTO receiverDto) {
        this.receiverDto = receiverDto;
        return this;
    }

    public String getPurposes() {
        return purposes;
    }

    public WipDeliver setPurposes(String purposes) {
        this.purposes = purposes;
        return this;
    }

    public WipWarehouse getWipWarehouse() {
        return wipWarehouse;
    }

    public WipDeliver setWipWarehouse(WipWarehouse wipWarehouse) {
        this.wipWarehouse = wipWarehouse;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public WipDeliver setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public WipDeliver setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }


    public String getOriginBatch() {
        return originBatch;
    }

    public WipDeliver setOriginBatch(String originBatch) {
        this.originBatch = originBatch;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public WipDeliver setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public BigDecimal getNumber() {
        return number;
    }

    public WipDeliver setNumber(BigDecimal number) {
        this.number = number;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public WipDeliver setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public StaffDTO getOperatorDto() {
        return operatorDto;
    }

    public WipDeliver setOperatorDto(StaffDTO operatorDto) {
        this.operatorDto = operatorDto;
        return this;
    }

    public LocalDateTime getRecordDate() {
        return recordDate;
    }

    public WipDeliver setRecordDate(LocalDateTime recordDate) {
        this.recordDate = recordDate;
        return this;
    }

    public String getNote() {
        return note;
    }

    public WipDeliver setNote(String note) {
        this.note = note;
        return this;
    }

    public List<DocumentDTO> getDocumentDtoList() {
        return documentDtoList;
    }

    public WipDeliver setDocumentDtoList(List<DocumentDTO> documentDtoList) {
        this.documentDtoList = documentDtoList;
        return this;
    }

    public BigDecimal getReturnNumber() {
        return returnNumber;
    }

    public WipDeliver setReturnNumber(BigDecimal returnNumber) {
        this.returnNumber = returnNumber;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WipDeliver wipDeliver = (WipDeliver) o;
        if (wipDeliver.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), wipDeliver.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
