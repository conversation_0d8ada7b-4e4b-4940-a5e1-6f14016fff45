package net.airuima.wip.domain.procedure;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.wip.domain.base.WipWarehouse;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 台账Domain
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Schema(name = "台账(WipLedger)", description = "台账")
@Entity
@Table(name = "procedure_wip_ledger")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@FetchEntity
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "wipLedgerEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "wipWarehouse",subgraph = "wipWarehouseEntityGraph"),
        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "wipWarehouseEntityGraph", attributeNodes = {
                        @NamedAttributeNode(value = "workLine")}),
                @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree")})})
public class WipLedger extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 时间
     */
    @Schema(description = "时间")
    @Column(name = "record_date")
    private LocalDateTime recordDate;

    /**
     * 线边仓
     */
    @NotNull
    @Schema(description = "线边仓", required = true)
    @ManyToOne
    @JoinColumn(name = "warehouse_id", nullable = false)
    private WipWarehouse wipWarehouse;

    /**
     * 物料id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "物料id")
    @Column(name = "material_id")
    private Long materialId;

    /**
     * 物料DTO
     */
    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "materialId")
    @Transient
    private MaterialDTO materialDto = new MaterialDTO();

    /**
     * 操作人ID
     */
    @Schema(description = "操作人ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "operator_id", nullable = false)
    private Long operatorId;

    /**
     * 操作人DTO
     */
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "operatorId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "operator_id")
    @Transient
    private StaffDTO operatorDto = new StaffDTO();

    /**
     * 入库(原始)批次
     */
    @Schema(description = "入库(原始)批次")
    @Column(name = "origin_batch")
    private String originBatch;

    /**
     * 出库（领用）批次
     */
    @Schema(description = "出库（领用）批次")
    @Column(name = "deliver_batch")
    private String deliverBatch;

    /**
     * 类型（1：工单出库；2：更改库存；3：领料入库；4:退料入库；5：盘盈入库；6：盘亏出库；7：工单入库;8: 领料出库）
     */
    @Schema(description = "类型（1：工单出库；2：更改库存；3：领料入库；4:退料入库；5：盘盈入库；6：盘亏出库；7：工单入库;8: 领料出库）")
    @Column(name = "operation")
    private int operation;

    /**
     * 库存类型（1：线边库存；2：工单库存；）
     */
    @Schema(description = "库存类型（1：线边库存；2：工单库存；）")
    @Column(name = "category")
    private int category;

    /**
     * 关联工单
     */
    @Schema(description = "关联工单")
    @ManyToOne
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;

    /**
     * 数量
     */
    @Schema(description = "数量")
    @Column(name = "number")
    private BigDecimal number;

    /**
     * 原始库存（变更前）
     */
    @Schema(description = "原始库存（变更前）")
    @Column(name = "origin_inventory")
    private BigDecimal originInventory;

    /**
     * 当前库存（变更后）
     */
    @Schema(description = "当前库存（变更后）")
    @Column(name = "latest_inventory")
    private BigDecimal latestInventory;

    public LocalDateTime getRecordDate() {
        return recordDate;
    }

    public WipLedger setRecordDate(LocalDateTime recordDate) {
        this.recordDate = recordDate;
        return this;
    }

    public WipWarehouse getWipWarehouse() {
        return wipWarehouse;
    }

    public WipLedger setWipWarehouse(WipWarehouse wipWarehouse) {
        this.wipWarehouse = wipWarehouse;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public WipLedger setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public WipLedger setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public WipLedger setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public StaffDTO getOperatorDto() {
        return operatorDto;
    }

    public WipLedger setOperatorDto(StaffDTO operatorDto) {
        this.operatorDto = operatorDto;
        return this;
    }

    public String getOriginBatch() {
        return originBatch;
    }

    public WipLedger setOriginBatch(String originBatch) {
        this.originBatch = originBatch;
        return this;
    }

    public String getDeliverBatch() {
        return deliverBatch;
    }

    public WipLedger setDeliverBatch(String deliverBatch) {
        this.deliverBatch = deliverBatch;
        return this;
    }

    public int getOperation() {
        return operation;
    }

    public WipLedger setOperation(int operation) {
        this.operation = operation;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public WipLedger setCategory(int category) {
        this.category = category;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public WipLedger setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public BigDecimal getNumber() {
        return number;
    }

    public WipLedger setNumber(BigDecimal number) {
        this.number = number;
        return this;
    }

    public BigDecimal getOriginInventory() {
        return originInventory;
    }

    public WipLedger setOriginInventory(BigDecimal originInventory) {
        this.originInventory = originInventory;
        return this;
    }

    public BigDecimal getLatestInventory() {
        return latestInventory;
    }

    public WipLedger setLatestInventory(BigDecimal latestInventory) {
        this.latestInventory = latestInventory;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WipLedger wipLedger = (WipLedger) o;
        if (wipLedger.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), wipLedger.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
