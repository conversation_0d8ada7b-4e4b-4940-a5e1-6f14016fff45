package net.airuima.wip.domain.procedure;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.wip.domain.base.WipWarehouse;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边仓入库（领料记录）Domain
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Schema(name = "线边仓入库（领料记录）(WipStore)", description = "线边仓入库（领料记录）")
@Entity
@Table(name = "procedure_wip_store")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@FetchEntity
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "wipStoreEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "wipWarehouse",subgraph = "wipWarehouseEntityGraph")},
        subgraphs = {@NamedSubgraph(name = "wipWarehouseEntityGraph", attributeNodes = {
                @NamedAttributeNode(value = "workLine")})})
public class WipStore extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 线边仓
     */
    @NotNull
    @Schema(description = "线边仓", required = true)
    @ManyToOne
    @JoinColumn(name = "warehouse_id", nullable = false)
    private WipWarehouse wipWarehouse;

    /**
     * 批次
     */
    @Schema(description = "批次")
    @Column(name = "batch")
    private String batch;

    /**
     * 物料id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "物料id")
    @Column(name = "material_id")
    private Long materialId;

    /**
     * 物料DTO
     */
    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "materialId")
    @Transient
    private MaterialDTO materialDto = new MaterialDTO();

    /**
     * 入库总数
     */
    @NotNull
    @Schema(description = "入库总数", required = true)
    @Column(name = "number", nullable = false)
    private BigDecimal number;

    /**
     * 入库人id
     */
    @Schema(description = "入库人id")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "operator_id", nullable = false)
    private Long operatorId;

    /**
     * 入库人DTO
     */
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "operatorId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "operator_id")
    @Transient
    private StaffDTO operatorDto = new StaffDTO();

    /**
     * 入库时间
     */
    @Schema(description = "入库时间", required = false)
    @Column(name = "record_date", nullable = true)
    private LocalDateTime recordDate;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    @Column(name = "note")
    private String note;

//    /**
//     * 到货日期
//     */
//    @Schema(description = "到货日期")
//    @Column(name = "arrival_date")
//    private LocalDate arrivalDate;

    /**
     * 生产日期
     */
    @Schema(description = "生产日期")
    @Column(name = "produce_date")
    private LocalDate produceDate;

    /**
     * 保质期(天)
     */
    @Schema(description = "保质期(天)")
    @Column(name = "expire_day")
    private int expireDay;

//    /**
//     * 采购周期（天）
//     */
//    @Schema(description = " 采购周期（天）")
//    @Column(name = "purchasing_cycle")
//    private int purchasingCycle;


    public LocalDate getProduceDate() {
        return produceDate;
    }

    public WipStore setProduceDate(LocalDate produceDate) {
        this.produceDate = produceDate;
        return this;
    }

    public int getExpireDay() {
        return expireDay;
    }

    public WipStore setExpireDay(int expireDay) {
        this.expireDay = expireDay;
        return this;
    }


    public String getBatch() {
        return batch;
    }

    public WipStore setBatch(String batch) {
        this.batch = batch;
        return this;
    }

    public BigDecimal getNumber() {
        return number;
    }

    public WipStore setNumber(BigDecimal number) {
        this.number = number;
        return this;
    }


    public LocalDateTime getRecordDate() {
        return recordDate;
    }

    public WipStore setRecordDate(LocalDateTime recordDate) {
        this.recordDate = recordDate;
        return this;
    }

    public String getNote() {
        return note;
    }

    public WipStore setNote(String note) {
        this.note = note;
        return this;
    }

    public WipWarehouse getWipWarehouse() {
        return wipWarehouse;
    }

    public WipStore setWipWarehouse(WipWarehouse wipWarehouse) {
        this.wipWarehouse = wipWarehouse;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public WipStore setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public WipStore setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public WipStore setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public StaffDTO getOperatorDto() {
        return operatorDto;
    }

    public WipStore setOperatorDto(StaffDTO operatorDto) {
        this.operatorDto = operatorDto;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WipStore wipStore = (WipStore) o;
        if (wipStore.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), wipStore.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
