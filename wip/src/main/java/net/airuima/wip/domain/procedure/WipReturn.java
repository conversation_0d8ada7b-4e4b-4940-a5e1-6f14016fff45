package net.airuima.wip.domain.procedure;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.wip.domain.base.WipWarehouse;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边仓退料记录Domain
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Schema(name = "线边仓退料记录(WipReturn)", description = "线边仓退料记录")
@Entity
@Table(name = "procedure_wip_return")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@FetchEntity
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "wipReturnEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "wipWarehouse",subgraph = "wipWarehouseEntityGraph")},
        subgraphs = {@NamedSubgraph(name = "wipWarehouseEntityGraph", attributeNodes = {
                @NamedAttributeNode(value = "workLine")})})
public class WipReturn extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 线边仓
     */
    @NotNull
    @Schema(description = "线边仓", required = true)
    @ManyToOne
    @JoinColumn(name = "warehouse_id", nullable = false)
    private WipWarehouse wipWarehouse;

    /**
     * 入库(原始)批次
     */
    @Schema(description = "入库(原始)批次")
    @Column(name = "origin_batch")
    private String originBatch;

    /**
     * 出库（领用）批次
     */
    @Schema(description = "出库（领用）批次")
    @Column(name = "deliver_batch")
    private String deliverBatch;

    /**
     * 物料id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "物料id")
    @Column(name = "material_id")
    private Long materialId;

    /**
     * 物料DTO
     */
    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "materialId")
    @Transient
    private MaterialDTO materialDto = new MaterialDTO();

    /**
     * 退料总数
     */
    @NotNull
    @Schema(description = "退料总数", required = true)
    @Column(name = "number", nullable = false)
    private BigDecimal number;

    /**
     * 退料人id
     */
    @NotNull
    @Schema(description = "退料人id", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "operator_id", nullable = false)
    private Long operatorId;

    /**
     * 退料人DTO
     */
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "operatorId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "operator_id")
    @Transient
    private StaffDTO operatorDto = new StaffDTO();

    /**
     * 退料时间
     */
    @Schema(description = "退料时间", required = false)
    @Column(name = "record_date", nullable = true)
    private LocalDateTime recordDate;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    @Column(name = "note")
    private String note;

    public String getOriginBatch() {
        return originBatch;
    }

    public WipReturn setOriginBatch(String originBatch) {
        this.originBatch = originBatch;
        return this;
    }

    public String getDeliverBatch() {
        return deliverBatch;
    }

    public WipReturn setDeliverBatch(String deliverBatch) {
        this.deliverBatch = deliverBatch;
        return this;
    }

    public BigDecimal getNumber() {
        return number;
    }

    public WipReturn setNumber(BigDecimal number) {
        this.number = number;
        return this;
    }

    public LocalDateTime getRecordDate() {
        return recordDate;
    }

    public WipReturn setRecordDate(LocalDateTime recordDate) {
        this.recordDate = recordDate;
        return this;
    }

    public String getNote() {
        return note;
    }

    public WipReturn setNote(String note) {
        this.note = note;
        return this;
    }

    public WipWarehouse getWipWarehouse() {
        return wipWarehouse;
    }

    public WipReturn setWipWarehouse(WipWarehouse wipWarehouse) {
        this.wipWarehouse = wipWarehouse;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public WipReturn setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public WipReturn setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public WipReturn setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public StaffDTO getOperatorDto() {
        return operatorDto;
    }

    public WipReturn setOperatorDto(StaffDTO operatorDto) {
        this.operatorDto = operatorDto;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WipReturn wipReturn = (WipReturn) o;
        if (wipReturn.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), wipReturn.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
