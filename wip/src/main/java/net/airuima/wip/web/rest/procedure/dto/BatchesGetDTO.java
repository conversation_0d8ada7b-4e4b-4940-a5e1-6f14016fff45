package net.airuima.wip.web.rest.procedure.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 获取线边库存批次信息请求dto
 */
@Schema(description = "获取线边库存批次信息请求dto")
public class BatchesGetDTO {

    @Schema(description = "物料id")
    private Long materialId;

    @Schema(description = "仓位id")
    private Long wipWarehouseId;
    
    @Schema(description = "名称或者编码")
    private String text;

    @Schema(description = "数量")
    private Integer size;

    public Long getMaterialId() {
        return materialId;
    }

    public BatchesGetDTO setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public Long getWipWarehouseId() {
        return wipWarehouseId;
    }

    public BatchesGetDTO setWipWarehouseId(Long wipWarehouseId) {
        this.wipWarehouseId = wipWarehouseId;
        return this;
    }

    public String getText() {
        return text;
    }

    public BatchesGetDTO setText(String text) {
        this.text = text;
        return this;
    }

    public Integer getSize() {
        return size;
    }

    public BatchesGetDTO setSize(Integer size) {
        this.size = size;
        return this;
    }
}
