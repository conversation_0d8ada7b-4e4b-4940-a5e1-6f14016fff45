package net.airuima.wip.web.rest.procedure.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.wip.domain.procedure.WipInventory;

import java.io.Serializable;
import java.util.List;

/**
 * 线边库存扫描录入返回DTO
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Schema(description = "线边库存扫描录入返回DTO")
public class WipInventoryFifoResDTO implements Serializable {

    /**
     * 是否成功
     */
    @Schema(description = "是否成功")
    private Boolean success;

    /**
     * 提示信息
     */
    @Schema(description = "提示信息")
    private String message;

    /**
     * 线边库存信息
     */
    @Schema(description = "线边库存信息")
    private WipInventory wipInventory;

    /**
     * 推荐的线边库存信息
     */
    @Schema(description = "推荐的线边库存信息")
    private List<WipInventory> recommendWipInventoryList;

    public Boolean getSuccess() {
        return success;
    }

    public WipInventoryFifoResDTO setSuccess(Boolean success) {
        this.success = success;
        return this;
    }

    public WipInventory getWipInventory() {
        return wipInventory;
    }

    public WipInventoryFifoResDTO setWipInventory(WipInventory wipInventory) {
        this.wipInventory = wipInventory;
        return this;
    }

    public List<WipInventory> getRecommendWipInventoryList() {
        return recommendWipInventoryList;
    }

    public WipInventoryFifoResDTO setRecommendWipInventoryList(List<WipInventory> recommendWipInventoryList) {
        this.recommendWipInventoryList = recommendWipInventoryList;
        return this;
    }

    public String getMessage() {
        return message;
    }

    public WipInventoryFifoResDTO setMessage(String message) {
        this.message = message;
        return this;
    }
}
