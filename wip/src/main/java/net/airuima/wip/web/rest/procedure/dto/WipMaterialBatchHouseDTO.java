package net.airuima.wip.web.rest.procedure.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/4/10
 */
@Schema(description = "线边仓库存唯一性DTO")
public class WipMaterialBatchHouseDTO {

    /**
     * 物料ID
     */
    @Schema(description = "物料ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long materialId;
    /**
     * 线边仓ID
     */
    @Schema(description = "线边仓ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long warehouseId;
    /**
     * 批次
     */
    @Schema(description = "批次")
    private String batch;

    public Long getMaterialId() {
        return materialId;
    }

    public WipMaterialBatchHouseDTO setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public WipMaterialBatchHouseDTO setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
        return this;
    }

    public String getBatch() {
        return batch;
    }

    public WipMaterialBatchHouseDTO setBatch(String batch) {
        this.batch = batch;
        return this;
    }
}
