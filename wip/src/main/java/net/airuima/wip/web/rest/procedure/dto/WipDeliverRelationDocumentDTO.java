package net.airuima.wip.web.rest.procedure.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/2/1
 */
@Schema(description = "线边出库文件关联DTO")
public class WipDeliverRelationDocumentDTO {

    /**
     * 出库id
     */
    @Schema(description = "出库id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long wipDeliverId;

    /**
     * 关联文件列表
     */
    @Schema(description = "关联文件列表")
    private List<Long> documentIdList;

    public Long getWipDeliverId() {
        return wipDeliverId;
    }

    public WipDeliverRelationDocumentDTO setWipDeliverId(Long wipDeliverId) {
        this.wipDeliverId = wipDeliverId;
        return this;
    }

    public List<Long> getDocumentIdList() {
        return documentIdList;
    }

    public WipDeliverRelationDocumentDTO setDocumentIdList(List<Long> documentIdList) {
        this.documentIdList = documentIdList;
        return this;
    }
}
