package net.airuima.wip.web.rest.procedure;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.util.ResponseData;
import net.airuima.web.BaseResource;
import net.airuima.wip.domain.procedure.WipVerificateDetail;
import net.airuima.wip.service.procedure.WipVerificateDetailService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边库存盘点单历史明细Resource
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Tag(name = "线边库存盘点单历史明细Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/wip-verificate-details")
@AuthorityRegion("线边仓管理")
@FuncInterceptor("SideWarehouse")
public class WipVerificateDetailResource extends BaseResource<WipVerificateDetail> {

    private final WipVerificateDetailService wipVerificateDetailService;

    public WipVerificateDetailResource(WipVerificateDetailService wipVerificateDetailService) {
        this.wipVerificateDetailService = wipVerificateDetailService;
        this.mapUri = "/api/wip-verificate-details";
    }

    /**
     * @description 查看盘点单单明细
     * <AUTHOR>
     * @param verificateId 盘点单历史id
     * @return ResponseEntity<ResponseData<List<WipVerificateDetail>>>
     **/
    @Parameters({
           @Parameter(name = "verificateId", description = "盘点单id", required = true)
    })
    @Operation(summary = "查看盘点单单明细")
    @GetMapping("/queryVerificateDetail")
    public ResponseEntity<ResponseData<List<WipVerificateDetail>>> queryDetail(@RequestParam(value = "verificateId") Long verificateId) {
        try {
            List<WipVerificateDetail> result = wipVerificateDetailService.queryDetailByVerificateId(verificateId);
            return ResponseData.ok(result);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

}
