package net.airuima.wip.web.rest.scan;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.wip.service.scan.WipScanService;
import net.airuima.wip.web.rest.scan.dto.WipScanDataDTO;
import net.airuima.wip.web.rest.scan.dto.WipScanDataResDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 线边仓扫码核验
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Tag(name = "线边仓扫码核验Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/wip-scan")
public class WipScanResource {

    @Autowired
    private WipScanService wipScanService;

    /**
     * 扫码核验基础数据
     *
     * @param wipScanDataDto 扫码核验基础数据
     * @return 扫码核验基础数据
     * <AUTHOR>
     * @since 1.8.1
     */
    @PostMapping("/check-data")
    public ResponseEntity<ResponseData<WipScanDataResDTO>> scanCheckData(@RequestBody WipScanDataDTO wipScanDataDto){
        try {
            return ResponseData.ok(wipScanService.scanCheckData(wipScanDataDto));
        }catch (ResponseException e){
            return ResponseData.error(e);
        }
    }

}
