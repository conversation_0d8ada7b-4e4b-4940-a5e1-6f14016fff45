package net.airuima.wip.web.rest.base;

import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.web.rest.errors.BadRequestAlertException;
import net.airuima.wip.domain.base.WipWarehouse;
import net.airuima.wip.service.base.WipWarehouseService;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边仓Resource
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Tag(name = "线边仓Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/wip-warehouses")
@AuthorityRegion("线边仓管理")
@FuncInterceptor("SideWarehouse")
public class WipWarehouseResource extends ProtectBaseResource<WipWarehouse> {
    private static final String EXCEPTION = "exception";
    private final WipWarehouseService wipWarehouseService;

    public WipWarehouseResource(WipWarehouseService wipWarehouseService) {
        this.wipWarehouseService = wipWarehouseService;
        this.mapUri = "/api/wip-warehouses";
    }

    /**
     *  删除线边仓，同步删除线边库存
     * <AUTHOR>
     * @param id 线边仓id
     * @return org.springframework.http.ResponseEntity<java.lang.Void>
     **/
    @Operation(summary = "根据id删除单个线边仓")
    @Parameters({
            @Parameter(name = "id", description = "线边仓id", required = true)
    })
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Override
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        try {
            wipWarehouseService.deleteByWipWarehouseId(id);
            return ResponseEntity.ok().headers(HeaderUtil.deletedAlert(this.entityName, id.toString())).build();
        }catch (ResponseException responseException){
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, EXCEPTION, e.getMessage())).build();
        }
    }

    /**
     * 删除线边仓，同步删除线边库存
     * <AUTHOR>
     * @param deleteIds 线边仓ids
     * @return org.springframework.http.ResponseEntity<java.lang.Void>
     **/
    @Parameters({
            @Parameter(name = "deleteIds", description = "线边仓ids", required = true)
    })
    @Operation(summary = "根据id集合删除多个线边仓")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Override
    public ResponseEntity<Void> delMultiSel(@RequestBody List<Long> deleteIds) {
        try {
            wipWarehouseService.deleteByWipWarehouseIds(deleteIds);
            return ResponseEntity.ok().headers(HeaderUtil.deletedAlert(this.entityName, JSON.toJSONString(deleteIds))).build();
        } catch (ResponseException responseException){
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        }catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, EXCEPTION, e.getMessage())).build();
        }
    }

    /**
     *  禁用/启用线边仓
     * <AUTHOR>
     * @param id 线边仓id
     * @param isEnable 是否启用
     * @return org.springframework.http.ResponseEntity<java.lang.Void>
     **/
    @Operation(summary = "禁用/启用线边仓")
    @Parameters({
            @Parameter(name = "id", description = "线边仓id", required = true),
            @Parameter(name = "isEnable", description = "是否启用(0:否;1:是)", required = true)
    })
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PutMapping("enable")
    @PreventRepeatSubmit
    public ResponseEntity<ResponseData<Void>> updateEnable(@RequestParam Long id,@RequestParam Boolean isEnable) {
        try {
            wipWarehouseService.updateEnableById(id, isEnable);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     *  根据线边仓编码模糊查询线边仓记录
     * <AUTHOR>
     * @param code 线边仓编码
     * @param size 数量
     * @return List<WipWarehouse>
     **/
    @Parameters({
           @Parameter(name = "code", description = "线边仓编码", required = true),
           @Parameter(name = "size", description = "数量", required = true)
    })
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "根据线边仓编码查询线边仓")
    @GetMapping("/byCodeLike")
    public ResponseEntity<ResponseData<List<WipWarehouse>>> queryInventoryInfoLike(@RequestParam(value = "code") String code, @RequestParam(value = "size") Integer size){
        return ResponseData.ok(wipWarehouseService.findByCodeContaining(code,size));
    }

    /**
     * 根据线边仓Id查询线边仓
     * @param id  线边仓Id
     * <AUTHOR>
     * @date  2023/1/11
     * @return net.airuima.rbase.domain.base.wip.WipWarehouse
     */
    @Operation(summary = "根据线边仓Id查询线边仓")
    @GetMapping("/id")
    public ResponseEntity<ResponseData<WipWarehouse>> queryWipWarehouseId(@RequestParam(value = "id") Long id){
        return ResponseData.ok(wipWarehouseService.findByIdAndDeleted(id,Constants.LONG_ZERO));
    }

    /**
     * 线边仓编码获取线边仓信息
     * @param code 线边仓编码
     * <AUTHOR>
     * @date  2023/2/3
     * @return WipWarehouse
     */
    @Operation(summary = "根据线边仓Id查询线边仓")
    @GetMapping("/code")
    public ResponseEntity<ResponseData<WipWarehouse>> wipWarehouseByCode(@RequestParam(value = "code") String code){
         return ResponseData.ok(wipWarehouseService.findByCode(code));
    }

    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览线边仓";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建线边仓";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改线边仓";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除线边仓";
        }else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_IMPORT)) {
            return "导入线边仓";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出线边仓";
        }
        return "";
    }

}
