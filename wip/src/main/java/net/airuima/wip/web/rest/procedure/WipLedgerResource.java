package net.airuima.wip.web.rest.procedure;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.web.ProtectBaseResource;
import net.airuima.wip.domain.procedure.WipLedger;
import net.airuima.wip.service.procedure.WipLedgerService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 台账Resource
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Tag(name = "台账Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/wip-ledgers")
@AuthorityRegion("线边仓管理")
@FuncInterceptor("SideWarehouse")
@AuthSkip("IECUD")
public class WipLedgerResource extends ProtectBaseResource<WipLedger> {
    private static final String MODULE = "线边仓库存台账";
    private final WipLedgerService wipLedgerService;

    public WipLedgerResource(WipLedgerService wipLedgerService) {
        this.wipLedgerService = wipLedgerService;
        this.mapUri = "/api/wip-ledgers";
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

}
