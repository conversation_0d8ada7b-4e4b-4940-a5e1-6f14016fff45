package net.airuima.wip.constant;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * 线边台账操作类型枚举
 * <AUTHOR>
 * @date 2023/1/11
 */

public enum WipLedgerOperationEnum {
    /**
     *  工单出库
     */
    WIP_LEDGER_OPERATION_WORK_SHEET_DELEVER("WIP_LEDGER_OPERATION_WORK_SHEET_DELEVER",1,"工单出库"),
    /**
     * 更改库存
     */
    WIP_LEDGER_OPERATION_CHANGE("WIP_LEDGER_OPERATION_CHANGE",2,"更改库存"),
    /**
     * 领料入库
     */
    WIP_LEDGER_OPERATION_STORE("WIP_LEDGER_OPERATION_STORE",3,"领料入库"),
    /**
     * 退料入库
     */
    WIP_LEDGER_OPERATION_RETURN("WIP_LEDGER_OPERATION_RETURN",4,"退料入库"),
    /**
     * 盘盈入库
     */
    WIP_LEDGER_OPERATION_MORETHEN("WIP_LEDGER_OPERATION_MORETHEN",5,"盘盈入库"),
    /**
     * 盘亏出库
     */
    WIP_LEDGER_OPERATION_LESSTHEN("WIP_LEDGER_OPERATION_LESSTHEN",6,"盘亏出库"),
    /**
     * 工单入库
     */
    WIP_LEDGER_OPERATION_WORK_SHEET_STORE("WIP_LEDGER_OPERATION_WORK_SHEET_STORE",7,"工单入库"),
    /**
     * 领料出库
     */
    WIP_LEDGER_OPERATION_GLUE_STORE("WIP_LEDGER_OPERATION_GLUE_STORE",8,"领料出库");


    private String  wipLedgerOperation;

    private int category;

    private String remark;

    WipLedgerOperationEnum(String wipLedgerOperation, int category, String remark) {
        this.wipLedgerOperation = wipLedgerOperation;
        this.category = category;
        this.remark = remark;
    }

    public String getWipLedgerOperation() {
        return wipLedgerOperation;
    }

    public WipLedgerOperationEnum setWipLedgerOperation(String wipLedgerOperation) {
        this.wipLedgerOperation = wipLedgerOperation;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public WipLedgerOperationEnum setCategory(int category) {
        this.category = category;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public WipLedgerOperationEnum setRemark(String remark) {
        this.remark = remark;
        return this;
    }
}
