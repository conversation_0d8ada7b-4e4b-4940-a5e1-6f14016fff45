package net.airuima.wip.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边库存盘点历史Domain
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Schema(description = "盘点单DTO")
public class VerificateDTO {

    /**
     * id
     */
    @Schema(description = "主键id", required = true)
    private Long id;

    /**
     * 盘点单号
     */
    @Schema(description = "盘点单号", required = true)
    private String serialNumber;

    /**
     * 线边仓
     */
    @Schema(description = "线边仓id", required = true)
    private Long wipWarehouseId;

    /**
     * 类型（1：线边库存；2：工单库存）
     */
    @Schema(description = "类型（1：线边库存；2：工单库存）", required = true)
    private Integer category;

    /**
     * 关联工单id
     */
    @Schema(description = "工单id")
    private Long workSheetId;

    /**
     * 盘点人id
     */
    @Schema(description = "盘点人id")
    private Long operatorId;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    private String note;

    /**
     * 盘点明细
     */
    @Schema(description = "盘点明细")
    private List<VerificateDetailDTO> verificateDetailList;

    public Long getId() {
        return id;
    }

    public VerificateDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public VerificateDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public VerificateDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public Long getWipWarehouseId() {
        return wipWarehouseId;
    }

    public VerificateDTO setWipWarehouseId(Long wipWarehouseId) {
        this.wipWarehouseId = wipWarehouseId;
        return this;
    }

    public Long getWorkSheetId() {
        return workSheetId;
    }

    public VerificateDTO setWorkSheetId(Long workSheetId) {
        this.workSheetId = workSheetId;
        return this;
    }

    public String getNote() {
        return note;
    }

    public VerificateDTO setNote(String note) {
        this.note = note;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public VerificateDTO setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public List<VerificateDetailDTO> getVerificateDetailList() {
        return verificateDetailList;
    }

    public VerificateDTO setVerificateDetailList(List<VerificateDetailDTO> verificateDetailList) {
        this.verificateDetailList = verificateDetailList;
        return this;
    }

    @Schema(description = "线边库存盘点单历史明细")
    public static class VerificateDetailDTO{

        /**
         * id
         */
        @Schema(description = "主键id", required = true)
        private Long id;
        /**
         * 物料id
         */
        @Schema(description = "物料id", required = true)
        private Long materialId;

        /**
         * 批次
         */
        @Schema(description = "批次", required = true)
        private String batch;

        /**
         * 线边仓
         */
        @Schema(description = "线边仓id")
        private Long wipWarehouseId;

        /**
         * 实盘数量
         */
        @Schema(description = "实盘数量", required = true)
        private BigDecimal actualNumber;

        public Long getWipWarehouseId() {
            return wipWarehouseId;
        }

        public VerificateDetailDTO setWipWarehouseId(Long wipWarehouseId) {
            this.wipWarehouseId = wipWarehouseId;
            return this;
        }

        public Long getId() {
            return id;
        }

        public VerificateDetailDTO setId(Long id) {
            this.id = id;
            return this;
        }

        public String getBatch() {
            return batch;
        }

        public VerificateDetailDTO setBatch(String batch) {
            this.batch = batch;
            return this;
        }

        public BigDecimal getActualNumber() {
            return actualNumber;
        }

        public VerificateDetailDTO setActualNumber(BigDecimal actualNumber) {
            this.actualNumber = actualNumber;
            return this;
        }

        public Long getMaterialId() {
            return materialId;
        }

        public VerificateDetailDTO setMaterialId(Long materialId) {
            this.materialId = materialId;
            return this;
        }

    }

}
