package net.airuima.wip.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边库存Domain
 *
 * <AUTHOR>
 * @date 2022-09-08
 */
@Schema(description = "线边库存DTO")
public class InventoryDTO {

    /**
     * 主键id
     */
    @Schema(description = "主键id", required = true)
    private Long id;

    /**
     * 剩余数量
     */
    @NotNull
    @Schema(description = "剩余数量", required = true)
    private BigDecimal leftNumber;

    /**
     * 操作人id
     */
    @Schema(description = "操作人id", required = true)
    private Long operatorId;


    public Long getId() {
        return id;
    }

    public InventoryDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public InventoryDTO setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public BigDecimal getLeftNumber() {
        return leftNumber;
    }

    public InventoryDTO setLeftNumber(BigDecimal leftNumber) {
        this.leftNumber = leftNumber;
        return this;
    }

}
