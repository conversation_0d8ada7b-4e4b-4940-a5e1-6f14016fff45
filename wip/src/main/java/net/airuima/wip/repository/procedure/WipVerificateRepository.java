package net.airuima.wip.repository.procedure;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.wip.domain.procedure.WipVerificate;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边库存盘点历史Repository
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Repository
public interface WipVerificateRepository extends LogicDeleteableRepository<WipVerificate>,
        EntityGraphJpaSpecificationExecutor<WipVerificate>, EntityGraphJpaRepository<WipVerificate, Long> {

    /**
     * @description 根据线边仓查询
     * <AUTHOR>
     * @param wipWarehouseId 线边仓id
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.wip.WipVerificate> 线边仓列表
     **/
    @DataFilter(isSkip = true)
    List<WipVerificate> findByWipWarehouseIdAndDeleted(Long wipWarehouseId, Long deleted);

    /**
     * @description 根据线边仓id删除（逻辑删除）
     * <AUTHOR>
     * @param wipWarehouseId 线边仓id
     * @return void
     **/
    @Modifying
    @Query(value = "update WipVerificate vf set vf.deleted = vf.id where vf.wipWarehouse.id=?1")
    void deleteByWipWarehouseId(Long wipWarehouseId);
}
