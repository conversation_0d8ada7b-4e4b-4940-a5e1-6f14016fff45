package net.airuima.wip.repository.procedure;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.wip.domain.procedure.WipReturn;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边仓退料记录Repository
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Repository
public interface WipReturnRepository extends LogicDeleteableRepository<WipReturn>,
        EntityGraphJpaSpecificationExecutor<WipReturn>, EntityGraphJpaRepository<WipReturn, Long> {

    /**
     * @description 根据线边仓主键id删除（逻辑删除）
     * <AUTHOR>
     * @param wipWarehouseId 线边仓主键id
     * @return void
     **/
    @Modifying
    @Query(value = "update WipReturn wr set wr.deleted = wr.id where wr.wipWarehouse.id=?1")
    void deleteByWipWarehouseId(Long wipWarehouseId);

    /**
     * 通过 入库批次，领料（出库）批次，线边仓，物料编码获取同一批次的退料记录
     * @param deliverBatch 领料批次
     * @param originBatch 入库批次
     * @param materialId 物料主键id
     * @param wipWarehouseId 线边仓主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2023/3/7
     * @return java.util.List<net.airuima.rbase.domain.procedure.wip.WipReturn> 线边仓退料记录列表
     */
    @DataFilter(isSkip = true)
    List<WipReturn> findByDeliverBatchAndOriginBatchAndMaterialIdAndWipWarehouseIdAndDeleted(String deliverBatch,String originBatch,Long materialId,Long wipWarehouseId,Long deleted);
}
