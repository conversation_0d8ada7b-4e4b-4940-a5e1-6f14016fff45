package net.airuima.wip.repository.procedure;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.wip.domain.procedure.WipInventory;
import net.airuima.wip.web.rest.procedure.dto.BatchesGetDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边库存Repository
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Repository
public interface WipInventoryRepository extends LogicDeleteableRepository<WipInventory>,
        EntityGraphJpaSpecificationExecutor<WipInventory>, EntityGraphJpaRepository<WipInventory, Long> {

    /**
     * @description 根据线边仓主键id删除（逻辑删除）
     * <AUTHOR>
     * @param wipWarehouseId 线边仓主键id
     * @return void
     **/
    @Modifying
    @Query(value = "update WipInventory it set it.deleted = it.id where it.wipWarehouse.id=?1")
    void deleteByWipWarehouseId(Long wipWarehouseId);

    /**
     * @description 根据线边仓主键id、物料主键id、批次查询线边库存记录
     * <AUTHOR>
     * @param wipWarehouseId 线边仓主键id
     * @param materialId 物料主键id
     * @param batch 批次
     * @return net.airuima.rbase.domain.procedure.wip.WipInventory  线边库存
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    WipInventory findByWipWarehouseIdAndMaterialIdAndBatchAndDeleted(Long wipWarehouseId, Long materialId, String batch, Long deleted);

    /**
     * @description 根据主键主键id查询线边库存
     * <AUTHOR>
     * @param id 主键id
     * @param deleted 删除标志
     * @return net.airuima.rbase.domain.procedure.wip.WipInventory  线边库存
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    WipInventory findByIdAndDeleted(Long id, Long deleted);

    /**
     * @description 根据线边仓主键id、物料主键id、模糊批次查询线边库存记录
     * <AUTHOR>
     * @param wipWarehouseId 线边仓主键id
     * @param materialId 物料主键id
     * @param batch 批次（模糊匹配）
     * @param deleted 删除标志
     * @param pageable 分页
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.procedure.wip.WipInventory>  线边库存分页
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select wi from WipInventory wi where wi.wipWarehouse.id=?1 and wi.materialId=?2 and wi.batch like concat('%',?3,'%') and wi.deleted=?4")
    Page<WipInventory> findByWipWarehouseIdAndMaterialIdAndBatchContainingAndDeleted(Long wipWarehouseId, Long materialId, String batch, Long deleted, Pageable pageable);

    /**
     * @description 根据线边仓id查询线边库存记录
     * <AUTHOR>
     * @param wipWarehouseId 线边仓主键id
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.procedure.wip.WipInventory>  线边库存分页
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    Page<WipInventory> findByWipWarehouseIdAndDeleted(Long wipWarehouseId, Long deleted, Pageable pageable);

    /**
     * 获取未过期的线边库存
     * @param expired 是否过期
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2023/1/9
     * @return java.util.List<net.airuima.rbase.domain.procedure.wip.WipInventory> 线边库存列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<WipInventory> findByExpiredAndDeletedAndExpirationDateIsNotNull(Boolean expired,Long deleted);

    /**
     * 通过线边仓回去剩余物料大于0 的线边库存信息
     * @param wipWarehouseId
     * @param leftNumber
     * @param deleted
     * <AUTHOR>
     * @date  2023/1/29
     * @return java.util.List<net.airuima.rbase.domain.procedure.wip.WipInventory> 线边库存列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<WipInventory> findByWipWarehouseIdAndLeftNumberGreaterThanAndDeleted(Long wipWarehouseId, BigDecimal leftNumber,Long deleted);

    /**
     * 通过线边仓id、物料id、剩余数量大于0、是否过期、逻辑删除 获取线边库存信息
     * @param wipWarehouseId 线边仓主键id
     * @param materialId 物料主键id
     * @param leftNumber 剩余数量
     * @param nowDate 当前时间
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @return java.util.List<net.airuima.rbase.domain.procedure.wip.WipInventory> 线边库存列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<WipInventory> findByWipWarehouseIdAndMaterialIdAndLeftNumberGreaterThanAndExpirationDateAfterAndDeleted
    (Long wipWarehouseId, Long materialId, BigDecimal leftNumber, LocalDate nowDate, Long deleted);

    /**
     * 通过物料主键id列表 获取线边库存列表
     *
     * @param materialIds 物料主键id列表
     * <AUTHOR>
     * @date  2023/03/20
     * @return java.util.List<net.airuima.rbase.domain.procedure.wip.WipInventory> 线边库存列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query(value = "select w from WipInventory w where w.deleted = 0l and w.materialId in ?1 group by w.materialId, w.wipWarehouse.id")
    List<WipInventory> findByMaterialIdGroupMaterialWipWarehouse(List<Long> materialIds);

    /**
     * 通过物料主键id、仓位主键id、批次名称或者编码 获取线边仓库存批次列表
     *
     * @param dto 获取线边库存批次信息请求dto
     * @param pageable 分页对象
     * <AUTHOR>
     * @date  2023/03/20
     * @return org.springframework.data.domain.Page<String> 获取线边仓库存批次列表
     */
    @DataFilter(isSkip = true)
    @Query(value = "select w.batch from WipInventory w where w.deleted = 0l" +
            " and w.materialId = :#{#dto.materialId}" +
            " and w.wipWarehouse.id = :#{#dto.wipWarehouseId}" +
            " and w.batch like concat('%',:#{#dto.text},'%')" )
    Page<String> getBatches(BatchesGetDTO dto, Pageable pageable);

    /**
     * 根据物料主键id 获取线边仓
     * @param materialId 物料主键id
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.wip.WipInventory> 线边库存列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select wi from WipInventory wi where wi.materialId=?1 and wi.deleted=?2 group by wi.wipWarehouse.id")
    List<WipInventory> findByMaterialIdAndDeletedGroupByWipWarehouseId(Long materialId ,Long deleted);
}
