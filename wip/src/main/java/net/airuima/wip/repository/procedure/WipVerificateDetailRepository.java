package net.airuima.wip.repository.procedure;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.wip.domain.procedure.WipVerificateDetail;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边库存盘点单历史明细Repository
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Repository
public interface WipVerificateDetailRepository extends LogicDeleteableRepository<WipVerificateDetail>,
        EntityGraphJpaSpecificationExecutor<WipVerificateDetail>, EntityGraphJpaRepository<WipVerificateDetail, Long> {

    /**
     * @description 根据线边仓主键id删除（逻辑删除）
     * <AUTHOR>
     * @param wipVerificateId 盘点单主键id
     * @return void
     **/
    @Modifying
    @Query(value = "update WipVerificateDetail wvf set wvf.deleted = wvf.id where wvf.wipVerificate.id=?1")
    void deleteByVerificateId(Long wipVerificateId);

    /**
     * @description 查看盘点单单明细
     * <AUTHOR>
     * @param verificateId 盘点单历史主键id
     * @param deleted 逻辑删除标志
     * @return java.util.List<net.airuima.rbase.domain.procedure.wip.WipVerificateDetail> 盘点单单明细列表
     **/
    @DataFilter(isSkip = true)
    List<WipVerificateDetail> findByWipVerificateIdAndDeleted(Long verificateId, Long deleted);
}
