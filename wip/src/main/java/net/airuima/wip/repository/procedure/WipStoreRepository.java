package net.airuima.wip.repository.procedure;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.wip.domain.procedure.WipStore;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边仓入库（领料记录）Repository
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Repository
public interface WipStoreRepository extends LogicDeleteableRepository<WipStore>,
        EntityGraphJpaSpecificationExecutor<WipStore>, EntityGraphJpaRepository<WipStore, Long> {

    /**
     * 根据线边仓主键id删除（逻辑删除）
     * <AUTHOR>
     * @param wipWarehouseId 线边仓主键id
     **/
    @Modifying
    @Query(value = "update WipStore ws set ws.deleted = ws.id where ws.wipWarehouse.id=?1")
    void deleteByWipWarehouseId(Long wipWarehouseId);
}
