package net.airuima.wip.repository.procedure;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.wip.domain.procedure.WipLedger;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 台账Repository
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Repository
public interface WipLedgerRepository extends LogicDeleteableRepository<WipLedger>,
        EntityGraphJpaSpecificationExecutor<WipLedger>, EntityGraphJpaRepository<WipLedger, Long> {

    /**
     * @description 根据线边仓主键id删除（逻辑删除）
     * <AUTHOR>
     * @param wipWarehouseId 线边仓主键id
     * @return void
     **/
    @Modifying
    @Query(value = "update WipLedger wl set wl.deleted = wl.id where wl.wipWarehouse.id=?1")
    void deleteByWipWarehouseId(Long wipWarehouseId);


    /**
     * 根据线边仓主键id，物料主键id，原始（入库）批次，获取前n天的台账数据
     * @param beforeDateTime 前n天
     * @param WipWarehouseId 线边仓主键id
     * @param materialId 物料主键id
     * @param originBatch 原始批次
     * @param category 库存类型
     * <AUTHOR>
     * @date  2023/1/9
     * @return java.util.List<net.airuima.rbase.domain.procedure.wip.WipLedger> 线边仓台账列表
     */
    @DataFilter(isSkip = true)
    @Query(value = "select wl from WipLedger wl where wl.recordDate > ?1 and wl.wipWarehouse.id = ?2 and wl.materialId = ?3 and wl.originBatch = ?4 and wl.category = ?5 and wl.deleted = 0")
    List<WipLedger> findByWipWarehouseIdAndMaterialIdAndOriginBatchAndCategoryAndBeforeDay(LocalDateTime beforeDateTime, Long WipWarehouseId, Long materialId, String originBatch, Integer category);
}
