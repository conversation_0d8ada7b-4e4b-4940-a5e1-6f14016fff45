package net.airuima.wip.service.procedure.impl;

import net.airuima.rbase.constant.WipLedgerCategoryEnum;
import net.airuima.rbase.constant.WipLedgerOperationEnum;
import net.airuima.rbase.util.MapperUtils;
import net.airuima.wip.domain.procedure.WipDeliver;
import net.airuima.wip.dto.DeliverDTO;
import net.airuima.wip.dto.GlueDeliverDTO;
import net.airuima.wip.repository.procedure.WipDeliverRepository;
import net.airuima.wip.service.procedure.WipDeliverService;
import net.airuima.wip.service.procedure.WipInventoryService;
import net.airuima.wip.service.procedure.api.IWipDeliverService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/10/27
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class WipDeliverServiceImpl implements IWipDeliverService {
    @Autowired
    private WipDeliverService wipDeliverService;
    @Autowired
    private WipInventoryService wipInventoryService;
    @Autowired
    private WipDeliverRepository wipDeliverRepository;

    /**
     * 新增胶水线边出库
     *
     * @param glueDeliverDto 胶水线边出库Dto
     * @return net.airuima.rbase.domain.procedure.wip.WipDeliver  线边仓出库信息
     * <AUTHOR>
     * @date 2023/1/11
     */
    @Override
    public WipDeliver createGlueDeliverInfo(GlueDeliverDTO glueDeliverDto) {
        wipInventoryService.validWipWarehouse(glueDeliverDto.getWipWarehouseId());
        // 新增胶水线边出库记录
        DeliverDTO.MaterialDetailDTO materialDetail = glueDeliverDto.getMaterialDetailDto();
        if (null != materialDetail) {
            WipDeliver deliver = wipDeliverService.getDeliver(MapperUtils.map(glueDeliverDto, DeliverDTO.class), null);
            deliver.setOriginBatch(materialDetail.getOriginBatch())
                    .setDeliverBatch(materialDetail.getDeliverBatch())
                    .setMaterialId(materialDetail.getMaterialId())
                    .setNumber(materialDetail.getNumber());
            wipDeliverRepository.save(deliver);
            // 新增线边台账记录，减去线边库存数量
            wipInventoryService.generateLedgerAndUpdateInventory(wipDeliverService.getLedgerDTO(deliver, deliver.getNumber().negate(), WipLedgerOperationEnum.WIP_LEDGER_OPERATION_GLUE_STORE.getCategory(), WipLedgerCategoryEnum.WIP_LEDGER_CATEGORY_INVENTORY.getCategory()));
            return deliver;
        }
        return null;
    }
}
