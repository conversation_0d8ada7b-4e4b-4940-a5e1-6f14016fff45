package net.airuima.wip.service.scan;

import net.airuima.constant.Constants;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.proxy.bom.RbaseMaterialProxy;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.util.ResponseException;
import net.airuima.wip.domain.base.WipWarehouse;
import net.airuima.wip.repository.base.WipWarehouseRepository;
import net.airuima.wip.web.rest.scan.dto.WipScanDataDTO;
import net.airuima.wip.web.rest.scan.dto.WipScanDataResDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * 线边仓扫码核验Service
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WipScanService {

    @Autowired
    private WipWarehouseRepository wipWarehouseRepository;
    @Autowired
    private RbaseMaterialProxy rbaseMaterialProxy;


    /**
     * 扫码核验基础数据
     *
     * @param wipScanDataDto 扫码核验基础数据
     * @return 扫码核验基础数据
     * <AUTHOR>
     * @since 1.8.1
     */
    public WipScanDataResDTO scanCheckData(WipScanDataDTO wipScanDataDto) {
        WipScanDataResDTO wipScanDataResDto = new WipScanDataResDTO();
        if (ValidateUtils.isValid(wipScanDataDto.getWipWarehouseCode())){
            WipWarehouse wipWarehouse = wipWarehouseRepository.findByCodeAndDeleted(wipScanDataDto.getWipWarehouseCode(), Constants.LONG_ZERO);
            if (Objects.isNull(wipWarehouse)){
                throw new ResponseException("wipWarehouseNotFind","线边仓不存在");
            }
            wipScanDataResDto.setWipWarehouse(wipWarehouse);
        }

        if (ValidateUtils.isValid(wipScanDataDto.getMaterialCode())){
            MaterialDTO materialDto = rbaseMaterialProxy.findByCodeAndDeleted(wipScanDataDto.getMaterialCode(), Constants.LONG_ZERO)
                    .orElseThrow(() -> new ResponseException("materialNotFind","物料不存在"));
            wipScanDataResDto.setMaterialDto(materialDto);
        }
        return wipScanDataResDto;
    }
}
