package net.airuima.wip.service.base;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import net.airuima.wip.domain.base.WipWarehouse;
import net.airuima.wip.domain.procedure.WipVerificate;
import net.airuima.wip.repository.base.WipWarehouseRepository;
import net.airuima.wip.repository.procedure.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边仓Service
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WipWarehouseService extends CommonJpaService<WipWarehouse> {

    private final String wipWarehouseEntityGraph = "wipWarehouseEntityGraph";
    private final WipWarehouseRepository wipWarehouseRepository;
    private final WipInventoryRepository wipInventoryRepository;
    private final WipStoreRepository wipStoreRepository;
    private final WipDeliverRepository wipDeliverRepository;
    private final WipLedgerRepository wipLedgerRepository;
    private final WipReturnRepository wipReturnRepository;
    private final WipVerificateRepository wipVerificateRepository;
    private final WipVerificateDetailRepository wipVerificateDetailRepository;

    public WipWarehouseService(WipWarehouseRepository wipWarehouseRepository, WipInventoryRepository wipInventoryRepository, WipStoreRepository wipStoreRepository, WipDeliverRepository wipDeliverRepository,
                               WipLedgerRepository wipLedgerRepository, WipReturnRepository wipReturnRepository, WipVerificateRepository wipVerificateRepository, WipVerificateDetailRepository wipVerificateDetailRepository) {
        this.wipWarehouseRepository = wipWarehouseRepository;
        this.wipInventoryRepository = wipInventoryRepository;
        this.wipStoreRepository = wipStoreRepository;
        this.wipDeliverRepository = wipDeliverRepository;
        this.wipLedgerRepository = wipLedgerRepository;
        this.wipReturnRepository = wipReturnRepository;
        this.wipVerificateRepository = wipVerificateRepository;
        this.wipVerificateDetailRepository = wipVerificateDetailRepository;

    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WipWarehouse> find(Specification<WipWarehouse> spec, Pageable pageable) {
        return wipWarehouseRepository.findAll(spec, pageable, new NamedEntityGraph(wipWarehouseEntityGraph));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<WipWarehouse> find(Specification<WipWarehouse> spec) {
        return wipWarehouseRepository.findAll(spec, new NamedEntityGraph(wipWarehouseEntityGraph));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WipWarehouse> findAll(Pageable pageable) {
        return wipWarehouseRepository.findAll(pageable, new NamedEntityGraph(wipWarehouseEntityGraph));
    }

    /**
     * @param wipWarehouseId 线边仓id
     * @return
     * @description 通过线边仓id删除线边库存，以及相关的出入库、台账、盘点
     * <AUTHOR>
     **/
    public void deleteByWipWarehouseId(Long wipWarehouseId) {
        wipInventoryRepository.deleteByWipWarehouseId(wipWarehouseId);
        wipWarehouseRepository.logicDelete(wipWarehouseId);
        wipStoreRepository.deleteByWipWarehouseId(wipWarehouseId);
        wipDeliverRepository.deleteByWipWarehouseId(wipWarehouseId);
        wipLedgerRepository.deleteByWipWarehouseId(wipWarehouseId);
        wipReturnRepository.deleteByWipWarehouseId(wipWarehouseId);
        List<WipVerificate> verificateList = wipVerificateRepository.findByWipWarehouseIdAndDeleted(wipWarehouseId, Constants.LONG_ZERO);
        verificateList.forEach(e -> wipVerificateDetailRepository.deleteByVerificateId(e.getId()));
        wipVerificateRepository.deleteByWipWarehouseId(wipWarehouseId);
    }

    /**
     * @param wipWarehouseIds 线边仓ids
     * @return org.springframework.http.ResponseEntity<java.lang.Void>
     * @description 通过线边仓ids循环删除线边库存
     * <AUTHOR>
     **/
    public void deleteByWipWarehouseIds(List<Long> wipWarehouseIds) {
        wipWarehouseIds.forEach(this::deleteByWipWarehouseId);
    }

    /**
     * 禁用、启用线边仓
     *
     * @param isEnable 是否启用(0:否;1:是)
     * @param id       主键id
     */
    public void updateEnableById(Long id, Boolean isEnable) {
        wipWarehouseRepository.updateEnableById(id, isEnable);
    }

    /**
     * @param code 线边仓编码
     * @param size 数量
     * @return List<WipWarehouse>
     * @description 根据线边仓编码模糊查询线边仓记录
     * <AUTHOR>
     **/
    public List<WipWarehouse> findByCodeContaining(String code, Integer size) {
        return Optional.ofNullable(wipWarehouseRepository.findByNameOrCode(code, true, Constants.LONG_ZERO, PageRequest.of(Constants.INT_ZERO, size))).map(Slice::getContent).orElse(null);
    }

    /**
     * @param workLineId 产线id
     * @return WipWarehouse
     * @description 根据产线id查询线边仓记录
     * <AUTHOR>
     **/
    public WipWarehouse findByWorkLineId(Long workLineId) {
        return wipWarehouseRepository.findByWorkLineIdAndIsEnableAndDeleted(workLineId, true, Constants.LONG_ZERO);
    }

    /**
     * 线边仓编码获取线边仓信息
     *
     * @param code 线边仓编码
     * @return WipWarehouse
     * <AUTHOR>
     * @date 2023/2/3
     */
    public WipWarehouse findByCode(String code) {
        return wipWarehouseRepository.findByCodeAndDeleted(code, Constants.LONG_ZERO);
    }

    /**
     * 新增或者更新线边仓
     * @param entity      待保存实例
     * @return net.airuima.rbase.domain.base.wip.WipWarehouse 线边仓
     * <AUTHOR>
     * @date 2023/10/10
     */
    @Override
    public WipWarehouse save(WipWarehouse entity) {
        WipWarehouse wipWarehouse = wipWarehouseRepository.findByCodeAndDeleted(entity.getCode(), Constants.LONG_ZERO);
        if((null == entity.getId() && null!=wipWarehouse)||(null!=entity.getId() && null!= wipWarehouse && !entity.getId().equals(wipWarehouse.getId()))){
            throw new ResponseException("error.wipWarehouseExist", "线边仓记录已存在");
        }
        return super.save(entity);
    }
}
