package net.airuima.wip.service.procedure;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.dto.StaffDTO;
import net.airuima.dto.UserDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.WipLedgerCategoryEnum;
import net.airuima.rbase.constant.WipLedgerOperationEnum;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.material.WsMaterialBatch;
import net.airuima.rbase.dto.document.DocumentDTO;
import net.airuima.rbase.dto.document.DocumentRelationDTO;
import net.airuima.rbase.proxy.document.RbaseDocumentProxy;
import net.airuima.rbase.proxy.organization.RbaseRbacProxy;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.material.WsMaterialBatchRepository;
import net.airuima.rbase.service.procedure.material.WsCheckMaterialDetailService;
import net.airuima.rbase.util.MapperUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import net.airuima.util.SecurityUtils;
import net.airuima.wip.domain.base.WipWarehouse;
import net.airuima.wip.domain.procedure.WipDeliver;
import net.airuima.wip.dto.DeliverDTO;
import net.airuima.wip.dto.LedgerDTO;
import net.airuima.wip.repository.procedure.WipDeliverRepository;
import net.airuima.wip.repository.procedure.WipInventoryRepository;
import net.airuima.wip.repository.procedure.WipLedgerRepository;
import net.airuima.wip.service.procedure.impl.WipLedgerServiceImpl;
import net.airuima.wip.web.rest.procedure.dto.WipDeliverRelationDocumentDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边仓出库表（工序上料，自动扣料）Service
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WipDeliverService extends CommonJpaService<WipDeliver> {
    private static final String WIP_DELIVER_ENTITY_GRAPH = "wipDeliverEntityGraph";
    private final WipDeliverRepository wipDeliverRepository;
    private final WipInventoryService wipInventoryService;
    private final WorkSheetRepository workSheetRepository;
    private final WsMaterialBatchRepository wsMaterialBatchRepository;
    private final WsCheckMaterialDetailService wsCheckMaterialDetailService;
    @Autowired
    private RbaseDocumentProxy rbaseDocumentProxy;
    @Autowired
    private RbaseRbacProxy rbaseRbacProxy;

    @Autowired
    private WipInventoryRepository wipInventoryRepository;
    @Autowired
    private WipLedgerRepository wipLedgerRepository;
    @Autowired
    private WipLedgerServiceImpl wipLedgerService;

    @Value("${spring.application.name}")
    private String applicationName;


    public WipDeliverService(WipDeliverRepository wipDeliverRepository, WipInventoryService wipInventoryService, WorkSheetRepository workSheetRepository, WsMaterialBatchRepository wsMaterialBatchRepository, WsCheckMaterialDetailService wsCheckMaterialDetailService) {
        this.wipDeliverRepository = wipDeliverRepository;
        this.wipInventoryService = wipInventoryService;
        this.workSheetRepository = workSheetRepository;
        this.wsMaterialBatchRepository = wsMaterialBatchRepository;
        this.wsCheckMaterialDetailService = wsCheckMaterialDetailService;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WipDeliver> find(Specification<WipDeliver> spec, Pageable pageable) {
        Page<WipDeliver> wipDeliverPage = wipDeliverRepository.findAll(spec, pageable, new NamedEntityGraph(WIP_DELIVER_ENTITY_GRAPH));
        return wipDeliverPageRelationDocument(wipDeliverPage, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<WipDeliver> find(Specification<WipDeliver> spec) {
        List<WipDeliver> wipDelivers = wipDeliverRepository.findAll(spec, new NamedEntityGraph(WIP_DELIVER_ENTITY_GRAPH));
        if (!ValidateUtils.isValid(wipDelivers)) {
            return wipDelivers;
        }
        return wipDelivers.stream().map(wipDeliver -> {
            List<DocumentDTO> documentDtoList = rbaseDocumentProxy.getByRecordId(wipDeliver.getId());
            return wipDeliver.setDocumentDtoList(documentDtoList);
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WipDeliver> findAll(Pageable pageable) {
        Page<WipDeliver> wipDeliverPage = wipDeliverRepository.findAll(pageable, new NamedEntityGraph(WIP_DELIVER_ENTITY_GRAPH));
        return this.wipDeliverPageRelationDocument(wipDeliverPage, pageable);
    }

    /**
     * 添加通用查询获取文件信息
     *
     * @param wipDeliverPage 分页数据
     * @param pageable       分页参数
     * <AUTHOR>
     * @date 2023/2/1
     */
    public Page<WipDeliver> wipDeliverPageRelationDocument(Page<WipDeliver> wipDeliverPage, Pageable pageable) {
        List<WipDeliver> wipDelivers = wipDeliverPage.getContent();
        if (!ValidateUtils.isValid(wipDelivers)) {
            return wipDeliverPage;
        }
        wipDelivers = wipDelivers.stream().map(wipDeliver -> {
            List<DocumentDTO> documentDtoList = rbaseDocumentProxy.getByRecordId(wipDeliver.getId());
            return wipDeliver.setDocumentDtoList(documentDtoList);
        }).collect(Collectors.toList());
        return PageableExecutionUtils.getPage(wipDelivers, pageable, wipDeliverPage::getTotalElements);
    }

    /**
     * 新增人工出库记录，新增台账和减库存
     *
     * @param deliverDTO 批量人工出库记录
     * <AUTHOR>
     **/
    public void createDeliverInfo(DeliverDTO deliverDTO) {
        wipInventoryService.validWipWarehouse(deliverDTO.getWipWarehouseId());
        Long workSheetId = deliverDTO.getWorkSheetId();
        WorkSheet workSheet = null;
        if (Objects.nonNull(workSheetId)) {
            Optional<WorkSheet> workSheetOptional = workSheetRepository.findByIdAndDeleted(workSheetId, Constants.LONG_ZERO);
            if (!workSheetOptional.isPresent()) {
                throw new ResponseException("error.workSheetNotExist", "工单记录不存在");
            }
            workSheet = workSheetOptional.get();
        }
        //指定当前系统操作人为出库人
        UserDTO userDTO = rbaseRbacProxy.getUserByLoginName(SecurityUtils.getCurrentUserLogin().orElse(null));
        StaffDTO operateDto = Objects.nonNull(userDTO) ? userDTO.getStaffDTO() : new StaffDTO();
        if (!ObjectUtils.isEmpty(operateDto)) {
            deliverDTO.setOperatorId(operateDto.getId());
        }
        // 新增人工出库记录
        List<DeliverDTO.MaterialDetailDTO> materialDetailDTOList = deliverDTO.getMaterialDetailDTOList();
        if (ValidateUtils.isValid(materialDetailDTOList)) {
            for (int i = 0; i < materialDetailDTOList.size(); i++) {
                DeliverDTO.MaterialDetailDTO materialDetail = materialDetailDTOList.get(i);
                WipDeliver deliver = this.getDeliver(deliverDTO, workSheet);
                deliver.setOriginBatch(materialDetail.getOriginBatch())
                        .setDeliverBatch(materialDetail.getDeliverBatch())
                        .setMaterialId(materialDetail.getMaterialId())
                        .setNumber(materialDetail.getNumber());
                wipDeliverRepository.save(deliver);
                if(Objects.isNull(workSheet)){
                    // 工单不填 出库类型为领料出库 新增线边台账
                    wipInventoryService.generateLedgerAndUpdateInventory(this.getLedgerDTO(deliver, deliver.getNumber().negate(), WipLedgerOperationEnum.WIP_LEDGER_OPERATION_GLUE_STORE.getCategory(), WipLedgerCategoryEnum.WIP_LEDGER_CATEGORY_INVENTORY.getCategory()));
                }else{
                   // 如果用户填了工单 出库类型为工单出库  新增线边台账
                    wipInventoryService.generateLedgerAndUpdateInventory(this.getLedgerDTO(deliver, deliver.getNumber().negate(), WipLedgerOperationEnum.WIP_LEDGER_OPERATION_WORK_SHEET_DELEVER.getCategory(), WipLedgerCategoryEnum.WIP_LEDGER_CATEGORY_INVENTORY.getCategory()));
                    WsMaterialBatch wsMaterialBatch = wsMaterialBatchRepository.findByWorkSheetIdAndWarehouseIdAndMaterialIdAndBatchAndDeleted(workSheet.getId(), deliver.getWipWarehouse().getId(), deliver.getMaterialId(), deliver.getOriginBatch(), Constants.LONG_ZERO);
                    if (wsMaterialBatch == null) {
                        wsMaterialBatch = MapperUtils.map(deliverDTO, WsMaterialBatch.class);
                        wsMaterialBatch.setWorkSheet(workSheet).setWarehouseId(deliver.getWipWarehouse().getId()).setBatch(deliver.getDeliverBatch()).setMaterialId(deliver.getMaterialId()).setNumber(0).setLeftNumber(0).setDeleted(Constants.LONG_ZERO);
                    }
                    wipLedgerService.saveLedgerInfo(null, wsMaterialBatch, this.getLedgerDTO(deliver, deliver.getNumber(), WipLedgerOperationEnum.WIP_LEDGER_OPERATION_WORK_SHEET_STORE.getCategory(), WipLedgerCategoryEnum.WIP_LEDGER_CATEGORY_WORK_SHEET.getCategory()).setWorkSheet(workSheet));
                    wsMaterialBatch.setLeftNumber(wsMaterialBatch.getLeftNumber() + deliver.getNumber().doubleValue())
                            .setNumber(wsMaterialBatch.getNumber() + deliver.getNumber().doubleValue());
                    wsMaterialBatchRepository.save(wsMaterialBatch);
                }
                //新增出库记录对应的审批文件
                if (ValidateUtils.isValid(deliverDTO.getDocumentIdList())) {
                    DocumentRelationDTO documentRelationDto = new DocumentRelationDTO();
                    documentRelationDto.setServiceName(StringUtils.upperCase(applicationName))
                            .setModeName("WipDeliver").setRecordId(deliver.getId())
                            .setDocumentList(deliverDTO.getDocumentIdList().stream().map(document -> new DocumentRelationDTO.Document().setDocumentId(document)).collect(Collectors.toList()));
                    rbaseDocumentProxy.relation(documentRelationDto);
                }
            }
        }
    }

    /**
     * 构造台账DTO
     *
     * @param deliver      出库的实体类
     * @param changeNumber 变化的数量
     * @param operation    操作人id
     * @param category     操作类型
     * @return LedgerDTO
     * <AUTHOR>
     **/
    public LedgerDTO getLedgerDTO(WipDeliver deliver, BigDecimal changeNumber, Integer operation, Integer category) {
        LedgerDTO ledgerDTO = new LedgerDTO();
        ledgerDTO.setWipWarehouse(deliver.getWipWarehouse())
                .setWorkSheet(deliver.getWorkSheet())
                .setOriginBatch(deliver.getOriginBatch())
                .setDeliverBatch(deliver.getDeliverBatch())
                .setMaterialId(deliver.getMaterialId())
                .setChangeNumber(changeNumber)
                .setOperation(operation)
                .setCategory(category)
                .setOperatorId(deliver.getOperatorId());
        return ledgerDTO;
    }

    /**
     * 构造出库实体类
     *
     * @param deliverDTO 批量人工出库记录
     * @return WipDeliver
     * <AUTHOR>
     **/
    public WipDeliver getDeliver(DeliverDTO deliverDTO, WorkSheet workSheet) {
        WipDeliver deliver = MapperUtils.map(deliverDTO, WipDeliver.class);
        WipWarehouse wipWarehouse = new WipWarehouse();
        wipWarehouse.setId(deliverDTO.getWipWarehouseId());
        deliver.setRecordDate(LocalDateTime.now())
                .setWipWarehouse(wipWarehouse)
                .setWorkSheet(workSheet)
                .setDeleted(Constants.LONG_ZERO);
        return deliver;
    }

    /**
     * 添加线边出库文件关联
     *
     * @param wipDeliverRelationDocumentDto 文件关联信息
     * <AUTHOR>
     * @date 2023/2/1
     */
    public void relationDocument(WipDeliverRelationDocumentDTO wipDeliverRelationDocumentDto) {
        if (!ValidateUtils.isValid(wipDeliverRelationDocumentDto.getDocumentIdList())) {
            throw new ResponseException("error.关联文件不存在", "DocumentIsNotExist");
        }
        //添加当前线边出库文件关联
        DocumentRelationDTO documentRelationDto = new DocumentRelationDTO();
        documentRelationDto.setServiceName(StringUtils.upperCase(applicationName))
                .setModeName("WipDeliver").setRecordId(wipDeliverRelationDocumentDto.getWipDeliverId())
                .setDocumentList(wipDeliverRelationDocumentDto.getDocumentIdList().stream().map(document -> new DocumentRelationDTO.Document().setDocumentId(document)).collect(Collectors.toList()));
        rbaseDocumentProxy.relation(documentRelationDto);
    }
}
