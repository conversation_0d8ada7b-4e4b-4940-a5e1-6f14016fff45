package net.airuima.wip.service.procedure.api;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.domain.procedure.material.WsMaterialBatch;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/7/19
 */
@FuncDefault
public interface IWipLedgerService {

    /**
     * 如工单库存跟线边仓关联，需要新增台账信息
     * @param workLine 产线
     * @param wsMaterialBatch 工单库存
     * @param changeNumber 变化的数量
     * @param operation 操作人主键ID
     **/
    @FuncInterceptor("SideWarehouse")
    default void processWarehouseAndSaveLedger(WorkLine workLine, WsMaterialBatch wsMaterialBatch, double changeNumber, int operation, Long operatorId){

    }
}
