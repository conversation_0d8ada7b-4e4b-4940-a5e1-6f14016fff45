package net.airuima.wip.service.procedure;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.dto.StaffDTO;
import net.airuima.dto.UserDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.WipLedgerCategoryEnum;
import net.airuima.rbase.constant.WipLedgerOperationEnum;
import net.airuima.rbase.proxy.organization.RbaseRbacProxy;
import net.airuima.service.CommonJpaService;
import net.airuima.wip.domain.procedure.WipInventory;
import net.airuima.wip.domain.procedure.WipStore;
import net.airuima.wip.dto.LedgerDTO;
import net.airuima.wip.repository.procedure.WipInventoryRepository;
import net.airuima.wip.repository.procedure.WipStoreRepository;
import net.airuima.wip.web.rest.procedure.dto.WipCreateStoreReturnDTO;
import net.airuima.wip.web.rest.procedure.dto.WipMaterialBatchHouseDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边仓入库（领料记录）Service
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WipStoreService extends CommonJpaService<WipStore> {

    private static final String WIP_STORE_ENTITY_GRAPH = "wipStoreEntityGraph";
    private final WipStoreRepository wipStoreRepository;
    private final WipInventoryService wipInventoryService;

    @Autowired
    private WipInventoryRepository wipInventoryRepository;
    @Autowired
    private RbaseRbacProxy rbaseRbacProxy;

    public WipStoreService(WipStoreRepository wipStoreRepository, WipInventoryService wipInventoryService) {
        this.wipStoreRepository = wipStoreRepository;
        this.wipInventoryService = wipInventoryService;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WipStore> find(Specification<WipStore> spec, Pageable pageable) {
        return wipStoreRepository.findAll(spec, pageable, new NamedEntityGraph(WIP_STORE_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<WipStore> find(Specification<WipStore> spec) {
        return wipStoreRepository.findAll(spec, new NamedEntityGraph(WIP_STORE_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WipStore> findAll(Pageable pageable) {
        return wipStoreRepository.findAll(pageable, new NamedEntityGraph(WIP_STORE_ENTITY_GRAPH));
    }

    /**
     * 新增领料入库，新增台账记录，更新库存
     *
     * @param wipStore 新增领料明细
     * @return net.airuima.rbase.domain.procedure.wip.WipStore
     * <AUTHOR>
     **/
    public WipCreateStoreReturnDTO createStoreInfo(WipStore wipStore) {
        wipInventoryService.validWipWarehouse(wipStore.getWipWarehouse().getId());
        UserDTO userDTO = rbaseRbacProxy.getUserByLoginName(net.airuima.util.SecurityUtils.getCurrentUserLogin().orElse(null));
        StaffDTO operateDto = Objects.nonNull(userDTO) ? userDTO.getStaffDTO() : new StaffDTO();
        if (!ObjectUtils.isEmpty(operateDto)) {
            wipStore.setOperatorId(operateDto.getId());
        }
        // 新增领料入库
        wipStore.setRecordDate(LocalDateTime.now())
                .setDeleted(Constants.LONG_ZERO);
        wipStoreRepository.save(wipStore);

        // 新增台账记录，更新库存
        LedgerDTO ledgerDTO = new LedgerDTO();
        ledgerDTO.setWipWarehouse(wipStore.getWipWarehouse())
                .setOriginBatch(wipStore.getBatch())
                .setDeliverBatch(wipStore.getBatch())
                .setExpireDay(wipStore.getExpireDay())
                .setProduceDate(wipStore.getProduceDate() == null ? LocalDate.now() : wipStore.getProduceDate())
                .setNote(wipStore.getNote())
                .setMaterialId(wipStore.getMaterialId())
                .setOperation(WipLedgerOperationEnum.WIP_LEDGER_OPERATION_STORE.getCategory())
                .setCategory(WipLedgerCategoryEnum.WIP_LEDGER_CATEGORY_INVENTORY.getCategory())
                .setChangeNumber(wipStore.getNumber())
                .setOperatorId(wipStore.getOperatorId());
        WipInventory wipInventory = wipInventoryService.generateLedgerAndUpdateInventory(ledgerDTO);
        WipCreateStoreReturnDTO wipCreateStoreReturnDTO = new WipCreateStoreReturnDTO();
        wipCreateStoreReturnDTO.setWipStore(wipStore).setWipInventory(wipInventory);
        return wipCreateStoreReturnDTO;
    }

    /**
     * 验证线边仓库存是否存在相同物料批次
     *
     * @param wipMaterialBatchHouseDto 验证线边仓库存是否存在相同物料批次DTO
     * @return boolean
     */
    public boolean verifyWipInventory(WipMaterialBatchHouseDTO wipMaterialBatchHouseDto) {
        return wipInventoryRepository.findByWipWarehouseIdAndMaterialIdAndBatchAndDeleted(wipMaterialBatchHouseDto.getWarehouseId(), wipMaterialBatchHouseDto.getMaterialId(), wipMaterialBatchHouseDto.getBatch(), Constants.LONG_ZERO) != null;
    }

}
