package net.airuima.wip.service.procedure;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import com.google.common.collect.Lists;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.WipLedgerCategoryEnum;
import net.airuima.rbase.constant.WipLedgerOperationEnum;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.proxy.bom.RbaseMaterialProxy;
import net.airuima.rbase.proxy.rule.RbaseSysCodeProxy;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import net.airuima.wip.domain.base.WipWarehouse;
import net.airuima.wip.domain.procedure.WipInventory;
import net.airuima.wip.domain.procedure.WipLedger;
import net.airuima.wip.dto.InventoryDTO;
import net.airuima.wip.dto.LedgerDTO;
import net.airuima.wip.repository.base.WipWarehouseRepository;
import net.airuima.wip.repository.procedure.WipInventoryRepository;
import net.airuima.wip.repository.procedure.WipLedgerRepository;
import net.airuima.wip.web.rest.procedure.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.klock.annotation.Klock;
import org.springframework.boot.autoconfigure.klock.model.LockTimeoutStrategy;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 线边库存Service
 *
 * <AUTHOR>
 * @date 2022-09-01
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WipInventoryService extends CommonJpaService<WipInventory> {

    private static final String WIP_INVENTORY_ENTITY_GRAPH = "wipInventoryEntityGraph";
    private static final String WIP_FIFO_SCAN_KEY = "key_fifo_level";
    private final WipInventoryRepository wipInventoryRepository;
    private final WipWarehouseRepository wipWarehouseRepository;
    private final WipLedgerService wipLedgerService;
    private final WipLedgerRepository wipLedgerRepository;

    @Autowired
    private RbaseMaterialProxy rbaseMaterialProxy;
    @Autowired
    private RbaseSysCodeProxy rbaseSysCodeProxy;

    public WipInventoryService(WipInventoryRepository wipInventoryRepository, WipWarehouseRepository wipWarehouseRepository, WipLedgerService wipLedgerService, WipLedgerRepository wipLedgerRepository) {
        this.wipInventoryRepository = wipInventoryRepository;
        this.wipWarehouseRepository = wipWarehouseRepository;
        this.wipLedgerService = wipLedgerService;
        this.wipLedgerRepository = wipLedgerRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WipInventory> find(Specification<WipInventory> spec, Pageable pageable) {
        return wipInventoryRepository.findAll(spec, pageable, new NamedEntityGraph(WIP_INVENTORY_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<WipInventory> find(Specification<WipInventory> spec) {
        return wipInventoryRepository.findAll(spec, new NamedEntityGraph(WIP_INVENTORY_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WipInventory> findAll(Pageable pageable) {
        return wipInventoryRepository.findAll(pageable, new NamedEntityGraph(WIP_INVENTORY_ENTITY_GRAPH));
    }

    /**
     * @param wipWarehouseId 线边仓id
     * @param materialId     物料id
     * @param batch          批次
     * @param deleted        删除标志
     * @return int
     * @description 根据线边仓id查询线边库存
     * <AUTHOR>
     **/
    public WipInventory findByWipWarehouseIdAndMaterialIdAndBatchAndDeleted(Long wipWarehouseId, Long materialId, String batch, Long deleted) {
        return wipInventoryRepository.findByWipWarehouseIdAndMaterialIdAndBatchAndDeleted(wipWarehouseId, materialId, batch, deleted);
    }

    /**
     * @return WipInventory
     * @Description 更改库存，写台账记录
     * <AUTHOR>
     * @Param inventoryDTO 线边库存记录
     **/
    public WipInventory updateInventoryInfo(InventoryDTO inventoryDTO) {
        WipInventory originInventory = wipInventoryRepository.findByIdAndDeleted(inventoryDTO.getId(), Constants.LONG_ZERO);
        if (null == originInventory) {
            throw new ResponseException("error.wipInventoryNotExist", "线边库存记录不存在");
        }
        LedgerDTO ledgerDTO = new LedgerDTO();
        ledgerDTO.setWipWarehouse(originInventory.getWipWarehouse())
                .setOriginBatch(originInventory.getBatch())
                .setDeliverBatch(originInventory.getBatch())
                .setMaterialId(originInventory.getMaterialId())
                .setOperation(WipLedgerOperationEnum.WIP_LEDGER_OPERATION_CHANGE.getCategory())
                .setCategory(WipLedgerCategoryEnum.WIP_LEDGER_CATEGORY_INVENTORY.getCategory())
                .setChangeNumber(inventoryDTO.getLeftNumber().subtract(originInventory.getLeftNumber()))
                .setOperatorId(inventoryDTO.getOperatorId());
        return this.generateLedgerAndUpdateInventory(ledgerDTO);
    }

    /**
     * @return WipInventory
     * @Description 公共方法：操作线边库存生成台账
     * <AUTHOR>
     * @Param ledgerDTO 台账dto
     **/
    @Klock(keys = {"#ledgerDTO.wipWarehouse.id", "#ledgerDTO.materialId", "#ledgerDTO.originBatch", "#ledgerDTO.deliverBatch"}, waitTime = 60, leaseTime = 60, lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST)
    public WipInventory generateLedgerAndUpdateInventory(LedgerDTO ledgerDTO) {
        WipInventory inventory = wipInventoryRepository.findByWipWarehouseIdAndMaterialIdAndBatchAndDeleted(ledgerDTO.getWipWarehouse().getId(), ledgerDTO.getMaterialId(), ledgerDTO.getOriginBatch(), Constants.LONG_ZERO);
        if (null == inventory) {
            // 退料入库，工单出库
            if (ledgerDTO.getOperation() == WipLedgerOperationEnum.WIP_LEDGER_OPERATION_RETURN.getCategory()
                    || ledgerDTO.getOperation() == WipLedgerOperationEnum.WIP_LEDGER_OPERATION_WORK_SHEET_DELEVER.getCategory()
                    || ledgerDTO.getOperation() == WipLedgerOperationEnum.WIP_LEDGER_OPERATION_GLUE_STORE.getCategory()) {
                throw new ResponseException("error.wipInventoryNotExist", "该线边库存记录不存在");
            }
            inventory = new WipInventory().setWipWarehouse(ledgerDTO.getWipWarehouse())
                    .setMaterialId(ledgerDTO.getMaterialId())
                    .setBatch(ledgerDTO.getOriginBatch())
                    .setProduceDate(ledgerDTO.getProduceDate() == null ? LocalDate.now() : ledgerDTO.getProduceDate())
                    .setExpireDay(ledgerDTO.getExpireDay() == null ? Constants.INT_ZERO : ledgerDTO.getExpireDay())
                    .setNumber(BigDecimal.valueOf(Constants.INT_ZERO))
                    .setLeftNumber(BigDecimal.valueOf(Constants.INT_ZERO));
            //截止有效时间 = 生产日期 + 有效期（天）
            inventory.setExpirationDate(inventory.getProduceDate().plusDays(inventory.getExpireDay()));
            inventory.setExpired(inventory.getExpirationDate().isBefore(LocalDate.now()));
            inventory.setDeleted(Constants.LONG_ZERO);
        }
        // 新增台账
        wipLedgerService.saveLedger(ledgerDTO, inventory.getLeftNumber());
        // 只有【领料入库】的情况，领料总数才会变更
        if (ledgerDTO.getOperation() == WipLedgerOperationEnum.WIP_LEDGER_OPERATION_STORE.getCategory()) {
            inventory.setNumber(inventory.getNumber().add(ledgerDTO.getChangeNumber()));
        }
        inventory.setLeftNumber(inventory.getLeftNumber().add(ledgerDTO.getChangeNumber()))
                .setNote(ObjectUtils.isEmpty(ledgerDTO.getNote()) ? inventory.getNote() : ledgerDTO.getNote());
        wipInventoryRepository.save(inventory);
        return inventory;
    }

    /**
     * @param warehouseId 线边仓id
     * @param materialId  物料id
     * @param batch       批次
     * @param size        数量
     * @return List<WipInventory>
     * @description 根据线边仓id、物料id、模糊批次查询线边库存记录
     * <AUTHOR>
     **/
    @Transactional(readOnly = true)
    public List<WipInventory> findByWarehouseIdAndMaterialIdAndBatchContaining(Long warehouseId, Long materialId, String batch, Integer size) {
        return Optional.ofNullable(wipInventoryRepository.findByWipWarehouseIdAndMaterialIdAndBatchContainingAndDeleted(warehouseId, materialId, batch, Constants.LONG_ZERO, PageRequest.of(Constants.INT_ZERO, size))).map(Slice::getContent).orElse(null);
    }

    /**
     * @param warehouseId 线边仓id
     * @param materialId  物料id
     * @param batch       批次
     * @return WipInventory
     * @description 根据线边仓id、物料id、模糊批次查询线边库存记录
     * <AUTHOR>
     **/
    @Transactional(readOnly = true)
    public WipInventory findByWarehouseIdAndMaterialIdAndBatch(Long warehouseId, Long materialId, String batch) {
        WipInventory wipInventory = wipInventoryRepository.findByWipWarehouseIdAndMaterialIdAndBatchAndDeleted(warehouseId, materialId, batch, Constants.LONG_ZERO);
        if (Objects.isNull(wipInventory)) {
            throw new ResponseException("error.wipInventoryNotExist", "线边库存记录不存在");
        }
        return wipInventory;
    }

    /**
     * @return List<WipInventory>
     * @description 线边库存数量最低 TOP 10
     * <AUTHOR>
     **/
    @Transactional(readOnly = true)
    public List<WipInventory> findAllSortByLeftNumber(Long workLineId, Integer size) {
        WipWarehouse wipWarehouse = wipWarehouseRepository.findByWorkLineIdAndIsEnableAndDeleted(workLineId, true, Constants.LONG_ZERO);
        if (wipWarehouse != null) {
            return Optional.ofNullable(wipInventoryRepository.findByWipWarehouseIdAndDeleted(wipWarehouse.getId(), Constants.LONG_ZERO, PageRequest.of(Constants.INT_ZERO, size, Sort.by("leftNumber").ascending()))).map(Slice::getContent).orElse(null);
        }
        return Collections.emptyList();
    }

    /**
     * @param id 主键id
     * @description 根据主键校验线边仓的可用性
     * <AUTHOR>
     **/
    public void validWipWarehouse(Long id) {
        WipWarehouse wipWarehouse = wipWarehouseRepository.findByIdAndIsEnableAndDeleted(id, true, Constants.LONG_ZERO);
        if (wipWarehouse == null) {
            throw new ResponseException("error.wipWarehouseNotExist", "有效线边仓记录不存在");
        }
    }

    /**
     * 计算台账的出入库使用差值 =（工单出库+领料出库） - 退料入库
     *
     * @param wipLedgers 台账列表
     * @return java.math.BigDecimal
     * <AUTHOR>
     * @date 2023/1/9
     */
    public BigDecimal poorUse(List<WipLedger> wipLedgers) {
        //退料增加线边库存（退料入库）
        BigDecimal pullNumber = wipLedgers.stream().filter(wipLedger ->
                wipLedger.getOperation() == WipLedgerOperationEnum.WIP_LEDGER_OPERATION_RETURN.getCategory()).map(WipLedger::getNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
        //领用扣减线边库存（工单出库-领料出库）
        BigDecimal putNumber = wipLedgers.stream().filter(wipLedger -> wipLedger.getOperation() == WipLedgerOperationEnum.WIP_LEDGER_OPERATION_WORK_SHEET_DELEVER.getCategory() ||
                wipLedger.getOperation() == WipLedgerOperationEnum.WIP_LEDGER_OPERATION_GLUE_STORE.getCategory()).map(WipLedger::getNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
        return putNumber.subtract(pullNumber);
    }

    /**
     * 通过线边仓回去剩余物料大于0 的线边库存物料信息
     *
     * @param warehouseId 线边仓id
     * @return List<MaterialDTO>
     * <AUTHOR>
     * @date 2023/1/29
     */
    public List<MaterialDTO> queryInventoryMaterialByWarehouse(Long warehouseId) {
        List<WipInventory> wipInventories = wipInventoryRepository.findByWipWarehouseIdAndLeftNumberGreaterThanAndDeleted(warehouseId, BigDecimal.ZERO, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(wipInventories)) {
            return wipInventories.stream().map(WipInventory::getMaterialDto).collect(
                    Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(MaterialDTO::getId))), ArrayList::new));
        }
        return Collections.emptyList();
    }


    /**
     * 通过物料名称或编码模糊查询线边仓库存列表
     *
     * @param text 物料名称或者编码
     * @return List<WipInventoryDTO>
     * @size text 条数
     * <AUTHOR>
     * @date 2023/03/20
     */
    public List<WipInventoryDTO> findByMaterialCodeOrName(String text, Integer size) {
        List<WipInventoryDTO> wipInventoryDtoList = Lists.newArrayList();
        List<MaterialDTO> materialDTOList = rbaseMaterialProxy.findByNameOrCode(text, null, size);
        if (!ValidateUtils.isValid(materialDTOList)) {
            return wipInventoryDtoList;
        }
        List<Long> materialIds = materialDTOList.stream().map(MaterialDTO::getId).collect(Collectors.toList());
        List<WipInventory> wipInventoryList = wipInventoryRepository.findByMaterialIdGroupMaterialWipWarehouse(materialIds);
        if (!ValidateUtils.isValid(wipInventoryList)) {
            return wipInventoryDtoList;
        }
        Map<Long, List<WipInventory>> wipInventoryMap = wipInventoryList.stream().collect(Collectors.groupingBy(WipInventory::getMaterialId));
        wipInventoryMap.forEach((materialId, wipInventories) -> {
            WipInventoryDTO wipInventoryDto = new WipInventoryDTO();
            List<WipInventoryDTO.WipWarehouseDTO> wipWarehouseDtoList = new ArrayList<>();
            materialDTOList.stream().filter(materialDTO -> Objects.equals(materialDTO.getId(), materialId)).findFirst().ifPresent(materialDTO -> {
                wipInventoryDto.setMaterialId(materialDTO.getId())
                        .setMaterialCode(materialDTO.getCode())
                        .setMaterialName(materialDTO.getName());
            });
            wipInventories.forEach(wipInventory -> {
                WipInventoryDTO.WipWarehouseDTO wipWarehouseDTO = new WipInventoryDTO.WipWarehouseDTO();
                wipWarehouseDTO.setWipWarehouseId(wipInventory.getWipWarehouse().getId())
                        .setWipWarehouseName(wipInventory.getWipWarehouse().getName())
                        .setWipWarehouseCode(wipInventory.getWipWarehouse().getCode());
                wipWarehouseDtoList.add(wipWarehouseDTO);
            });
            wipInventoryDto.setWipWarehouseDtoList(wipWarehouseDtoList);
            wipInventoryDtoList.add(wipInventoryDto);
        });
        return wipInventoryDtoList;
    }

    /**
     * 通过物料id、仓位id、批次名称或者编码 获取线边仓库存批次列表
     *
     * @param batchesGetDto 获取线边库存批次信息请求dto
     * @return List<String>
     * <AUTHOR>
     * @date 2023/03/20
     */
    public List<String> getBatches(BatchesGetDTO batchesGetDto) {
        Page<String> page = wipInventoryRepository.getBatches(batchesGetDto, PageRequest.of(Constants.INT_ZERO, batchesGetDto.getSize()));
        return Optional.ofNullable(page).map(Slice::getContent).orElse(null);
    }

    /**
     * 通过线边仓id 物料id 入库批次 获取唯一 线边库存信息
     *
     * @param wipMaterialBatchHouseDto 线边库存唯一性dto
     * @return net.airuima.rbase.domain.procedure.wip.WipInventory
     * <AUTHOR>
     * @date 2023/4/10
     */
    public WipInventory findByMaterialIdAndBatchAndHouseId(WipMaterialBatchHouseDTO wipMaterialBatchHouseDto) {
        return wipInventoryRepository.findByWipWarehouseIdAndMaterialIdAndBatchAndDeleted(wipMaterialBatchHouseDto.getWarehouseId(), wipMaterialBatchHouseDto.getMaterialId(), wipMaterialBatchHouseDto.getBatch(), Constants.LONG_ZERO);
    }

    /**
     * 通过物料id 获取全部的 线边仓库
     *
     * @param materialId 物料id
     * @return WipInventoryDTO
     */
    public WipInventoryDTO queryInventoryHouseByMaterial(Long materialId) {

        List<WipInventory> wipInventories = wipInventoryRepository.findByMaterialIdAndDeletedGroupByWipWarehouseId(materialId, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(wipInventories)) {
            return null;
        }
        MaterialDTO materialDto = wipInventories.get(Constants.INT_ZERO).getMaterialDto();
        WipInventoryDTO wipInventoryDTO = new WipInventoryDTO();
        wipInventoryDTO.setMaterialId(materialDto.getId()).setMaterialCode(materialDto.getCode()).setMaterialName(materialDto.getName());
        List<WipInventoryDTO.WipWarehouseDTO> wipWarehouseDtoList = new ArrayList<>();
        wipInventories.forEach(wipInventory -> {
            WipInventoryDTO.WipWarehouseDTO wipWarehouseDTO = new WipInventoryDTO.WipWarehouseDTO();
            wipWarehouseDTO.setWipWarehouseId(wipInventory.getWipWarehouse().getId())
                    .setWipWarehouseName(wipInventory.getWipWarehouse().getName())
                    .setWipWarehouseCode(wipInventory.getWipWarehouse().getCode());
            wipWarehouseDtoList.add(wipWarehouseDTO);
        });

        wipInventoryDTO.setWipWarehouseDtoList(wipWarehouseDtoList);
        return wipInventoryDTO;
    }

    /**
     * 线边仓先进先出
     * 获取系统配置的扫描模式：
     * 0: 不管控，只验证扫描信息的合法性；
     * 1: 一般模式，验证扫入合法性，给出推荐最先入库的线边库存，但用户可以不采纳;
     * 2: 强制模式，验证扫入合法性，给出推荐最先入库的线边库存，用户必须采纳;
     *
     * @param wipInventoryFifoDto 入库先进先出dto
     * @return WipInventoryFifoResDTO
     * <AUTHOR>
     * @since 1.8.1
     */
    public WipInventoryFifoResDTO fifoInventory(WipInventoryFifoDTO wipInventoryFifoDto) {
        WipInventoryFifoResDTO wipInventoryFifoResDto = new WipInventoryFifoResDTO();
        WipWarehouse wipWarehouse = null;
        if (Objects.nonNull(wipInventoryFifoDto.getWarehouseId()) || Objects.nonNull(wipInventoryFifoDto.getWarehouseCode())) {
            wipWarehouse = Objects.nonNull(wipInventoryFifoDto.getWarehouseId()) ?
                    wipWarehouseRepository.findById(wipInventoryFifoDto.getWarehouseId()).orElse(null) :
                    wipWarehouseRepository.findByCodeAndDeleted(wipInventoryFifoDto.getWarehouseCode(), Constants.LONG_ZERO);
        }
        if (Objects.isNull(wipWarehouse)) {
            throw new ResponseException("error.wipWarehouseNotExist", "线边仓不存在");
        }
        Optional<MaterialDTO> materialOptional = rbaseMaterialProxy.findByCodeAndDeleted(wipInventoryFifoDto.getMaterialCode(), Constants.LONG_ZERO);
        if (materialOptional.isEmpty()) {
            throw new ResponseException("error.materialNotExist", "物料不存在");
        }
        MaterialDTO materialDto = materialOptional.get();

        WipInventory wipInventory = wipInventoryRepository.findByWipWarehouseIdAndMaterialIdAndBatchAndDeleted(wipWarehouse.getId(), materialDto.getId(), wipInventoryFifoDto.getBatch(), Constants.LONG_ZERO);

        //获取系统配置的扫描模式：
        // 0: 不管控，只验证扫描信息的合法性；
        // 1: 一般模式，验证扫入合法性，给出推荐最先入库的线边库存，但用户可以不采纳;
        // 2: 强制模式，验证扫入合法性，给出推荐最先入库的线边库存，用户必须采纳;
        String scanMode = rbaseSysCodeProxy.findByCode(WIP_FIFO_SCAN_KEY);
        scanMode = Objects.isNull(scanMode) ? "0" : scanMode;
        validDateScanInfo(wipInventoryFifoDto.getScanWipInventoryIds(), wipInventory, wipWarehouse, materialDto, wipInventoryFifoResDto);
        switch (scanMode) {
            case "0":
                // 不管控，只验证扫描信息的合法性
                break;
            case "1":
                // 一般模式，验证扫入合法性，给出推荐最先入库的线边库存，但用户可以不采纳
                recommendInventory(wipInventory, wipInventoryFifoResDto);
                break;
            case "2":
                // 强制模式，验证扫入合法性，给出推荐最先入库的线边库存，用户必须采纳
                enforceInventory(wipInventory, wipInventoryFifoResDto);
                break;
            default:
                throw new ResponseException("error.invalidScanMode", "无效的扫描模式");
        }
        return wipInventoryFifoResDto;
    }

    /**
     * 验证扫描信息的合法性
     *
     * @param scanWipInventoryIds    扫描的线边库存id
     * @param wipInventory           线边库存信息
     * @param wipWarehouse           线边仓信息
     * @param materialDto            物料信息
     * @param wipInventoryFifoResDto 线边库存扫描录入返回DTO
     * @return WipInventoryFifoResDTO
     * <AUTHOR>
     * @since 1.8.1
     */
    private WipInventoryFifoResDTO validDateScanInfo(List<Long> scanWipInventoryIds, WipInventory wipInventory,
                                                     WipWarehouse wipWarehouse, MaterialDTO materialDto,
                                                     WipInventoryFifoResDTO wipInventoryFifoResDto) {
        // 验证扫描信息的合法性
        if (Objects.nonNull(wipInventory)) {
            wipInventoryFifoResDto.setWipInventory(wipInventory).setSuccess(Boolean.TRUE);
        } else {
            wipInventoryFifoResDto.setSuccess(Boolean.FALSE);
        }

        if (Objects.nonNull(wipInventory) && wipInventory.getExpired()) {
            wipInventoryFifoResDto.setSuccess(Boolean.FALSE).setMessage("物料已过期，请使用以下批次重试");
        }
        // 给出推荐最先入库的线边库存
        List<WipInventory> recommendedInventories = wipInventoryRepository.findByWipWarehouseIdAndMaterialIdAndLeftNumberGreaterThanAndExpirationDateAfterAndDeleted(wipWarehouse.getId(), materialDto.getId(), BigDecimal.ZERO, LocalDate.now(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(recommendedInventories)) {
            recommendedInventories = recommendedInventories.stream()
                    .sorted(Comparator.comparing(WipInventory::getExpirationDate)
                            .thenComparing(WipInventory::getLeftNumber))
                    .collect(Collectors.toList());
            if (ValidateUtils.isValid(scanWipInventoryIds)) {
                recommendedInventories = recommendedInventories.stream()
                        .filter(recommendedInventory -> !scanWipInventoryIds.contains(recommendedInventory.getId()))
                        .collect(Collectors.toList());
            }
            wipInventoryFifoResDto.setRecommendWipInventoryList(recommendedInventories);
        }
        if (Objects.isNull(wipInventory) && !ValidateUtils.isValid(wipInventoryFifoResDto.getRecommendWipInventoryList())) {
            wipInventoryFifoResDto.setSuccess(Boolean.FALSE).setMessage("无可用批次，请入库后重试");
        }
        if (Objects.isNull(wipInventory) && ValidateUtils.isValid(wipInventoryFifoResDto.getRecommendWipInventoryList())) {
            wipInventoryFifoResDto.setSuccess(Boolean.FALSE).setMessage("无可用批次，推荐使用下列第一个批次出库");
        }
        //如果推荐的批次和扫描的批次一致，则不推荐
        if (Objects.nonNull(wipInventory) && ValidateUtils.isValid(wipInventoryFifoResDto.getRecommendWipInventoryList())) {
            if (Objects.equals(wipInventoryFifoResDto.getRecommendWipInventoryList().get(0).getId(), wipInventory.getId())) {
                wipInventoryFifoResDto.setRecommendWipInventoryList(null);
            }
        }
        return wipInventoryFifoResDto;
    }

    /**
     * 给出推荐最先入库的线边库存
     *
     * @param wipInventory           线边库存信息
     * @param wipInventoryFifoResDto 线边库存扫描录入返回DTO
     * <AUTHOR>
     * @since 1.8.1
     */
    private void recommendInventory(WipInventory wipInventory, WipInventoryFifoResDTO wipInventoryFifoResDto) {
        if (Objects.nonNull(wipInventory)) {
            wipInventoryFifoResDto.setSuccess(Boolean.TRUE)
                    .setWipInventory(wipInventory);
            if (ValidateUtils.isValid(wipInventoryFifoResDto.getRecommendWipInventoryList()) &&
                    !Objects.equals(wipInventoryFifoResDto.getRecommendWipInventoryList().get(0).getId(), wipInventory.getId())) {
                wipInventoryFifoResDto.setMessage("推荐使用下列第一个批次出库");
            }
        }
    }

    /**
     * 强制模式，验证扫入合法性，给出推荐最先入库的线边库存，用户必须采纳
     *
     * @param wipInventory           线边库存信息
     * @param wipInventoryFifoResDto 线边库存扫描录入返回DTO
     * <AUTHOR>
     * @since 1.8.1
     */
    private void enforceInventory(WipInventory wipInventory, WipInventoryFifoResDTO wipInventoryFifoResDto) {
        if (Objects.nonNull(wipInventory)) {
            wipInventoryFifoResDto.setSuccess(Boolean.TRUE)
                    .setWipInventory(wipInventory);
            if (ValidateUtils.isValid(wipInventoryFifoResDto.getRecommendWipInventoryList()) &&
                    !Objects.equals(wipInventoryFifoResDto.getRecommendWipInventoryList().get(0).getId(), wipInventory.getId())) {
                wipInventoryFifoResDto.setWipInventory(wipInventory).setSuccess(Boolean.FALSE).setMessage("请使用下列第一个批次出库");
            }
        }
    }

}
