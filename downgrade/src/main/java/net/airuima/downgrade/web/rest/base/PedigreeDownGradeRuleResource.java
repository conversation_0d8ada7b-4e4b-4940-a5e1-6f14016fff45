package net.airuima.downgrade.web.rest.base;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.downgrade.domain.base.PedigreeDownGradeRule;
import net.airuima.downgrade.service.base.PedigreeDownGradeRuleService;
import net.airuima.downgrade.web.rest.base.dto.PedigreeDownGradeRuleDTO;
import net.airuima.downgrade.web.rest.base.dto.PedigreeDownGradeSearchDTO;
import net.airuima.rbase.constant.Constants;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 产成品降级规则Resource
 *
 * <AUTHOR>
 * @date 2022-03-29
 **/
@Tag(name = "产成品降级规则Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/pedigree-down-grade-rule")
@AuthorityRegion("产成品降级")
@FuncInterceptor("Downgrade")
@AuthSkip("I")
public class PedigreeDownGradeRuleResource extends ProtectBaseResource<PedigreeDownGradeRule> {

    public static final String MODULE = "产成品降级规则";

    private static final String exception = "exception";
    private final PedigreeDownGradeRuleService pedigreeDownGradeRuleService;


    public PedigreeDownGradeRuleResource(PedigreeDownGradeRuleService pedigreeDownGradeRuleService) {
        this.pedigreeDownGradeRuleService = pedigreeDownGradeRuleService;
        this.mapUri = "/api/pedigree-down-grade-rule";
    }


    /**
     * 通过产成品降级规则名称和编码模糊查询降级规则
     *
     * @param text 名称或编码
     * @param size 行数
     * @return 降级规则
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过产成品降级规则名称或编码模糊查询产成品降级规则", parameters = {
            @Parameter(name = "text", description = "产成品降级规则名称或编码", required = true, schema = @Schema(type = "string"),in = ParameterIn.QUERY),
            @Parameter(name = "size",required = true,description = "查询匹配返回前N行", schema = @Schema(type = "integer",format = "int32"),in = ParameterIn.QUERY)
    })
    @GetMapping("/byNameOrCode")
    public ResponseEntity<ResponseData<List<PedigreeDownGradeRule>>> byNameOrCode(@RequestParam(value = "text") String text,
                                                    @RequestParam(value = "size") Integer size) {
        return ResponseData.ok(pedigreeDownGradeRuleService.findByNameOrCode(text, size));
    }

    /**
     * 通过产品名称或编码模糊查询产成品降级规则
     *
     * @param text 名称或编码
     * @param size 行数
     * @return 降级产品型号列表
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过产品名称或编码模糊查询产成品降级规则", parameters = {
            @Parameter(name = "text", description = "产品名称或编码", required = true, schema = @Schema(type = "string"),in = ParameterIn.QUERY),
            @Parameter(name = "size",required = true,description = "查询匹配返回前N行", schema = @Schema(type = "integer",format = "int32"),in = ParameterIn.QUERY)
    })
    @GetMapping("/byPedigreeNameOrCode")
    public ResponseEntity<ResponseData<List<PedigreeDownGradeRule>>> byPedigreeNameOrCode(@RequestParam(value = "text") String text,
                                                            @RequestParam(value = "size") Integer size) {
        return ResponseData.ok(pedigreeDownGradeRuleService.findByPedigreeNameOrCode(text, size));
    }

    /**
     * 通过产品名称或编码查询降级产品型号
     *
     * @param text 名称或编码
     * @param size 行数
     * @return 降级产品型号列表
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过产品名称或编码查询降级产品型号", parameters = {
            @Parameter(name = "text", description = "产品名称或编码", required = true, schema = @Schema(type = "string"),in = ParameterIn.QUERY),
            @Parameter(name = "size",required = true,description = "查询匹配返回前N行", schema = @Schema(type = "integer",format = "int32"),in = ParameterIn.QUERY)
    })
    @GetMapping("/byEqualPedigreeNameOrCode")
    public ResponseEntity<ResponseData<List<PedigreeDownGradeRule>>> byEqualPedigreeNameOrCode(@RequestParam(value = "text") String text,
                                                                 @RequestParam(value = "size") Integer size) {
        return ResponseData.ok(pedigreeDownGradeRuleService.findByEqualPedigreeNameOrCode(text, size));
    }

    /**
     * 通过查询条件查找产成品降级规则列表
     *
     * @param pedigreeDownGradeSearchDTO 查询条件
     * @return 规则列表
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过查询条件查找规产成品降级规则列表")
    @GetMapping("/findByCondition")
    public ResponseEntity<ResponseData<List<PedigreeDownGradeRule>>> findByCondition(@Parameter(schema = @Schema(implementation = PedigreeDownGradeSearchDTO.class),required = true,description = "查询条件")
                                                                                          @RequestBody PedigreeDownGradeSearchDTO pedigreeDownGradeSearchDTO) {
        return ResponseData.ok(pedigreeDownGradeRuleService.findByCondition(pedigreeDownGradeSearchDTO));
    }

    /**
     * 新增产成品规则
     *
     * @param pedigreeDownGradeRuleDTO 产成品规则
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "新增产成品规则")
    @PostMapping("/create")
    public ResponseEntity<ResponseData<Void>> create(@Parameter(schema = @Schema(implementation = PedigreeDownGradeRuleDTO.class),required = true,description = "新增产成品规则信息")
                                                          @RequestBody PedigreeDownGradeRuleDTO pedigreeDownGradeRuleDTO) {
        try {
            pedigreeDownGradeRuleService.createInstance(pedigreeDownGradeRuleDTO);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            return ResponseData.error(e);
        }

    }

    /**
     * 编辑产成品规则
     *
     * @param pedigreeDownGradeRuleDTO 产成品规则
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "编辑产成品规则")
    @PutMapping("/updateCustom")
    public ResponseEntity<ResponseData<Void>> updateCustom(@Parameter(schema = @Schema(implementation = PedigreeDownGradeRuleDTO.class),required = true,description = "新增产成品规则信息") @RequestBody PedigreeDownGradeRuleDTO pedigreeDownGradeRuleDTO) {
        try {
            pedigreeDownGradeRuleService.updateCustom(pedigreeDownGradeRuleDTO);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            return ResponseData.error(e);
        }
    }

    /**
     * 启用禁用降级规则
     *
     * @param ruleId   降级规则ID
     * @param isEnable 启用1；禁用0
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "启用禁用产成品规则")
    @PutMapping("/enable")
    @Parameters({
           @Parameter(name = "ruleId", description = "降级规则ID", required = true, schema = @Schema(type = "int64"),in = ParameterIn.QUERY),
           @Parameter(name = "isEnable", description = "状态(0:禁用;1:启用)", required = true, schema = @Schema(type = "boolean"),in = ParameterIn.QUERY)
    })
    public ResponseEntity<ResponseData<Void>> enable(@RequestParam(value = "ruleId") Long ruleId,
                                       @RequestParam(value = "isEnable") Boolean isEnable) {
        pedigreeDownGradeRuleService.enableRule(ruleId, isEnable);
        return ResponseData.save();
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, "产成品降级规则");
    }
}
