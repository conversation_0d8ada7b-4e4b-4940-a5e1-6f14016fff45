package net.airuima.downgrade.web.rest.procedure.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;


/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 产成品降级明细编辑DTO
 * <AUTHOR>
 * @date 2022-04-22
 **/
@Schema(description = "产成品降级明细编辑DTO")
public class PedigreeDownGradeDetailEditDTO {
    /**
     * 降级详情ID
     */
    @Schema(description = "降级详情ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;


    /**
     * 申请备注
     */
    @Schema(description = "申请备注")
    private String applyNote;

    /**
     * 可申请降级数量
     */
    @Schema(description = "工单可降级数量")
    private Integer enableApplyNumber;

    /**
     * 降级数量
     */
    @NotNull
    @Schema(description = "降级数量")
    private Integer number;

    public Integer getEnableApplyNumber() {
        return enableApplyNumber;
    }

    public void setEnableApplyNumber(Integer enableApplyNumber) {
        this.enableApplyNumber = enableApplyNumber;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getApplyNote() {
        return applyNote;
    }

    public void setApplyNote(String applyNote) {
        this.applyNote = applyNote;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }
}
