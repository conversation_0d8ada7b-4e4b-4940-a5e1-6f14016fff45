package net.airuima.downgrade.web.rest.procedure.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 降级型号申请列表
 * <AUTHOR>
 * @date 2022-03-30
 **/
@Schema(description = "降级型号申请列表DTO")
public class PedigreeDownGradeListDTO {
    /**
     * 降级型号id
     */
    @NotNull
    @Schema(description = "降级型号id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long targetPedigreeId;

    /**
     * 降级数量
     */
    @NotNull
    @Schema(description = "降级数量")
    private Integer number;

    public Long getTargetPedigreeId() {
        return targetPedigreeId;
    }

    public void setTargetPedigreeId(Long targetPedigreeId) {
        this.targetPedigreeId = targetPedigreeId;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

}
