package net.airuima.downgrade.web.rest.procedure;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.downgrade.domain.procedure.PedigreeDownGradeDetail;
import net.airuima.downgrade.service.procedure.PedigreeDownGradeDetailService;
import net.airuima.downgrade.web.rest.procedure.dto.PedigreeDownGradeDetailDTO;
import net.airuima.downgrade.web.rest.procedure.dto.PedigreeDownGradeDetailEditDTO;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 产成品降级明细
 *
 * <AUTHOR>
 * @date 2022-03-29
 **/
@Tag(name = "产成品降级明细Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/pedigree-down-grade-detail")
@AuthorityRegion("产成品降级")
@FuncInterceptor("Downgrade")
@AuthSkip("I")
public class PedigreeDownGradeDetailResource extends ProtectBaseResource<PedigreeDownGradeDetail> {

    public static final String MODULE = "产成品降级明细";
    private final PedigreeDownGradeDetailService pedigreeDownGradeDetailService;
    private static final String exception = "exception";


    public PedigreeDownGradeDetailResource(PedigreeDownGradeDetailService pedigreeDownGradeDetailService) {
        this.pedigreeDownGradeDetailService = pedigreeDownGradeDetailService;
        this.mapUri = "/api/pedigree-down-grade-detail";
    }


    /**
     * 新增产成品降级明细
     *
     * @param pedigreeDownGradeDetailDto 产成品降级明细
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "新增产成品明细")
    @PostMapping("/createCustom")
    public ResponseEntity<ResponseData<Void>> createCustom(@RequestBody PedigreeDownGradeDetailDTO pedigreeDownGradeDetailDto) {
        try {
            pedigreeDownGradeDetailService.createInstance(pedigreeDownGradeDetailDto);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            return ResponseData.error(e);
        }
    }


    /**
     * 降级审核(单个）
     *
     * @param detailId    降级详情ID
     * @param status      审核状态(0:待审核;1:已通过;2:已拒绝)
     * @param approverId  审批人ID
     * @param approveNote 审批备注
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_APPROVE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "降级审核(单个）")
    @PutMapping("/approve")
    @Parameters({
           @Parameter(name = "detailId", description = "降级详情ID", required = true),
           @Parameter(name = "status", description = "审核状态(0:待审核;1:已通过;2:已拒绝)", required = true),
           @Parameter(name = "approverId", description = "审批人ID", required = true),
           @Parameter(name = "approveNote", description = "审批备注", required = true)
    })
    public ResponseEntity<ResponseData<Void>> approve(@RequestParam(value = "detailId") Long detailId,
                                        @RequestParam(value = "status") Integer status,
                                        @RequestParam(value = "approverId") Long approverId,
                                        @RequestParam(value = "approveNote", required = false) String approveNote) {
        try {
            pedigreeDownGradeDetailService.approve(detailId, status, approverId, approveNote);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            return ResponseData.error(e);
        }

    }

    /**
     * 降级审核(批量）
     *
     * @param detailIdList 降级详情ID列表
     * @param status       审核状态(0:待审核;1:已通过;2:已拒绝)
     * @param approverId   审批人ID
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_APPROVE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "降级审核(批量）")
    @PutMapping("/approveBatch")
    @Parameters({
            @Parameter(name = "detailIdList", description = "降级详情ID列表", required = true),
            @Parameter(name = "status", description = "审核状态(0:待审核;1:已通过;2:已拒绝)"),
           @Parameter(name = "approverId", description = "审批人ID", required = true),
           @Parameter(name = "approveNote", description = "审批备注", required = true)
    })
    public ResponseEntity<ResponseData<Void>> approveBatch(@RequestBody() List<Long> detailIdList,
                                             @RequestParam(value = "status") Integer status,
                                             @RequestParam(value = "approverId") Long approverId,
                                             @RequestParam(value = "approveNote", required = false) String approveNote) {
        try {
            pedigreeDownGradeDetailService.approveBatch(detailIdList, status, approverId, approveNote);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            return ResponseData.error(e);
        }
    }

    /**
     * 通过审核状态查询降级详情列表
     *
     * @param status 审核状态(0:待审核;1:已通过;2:已拒绝)
     * @param size   行数
     * @return List<PedigreeDownGradeDetail>
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "通过审核状态查询降级详情列表")
    @GetMapping("/byStatus")
    @Parameters({
           @Parameter(name = "status", description = "审核状态(0:待审核;1:已通过;2:已拒绝)", required = true),
           @Parameter(name = "size", description = "行数", required = true)
    })
    public ResponseEntity<ResponseData<List<PedigreeDownGradeDetail>>> byStatus(@RequestParam(value = "status") Integer status,
                                                  @RequestParam(value = "size") Integer size) {
        return ResponseData.ok(pedigreeDownGradeDetailService.findPageByStatus(status, size));
    }


    /**
     * 通过总工单号模糊查询已有降级产品
     *
     * @param text 总工单号
     * @param size 行数
     * @return List<WorkSheet>
     * <AUTHOR>
     * @date 2022-04-12
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过总工单号模糊查询已有降级总工单列表")
    @GetMapping("/worksheet/serial-number")
    @Parameters({
           @Parameter(name = "text", description = "总工单号", required = true),
           @Parameter(name = "size", description = "行数", required = true)
    })
    public ResponseEntity<ResponseData<List<WorkSheet>>> findWorkSheetBySerialNumber(@RequestParam(value = "text") String text,
                                                       @RequestParam(value = "size") Integer size) {
        return ResponseData.ok(pedigreeDownGradeDetailService.findWorkSheetBySerialNumber(text, size));
    }

    /**
     * 通过总工单号查询是否已有降级商品
     *
     * @param text 总工单号
     * @return 数量
     * <AUTHOR>
     * @date 2022-04-12
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "通过总工单号查询是否已有降级商品")
    @GetMapping("/countWorkSheetBySerialNumber")
    @Parameter(name = "text", description = "总工单号", required = true)
    public ResponseEntity<ResponseData<Long>> countWorkSheetBySerialNumber(@RequestParam(value = "text") String text) {
        return ResponseData.ok(pedigreeDownGradeDetailService.countWorkSheetBySerialNumber(text));
    }


    /**
     * 通过子工单号模糊查询已有降级子工单列表
     *
     * @param text 子工单号
     * @param size 行数
     * @return List<WorkSheet>
     * @date 2022-04-12
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过子工单号模糊查询已有降级子工单列表")
    @GetMapping("/subworksheet/serial-number")
    @Parameters({
           @Parameter(name = "text", description = "子工单号", required = true),
           @Parameter(name = "size", description = "行数", required = true)
    })
    public ResponseEntity<ResponseData<List<SubWorkSheet>>> findSubWorkSheetBySerialNumber(@RequestParam(value = "text") String text,
                                                             @RequestParam(value = "size") Integer size) {
        return ResponseData.ok(pedigreeDownGradeDetailService.findSubWorkSheetBySerialNumber(text, size));
    }

    /**
     * 通过子工单号查询是否已有降级商品
     *
     * @param text 子工单号
     * @return 数量
     * @date 2022-04-12
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过子工单号查询是否已有降级商品")
    @GetMapping("/countSubWorkSheetBySerialNumber")
    @Parameter(name = "text", description = "子工单号", required = true)
    public ResponseEntity<ResponseData<Long>> countSubWorkSheetBySerialNumber(@RequestParam(value = "text") String text) {
        return ResponseData.ok(pedigreeDownGradeDetailService.countSubWorkSheetBySerialNumber(text));
    }

    /**
     * 通过工单id和商品id及合格数查询可降级申请数量
     *
     * @param workSheetId     总工单id
     * @param pedigreeId      产品id
     * @param qualifiedNumber 工单合格数
     * @return 数量
     * <AUTHOR>
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "通过工单id和商品id及合格数查询可降级申请数量")
    @Parameters({
           @Parameter(name = "workSheetId", description = "总工单id", required = true),
           @Parameter(name = "pedigreeId", description = "产品id", required = true),
           @Parameter(name = "qualifiedNumber", description = "工单合格数", required = true)
    })
    @GetMapping("/sumNumberByWorkSheet")
    public ResponseEntity<ResponseData<Long>> sumNumberByWorkSheet(@RequestParam(value = "workSheetId") Long workSheetId,
                                     @RequestParam(value = "pedigreeId") Long pedigreeId,
                                     @RequestParam(value = "qualifiedNumber") Integer qualifiedNumber) {
        return ResponseData.ok(pedigreeDownGradeDetailService.sumDownGradeNumberByWorkSheet(workSheetId, pedigreeId, qualifiedNumber));
    }

    /**
     * 通过子工单id和商品id及合格数查询可降级申请数量
     *
     * @param subWorkSheetId  子工单id
     * @param pedigreeId      产品id
     * @param qualifiedNumber 工单合格数
     * @return 数量
     * <AUTHOR>
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "通过子工单id和商品id及合格数查询可降级申请数量")
    @GetMapping("/sumNumberBySubWorkSheet")
    @Parameters({
           @Parameter(name = "subWorkSheetId", description = "子工单id", required = true),
           @Parameter(name = "pedigreeId", description = "产品id", required = true),
           @Parameter(name = "qualifiedNumber", description = "工单合格数", required = true)
    })
    public ResponseEntity<ResponseData<Long>> sumNumberBySubWorkSheet(@RequestParam(value = "subWorkSheetId") Long subWorkSheetId,
                                        @RequestParam(value = "pedigreeId") Long pedigreeId,
                                        @RequestParam(value = "qualifiedNumber") Integer qualifiedNumber) {
        return ResponseData.ok(pedigreeDownGradeDetailService.sumDownGradeNumberBySubWorkSheet(subWorkSheetId, pedigreeId, qualifiedNumber));
    }


    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览产成品降级明细";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建产成品降级明细";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改产成品降级明细";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除产成品降级明细";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_IMPORT)) {
            return "导入降级详情";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出降级详情";
        } else if (authority.equals(this.entityName.toUpperCase() + "_APPROVE")) {
            return "产成品降级审批";
        }
        return "";
    }

    @Override
    public String getRequiredAuthority(String requestType) {
        if (StringUtils.isNotBlank(requestType) && requestType.equals("ENTITY_APPROVE")) {
            return "PEDIGREEDOWNGRADEDETAIL_APPROVE";
        }
        return super.getRequiredAuthority(requestType);
    }


    /**
     * 编辑产成品降级明细
     * 编辑时只可编辑申请数量、原因
     *
     * @param pedigreeDownGradeDetailEditDTO 产成品降级明细编辑
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "编辑产成品明细")
    @PutMapping("/updateCustom")
    public ResponseEntity<ResponseData<Void>> updateCustom(@RequestBody PedigreeDownGradeDetailEditDTO pedigreeDownGradeDetailEditDTO) {
        try{
            pedigreeDownGradeDetailService.updateCustom(pedigreeDownGradeDetailEditDTO);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            return ResponseData.error(e);
        }
    }

    /**
     * 编辑时通过降级详情ID查询可降级申请数量
     *
     * @param detailId 降级详情ID
     * @return 数量
     * <AUTHOR>
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "编辑时通过降级详情ID查询可降级申请数量")
   @Parameter(name = "detailId", description = "降级详情ID", required = true)
    @GetMapping("/sumNumberByEdit")
    public Long sumNumberByEdit(@RequestParam(value = "detailId") Long detailId) {
        return pedigreeDownGradeDetailService.sumNumberByEdit(detailId);
    }
}
