package net.airuima.downgrade.web.rest.base.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 产成品降级规则DTO
 * <AUTHOR>
 * @date 2022-03-29
 **/
@Schema(description = "产成品降级规则DTO")
public class PedigreeDownGradeRuleDTO {
    /**
     * 规则ID
     */
    @Schema(description = "规则ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long ruleId;

    /**
     * 规则编码(更新时用)
     */
    @Schema(description = "规则编码(更新时用)")
    private String code;

    /**
     * 规则名称
     */
    @NotNull
    @Schema(description = "规则名称")
    private String name;

    /**
     * 产品型号
     */
    @NotNull
    @Schema(description = "产品型号")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long originPedigreeId;

    /**
     * 状态(0:禁用;1:启用)
     */
    @NotNull
    @Schema(description = "状态(0:禁用;1:启用)")
    private boolean isEnable;

    /**
     * 是否需要审批标志(0:不需要;1:需要)
     */
    @NotNull
    @Schema(description = "是否需要审批标志(0:不需要;1:需要)")
    private boolean isApproval;

    /**
     * 降级规则型号列表
     */
    private List<PedigreeDownGradeDTO> listDownGrade;

    public Long getRuleId() {
        return ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getOriginPedigreeId() {
        return originPedigreeId;
    }

    public void setOriginPedigreeId(Long originPedigreeId) {
        this.originPedigreeId = originPedigreeId;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
    }

    public boolean getIsApproval() {
        return isApproval;
    }

    public void setIsApproval(boolean isApproval) {
        this.isApproval = isApproval;
    }

    public List<PedigreeDownGradeDTO> getListDownGrade() {
        return listDownGrade;
    }

    public void setListDownGrade(List<PedigreeDownGradeDTO> listDownGrade) {
        this.listDownGrade = listDownGrade;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}

