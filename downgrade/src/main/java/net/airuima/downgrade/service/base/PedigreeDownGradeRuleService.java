package net.airuima.downgrade.service.base;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.downgrade.domain.base.PedigreeDownGradeRule;
import net.airuima.downgrade.repository.base.PedigreeDownGradeRuleRepository;
import net.airuima.downgrade.web.rest.base.dto.PedigreeDownGradeDTO;
import net.airuima.downgrade.web.rest.base.dto.PedigreeDownGradeRuleDTO;
import net.airuima.downgrade.web.rest.base.dto.PedigreeDownGradeSearchDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.service.base.serialnumber.ISerialNumberGenerate;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.ZoneId;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 产成品降级规则
 *
 * <AUTHOR>
 * @date 2022/3/29-9:47
 **/
@Service
@Transactional(rollbackFor = Exception.class)
public class PedigreeDownGradeRuleService extends CommonJpaService<PedigreeDownGradeRule> {
    private final String PEDIGREE_DOWN_GRADE_RULE_ENTITY_GRAPH = "pedigreeDownGradeRuleEntityGraph";
    /**
     * 产品不存在
     */
    private static final String ORIGIN_PEDIGREE_NOT_EXIST = "originPedigreeNotExist";
    /**
     * 降级产品不存在
     */
    private static final String TARGET_PEDIGREE_NOT_EXIST = "targetPedigreeNotExist";
    /**
     * 超过比例
     */
    private static final String PROPORTION_EXCEED = "proportionExceed";
    /**
     * 降级产品与产品相同
     */
    private static final String ORIGIN_PEDIGREE_SAME_OF_TARGET_PEDIGREE = "originPedigreeSameOfTargetPedigree";
    /**
     * 规则名称已存在
     */
    private static final String RULE_NAME_REPEAT = "ruleNameRepeat";
    /**
     * 产品已关联规则
     */
    private static final String ORIGIN_PEDIGREE_ALREADY_RULE = "originPedigreeAlreadyRule";
    /**
     * 降级产品与降级产品相同
     */
    private static final String TARGET_PEDIGREE_SAME_OF_TARGET_PEDIGREE = "TargetPedigreeSameOfTargetPedigree";

    private final PedigreeDownGradeRuleRepository pedigreeDownGradeRuleRepository;
    private final PedigreeRepository pedigreeRepository;

    @Autowired
    private ISerialNumberGenerate[] serialNumberGenerate;
    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;

    public PedigreeDownGradeRuleService(PedigreeDownGradeRuleRepository pedigreeDownGradeRuleRepository, PedigreeRepository pedigreeRepository) {
        this.pedigreeDownGradeRuleRepository = pedigreeDownGradeRuleRepository;
        this.pedigreeRepository = pedigreeRepository;
    }


    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<PedigreeDownGradeRule> find(Specification<PedigreeDownGradeRule> spec, Pageable pageable) {
        return pedigreeDownGradeRuleRepository.findAll(spec, pageable,new NamedEntityGraph(PEDIGREE_DOWN_GRADE_RULE_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<PedigreeDownGradeRule> find(Specification<PedigreeDownGradeRule> spec) {
        return pedigreeDownGradeRuleRepository.findAll(spec,new NamedEntityGraph(PEDIGREE_DOWN_GRADE_RULE_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<PedigreeDownGradeRule> findAll(Pageable pageable) {
        return pedigreeDownGradeRuleRepository.findAll(pageable,new NamedEntityGraph(PEDIGREE_DOWN_GRADE_RULE_ENTITY_GRAPH));
    }

    /**
     * 根据降级规则编码或者名称获取信息
     *
     * @param text     谱系编码或者名称
     * @param size     返回数据条数
     * @return 降级规则
     */
    @Transactional(readOnly = true)
    public List<PedigreeDownGradeRule> findByNameOrCode(String text, Integer size) {
        return Optional.ofNullable(pedigreeDownGradeRuleRepository
                .findByNameOrCode(text, PageRequest.of(Constants.INT_ZERO, size))).map(Slice::getContent).orElse(null);
    }

    /**
     * 通过产品名称和编码模糊查询降级产品型号
     *
     * @param text     谱系编码或者名称
     * @param size     返回数据条数
     * @return 降级型号
     */
    @Transactional(readOnly = true)
    public List<PedigreeDownGradeRule> findByPedigreeNameOrCode(String text, Integer size) {
        return Optional.ofNullable(pedigreeDownGradeRuleRepository
                .findByPedigreeNameOrCode(text, PageRequest.of(Constants.INT_ZERO, size))).map(Slice::getContent).orElse(null);
    }

    /**
     * 通过产品名称和编码查询降级产品型号
     *
     * @param text     谱系编码或者名称
     * @param size     返回数据条数
     * @return 降级型号
     */
    @Transactional(readOnly = true)
    public List<PedigreeDownGradeRule> findByEqualPedigreeNameOrCode(String text, Integer size) {
        return Optional.ofNullable(pedigreeDownGradeRuleRepository
                .findByEqualPedigreeNameOrCode(text, PageRequest.of(Constants.INT_ZERO, size))).map(Slice::getContent).orElse(null);
    }

    /**
     * 根据条件模糊查找降级规则
     * @param pedigreeDownGradeSearchDTO 条件
     * @return 降级规则
     */
    @Transactional(readOnly = true)
    public List<PedigreeDownGradeRule> findByCondition(PedigreeDownGradeSearchDTO pedigreeDownGradeSearchDTO) {

        Instant startTimestamp = null;
        if (pedigreeDownGradeSearchDTO.getStartDate() != null) {
            startTimestamp = pedigreeDownGradeSearchDTO.getStartDate().atStartOfDay(ZoneId.systemDefault()).toInstant();
        }
        Instant endTimestamp = null;
        if (pedigreeDownGradeSearchDTO.getEndDate() != null) {
            endTimestamp = pedigreeDownGradeSearchDTO.getEndDate().atStartOfDay(ZoneId.systemDefault()).toInstant();
        }

        Page<PedigreeDownGradeRule> page = pedigreeDownGradeRuleRepository.findByCondition( pedigreeDownGradeSearchDTO,
                startTimestamp, endTimestamp, PageRequest.of(pedigreeDownGradeSearchDTO.getCurrentPage(), pedigreeDownGradeSearchDTO.getSize()));

        return Optional.ofNullable(page).map(Slice::getContent).orElse(null);
    }

    /**
     * 新增产成品降级规则
     * @param pedigreeDownGradeRuleDto 规则数据
     * @return 处理结果
     */
    public void createInstance(PedigreeDownGradeRuleDTO pedigreeDownGradeRuleDto) {
        //查找是否已存在规则名称
        Integer countName = pedigreeDownGradeRuleRepository.countByName(pedigreeDownGradeRuleDto.getName());
        if (countName != null && countName > 0) {
            throw new ResponseException(RULE_NAME_REPEAT, "规则名称重复");
        }
        validateDownGradeRule(pedigreeDownGradeRuleDto);
        validateNewDownGradeRule(pedigreeDownGradeRuleDto);
        Pedigree pedigree = pedigreeRepository.getReferenceById(pedigreeDownGradeRuleDto.getOriginPedigreeId());
        //保存产品规则
        if (pedigreeDownGradeRuleDto.getListDownGrade() != null) {
            pedigreeDownGradeRuleDto.getListDownGrade().forEach(item -> {
                PedigreeDownGradeRule pedigreeDownGradeRule = new PedigreeDownGradeRule();
                Pedigree targetPedigree = pedigreeRepository.getReferenceById(item.getTargetPedigreeId());
                SerialNumberDTO serialNumberDto = serialNumberGenerate[0].getSerialNumber(Constants.KEY_PEDIGREE_DOWN_GRADE_CODE);
                    //自动生成code
                pedigreeDownGradeRule.setCode(rbaseSerialNumberProxy.generate(serialNumberDto))
                        .setName(pedigreeDownGradeRuleDto.getName())
                        .setOriginPedigree(pedigree)
                        .setTargetPedigree(targetPedigree)
                        .setProportion(item.getProportion())
                        .setIsApproval(pedigreeDownGradeRuleDto.getIsApproval())
                        .setIsEnable(pedigreeDownGradeRuleDto.getIsEnable())
                        .setDeleted(Constants.LONG_ZERO);

                pedigreeDownGradeRuleRepository.save(pedigreeDownGradeRule);
            });
        }
    }

    /**
     * 验证降级规则是否合法
     *
     * @param pedigreeDownGradeRuleDto 降级规则DTO
     * @return BaseDTO
     **/
    private void validateDownGradeRule(PedigreeDownGradeRuleDTO pedigreeDownGradeRuleDto) {
        //查找产品
        Pedigree originPedigree = pedigreeRepository.getReferenceById(pedigreeDownGradeRuleDto.getOriginPedigreeId());
        if (originPedigree.getId() == null) {
            throw new ResponseException(ORIGIN_PEDIGREE_NOT_EXIST, "产品不存在");
        }
        //查找降级产品
        if (!ValidateUtils.isValid(pedigreeDownGradeRuleDto.getListDownGrade())) {
            throw new ResponseException(TARGET_PEDIGREE_NOT_EXIST, "降级产品不存在");
        }

        //降级产品与产品相同无法添加
        if (pedigreeDownGradeRuleDto.getListDownGrade().stream().anyMatch(
                dg -> dg.getTargetPedigreeId().equals(originPedigree.getId()))) {
            throw new ResponseException(ORIGIN_PEDIGREE_SAME_OF_TARGET_PEDIGREE, "降级产品与产品相同");
        }
        //降级产品与降级产品相同无法添加
        if( pedigreeDownGradeRuleDto.getListDownGrade().size() != pedigreeDownGradeRuleDto.getListDownGrade().
                stream().map(PedigreeDownGradeDTO::getTargetPedigreeId).distinct().count() ) {
            throw new ResponseException(TARGET_PEDIGREE_SAME_OF_TARGET_PEDIGREE, "降级产品与降级产品相同");
        }

        //降级规则比例的总数
        double proportionSum = pedigreeDownGradeRuleDto.getListDownGrade().stream().mapToDouble(PedigreeDownGradeDTO::getProportion).sum();
        if (proportionSum > 1.00) {
            throw new ResponseException(PROPORTION_EXCEED, "降级规则比例的总数超过100%");
        }
        PedigreeDownGradeDTO pedigreeDownGradeDTO;
        for (int i = 0; i < pedigreeDownGradeRuleDto.getListDownGrade().size(); i++) {
            pedigreeDownGradeDTO = pedigreeDownGradeRuleDto.getListDownGrade().get(i);
            if (pedigreeDownGradeDTO == null) {
                throw new ResponseException(TARGET_PEDIGREE_NOT_EXIST, "降级产品不存在");
            }
            Pedigree targetPedigree = pedigreeRepository.getReferenceById(pedigreeDownGradeDTO.getTargetPedigreeId());
            if (targetPedigree.getId() == null) {
                throw new ResponseException(TARGET_PEDIGREE_NOT_EXIST, "降级产品不存在");
            }
        }

    }

    /**
     * 新增时，验证降级规则是否合法
     *
     * @param pedigreeDownGradeRuleDto 降级规则DTO
     * @return BaseDTO
     **/
    private void validateNewDownGradeRule(PedigreeDownGradeRuleDTO pedigreeDownGradeRuleDto) {
        //原比例合计
        Float originProportion = pedigreeDownGradeRuleRepository.sumProportionByOriginPedigreeId(pedigreeDownGradeRuleDto.getOriginPedigreeId());
        if (originProportion == null) {
            originProportion = 0.0f;
        }
        double proportionSum = pedigreeDownGradeRuleDto.getListDownGrade().stream().mapToDouble(PedigreeDownGradeDTO::getProportion).sum();
        //计算原比例和当前比例是否超过1
        if ((proportionSum + originProportion) > 1.00) {
            throw new ResponseException(PROPORTION_EXCEED, "降级规则比例的总数超过100%");
        }

        //产品是否已关联规则
        List<PedigreeDownGradeRule> repeatRule = pedigreeDownGradeRuleRepository.findByOriginPedigreeIdAndDeleted(
                pedigreeDownGradeRuleDto.getOriginPedigreeId(), Constants.LONG_ZERO);
        //遍历查看产品+降级型号是否已关联规则
        if (ValidateUtils.isValid(repeatRule) && repeatRule.stream().anyMatch( toRule ->
                pedigreeDownGradeRuleDto.getListDownGrade().stream().anyMatch(
                        dg -> dg.getTargetPedigreeId().equals(toRule.getTargetPedigree().getId()) ) )) {
            throw new ResponseException(ORIGIN_PEDIGREE_ALREADY_RULE, "产品已关联规则");
        }

    }

    /**
     * 启用禁用降级规则
     * @param ruleId 降级规则ID
     * @param isEnable 启用1；禁用0
     */
    public void enableRule(Long ruleId, Boolean isEnable) {
        //修改产品规则启用禁用
        PedigreeDownGradeRule pedigreeDownGradeRule = pedigreeDownGradeRuleRepository.getReferenceById(ruleId);
        pedigreeDownGradeRule.setIsEnable(isEnable);

        pedigreeDownGradeRuleRepository.save(pedigreeDownGradeRule);
    }


    /**
     * 更新降级规则
     * @param pedigreeDownGradeRuleDto 降级规则
     */
    public void updateCustom(PedigreeDownGradeRuleDTO pedigreeDownGradeRuleDto) {
        PedigreeDownGradeRule pedigreeDownGradeRule = pedigreeDownGradeRuleRepository.getReferenceById(pedigreeDownGradeRuleDto.getRuleId());
        if (!pedigreeDownGradeRule.getName().equals(pedigreeDownGradeRuleDto.getName())) {
            //更新时，查找是否已存在规则名称
            Integer countName = pedigreeDownGradeRuleRepository.countByName(pedigreeDownGradeRuleDto.getName());
            if (countName != null && countName > 0) {
                throw new ResponseException(RULE_NAME_REPEAT, "规则名称重复");
            }
        }
        validateDownGradeRule(pedigreeDownGradeRuleDto);
        Pedigree pedigree = pedigreeRepository.getReferenceById(pedigreeDownGradeRuleDto.getOriginPedigreeId());
        //保存产品规则
        if (pedigreeDownGradeRuleDto.getListDownGrade() != null) {
            //原比例合计
            Float originProportion = pedigreeDownGradeRuleRepository.sumProportionByOriginPedigreeId(pedigreeDownGradeRule.getOriginPedigree().getId());
            if (originProportion == null) {
                originProportion = 0.0f;
            }
            // 减去降级对应的原先比例
            originProportion = originProportion - pedigreeDownGradeRule.getProportion();
            for (int index=0; index< pedigreeDownGradeRuleDto.getListDownGrade().size(); index++) {
                PedigreeDownGradeDTO item = pedigreeDownGradeRuleDto.getListDownGrade().get(index);
                if (originProportion + item.getProportion() > 1.00) {
                    throw new ResponseException(PROPORTION_EXCEED, "降级规则比例的总数超过100%");
                }
                Pedigree targetPedigree = pedigreeRepository.getReferenceById(item.getTargetPedigreeId());
                //更新字段
                pedigreeDownGradeRule
                        .setName(pedigreeDownGradeRuleDto.getName())
                        .setOriginPedigree(pedigree)
                        .setTargetPedigree(targetPedigree)
                        .setProportion(item.getProportion())
                        .setIsApproval(pedigreeDownGradeRuleDto.getIsApproval())
                        .setIsEnable(pedigreeDownGradeRuleDto.getIsEnable())
                        .setDeleted(Constants.LONG_ZERO);
                pedigreeDownGradeRule.setId(pedigreeDownGradeRuleDto.getRuleId());
                pedigreeDownGradeRuleRepository.save(pedigreeDownGradeRule);
            }
        }
    }


}
