package net.airuima.downgrade.domain.base;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 产成品降级规则
 *
 * <AUTHOR>
 * @date 2022/3/28-16:38
 **/
@Schema(name = "产成品降级规则", description = "产成品降级规则")
@Entity
@Table(name = "base_pedigree_down_grade_rule", uniqueConstraints =
        {
        @UniqueConstraint(columnNames = {"origin_pedigree_id","target_pedigree_id", "deleted"}),
        @UniqueConstraint(columnNames = {"code", "deleted"}) } )
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@AuditEntity(value = "产成品降级规则数据")
@NamedEntityGraph(name = "pedigreeDownGradeRuleEntityGraph",attributeNodes = {@NamedAttributeNode("originPedigree"),@NamedAttributeNode("targetPedigree")})
public class PedigreeDownGradeRule extends CustomBaseEntity implements Serializable {
    private static final long serialVersionUID = 8469011588072080499L;

    /**
     * 规则编码
     */
    @Schema(description = "规则编码")
    @Column(name = "code", length = 32, nullable = false)
    private String code;

    /**
     * 规则名称
     */
    @Schema(description = "规则名称")
    @Column(name = "name", length = 15, nullable = false)
    private String name;

    /**
     * 产品型号
     */
    @NotNull
    @Schema(description = "产品型号")
    @ManyToOne
    @JoinColumn(name="origin_pedigree_id")
    private Pedigree originPedigree;


    /**
     * 降级型号
     */
    @NotNull
    @Schema(description = "降级型号")
    @ManyToOne
    @JoinColumn(name="target_pedigree_id")
    private Pedigree targetPedigree;


    /**
     * 状态(0:禁用;1:启用)
     */
    @Schema(description = "状态(0:禁用;1:启用)")
    @Column(name = "is_enable", nullable = false)
    private boolean isEnable;

    /**
     * 是否需要审批标志(0:不需要;1:需要)
     */
    @Schema(description = "是否需要审批标志(0:不需要;1:需要)")
    @Column(name = "is_approval", nullable = false)
    private boolean isApproval;

    /**
     * 可降级比例
     */
    @Schema(description = "可降级比例")
    @Column(name = "proportion")
    private float proportion;

    public String getCode() {
        return code;
    }

    public PedigreeDownGradeRule setCode(String code) {
        this.code = code;
        return this;
    }

    public String getName() {
        return name;
    }

    public PedigreeDownGradeRule setName(String name) {
        this.name = name;
        return this;
    }

    public Pedigree getOriginPedigree() {
        return originPedigree;
    }

    public PedigreeDownGradeRule setOriginPedigree(Pedigree originPedigree) {
        this.originPedigree = originPedigree;
        return this;
    }

    public Pedigree getTargetPedigree() {
        return targetPedigree;
    }

    public PedigreeDownGradeRule setTargetPedigree(Pedigree targetPedigree) {
        this.targetPedigree = targetPedigree;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public PedigreeDownGradeRule setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    public boolean getIsApproval() {
        return isApproval;
    }

    public PedigreeDownGradeRule setIsApproval(boolean isApproval) {
        this.isApproval = isApproval;
        return this;
    }

    public float getProportion() {
        return proportion;
    }

    public PedigreeDownGradeRule setProportion(float proportion) {
        this.proportion = proportion;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PedigreeDownGradeRule that = (PedigreeDownGradeRule) o;
        if (that.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), that.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }
}
