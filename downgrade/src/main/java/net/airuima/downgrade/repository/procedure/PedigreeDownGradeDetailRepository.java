package net.airuima.downgrade.repository.procedure;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.downgrade.domain.procedure.PedigreeDownGradeDetail;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 产成品降级明细
 *
 * <AUTHOR>
 * @date 2022-03-29
 **/
@Repository
public interface PedigreeDownGradeDetailRepository extends LogicDeleteableRepository<PedigreeDownGradeDetail>,
        EntityGraphJpaSpecificationExecutor<PedigreeDownGradeDetail>, EntityGraphJpaRepository<PedigreeDownGradeDetail, Long> {


    /**
     * 降级审核（批量）
     * @param status 审核状态(0:待审核;1:已通过;2:已拒绝)
     * @param approverId 审批人ID
     * @param collection 降级详情ID列表
     * @return int 审核数量
     */
    @Modifying
    @Transactional
    @Query(value = "update PedigreeDownGradeDetail p set p.status=:status,p.approverId=:approverId,p.approveNote=:approveNote,p.approveTime=current_timestamp,p.approveDate=current_date where p.id in (:collection)")
    int updateStatusBatch(@Param("status") Integer status, @Param("approverId") Long approverId, @Param("collection") Collection<Long> collection, @Param("approveNote") String approveNote);

    /**
     * 根据状态查询降级详情
     * @param status 审核状态(0:待审核;1:已通过;2:已拒绝)
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.procedure.pedigree.PedigreeDownGradeDetail> 产成品降级明细分页
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    Page<PedigreeDownGradeDetail> findByStatusAndDeleted(Integer status, Long deleted, Pageable pageable);


    /**
     * 通过子工单号模糊查询已有降级子工单
     * @param text 子工单号
     * @param pageable   分页
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.procedure.aps.SubWorkSheet> 子工单
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query(value = "select distinct sws FROM SubWorkSheet sws,PedigreeDownGradeRule r WHERE sws.workSheet.pedigree.id=r.originPedigree.id and sws.serialNumber like %?1%  and sws.deleted=0 and r.isEnable = true and r.deleted=0 ")
    Page<SubWorkSheet>  findSubWorkSheetBySerialNumber(String text, Pageable pageable);

    /**
     * 通过子工单号查询是否有降级商品
     * @param text 子工单号
     * @return java.lang.Long 数量
     * <AUTHOR>
     * @date  2022/4/12
     **/
    @DataFilter(isSkip = true)
    @Query(value = "SELECT count(sws.id) " +
            " FROM SubWorkSheet sws,PedigreeDownGradeRule r WHERE sws.workSheet.pedigree.id=r.originPedigree.id and sws.serialNumber like ?1%  and sws.deleted=0 and r.deleted=0 ")
    Long countSubWorkSheetBySerialNumber(String text);


    /**
     * 通过总工单号模糊查询已有降级总工单列表
     * @param text 总工单号
     * @param pageable   分页
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.procedure.aps.SubWorkSheet> 子工单
     * <AUTHOR>
     * @date  2022/4/12
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select distinct ws FROM WorkSheet ws,PedigreeDownGradeRule r WHERE ws.pedigree.id=r.originPedigree.id and ws.serialNumber like %?1%  and ws.deleted=0 and r.isEnable = true and r.deleted=0")
    Page<WorkSheet> findWorkSheetBySerialNumber(String text, Pageable pageable);

    /**
     * 通过总工单号查询是否有降级商品
     * @param text 总工单号
     * @return java.lang.Long 数量
     * <AUTHOR>
     * @date  2022/4/12
     **/
    @DataFilter(isSkip = true)
    @Query(value = "SELECT count(ws.id) " +
            " FROM WorkSheet ws,PedigreeDownGradeRule r WHERE ws.pedigree.id=r.originPedigree.id and ws.serialNumber = ?1  and ws.deleted=0 and r.deleted=0")
    Long countWorkSheetBySerialNumber(String text);


    /**
     * 根据工单号统计该工单降级数量之和
     * @param workSheetId 工单主键ID
     * @return java.lang.Long 降级数量之和
     */
    @DataFilter(isSkip = true)
    @Query(value = "select sum(p.number) from PedigreeDownGradeDetail p  where p.workSheet.id=?1  and (p.status=0 or p.status=1 ) and p.deleted=0")
    Long sumNumberByWorkSheet(Long workSheetId);


    /**
     * 审批后根据工单号统计该工单降级数量之和
     * @param workSheetId 工单主键ID
     * @param status 审核状态(0:待审核;1:已通过;2:已拒绝)
     * @return java.lang.Long 降级数量之和
     */
    @DataFilter(isSkip = true)
    @Query(value = "select sum(p.number) from PedigreeDownGradeDetail p  where p.workSheet.id=?1  and p.status=?2  and p.deleted=0")
    Long sumApproveNumberByWorkSheetAndStatus(Long workSheetId, Integer status);

    /**
     * 根据子工单号统计该子工单降级数量之和
     * @param subWorkSheetId 子工单主键
     * @return java.lang.Long 降级数量之和
     */
    @DataFilter(isSkip = true)
    @Query(value = "select sum(p.number) from PedigreeDownGradeDetail p  where p.subWorkSheet.id=?1 and (p.status=0 or p.status=1 ) and p.deleted=0")
    Long sumNumberBySubWorkSheet(Long subWorkSheetId);

    /**
     * 审批后根据子工单号统计该子工单降级数量之和
     * @param subWorkSheetId 子工单主键
     * @param status 审核状态(0:待审核;1:已通过;2:已拒绝)
     * @return java.lang.Long 降级数量之和
     */
    @DataFilter(isSkip = true)
    @Query(value = "select sum(p.number) from PedigreeDownGradeDetail p  where p.subWorkSheet.id=?1 and p.status=?2  and p.deleted=0")
    Long sumApproveNumberBySubWorkSheetAndStatus(Long subWorkSheetId, Integer status);

    /**
     * 查询批量审批的总工单降级详情
     * @param collection 降级详情主键ID列表
     * @return java.util.List<net.airuima.rbase.domain.procedure.pedigree.PedigreeDownGradeDetail> 产成品降级明细列表
     */
    @DataFilter(isSkip = true)
    @Query(value = "select p from PedigreeDownGradeDetail p  where p.id in (:collection)  and p.deleted=0 GROUP BY p.workSheet.id")
    List<PedigreeDownGradeDetail> findWorkSheetByDetailIds(@Param("collection") Collection<Long> collection);

    /**
     * 查询批量审批的子工单降级详情
     * @param collection 降级详情主键ID列表
     * @return java.util.List<net.airuima.rbase.domain.procedure.pedigree.PedigreeDownGradeDetail> 产成品降级明细列表
     */
    @DataFilter(isSkip = true)
    @Query(value = "select p from PedigreeDownGradeDetail p  where p.id in (:collection) and p.subWorkSheet is not null and p.deleted=0 GROUP BY p.subWorkSheet.id")
    List<PedigreeDownGradeDetail> findSubWorkSheetByDetailIds(@Param("collection") Collection<Long> collection);
}
