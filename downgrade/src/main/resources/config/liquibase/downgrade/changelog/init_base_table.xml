<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.6.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.6.xsd">
    <changeSet author="zhuhuawu (generated)" id="1737512618718-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="base_pedigree_down_grade_rule"/>
            </not>
        </preConditions>
        <createTable tableName="base_pedigree_down_grade_rule">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="code" remarks="规则编码" type="VARCHAR(32)">
                <constraints nullable="false"/>
            </column>
            <column name="name" remarks="规则名称" type="VARCHAR(15)">
                <constraints nullable="false"/>
            </column>
            <column name="origin_pedigree_id" remarks="产品型号" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueBoolean="false" name="is_enable" remarks="状态(0:禁用;1:启用)" type="BIT(1)"/>
            <column defaultValueBoolean="true" name="is_approval" remarks="是否需要审批标志(0:不需要;1:需要)" type="BIT(1)"/>
            <column name="target_pedigree_id" remarks="降级型号" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="1.0" name="proportion" remarks="可降级比例" type="FLOAT(5, 2)"/>
            <column defaultValueNumeric="0" name="deleted" remarks="逻辑删除" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
        <createTable tableName="procedure_pedigree_down_grade_detail">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="serial_number" remarks="降级申请单号" type="VARCHAR(50)"/>
            <column name="work_sheet_id" remarks="总工单id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="sub_work_sheet_id" remarks="子工单id" type="BIGINT"/>
            <column name="origin_pedigree_id" remarks="产品型号" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="target_pedigree_id" remarks="降级型号" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="rule_id" remarks="规则id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="number" remarks="降档数量" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="status" remarks="审核状态(0:待审核;1:已通过;2:已拒绝)" type="TINYINT(3)"/>
            <column name="applier_id" remarks="申请人ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="apply_time" remarks="申请时间" type="timestamp"/>
            <column name="apply_date" type="timestamp"/>
            <column name="apply_note" remarks="申请备注" type="VARCHAR(255)"/>
            <column name="approver_id" remarks="审批人ID" type="BIGINT"/>
            <column name="approve_time" remarks="审批时间" type="timestamp"/>
            <column name="approve_date" type="timestamp"/>
            <column name="approve_note" remarks="审批备注" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" remarks="逻辑删除" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueBoolean="true" name="is_approval" remarks="是否需要审批标志(0:不需要;1:需要)" type="BIT(1)"/>
        </createTable>

        <addUniqueConstraint columnNames="code, deleted" constraintName="base_pedigree_down_code_unique" tableName="base_pedigree_down_grade_rule"/>
        <addUniqueConstraint columnNames="origin_pedigree_id, target_pedigree_id, deleted" constraintName="base_pedigree_down_unique" tableName="base_pedigree_down_grade_rule"/>
        <createIndex indexName="base_pedigree_down_down_grade_pedigree_id_index" tableName="base_pedigree_down_grade_rule">
            <column name="target_pedigree_id"/>
        </createIndex>
        <createIndex indexName="base_pedigree_down_pedigree_id_index" tableName="base_pedigree_down_grade_rule">
            <column name="origin_pedigree_id"/>
        </createIndex>
        <createIndex indexName="procedure_pedigree_down_grade_detail_applier_index" tableName="procedure_pedigree_down_grade_detail">
            <column name="applier_id"/>
        </createIndex>
        <createIndex indexName="procedure_pedigree_down_grade_detail_apply_date_index" tableName="procedure_pedigree_down_grade_detail">
            <column name="apply_date"/>
        </createIndex>
        <createIndex indexName="procedure_pedigree_down_grade_detail_approve_date_index" tableName="procedure_pedigree_down_grade_detail">
            <column name="approve_date"/>
        </createIndex>
        <createIndex indexName="procedure_pedigree_down_grade_detail_serial_number_index" tableName="procedure_pedigree_down_grade_detail">
            <column name="serial_number"/>
        </createIndex>
        <createIndex indexName="procedure_pedigree_down_grade_detail_status_index" tableName="procedure_pedigree_down_grade_detail">
            <column name="status"/>
        </createIndex>
        <createIndex indexName="procedure_pedigree_down_grade_detail_sub_work_sheet_id_index" tableName="procedure_pedigree_down_grade_detail">
            <column name="sub_work_sheet_id"/>
        </createIndex>
        <createIndex indexName="procedure_pedigree_down_grade_detail_work_sheet_id_index" tableName="procedure_pedigree_down_grade_detail">
            <column name="work_sheet_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
