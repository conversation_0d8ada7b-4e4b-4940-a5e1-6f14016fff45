package net.airuima.oem.proxy;

import net.airuima.config.bean.BeanDefine;
import net.airuima.config.bean.ObjectField;
import net.airuima.rbase.dto.qms.SampleCaseDTO;
import org.springframework.stereotype.Component;

@Component
public class OemQuerySamplingStrategyProxy {

    /**
     * 根据抽样方案和需要抽样的数量获取抽样结果
     *
     * @param sampleCase 抽样方案
     * @param number     需要抽样的数量
     * @return 抽样结果
     */
    @BeanDefine(value = "querySamplingStrategyServiceImpl",funcKey = "StepOem")
    public Integer getSampleResult(@ObjectField("net.airuima.qms.domain.base.SampleCase") SampleCaseDTO sampleCase,
                                   Integer number) {
        return null;
    }

}
