package net.airuima.oem.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.base.process.Step;

import java.io.Serializable;

@Schema(description = "外协工序剩余可外协数量DTO")
public class StepOemRemainingNumberDTO implements Serializable {

    /**
     * 外协工序
     */
    @Schema(description = "外协工序")
    private Step step;

    /**
     * 剩余外协数量
     */
    @Schema(description = "剩余外协数量")
    private Integer remainingNumber;

    public Step getStep() {
        return step;
    }

    public StepOemRemainingNumberDTO setStep(Step step) {
        this.step = step;
        return this;
    }

    public Integer getRemainingNumber() {
        return remainingNumber;
    }

    public StepOemRemainingNumberDTO setRemainingNumber(Integer remainingNumber) {
        this.remainingNumber = remainingNumber;
        return this;
    }
}
