package net.airuima.oem.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.oem.domain.procedure.OemInspect;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.dto.organization.SupplierDTO;

import java.io.Serializable;
import java.util.Objects;

@Schema(description = "外协收货待检任务")
public class OemTodoInspectHistoryDTO implements Serializable {

    /**
     * 外协质检任务单id
     */
    @Schema(description = "外协质检任务单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long taskId;

    /**
     * 外协质检编号
     */
    @Schema(description = "外协质检编号")
    private String serialNumber;

    /**
     * 供应商
     */
    @Schema(description = "供应商")
    private SupplierDTO supplierDto;

    /**
     * 外协单号
     */
    @Schema(description = "外协单号")
    private String oemSerialNumber;

    /**
     * 源关联工单编码
     */
    @Schema(description = "源关联工单编码")
    private String originalSerialNumber;

    /**
     * 源关联工序
     */
    @Schema(description = "源关联工序")
    private Step step;

    /**
     * 来料数量
     */
    @Schema(description = "来料数量")
    private Integer number;

    public OemTodoInspectHistoryDTO() {
    }

    public OemTodoInspectHistoryDTO(OemInspect oemInspect) {
        this.taskId = oemInspect.getId();
        this.serialNumber = oemInspect.getSerialNumber();
        this.supplierDto = oemInspect.getOemReceiptHistory().getOemOrder().getSupplierDto();
        this.oemSerialNumber = oemInspect.getOemReceiptHistory().getOemOrder().getWorkSheet().getSerialNumber();
        this.originalSerialNumber = Objects.nonNull(oemInspect.getOemReceiptHistory().getOemOrder().getOriginalSubWorkSheet()) ?
                oemInspect.getOemReceiptHistory().getOemOrder().getOriginalSubWorkSheet().getSerialNumber() :
                oemInspect.getOemReceiptHistory().getOemOrder().getOriginalWorkSheet().getSerialNumber();
        this.step = oemInspect.getOemReceiptHistory().getOemOrder().getStep();
        this.number = oemInspect.getNumber();
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public OemTodoInspectHistoryDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }


    public SupplierDTO getSupplierDto() {
        return supplierDto;
    }

    public OemTodoInspectHistoryDTO setSupplierDto(SupplierDTO supplierDto) {
        this.supplierDto = supplierDto;
        return this;
    }

    public String getOemSerialNumber() {
        return oemSerialNumber;
    }

    public OemTodoInspectHistoryDTO setOemSerialNumber(String oemSerialNumber) {
        this.oemSerialNumber = oemSerialNumber;
        return this;
    }

    public String getOriginalSerialNumber() {
        return originalSerialNumber;
    }

    public OemTodoInspectHistoryDTO setOriginalSerialNumber(String originalSerialNumber) {
        this.originalSerialNumber = originalSerialNumber;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public OemTodoInspectHistoryDTO setStep(Step step) {
        this.step = step;
        return this;
    }

    public Long getTaskId() {
        return taskId;
    }

    public OemTodoInspectHistoryDTO setTaskId(Long taskId) {
        this.taskId = taskId;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public OemTodoInspectHistoryDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }
}
