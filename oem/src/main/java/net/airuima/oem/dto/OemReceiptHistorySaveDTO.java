package net.airuima.oem.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

@Schema(description = "外协收货DTO")
public class OemReceiptHistorySaveDTO implements Serializable {

    /**
     * 外协id
     */
    @Schema(description = "外协id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long oemOrderId;

    /**
     * 收货数量
     */
    @Schema(description = "收货数量")
    private Integer number;

    /**
     * 操作用户id
     */
    @Schema(description = "操作用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long operatorId;

    /**
     * sn列表
     */
    @Schema(description = "sn列表")
    private List<String> snList;

    public Long getOemOrderId() {
        return oemOrderId;
    }

    public OemReceiptHistorySaveDTO setOemOrderId(Long oemOrderId) {
        this.oemOrderId = oemOrderId;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public OemReceiptHistorySaveDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public List<String> getSnList() {
        return snList;
    }

    public OemReceiptHistorySaveDTO setSnList(List<String> snList) {
        this.snList = snList;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public OemReceiptHistorySaveDTO setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }
}
