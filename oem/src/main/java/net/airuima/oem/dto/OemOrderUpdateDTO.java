package net.airuima.oem.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "外协工单修改DTO")
public class OemOrderUpdateDTO implements Serializable {

    /**
     * 外协工单ID
     */
    @Schema(description = "外协工单ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long oemOrderId;

    /**
     * 供应商ID
     */
    @Schema(description = "供应商ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long supplierId;

    public Long getOemOrderId() {
        return oemOrderId;
    }

    public OemOrderUpdateDTO setOemOrderId(Long oemOrderId) {
        this.oemOrderId = oemOrderId;
        return this;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public OemOrderUpdateDTO setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
        return this;
    }
}
