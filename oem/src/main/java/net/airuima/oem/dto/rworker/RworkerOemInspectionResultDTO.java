package net.airuima.oem.dto.rworker;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import net.airuima.rbase.dto.rworker.quality.dto.RworkerInspectionResultDTO;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@Schema(description = "保存外协质检结果信息DTO")
public class RworkerOemInspectionResultDTO implements Serializable {

    /**
     * 类型(0,首检;1 巡检，3 终检，4，抽检，5，来料检，6，外协质检)
     */
    @Schema(description = "类型(0,首检;1 巡检，3 终检，4，抽检，5，来料检，6，外协质检)")
    @NotNull(message = "下交质检类型不能为空")
    private Integer category;

    /**
     * 质检任务ID
     */
    @Schema(description = "质检任务ID")
    private Long taskId;

    /**
     * 质检方案id
     */
    @Schema(description = "质检方案id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long checkRuleId;


    /**
     * 被检测工位ID
     */
    @Schema(description = "被检测工位ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long workCellId;

    /**
     * 报检数量
     */
    @Schema(description = "报检数量")
    @NotNull(message = "报检数量不能为空")
    private Integer inspectNumber;


    /**
     * 合格数量
     */
    @Schema(description = "合格数量")
    @NotNull(message = "合格数量不能为空")
    private Integer qualifiedNumber;
    /**
     * 不合格数量
     */
    @Schema(description = "不合格数量")
    @NotNull(message = "不合格数量不能为空")
    private Integer unqualifiedNumber;
    /**
     * 检查结果
     */
    @Schema(description = "检查结果")
    @NotNull(message = "检查结果不能为空")
    private Boolean result;

    /**
     * 操作人ID
     */
    @Schema(description = "操作人ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "操作人不能为空")
    private Long operatorId;

    /**
     * 处理方式
     */
    @Schema(description = "处理方式")
    private Integer dealWay;

    /**
     * MRB申请原因
     */
    @Schema(description = "MRB申请原因")
    private String reason;

    /**
     * sn检测项目列表
     */
    @Schema(description = "sn检测项目列表")
    private List<RworkerInspectionResultDTO.SnCheckItemDTO> snCheckItemDtoList;

    /**
     * 挑选处理
     */
    @Schema(description = "挑选处理")
    private SelectCheckResult selectCheckResult;

    /**
     * 是否破坏性检测
     */
    @Schema(description = "是否破坏性检测")
    private DestroyCheckResult destroyCheckResult;

    public DestroyCheckResult getDestroyCheckResult() {
        return destroyCheckResult;
    }

    public RworkerOemInspectionResultDTO setDestroyCheckResult(DestroyCheckResult destroyCheckResult) {
        this.destroyCheckResult = destroyCheckResult;
        return this;
    }


    public Integer getCategory() {
        return category;
    }

    public RworkerOemInspectionResultDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public Long getTaskId() {
        return taskId;
    }

    public RworkerOemInspectionResultDTO setTaskId(Long taskId) {
        this.taskId = taskId;
        return this;
    }

    public Long getWorkCellId() {
        return workCellId;
    }

    public RworkerOemInspectionResultDTO setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
        return this;
    }


    public Integer getInspectNumber() {
        return inspectNumber;
    }

    public RworkerOemInspectionResultDTO setInspectNumber(Integer inspectNumber) {
        this.inspectNumber = inspectNumber;
        return this;
    }

    public Integer getQualifiedNumber() {
        return qualifiedNumber;
    }

    public RworkerOemInspectionResultDTO setQualifiedNumber(Integer qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public Integer getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public RworkerOemInspectionResultDTO setUnqualifiedNumber(Integer unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public Boolean getResult() {
        return result;
    }

    public RworkerOemInspectionResultDTO setResult(Boolean result) {
        this.result = result;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public RworkerOemInspectionResultDTO setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public List<RworkerInspectionResultDTO.SnCheckItemDTO> getSnCheckItemDtoList() {
        return snCheckItemDtoList;
    }

    public RworkerOemInspectionResultDTO setSnCheckItemDtoList(List<RworkerInspectionResultDTO.SnCheckItemDTO> snCheckItemDtoList) {
        this.snCheckItemDtoList = snCheckItemDtoList;
        return this;
    }

    public Long getCheckRuleId() {
        return checkRuleId;
    }

    public RworkerOemInspectionResultDTO setCheckRuleId(Long checkRuleId) {
        this.checkRuleId = checkRuleId;
        return this;
    }

    public Integer getDealWay() {
        return dealWay;
    }

    public RworkerOemInspectionResultDTO setDealWay(Integer dealWay) {
        this.dealWay = dealWay;
        return this;
    }

    public String getReason() {
        return reason;
    }

    public RworkerOemInspectionResultDTO setReason(String reason) {
        this.reason = reason;
        return this;
    }

    public SelectCheckResult getSelectCheckResult() {
        return selectCheckResult;
    }

    public RworkerOemInspectionResultDTO setSelectCheckResult(SelectCheckResult selectCheckResult) {
        this.selectCheckResult = selectCheckResult;
        return this;
    }

    @Schema(description = "是否破坏性检测")
    public static class DestroyCheckResult {

        @Schema(description = "不良项目Id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long unqualifiedItemId;

        @Schema(description = "sn")
        private List<String> snList;

        public Long getUnqualifiedItemId() {
            return unqualifiedItemId;
        }

        public DestroyCheckResult setUnqualifiedItemId(Long unqualifiedItemId) {
            this.unqualifiedItemId = unqualifiedItemId;
            return this;
        }

        public List<String> getSnList() {
            return snList;
        }

        public DestroyCheckResult setSnList(List<String> snList) {
            this.snList = snList;
            return this;
        }
    }

    @Schema(description = "挑选结果")
    public static class SelectCheckResult {

        private Integer qualifiedNumber;

        private Integer unqualifiedNumber;

        private List<String> selectSnList;

        public Integer getQualifiedNumber() {
            return qualifiedNumber;
        }

        public SelectCheckResult setQualifiedNumber(Integer qualifiedNumber) {
            this.qualifiedNumber = qualifiedNumber;
            return this;
        }

        public Integer getUnqualifiedNumber() {
            return unqualifiedNumber;
        }

        public SelectCheckResult setUnqualifiedNumber(Integer unqualifiedNumber) {
            this.unqualifiedNumber = unqualifiedNumber;
            return this;
        }

        public List<String> getSelectSnList() {
            return selectSnList;
        }

        public SelectCheckResult setSelectSnList(List<String> selectSnList) {
            this.selectSnList = selectSnList;
            return this;
        }
    }


}
