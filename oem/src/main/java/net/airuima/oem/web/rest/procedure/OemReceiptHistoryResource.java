package net.airuima.oem.web.rest.procedure;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.oem.domain.procedure.OemReceiptHistory;
import net.airuima.oem.dto.OemReceiptHistorySaveDTO;
import net.airuima.oem.service.procedure.OemReceiptHistoryService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "外协收货历史Resource")
@RestController
@RequestMapping("/api/oem-receipt-histories")
@AppKey("OemService")
@AuthorityRegion("外协Oem")
@FuncInterceptor("StepOem")
public class OemReceiptHistoryResource extends ProtectBaseResource<OemReceiptHistory> {

    private final OemReceiptHistoryService oemReceiptHistoryService;

    public OemReceiptHistoryResource(OemReceiptHistoryService oemReceiptHistoryService) {
        this.oemReceiptHistoryService = oemReceiptHistoryService;
        this.mapUri = "/api/oem-receipt-histories";
    }

    /**
     * 工单外协创建收货记录
     *
     * @param oemReceiptHistorySaveDto 收货数据
     */
    @PostMapping("/create")
    public ResponseEntity<ResponseData<Void>> createOemReceiptHistory(@RequestBody OemReceiptHistorySaveDTO oemReceiptHistorySaveDto) {

        try {
            oemReceiptHistoryService.createOemReceiptHistory(oemReceiptHistorySaveDto);
            return ResponseData.ok();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            return ResponseData.error(e);
        }
    }


    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, "外协收货历史");
    }
}
