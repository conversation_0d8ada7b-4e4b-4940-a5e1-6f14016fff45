package net.airuima.oem.web.rest.rworker.quality;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AppKey;
import net.airuima.oem.dto.OemTodoInspectHistoryDTO;
import net.airuima.oem.dto.rworker.RworkerOemInspectionPlanDTO;
import net.airuima.oem.dto.rworker.RworkerOemInspectionResultDTO;
import net.airuima.oem.service.rworker.quality.IOemQualityService;
import net.airuima.rbase.dto.rworker.quality.dto.RworkerQualityInspectionPlanDTO;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "RWorker-Web外协检查相关Resource")
@RestController
@AppKey("OemService")
@RequestMapping("/api/rworker/oem/qualities")
public class RworkerOemQualityResource {

    @Autowired
    private IOemQualityService[] oemQualityServices;

    /**
     * 待做的外协收货检验单
     *
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData < java.util.List < net.airuima.rbase.domain.procedure.quality.IqcCheckHistory>> 来料检验列表
     */
    @Operation(summary = "待做的外协收货检验单")
    @GetMapping("/todo")
    @PreAuthorize("@sc.checkSecurity()")
    public ResponseEntity<ResponseData<List<OemTodoInspectHistoryDTO>>> getTodoHistory() {
        try {
            return ResponseData.ok(oemQualityServices[0].getTodoHistory());
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }


    /**
     * 外协质检获取质检方案
     *
     * @param rworkerOemInspectionPlanDto 外协信息
     */
    @Operation(summary = "获取质检方案")
    @PreAuthorize("@sc.checkSecurity()")
    @PostMapping("/qualityInspectionPlan")
    public ResponseEntity<ResponseData<RworkerQualityInspectionPlanDTO>> qualityInspectionPlan(@RequestBody RworkerOemInspectionPlanDTO rworkerOemInspectionPlanDto) {
        try {
            return ResponseData.ok(oemQualityServices[0].qualityInspectionPlan(rworkerOemInspectionPlanDto));
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 保存外协入库检测记录
     *
     * <AUTHOR>
     */
    @Operation(summary = "保存外协入库检测记录")
    @PreAuthorize("@sc.checkSecurity()")
    @PreventRepeatSubmit
    @PostMapping("/inspection/records")
    public ResponseEntity<ResponseData<Void>> saveFirstInspectionRecord(@Valid @RequestBody RworkerOemInspectionResultDTO rworkerOemInspectionResultDto) {
        try {
            oemQualityServices[0].saveInspectionRecord(rworkerOemInspectionResultDto);
            return ResponseData.ok();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }


}
