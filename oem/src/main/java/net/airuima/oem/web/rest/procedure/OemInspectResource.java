package net.airuima.oem.web.rest.procedure;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.oem.domain.procedure.OemInspect;
import net.airuima.oem.service.procedure.OemInspectService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.web.ProtectBaseResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "外协质检结果Resource")
@RestController
@RequestMapping("/api/oem-inspects")
@AppKey("OemService")
@AuthorityRegion("外协Oem")
@FuncInterceptor("StepOem")
public class OemInspectResource extends ProtectBaseResource<OemInspect> {

    private final OemInspectService oemInspectService;

    public OemInspectResource(OemInspectService oemInspectService) {
        this.oemInspectService = oemInspectService;
        this.mapUri = "/api/oem-inspects";
    }



    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, "外协质检结果");
    }
}
