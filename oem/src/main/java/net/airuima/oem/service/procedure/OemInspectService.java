package net.airuima.oem.service.procedure;

import net.airuima.constant.Constants;
import net.airuima.oem.domain.procedure.OemInspect;
import net.airuima.oem.dto.OemTodoInspectHistoryDTO;
import net.airuima.oem.repository.procedure.OemInspectRepository;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class OemInspectService extends CommonJpaService<OemInspect> {

    private final OemInspectRepository oemInspectRepository;

    public OemInspectService(OemInspectRepository oemInspectRepository) {
        this.oemInspectRepository = oemInspectRepository;
    }

    @Override
    @FetchMethod
    public List<OemInspect> find(Specification<OemInspect> spec) {
        return oemInspectRepository.findAll(spec);
    }

    @Override
    @FetchMethod
    public Page<OemInspect> find(Specification<OemInspect> spec, Pageable pageable) {
        return oemInspectRepository.findAll(spec, pageable);
    }

    @Override
    @FetchMethod
    public Page<OemInspect> findAll(Pageable pageable) {
        return oemInspectRepository.findAll(pageable);
    }


    /**
     * 外协收货待检任务列表
     *
     * @return List<OemTodoInspectHistoryDTO>
     */
    public List<OemTodoInspectHistoryDTO> getTodoHistory() {
        List<OemInspect> oemInspects = oemInspectRepository.findByStatusAndDeletedOrderByIdAsc(Constants.INT_ZERO, Constants.LONG_ZERO);

        if (!ValidateUtils.isValid(oemInspects)) {
            return Collections.emptyList();
        }
        return oemInspects.stream().map(OemTodoInspectHistoryDTO::new).toList();
    }

    /**
     * 更新缓存
     *
     * @param id    主键
     * @param cache 缓存内容
     */
    public void updateCache(Long id,String cache){
        oemInspectRepository.updateCache(id,cache);
    }

}
