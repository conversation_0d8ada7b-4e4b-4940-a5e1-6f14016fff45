package net.airuima.oem.service.procedure;

import net.airuima.constant.Constants;
import net.airuima.oem.domain.procedure.OemInspect;
import net.airuima.oem.domain.procedure.OemInspectDetail;
import net.airuima.oem.dto.OemInspectDetailGetDTO;
import net.airuima.oem.repository.procedure.OemInspectDetailRepository;
import net.airuima.oem.repository.procedure.OemInspectRepository;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import net.airuima.util.ValidateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class OemInspectDetailService extends CommonJpaService<OemInspectDetail> {

    private final OemInspectDetailRepository oemInspectDetailRepository;

    @Autowired
    private OemInspectRepository oemInspectRepository;

    public OemInspectDetailService(OemInspectDetailRepository oemInspectDetailRepository) {
        this.oemInspectDetailRepository = oemInspectDetailRepository;
    }

    @Override
    @FetchMethod
    public Page<OemInspectDetail> find(Specification<OemInspectDetail> spec, Pageable pageable) {
        return oemInspectDetailRepository.findAll(spec, pageable);
    }

    @Override
    @FetchMethod
    public List<OemInspectDetail> find(Specification<OemInspectDetail> spec) {
        return oemInspectDetailRepository.findAll(spec);
    }

    @Override
    @FetchMethod
    public Page<OemInspectDetail> findAll(Pageable pageable) {
        return oemInspectDetailRepository.findAll(pageable);
    }


    /**
     * 获取外协质检结果明细
     *
     * @param oemInspectId 外协质检结果ID
     * @return OemInspectDetailGetDTO
     */
    public OemInspectDetailGetDTO getOemInspectDetail(Long oemInspectId) {
        OemInspectDetailGetDTO oemInspectDetailGetDto = new OemInspectDetailGetDTO();
        OemInspect oemInspect = oemInspectRepository.findByIdAndDeleted(oemInspectId, Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("oemInspectNotFind", "外协质检结果不存在"));
        oemInspectDetailGetDto.setOemInspect(oemInspect);

        //质检详情信息
        List<OemInspectDetail> oemInspectDetails = oemInspectDetailRepository.findByOemInspectIdAndDeleted(oemInspectId, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(oemInspectDetails)) {
            oemInspectDetailGetDto.setOemInspectDetailsInfoList(oemInspectDetails.stream().map(OemInspectDetailGetDTO.OemInspectDetailsInfo::new).toList());
        }
        return oemInspectDetailGetDto;

    }
}
