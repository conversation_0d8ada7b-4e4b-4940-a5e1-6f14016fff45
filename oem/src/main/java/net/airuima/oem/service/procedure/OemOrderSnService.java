package net.airuima.oem.service.procedure;

import net.airuima.constant.Constants;
import net.airuima.oem.domain.procedure.OemOrderSn;
import net.airuima.oem.repository.procedure.OemOrderSnRepository;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import org.apache.commons.compress.utils.Lists;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
@Transactional(rollbackFor = Exception.class)
public class OemOrderSnService extends CommonJpaService<OemOrderSn> {

    private final OemOrderSnRepository oemOrderSnRepository;

    public OemOrderSnService(OemOrderSnRepository oemOrderSnRepository) {
        this.oemOrderSnRepository = oemOrderSnRepository;
    }

    @Override
    @FetchMethod
    public Page<OemOrderSn> find(Specification<OemOrderSn> spec, Pageable pageable) {
        return oemOrderSnRepository.findAll(spec, pageable);
    }

    @Override
    @FetchMethod
    public List<OemOrderSn> find(Specification<OemOrderSn> spec) {
        return oemOrderSnRepository.findAll(spec);
    }

    @Override
    @FetchMethod
    public Page<OemOrderSn> findAll(Pageable pageable) {
        return oemOrderSnRepository.findAll(pageable);
    }

    /**
     * 获取投产工单绑定的 工序外协sn
     *
     * @param workSheetId    工单id
     * @param subWorkSheetId 子工单id
     * @param stepId         工序id
     * @return List<String>
     */
    public List<String> getStepOemOrderBindSn(Long workSheetId, Long subWorkSheetId, Long stepId) {
        List<OemOrderSn> oemOrderSns = Objects.nonNull(subWorkSheetId) ? oemOrderSnRepository.findByOemOrderOriginalSubWorkSheetIdAndOemOrderStepIdAndDeleted(subWorkSheetId, stepId, Constants.LONG_ZERO) :
                oemOrderSnRepository.findByOemOrderOriginalWorkSheetIdAndOemOrderStepIdAndDeleted(workSheetId, stepId, Constants.LONG_ZERO);

        if (ValidateUtils.isValid(oemOrderSns)) {
            return oemOrderSns.stream().map(OemOrderSn::getSn).distinct().toList();
        }
        return Collections.emptyList();
    }

}
