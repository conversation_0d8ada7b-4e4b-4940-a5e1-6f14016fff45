package net.airuima.oem.domain.procedure;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "检测明细")
public class OemInspectDetailCheckItemJson {

    /**
     * 检测项目
     */
    @Schema(description = "检测项目")
    private CheckItemDTO checkItem;

    /**
     * 检测数据值
     */
    @Schema(description = "检测数据值")
    private String checkData;

    /**
     * 合格范围(开闭区间或者OK)
     */
    @Schema(description = "合格范围(开闭区间或者OK)")
    private String qualifiedRange;

    /**
     * 检测结果(0:不合格;1:合格)
     */
    @Schema(description = "检测结果(0:不合格;1:合格)")
    private Boolean result;

    public CheckItemDTO getCheckItem() {
        return checkItem;
    }

    public OemInspectDetailCheckItemJson setCheckItem(CheckItemDTO checkItem) {
        this.checkItem = checkItem;
        return this;
    }

    public String getCheckData() {
        return checkData;
    }

    public OemInspectDetailCheckItemJson setCheckData(String checkData) {
        this.checkData = checkData;
        return this;
    }

    public String getQualifiedRange() {
        return qualifiedRange;
    }

    public OemInspectDetailCheckItemJson setQualifiedRange(String qualifiedRange) {
        this.qualifiedRange = qualifiedRange;
        return this;
    }

    public Boolean getResult() {
        return result;
    }

    public OemInspectDetailCheckItemJson setResult(Boolean result) {
        this.result = result;
        return this;
    }

    @Schema(description = "检测项目")
    public static class CheckItemDTO {

        @Schema(description = "检测项id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 检测项名称
         */
        @Schema(description = "检测项名称")
        private String name;

        /**
         * 检测项编码
         */
        @Schema(description = "检测项编码")
        private String code;

        /**
         * 合格范围(开闭区间或者OK)
         */
        @Schema(description = "合格范围(开闭区间或者OK)")
        private String qualifiedRange;

        public Long getId() {
            return id;
        }

        public CheckItemDTO setId(Long id) {
            this.id = id;
            return this;
        }

        public String getName() {
            return name;
        }

        public CheckItemDTO setName(String name) {
            this.name = name;
            return this;
        }

        public String getQualifiedRange() {
            return qualifiedRange;
        }

        public CheckItemDTO setQualifiedRange(String qualifiedRange) {
            this.qualifiedRange = qualifiedRange;
            return this;
        }

        public String getCode() {
            return code;
        }

        public CheckItemDTO setCode(String code) {
            this.code = code;
            return this;
        }
    }


}
