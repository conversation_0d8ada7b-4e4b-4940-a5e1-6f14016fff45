package net.airuima.oem.domain.procedure;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.dto.UserDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(name = "外协收货历史(OemReceiptHistory)", description = "外协收货历史")
@Entity
@Table(name = "procedure_oem_receipt_history")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
public class OemReceiptHistory extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 外协订单id
     */
    @NotNull
    @ManyToOne
    @Schema(description = "外协订单id", required = true)
    @JoinColumn(name = "oem_order_id", nullable = false)
    private OemOrder oemOrder;

    /**
     * 收货数量
     */
    @Schema(description = "收货数量")
    @Column(name = "quantity")
    private int quantity;

    /**
     * 收货时间
     */
    @Column(name = "receipt_time")
    @Schema(description = "收货时间")
    private LocalDateTime receiptTime;

    /**
     * 操作用户id
     */
    @Schema(description = "操作用户id")
    @Column(name = "operator_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long operatorId;

    /**
     * 操作用户DTO
     */
    @Transient
    @Schema(description = "操作用户DTO", required = true)
    @FetchField(mapUri = "/api/users", serviceId = "mom", paramKey = "operatorId")
    @FetchDataFilter(schema = "mom", tableName = "user", foreignKey = "operator_id")
    private UserDTO operatorDto = new UserDTO();


    public OemOrder getOemOrder() {
        return oemOrder;
    }

    public OemReceiptHistory setOemOrder(OemOrder oemOrder) {
        this.oemOrder = oemOrder;
        return this;
    }

    public int getQuantity() {
        return quantity;
    }

    public OemReceiptHistory setQuantity(int quantity) {
        this.quantity = quantity;
        return this;
    }

    public LocalDateTime getReceiptTime() {
        return receiptTime;
    }

    public OemReceiptHistory setReceiptTime(LocalDateTime receiptTime) {
        this.receiptTime = receiptTime;
        return this;
    }

    public long getOperatorId() {
        return operatorId;
    }

    public OemReceiptHistory setOperatorId(long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public UserDTO getOperatorDto() {
        return operatorDto;
    }

    public OemReceiptHistory setOperatorDto(UserDTO operatorDto) {
        this.operatorDto = operatorDto;
        return this;
    }
}
