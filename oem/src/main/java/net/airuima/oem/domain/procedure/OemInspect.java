package net.airuima.oem.domain.procedure;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.dto.UserDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.dto.organization.StaffDTO;
import org.hibernate.annotations.*;
import org.hibernate.annotations.Cache;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Schema(name = "外协质检结果(OemInspect)", description = "外协质检结果")
@Entity
@Table(name = "procedure_oem_inspect")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
public class OemInspect extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 检验单号，必须唯一
     */
    @Column(name = "serial_number", nullable = false)
    @Schema(description = "检验单号，必须唯一")
    private String serialNumber;

    /**
     * 状态(0:待检验;1:已质检)
     */
    @Column(name = "status", nullable = false)
    @Schema(description = "状态(0:待检验;1:已质检)")
    private Integer status;

    /**
     * 质检方案
     */
    @Schema(description = "质检方案")
    @ManyToOne
    @JoinColumn(name = "check_rule_id")
    private PedigreeStepCheckRule pedigreeStepCheckRule;

    /**
     * 外协收货历史id
     */
    @NotNull
    @ManyToOne
    @Schema(description = "外协收货历史id", required = true)
    @JoinColumn(name = "oem_receipt_history_id", nullable = false)
    private OemReceiptHistory oemReceiptHistory;

    /**
     * 来料数量
     */
    @Column(name = "number")
    @Schema(description = "来料数量")
    private int number;

    /**
     * 合格数
     */
    @Schema(description = "合格数", requiredMode = Schema.RequiredMode.REQUIRED, type = "integer", format = "int32", defaultValue = "0", minimum = "0")
    @Column(name = "qualified_number", nullable = false)
    private int qualifiedNumber;

    /**
     * 不合格数
     */
    @Schema(description = "不合格数", type = "integer", format = "int32", defaultValue = "0", minimum = "0")
    @Column(name = "unqualified_number", nullable = false)
    private int unqualifiedNumber;

    /**
     * 来料日期
     */
    @Column(name = "arrival_time")
    @Schema(description = "来料日期")
    private LocalDateTime arrivalTime;


    /**
     * 质检时间
     */
    @Column(name = "inspect_time")
    @Schema(description = "质检时间")
    private LocalDateTime inspectTime;

    /**
     * 检验结果(0:不合格;1:合格)
     */
    @Column(name = "result")
    @Schema(description = "检验结果(0:不合格;1:合格)")
    private Boolean result;

    /**
     * 质检工位
     */
    @ManyToOne
    @JoinColumn(name = "work_cell_id")
    @Schema(description = "质检工位")
    private WorkCell workCell;

    /**
     * 检验员id
     */
    @Column(name = "operator_id")
    @Schema(description = "检验员id")
    private Long operatorId;

    /**
     *检验员DTO
     */
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "operatorId")
    @FetchDataFilter(schema = "mom", tableName = "staff", foreignKey = "operator_id")
    @Transient
    private StaffDTO operatorDto = new StaffDTO();


    /**
     * 处理方式(0-待处理;1-合格接收;2-让步接收;3-判退;4-挑选;5-MRB，6 返工，)
     */
    @Column(name = "deal_way")
    @Schema(description = "处理方式(0-待处理;1-合格接收;2-让步接收;3-判退;4-挑选;5-MRB；6,返工，7.报废)")
    private Integer dealWay;

    /**
     * 缓存具体内容
     */
    @Schema(description = "缓存具体内容")
    @Column(name = "cache")
    private String cache;

    /**
     * 是否破坏性检测 0.否，1是
     */
    @Schema(description = "是否破坏性检测")
    @Column(name = "destroy_check")
    private Boolean destroyCheck;

    /**
     * 不良项目
     */
    @ManyToOne
    @Schema(name = "破坏性检测不良项目")
    @JoinColumn(name = "destroy_item_id")
    private UnqualifiedItem destroyItem;


    /**
     * 挑选不良sn
     */
    @Schema(description = "挑选不良sn")
    @Type(JsonType.class)
    @Column(name = "select_sn_list")
    private List<String> selectSnList;

    public List<String> getSelectSnList() {
        return selectSnList;
    }

    public OemInspect setSelectSnList(List<String> selectSnList) {
        this.selectSnList = selectSnList;
        return this;
    }

    public UnqualifiedItem getDestroyItem() {
        return destroyItem;
    }

    public OemInspect setDestroyItem(UnqualifiedItem destroyItem) {
        this.destroyItem = destroyItem;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public OemInspect setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public OemInspect setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public PedigreeStepCheckRule getPedigreeStepCheckRule() {
        return pedigreeStepCheckRule;
    }

    public OemInspect setPedigreeStepCheckRule(PedigreeStepCheckRule pedigreeStepCheckRule) {
        this.pedigreeStepCheckRule = pedigreeStepCheckRule;
        return this;
    }

    public OemReceiptHistory getOemReceiptHistory() {
        return oemReceiptHistory;
    }

    public OemInspect setOemReceiptHistory(OemReceiptHistory oemReceiptHistory) {
        this.oemReceiptHistory = oemReceiptHistory;
        return this;
    }

    public int getQualifiedNumber() {
        return qualifiedNumber;
    }

    public OemInspect setQualifiedNumber(int qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public OemInspect setNumber(int number) {
        this.number = number;
        return this;
    }

    public int getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public OemInspect setUnqualifiedNumber(int unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public LocalDateTime getArrivalTime() {
        return arrivalTime;
    }

    public OemInspect setArrivalTime(LocalDateTime arrivalTime) {
        this.arrivalTime = arrivalTime;
        return this;
    }

    public LocalDateTime getInspectTime() {
        return inspectTime;
    }

    public OemInspect setInspectTime(LocalDateTime inspectTime) {
        this.inspectTime = inspectTime;
        return this;
    }

    public Boolean getResult() {
        return result;
    }

    public OemInspect setResult(Boolean result) {
        this.result = result;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public OemInspect setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public OemInspect setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public StaffDTO getOperatorDto() {
        return operatorDto;
    }

    public OemInspect setOperatorDto(StaffDTO operatorDto) {
        this.operatorDto = operatorDto;
        return this;
    }

    public Integer getDealWay() {
        return dealWay;
    }

    public OemInspect setDealWay(Integer dealWay) {
        this.dealWay = dealWay;
        return this;
    }

    public String getCache() {
        return cache;
    }

    public OemInspect setCache(String cache) {
        this.cache = cache;
        return this;
    }

    public Boolean getDestroyCheck() {
        return destroyCheck;
    }

    public OemInspect setDestroyCheck(Boolean destroyCheck) {
        this.destroyCheck = destroyCheck;
        return this;
    }
}
