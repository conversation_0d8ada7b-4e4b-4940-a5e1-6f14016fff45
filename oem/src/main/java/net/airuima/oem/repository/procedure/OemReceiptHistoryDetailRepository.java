package net.airuima.oem.repository.procedure;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.oem.domain.procedure.OemReceiptHistoryDetail;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface OemReceiptHistoryDetailRepository extends LogicDeleteableRepository<OemReceiptHistoryDetail>,
        EntityGraphJpaSpecificationExecutor<OemReceiptHistoryDetail>, EntityGraphJpaRepository<OemReceiptHistoryDetail, Long> {

    /**
     * 通过外协收货历史id 获取 收货sn列表
     *
     * @param oemReceiptHistoryId 外协收货历史id
     * @param deleted             逻辑删除
     * @return 收货sn列表
     */
    @Query("""
                    select ord.sn from OemReceiptHistoryDetail ord where ord.oemReceiptHistory.id = ?1 and ord.deleted = ?2
            """)
    List<String> findByOemReceiptHistoryIdAndDeleted(Long oemReceiptHistoryId, Long deleted);

    /**
     * 通过外协收货历史id 获取 收货sn列表
     *
     * @param oemReceiptHistoryIds 外协收货历史id
     * @param deleted             逻辑删除
     * @return 收货sn列表
     */
    @Query("""
                    select ord.sn from OemReceiptHistoryDetail ord where ord.oemReceiptHistory.id in ?1 and ord.deleted = ?2
            """)
    List<String> findByOemReceiptHistoryIdInAndDeleted(List<Long> oemReceiptHistoryIds, Long deleted);


    /**
     * 通过外协收货历史id 和 sn 获取 收货sn列表
     *
     * @param oemReceiptHistoryId 外协收货历史id
     * @param sn                  sn
     * @param deleted             逻辑删除
     * @return 收货sn列表
     */
    @FetchMethod
    Optional<OemReceiptHistoryDetail> findByOemReceiptHistoryIdAndSnAndDeleted(Long oemReceiptHistoryId, String sn, Long deleted);
}
