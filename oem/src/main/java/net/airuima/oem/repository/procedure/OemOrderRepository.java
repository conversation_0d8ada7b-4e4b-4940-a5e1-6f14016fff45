package net.airuima.oem.repository.procedure;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.oem.domain.procedure.OemOrder;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface OemOrderRepository extends LogicDeleteableRepository<OemOrder>,
        EntityGraphJpaSpecificationExecutor<OemOrder>, EntityGraphJpaRepository<OemOrder, Long> {

    /**
     * 子工单工序外协 获取已外协数量
     *
     * @param stepId                 工序外协
     * @param originalSubWorkSheetId 外协子工单id
     * @param deleted                逻辑删除
     * @return 外协数量
     */
    @Query("""
                    select sum(oo.workSheet.number) from OemOrder oo where oo.step.id = ?1 and oo.originalSubWorkSheet.id = ?2 and oo.deleted = ?3
            """)
    Integer sumOemOrderWorkSheetNumberByStepIdAndOriginalSubWorkSheetIdAndDeleted(Long stepId, Long originalSubWorkSheetId, Long deleted);


    /**
     * 工单工序外协 获取已外协数量
     *
     * @param stepId              工序外协
     * @param originalWorkSheetId 外协工单id
     * @param deleted             逻辑删除
     * @return 外协数量
     */
    @Query("""
                    select sum(oo.workSheet.number) from OemOrder oo where oo.step.id = ?1 and oo.originalWorkSheet.id = ?2 and oo.deleted = ?3
            """)
    Integer sumOemOrderWorkSheetNumberByStepIdAndOriginalWorkSheetIdAndDeleted(Long stepId, Long originalWorkSheetId, Long deleted);


    /**
     * 通过外协工单id 获取外协订单
     *
     * @param id      外协工单id
     * @param deleted 逻辑删除
     * @return List<OemOrder>
     */
    @FetchMethod
    Optional<OemOrder> findByIdAndDeleted(Long id, Long deleted);
}
