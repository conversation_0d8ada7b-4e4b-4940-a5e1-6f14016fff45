package net.airuima.oem.repository.procedure;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.oem.domain.procedure.OemInspectWarehouse;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OemInspectWarehouseRepository extends LogicDeleteableRepository<OemInspectWarehouse>,
        EntityGraphJpaSpecificationExecutor<OemInspectWarehouse>, EntityGraphJpaRepository<OemInspectWarehouse, Long> {

    /**
     * 通过外协工单id和sn查询质检记录
     *
     * @param oemOrderId 外协工单id
     * @param snList     sn列表
     * @param deleted    逻辑删除
     * @return List<OemInspectWarehouse>
     */
    List<OemInspectWarehouse> findByOemOrderIdAndSnInAndDeleted(Long oemOrderId, List<String> snList, Long deleted);

    /**
     * 通过外协工单id和sn查询质检记录
     *
     * @param oemOrderId 外协工单id
     * @param sn         sn
     * @param deleted    逻辑删除
     * @return List<OemInspectWarehouse>
     */
    List<OemInspectWarehouse> findByOemOrderIdAndSnAndDeleted(Long oemOrderId, String sn, Long deleted);

    /**
     * 获取外协工单已入库的sn列表
     *
     * @param oemOrderId 外协工单id
     * @param deleted    逻辑删除
     * @return List<String>
     */
    @Query("""
                   select  o.sn FROM OemInspectWarehouse o WHERE o.oemOrder.id = ?1 AND o.deleted = ?2
            """)
    List<String> findOemOrderIdAndDeletedBySns(Long oemOrderId, Long deleted);

    /**
     * 获取外协工单已入库的sn列表
     *
     * @param wsId    源工单id
     * @param stepId  源工序id
     * @param deleted 逻辑删除
     * @return 已入库的sn列表
     */
    @Query("""
                    select o.sn from OemInspectWarehouse o where o.oemOrder.originalWorkSheet.id =?1 and o.oemOrder.step.id =?2 and o.deleted =?3
            """)
    List<String> findOemOrderOriginalWorkSheetIdAndOemOrderStepIdAndDeletedBySns(Long wsId, Long stepId, Long deleted);

    /**
     * 获取外协工单已入库的sn列表
     *
     * @param subWsId 源子工单id
     * @param stepId  源工序id
     * @param deleted 逻辑删除
     * @return 已入库的sn列表
     */
    @Query("""
               select o.sn from OemInspectWarehouse o where o.oemOrder.originalSubWorkSheet.id =?1 and o.oemOrder.step.id =?2 and o.deleted =?3
            """)
    List<String> findOemOrderOriginalSubWorkSheetIdAndOemOrderStepIdAndDeletedBySns(Long subWsId, Long stepId, Long deleted);
}
