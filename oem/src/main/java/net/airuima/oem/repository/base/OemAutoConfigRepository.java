package net.airuima.oem.repository.base;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.oem.domain.base.OemAutoConfig;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OemAutoConfigRepository extends LogicDeleteableRepository<OemAutoConfig>,
        EntityGraphJpaSpecificationExecutor<OemAutoConfig>, EntityGraphJpaRepository<OemAutoConfig, Long> {

    /**
     * 获取工序外协分单配置
     *
     * @param pedigreeIds 产品谱系列表
     * @param deleted     逻辑删除
     * @param isEnable    是否启用
     * @return List<OemAutoConfig>
     */
    List<OemAutoConfig> findByPedigreeIdInAndDeletedAndIsEnable(List<Long> pedigreeIds, Long deleted, Boolean isEnable);

}
