<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="202406201607-001" author="simon">
        <addColumn tableName="base_work_cell_check_start_rule">
            <column name="attribute_id" type="bigint(20)" remarks="物料属性id"/>
        </addColumn>
        <addColumn tableName="base_work_cell_check_start_rule">
            <column name="material_id" type="bigint(20)" remarks="物料id"/>
        </addColumn>
        <addColumn tableName="base_work_cell_check_start_rule">
            <column name="supplier_id" type="bigint(20)" remarks="供应商id"/>
        </addColumn>
        <addColumn tableName="base_work_cell_check_start_rule">
            <column name="client_id" type="bigint(20)" remarks="客户id"/>
        </addColumn>
    </changeSet>

    <changeSet id="202505151916-001" author="YangS">
        <addColumn tableName="base_work_cell_check_start_rule">
            <column defaultValueNumeric="0" name="extend_type" type="tinyint(2)" remarks="宽放类型(0:按计划宽放,1:按实际执行宽放)"/>
        </addColumn>

        <addColumn tableName="procedure_latest_check_result">
            <column defaultValueNumeric="0" name="extend_time" type="double" remarks="宽放类型(0:按计划宽放,1:按实际执行宽放)"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>
