<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <include file="config/liquibase/rbase/changelog/init_base_table_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/rbase/changelog/add_procedure_ws_sn_table.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/rbase/changelog/add_ysn_to_sn_work_detail_and_status.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/rbase/changelog/add_custom_index_for_base_table.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/rbase/changelog/add_step_interval_event_table.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/rbase/changelog/alert_base_pedigree_step_interval_config_update_column.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/rbase/changelog/alert_base_work_cell_check_start_rule_iqc_table_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/rbase/changelog/alert_base_pedigree_step_check_rule_iqc_table_changelog.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/rbase/changelog/add_procedure_iqc_check_history_table.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/rbase/changelog/add_procedure_iqc_check_history_detail_table.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/alert_base_pedigree_step_check_item_changelog.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/add_cascade_work_sheet_table_changelog.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/add_step_reinspect_table_changelog.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/add_auto_record_column_for_wearing_part_changelog.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/add_serial_number_for_inspect.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/rbase/changelog/add_wearing_part_table.xml" relativeToChangelogFile="false"/>
</databaseChangeLog>
