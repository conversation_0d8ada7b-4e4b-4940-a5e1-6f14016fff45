package net.airuima.rbase.constant;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/5/6
 */
public enum DetectionModeEnum {
    PENDING(0, "待处理"),
    PASSED(1, "通过"),
    RECHECK(2, "重检"),
    RELEASED(3, "放行"),
    BATCH_REJECTED(4, "批退"),
    MRB(5, "MRB");

    private final int code;
    private final String name;

    DetectionModeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static DetectionModeEnum fromCode(int code) {
        for (DetectionModeEnum mode : DetectionModeEnum.values()) {
            if (mode.getCode() == code) {
                return mode;
            }
        }
        throw new IllegalArgumentException("Invalid DetectionMode code: " + code);
    }
}
