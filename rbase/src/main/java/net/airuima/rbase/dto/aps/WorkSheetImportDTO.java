package net.airuima.rbase.dto.aps;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "工单批量导入创建")
public class WorkSheetImportDTO implements Serializable {

    /**
     * 优先级
     */
    @Schema(description = "优先级")
    @Excel(name = "优先级",replace = {"普通_0","加急_1"})
    private Integer priority;

    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    @Excel(name = "部门名称")
    private String organizationName;

    /**
     * 部门编码
     */
    @Schema(description = "部门编码")
    @Excel(name = "部门编码")
    private String organizationCode;

    /**
     * 工单号
     */
    @Schema(description = "工单号")
    @Excel(name = "工单号")
    private String serialNumber;

    /**
     * 工单类型
     */
    @Schema(description = "工单类型")
    @Excel(name = "工单类型",replace = {"正常单_1","返修单_0"})
    private Integer category;

    /**
     * 投产数
     */
    @Schema(description = "投产数")
    @Excel(name = "投产数")
    private Integer number;

    /**
     * 客户编码
     */
    @Schema(description = "客户编码")
    @Excel(name = "客户编码")
    private String clientCode;

    /**
     * 产品谱系名称
     */
    @Schema(description = "产品谱系名称")
    @Excel(name = "产品谱系名称")
    private String pedigreeName;

    /**
     * 产品谱系编码
     */
    @Schema(description = "产品谱系编码")
    @Excel(name = "产品谱系编码")
    private String pedigreeCode;

    /**
     * 物料清单编码
     */
    @Schema(description = "物料清单编码")
    @Excel(name = "物料清单编码")
    private String bomInfoCode;


    /**
     * 工艺路线名称
     */
    @Schema(description = "工艺路线名称")
    @Excel(name = "工艺路线名称")
    private String workFlowName;

    /**
     * 工艺路线编码
     */
    @Schema(description = "工艺路线编码")
    @Excel(name = "工艺路线编码")
    private String workFlowCode;

    /**
     * 生产线名称
     */
    @Schema(description = "生产线名称")
    @Excel(name = "生产线名称")
    private String workLineName;

    /**
     * 生产线编码
     */
    @Schema(description = "生产线编码")
    @Excel(name = "生产线编码")
    private String workLineCode;

    /**
     * 计划开工日期
     */
    @Schema(description = "计划开工日期", example = "2022-05-23 23:45:33")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @Excel(name = "计划开工日期",format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planStartDate;


    /**
     * 计划完工日期
     */
    @Schema(description = "计划完工日期", example = "2022-05-23 23:45:33")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @Excel(name = "计划完工日期",format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planEndDate;

    /**
     * 交付日期
     */
    @Schema(description = "交付日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @Excel(name = "交付日期",format = "yyyy-MM-dd")
    private LocalDate deliveryDate;

    /**
     * 交付日期
     */
    @Excel(name = "错误原因")
    private String message;


    public Integer getPriority() {
        return priority;
    }

    public WorkSheetImportDTO setPriority(Integer priority) {
        this.priority = priority;
        return this;
    }

    public String getOrganizationCode() {
        return organizationCode;
    }

    public WorkSheetImportDTO setOrganizationCode(String organizationCode) {
        this.organizationCode = organizationCode;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public WorkSheetImportDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public WorkSheetImportDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public String getClientCode() {
        return clientCode;
    }

    public WorkSheetImportDTO setClientCode(String clientCode) {
        this.clientCode = clientCode;
        return this;
    }

    public String getPedigreeCode() {
        return pedigreeCode;
    }

    public WorkSheetImportDTO setPedigreeCode(String pedigreeCode) {
        this.pedigreeCode = pedigreeCode;
        return this;
    }

    public String getWorkFlowCode() {
        return workFlowCode;
    }

    public WorkSheetImportDTO setWorkFlowCode(String workFlowCode) {
        this.workFlowCode = workFlowCode;
        return this;
    }

    public String getWorkLineCode() {
        return workLineCode;
    }

    public WorkSheetImportDTO setWorkLineCode(String workLineCode) {
        this.workLineCode = workLineCode;
        return this;
    }

    public LocalDateTime getPlanStartDate() {
        return planStartDate;
    }

    public WorkSheetImportDTO setPlanStartDate(LocalDateTime planStartDate) {
        this.planStartDate = planStartDate;
        return this;
    }

    public LocalDateTime getPlanEndDate() {
        return planEndDate;
    }

    public WorkSheetImportDTO setPlanEndDate(LocalDateTime planEndDate) {
        this.planEndDate = planEndDate;
        return this;
    }

    public LocalDate getDeliveryDate() {
        return deliveryDate;
    }

    public WorkSheetImportDTO setDeliveryDate(LocalDate deliveryDate) {
        this.deliveryDate = deliveryDate;
        return this;
    }

    public String getMessage() {
        return message;
    }

    public WorkSheetImportDTO setMessage(String message) {
        this.message = message;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public WorkSheetImportDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public String getBomInfoCode() {
        return bomInfoCode;
    }

    public WorkSheetImportDTO setBomInfoCode(String bomInfoCode) {
        this.bomInfoCode = bomInfoCode;
        return this;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public WorkSheetImportDTO setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
        return this;
    }

    public String getPedigreeName() {
        return pedigreeName;
    }

    public WorkSheetImportDTO setPedigreeName(String pedigreeName) {
        this.pedigreeName = pedigreeName;
        return this;
    }

    public String getWorkFlowName() {
        return workFlowName;
    }

    public WorkSheetImportDTO setWorkFlowName(String workFlowName) {
        this.workFlowName = workFlowName;
        return this;
    }

    public String getWorkLineName() {
        return workLineName;
    }

    public WorkSheetImportDTO setWorkLineName(String workLineName) {
        this.workLineName = workLineName;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        WorkSheetImportDTO that = (WorkSheetImportDTO) o;
        return Objects.equals(serialNumber, that.serialNumber);
    }

    @Override
    public int hashCode() {
        return Objects.hash(serialNumber);
    }
}
