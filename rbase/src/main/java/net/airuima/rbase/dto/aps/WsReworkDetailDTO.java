package net.airuima.rbase.dto.aps;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023-01-12
 */
@Schema(description = "返工单对应生成该返工单的不良组别明细")
public class WsReworkDetailDTO {
    /**
     * 工单号
     */
    @Schema(description = "原始工单号")
    private String originalSerialNumber;

    /**
     * 原始子工单列表
     */
    @Schema(description = "原始子工单号列表")
    private String orgSubWorkSheetList;

    /**
     * 不良产生对应工序名称
     */
    @Schema(description = "产生不良的工序名称")
    private String stepName;

    /**
     * 不良产生对应工序编码
     */
    @Schema(description = "产生不良的工序编码")
    private String stepCode;

    /**
     * 不良项目组别名称
     */
    @Schema(description = "不良项目组别名称")
    private String unqualifiedGroupName;

    /**
     * 不良项目组别编码
     */
    @Schema(description = "不良项目组别编码")
    private String unqualifiedGroupCode;

    public String getOriginalSerialNumber() {
        return originalSerialNumber;
    }

    public WsReworkDetailDTO setOriginalSerialNumber(String originalSerialNumber) {
        this.originalSerialNumber = originalSerialNumber;
        return this;
    }

    public String getOrgSubWorkSheetList() {
        return orgSubWorkSheetList;
    }

    public WsReworkDetailDTO setOrgSubWorkSheetList(String orgSubWorkSheetList) {
        this.orgSubWorkSheetList = orgSubWorkSheetList;
        return this;
    }

    public String getStepName() {
        return stepName;
    }

    public WsReworkDetailDTO setStepName(String stepName) {
        this.stepName = stepName;
        return this;
    }

    public String getStepCode() {
        return stepCode;
    }

    public WsReworkDetailDTO setStepCode(String stepCode) {
        this.stepCode = stepCode;
        return this;
    }

    public String getUnqualifiedGroupName() {
        return unqualifiedGroupName;
    }

    public WsReworkDetailDTO setUnqualifiedGroupName(String unqualifiedGroupName) {
        this.unqualifiedGroupName = unqualifiedGroupName;
        return this;
    }

    public String getUnqualifiedGroupCode() {
        return unqualifiedGroupCode;
    }

    public WsReworkDetailDTO setUnqualifiedGroupCode(String unqualifiedGroupCode) {
        this.unqualifiedGroupCode = unqualifiedGroupCode;
        return this;
    }
}
