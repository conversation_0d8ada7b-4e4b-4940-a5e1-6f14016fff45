package net.airuima.rbase.dto.client;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-06-13
 */
@Schema(description = "Rwork保存提前预警参数信息")
public class ClientSaveUnqualifiedEventDTO {
    /**
     * 当前子工单id
     */
    @Schema(description = "投产子工单ID")
    private Long subWsId;

    /**
     * 当前工序的id
     */
    @Schema(description = "投产子工序ID")
    private Long stepId;

    /**
     * 当前操作人id
     */
    @Schema(description = "操作人ID")
    private Long operatorId;

    /**
     * 员工工位id
     */
    @Schema(description = "工位ID")
    private Long workCellId;

    /**
     * 不良项目ID
     */
    @Schema(description = "不良项目ID")
    private Long unqualifiedItemId;

    /**
     * 预警原因(0:不良超标;1:合格率不达标)
     */
    @Schema(description = "0:不良超标;1:合格率不达标")
    private Integer reasonType;

    /**
     * 不良占有率
     */
    @Schema(description = "不良占有率")
    private Double unqualifiedRate;

    public Long getSubWsId() {
        return subWsId;
    }

    public ClientSaveUnqualifiedEventDTO setSubWsId(Long subWsId) {
        this.subWsId = subWsId;
        return this;
    }

    public Long getStepId() {
        return stepId;
    }

    public ClientSaveUnqualifiedEventDTO setStepId(Long stepId) {
        this.stepId = stepId;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public ClientSaveUnqualifiedEventDTO setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public Long getWorkCellId() {
        return workCellId;
    }

    public ClientSaveUnqualifiedEventDTO setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
        return this;
    }

    public Long getUnqualifiedItemId() {
        return unqualifiedItemId;
    }

    public ClientSaveUnqualifiedEventDTO setUnqualifiedItemId(Long unqualifiedItemId) {
        this.unqualifiedItemId = unqualifiedItemId;
        return this;
    }

    public Integer getReasonType() {
        return reasonType;
    }

    public ClientSaveUnqualifiedEventDTO setReasonType(Integer reasonType) {
        this.reasonType = reasonType;
        return this;
    }

    public Double getUnqualifiedRate() {
        return unqualifiedRate;
    }

    public ClientSaveUnqualifiedEventDTO setUnqualifiedRate(Double unqualifiedRate) {
        this.unqualifiedRate = unqualifiedRate;
        return this;
    }
}
