package net.airuima.rbase.dto.calendar;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalTime;

@Schema( description = "每日班次信息")
public class EveryDayShiftDTO implements Serializable {

    /**
     * 班次ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "班次ID")
    private Long id;

    /**
     * 班次名称
     */
    @Schema(description = "班次名称")
    private String name;

    /**
     * 计划开始时间
     */
    @Schema(description = "计划开始时间")
    private LocalTime planStartTime;

    /**
     * 计划结束时间
     */
    @Schema(description = "计划结束时间")
    private LocalTime planEndTime;

    /**
     * 实际开始时间
     */
    @Schema(description = "实际开始时间")
    private LocalTime actualStartTime;

    /**
     * 实际结束时间
     */
    @Schema(description = "实际结束时间")
    private LocalTime actualEndTime;

    /**
     * 是否启用(true:启用;false:禁用)
     */
    @Schema(description = "是否启用(true:启用;false:禁用)")
    private Boolean enable;

    public Long getId() {
        return id;
    }

    public EveryDayShiftDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getName() {
        return name;
    }

    public EveryDayShiftDTO setName(String name) {
        this.name = name;
        return this;
    }

    public LocalTime getPlanStartTime() {
        return planStartTime;
    }

    public EveryDayShiftDTO setPlanStartTime(LocalTime planStartTime) {
        this.planStartTime = planStartTime;
        return this;
    }

    public LocalTime getPlanEndTime() {
        return planEndTime;
    }

    public EveryDayShiftDTO setPlanEndTime(LocalTime planEndTime) {
        this.planEndTime = planEndTime;
        return this;
    }

    public LocalTime getActualStartTime() {
        return actualStartTime;
    }

    public EveryDayShiftDTO setActualStartTime(LocalTime actualStartTime) {
        this.actualStartTime = actualStartTime;
        return this;
    }

    public LocalTime getActualEndTime() {
        return actualEndTime;
    }

    public EveryDayShiftDTO setActualEndTime(LocalTime actualEndTime) {
        this.actualEndTime = actualEndTime;
        return this;
    }

    public Boolean getEnable() {
        return enable;
    }

    public EveryDayShiftDTO setEnable(Boolean enable) {
        this.enable = enable;
        return this;
    }

    public EveryDayShiftDTO() {
    }

    public EveryDayShiftDTO(Long id, String name, LocalTime planStartTime, LocalTime planEndTime, LocalTime actualStartTime, LocalTime actualEndTime, Boolean enable) {
        this.id = id;
        this.name = name;
        this.planStartTime = planStartTime;
        this.planEndTime = planEndTime;
        this.actualStartTime = actualStartTime;
        this.actualEndTime = actualEndTime;
        this.enable = enable;
    }
}
