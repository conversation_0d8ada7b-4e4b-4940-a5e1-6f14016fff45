package net.airuima.rbase.dto.report;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.util.NumberUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-03-24
 */
@Schema(description = "工单进度DTO")
public class WorkSheetProgressReportDTO implements Serializable {
    /**
     * 工单ID
     */
    @Schema(description = "工单ID")
    private Long workSheetId;

    /**
     * 子工单ID
     */
    @Schema(description = "子工单ID")
    private Long subWorkSheetId;

    /**
     * 工单号
     */
    @Schema(description = "工单号/子工单号")
    private String serialNumber;

    /**
     * 工单进度
     */
    @Schema(description = "工单进度/子工单进度")
    private BigDecimal progress;

    /**
     * 工单状态(0:已下单;1:投产中;2:已暂停;3:已完成;4:正常结单;5:异常结单)
     */
    @Schema(description = "工单状态(-2:已取消;-1:审批中;0:已下单;1:投产中;2:已暂停;3:已完成;4:正常结单;5:异常结单)")
    private Integer status;

    /**
     * 编码
     */
    @Schema(description = "编码")
    private String code;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 规格型号
     */
    @Schema(description = "规格型号")
    private String specification;

    /**
     * 投产数
     */
    @Schema(description = "投产数")
    private Integer inputNumber;

    /**
     * 工序实际完成数量
     */
    @Schema(description = "完成数", required = true)
    private Integer finishNumber;

    /**
     * 工序集合(前置,当前,后置)
     */
    @Schema(description = "工序集合前置,当前,后置", required = true)
    private Map<String, String> stepMap;

    /**
     * 是否逾期
     */
    @Schema(description = "是否逾期")
    private Boolean overdue;

    public WorkSheetProgressReportDTO() {
    }

    public WorkSheetProgressReportDTO(Long workSheetId, String serialNumber, Integer status, String code, String name, String specification, Integer inputNumber, Integer finishNumber, LocalDateTime planEndDate) {
        this.workSheetId = workSheetId;
        this.serialNumber = serialNumber;
        this.status = status;
        this.code = code;
        this.name = name;
        this.specification = specification;
        this.inputNumber = inputNumber;
        this.finishNumber = finishNumber;
        this.overdue = planEndDate == null || planEndDate.isBefore(LocalDateTime.now());
    }

    public WorkSheetProgressReportDTO(Long workSheetId, Long subWorkSheetId, String serialNumber, Integer status, String code, String name, String specification, Integer inputNumber, Integer finishNumber, LocalDateTime planEndDate) {
        this.workSheetId = workSheetId;
        this.subWorkSheetId = subWorkSheetId;
        this.serialNumber = serialNumber;
        this.status = status;
        this.code = code;
        this.name = name;
        this.specification = specification;
        this.inputNumber = inputNumber;
        this.finishNumber = finishNumber;
        this.overdue = planEndDate == null || planEndDate.isBefore(LocalDateTime.now());
    }

    public Long getSubWorkSheetId() {
        return subWorkSheetId;
    }

    public void setSubWorkSheetId(Long subWorkSheetId) {
        this.subWorkSheetId = subWorkSheetId;
    }

    public Long getWorkSheetId() {
        return workSheetId;
    }

    public void setWorkSheetId(Long workSheetId) {
        this.workSheetId = workSheetId;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public BigDecimal getProgress() {
        return NumberUtils.divide(this.finishNumber, this.inputNumber, Constants.INT_FOUR);
    }

    public void setProgress(BigDecimal progress) {
        this.progress = progress;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public Integer getInputNumber() {
        return inputNumber;
    }

    public void setInputNumber(Integer inputNumber) {
        this.inputNumber = inputNumber;
    }

    public Integer getFinishNumber() {
        return finishNumber;
    }

    public void setFinishNumber(Integer finishNumber) {
        this.finishNumber = finishNumber;
    }

    public Map<String, String> getStepMap() {
        return stepMap;
    }

    public void setStepMap(Map<String, String> stepMap) {
        this.stepMap = stepMap;
    }

    public Boolean getOverdue() {
        return overdue;
    }

    public WorkSheetProgressReportDTO setOverdue(Boolean overdue) {
        this.overdue = overdue;
        return this;
    }
}
