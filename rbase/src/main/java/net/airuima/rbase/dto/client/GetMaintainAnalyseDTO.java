package net.airuima.rbase.dto.client;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/9/30
 */
@Schema(description = "RWorker请求维修分析参数DTO")
public class GetMaintainAnalyseDTO {

    /**
     * 维修分析类型：0：sn单支,1:容器
     * @return
     */
    @Schema(description = "维修分析类型：0：sn单支,1:容器")
    private Integer maintainType;

    /**
     * 维修sn
     */
    @Schema(description = "维修sn")
    private String sn;

    /**
     * 容器号
     */
    @Schema(description = "容器号")
    private String containerCode;

    public Integer getMaintainType() {
        return maintainType;
    }

    public GetMaintainAnalyseDTO setMaintainType(Integer maintainType) {
        this.maintainType = maintainType;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public GetMaintainAnalyseDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public GetMaintainAnalyseDTO setContainerCode(String containerCode) {
        this.containerCode = containerCode;
        return this;
    }
}
