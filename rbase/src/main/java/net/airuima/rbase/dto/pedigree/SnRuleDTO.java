package net.airuima.rbase.dto.pedigree;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 序列号规则DTO
 *
 * <AUTHOR>
 * @date 2022/11/11
 */
@Schema(description = "序列号规则DTO")
public class SnRuleDTO implements Serializable {

    /**
     * 正则分类
     * 0 以...开头(^)
     * 1 以...结尾($)
     * 2 长度限制(^.{n}$)
     * 3 包含某个或某段字符(abc)
     * 4 从第几位开始匹配字符(^.{n}[abc])
     * 5 全是数字([0-9]+)
     * 6 全是大写英文([A-Z]+)
     * 7 全是小写英文([a-z]+)
     * 8 英文或数字([0-9a-zA-Z]+)
     * 9 手动输入正则表达式
     */
    @Schema(description = "正则分类")
    private String category;

    /**
     * 正则
     */
    @Schema(description = "正则")
    private String regular;

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getRegular() {
        return regular;
    }

    public void setRegular(String regular) {
        this.regular = regular;
    }

}
