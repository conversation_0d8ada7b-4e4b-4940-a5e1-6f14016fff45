package net.airuima.rbase.dto.client;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.domain.base.pedigree.PedigreeConfig;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/7/4
 */
@Schema(description = "产品谱系配置")
public class ClientPedigreeConfigDTO extends BaseClientDTO {

    /**
     * 产品谱系配置
     */
    @Schema(description = "产品谱系配置")
    private PedigreeConfig pedigreeConfig;

    public PedigreeConfig getPedigreeConfig() {
        return pedigreeConfig;
    }

    public ClientPedigreeConfigDTO setPedigreeConfig(PedigreeConfig pedigreeConfig) {
        this.pedigreeConfig = pedigreeConfig;
        return this;
    }
}
