package net.airuima.rbase.dto.bom;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.config.annotation.Forbidden;
import net.airuima.dto.AbstractDto;

import java.io.Serializable;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/1/28
 */
@Schema(description = "物料属性")
public class MaterialAttributeDTO extends AbstractDto implements Serializable {


    /**
     * 物料属性名称
     */
    @Schema(description = "物料属性名称")
    private String name;

    /**
     * 物料属性编码
     */
    @Schema(description = "物料属性编码")
    private String code;

    /**
     * 禁用启用(0:禁用;1:启用)
     */
    @Schema(description = "禁用启用(0:禁用;1:启用)")
    @Forbidden
    private boolean isEnable;



    public String getName() {
        return name;
    }

    public MaterialAttributeDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public MaterialAttributeDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public MaterialAttributeDTO setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }
}
