package net.airuima.rbase.dto.ocmes;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.dto.client.*;

import java.util.Collections;
import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/22
 */
@Schema(description = "下交工序保存烘烤温循老化参数信息")
public class BakeCycleBakeAgeingSaveDTO {

    /**
     * 子工单
     */
    @Schema(description = "子工单")
    private SubWorkSheet subWorkSheet;

    /**
     * 工序
     */
    @Schema(description = "工序")
    private Step step;

    /**
     * 生产工单定制工序
     */
    @Schema(description = "生产工单定制工序")
    private WsStep wsStep;

    /**
     * 操作员工id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "操作员工id")
    private Long staffId;

    /**
     * 烘烤温循老化类型(0:放入;1:取出)
     */
    @Schema(description = "烘烤温循老化类型(0:放入;1:取出)")
    private Integer bakeFlag;

    @Schema(description = "烘烤历史信息集合")
    private List<BakeHistoryInfoDTO> bakeHistoryInfoList;
    @Schema(description = "温循历史信息集合")
    private List<CycleBakeHistoryInfoDTO> cycleBakeHistoryInfoList;
    @Schema(description = "老化历史信息集合")
    private List<AgeingHistoryInfoDTO> ageingHistoryInfoList;

    public BakeCycleBakeAgeingSaveDTO(ClientSaveStepInfoDTO clientSaveStepInfoDto) {
        this.bakeFlag = clientSaveStepInfoDto.getBakeFlag();
        this.bakeHistoryInfoList = clientSaveStepInfoDto.getBakeHistoryInfoList();
        this.cycleBakeHistoryInfoList = clientSaveStepInfoDto.getCycleBakeHistoryInfoList();
        this.ageingHistoryInfoList = clientSaveStepInfoDto.getAgeingHistoryInfoList();
        this.staffId = clientSaveStepInfoDto.getStaffId();
    }

    public BakeCycleBakeAgeingSaveDTO(ClientSaveSnStepInfoDTO clientSaveSnStepInfoDto) {
        this.bakeFlag = clientSaveSnStepInfoDto.getBakeFlag();
        this.bakeHistoryInfoList = clientSaveSnStepInfoDto.getBakeHistoryInfo() == null ? null: Collections.singletonList(clientSaveSnStepInfoDto.getBakeHistoryInfo());
        this.cycleBakeHistoryInfoList = clientSaveSnStepInfoDto.getCycleBakeHistoryInfo() == null ? null: Collections.singletonList(clientSaveSnStepInfoDto.getCycleBakeHistoryInfo());
        this.ageingHistoryInfoList = clientSaveSnStepInfoDto.getAgeingHistoryInfo() == null ? null: Collections.singletonList(clientSaveSnStepInfoDto.getAgeingHistoryInfo());
        this.staffId = clientSaveSnStepInfoDto.getStaffId();
    }

    public Long getStaffId() {
        return staffId;
    }

    public BakeCycleBakeAgeingSaveDTO setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public BakeCycleBakeAgeingSaveDTO setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public BakeCycleBakeAgeingSaveDTO setStep(Step step) {
        this.step = step;
        return this;
    }

    public WsStep getWsStep() {
        return wsStep;
    }

    public BakeCycleBakeAgeingSaveDTO setWsStep(WsStep wsStep) {
        this.wsStep = wsStep;
        return this;
    }

    public Integer getBakeFlag() {
        return bakeFlag;
    }

    public BakeCycleBakeAgeingSaveDTO setBakeFlag(Integer bakeFlag) {
        this.bakeFlag = bakeFlag;
        return this;
    }

    public List<BakeHistoryInfoDTO> getBakeHistoryInfoList() {
        return bakeHistoryInfoList;
    }

    public BakeCycleBakeAgeingSaveDTO setBakeHistoryInfoList(List<BakeHistoryInfoDTO> bakeHistoryInfoList) {
        this.bakeHistoryInfoList = bakeHistoryInfoList;
        return this;
    }

    public List<CycleBakeHistoryInfoDTO> getCycleBakeHistoryInfoList() {
        return cycleBakeHistoryInfoList;
    }

    public BakeCycleBakeAgeingSaveDTO setCycleBakeHistoryInfoList(List<CycleBakeHistoryInfoDTO> cycleBakeHistoryInfoList) {
        this.cycleBakeHistoryInfoList = cycleBakeHistoryInfoList;
        return this;
    }

    public List<AgeingHistoryInfoDTO> getAgeingHistoryInfoList() {
        return ageingHistoryInfoList;
    }

    public BakeCycleBakeAgeingSaveDTO setAgeingHistoryInfoList(List<AgeingHistoryInfoDTO> ageingHistoryInfoList) {
        this.ageingHistoryInfoList = ageingHistoryInfoList;
        return this;
    }
}
