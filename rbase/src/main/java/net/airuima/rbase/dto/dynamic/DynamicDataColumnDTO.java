package net.airuima.rbase.dto.dynamic;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.AbstractDto;

import java.util.List;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
public class DynamicDataColumnDTO extends AbstractDto {

    /**
     * 动态元数据名称
     */
    @Schema(description = "动态元数据名称",requiredMode = Schema.RequiredMode.REQUIRED,type = "string",maxLength = 255)
    private String name;

    /**
     * 动态元数据编码
     */
    @Schema(description = "动态元数据编码",requiredMode = Schema.RequiredMode.REQUIRED,type = "string",maxLength = 255)
    private String code;

    /**
     * 引导信息
     */
    @Schema(description = "引导信息" ,requiredMode = Schema.RequiredMode.NOT_REQUIRED,type = "string",maxLength = 255)
    private String guidance;

    /**
     * 提示信息
     */
    @Schema(description = "提示信息" ,requiredMode = Schema.RequiredMode.NOT_REQUIRED,type = "string",maxLength = 255)
    private String prompt;

    /**
     * 表格展示顺序
     */
    @Schema(description = "表格展示顺序",requiredMode = Schema.RequiredMode.NOT_REQUIRED,type = "integer",format = "int32",maxLength = 11,defaultValue = "0")
    private int tableOrder;

    /**
     * 表单展示顺序
     */
    @Schema(description = "表单展示顺序",requiredMode = Schema.RequiredMode.NOT_REQUIRED,type = "integer",format = "int32",maxLength = 11,defaultValue = "0")
    private int formOrder;

    /**
     * 字段类型
     */
    @Schema(description = "字段类型" ,requiredMode = Schema.RequiredMode.NOT_REQUIRED,type = "string",maxLength = 255)
    private String category;

    /**
     * 前端组件
     */
    @Schema(description = "前端组件",requiredMode = Schema.RequiredMode.REQUIRED,type = "string",maxLength = 255)
    private String widget;

    /**
     * 前端组件数据
     */
    @Schema(description = "前端组件数据" ,requiredMode = Schema.RequiredMode.NOT_REQUIRED,type = "string",maxLength = 255)
    private String widgetData;

    /**
     * 前端字段验证
     */
    @Schema(description = "前端字段验证" ,requiredMode = Schema.RequiredMode.NOT_REQUIRED,type = "string",maxLength = 255)
    private String columnValidate;

    /**
     * 父级ID
     */
    @Schema(description = "父级",requiredMode = Schema.RequiredMode.REQUIRED)
    private DynamicDataColumnDTO parent;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用",requiredMode = Schema.RequiredMode.NOT_REQUIRED,type = "boolean",defaultValue = "true")
    private boolean isEnable;

    @Schema(description = "当前元数据的子项数据")
    private List<String> subDynamicDataColumns;

    public String getName() {
        return name;
    }

    public DynamicDataColumnDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public DynamicDataColumnDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getGuidance() {
        return guidance;
    }

    public DynamicDataColumnDTO setGuidance(String guidance) {
        this.guidance = guidance;
        return this;
    }

    public String getPrompt() {
        return prompt;
    }

    public DynamicDataColumnDTO setPrompt(String prompt) {
        this.prompt = prompt;
        return this;
    }

    public int getTableOrder() {
        return tableOrder;
    }

    public DynamicDataColumnDTO setTableOrder(int tableOrder) {
        this.tableOrder = tableOrder;
        return this;
    }

    public int getFormOrder() {
        return formOrder;
    }

    public DynamicDataColumnDTO setFormOrder(int formOrder) {
        this.formOrder = formOrder;
        return this;
    }

    public String getCategory() {
        return category;
    }

    public DynamicDataColumnDTO setCategory(String category) {
        this.category = category;
        return this;
    }

    public String getWidget() {
        return widget;
    }

    public DynamicDataColumnDTO setWidget(String widget) {
        this.widget = widget;
        return this;
    }

    public String getWidgetData() {
        return widgetData;
    }

    public DynamicDataColumnDTO setWidgetData(String widgetData) {
        this.widgetData = widgetData;
        return this;
    }

    public String getColumnValidate() {
        return columnValidate;
    }

    public DynamicDataColumnDTO setColumnValidate(String columnValidate) {
        this.columnValidate = columnValidate;
        return this;
    }

    public DynamicDataColumnDTO getParent() {
        return parent;
    }

    public DynamicDataColumnDTO setParent(DynamicDataColumnDTO parent) {
        this.parent = parent;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public DynamicDataColumnDTO setIsEnable(boolean enable) {
        isEnable = enable;
        return this;
    }

    public List<String> getSubDynamicDataColumns() {
        return subDynamicDataColumns;
    }

    public DynamicDataColumnDTO setSubDynamicDataColumns(List<String> subDynamicDataColumns) {
        this.subDynamicDataColumns = subDynamicDataColumns;
        return this;
    }
}
