package net.airuima.rbase.dto.client;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.client.base.BaseClientDTO;

import java.util.List;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-03-23
 */
@Schema(description = "RWorker保存首中末检相关信息DTO")
public class ClientSaveCheckInfoDTO extends BaseClientDTO {
    @Schema(description = "类型(0,首检;1,QC抽检)")
    private Integer category;
    @Schema(description = "被检测工位ID")
    private Long workCellId;
    @Schema(description = "子工单ID")
    private Long subWsId;
    @Schema(description = "操作人ID")
    private Long operatorId;
    @Schema(description = "被检测工序ID")
    private Long stepId;
    @Schema(description = "检测规则信息")
    private CheckRuleInfo checkRuleInfo;
    @Schema(description = "SN及对应检测项目数据信息")
    private List<SnCheckItemInfo> snCheckItemInfoList;
    /**
     * 项目类型
     */
    @Schema(description = "项目类型")
    private Integer variety;

    public ClientSaveCheckInfoDTO(){

    }

    public ClientSaveCheckInfoDTO(BaseClientDTO baseClientDto) {
        this.setStatus(baseClientDto.getStatus());
        this.setMessage(baseClientDto.getMessage());
    }

    public Integer getVariety() {
        return variety;
    }

    public ClientSaveCheckInfoDTO setVariety(Integer variety) {
        this.variety = variety;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public ClientSaveCheckInfoDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public Long getWorkCellId() {
        return workCellId;
    }

    public ClientSaveCheckInfoDTO setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
        return this;
    }

    public Long getSubWsId() {
        return subWsId;
    }

    public ClientSaveCheckInfoDTO setSubWsId(Long subWsId) {
        this.subWsId = subWsId;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public ClientSaveCheckInfoDTO setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public Long getStepId() {
        return stepId;
    }

    public ClientSaveCheckInfoDTO setStepId(Long stepId) {
        this.stepId = stepId;
        return this;
    }

    public CheckRuleInfo getCheckRuleInfo() {
        return checkRuleInfo;
    }

    public ClientSaveCheckInfoDTO setCheckRuleInfo(CheckRuleInfo checkRuleInfo) {
        this.checkRuleInfo = checkRuleInfo;
        return this;
    }

    public List<SnCheckItemInfo> getSnCheckItemInfoList() {
        return snCheckItemInfoList;
    }

    public ClientSaveCheckInfoDTO setSnCheckItemInfoList(List<SnCheckItemInfo> snCheckItemInfoList) {
        this.snCheckItemInfoList = snCheckItemInfoList;
        return this;
    }

    @Schema(description = "检测规则信息")
    public static class CheckRuleInfo {
        @Schema(description = "检测数量")
        private Integer number;
        @Schema(description = "判定方式(0:数量;1:比例)")
        private Integer judgeWay;
        @Schema(description = "合格判定数量")
        private int qualifiedNumber;
        @Schema(description = "合格比例")
        private Double qualifiedRate;

        public Integer getNumber() {
            return number;
        }

        public CheckRuleInfo setNumber(Integer number) {
            this.number = number;
            return this;
        }

        public Double getQualifiedRate() {
            return qualifiedRate;
        }

        public CheckRuleInfo setQualifiedRate(Double qualifiedRate) {
            this.qualifiedRate = qualifiedRate;
            return this;
        }

        public Integer getJudgeWay() {
            return judgeWay;
        }

        public CheckRuleInfo setJudgeWay(Integer judgeWay) {
            this.judgeWay = judgeWay;
            return this;
        }

        public int getQualifiedNumber() {
            return qualifiedNumber;
        }

        public CheckRuleInfo setQualifiedNumber(int qualifiedNumber) {
            this.qualifiedNumber = qualifiedNumber;
            return this;
        }
    }

    @Schema(description = "SN及对应检测项目数据信息")
    public static class SnCheckItemInfo {
        @Schema(description = "检测SN")
        private String sn;
        @Schema(description = "检测项目列表")
        private List<CheckItemInfo> checkItemInfoList;

        public String getSn() {
            return sn;
        }

        public SnCheckItemInfo setSn(String sn) {
            this.sn = sn;
            return this;
        }

        public List<CheckItemInfo> getCheckItemInfoList() {
            return checkItemInfoList;
        }

        public SnCheckItemInfo setCheckItemInfoList(List<CheckItemInfo> checkItemInfoList) {
            this.checkItemInfoList = checkItemInfoList;
            return this;
        }

        @Schema(description = "检测项目信息")
        public static class CheckItemInfo {
            private Long checkItemId;
            @Schema(description = "检测项目编码")
            private String checkItemCode;
            @Schema(description = "检测项目名称")
            private String checkItemName;
            @Schema(description = "合格范围(开闭区间或者OK)")
            private String qualifiedRange;
            @Schema(description = "检测数据值")
            private String checkData;

            public Long getCheckItemId() {
                return checkItemId;
            }

            public CheckItemInfo setCheckItemId(Long checkItemId) {
                this.checkItemId = checkItemId;
                return this;
            }

            public String getCheckItemCode() {
                return checkItemCode;
            }

            public CheckItemInfo setCheckItemCode(String checkItemCode) {
                this.checkItemCode = checkItemCode;
                return this;
            }

            public String getCheckItemName() {
                return checkItemName;
            }

            public CheckItemInfo setCheckItemName(String checkItemName) {
                this.checkItemName = checkItemName;
                return this;
            }

            public String getQualifiedRange() {
                return qualifiedRange;
            }

            public CheckItemInfo setQualifiedRange(String qualifiedRange) {
                this.qualifiedRange = qualifiedRange;
                return this;
            }

            public String getCheckData() {
                return checkData;
            }

            public CheckItemInfo setCheckData(String checkData) {
                this.checkData = checkData;
                return this;
            }
        }
    }
}
