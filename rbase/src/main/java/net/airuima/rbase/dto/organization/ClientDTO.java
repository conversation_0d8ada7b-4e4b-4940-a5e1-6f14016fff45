package net.airuima.rbase.dto.organization;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.config.annotation.Forbidden;
import net.airuima.dto.AbstractDto;

import java.io.Serializable;

/**
 * 客户DTO
 *
 * <AUTHOR>
 * @date 2022/11/7 14:37
 */
@Schema(description = "客户DTO")
public class ClientDTO extends AbstractDto implements Serializable {
    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    private String name;

    /**
     * 客户编码
     */
    @Schema(description = "客户编码")
    private String code;

    /**
     * 禁用启用(0:禁用;1:启用)
     */
    @Schema(description = "禁用启用(0:禁用;1:启用)")
    @Forbidden
    private Boolean isEnable;

    /**
     * 客户地址
     */
    @Schema(description = "客户地址")
    private String address;

    /**
     * 客户联系电话
     */
    @Schema(description = "客户联系电话")
    private String phoneNumber;

    /**
     * 客户联系人
     */
    @Schema(description = "客户联系人")
    private String contact;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    private String note;

    /**
     * 客户类型
     */
    @Schema(description = "客户类型")
    private Integer category;

    /**
     * 客户等级
     */
    @Schema(description = "客户等级")
    private Integer degree;

    public ClientDTO() {
    }

    public ClientDTO(Long clientId) {
    }

    public String getName() {
        return name;
    }

    public ClientDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public ClientDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public Boolean getEnable() {
        return isEnable;
    }

    public ClientDTO setEnable(Boolean enable) {
        isEnable = enable;
        return this;
    }

    public String getAddress() {
        return address;
    }

    public ClientDTO setAddress(String address) {
        this.address = address;
        return this;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public ClientDTO setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
        return this;
    }

    public String getContact() {
        return contact;
    }

    public ClientDTO setContact(String contact) {
        this.contact = contact;
        return this;
    }

    public String getNote() {
        return note;
    }

    public ClientDTO setNote(String note) {
        this.note = note;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public ClientDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public Integer getDegree() {
        return degree;
    }

    public ClientDTO setDegree(Integer degree) {
        this.degree = degree;
        return this;
    }
}
