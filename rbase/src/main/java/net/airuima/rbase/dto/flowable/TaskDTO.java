package net.airuima.rbase.dto.flowable;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-02-26
 */
@Schema(description = "工作流任务DTO")
public class TaskDTO {
    @Schema(description = "流程定义KEY")
    private String processDefinitionKey;
    @Schema(description = "流程定义名称")
    private String processDefinitionName;
    @Schema(description = "流程定义版本")
    private Integer processDefinitionVersion;
    @Schema(description = "流程实例ID")
    private String processInstanceId;
    @Schema(description = "业务数据ID")
    private String businessKey;
    @Schema(description = "任务ID")
    private String taskId;
    @Schema(description = "任务名称")
    private String taskName;
    @Schema(description = "任务节点KEY")
    private String taskDefinitionKey;
    @Schema(description = "业务数据序列号")
    private String serialNumber;
    @Schema(description = "任务处理人ID(完成任务请求参数)")
    private Long userId;
    @Schema(description = "审批结果键值对(完成任务请求参数)")
    private Map<String,Object> paramater;
    @Schema(description = "审批备注意见(完成任务请求参数)")
    private String comment;

    public String getProcessDefinitionKey() {
        return processDefinitionKey;
    }

    public TaskDTO setProcessDefinitionKey(String processDefinitionKey) {
        this.processDefinitionKey = processDefinitionKey;
        return this;
    }

    public String getProcessDefinitionName() {
        return processDefinitionName;
    }

    public TaskDTO setProcessDefinitionName(String processDefinitionName) {
        this.processDefinitionName = processDefinitionName;
        return this;
    }

    public Integer getProcessDefinitionVersion() {
        return processDefinitionVersion;
    }

    public TaskDTO setProcessDefinitionVersion(Integer processDefinitionVersion) {
        this.processDefinitionVersion = processDefinitionVersion;
        return this;
    }

    public String getProcessInstanceId() {
        return processInstanceId;
    }

    public TaskDTO setProcessInstanceId(String processInstanceId) {
        this.processInstanceId = processInstanceId;
        return this;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public TaskDTO setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
        return this;
    }

    public String getTaskId() {
        return taskId;
    }

    public TaskDTO setTaskId(String taskId) {
        this.taskId = taskId;
        return this;
    }

    public String getTaskName() {
        return taskName;
    }

    public TaskDTO setTaskName(String taskName) {
        this.taskName = taskName;
        return this;
    }

    public String getTaskDefinitionKey() {
        return taskDefinitionKey;
    }

    public TaskDTO setTaskDefinitionKey(String taskDefinitionKey) {
        this.taskDefinitionKey = taskDefinitionKey;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public TaskDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public Long getUserId() {
        return userId;
    }

    public TaskDTO setUserId(Long userId) {
        this.userId = userId;
        return this;
    }

    public Map<String, Object> getParamater() {
        return paramater;
    }

    public TaskDTO setParamater(Map<String, Object> paramater) {
        this.paramater = paramater;
        return this;
    }

    public String getComment() {
        return comment;
    }

    public TaskDTO setComment(String comment) {
        this.comment = comment;
        return this;
    }
}
