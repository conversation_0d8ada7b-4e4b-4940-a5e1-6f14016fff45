package net.airuima.rbase.dto.ocmes;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.client.AgeingConfigInfoDTO;
import net.airuima.rbase.dto.client.BakeConfigInfoDTO;
import net.airuima.rbase.dto.client.CycleBakeConfigInfoDTO;


/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/3/29
 */
@Schema(description = "烘烤温循老化配置dto")
public class BakeCycleBakeAgeingConfigDTO {

    /**
     * 烘烤配置参数详情
     */
    @Schema(description = "烘烤配置参数详情")
    private BakeConfigInfoDTO bakeConfigInfo;

    /**
     * 温循配置参数详情
     */
    @Schema(description = "温循配置参数详情")
    private CycleBakeConfigInfoDTO cycleBakeConfigInfo;

    /**
     * 老化配置参数详情
     */
    @Schema(description = "老化配置参数详情")
    private AgeingConfigInfoDTO ageingConfigInfo;

    public BakeConfigInfoDTO getBakeConfigInfo() {
        return bakeConfigInfo;
    }

    public BakeCycleBakeAgeingConfigDTO setBakeConfigInfo(BakeConfigInfoDTO bakeConfigInfo) {
        this.bakeConfigInfo = bakeConfigInfo;
        return this;
    }

    public CycleBakeConfigInfoDTO getCycleBakeConfigInfo() {
        return cycleBakeConfigInfo;
    }

    public BakeCycleBakeAgeingConfigDTO setCycleBakeConfigInfo(CycleBakeConfigInfoDTO cycleBakeConfigInfo) {
        this.cycleBakeConfigInfo = cycleBakeConfigInfo;
        return this;
    }

    public AgeingConfigInfoDTO getAgeingConfigInfo() {
        return ageingConfigInfo;
    }

    public BakeCycleBakeAgeingConfigDTO setAgeingConfigInfo(AgeingConfigInfoDTO ageingConfigInfo) {
        this.ageingConfigInfo = ageingConfigInfo;
        return this;
    }
}
