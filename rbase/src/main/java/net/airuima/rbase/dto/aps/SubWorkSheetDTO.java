package net.airuima.rbase.dto.aps;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import net.airuima.dto.AbstractDto;
import net.airuima.rbase.service.procedure.batch.dto.WsStepWorkCellGetDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 子工单DTO
 * <AUTHOR>
 * @date 2021-04-28
 */
@Schema(description = "子工单DTO(主要用于手动分单参数)")
public class SubWorkSheetDTO extends AbstractDto {
    /**
     * 总工单
     */
    @NotNull
    @Schema(description = "总工单Id", required = true)
    private Long workSheetId;

    /**
     * 投产数
     */
    @NotNull
    @Schema(description = "投产数", required = true)
    private Integer number;

    @Schema(description = "子工单号", required = false)
    private String serialNumber;

    /**
     * 计划开工日期
     */
    @Schema(description = "计划开工日期")
    private LocalDateTime planStartDate;

    /**
     * 计划完工日期
     */
    @Schema(description = "计划完工日期")
    private LocalDateTime planEndDate;

    /**
     * 子工单分单备注,RWorker进行展示
     */
    @Schema(description = "子工单分单备注,RWorker进行展示")
    private String note;

    /**
     * 结单原因
     */
    @Schema(description = "结单原因")
    private String statementReason;

    /**
     * 工序指定工位
     */
    @Schema(description = "工序指定工位")
    private List<WsStepWorkCellGetDTO> wsStepWorkCellGetDto;

    public Long getWorkSheetId() {
        return workSheetId;
    }

    public SubWorkSheetDTO setWorkSheetId(Long workSheetId) {
        this.workSheetId = workSheetId;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public SubWorkSheetDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public SubWorkSheetDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public LocalDateTime getPlanStartDate() {
        return planStartDate;
    }

    public SubWorkSheetDTO setPlanStartDate(LocalDateTime planStartDate) {
        this.planStartDate = planStartDate;
        return this;
    }

    public LocalDateTime getPlanEndDate() {
        return planEndDate;
    }

    public SubWorkSheetDTO setPlanEndDate(LocalDateTime planEndDate) {
        this.planEndDate = planEndDate;
        return this;
    }

    public String getNote() {
        return note;
    }

    public SubWorkSheetDTO setNote(String note) {
        this.note = note;
        return this;
    }

    public String getStatementReason() {
        return statementReason;
    }

    public SubWorkSheetDTO setStatementReason(String statementReason) {
        this.statementReason = statementReason;
        return this;
    }

    public List<WsStepWorkCellGetDTO> getWsStepWorkCellGetDto() {
        return wsStepWorkCellGetDto;
    }

    public SubWorkSheetDTO setWsStepWorkCellGetDto(List<WsStepWorkCellGetDTO> wsStepWorkCellGetDto) {
        this.wsStepWorkCellGetDto = wsStepWorkCellGetDto;
        return this;
    }
}
