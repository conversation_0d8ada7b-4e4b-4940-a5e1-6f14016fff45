package net.airuima.rbase.dto.report;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetailMaterialBatch;
import net.airuima.rbase.domain.procedure.batch.ContainerDetailMaterialBatch;
import net.airuima.rbase.domain.procedure.single.SnWorkDetailMaterialBatch;
import net.airuima.rbase.dto.organization.SupplierDTO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Optional;

/**
 * 批次追溯DTO
 */
@Schema(description = "批次追溯DTO")
public class BatchTraceReportDTO {
    private BatchTraceReportDTO() {
        // 静态成员的集合，不需要实例化
    }

    /**
     * 请求参数DTO
     */
    @Schema(description = "批次追溯请求参数DTO")
    public static class RequestInfo {

        /**
         * 物料批次
         */
        @Schema(description = "批次追溯物料批次请求参数")
        @NotBlank
        private String materialBatch;

        /**
         * 工序完成日期开始范围
         */
        @Schema(description = "批次追溯开始日期请求参数")
        @NotNull
        private LocalDate startDate;

        /**
         * 工序完成日期结束范围
         */
        @Schema(description = "批次追溯结束日期请求参数")
        @NotNull
        private LocalDate endDate;

        /**
         * 搜索类型
         */
        @Schema(description = "搜索类型")
        private Integer category;

        /**
         * 当前页数
         */
        @Schema(description = "当前页数")
        private Integer currentPage;

        /**
         * 每页显示数
         */
        @Schema(description = "每页显示数")
        private Integer pageSize;

        public String getMaterialBatch() {
            return materialBatch;
        }

        public void setMaterialBatch(String materialBatch) {
            this.materialBatch = materialBatch;
        }

        public LocalDate getStartDate() {
            return startDate;
        }

        public RequestInfo setStartDate(LocalDate startDate) {
            this.startDate = startDate;
            return this;
        }

        public LocalDate getEndDate() {
            return endDate;
        }

        public RequestInfo setEndDate(LocalDate endDate) {
            this.endDate = endDate;
            return this;
        }

        public Integer getCategory() {
            return category;
        }

        public RequestInfo setCategory(Integer category) {
            this.category = category;
            return this;
        }

        public Integer getCurrentPage() {
            return currentPage;
        }

        public RequestInfo setCurrentPage(Integer currentPage) {
            this.currentPage = currentPage;
            return this;
        }

        public Integer getPageSize() {
            return pageSize;
        }

        public RequestInfo setPageSize(Integer pageSize) {
            this.pageSize = pageSize;
            return this;
        }
    }

    /**
     * SN物料批次
     */
    @Schema(description = "SN物料批次")
    public static class SnMaterialBatch {

        /**
         * SN
         */
        @Schema(description = "SN")
        private String sn;

        /**
         * 工序名称
         */
        @Schema(description = "工序名称")
        private String stepName;

        /**
         * 工序编码
         */
        @Schema(description = "工序编码")
        private String stepCode;

        /**
         * 操作人名字
         */
        @Schema(description = "操作人名字")
        private String operatorName;

        /**
         * 操作人员工号
         */
        @Schema(description = "操作人员工号")
        private String operatorCode;

        /**
         * 工位名称
         */
        @Schema(description = "工位名称")
        private String workCellName;

        /**
         * 工位编码
         */
        @Schema(description = "工位编码")
        private String workCellCode;

        /**
         * 子工单号
         */
        @Schema(description = "子工单号")
        private String subWorkSheetSerialNumber;

        /**
         * 物料名称
         */
        @Schema(description = "物料名称")
        private String materialName;

        /**
         * 物料编码
         */
        @Schema(description = "物料编码")
        private String materialCode;

        /**
         * 物料批次
         */
        @Schema(description = "物料批次")
        private String batch;

        /**
         * 供应商名称
         */
        @Schema(description = "供应商名称")
        private String supplierName;

        /**
         * 供应商编码
         */
        @Schema(description = "供应商名称")
        private String supplierCode;

        /**
         * 创建时间
         */
        @Schema(description = "创建时间")
        private LocalDateTime createdDate;

        public SnMaterialBatch(SnWorkDetailMaterialBatch snWorkDetailMaterialBatch) {
            this.sn = snWorkDetailMaterialBatch.getSnWorkDetail().getSn();
            this.stepName = snWorkDetailMaterialBatch.getSnWorkDetail().getStep().getName();
            this.stepCode = snWorkDetailMaterialBatch.getSnWorkDetail().getStep().getCode();
            this.operatorName = snWorkDetailMaterialBatch.getSnWorkDetail().getOperatorDto().getName();
            this.operatorCode = snWorkDetailMaterialBatch.getSnWorkDetail().getOperatorDto().getCode();
            this.workCellName = snWorkDetailMaterialBatch.getSnWorkDetail().getWorkCell().getName();
            this.workCellCode = snWorkDetailMaterialBatch.getSnWorkDetail().getWorkCell().getCode();
            this.subWorkSheetSerialNumber = snWorkDetailMaterialBatch.getSnWorkDetail().getSubWorkSheet().getSerialNumber();
            this.materialName = snWorkDetailMaterialBatch.getMaterialDto().getName();
            this.materialCode = snWorkDetailMaterialBatch.getMaterialDto().getCode();
            this.batch = snWorkDetailMaterialBatch.getMaterialBatch();
            this.supplierName = Optional.ofNullable(snWorkDetailMaterialBatch.getSupplierDTO()).map(SupplierDTO::getName).orElse(null);
            this.supplierCode = Optional.ofNullable(snWorkDetailMaterialBatch.getSupplierDTO()).map(SupplierDTO::getCode).orElse(null);
            this.createdDate = LocalDateTime.ofInstant(snWorkDetailMaterialBatch.getCreatedDate(), ZoneId.systemDefault());
        }

        public String getSn() {
            return sn;
        }

        public void setSn(String sn) {
            this.sn = sn;
        }

        public String getStepName() {
            return stepName;
        }

        public void setStepName(String stepName) {
            this.stepName = stepName;
        }

        public String getStepCode() {
            return stepCode;
        }

        public void setStepCode(String stepCode) {
            this.stepCode = stepCode;
        }

        public String getOperatorName() {
            return operatorName;
        }

        public void setOperatorName(String operatorName) {
            this.operatorName = operatorName;
        }

        public String getOperatorCode() {
            return operatorCode;
        }

        public void setOperatorCode(String operatorCode) {
            this.operatorCode = operatorCode;
        }

        public String getWorkCellName() {
            return workCellName;
        }

        public void setWorkCellName(String workCellName) {
            this.workCellName = workCellName;
        }

        public String getWorkCellCode() {
            return workCellCode;
        }

        public void setWorkCellCode(String workCellCode) {
            this.workCellCode = workCellCode;
        }

        public String getSubWorkSheetSerialNumber() {
            return subWorkSheetSerialNumber;
        }

        public void setSubWorkSheetSerialNumber(String subWorkSheetSerialNumber) {
            this.subWorkSheetSerialNumber = subWorkSheetSerialNumber;
        }

        public String getMaterialName() {
            return materialName;
        }

        public void setMaterialName(String materialName) {
            this.materialName = materialName;
        }

        public String getMaterialCode() {
            return materialCode;
        }

        public void setMaterialCode(String materialCode) {
            this.materialCode = materialCode;
        }

        public String getBatch() {
            return batch;
        }

        public void setBatch(String batch) {
            this.batch = batch;
        }

        public String getSupplierName() {
            return supplierName;
        }

        public void setSupplierName(String supplierName) {
            this.supplierName = supplierName;
        }

        public String getSupplierCode() {
            return supplierCode;
        }

        public void setSupplierCode(String supplierCode) {
            this.supplierCode = supplierCode;
        }

        public LocalDateTime getCreatedDate() {
            return createdDate;
        }

        public void setCreatedDate(LocalDateTime createdDate) {
            this.createdDate = createdDate;
        }
    }

    /**
     * 容器物料批次
     */
    @Schema(description = "容器物料批次")
    public static class ContainerMaterialBatch {

        /**
         * 容器编码
         */
        @Schema(description = "容器编码")
        private String containerCode;

        /**
         * 工序名称
         */
        @Schema(description = "工序名称")
        private String stepName;

        /**
         * 工序编码
         */
        @Schema(description = "工序编码")
        private String stepCode;

        /**
         * 操作人名字
         */
        @Schema(description = "操作人名字")
        private String operatorName;

        /**
         * 操作人员工号
         */
        @Schema(description = "操作人员工号")
        private String operatorCode;

        /**
         * 工位名称
         */
        @Schema(description = "工位名称")
        private String workCellName;

        /**
         * 工位编码
         */
        @Schema(description = "工位编码")
        private String workCellCode;

        /**
         * 子工单号
         */
        @Schema(description = "子工单号")
        private String subWorkSheetSerialNumber;

        /**
         * 物料名称
         */
        @Schema(description = "物料名称")
        private String materialName;

        /**
         * 物料编码
         */
        @Schema(description = "物料编码")
        private String materialCode;

        /**
         * 物料批次
         */
        @Schema(description = "物料批次")
        private String batch;

        /**
         * 供应商名称
         */
        @Schema(description = "物料批次")
        private String supplierName;

        /**
         * 供应商编码
         */
        @Schema(description = "供应商编码")
        private String supplierCode;

        /**
         * 创建时间
         */
        @Schema(description = "创建时间")
        private LocalDateTime createdDate;

        public ContainerMaterialBatch(ContainerDetailMaterialBatch containerDetailMaterialBatch) {
            this.containerCode = containerDetailMaterialBatch.getContainerDetail().getContainerCode();
            this.stepName = containerDetailMaterialBatch.getContainerDetail().getBatchWorkDetail().getStep().getName();
            this.stepCode = containerDetailMaterialBatch.getContainerDetail().getBatchWorkDetail().getStep().getCode();
            this.operatorName = containerDetailMaterialBatch.getContainerDetail().getStaffDto().getName();
            this.operatorCode = containerDetailMaterialBatch.getContainerDetail().getStaffDto().getCode();
            this.workCellName = containerDetailMaterialBatch.getContainerDetail().getWorkCell().getName();
            this.workCellCode = containerDetailMaterialBatch.getContainerDetail().getWorkCell().getCode();
            this.subWorkSheetSerialNumber = containerDetailMaterialBatch.getContainerDetail().getBatchWorkDetail().getSubWorkSheet().getSerialNumber();
            this.materialName = containerDetailMaterialBatch.getMaterialDto().getName();
            this.materialCode = containerDetailMaterialBatch.getMaterialDto().getCode();
            this.batch = containerDetailMaterialBatch.getBatch();
            this.supplierName = Optional.ofNullable(containerDetailMaterialBatch.getSupplierDTO()).map(SupplierDTO::getName).orElse(null);
            this.supplierCode =  Optional.ofNullable(containerDetailMaterialBatch.getSupplierDTO()).map(SupplierDTO::getCode).orElse(null);
            this.createdDate = LocalDateTime.ofInstant(containerDetailMaterialBatch.getCreatedDate(), ZoneId.systemDefault());
        }

        public String getContainerCode() {
            return containerCode;
        }

        public void setContainerCode(String containerCode) {
            this.containerCode = containerCode;
        }

        public String getStepName() {
            return stepName;
        }

        public void setStepName(String stepName) {
            this.stepName = stepName;
        }

        public String getStepCode() {
            return stepCode;
        }

        public void setStepCode(String stepCode) {
            this.stepCode = stepCode;
        }

        public String getOperatorName() {
            return operatorName;
        }

        public void setOperatorName(String operatorName) {
            this.operatorName = operatorName;
        }

        public String getOperatorCode() {
            return operatorCode;
        }

        public void setOperatorCode(String operatorCode) {
            this.operatorCode = operatorCode;
        }

        public String getWorkCellName() {
            return workCellName;
        }

        public void setWorkCellName(String workCellName) {
            this.workCellName = workCellName;
        }

        public String getWorkCellCode() {
            return workCellCode;
        }

        public void setWorkCellCode(String workCellCode) {
            this.workCellCode = workCellCode;
        }

        public String getSubWorkSheetSerialNumber() {
            return subWorkSheetSerialNumber;
        }

        public void setSubWorkSheetSerialNumber(String subWorkSheetSerialNumber) {
            this.subWorkSheetSerialNumber = subWorkSheetSerialNumber;
        }

        public String getMaterialName() {
            return materialName;
        }

        public void setMaterialName(String materialName) {
            this.materialName = materialName;
        }

        public String getMaterialCode() {
            return materialCode;
        }

        public void setMaterialCode(String materialCode) {
            this.materialCode = materialCode;
        }

        public String getBatch() {
            return batch;
        }

        public void setBatch(String batch) {
            this.batch = batch;
        }

        public String getSupplierName() {
            return supplierName;
        }

        public void setSupplierName(String supplierName) {
            this.supplierName = supplierName;
        }

        public String getSupplierCode() {
            return supplierCode;
        }

        public void setSupplierCode(String supplierCode) {
            this.supplierCode = supplierCode;
        }

        public LocalDateTime getCreatedDate() {
            return createdDate;
        }

        public void setCreatedDate(LocalDateTime createdDate) {
            this.createdDate = createdDate;
        }
    }

    /**
     * 批量物料批次
     */
    @Schema(description = "批量物料批次")
    public static class BatchMaterialBatch {

        /**
         * 工序名称
         */
        @Schema(description = "工序名称")
        private String stepName;

        /**
         * 工序编码
         */
        @Schema(description = "工序编码")
        private String stepCode;

        /**
         * 操作人名字
         */
        @Schema(description = "操作人名字")
        private String operatorName;

        /**
         * 操作人员工号
         */
        @Schema(description = "操作人员工号")
        private String operatorCode;

        /**
         * 工位名称
         */
        @Schema(description = "工位名称")
        private String workCellName;

        /**
         * 工位编码
         */
        @Schema(description = "工位编码")
        private String workCellCode;

        /**
         * 子工单号
         */
        @Schema(description = "子工单号")
        private String subWorkSheetSerialNumber;

        /**
         * 物料名称
         */
        @Schema(description = "物料名称")
        private String materialName;

        /**
         * 物料编码
         */
        @Schema(description = "物料编码")
        private String materialCode;

        /**
         * 物料批次
         */
        @Schema(description = "物料批次")
        private String batch;

        /**
         * 供应商名称
         */
        @Schema(description = "供应商名称")
        private String supplierName;

        /**
         * 供应商编码
         */
        @Schema(description = "供应商编码")
        private String supplierCode;

        /**
         * 创建时间
         */
        @Schema(description = "创建时间")
        private LocalDateTime createdDate;

        public BatchMaterialBatch(BatchWorkDetailMaterialBatch batchWorkDetailMaterialBatch) {
            this.stepName = batchWorkDetailMaterialBatch.getBatchWorkDetail().getStep().getName();
            this.stepCode = batchWorkDetailMaterialBatch.getBatchWorkDetail().getStep().getCode();
            this.operatorName = batchWorkDetailMaterialBatch.getBatchWorkDetail().getOperatorDto().getName();
            this.operatorCode = batchWorkDetailMaterialBatch.getBatchWorkDetail().getOperatorDto().getCode();
            this.workCellName = batchWorkDetailMaterialBatch.getBatchWorkDetail().getWorkCell().getName();
            this.workCellCode = batchWorkDetailMaterialBatch.getBatchWorkDetail().getWorkCell().getCode();
            this.subWorkSheetSerialNumber = batchWorkDetailMaterialBatch.getBatchWorkDetail().getSubWorkSheet().getSerialNumber();
            this.materialName = batchWorkDetailMaterialBatch.getMaterialDto().getName();
            this.materialCode = batchWorkDetailMaterialBatch.getMaterialDto().getCode();
            this.batch = batchWorkDetailMaterialBatch.getMaterialBatch();
            this.supplierName = Optional.ofNullable(batchWorkDetailMaterialBatch.getSupplierDTO()).map(SupplierDTO::getName).orElse(null);
            this.supplierCode = Optional.ofNullable(batchWorkDetailMaterialBatch.getSupplierDTO()).map(SupplierDTO::getCode).orElse(null);
            this.createdDate = LocalDateTime.ofInstant(batchWorkDetailMaterialBatch.getCreatedDate(), ZoneId.systemDefault());
        }

        public String getStepName() {
            return stepName;
        }

        public void setStepName(String stepName) {
            this.stepName = stepName;
        }

        public String getStepCode() {
            return stepCode;
        }

        public void setStepCode(String stepCode) {
            this.stepCode = stepCode;
        }

        public String getOperatorName() {
            return operatorName;
        }

        public void setOperatorName(String operatorName) {
            this.operatorName = operatorName;
        }

        public String getOperatorCode() {
            return operatorCode;
        }

        public void setOperatorCode(String operatorCode) {
            this.operatorCode = operatorCode;
        }

        public String getWorkCellName() {
            return workCellName;
        }

        public void setWorkCellName(String workCellName) {
            this.workCellName = workCellName;
        }

        public String getWorkCellCode() {
            return workCellCode;
        }

        public void setWorkCellCode(String workCellCode) {
            this.workCellCode = workCellCode;
        }

        public String getSubWorkSheetSerialNumber() {
            return subWorkSheetSerialNumber;
        }

        public void setSubWorkSheetSerialNumber(String subWorkSheetSerialNumber) {
            this.subWorkSheetSerialNumber = subWorkSheetSerialNumber;
        }

        public String getMaterialName() {
            return materialName;
        }

        public void setMaterialName(String materialName) {
            this.materialName = materialName;
        }

        public String getMaterialCode() {
            return materialCode;
        }

        public void setMaterialCode(String materialCode) {
            this.materialCode = materialCode;
        }

        public String getBatch() {
            return batch;
        }

        public void setBatch(String batch) {
            this.batch = batch;
        }

        public String getSupplierName() {
            return supplierName;
        }

        public void setSupplierName(String supplierName) {
            this.supplierName = supplierName;
        }

        public String getSupplierCode() {
            return supplierCode;
        }

        public void setSupplierCode(String supplierCode) {
            this.supplierCode = supplierCode;
        }

        public LocalDateTime getCreatedDate() {
            return createdDate;
        }

        public void setCreatedDate(LocalDateTime createdDate) {
            this.createdDate = createdDate;
        }
    }

    /**
     * 返回数据DTO
     */
    @Schema(description = "批次追溯返回数据DTO")
    public static class ResponseInfo {

        /**
         * 返回数据条数
         */
        @Schema(description = "返回数据条数")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long countSize;

        /**
         * SN物料信息
         */
        @Schema(description = "SN物料信息")
        private List<SnMaterialBatch> snMaterialBatchList;

        /**
         * 容器物料信息
         */
        @Schema(description = "容器物料信息")
        private List<ContainerMaterialBatch> containerMaterialBatchList;

        /**
         * 批量物料信息
         */
        @Schema(description = "批量物料信息")
        private List<BatchMaterialBatch> batchMaterialBatchList;

        public List<SnMaterialBatch> getSnMaterialBatchList() {
            return snMaterialBatchList;
        }

        public void setSnMaterialBatchList(List<SnMaterialBatch> snMaterialBatchList) {
            this.snMaterialBatchList = snMaterialBatchList;
        }

        public List<ContainerMaterialBatch> getContainerMaterialBatchList() {
            return containerMaterialBatchList;
        }

        public void setContainerMaterialBatchList(List<ContainerMaterialBatch> containerMaterialBatchList) {
            this.containerMaterialBatchList = containerMaterialBatchList;
        }

        public List<BatchMaterialBatch> getBatchMaterialBatchList() {
            return batchMaterialBatchList;
        }

        public void setBatchMaterialBatchList(List<BatchMaterialBatch> batchMaterialBatchList) {
            this.batchMaterialBatchList = batchMaterialBatchList;
        }

        public Long getCountSize() {
            return countSize;
        }

        public void setCountSize(Long countSize) {
            this.countSize = countSize;
        }
    }

}
