package net.airuima.rbase.dto.sync;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 同步工单换料 -》目前仅（sap使用此dto）
 * <AUTHOR>
 * @date 2021-06-04
 */
public class SyncWorkSheetMaterialExchangeDTO {
    /**
     * 工单换料记录ID
     */
    @Schema(description = "工单换料记录ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     *工单号
     */
    @Schema(description = "工单号")
    private String serialNumber;

    /**
     *原始子件物料编码
     */
    @Schema(description = "原始子件物料编码")
    private String originMaterialCode;

    /**
     *换料后子件物料编码
     */
    @Schema(description = "换料后子件物料编码")
    private String materialCode;

    /**
     * 投料数量
     */
    @Schema(description = "投料数量")
    private Double number;

    public Double getNumber() {
        return number;
    }

    public SyncWorkSheetMaterialExchangeDTO setNumber(Double number) {
        this.number = number;
        return this;
    }

    public Long getId() {
        return id;
    }

    public SyncWorkSheetMaterialExchangeDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public SyncWorkSheetMaterialExchangeDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public String getOriginMaterialCode() {
        return originMaterialCode;
    }

    public SyncWorkSheetMaterialExchangeDTO setOriginMaterialCode(String originMaterialCode) {
        this.originMaterialCode = originMaterialCode;
        return this;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public SyncWorkSheetMaterialExchangeDTO setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
        return this;
    }
}
