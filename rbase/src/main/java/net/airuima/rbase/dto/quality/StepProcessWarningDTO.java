package net.airuima.rbase.dto.quality;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.base.quality.StepWarningStandard;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.base.quality.UnqualifiedItemWarningStandard;

@Schema(description = "工序质量预警相关数据DTO")
public class StepProcessWarningDTO {
    /**
     * 不良项目
     */
    @Schema(description = "不良项目")
    private UnqualifiedItem unqualifiedItem;
    /**
     * 工序质量预警生产汇总数据DTO
     */
    @Schema(description = "工序质量预警生产汇总数据DTO")
    private StepWaringData stepWaringData;
    /**
     * 产品谱系工序不良项目预警标准
     */
    @Schema(description = "产品谱系工序不良项目预警标准")
    private UnqualifiedItemWarningStandard unqualifiedItemWarningStandard;
    /**
     * 生产工序预警标准
     */
    @Schema(description = "生产工序预警标准")
    private StepWarningStandard stepWarningStandard;

    public StepProcessWarningDTO() {
    }

    public StepProcessWarningDTO(UnqualifiedItem unqualifiedItem, StepWaringData stepWaringData, UnqualifiedItemWarningStandard unqualifiedItemWarningStandard) {
        this.unqualifiedItem = unqualifiedItem;
        this.stepWaringData = stepWaringData;
        this.unqualifiedItemWarningStandard = unqualifiedItemWarningStandard;
    }

    public StepProcessWarningDTO(StepWaringData stepWaringData, StepWarningStandard stepWarningStandard) {
        this.stepWaringData = stepWaringData;
        this.stepWarningStandard = stepWarningStandard;
    }

    public UnqualifiedItem getUnqualifiedItem() {
        return unqualifiedItem;
    }

    public StepProcessWarningDTO setUnqualifiedItem(UnqualifiedItem unqualifiedItem) {
        this.unqualifiedItem = unqualifiedItem;
        return this;
    }

    public StepWaringData getStepWaringData() {
        return stepWaringData;
    }

    public StepProcessWarningDTO setStepWaringData(StepWaringData stepWaringData) {
        this.stepWaringData = stepWaringData;
        return this;
    }

    public UnqualifiedItemWarningStandard getUnqualifiedItemWarningStandard() {
        return unqualifiedItemWarningStandard;
    }

    public StepProcessWarningDTO setUnqualifiedItemWarningStandard(UnqualifiedItemWarningStandard unqualifiedItemWarningStandard) {
        this.unqualifiedItemWarningStandard = unqualifiedItemWarningStandard;
        return this;
    }

    public StepWarningStandard getStepWarningStandard() {
        return stepWarningStandard;
    }

    public StepProcessWarningDTO setStepWarningStandard(StepWarningStandard stepWarningStandard) {
        this.stepWarningStandard = stepWarningStandard;
        return this;
    }

    @Schema(description = "工序质量预警生产汇总数据DTO")
    public static class StepWaringData {

        /**
         * 工序完成总数
         */
        @Schema(description = "工序完成总数")
        private int finishedNumber;

        /**
         * 工序合格率
         */
        @Schema(description = "工序合格率")
        private double qualifiedRate;

        /**
         * 不良项目占有率
         */
        @Schema(description = "不良项目占有率")
        private double unqualifiedRate;

        /**
         * 不良项目数量
         */
        @Schema(description = "不良项目数量")
        private int unqualifiedItemNumber;

        public StepWaringData() {
        }

        public StepWaringData(int finishedNumber, double unqualifiedRate, int unqualifiedItemNumber) {
            this.finishedNumber = finishedNumber;
            this.unqualifiedRate = unqualifiedRate;
            this.unqualifiedItemNumber = unqualifiedItemNumber;
        }

        public StepWaringData(int finishedNumber, double qualifiedRate) {
            this.finishedNumber = finishedNumber;
            this.qualifiedRate = qualifiedRate;
        }

        public int getFinishedNumber() {
            return finishedNumber;
        }

        public StepWaringData setFinishedNumber(int finishedNumber) {
            this.finishedNumber = finishedNumber;
            return this;
        }

        public double getQualifiedRate() {
            return qualifiedRate;
        }

        public StepWaringData setQualifiedRate(double qualifiedRate) {
            this.qualifiedRate = qualifiedRate;
            return this;
        }

        public double getUnqualifiedRate() {
            return unqualifiedRate;
        }

        public StepWaringData setUnqualifiedRate(double unqualifiedRate) {
            this.unqualifiedRate = unqualifiedRate;
            return this;
        }

        public int getUnqualifiedItemNumber() {
            return unqualifiedItemNumber;
        }

        public StepWaringData setUnqualifiedItemNumber(int unqualifiedItemNumber) {
            this.unqualifiedItemNumber = unqualifiedItemNumber;
            return this;
        }
    }
}


