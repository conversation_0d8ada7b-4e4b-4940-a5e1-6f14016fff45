package net.airuima.rbase.dto.sync;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import net.airuima.rbase.dto.custom.CustomDTO;
import net.airuima.rbase.dto.digiwin.WorkSheetMaterialDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 同步工单信息 -》 目前与（sap和erp都使用此dto）
 * <AUTHOR>
 * @date 2021-06-03
 */
@Schema(description = "同步工单信息")
public class SyncWorkSheetDTO extends CustomDTO {

    @Schema(description = "SAP自身工单ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 工单号
     */
    @NotNull
    @Schema(description = "工单号", required = true)
    private String serialNumber;


    /**
     * 销售订单号
     */
    @Schema(description = "销售订单号", required = true)
    private String saleOrderSerialNumber;

    /**
     * 客户编码
     */
    @Schema(description = "客户编码", required = true)
    private String clientCode;

    /**
     * 工单类型(0:离线返修单;1:正常单)
     */
    @NotNull
    @Schema(description = "工单类型(0:离线返修单;1:正常单;)", required = true)
    private Integer category;

    /**
     * 组织编码
     */
    @NotNull
    @Schema(description = "组织编码", required = true)
    private String organizationCode;

    /**
     * 主件编码
     */
    @NotNull
    @Schema(description = "主件编码", required = true)
    private String materialCode;

    /**
     * BOM编码
     */
    @NotNull
    @Schema(description = "BOM编码", required = true)
    private String bomCode;

    /**
     * 投产数量
     */
    @NotNull
    @Schema(description = "投产数量", required = true)
    private Integer number;

    /**
     * 计划开工日期
     */
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @Schema(description = "计划开工日期", required = true)
    private LocalDateTime planStartDate;

    /**
     * 计划结单日期
     */
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @Schema(description = "计划结单日期", required = true)
    private LocalDateTime planEndDate;

    /**
     * 生产线编码
     */
    @Schema(description = "生产线编码")
    private String workLineCode;

    /**
     * 工艺路线编码
     */
    @Schema(description = "工艺路线编码")
    private String workFlowCode;

    /**
     * 子件物料
     */
    @Schema(description = "子件物料")
    private List<WorkSheetMaterialDTO.WsMaterialAdaptDTO> wsMaterialDtoList;

    /**
     * 同步类型(0:新增;1:修改;2:删除;3:退料结单)
     */
    @NotNull
    @Schema(description = "同步类型(0:新增;1:修改;2:删除;3:退料结单)", required = true)
    private Integer operate;

    public Long getId() {
        return id;
    }

    public SyncWorkSheetDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public SyncWorkSheetDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public SyncWorkSheetDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public String getOrganizationCode() {
        return organizationCode;
    }

    public SyncWorkSheetDTO setOrganizationCode(String organizationCode) {
        this.organizationCode = organizationCode;
        return this;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public SyncWorkSheetDTO setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
        return this;
    }

    public String getBomCode() {
        return bomCode;
    }

    public SyncWorkSheetDTO setBomCode(String bomCode) {
        this.bomCode = bomCode;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public SyncWorkSheetDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public LocalDateTime getPlanStartDate() {
        return planStartDate;
    }

    public SyncWorkSheetDTO setPlanStartDate(LocalDateTime planStartDate) {
        this.planStartDate = planStartDate;
        return this;
    }

    public LocalDateTime getPlanEndDate() {
        return planEndDate;
    }

    public SyncWorkSheetDTO setPlanEndDate(LocalDateTime planEndDate) {
        this.planEndDate = planEndDate;
        return this;
    }

    public Integer getOperate() {
        return operate;
    }

    public SyncWorkSheetDTO setOperate(Integer operate) {
        this.operate = operate;
        return this;
    }

    public List<WorkSheetMaterialDTO.WsMaterialAdaptDTO> getWsMaterialDtoList() {
        return wsMaterialDtoList;
    }

    public SyncWorkSheetDTO setWsMaterialDtoList(List<WorkSheetMaterialDTO.WsMaterialAdaptDTO> wsMaterialDtoList) {
        this.wsMaterialDtoList = wsMaterialDtoList;
        return this;
    }

    public String getSaleOrderSerialNumber() {
        return saleOrderSerialNumber;
    }

    public SyncWorkSheetDTO setSaleOrderSerialNumber(String saleOrderSerialNumber) {
        this.saleOrderSerialNumber = saleOrderSerialNumber;
        return this;
    }

    public String getClientCode() {
        return clientCode;
    }

    public SyncWorkSheetDTO setClientCode(String clientCode) {
        this.clientCode = clientCode;
        return this;
    }

    public String getWorkLineCode() {
        return workLineCode;
    }

    public SyncWorkSheetDTO setWorkLineCode(String workLineCode) {
        this.workLineCode = workLineCode;
        return this;
    }

    public String getWorkFlowCode() {
        return workFlowCode;
    }

    public SyncWorkSheetDTO setWorkFlowCode(String workFlowCode) {
        this.workFlowCode = workFlowCode;
        return this;
    }
}
