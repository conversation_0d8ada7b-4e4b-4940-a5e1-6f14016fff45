package net.airuima.rbase.dto.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 文件基本信息Domain
 *
 * <AUTHOR>
 * @date 2022-08-09
 */
@Schema( description = "文件基本信息DTO")
public class DocumentDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Schema(description = "ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 名称
     */
    @Schema(description = "名称", required = true)
    private String name;

    /**
     * 文件类型，0：图片，1：文档，2：视频，3：压缩包，4：音频，5：可执行文件
     */
    @Schema(description = "文件类型，0：图片，1：文档，2：视频，3：压缩包，4：音频，5：可执行文件", required = true)
    private Integer documentCategory;

    /**
     * 业务类型，0：基础文件，1：业务文件
     */
    @Schema(description = "业务类型，0：基础文件，1：业务文件", required = true)
    private Integer businessCategory;

    /**
     * 来源
     */
    @Schema(description = "来源", required = true)
    private String serviceName;

    /**
     * 路径
     */
    @Schema(description = "路径", required = true)
    private String path;

    /**
     * 关联类型，用户自定义
     */
    @Schema(description = "关联类型，用户自定义")
    private Integer category;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getDocumentCategory() {
        return documentCategory;
    }

    public void setDocumentCategory(Integer documentCategory) {
        this.documentCategory = documentCategory;
    }

    public Integer getBusinessCategory() {
        return businessCategory;
    }

    public void setBusinessCategory(Integer businessCategory) {
        this.businessCategory = businessCategory;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }
}
