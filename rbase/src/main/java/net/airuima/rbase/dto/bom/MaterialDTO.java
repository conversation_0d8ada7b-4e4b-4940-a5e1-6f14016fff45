package net.airuima.rbase.dto.bom;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.config.annotation.Forbidden;
import net.airuima.dto.AbstractDto;

import java.io.Serializable;
import java.util.Set;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2020/12/15
 */
@Schema(description = "物料信息DTO")
public class MaterialDTO extends AbstractDto implements Serializable {

    /**
     * 物料名称
     */
    @Schema(description = "物料名称")
    private String name;

    /**
     * 物料代码
     */
    @Schema(description = "物料代码")
    private String code;

    /**
     * 全名
     */
    @Schema(description = "全名")
    private String fullName;

    /**
     * 物料类型
     */
    @Schema(description = "物料类型(0:原材料、1:半成品、2:成品、3:其它)")
    private Integer category;

    /**
     * 主辅类型(1:主料;0:辅料)
     */
    @Schema(description = "主辅类型(1:主料;0:辅料)")
    private Integer materialCategory;

    /**
     * 是否展示物料(0:不展示;1:展示)
     */
    @Schema(description = "是否展示物料(0:不展示;1:展示)")
    private Boolean display;

    /**
     * 规格型号
     */
    @Schema(description = "规格型号")
    private String specification;

    /**
     * 单位
     */
    @Schema(description = "单位")
    private String unit;

    /**
     * 单位实体
     */
    @Schema(description = "单位实体")
    private MeteringUnitDTO meteringUnit;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Double number;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    @Forbidden
    private Boolean isEnable;

    /**
     * 保质期(天数)
     */
    @Schema(description = "保质期(天数)")
    private int expireDay;

    private Set<MaterialAttributeDTO> materialAttribute;

    public String getName() {
        return name;
    }

    public MaterialDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public MaterialDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getFullName() {
        return fullName;
    }

    public MaterialDTO setFullName(String fullName) {
        this.fullName = fullName;
        return this;
    }

    public Integer getMaterialCategory() {
        return materialCategory;
    }

    public MaterialDTO setMaterialCategory(Integer materialCategory) {
        this.materialCategory = materialCategory;
        return this;
    }

    public Boolean getDisplay() {
        return display;
    }

    public MaterialDTO setDisplay(Boolean display) {
        this.display = display;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public MaterialDTO setSpecification(String specification) {
        this.specification = specification;
        return this;
    }

    public String getUnit() {
        return unit;
    }

    public MaterialDTO setUnit(String unit) {
        this.unit = unit;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public MaterialDTO setDescription(String description) {
        this.description = description;
        return this;
    }

    public Double getNumber() {
        return number;
    }

    public MaterialDTO setNumber(Double number) {
        this.number = number;
        return this;
    }

    public Boolean getIsEnable() {
        return isEnable;
    }

    public MaterialDTO setIsEnable(Boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    public int getExpireDay() {
        return expireDay;
    }

    public MaterialDTO setExpireDay(int expireDay) {
        this.expireDay = expireDay;
        return this;
    }

    public MeteringUnitDTO getMeteringUnit() {
        return meteringUnit;
    }

    public MaterialDTO setMeteringUnit(MeteringUnitDTO meteringUnit) {
        this.meteringUnit = meteringUnit;
        return this;
    }

    public Set<MaterialAttributeDTO> getMaterialAttribute() {
        return materialAttribute;
    }

    public MaterialDTO setMaterialAttribute(Set<MaterialAttributeDTO> materialAttribute) {
        this.materialAttribute = materialAttribute;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public MaterialDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }
}
