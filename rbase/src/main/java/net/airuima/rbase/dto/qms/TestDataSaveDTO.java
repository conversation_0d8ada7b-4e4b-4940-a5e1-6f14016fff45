package net.airuima.rbase.dto.qms;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/7/1
 */
@Schema(description = "接收保存测试数据DTO")
public class TestDataSaveDTO {

    /**
     * 数据分析类型名称
     */
    @Schema(description = "数据分析类型名称")
    private String analyseCategoryName;

    /**
     * 数据分析类型编码
     */
    @Schema(description = "数据分析类型编码")
    private String analyseCategoryCode;

    /**
     * SN
     */
    @Schema(description = "SN")
    private String sn;

    /**
     * ysn
     */
    @Schema(description = "ysn")
    private String ysn;

    /**
     * 工单号
     */
    @Schema(description = "工单号")
    private String workSheetSerialNumber;

    /**
     * 工位编码
     */
    @Schema(description = "工位编码")
    private String workCellCode;

    /**
     * 工位名称
     */
    @Schema(description = "工位名称")
    private String workCellName;

    /**
     * 组织架构编码
     */
    @Schema(description = "组织架构编码")
    private String organizationCode;

    /**
     * 组织架构名称
     */
    @Schema(description = "组织架构名称")
    private String organizationName;

    /**
     * 产品谱系编码
     */
    @Schema(description = "产品谱系编码")
    private String pedigreeCode;

    /**
     * 产品谱系名称
     */
    @Schema(description = "产品谱系名称")
    private String pedigreeName;

    /**
     * 设备编码
     */
    @Schema(description = "设备编码")
    private String facilityCode;

    /**
     * 设备名称
     */
    @Schema(description = "设备名称")
    private String facilityName;

    /**
     * 测试员工编码
     */
    @Schema(description = "测试员工编码")
    private String staffCode;

    /**
     * 测试员工姓名
     */
    @Schema(description = "测试员工姓名")
    private String staffName;

    /**
     * 测试结果(0:不合格;1:合格)
     */
    @Schema(description = "测试结果(0:不合格;1:合格)")
    private Boolean result;

    /**
     * 测试数据明细
     */
    @Schema(description = "测试数据明细")
    private List<TestDataDTO> testDataList;

    /**
     * 测试时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime testDate;

    public TestDataSaveDTO() {
    }

    public TestDataSaveDTO(BatchWorkDetail batchWorkDetail) {
        WorkSheet workSheet = batchWorkDetail.getWorkSheet() != null ? batchWorkDetail.getWorkSheet():
                batchWorkDetail.getSubWorkSheet().getWorkSheet();
        SubWorkSheet subWorkSheet = batchWorkDetail.getSubWorkSheet();
        this.workSheetSerialNumber =null != subWorkSheet?subWorkSheet.getSerialNumber():workSheet.getSerialNumber();
        this.workCellCode = batchWorkDetail.getWorkCell().getCode();
        this.workCellName = batchWorkDetail.getWorkCell().getName();
        this.organizationCode = ObjectUtils.isEmpty(workSheet.getOrganizationId())?workSheet.getOrganizationDto().getCode():null;
        this.organizationName = ObjectUtils.isEmpty(workSheet.getOrganizationId())?workSheet.getOrganizationDto().getName():null;
        this.pedigreeCode = workSheet.getPedigree().getCode();
        this.pedigreeName = workSheet.getPedigree().getName();
        this.staffCode = batchWorkDetail.getOperatorDto().getCode();
        this.staffName = batchWorkDetail.getOperatorDto().getName();
        this.result = true;
        this.testDate = LocalDateTime.now();
    }

    public TestDataSaveDTO(ContainerDetail containerDetail) {
        WorkSheet workSheet = containerDetail.getBatchWorkDetail().getWorkSheet() != null ? containerDetail.getBatchWorkDetail().getWorkSheet():
                containerDetail.getBatchWorkDetail().getSubWorkSheet().getWorkSheet();
        SubWorkSheet subWorkSheet = containerDetail.getBatchWorkDetail().getSubWorkSheet();
        this.workSheetSerialNumber =null != subWorkSheet?subWorkSheet.getSerialNumber():workSheet.getSerialNumber();
        this.workCellCode = containerDetail.getWorkCell().getCode();
        this.workCellName = containerDetail.getWorkCell().getName();
        this.organizationCode = ObjectUtils.isEmpty(workSheet.getOrganizationId())?workSheet.getOrganizationDto().getCode():null;
        this.organizationName = ObjectUtils.isEmpty(workSheet.getOrganizationId())?workSheet.getOrganizationDto().getName():null;
        this.pedigreeCode = workSheet.getPedigree().getCode();
        this.pedigreeName = workSheet.getPedigree().getName();
        this.staffCode = containerDetail.getStaffDto().getCode();
        this.staffName = containerDetail.getStaffDto().getName();
        this.result = true;
        this.testDate = containerDetail.getRecordDate();
    }

    public TestDataSaveDTO(SnWorkDetail snWorkDetail) {
        WorkSheet workSheet = snWorkDetail.getWorkSheet() != null ? snWorkDetail.getWorkSheet():
                snWorkDetail.getSubWorkSheet().getWorkSheet();
        SubWorkSheet subWorkSheet = snWorkDetail.getSubWorkSheet();
        this.sn = snWorkDetail.getSn();
        this.ysn = snWorkDetail.getYsn();
        this.workSheetSerialNumber =null != subWorkSheet?subWorkSheet.getSerialNumber():workSheet.getSerialNumber();
        this.workCellCode = snWorkDetail.getWorkCell().getCode();
        this.workCellName = snWorkDetail.getWorkCell().getName();
        this.organizationCode = ObjectUtils.isEmpty(workSheet.getOrganizationId())?workSheet.getOrganizationDto().getCode():null;
        this.organizationName = ObjectUtils.isEmpty(workSheet.getOrganizationId())?workSheet.getOrganizationDto().getName():null;
        this.pedigreeCode = workSheet.getPedigree().getCode();
        this.pedigreeName = workSheet.getPedigree().getName();
        this.staffCode = snWorkDetail.getOperatorDto().getCode();
        this.staffName = snWorkDetail.getOperatorDto().getName();
        this.result = true;
        this.testDate = snWorkDetail.getStartDate();
    }


    public String getAnalyseCategoryName() {
        return analyseCategoryName;
    }

    public TestDataSaveDTO setAnalyseCategoryName(String analyseCategoryName) {
        this.analyseCategoryName = analyseCategoryName;
        return this;
    }

    public String getAnalyseCategoryCode() {
        return analyseCategoryCode;
    }

    public TestDataSaveDTO setAnalyseCategoryCode(String analyseCategoryCode) {
        this.analyseCategoryCode = analyseCategoryCode;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public TestDataSaveDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public String getYsn() {
        return ysn;
    }

    public TestDataSaveDTO setYsn(String ysn) {
        this.ysn = ysn;
        return this;
    }

    public String getWorkSheetSerialNumber() {
        return workSheetSerialNumber;
    }

    public TestDataSaveDTO setWorkSheetSerialNumber(String workSheetSerialNumber) {
        this.workSheetSerialNumber = workSheetSerialNumber;
        return this;
    }

    public String getWorkCellCode() {
        return workCellCode;
    }

    public TestDataSaveDTO setWorkCellCode(String workCellCode) {
        this.workCellCode = workCellCode;
        return this;
    }

    public String getWorkCellName() {
        return workCellName;
    }

    public TestDataSaveDTO setWorkCellName(String workCellName) {
        this.workCellName = workCellName;
        return this;
    }

    public String getOrganizationCode() {
        return organizationCode;
    }

    public TestDataSaveDTO setOrganizationCode(String organizationCode) {
        this.organizationCode = organizationCode;
        return this;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public TestDataSaveDTO setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
        return this;
    }

    public String getPedigreeCode() {
        return pedigreeCode;
    }

    public TestDataSaveDTO setPedigreeCode(String pedigreeCode) {
        this.pedigreeCode = pedigreeCode;
        return this;
    }

    public String getPedigreeName() {
        return pedigreeName;
    }

    public TestDataSaveDTO setPedigreeName(String pedigreeName) {
        this.pedigreeName = pedigreeName;
        return this;
    }

    public String getFacilityCode() {
        return facilityCode;
    }

    public TestDataSaveDTO setFacilityCode(String facilityCode) {
        this.facilityCode = facilityCode;
        return this;
    }

    public String getFacilityName() {
        return facilityName;
    }

    public TestDataSaveDTO setFacilityName(String facilityName) {
        this.facilityName = facilityName;
        return this;
    }

    public String getStaffCode() {
        return staffCode;
    }

    public TestDataSaveDTO setStaffCode(String staffCode) {
        this.staffCode = staffCode;
        return this;
    }

    public String getStaffName() {
        return staffName;
    }

    public TestDataSaveDTO setStaffName(String staffName) {
        this.staffName = staffName;
        return this;
    }

    public Boolean getResult() {
        return result;
    }

    public TestDataSaveDTO setResult(Boolean result) {
        this.result = result;
        return this;
    }

    public List<TestDataDTO> getTestDataList() {
        return testDataList;
    }

    public TestDataSaveDTO setTestDataList(List<TestDataDTO> testDataList) {
        this.testDataList = testDataList;
        return this;
    }

    public LocalDateTime getTestDate() {
        return testDate;
    }

    public TestDataSaveDTO setTestDate(LocalDateTime testDate) {
        this.testDate = testDate;
        return this;
    }
}
