package net.airuima.rbase.dto.pedigree;

import net.airuima.dto.AbstractDto;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工序不良现象DTO
 *
 * <AUTHOR>
 * @date 2020/12/26
 */
public class PedigreeStepUnqualifiedItemDTO extends AbstractDto {

    /**
     * 产品谱系
     */
    private Pedigree pedigree;

    /**
     * 工序
     **/
    private Step step;

    /**
     * 不良id
     */
    private List<UnqualifiedItem> unqualifiedItems;

    public Pedigree getPedigree() {
        return pedigree;
    }

    public PedigreeStepUnqualifiedItemDTO setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public PedigreeStepUnqualifiedItemDTO setStep(Step step) {
        this.step = step;
        return this;
    }

    public List<UnqualifiedItem> getUnqualifiedItems() {
        return unqualifiedItems;
    }

    public PedigreeStepUnqualifiedItemDTO setUnqualifiedItems(List<UnqualifiedItem> unqualifiedItems) {
        this.unqualifiedItems = unqualifiedItems;
        return this;
    }
}
