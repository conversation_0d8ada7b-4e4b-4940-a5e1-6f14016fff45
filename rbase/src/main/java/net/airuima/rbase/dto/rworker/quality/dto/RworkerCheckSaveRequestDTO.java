package net.airuima.rbase.dto.rworker.quality.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/1/31
 */
@Schema(description = "保存检测相关信息dto")
public class RworkerCheckSaveRequestDTO implements Serializable {
    /**
     * 类型(0,首检;1,PQC抽检)
     */
    @Schema(description = "类型(0,首检;1,PQC抽检)")
    private Integer category;
    /**
     * 被检测工位ID
     */
    @Schema(description = "被检测工位ID")
    private Long workCellId;
    /**
     * 投产工单ID
     */
    @Schema(description = "投产工单ID")
    private Long productWorkSheetId;
    /**
     * 操作人ID
     */
    @Schema(description = "操作人ID")
    private Long operatorId;
    /**
     * 被检测工序ID
     */
    @Schema(description = "被检测工序ID")
    private Long stepId;
    /**
     * 项目类型
     */
    @Schema(description = "项目类型")
    private Integer variety;
    /**
     * 检查数量
     */
    @Schema(description = "检查数量")
    private Integer checkNumber;
    /**
     * 合格数量
     */
    @Schema(description = "合格数量")
    private Integer qualifiedNumber;
    /**
     * 不合格数量
     */
    @Schema(description = "不合格数量")
    private Integer unqualifiedNumber;
    /**
     * 检查结果
     */
    @Schema(description = "检查结果")
    private Boolean result;
    /**
     * SN及对应检测项目数据信息
     */
    @Schema(description = "SN及对应检测项目数据信息")
    private List<SnCheckItemInfo> snCheckItemInfoList;

    public Boolean getResult() {
        return result;
    }

    public RworkerCheckSaveRequestDTO setResult(Boolean result) {
        this.result = result;
        return this;
    }

    public Integer getQualifiedNumber() {
        return qualifiedNumber;
    }

    public RworkerCheckSaveRequestDTO setQualifiedNumber(Integer qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public Integer getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public RworkerCheckSaveRequestDTO setUnqualifiedNumber(Integer unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public RworkerCheckSaveRequestDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public Long getWorkCellId() {
        return workCellId;
    }

    public RworkerCheckSaveRequestDTO setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
        return this;
    }

    public Long getProductWorkSheetId() {
        return productWorkSheetId;
    }

    public RworkerCheckSaveRequestDTO setProductWorkSheetId(Long productWorkSheetId) {
        this.productWorkSheetId = productWorkSheetId;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public RworkerCheckSaveRequestDTO setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public Long getStepId() {
        return stepId;
    }

    public RworkerCheckSaveRequestDTO setStepId(Long stepId) {
        this.stepId = stepId;
        return this;
    }

    public Integer getVariety() {
        return variety;
    }

    public RworkerCheckSaveRequestDTO setVariety(Integer variety) {
        this.variety = variety;
        return this;
    }

    public Integer getCheckNumber() {
        return checkNumber;
    }

    public RworkerCheckSaveRequestDTO setCheckNumber(Integer checkNumber) {
        this.checkNumber = checkNumber;
        return this;
    }

    public List<SnCheckItemInfo> getSnCheckItemInfoList() {
        return snCheckItemInfoList;
    }

    public RworkerCheckSaveRequestDTO setSnCheckItemInfoList(List<SnCheckItemInfo> snCheckItemInfoList) {
        this.snCheckItemInfoList = snCheckItemInfoList;
        return this;
    }

    @Schema(description = "SN及对应检测项目数据信息")
    public static class SnCheckItemInfo implements Serializable{
        @Schema(description = "检测SN")
        private String sn;
        @Schema(description = "检测项目列表")
        private List<CheckItemInfo> checkItemInfoList;

        public String getSn() {
            return sn;
        }

        public SnCheckItemInfo setSn(String sn) {
            this.sn = sn;
            return this;
        }

        public List<CheckItemInfo> getCheckItemInfoList() {
            return checkItemInfoList;
        }

        public SnCheckItemInfo setCheckItemInfoList(List<CheckItemInfo> checkItemInfoList) {
            this.checkItemInfoList = checkItemInfoList;
            return this;
        }

        @Schema(description = "检测项目信息")
        public static class CheckItemInfo implements Serializable{
            private Long checkItemId;
            @Schema(description = "检测项目编码")
            private String checkItemCode;
            @Schema(description = "检测项目名称")
            private String checkItemName;
            @Schema(description = "合格范围(开闭区间或者OK)")
            private String qualifiedRange;
            @Schema(description = "检测数据值")
            private String checkData;
            @Schema(description = "检测结果")
            private Boolean result;

            public Boolean getResult() {
                return result;
            }

            public CheckItemInfo setResult(Boolean result) {
                this.result = result;
                return this;
            }

            public Long getCheckItemId() {
                return checkItemId;
            }

            public CheckItemInfo setCheckItemId(Long checkItemId) {
                this.checkItemId = checkItemId;
                return this;
            }

            public String getCheckItemCode() {
                return checkItemCode;
            }

            public CheckItemInfo setCheckItemCode(String checkItemCode) {
                this.checkItemCode = checkItemCode;
                return this;
            }

            public String getCheckItemName() {
                return checkItemName;
            }

            public CheckItemInfo setCheckItemName(String checkItemName) {
                this.checkItemName = checkItemName;
                return this;
            }

            public String getQualifiedRange() {
                return qualifiedRange;
            }

            public CheckItemInfo setQualifiedRange(String qualifiedRange) {
                this.qualifiedRange = qualifiedRange;
                return this;
            }

            public String getCheckData() {
                return checkData;
            }

            public CheckItemInfo setCheckData(String checkData) {
                this.checkData = checkData;
                return this;
            }
        }
    }
}
