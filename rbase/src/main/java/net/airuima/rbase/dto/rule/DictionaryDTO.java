package net.airuima.rbase.dto.rule;

import net.airuima.dto.AbstractDto;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
public class DictionaryDTO extends AbstractDto implements Serializable {

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 数据
     */
    private String data;

    /**
     * 数据类型 1:字符串2:数值3:布尔4:json
     */
    private int category;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否显示(1:显示;0:隐藏)
     */
    private int display;

    public String getName() {
        return name;
    }

    public DictionaryDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public DictionaryDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getData() {
        return data;
    }

    public DictionaryDTO setData(String data) {
        this.data = data;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public DictionaryDTO setCategory(int category) {
        this.category = category;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public DictionaryDTO setDescription(String description) {
        this.description = description;
        return this;
    }

    public int getDisplay() {
        return display;
    }

    public DictionaryDTO setDisplay(int display) {
        this.display = display;
        return this;
    }
}
