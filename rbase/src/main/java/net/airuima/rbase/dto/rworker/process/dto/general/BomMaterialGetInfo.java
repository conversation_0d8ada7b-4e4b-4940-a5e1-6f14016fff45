package net.airuima.rbase.dto.rworker.process.dto.general;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.procedure.batch.WsMaterial;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/4/6
 */
public class BomMaterialGetInfo implements Serializable {

    /**
     * 物料ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "物料ID")
    private Long id;

    /**
     * 物料编码
     */
    @Schema(description = "物料编码")
    private String code;

    /**
     * 物料名称
     */
    @Schema(description = "物料名称")
    private String name;

    /**
     * 物料种类
     */
    @Schema(description = "物料种类")
    private Integer category;

    /**
     * 物料规格
     */
    @Schema(description = "物料规格")
    private String specification;

    /**
     * 物料数量
     */
    @Schema(description = "物料数量")
    private Double number;

    public BomMaterialGetInfo() {

    }

    public BomMaterialGetInfo(WsMaterial wsMaterial) {
        this.id = wsMaterial.getMaterialId();
        this.code = wsMaterial.getMaterialDto().getCode();
        this.name = wsMaterial.getMaterialDto().getName();
        this.category = wsMaterial.getMaterialDto().getMaterialCategory();
        this.specification = wsMaterial.getMaterialDto().getSpecification();
        this.number = wsMaterial.getNumber();
    }

    public Long getId() {
        return id;
    }

    public BomMaterialGetInfo setId(Long id) {
        this.id = id;
        return this;
    }

    public String getCode() {
        return code;
    }

    public BomMaterialGetInfo setCode(String code) {
        this.code = code;
        return this;
    }

    public String getName() {
        return name;
    }

    public BomMaterialGetInfo setName(String name) {
        this.name = name;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public BomMaterialGetInfo setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public BomMaterialGetInfo setSpecification(String specification) {
        this.specification = specification;
        return this;
    }

    public Double getNumber() {
        return number;
    }

    public BomMaterialGetInfo setNumber(Double number) {
        this.number = number;
        return this;
    }
}
