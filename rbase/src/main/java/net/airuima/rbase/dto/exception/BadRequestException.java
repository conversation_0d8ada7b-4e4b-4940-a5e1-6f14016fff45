package net.airuima.rbase.dto.exception;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.constant.Constants;


/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/11/29
 */
public class BadRequestException extends RuntimeException{
    /**
     * 结果返回状态
     */
    @Schema(description = "处理状态(0:未处理;1:处理成功;2:处理失败)")
    private Integer status;

    /**
     * 结果返回消息
     */
    @Schema(description = "异常提醒信息")
    private String message;

    public BadRequestException() {
    }

    public BadRequestException(String message) {
        this.message = message;
        this.status = Constants.ERROR_ALERT;
    }

    public Integer getStatus() {
        return status;
    }

    public BadRequestException setStatus(Integer status) {
        this.status = status;
        return this;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public BadRequestException setMessage(String message) {
        this.message = message;
        return this;
    }

}
