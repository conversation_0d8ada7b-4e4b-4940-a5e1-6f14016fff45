package net.airuima.rbase.dto.base;

import io.swagger.v3.oas.annotations.media.Schema;


/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 结果信息DTO
 *
 * <AUTHOR>
 * @date 2023/09/14
 */
@Schema(description = "结果信息DTO")
public class BaseResultDTO<T> {

    /**
     * 结果返回状态
     */
    @Schema(description = "OK/KO")
    private String status;

    /**
     * 国际化key
     */
    @Schema(description = "国际化key")
    private String key;

    /**
     * 类名
     */
    @Schema(description = "类名")
    private String entityName;

    /**
     * 结果返回消息
     */
    @Schema(description = "提醒信息")
    private String message;


    /**
     * 结果数据
     */
    @Schema(description = "结果数据")
    private T data;


    public BaseResultDTO() {
    }

    public BaseResultDTO(String status) {
        this.status = status;
    }

    public BaseResultDTO(String status, T data) {
        this.status = status;
        this.data = data;
    }

    public BaseResultDTO(String status, String message, T data) {
        this.status = status;
        this.message = message;
        this.data = data;
    }

    public BaseResultDTO(String status, String message, String entityName, String key) {
        this.status = status;
        this.key = key;
        this.entityName = entityName;
        this.message = message;
    }

    public BaseResultDTO(String status, String key, String message) {
        this.status = status;
        this.key = key;
        this.message = message;
    }

    public BaseResultDTO(String status, String key, String message, T data) {
        this.status = status;
        this.key = key;
        this.message = message;
        this.data = data;
    }

    public String getEntityName() {
        return entityName;
    }

    public BaseResultDTO setEntityName(String entityName) {
        this.entityName = entityName;
        return this;
    }

    public T getData() {
        return data;
    }

    public BaseResultDTO setData(T data) {
        this.data = data;
        return this;
    }

    public String getKey() {
        return key;
    }

    public BaseResultDTO setKey(String key) {
        this.key = key;
        return this;
    }

    public String getStatus() {
        return status;
    }

    public BaseResultDTO setStatus(String status) {
        this.status = status;
        return this;
    }

    public String getMessage() {
        return message;
    }

    public BaseResultDTO setMessage(String message) {
        this.message = message;
        return this;
    }

}

