package net.airuima.rbase.dto.client;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.dto.standardpart.StandardPartDTO;

import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/5/23
 */
@Schema(description = "验证标准件返回参数DTO")
public class ClientStandardPartGetDTO extends BaseClientDTO {
    /**
     * 标准件SN
     */
    @Schema(description = "标准件SN")
    private String sn;

    /**
     * 标准件状态
     */
    @Schema(description = "状态(0:正常;1:超期;2:报废)")
    private Integer standardPartStatus;

    /**
     * 到期日期
     */
    @Schema(description = "到期日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private LocalDateTime expireDate;

    public ClientStandardPartGetDTO(){

    }

    public ClientStandardPartGetDTO(StandardPartDTO standardPart){
        this.sn = standardPart.getSn();
        this.standardPartStatus = standardPart.getStatus();
        this.expireDate = standardPart.getExpireDate();
        this.setStatus(Constants.OK);
    }

    public ClientStandardPartGetDTO(BaseClientDTO baseClientDto){
        this.setStatus(baseClientDto.getStatus());
        this.setMessage(baseClientDto.getMessage());
    }


    public String getSn() {
        return sn;
    }

    public ClientStandardPartGetDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public Integer getStandardPartStatus() {
        return standardPartStatus;
    }

    public ClientStandardPartGetDTO setStandardPartStatus(Integer standardPartStatus) {
        this.standardPartStatus = standardPartStatus;
        return this;
    }

    public LocalDateTime getExpireDate() {
        return expireDate;
    }

    public ClientStandardPartGetDTO setExpireDate(LocalDateTime expireDate) {
        this.expireDate = expireDate;
        return this;
    }
}
