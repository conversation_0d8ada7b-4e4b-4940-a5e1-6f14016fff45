package net.airuima.rbase.dto.report;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 良率报表DTO
 */
@Schema(description = "良率报表DTO")
public class QualityReportDTO {
    private QualityReportDTO() {
        // 静态成员的集合，不需要实例化
    }
    /**
     * 请求参数
     */
    @Schema(description = "请求参数DTO")
    public static class RequestInfo extends PageDTO {

        /**
         * 总工单号
         */
        @Schema(description = "总工单号")
        private String wsSerialNumber;

        /**
         * 产品谱系id(最小层级)
         */
        @Schema(description = "产品谱系id(最小层级)")
        private Long pedigreeId;

        /**
         * 下单日期开始范围
         */
        @Schema(description = "下单日期开始范围")
        private LocalDateTime startDate;

        /**
         * 下单日期结束范围
         */
        @Schema(description = "下单日期结束范围")
        private LocalDateTime endDate;

        public String getWsSerialNumber() {
            return wsSerialNumber;
        }

        public void setWsSerialNumber(String wsSerialNumber) {
            this.wsSerialNumber = wsSerialNumber;
        }

        public Long getPedigreeId() {
            return pedigreeId;
        }

        public void setPedigreeId(Long pedigreeId) {
            this.pedigreeId = pedigreeId;
        }

        public LocalDateTime getStartDate() {
            return startDate;
        }

        public void setStartDate(LocalDateTime startDate) {
            this.startDate = startDate;
        }

        public LocalDateTime getEndDate() {
            return endDate;
        }

        public void setEndDate(LocalDateTime endDate) {
            this.endDate = endDate;
        }

    }

    /**
     * 质量信息
     */
    @Schema(description = "质量信息")
    public static class QualifiedInfo {

        /**
         * 不良项目名称
         */
        @Schema(description = "不良项目名称")
        private String unqualifiedItemName;

        /**
         * 不良项目编码
         */
        @Schema(description = "不良项目编码")
        private String unqualifiedItemCode;

        /**
         * 不良项目数量
         */
        @Schema(description = "不良项目数量")
        private Integer unqualifiedItemNumber;

        public String getUnqualifiedItemName() {
            return unqualifiedItemName;
        }

        public void setUnqualifiedItemName(String unqualifiedItemName) {
            this.unqualifiedItemName = unqualifiedItemName;
        }

        public String getUnqualifiedItemCode() {
            return unqualifiedItemCode;
        }

        public void setUnqualifiedItemCode(String unqualifiedItemCode) {
            this.unqualifiedItemCode = unqualifiedItemCode;
        }

        public Integer getUnqualifiedItemNumber() {
            return unqualifiedItemNumber;
        }

        public void setUnqualifiedItemNumber(Integer unqualifiedItemNumber) {
            this.unqualifiedItemNumber = unqualifiedItemNumber;
        }
    }

    /**
     * 返回数据
     */
    @Schema(description = "返回数据")
    public static class ResponseInfo extends PageDTO{

        /**
         * 良率报表数据列表
         */
        @Schema(description = "良率报表数据列表")
        private List<QualityInfo> qualityInfoList;

        public List<QualityInfo> getQualityInfoList() {
            return qualityInfoList;
        }

        public ResponseInfo setQualityInfoList(List<QualityInfo> qualityInfoList) {
            this.qualityInfoList = qualityInfoList;
            return this;
        }
    }

    /**
     * 良率报表数据
     */
    @Schema(description = "良率报表数据")
    public static class QualityInfo{
        /**
         * 总工单
         */
        @Schema(description = "总工单")
        private WorkSheet workSheet;

        /**
         * 一次不良项
         */
        @Schema(description = "一次不良项")
        private List<QualifiedInfo> firstQualifiedInfo;

        /**
         * 成品不良项
         */
        @Schema(description = "成品不良项")
        private List<QualifiedInfo> qualifiedInfo;

        public WorkSheet getWorkSheet() {
            return workSheet;
        }

        public void setWorkSheet(WorkSheet workSheet) {
            this.workSheet = workSheet;
        }

        public List<QualifiedInfo> getFirstQualifiedInfo() {
            return firstQualifiedInfo;
        }

        public void setFirstQualifiedInfo(List<QualifiedInfo> firstQualifiedInfo) {
            this.firstQualifiedInfo = firstQualifiedInfo;
        }

        public List<QualifiedInfo> getQualifiedInfo() {
            return qualifiedInfo;
        }

        public void setQualifiedInfo(List<QualifiedInfo> qualifiedInfo) {
            this.qualifiedInfo = qualifiedInfo;
        }
    }

}
