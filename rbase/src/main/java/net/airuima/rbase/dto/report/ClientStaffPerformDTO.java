package net.airuima.rbase.dto.report;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.rbase.dto.client.ClientStaffPerformReportGetDTO;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/5
 */
@Schema(description = "客户员工产量信息DTO")
public class ClientStaffPerformDTO extends BaseClientDTO {

    @Schema(description = "分段时间产量信息")
    List<StaffPerformReportInfo> staffPerformReportInfo;

    public ClientStaffPerformDTO() {
    }

    public ClientStaffPerformDTO(String status, String message) {
        super(status, message);
    }

    public ClientStaffPerformDTO(String status, List<StaffPerformReportInfo> staffPerformReportInfo) {
        super(status);
        this.staffPerformReportInfo = staffPerformReportInfo;
    }

    public List<StaffPerformReportInfo> getStaffPerformReportInfo() {
        return staffPerformReportInfo;
    }

    public ClientStaffPerformDTO setStaffPerformReportInfo(List<StaffPerformReportInfo> staffPerformReportInfo) {
        this.staffPerformReportInfo = staffPerformReportInfo;
        return this;
    }

    public static class StaffPerformReportInfo{
        private ClientStaffPerformReportGetDTO.TimePeriodInfo timePeriodInfo;

        private List<StaffPerformDetailInfo> StaffPerformDetailInfos;

        public ClientStaffPerformReportGetDTO.TimePeriodInfo getTimePeriodInfo() {
            return timePeriodInfo;
        }

        public StaffPerformReportInfo setTimePeriodInfo(ClientStaffPerformReportGetDTO.TimePeriodInfo timePeriodInfo) {
            this.timePeriodInfo = timePeriodInfo;
            return this;
        }

        public List<StaffPerformDetailInfo> getStaffPerformDetailInfos() {
            return StaffPerformDetailInfos;
        }

        public StaffPerformReportInfo setStaffPerformDetailInfos(List<StaffPerformDetailInfo> staffPerformDetailInfos) {
            StaffPerformDetailInfos = staffPerformDetailInfos;
            return this;
        }

        public static class StaffPerformDetailInfo{
            private String pedigreeCode;
            private String pedigreeName;
            private Integer inputNumber;
            private Integer qualifiedNumber;
            private Integer unqualifiedNumber;
            private Double unqualifiedRate;

            public String getPedigreeCode() {
                return pedigreeCode;
            }

            public StaffPerformDetailInfo setPedigreeCode(String pedigreeCode) {
                this.pedigreeCode = pedigreeCode;
                return this;
            }

            public String getPedigreeName() {
                return pedigreeName;
            }

            public StaffPerformDetailInfo setPedigreeName(String pedigreeName) {
                this.pedigreeName = pedigreeName;
                return this;
            }

            public Integer getInputNumber() {
                return inputNumber;
            }

            public StaffPerformDetailInfo setInputNumber(Integer inputNumber) {
                this.inputNumber = inputNumber;
                return this;
            }

            public Integer getQualifiedNumber() {
                return qualifiedNumber;
            }

            public StaffPerformDetailInfo setQualifiedNumber(Integer qualifiedNumber) {
                this.qualifiedNumber = qualifiedNumber;
                return this;
            }

            public Integer getUnqualifiedNumber() {
                return unqualifiedNumber;
            }

            public StaffPerformDetailInfo setUnqualifiedNumber(Integer unqualifiedNumber) {
                this.unqualifiedNumber = unqualifiedNumber;
                return this;
            }

            public Double getUnqualifiedRate() {
                return unqualifiedRate;
            }

            public StaffPerformDetailInfo setUnqualifiedRate(Double unqualifiedRate) {
                this.unqualifiedRate = unqualifiedRate;
                return this;
            }
        }

    }

}
