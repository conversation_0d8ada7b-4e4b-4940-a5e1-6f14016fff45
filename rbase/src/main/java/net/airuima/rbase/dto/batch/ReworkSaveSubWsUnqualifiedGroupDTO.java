package net.airuima.rbase.dto.batch;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-05-07
 */
@Schema(description = "保存待生成在线返修单的不良组别信息DTO")
public class ReworkSaveSubWsUnqualifiedGroupDTO {
    @Schema(description = "总工单ID")
    private Long workSheetId;

    @Schema(description = "不良组别及对应的子工单列表")
    private List<UnqualifiedGroupInfoDTO> unqualifiedGroupInfoDtoList;

    public Long getWorkSheetId() {
        return workSheetId;
    }

    public ReworkSaveSubWsUnqualifiedGroupDTO setWorkSheetId(Long workSheetId) {
        this.workSheetId = workSheetId;
        return this;
    }

    public List<UnqualifiedGroupInfoDTO> getUnqualifiedGroupInfoDtoList() {
        return unqualifiedGroupInfoDtoList;
    }

    public ReworkSaveSubWsUnqualifiedGroupDTO setUnqualifiedGroupInfoDtoList(List<UnqualifiedGroupInfoDTO> unqualifiedGroupInfoDtoList) {
        this.unqualifiedGroupInfoDtoList = unqualifiedGroupInfoDtoList;
        return this;
    }

    @Schema(description = "待生成在线返修单的不良组别及子工单列表")
    public static class UnqualifiedGroupInfoDTO{
        @Schema(description = "不良组别ID")
        private Long unqualifiedGroupId;
        @Schema(description = "产生不良的工序ID")
        private Long stepId;
        @Schema(description = "不良组别汇总数量")
        private Integer number;
        @Schema(description = "工艺路线ID")
        private Long workFlowId;
        @Schema(description = "属于当前不良组别的子工单ID列表")
        private List<subWorkSheetStepDTO> subWorkSheetStepDtoList;


        public static class subWorkSheetStepDTO{

            private Long subWorkSheetId;

            private List<ReworkGetSubWsDTO.SubWsUnqualifiedGroupInfoDTO.UnqualifiedItemStepDTO> unqualifiedItemStepDtoList;

            public Long getSubWorkSheetId() {
                return subWorkSheetId;
            }

            public subWorkSheetStepDTO setSubWorkSheetId(Long subWorkSheetId) {
                this.subWorkSheetId = subWorkSheetId;
                return this;
            }

            public List<ReworkGetSubWsDTO.SubWsUnqualifiedGroupInfoDTO.UnqualifiedItemStepDTO> getUnqualifiedItemStepDtoList() {
                return unqualifiedItemStepDtoList;
            }

            public subWorkSheetStepDTO setUnqualifiedItemStepDtoList(List<ReworkGetSubWsDTO.SubWsUnqualifiedGroupInfoDTO.UnqualifiedItemStepDTO> unqualifiedItemStepDtoList) {
                this.unqualifiedItemStepDtoList = unqualifiedItemStepDtoList;
                return this;
            }
        }
        public Long getUnqualifiedGroupId() {
            return unqualifiedGroupId;
        }

        public UnqualifiedGroupInfoDTO setUnqualifiedGroupId(Long unqualifiedGroupId) {
            this.unqualifiedGroupId = unqualifiedGroupId;
            return this;
        }

        public Long getStepId() {
            return stepId;
        }

        public UnqualifiedGroupInfoDTO setStepId(Long stepId) {
            this.stepId = stepId;
            return this;
        }

        public Integer getNumber() {
            return number;
        }

        public UnqualifiedGroupInfoDTO setNumber(Integer number) {
            this.number = number;
            return this;
        }

        public Long getWorkFlowId() {
            return workFlowId;
        }

        public UnqualifiedGroupInfoDTO setWorkFlowId(Long workFlowId) {
            this.workFlowId = workFlowId;
            return this;
        }

        public List<subWorkSheetStepDTO> getSubWorkSheetStepDTOList() {
            return subWorkSheetStepDtoList;
        }

        public UnqualifiedGroupInfoDTO setSubWorkSheetStepDTOList(List<subWorkSheetStepDTO> subWorkSheetStepDtoList) {
            this.subWorkSheetStepDtoList = subWorkSheetStepDtoList;
            return this;
        }
    }
}
