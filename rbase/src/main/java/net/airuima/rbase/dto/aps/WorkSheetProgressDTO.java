package net.airuima.rbase.dto.aps;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-04-28
 */
@Schema(description = "工单生产进度DTO")
public class WorkSheetProgressDTO {

    /**
     * 工单ID
     */
    @Schema(description = "工单ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 工单号
     */
    @Schema(description = "工单号")
    private String serialNumber;
    /**
     * 工单类型
     */
    @Schema(description = "工单类型")
    private Integer category;

    /**
     * 生产线名称
     */
    @Schema(description = "生产线名称")
    private String workLineName;

    /**
     * 生产线编码
     */
    @Schema(description = "生产线编码")
    private String workLineCode;

    /**
     * 开始日期
     */
    @Schema(description = "生产开始日期")
    private LocalDateTime actualStartDate;

    /**
     * 完成日期
     */
    @Schema(description = "生产完成日期")
    private LocalDateTime actualEndDate;

    /**
     * 是否完成
     */
    @Schema(description = "是否完成")
    private Boolean isFinish;

    /**
     * 投产数
     */
    @Schema(description = "工单投产数")
    private Integer number;

    /**
     * 完成数
     */
    @Schema(description = "工单完成数")
    private Integer finishNumber;

    /**
     * 不合格数
     */
    @Schema(description = "工单合格数")
    private Integer qualifiedNumber;

    /**
     * 完成进度
     */
    @Schema(description = "工单完成进度")
    private BigDecimal progress;

    public String getSerialNumber() {
        return serialNumber;
    }

    public WorkSheetProgressDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public WorkSheetProgressDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public String getWorkLineName() {
        return workLineName;
    }

    public WorkSheetProgressDTO setWorkLineName(String workLineName) {
        this.workLineName = workLineName;
        return this;
    }

    public String getWorkLineCode() {
        return workLineCode;
    }

    public WorkSheetProgressDTO setWorkLineCode(String workLineCode) {
        this.workLineCode = workLineCode;
        return this;
    }

    public LocalDateTime getActualStartDate() {
        return actualStartDate;
    }

    public WorkSheetProgressDTO setActualStartDate(LocalDateTime actualStartDate) {
        this.actualStartDate = actualStartDate;
        return this;
    }

    public LocalDateTime getActualEndDate() {
        return actualEndDate;
    }

    public WorkSheetProgressDTO setActualEndDate(LocalDateTime actualEndDate) {
        this.actualEndDate = actualEndDate;
        return this;
    }

    @Schema(description = "是否完成")
    public Boolean getFinish() {
        return isFinish;
    }

    public WorkSheetProgressDTO setFinish(Boolean finish) {
        isFinish = finish;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public WorkSheetProgressDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public Integer getFinishNumber() {
        return finishNumber;
    }

    public WorkSheetProgressDTO setFinishNumber(Integer finishNumber) {
        this.finishNumber = finishNumber;
        return this;
    }

    public Integer getQualifiedNumber() {
        return qualifiedNumber;
    }

    public WorkSheetProgressDTO setQualifiedNumber(Integer qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public BigDecimal getProgress() {
        return progress;
    }

    public WorkSheetProgressDTO setProgress(BigDecimal progress) {
        this.progress = progress;
        return this;
    }

    public Long getId() {
        return id;
    }

    public WorkSheetProgressDTO setId(Long id) {
        this.id = id;
        return this;
    }
}
