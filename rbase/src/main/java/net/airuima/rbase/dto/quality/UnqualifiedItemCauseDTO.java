package net.airuima.rbase.dto.quality;

import jakarta.validation.constraints.NotNull;

import java.util.Set;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2020/12/25
 */
public class UnqualifiedItemCauseDTO {

    @NotNull
    Long unqualifiedItemId;

    @NotNull
    Set<Long> unqualifiedCauseIds;

    public Long getUnqualifiedItemId() {
        return unqualifiedItemId;
    }

    public UnqualifiedItemCauseDTO setUnqualifiedItemId(Long unqualifiedItemId) {
        this.unqualifiedItemId = unqualifiedItemId;
        return this;
    }


    public Set<Long> getUnqualifiedCauseIds() {
        return unqualifiedCauseIds;
    }

    public UnqualifiedItemCauseDTO setUnqualifiedCauseIds(Set<Long> unqualifiedCauseIds) {
        this.unqualifiedCauseIds = unqualifiedCauseIds;
        return this;
    }
}
