package net.airuima.rbase.dto.calibrate;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.AbstractDto;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.dto.rfms.FacilityDTO;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
public class CalibrateRuleDTO extends AbstractDto {

    /**
     * 校准工位
     */
    @Schema(description = "校准工位" ,nullable = true,requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private WorkCell workCell;

    /**
     * 校准设备ID
     */
    @Schema(description = "校准设备ID",nullable = true,requiredMode= Schema.RequiredMode.NOT_REQUIRED,maxLength = 20,type = "integer",format = "int64")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long facilityId;

    /**
     * 校准设备设备DTO
     */
    @Schema(description = "校准设备DTO")
    private FacilityDTO facilityDto = new FacilityDTO();

    /**
     * 校准程序，0：内校，1：外校
     */
    @Schema(description = "校准程序，0：内校，1：外校",requiredMode = Schema.RequiredMode.REQUIRED,
            maximum = "1",minimum = "0" ,defaultValue = "0",type = "integer",format = "int32")
    private int process;

    /**
     * 校准周期数
     */
    @Schema(description = "校准周期数", requiredMode = Schema.RequiredMode.REQUIRED,defaultValue = "1",type = "integer",format = "int32")
    private int periodNumber;

    /**
     * 校准周期单位(0:天，1:月，2:年)
     */
    @Schema(description = "校准周期单位(0:天，1:月，2:年)", requiredMode = Schema.RequiredMode.REQUIRED,maximum = "2",minimum = "0" ,defaultValue = "0",type = "integer",format = "int32")
    private int periodUnit;

    /**
     * 提醒周期数(正整数)
     */
    @Schema(description = "提醒周期数", requiredMode = Schema.RequiredMode.REQUIRED,defaultValue = "1",type = "integer",format = "int32")
    private int remindPeriodNumber;

    /**
     * 提醒周期单位(0:天，1:月，2:年)
     */
    @Schema(description = "提醒周期单位(0:天，1:月，2:年)", requiredMode = Schema.RequiredMode.REQUIRED,maximum = "2",minimum = "0" ,defaultValue = "0",type = "integer",format = "int32")
    private int remindPeriodUnit;

    public WorkCell getWorkCell() {
        return workCell;
    }

    public CalibrateRuleDTO setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public Long getFacilityId() {
        return facilityId;
    }

    public CalibrateRuleDTO setFacilityId(Long facilityId) {
        this.facilityId = facilityId;
        return this;
    }

    public FacilityDTO getFacilityDto() {
        return facilityDto;
    }

    public CalibrateRuleDTO setFacilityDto(FacilityDTO facilityDto) {
        this.facilityDto = facilityDto;
        return this;
    }

    public int getProcess() {
        return process;
    }

    public CalibrateRuleDTO setProcess(int process) {
        this.process = process;
        return this;
    }

    public int getPeriodNumber() {
        return periodNumber;
    }

    public CalibrateRuleDTO setPeriodNumber(int periodNumber) {
        this.periodNumber = periodNumber;
        return this;
    }

    public int getPeriodUnit() {
        return periodUnit;
    }

    public CalibrateRuleDTO setPeriodUnit(int periodUnit) {
        this.periodUnit = periodUnit;
        return this;
    }

    public int getRemindPeriodNumber() {
        return remindPeriodNumber;
    }

    public CalibrateRuleDTO setRemindPeriodNumber(int remindPeriodNumber) {
        this.remindPeriodNumber = remindPeriodNumber;
        return this;
    }

    public int getRemindPeriodUnit() {
        return remindPeriodUnit;
    }

    public CalibrateRuleDTO setRemindPeriodUnit(int remindPeriodUnit) {
        this.remindPeriodUnit = remindPeriodUnit;
        return this;
    }
}
