package net.airuima.rbase.dto.qms;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.AbstractDto;

import java.io.Serializable;
import java.time.LocalDate;

@Schema(name = "抽样方案表(SampleCase)", description = "抽样方案表")
public class SampleCaseDTO extends AbstractDto implements Serializable {

    /**
     * 抽样方案名称
     */
    @Schema(description = "抽样方案名称")
    private String name;

    /**
     * 抽样方案编码
     */
    @Schema(description = "抽样方案编码")
    private String code;

    /**
     * 抽样类型:全检0/固定数量1/按百分比抽样2/按国标抽样3
     */
    @Schema(description = "抽样类型:全检0/固定数量1/按百分比抽样2/按国标抽样3")
    private int category;

    /**
     * 抽样数量
     */
    @Schema(description = "抽样数量")
    private int number;

    /**
     * 允收数ac
     */
    @Schema(description = "允收数ac")
    private int ac;

    /**
     * 抽样百分比
     */
    @Schema(description = "抽样百分比")
    private double rate;

    /**
     * 合格百分比
     */
    @Schema(description = "合格百分比")
    private double qualifiedRate;

    /**
     * 国标类型
     */
    @Schema(description = "国标类型")
    private GbtDTO gbt;

    /**
     * 检验水平
     */
    @Schema(description = "检验水平")
    private String insolationLevel;

    /**
     * 接收质量限AQL
     */
    @Schema(description = "接收质量限AQL")
    private double aql;

    /**
     * 有效期
     */
    @Schema(description = "有效期")
    private LocalDate expiryDate;

    /**
     * 是否启用(0:禁用;1:启用)
     */
    @Schema(description = "是否启用(0:禁用;1:启用)")
    private boolean isEnable;

    public String getName() {
        return name;
    }

    public SampleCaseDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public SampleCaseDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public SampleCaseDTO setCategory(int category) {
        this.category = category;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public SampleCaseDTO setNumber(int number) {
        this.number = number;
        return this;
    }

    public int getAc() {
        return ac;
    }

    public SampleCaseDTO setAc(int ac) {
        this.ac = ac;
        return this;
    }

    public double getRate() {
        return rate;
    }

    public SampleCaseDTO setRate(double rate) {
        this.rate = rate;
        return this;
    }

    public double getQualifiedRate() {
        return qualifiedRate;
    }

    public SampleCaseDTO setQualifiedRate(double qualifiedRate) {
        this.qualifiedRate = qualifiedRate;
        return this;
    }

    public GbtDTO getGbt() {
        return gbt;
    }

    public SampleCaseDTO setGbt(GbtDTO gbt) {
        this.gbt = gbt;
        return this;
    }

    public String getInsolationLevel() {
        return insolationLevel;
    }

    public SampleCaseDTO setInsolationLevel(String insolationLevel) {
        this.insolationLevel = insolationLevel;
        return this;
    }

    public double getAql() {
        return aql;
    }

    public SampleCaseDTO setAql(double aql) {
        this.aql = aql;
        return this;
    }

    public LocalDate getExpiryDate() {
        return expiryDate;
    }

    public SampleCaseDTO setExpiryDate(LocalDate expiryDate) {
        this.expiryDate = expiryDate;
        return this;
    }

    public boolean isEnable() {
        return isEnable;
    }

    public SampleCaseDTO setEnable(boolean enable) {
        isEnable = enable;
        return this;
    }
}
