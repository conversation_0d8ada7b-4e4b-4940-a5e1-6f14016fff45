package net.airuima.rbase.dto.rfms;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.AbstractDto;
import net.airuima.rbase.dto.organization.StaffDTO;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 设备履历
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Schema(name = "设备履历")
public class FacilityLedgerDTO extends AbstractDto implements Serializable {

    /**
     * 流水号
     */
    @Schema(description = "流水号")
    private String serialNumber;

    /**
     * 设备
     */
    @Schema(description = "设备", required = true)
    private FacilityDTO facility;

    /**
     * 类型(0:设备巡检;1:设备点检;2:故障报告;3:故障恢复;4:设备保养;5:设备停用;6:停用恢复;7:设备报废;8:报废恢复)
     */
    @Schema(description = "类型(0:设备巡检;1:设备点检;2:故障报告;3:故障恢复;4:设备保养;5:设备停用;6:停用恢复;7:设备报废;8:报废恢复)", required = true)
    private int category;

    /**
     * 操作人ID
     */
    @Schema(description = "操作人id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long operatorId;

    /**
     * 操作人DTO
     */
    private StaffDTO operatorDto;

    /**
     * 记录日期
     */
    @Schema(description = "记录日期", required = true)
    private LocalDateTime recordDate;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    private String note;

    public String getSerialNumber() {
        return serialNumber;
    }

    public FacilityLedgerDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public FacilityDTO getFacility() {
        return facility;
    }

    public FacilityLedgerDTO setFacility(FacilityDTO facility) {
        this.facility = facility;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public FacilityLedgerDTO setCategory(int category) {
        this.category = category;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public FacilityLedgerDTO setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public StaffDTO getOperatorDto() {
        return operatorDto;
    }

    public FacilityLedgerDTO setOperatorDto(StaffDTO operatorDto) {
        this.operatorDto = operatorDto;
        return this;
    }

    public LocalDateTime getRecordDate() {
        return recordDate;
    }

    public FacilityLedgerDTO setRecordDate(LocalDateTime recordDate) {
        this.recordDate = recordDate;
        return this;
    }

    public String getNote() {
        return note;
    }

    public FacilityLedgerDTO setNote(String note) {
        this.note = note;
        return this;
    }
}
