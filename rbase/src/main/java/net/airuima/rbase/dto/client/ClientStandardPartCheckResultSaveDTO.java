package net.airuima.rbase.dto.client;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;

import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/5/23
 */
@Schema(description = "Rworker上传保存标准件测试数据参数DTO/导入标准件测试数据DTO")
public class ClientStandardPartCheckResultSaveDTO {
    /**
     * 标准件编码
     */
    @NotEmpty
    @Schema(description = "标准件编码")
    @Excel(name = "标准件编码",orderNum = "1")
    private String sn;

    /**
     * 工具编号
     */
    @NotEmpty
    @Schema(description = "工具编号")
    @Excel(name = "工具编号",orderNum = "2")
    private String tool;

    /**
     * 测试时间
     */
    @NotEmpty
    @Schema(description = "测试时间")
    @Excel(name = "测试时间",orderNum = "3",format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime testTime;

    /**
     * 测试员工编号
     */
    @NotEmpty
    @Schema(description = "测试员工编号")
    @Excel(name = "测试员工编号",orderNum = "4")
    private String tester;

    /**
     * 物料编码
     */
    @NotEmpty
    @Schema(description = "物料编码")
    @Excel(name = "物料编码",orderNum = "5")
    private String materialCode;

    /**
     * 测试次数
     */
    @NotEmpty
    @Schema(description = "测试次数")
    @Excel(name = "测试次数",orderNum = "6")
    private Integer times;

    /**
     * 检测项目编码
     */
    @NotEmpty
    @Schema(description = "检测项目编码")
    @Excel(name = "检测项目编码",orderNum = "7")
    private String checkItemCode;

    /**
     * 标准件子模块
     */
    @NotEmpty
    @Schema(description = "标准件子模块")
    @Excel(name = "子模块",orderNum = "8")
    private String subModule;

    /**
     * 测试值
     */
    @NotEmpty
    @Schema(description = "测试值")
    @Excel(name = "测试值",orderNum = "9",numFormat = "#.###")
    private Double number;


    /**
     * 上限值
     */
    @NotEmpty
    @Schema(description = "上限值")
    @Excel(name = "上限值",orderNum = "10",numFormat = "#.###")
    private Double upperNumber;

    /**
     * 中心值
     */
    @NotEmpty
    @Schema(description = "中心值")
    @Excel(name = "中心值",orderNum = "11",numFormat = "#.###")
    private Double middleNumber;

    /**
     * 下限值
     */
    @NotEmpty
    @Schema(description = "下限值")
    @Excel(name = "下限值",orderNum = "12",numFormat = "#.###")
    private Double lowerNumber;

    public ClientStandardPartCheckResultSaveDTO() {

    }

    public String getSn() {
        return sn;
    }

    public ClientStandardPartCheckResultSaveDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public String getTool() {
        return tool;
    }

    public ClientStandardPartCheckResultSaveDTO setTool(String tool) {
        this.tool = tool;
        return this;
    }

    public LocalDateTime getTestTime() {
        return testTime;
    }

    public ClientStandardPartCheckResultSaveDTO setTestTime(LocalDateTime testTime) {
        this.testTime = testTime;
        return this;
    }

    public String getTester() {
        return tester;
    }

    public ClientStandardPartCheckResultSaveDTO setTester(String tester) {
        this.tester = tester;
        return this;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public ClientStandardPartCheckResultSaveDTO setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
        return this;
    }

    public Integer getTimes() {
        return times;
    }

    public ClientStandardPartCheckResultSaveDTO setTimes(Integer times) {
        this.times = times;
        return this;
    }

    public String getCheckItemCode() {
        return checkItemCode;
    }

    public ClientStandardPartCheckResultSaveDTO setCheckItemCode(String checkItemCode) {
        this.checkItemCode = checkItemCode;
        return this;
    }


    public String getSubModule() {
        return subModule;
    }

    public ClientStandardPartCheckResultSaveDTO setSubModule(String subModule) {
        this.subModule = subModule;
        return this;
    }

    public Double getNumber() {
        return number;
    }

    public ClientStandardPartCheckResultSaveDTO setNumber(Double number) {
        this.number = number;
        return this;
    }

    public Double getUpperNumber() {
        return upperNumber;
    }

    public ClientStandardPartCheckResultSaveDTO setUpperNumber(Double upperNumber) {
        this.upperNumber = upperNumber;
        return this;
    }

    public Double getMiddleNumber() {
        return middleNumber;
    }

    public ClientStandardPartCheckResultSaveDTO setMiddleNumber(Double middleNumber) {
        this.middleNumber = middleNumber;
        return this;
    }

    public Double getLowerNumber() {
        return lowerNumber;
    }

    public ClientStandardPartCheckResultSaveDTO setLowerNumber(Double lowerNumber) {
        this.lowerNumber = lowerNumber;
        return this;
    }
}
