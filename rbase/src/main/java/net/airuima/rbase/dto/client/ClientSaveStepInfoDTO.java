package net.airuima.rbase.dto.client;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.custom.CustomDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * RWork保存工序相关接口DTO
 *
 * <AUTHOR>
 * @date 2020/12/30
 */
@Schema(description = "Rwork保存工序参数")
public class ClientSaveStepInfoDTO extends CustomDTO {

    /**
     * 当前工序投产数
     */
    @Schema(description = "工序投产数")
    private Integer number;

    /**
     * 工序合格数量
     */
    @Schema(description = "工序合格数")
    private Integer qualifiedNumber;

    /**
     * 工序不合格数量
     */
    @Schema(description = "工序不合格数")
    private Integer unqualifiedNumber;

    /**
     * 当前子工单id
     */
    @Schema(description = "子工单ID")
    private Long subWsId;

    /**
     * 当前工序的id
     */
    @Schema(description = "工序ID")
    private Long stepId;

    /**
     * 当前操作人id
     */
    @Schema(description = "操作人ID")
    private Long staffId;

    /**
     * 员工工位id
     */
    @Schema(description = "员工工位ID")
    private Long workCellId;

    /**
     * 请求的容器id列表
     */
    @Schema(description = "请求的容器ID列表")
    private List<RequestContainerInfo> requestContainerInfos;

    /**
     * 绑定的容器id
     */
    @Schema(description = "绑定的容器ID列表")
    private Long bindContainerId;

    /**
     * 工序开始时间
     */
    @Schema(description = "工序开始时间")
    private LocalDateTime startTime;

    /**
     * 工序完成日期
     */
    @Schema(description = "工序完成时间")
    private LocalDateTime endTime;

    /**
     * 不良项目及数量信息
     */
    @Schema(description = "不良项目及对应数量信息")
    private List<UnqualifiedItemInfo> unqualifiedItemInfoList;

    /**
     * SN信息
     */
    @Schema(description = "SN信息集合")
    private List<SnInfo> snInfoList;

    /**
     * 物料批次信息
     */
    @Schema(description = "物料批次信息集合")
    private List<MaterialBatchInfo> materialBatchInfoList;

    /**
     * 设备信息
     */
    @Schema(description = "设备信息集合")
    private List<EquipmentInfo> equipmentInfoList;

    /**
     * FQC处理结果(0:批退;1:放行;2:网页端处理)
     */
    @Schema(description = "QC处理结果(0:批退;1:放行;2:网页端处理)")
    private Integer fqcDealResult;

    @Schema(description = "FQC抽检数量")
    private Integer fqcCheckNumber;

    /**
     * 批退处理流程id
     */
    @Schema(description = "批退处理流程框图id")
    private Long fqcWorkFlowId;

    /**
     * FQC处理原因
     */
    @Schema(description = "FQC处理原因")
    private String fqcReason;

    /**
     * 易损件信息
     */
    @Schema(description = "易损件信息集合")
    private List<WearingPartInfo> wearingPartInfoList;

    /**
     * 保存抽检结果数据信息
     */
    @Schema(description = "保存抽检结果数据信息")
    private RandomInspectSaveInfo randomInspectSaveInfo;

    /**
     * 防重复提交Token(采用32位uuid)
     */
    @Schema(description = "防重复提交Token")
    private String xsrfToken;

    /**
     * 项目类型
     */
    @Schema(description = "项目类型")
    private Integer variety;

    /**
     * 烘烤温循老化类型(0:放入;1:取出)
     */
    @Schema(description = "烘烤温循老化类型(0:放入;1:取出)")
    private Integer bakeFlag;
    @Schema(description = "烘烤历史信息集合")
    private List<BakeHistoryInfoDTO> bakeHistoryInfoList;
    @Schema(description = "温循历史信息集合")
    private List<CycleBakeHistoryInfoDTO> cycleBakeHistoryInfoList;
    @Schema(description = "老化历史信息集合")
    private List<AgeingHistoryInfoDTO> ageingHistoryInfoList;

    public Integer getBakeFlag() {
        return bakeFlag;
    }

    public ClientSaveStepInfoDTO setBakeFlag(Integer bakeFlag) {
        this.bakeFlag = bakeFlag;
        return this;
    }

    public List<BakeHistoryInfoDTO> getBakeHistoryInfoList() {
        return bakeHistoryInfoList;
    }

    public ClientSaveStepInfoDTO setBakeHistoryInfoList(List<BakeHistoryInfoDTO> bakeHistoryInfoList) {
        this.bakeHistoryInfoList = bakeHistoryInfoList;
        return this;
    }

    public List<CycleBakeHistoryInfoDTO> getCycleBakeHistoryInfoList() {
        return cycleBakeHistoryInfoList;
    }

    public ClientSaveStepInfoDTO setCycleBakeHistoryInfoList(List<CycleBakeHistoryInfoDTO> cycleBakeHistoryInfoList) {
        this.cycleBakeHistoryInfoList = cycleBakeHistoryInfoList;
        return this;
    }

    public List<AgeingHistoryInfoDTO> getAgeingHistoryInfoList() {
        return ageingHistoryInfoList;
    }

    public ClientSaveStepInfoDTO setAgeingHistoryInfoList(List<AgeingHistoryInfoDTO> ageingHistoryInfoList) {
        this.ageingHistoryInfoList = ageingHistoryInfoList;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public ClientSaveStepInfoDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public Integer getVariety() {
        return variety;
    }

    public ClientSaveStepInfoDTO setVariety(Integer variety) {
        this.variety = variety;
        return this;
    }

    public Integer getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public ClientSaveStepInfoDTO setUnqualifiedNumber(Integer unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public Long getSubWsId() {
        return subWsId;
    }

    public ClientSaveStepInfoDTO setSubWsId(Long subWsId) {
        this.subWsId = subWsId;
        return this;
    }

    public Long getStepId() {
        return stepId;
    }

    public ClientSaveStepInfoDTO setStepId(Long stepId) {
        this.stepId = stepId;
        return this;
    }

    public Long getStaffId() {
        return staffId;
    }

    public ClientSaveStepInfoDTO setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public Long getWorkCellId() {
        return workCellId;
    }

    public ClientSaveStepInfoDTO setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
        return this;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public ClientSaveStepInfoDTO setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
        return this;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public ClientSaveStepInfoDTO setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
        return this;
    }

    public List<UnqualifiedItemInfo> getUnqualifiedItemInfoList() {
        return unqualifiedItemInfoList;
    }

    public ClientSaveStepInfoDTO setUnqualifiedItemInfoList(List<UnqualifiedItemInfo> unqualifiedItemInfoList) {
        this.unqualifiedItemInfoList = unqualifiedItemInfoList;
        return this;
    }

    public List<SnInfo> getSnInfoList() {
        return snInfoList;
    }

    public ClientSaveStepInfoDTO setSnInfoList(List<SnInfo> snInfoList) {
        this.snInfoList = snInfoList;
        return this;
    }

    public List<MaterialBatchInfo> getMaterialBatchInfoList() {
        return materialBatchInfoList;
    }

    public ClientSaveStepInfoDTO setMaterialBatchInfoList(List<MaterialBatchInfo> materialBatchInfoList) {
        this.materialBatchInfoList = materialBatchInfoList;
        return this;
    }

    public List<EquipmentInfo> getEquipmentInfoList() {
        return equipmentInfoList;
    }

    public ClientSaveStepInfoDTO setEquipmentInfoList(List<EquipmentInfo> equipmentInfoList) {
        this.equipmentInfoList = equipmentInfoList;
        return this;
    }

    public List<RequestContainerInfo> getRequestContainerInfos() {
        return requestContainerInfos;
    }

    public ClientSaveStepInfoDTO setRequestContainerInfos(List<RequestContainerInfo> requestContainerInfos) {
        this.requestContainerInfos = requestContainerInfos;
        return this;
    }

    public Long getBindContainerId() {
        return bindContainerId;
    }

    public ClientSaveStepInfoDTO setBindContainerId(Long bindContainerId) {
        this.bindContainerId = bindContainerId;
        return this;
    }

    public Integer getQualifiedNumber() {
        return qualifiedNumber;
    }

    public ClientSaveStepInfoDTO setQualifiedNumber(Integer qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public Integer getFqcDealResult() {
        return fqcDealResult;
    }

    public ClientSaveStepInfoDTO setFqcDealResult(Integer fqcDealResult) {
        this.fqcDealResult = fqcDealResult;
        return this;
    }

    public Integer getFqcCheckNumber() {
        return fqcCheckNumber;
    }

    public ClientSaveStepInfoDTO setFqcCheckNumber(Integer fqcCheckNumber) {
        this.fqcCheckNumber = fqcCheckNumber;
        return this;
    }

    public Long getFqcWorkFlowId() {
        return fqcWorkFlowId;
    }

    public ClientSaveStepInfoDTO setFqcWorkFlowId(Long fqcWorkFlowId) {
        this.fqcWorkFlowId = fqcWorkFlowId;
        return this;
    }

    public String getFqcReason() {
        return fqcReason;
    }

    public ClientSaveStepInfoDTO setFqcReason(String fqcReason) {
        this.fqcReason = fqcReason;
        return this;
    }

    public List<WearingPartInfo> getWearingPartInfoList() {
        return wearingPartInfoList;
    }

    public ClientSaveStepInfoDTO setWearingPartInfoList(List<WearingPartInfo> wearingPartInfoList) {
        this.wearingPartInfoList = wearingPartInfoList;
        return this;
    }

    public RandomInspectSaveInfo getRandomInspectSaveInfo() {
        return randomInspectSaveInfo;
    }

    public ClientSaveStepInfoDTO setRandomInspectSaveInfo(RandomInspectSaveInfo randomInspectSaveInfo) {
        this.randomInspectSaveInfo = randomInspectSaveInfo;
        return this;
    }

    public String getXsrfToken() {
        return xsrfToken;
    }

    public ClientSaveStepInfoDTO setXsrfToken(String xsrfToken) {
        this.xsrfToken = xsrfToken;
        return this;
    }

    /**
     * 不良项目信息
     */
    @Schema(description = "不良项目信息")
    public static class UnqualifiedItemInfo {

        /**
         * 不良项目id
         */
        @Schema(description = "不良项目ID")
        private Long unqualifiedItemId;

        /**
         * 不良项目数量
         */
        @Schema(description = "不良项目数量")
        private Integer number;

        public Long getUnqualifiedItemId() {
            return unqualifiedItemId;
        }

        public UnqualifiedItemInfo setUnqualifiedItemId(Long unqualifiedItemId) {
            this.unqualifiedItemId = unqualifiedItemId;
            return this;
        }

        public Integer getNumber() {
            return number;
        }

        public UnqualifiedItemInfo setNumber(Integer number) {
            this.number = number;
            return this;
        }
    }

    /**
     * SN信息
     */
    @Schema(description = "SN信息")
    public static class SnInfo {

        /**
         * SN
         */
        @Schema(description = "SN")
        private String sn;

        /**
         * 开始工作时间
         */
        @Schema(description = "开始时间")
        private LocalDateTime startTime;

        /**
         * 结束工作时间
         */
        @Schema(description = "完成时间")
        private LocalDateTime endTime;

        /**
         * 不良项目id
         */
        @Schema(description = "不良项目ID")
        private Long unqualifiedItemId;

        /**
         * 检测结果(0不合格,1合格)
         */
        @Schema(description = "结果(0不合格,1合格)")
        private Integer result;

        @Schema(description = "是否为抽检SN")
        private Boolean isChecked;

        /**
         * 客户产品代码降档编码
         */
        @Schema(description = "客户产品代码降档编码")
        private String custom1;

        /**
         * 备用字段
         */
        @Schema(description = "备用字段")
        private String custom2;
        /**
         * 备用字段
         */
        @Schema(description = "备用字段")
        private String custom3;
        /**
         * 备用字段
         */
        @Schema(description = "备用字段")
        private String custom4;
        /**
         * 备用字段
         */
        @Schema(description = "备用字段")
        private String custom5;

        public String getSn() {
            return sn;
        }

        public SnInfo setSn(String sn) {
            this.sn = sn;
            return this;
        }

        public LocalDateTime getStartTime() {
            return startTime;
        }

        public SnInfo setStartTime(LocalDateTime startTime) {
            this.startTime = startTime;
            return this;
        }

        public LocalDateTime getEndTime() {
            return endTime;
        }

        public SnInfo setEndTime(LocalDateTime endTime) {
            this.endTime = endTime;
            return this;
        }

        public Long getUnqualifiedItemId() {
            return unqualifiedItemId;
        }

        public SnInfo setUnqualifiedItemId(Long unqualifiedItemId) {
            this.unqualifiedItemId = unqualifiedItemId;
            return this;
        }

        public Integer getResult() {
            return result;
        }

        public SnInfo setResult(Integer result) {
            this.result = result;
            return this;
        }

        public Boolean getIsChecked() {
            return isChecked;
        }

        public SnInfo setIsChecked(Boolean isChecked) {
            this.isChecked = isChecked;
            return this;
        }

        public String getCustom1() {
            return custom1;
        }

        public SnInfo setCustom1(String custom1) {
            this.custom1 = custom1;
            return this;
        }

        public String getCustom2() {
            return custom2;
        }

        public SnInfo setCustom2(String custom2) {
            this.custom2 = custom2;
            return this;
        }

        public String getCustom3() {
            return custom3;
        }

        public SnInfo setCustom3(String custom3) {
            this.custom3 = custom3;
            return this;
        }

        public String getCustom4() {
            return custom4;
        }

        public SnInfo setCustom4(String custom4) {
            this.custom4 = custom4;
            return this;
        }

        public String getCustom5() {
            return custom5;
        }

        public SnInfo setCustom5(String custom5) {
            this.custom5 = custom5;
            return this;
        }
    }

    /**
     * 物料批次信息
     */
    @Schema(description = "物料批次信息")
    public static class MaterialBatchInfo {

        /**
         * 供应商编码
         */
        @Schema(description = "供应商编码")
        private String supplierCode;

        /**
         * 物料批次
         */
        @Schema(description = "物料批次")
        private String materialBatch;

        /**
         * 批次流水号
         */
        @Schema(description = "批次流水号")
        private String serial;

        /**
         * 物料id
         */
        @Schema(description = "物料ID")
        private Long materialId;

        /**
         * 批次数量
         */
        @Schema(description = "批次数量")
        private Double number;

        public String getMaterialBatch() {
            return materialBatch;
        }

        public MaterialBatchInfo setMaterialBatch(String materialBatch) {
            this.materialBatch = materialBatch;
            return this;
        }

        public String getSerial() {
            return serial;
        }

        public MaterialBatchInfo setSerial(String serial) {
            this.serial = serial;
            return this;
        }

        public Long getMaterialId() {
            return materialId;
        }

        public MaterialBatchInfo setMaterialId(Long materialId) {
            this.materialId = materialId;
            return this;
        }

        public Double getNumber() {
            return number;
        }

        public MaterialBatchInfo setNumber(Double number) {
            this.number = number;
            return this;
        }

        public String getSupplierCode() {
            return supplierCode;
        }

        public MaterialBatchInfo setSupplierCode(String supplierCode) {
            this.supplierCode = supplierCode;
            return this;
        }
    }

    /**
     * 设备信息
     *
     * <AUTHOR>
     * @date 2021-01-18
     **/
    @Schema(description = "设备信息")
    public static class EquipmentInfo {
        /**
         * 设备ID
         */
        @Schema(description = "设备ID")
        private Long equipmentId;
        /**
         * 设备编码
         */
        @Schema(description = "设备编码")
        private String equipmentCode;
        /**
         * 设备名称
         */
        @Schema(description = "设备名称")
        private String equipmentName;

        public Long getEquipmentId() {
            return equipmentId;
        }

        public ClientSaveStepInfoDTO.EquipmentInfo setEquipmentId(Long equipmentId) {
            this.equipmentId = equipmentId;
            return this;
        }

        public String getEquipmentCode() {
            return equipmentCode;
        }

        public ClientSaveStepInfoDTO.EquipmentInfo setEquipmentCode(String equipmentCode) {
            this.equipmentCode = equipmentCode;
            return this;
        }

        public String getEquipmentName() {
            return equipmentName;
        }

        public ClientSaveStepInfoDTO.EquipmentInfo setEquipmentName(String equipmentName) {
            this.equipmentName = equipmentName;
            return this;
        }
    }

    /**
     * 易损件信息
     * <AUTHOR>
     * @date 2021-6-23
     */
    @Schema(description = "易损件信息")
    public static class WearingPartInfo{
        /**
         * 易损件ID
         */
        @Schema(description = "易损件ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long wearingPartId;
        /**
         * 易损件编码
         */
        @Schema(description = "易损件编码")
        private String wearingPartCode;
        /**
         * 易损件名称
         */
        @Schema(description = "易损件名称")
        private String wearingPartName;

        /**
         * 扣减次数
         */
        @Schema(description = "扣减次数")
        private Integer abatementNumber;

        public WearingPartInfo() {
        }

        public WearingPartInfo(Long wearingPartId, String wearingPartCode, String wearingPartName) {
            this.wearingPartId = wearingPartId;
            this.wearingPartCode = wearingPartCode;
            this.wearingPartName = wearingPartName;
        }

        public Long getWearingPartId() {
            return wearingPartId;
        }

        public WearingPartInfo setWearingPartId(Long wearingPartId) {
            this.wearingPartId = wearingPartId;
            return this;
        }

        public String getWearingPartCode() {
            return wearingPartCode;
        }

        public WearingPartInfo setWearingPartCode(String wearingPartCode) {
            this.wearingPartCode = wearingPartCode;
            return this;
        }

        public String getWearingPartName() {
            return wearingPartName;
        }

        public WearingPartInfo setWearingPartName(String wearingPartName) {
            this.wearingPartName = wearingPartName;
            return this;
        }

        public Integer getAbatementNumber() {
            return abatementNumber;
        }

        public WearingPartInfo setAbatementNumber(Integer abatementNumber) {
            this.abatementNumber = abatementNumber;
            return this;
        }
    }

    @Schema(description = "请求容器信息")
    public static class RequestContainerInfo{

        @Schema(description = "容器ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long containerId;

        @Schema(description = "容器流转数量")
        private Integer number;

        public RequestContainerInfo() {
        }

        public RequestContainerInfo(Long containerId, Integer number) {
            this.containerId = containerId;
            this.number = number;
        }

        public Long getContainerId() {
            return containerId;
        }

        public RequestContainerInfo setContainerId(Long containerId) {
            this.containerId = containerId;
            return this;
        }

        public Integer getNumber() {
            return number;
        }

        public RequestContainerInfo setNumber(Integer number) {
            this.number = number;
            return this;
        }
    }

    @Schema(description = "抽检结果数据信息")
    public static class RandomInspectSaveInfo{

        @Schema(description = "类型(0,首检;1,巡检;2:末检;3;终检;4:抽检)")
        private Integer category;

        @Schema(description = "抽检数量")
        private Integer number;

        @Schema(description = "抽检合格数量")
        private Integer qualifiedNumber;

        @Schema(description = "抽检不合格数量")
        private Integer unQualifiedNumber;

        @Schema(description = "检测结果(合格:true,不合格:false)")
        private Boolean result;

        @Schema(description = "SN及对应检测项目数据信息")
        private List<SnCheckItemInfo> snCheckItemInfoList;

        public Integer getCategory() {
            return category;
        }

        public RandomInspectSaveInfo setCategory(Integer category) {
            this.category = category;
            return this;
        }

        public Boolean getResult() {
            return result;
        }

        public RandomInspectSaveInfo setResult(Boolean result) {
            this.result = result;
            return this;
        }

        public Integer getNumber() {
            return number;
        }

        public RandomInspectSaveInfo setNumber(Integer number) {
            this.number = number;
            return this;
        }

        public Integer getQualifiedNumber() {
            return qualifiedNumber;
        }

        public RandomInspectSaveInfo setQualifiedNumber(Integer qualifiedNumber) {
            this.qualifiedNumber = qualifiedNumber;
            return this;
        }

        public Integer getUnQualifiedNumber() {
            return unQualifiedNumber;
        }

        public RandomInspectSaveInfo setUnQualifiedNumber(Integer unQualifiedNumber) {
            this.unQualifiedNumber = unQualifiedNumber;
            return this;
        }

        public List<SnCheckItemInfo> getSnCheckItemInfoList() {
            return snCheckItemInfoList;
        }

        public RandomInspectSaveInfo setSnCheckItemInfoList(List<SnCheckItemInfo> snCheckItemInfoList) {
            this.snCheckItemInfoList = snCheckItemInfoList;
            return this;
        }

        @Schema(description = "SN及对应检测项目数据信息")
        public static class SnCheckItemInfo {
            @Schema(description = "检测SN")
            private String sn;

            @Schema(description = "检测项目数据列表")
            private List<CheckItemInfo> checkItemInfoList;

            public String getSn() {
                return sn;
            }

            public SnCheckItemInfo setSn(String sn) {
                this.sn = sn;
                return this;
            }

            public List<CheckItemInfo> getCheckItemInfoList() {
                return checkItemInfoList;
            }

            public SnCheckItemInfo setCheckItemInfoList(List<CheckItemInfo> checkItemInfoList) {
                this.checkItemInfoList = checkItemInfoList;
                return this;
            }

            @Schema(description = "检测项目数据信息")
            public static class CheckItemInfo{
                @Schema(description = "检测项目ID")
                private Long checkItemId;

                @Schema(description = "合格范围(开闭区间或者OK)")
                private String qualifiedRange;

                @Schema(description = "检测数据值")
                private String checkData;

                @Schema(description = "检测结果(合格:true,不合格:false)")
                private Boolean result;

                public Long getCheckItemId() {
                    return checkItemId;
                }

                public CheckItemInfo setCheckItemId(Long checkItemId) {
                    this.checkItemId = checkItemId;
                    return this;
                }

                public String getQualifiedRange() {
                    return qualifiedRange;
                }

                public CheckItemInfo setQualifiedRange(String qualifiedRange) {
                    this.qualifiedRange = qualifiedRange;
                    return this;
                }

                public String getCheckData() {
                    return checkData;
                }

                public CheckItemInfo setCheckData(String checkData) {
                    this.checkData = checkData;
                    return this;
                }

                public Boolean getResult() {
                    return result;
                }

                public CheckItemInfo setResult(Boolean result) {
                    this.result = result;
                    return this;
                }
            }

        }
    }
}
