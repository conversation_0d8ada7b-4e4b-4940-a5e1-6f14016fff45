package net.airuima.rbase.dto.qms;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/7/1
 */
@Schema(description = "映射测试数据明细DTO")
public class TestDataDTO {
    /**
     * 测试项目编码
     */
    @Schema(description = "测试项目编码")
    @Excel(name = "测试项目编码")
    private String testColumn;

    /**
     * 测试项目名称
     */
    @Schema(description = "测试项目名称")
    @Excel(name = "测试项目名称")
    private String testColumnName;

    /**
     * 测试值
     */
    @Schema(description = "测试值")
    @Excel(name = "测试项目数值")
    private String testValue;

    /**
     * 测试结果
     */
    @Schema(description = "测试结果")
    @Excel(name = "测试项目结果",replace = {"合格_true","不合格_false"})
    private Boolean testResult;

    public String getTestColumn() {
        return testColumn;
    }

    public String getTestColumnName() {
        return testColumnName;
    }

    public TestDataDTO setTestColumnName(String testColumnName) {
        this.testColumnName = testColumnName;
        return this;
    }

    public TestDataDTO setTestColumn(String testColumn) {
        this.testColumn = testColumn;
        return this;
    }

    public String getTestValue() {
        return testValue;
    }

    public TestDataDTO setTestValue(String testValue) {
        this.testValue = testValue;
        return this;
    }

    public Boolean getTestResult() {
        return testResult;
    }

    public TestDataDTO setTestResult(Boolean testResult) {
        this.testResult = testResult;
        return this;
    }

}
