package net.airuima.rbase.dto.quality;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.base.quality.UnqualifiedCause;
import net.airuima.rbase.domain.base.quality.UnqualifiedGroup;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;

import java.util.List;

/**
 * 不良DTO
 */
@Schema(description = "不良DTO")
public class UnqualifiedDTO {
    private UnqualifiedDTO() {
        // 静态成员的集合，不需要实例化
    }

    @Schema(description = "不良种类DTO")
    public static class UnqualifiedGroupDTO {

        /**
         * id
         */
        @JsonSerialize(using = ToStringSerializer.class)
        @Schema(description = "id")
        private Long id;

        /**
         * 名称
         */
        @Schema(description = "名称")
        private String name;

        /**
         * 编码
         */
        @Schema(description = "编码")
        private String code;

        @Schema(description = "是否启用")
        private Boolean isEnable;

        /**
         * 不良项目数组
         */
        @Schema(description = "不良项目数组")
        private List<UnqualifiedItemDTO> unqualifiedItemList;

        public UnqualifiedGroupDTO() {
        }

        public UnqualifiedGroupDTO(UnqualifiedGroup unqualifiedGroup) {
            this.id = unqualifiedGroup.getId();
            this.name = unqualifiedGroup.getName();
            this.code = unqualifiedGroup.getCode();
            this.isEnable = unqualifiedGroup.getIsEnable();
        }

        public Long getId() {
            return id;
        }

        public UnqualifiedGroupDTO setId(Long id) {
            this.id = id;
            return this;
        }

        public String getName() {
            return name;
        }

        public UnqualifiedGroupDTO setName(String name) {
            this.name = name;
            return this;
        }

        public String getCode() {
            return code;
        }

        public UnqualifiedGroupDTO setCode(String code) {
            this.code = code;
            return this;
        }

        public Boolean getIsEnable() {
            return isEnable;
        }

        public UnqualifiedGroupDTO setIsEnable(Boolean isEnable) {
            this.isEnable = isEnable;
            return this;
        }

        public List<UnqualifiedItemDTO> getUnqualifiedItemList() {
            return unqualifiedItemList;
        }

        public UnqualifiedGroupDTO setUnqualifiedItemList(List<UnqualifiedItemDTO> unqualifiedItemList) {
            this.unqualifiedItemList = unqualifiedItemList;
            return this;
        }
    }

    @Schema(description = "不良项目DTO")
    public static class UnqualifiedItemDTO {

        /**
         * id
         */
        @JsonSerialize(using = ToStringSerializer.class)
        @Schema(description = "id")
        private Long id;

        /**
         * 不合格项目代码
         */
        @Schema(description = "不合格项目代码")
        private String code;

        /**
         * 不合格项目名称
         */
        @Schema(description = "不合格项目名称")
        private String name;

        /**
         * 禁用启用(0:禁用;1:启用)
         */
        @Schema(description = "禁用启用(0:禁用;1:启用)")
        private Boolean isEnable;

        /**
         * 处理方式(0,在线返修;1,流程返修;2,报废）
         */
        @Schema(description = "处理方式(0,在线返修;1,流程返修;2,报废)")
        private Integer dealWay;

        /**
         * 不良种类id
         */
        @JsonSerialize(using = ToStringSerializer.class)
        @Schema(description = "不良种类")
        private Long unqualifiedGroupId;

        /**
         * 不良原因数组
         */
        @Schema(description = "不良原因数组")
        private List<UnqualifiedCauseDTO> unqualifiedCauseList;

        public UnqualifiedItemDTO(String code) {
            this.code = code;
        }

        public UnqualifiedItemDTO(UnqualifiedItem unqualifiedItem) {
            this.id = unqualifiedItem.getId();
            this.code = unqualifiedItem.getCode();
            this.name = unqualifiedItem.getName();
            this.isEnable = unqualifiedItem.getIsEnable();
            this.dealWay = unqualifiedItem.getDealWay();
            this.unqualifiedGroupId = unqualifiedItem.getUnqualifiedGroup().getId();
        }

        public Long getId() {
            return id;
        }

        public UnqualifiedItemDTO setId(Long id) {
            this.id = id;
            return this;
        }

        public String getCode() {
            return code;
        }

        public UnqualifiedItemDTO setCode(String code) {
            this.code = code;
            return this;
        }

        public String getName() {
            return name;
        }

        public UnqualifiedItemDTO setName(String name) {
            this.name = name;
            return this;
        }

        public Boolean getIsEnable() {
            return isEnable;
        }

        public UnqualifiedItemDTO setIsEnable(Boolean isEnable) {
            this.isEnable = isEnable;
            return this;
        }

        public Integer getDealWay() {
            return dealWay;
        }

        public UnqualifiedItemDTO setDealWay(Integer dealWay) {
            this.dealWay = dealWay;
            return this;
        }

        public Long getUnqualifiedGroupId() {
            return unqualifiedGroupId;
        }

        public UnqualifiedItemDTO setUnqualifiedGroupId(Long unqualifiedGroupId) {
            this.unqualifiedGroupId = unqualifiedGroupId;
            return this;
        }

        public List<UnqualifiedCauseDTO> getUnqualifiedCauseList() {
            return unqualifiedCauseList;
        }

        public UnqualifiedItemDTO setUnqualifiedCauseList(List<UnqualifiedCauseDTO> unqualifiedCauseList) {
            this.unqualifiedCauseList = unqualifiedCauseList;
            return this;
        }
    }

    @Schema(description = "不良原因DTO")
    public static class UnqualifiedCauseDTO {

        /**
         * id
         */
        @JsonSerialize(using = ToStringSerializer.class)
        @Schema(description = "id")
        private Long id;

        /**
         * 名称
         */
        @Schema(description = "名称")
        private String name;

        /**
         * 编码
         */
        @Schema(description = "编码")
        private String code;

        @Schema(description = "是否启用")
        private Boolean isEnable;

        /**
         * 备注
         */
        @Schema(description = "备注")
        private String note;

        public UnqualifiedCauseDTO() {
        }

        public UnqualifiedCauseDTO(UnqualifiedCause unqualifiedCause) {
            this.id = unqualifiedCause.getId();
            this.name = unqualifiedCause.getName();
            this.code = unqualifiedCause.getCode();
            this.note = unqualifiedCause.getNote();
            this.isEnable = unqualifiedCause.getIsEnable();
        }

        public Long getId() {
            return id;
        }

        public UnqualifiedCauseDTO setId(Long id) {
            this.id = id;
            return this;
        }

        public String getName() {
            return name;
        }

        public UnqualifiedCauseDTO setName(String name) {
            this.name = name;
            return this;
        }

        public String getCode() {
            return code;
        }

        public Boolean getIsEnable() {
            return isEnable;
        }

        public UnqualifiedCauseDTO setIsEnable(Boolean isEnable) {
            this.isEnable = isEnable;
            return this;
        }

        public UnqualifiedCauseDTO setCode(String code) {
            this.code = code;
            return this;
        }

        public String getNote() {
            return note;
        }

        public UnqualifiedCauseDTO setNote(String note) {
            this.note = note;
            return this;
        }
    }

}
