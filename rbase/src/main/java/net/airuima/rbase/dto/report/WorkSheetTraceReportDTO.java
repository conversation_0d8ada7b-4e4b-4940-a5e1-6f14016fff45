package net.airuima.rbase.dto.report;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetailMaterialBatch;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.SupplierDTO;
import net.airuima.rbase.dto.rfms.FacilityDTO;

import java.time.LocalDate;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单追溯报表DTO
 *
 * <AUTHOR>
 * @date 2021-3-12
 */
@Schema(description = "工单追溯报表DTO")
@FetchEntity
public class WorkSheetTraceReportDTO {
    private WorkSheetTraceReportDTO() {
        // 静态成员的集合，不需要实例化
    }
    /**
     * 工单追溯报表请求信息
     */
    @Schema(description = "工单追溯报表请求数据DTO")
    public static class RequestInfo extends PageDTO{

        /**
         * 总工单号
         */
        @Schema(description = "总工单号")
        private String wsSerialNumber;

        /**
         * 子工单号
         */
        @Schema(description = "子工单号")
        private String subWsSerialNumber;

        /**
         * 产品谱系id(最小层级)
         */
        @Schema(description = "产品谱系id(最小层级)")
        private Long pedigreeId;

        /**
         * 下单日期开始范围
         */
        @Schema(description = "下单日期开始范围")
        private LocalDate startDate;

        /**
         * 下单日期结束范围
         */
        @Schema(description = "下单日期结束范围")
        private LocalDate endDate;

        public String getWsSerialNumber() {
            return wsSerialNumber;
        }

        public void setWsSerialNumber(String wsSerialNumber) {
            this.wsSerialNumber = wsSerialNumber;
        }

        public String getSubWsSerialNumber() {
            return subWsSerialNumber;
        }

        public void setSubWsSerialNumber(String subWsSerialNumber) {
            this.subWsSerialNumber = subWsSerialNumber;
        }

        public Long getPedigreeId() {
            return pedigreeId;
        }

        public void setPedigreeId(Long pedigreeId) {
            this.pedigreeId = pedigreeId;
        }

        public LocalDate getStartDate() {
            return startDate;
        }

        public void setStartDate(LocalDate startDate) {
            this.startDate = startDate;
        }

        public LocalDate getEndDate() {
            return endDate;
        }

        public void setEndDate(LocalDate endDate) {
            this.endDate = endDate;
        }
    }

    /**
     * 物料信息
     */
    @Schema(description = "物料信息DTO")
    public static class MaterialInfo {

        /**
         * 物料批次
         */
        @Schema(description = "物料批次")
        private String materialBatch;

        /**
         * 批次流水号
         */
        @Schema(description = "批次流水号")
        private String serial;

        /**
         * 物料相关信息
         */
        @Schema(description = "物料相关信息")
        private MaterialDTO materialDto;

        /**
         * 供应商ID
         */
        @Schema(description = "供应商ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long supplierId;

        /**
         * 供应商DTO
         */
        @Schema(description = "供应商DTO")
        @FetchField(mapUri = "/api/suppliers", serviceId = "mom", paramKey = "supplierId",tableName = "supplier")
        private SupplierDTO supplierDTO = new SupplierDTO();

        public MaterialInfo(BatchWorkDetailMaterialBatch batchWorkDetailMaterialBatch) {
            this.materialBatch = batchWorkDetailMaterialBatch.getMaterialBatch();
            this.serial = batchWorkDetailMaterialBatch.getSerial();
            this.materialDto = batchWorkDetailMaterialBatch.getMaterialDto();
            this.supplierId = batchWorkDetailMaterialBatch.getSupplierId();
            this.supplierDTO = batchWorkDetailMaterialBatch.getSupplierDTO();
        }

        public String getMaterialBatch() {
            return materialBatch;
        }

        public void setMaterialBatch(String materialBatch) {
            this.materialBatch = materialBatch;
        }

        public String getSerial() {
            return serial;
        }

        public void setSerial(String serial) {
            this.serial = serial;
        }

        public MaterialDTO getMaterialDto() {
            return materialDto;
        }

        public void setMaterialDto(MaterialDTO materialDto) {
            this.materialDto = materialDto;
        }

        public Long getSupplierId() {
            return supplierId;
        }

        public MaterialInfo setSupplierId(Long supplierId) {
            this.supplierId = supplierId;
            return this;
        }

        public SupplierDTO getSupplierDTO() {
            return supplierDTO;
        }

        public MaterialInfo setSupplierDTO(SupplierDTO supplierDTO) {
            this.supplierDTO = supplierDTO;
            return this;
        }
    }

    /**
     * 工序相关信息
     */
    @Schema(description = "工序相关信息")
    public static class StepInfo {

        @Schema(description = "生产详情")
        private BatchWorkDetail batchWorkDetail;

        @Schema(description = "设备")
        private List<FacilityDTO> facilityDtoList;

        @Schema(description = "物料信息")
        private List<MaterialInfo> materialInfoList;

        public BatchWorkDetail getBatchWorkDetail() {
            return batchWorkDetail;
        }

        public void setBatchWorkDetail(BatchWorkDetail batchWorkDetail) {
            this.batchWorkDetail = batchWorkDetail;
        }

        public List<FacilityDTO> getFacilityDtoList() {
            return facilityDtoList;
        }

        public StepInfo setFacilityDtoList(List<FacilityDTO> facilityDtoList) {
            this.facilityDtoList = facilityDtoList;
            return this;
        }

        public List<MaterialInfo> getMaterialInfoList() {
            return materialInfoList;
        }

        public void setMaterialInfoList(List<MaterialInfo> materialInfoList) {
            this.materialInfoList = materialInfoList;
        }
    }

    /**
     * 工单追溯报表返回数据
     */
    @Schema(description = "工单追溯报表返回数据")
    public static class ResponseInfo extends PageDTO{
        /**
         * 子工单与工序信息 List
         */
        @Schema(description = "子工单与工序信息 List")
        private List<SubWorkSheetStepInfo> subWorkSheetStepInfoList;

        public List<SubWorkSheetStepInfo> getSubWorkSheetStepInfoList() {
            return subWorkSheetStepInfoList;
        }

        public ResponseInfo setSubWorkSheetStepInfoList(List<SubWorkSheetStepInfo> subWorkSheetStepInfoList) {
            this.subWorkSheetStepInfoList = subWorkSheetStepInfoList;
            return this;
        }
    }

    /**
     * 子工单与工序信息
     */
    @Schema(description = "子工单与工序信息")
    public static class SubWorkSheetStepInfo {
        /**
         * 子工单
         */
        @Schema(description = "子工单")
        private SubWorkSheet subWorkSheet;

        /**
         * 工序信息
         */
        @Schema(description = "工序信息")
        private List<StepInfo> stepInfoList;

        public SubWorkSheet getSubWorkSheet() {
            return subWorkSheet;
        }

        public SubWorkSheetStepInfo setSubWorkSheet(SubWorkSheet subWorkSheet) {
            this.subWorkSheet = subWorkSheet;
            return this;
        }

        public List<StepInfo> getStepInfoList() {
            return stepInfoList;
        }

        public SubWorkSheetStepInfo setStepInfoList(List<StepInfo> stepInfoList) {
            this.stepInfoList = stepInfoList;
            return this;
        }
    }

}
