package net.airuima.rbase.dto.qms;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.AbstractDto;

import java.io.Serializable;

@Schema(name = "项目类型表(Variety)", description = "项目类型表")
public class VarietyDTO extends AbstractDto implements Serializable {

    /**
     * 项目类型名称
     */
    @Schema(description = "项目类型名称")
    private String name;

    /**
     * 项目类型编码
     */
    @Schema(description = "项目类型编码")
    private String code;

    /**
     * 是否启用(0:禁用;1:启用)
     */
    @Schema(description = "是否启用(0:禁用;1:启用)")
    private boolean isEnable;

    public String getName() {
        return name;
    }

    public VarietyDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public VarietyDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public VarietyDTO setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }
}
