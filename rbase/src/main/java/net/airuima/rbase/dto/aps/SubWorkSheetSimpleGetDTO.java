package net.airuima.rbase.dto.aps;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.AbstractDto;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;

import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2024/1/29
 */
@Schema(description = "子工单基础信息(仅包含子工单自身的基础信息)")
public class SubWorkSheetSimpleGetDTO extends AbstractDto {

    /**
     * 所属工单信息
     */
    @Schema(description = "所属工单信息", implementation = WorkSheetSimpleGetDTO.class)
    private WorkSheetSimpleGetDTO workSheet;

    /**
     * 子工单号
     */
    @Schema(description = "子工单号", type = "string")
    private String serialNumber;

    /**
     * 子工单投产数量
     */
    @Schema(description = "子工单投产数量", type = "int", format = "int32")
    private Integer number;

    /**
     * 合格数
     */
    @Schema(description = "子工合格数", type = "int", format = "int32")
    private int qualifiedNumber;

    /**
     * 不合格数
     */
    @Schema(description = "子工不合格数", type = "int", format = "int32")
    private int unqualifiedNumber;

    /**
     * 计划开工日期
     */
    @Schema(description = "计划开工日期", type = "date", format = "date-time")
    private LocalDateTime planStartDate;

    /**
     * 计划结单日期
     */
    @Schema(description = "计划结单日期", type = "date", format = "date-time")
    private LocalDateTime planEndDate;

    /**
     * 工单状态
     */
    @Schema(description = "工单状态", type = "integer", format = "int32", example = "0:已下单;1:投产中;2:已暂停;3:已完成;4:正常结单;5:异常结单")
    private Integer status;

    /**
     * 工单状态
     */
    @Schema(description = "工单是否已上传", type = "integer", format = "int32", example = "0:未上传;1:已上传")
    private Integer syncStatus;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息", type = "string")
    private String note;


    /**
     * 工艺路线信息
     */
    @Schema(description = "工艺路线信息", implementation = WorkSheetSimpleGetDTO.WorkFlowBaseDTO.class)
    private WorkSheetSimpleGetDTO.WorkFlowBaseDTO workFlow;

    /**
     * 生产线信息
     */
    @Schema(description = "生产线信息", implementation = WorkSheetSimpleGetDTO.WorkLineBaseDTO.class)
    private WorkSheetSimpleGetDTO.WorkLineBaseDTO workLine;

    /**
     * 子工单优先级
     */
    @Schema(description = "子工单优先级", type = "integer", format = "int32", example = "0:普通,1:高")
    private Integer priority;

    public SubWorkSheetSimpleGetDTO() {

    }

    public SubWorkSheetSimpleGetDTO(SubWorkSheet subWorkSheet) {
        this.id = subWorkSheet.getId();
        this.createdDate = subWorkSheet.getCreatedDate();
        this.serialNumber = subWorkSheet.getSerialNumber();
        this.status = subWorkSheet.getStatus();
        this.syncStatus = subWorkSheet.getSyncStatus();
        this.number = subWorkSheet.getNumber();
        this.qualifiedNumber = subWorkSheet.getQualifiedNumber();
        this.unqualifiedNumber = subWorkSheet.getUnqualifiedNumber();
        this.planStartDate = subWorkSheet.getPlanStartDate();
        this.planEndDate = subWorkSheet.getPlanEndDate();
        this.priority = subWorkSheet.getPriority();
        this.planStartDate = subWorkSheet.getPlanStartDate();
        this.workFlow = new WorkSheetSimpleGetDTO.WorkFlowBaseDTO(subWorkSheet.getWorkFlow());
        this.workLine = new WorkSheetSimpleGetDTO.WorkLineBaseDTO(subWorkSheet.getWorkLine());
        this.note = subWorkSheet.getNote();
    }

    public Integer getSyncStatus() {
        return syncStatus;
    }

    public SubWorkSheetSimpleGetDTO setSyncStatus(Integer syncStatus) {
        this.syncStatus = syncStatus;
        return this;
    }

    public WorkSheetSimpleGetDTO getWorkSheet() {
        return workSheet;
    }

    public SubWorkSheetSimpleGetDTO setWorkSheet(WorkSheetSimpleGetDTO workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public SubWorkSheetSimpleGetDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public SubWorkSheetSimpleGetDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public SubWorkSheetSimpleGetDTO setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public LocalDateTime getPlanStartDate() {
        return planStartDate;
    }

    public SubWorkSheetSimpleGetDTO setPlanStartDate(LocalDateTime planStartDate) {
        this.planStartDate = planStartDate;
        return this;
    }

    public LocalDateTime getPlanEndDate() {
        return planEndDate;
    }

    public SubWorkSheetSimpleGetDTO setPlanEndDate(LocalDateTime planEndDate) {
        this.planEndDate = planEndDate;
        return this;
    }

    public String getNote() {
        return note;
    }

    public SubWorkSheetSimpleGetDTO setNote(String note) {
        this.note = note;
        return this;
    }

    public WorkSheetSimpleGetDTO.WorkFlowBaseDTO getWorkFlow() {
        return workFlow;
    }

    public SubWorkSheetSimpleGetDTO setWorkFlow(WorkSheetSimpleGetDTO.WorkFlowBaseDTO workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public WorkSheetSimpleGetDTO.WorkLineBaseDTO getWorkLine() {
        return workLine;
    }

    public SubWorkSheetSimpleGetDTO setWorkLine(WorkSheetSimpleGetDTO.WorkLineBaseDTO workLine) {
        this.workLine = workLine;
        return this;
    }

    public int getQualifiedNumber() {
        return qualifiedNumber;
    }

    public SubWorkSheetSimpleGetDTO setQualifiedNumber(int qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public int getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public SubWorkSheetSimpleGetDTO setUnqualifiedNumber(int unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public Integer getPriority() {
        return priority;
    }

    public SubWorkSheetSimpleGetDTO setPriority(Integer priority) {
        this.priority = priority;
        return this;
    }
}
