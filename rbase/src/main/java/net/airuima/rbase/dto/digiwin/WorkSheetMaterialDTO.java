package net.airuima.rbase.dto.digiwin;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *   工单以及投料单信息
 *
 * <AUTHOR>
 * @date 2021/7/30
 */
public class WorkSheetMaterialDTO {

    @Schema(description = "工单ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 工单号
     */
    @Schema(description = "工单号")
    private String serialNumber;

    /**
     * 工单类型(0:离线返修单;1:正常单)
     */
    @Schema(description = "工单类型(0:离线返修单;1:正常单;)")
    private Integer category;

    /**
     * 组织编码
     */
    @Schema(description = "组织编码")
    private String organizationCode;

    /**
     * 主件编码
     */
    @Schema(description = "主件编码")
    private String materialCode;

    /**
     * BOM编码
     */
    @Schema(description = "BOM编码")
    private String bomCode;

    /**
     * 投产数量
     */
    @Schema(description = "投产数量")
    private Integer number;

    /**
     * 计划开工日期
     */
    @Schema(description = "计划开工日期")
    private LocalDateTime planStartDate;

    /**
     * 计划结单日期
     */
    @Schema(description = "计划完工日期")
    private LocalDateTime planEndDate;

    /**
     * 投料单列表
     */
    @Schema(description = "投料单列表")
    private List<WsMaterialAdaptDTO> wsMaterialDtoList;

    public static class WsMaterialAdaptDTO {

        /**
         * 是否倒冲物料
         */
        @Schema(description = "是否倒冲物料")
        private Boolean backFlush;

        /**
         * 投料数量
         */
        @Schema(description = "投料数量")
        private Double number;

        /**
         * 总工单
         */
        @Schema(description = "总工单")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long workSheetId;


        /**
         * 当前物料编码
         */
        @Schema(description = "当前物料编码")
        private String materialCode;

        /**
         * 原始物料编码
         */
        @Schema(description = "原始物料编码")
        private String originMaterialCode;

        public Boolean getBackFlush() {
            return backFlush;
        }

        public WsMaterialAdaptDTO setBackFlush(Boolean backFlush) {
            this.backFlush = backFlush;
            return this;
        }

        public Double getNumber() {
            return number;
        }

        public WsMaterialAdaptDTO setNumber(Double number) {
            this.number = number;
            return this;
        }

        public Long getWorkSheetId() {
            return workSheetId;
        }

        public WsMaterialAdaptDTO setWorkSheetId(Long workSheetId) {
            this.workSheetId = workSheetId;
            return this;
        }

        public String getMaterialCode() {
            return materialCode;
        }

        public WsMaterialAdaptDTO setMaterialCode(String materialCode) {
            this.materialCode = materialCode;
            return this;
        }

        public String getOriginMaterialCode() {
            return originMaterialCode;
        }

        public WsMaterialAdaptDTO setOriginMaterialCode(String originMaterialCode) {
            this.originMaterialCode = originMaterialCode;
            return this;
        }
    }

    public Long getId() {
        return id;
    }

    public WorkSheetMaterialDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public WorkSheetMaterialDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public WorkSheetMaterialDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public String getOrganizationCode() {
        return organizationCode;
    }

    public WorkSheetMaterialDTO setOrganizationCode(String organizationCode) {
        this.organizationCode = organizationCode;
        return this;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public WorkSheetMaterialDTO setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
        return this;
    }

    public String getBomCode() {
        return bomCode;
    }

    public WorkSheetMaterialDTO setBomCode(String bomCode) {
        this.bomCode = bomCode;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public WorkSheetMaterialDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public LocalDateTime getPlanStartDate() {
        return planStartDate;
    }

    public WorkSheetMaterialDTO setPlanStartDate(LocalDateTime planStartDate) {
        this.planStartDate = planStartDate;
        return this;
    }

    public LocalDateTime getPlanEndDate() {
        return planEndDate;
    }

    public WorkSheetMaterialDTO setPlanEndDate(LocalDateTime planEndDate) {
        this.planEndDate = planEndDate;
        return this;
    }

    public List<WsMaterialAdaptDTO> getWsMaterialDtoList() {
        return wsMaterialDtoList;
    }

    public WorkSheetMaterialDTO setWsMaterialDtoList(List<WsMaterialAdaptDTO> wsMaterialDtoList) {
        this.wsMaterialDtoList = wsMaterialDtoList;
        return this;
    }
}
