package net.airuima.rbase.dto.rworker.process.dto.general;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 工序保存对应易损件信息
 * <AUTHOR>
 * @date 2023/4/10
 */
@Schema(description = "工序保存对应易损件信息")
public class WearingPartSaveInfo implements Serializable {

    /**
     * 易损件ID
     */
    @Schema(description = "易损件ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;


    /**
     * 工序易损件使用规则ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "工序易损件使用规则ID")
    private Long ruleId;

    /**
     * 是否自动记录易损件(false，否 true，是)
     */
    @Schema(description = "是否自动记录易损件(false，否 true，是)")
    private Boolean autoWorkRecord = Boolean.FALSE;


    /**
     * 扣减次数
     */
    @Schema(description = "扣减次数")
    private Integer abatementNumber;

    /**
     * 扣减时长
     */
    @Schema(description = "扣减时长")
    private Integer abatementTime;

    public Long getId() {
        return id;
    }

    public WearingPartSaveInfo setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getRuleId() {
        return ruleId;
    }

    public WearingPartSaveInfo setRuleId(Long ruleId) {
        this.ruleId = ruleId;
        return this;
    }

    public Boolean getAutoWorkRecord() {
        return autoWorkRecord;
    }

    public WearingPartSaveInfo setAutoWorkRecord(Boolean autoWorkRecord) {
        this.autoWorkRecord = autoWorkRecord;
        return this;
    }

    public Integer getAbatementNumber() {
        return abatementNumber;
    }

    public WearingPartSaveInfo setAbatementNumber(Integer abatementNumber) {
        this.abatementNumber = abatementNumber;
        return this;
    }

    public Integer getAbatementTime() {
        return abatementTime;
    }

    public WearingPartSaveInfo setAbatementTime(Integer abatementTime) {
        this.abatementTime = abatementTime;
        return this;
    }
}
