package net.airuima.rbase.dto.quality;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Schema(name = "MrbProcessResultDTO", description = "质检处理结果明细(MRB模式下)")
public class MrbProcessResultDTO implements Serializable {

    /**
     * 判定结果：0：退货;1:让步接收;2:特采放行;3:返工;4:挑选(全检);5:报废
     */
    @Schema(description = "判定结果：0：退货;1:让步接收;2:特采放行;3:返工;4:挑选(全检);5:报废")
    private Integer result;

    /**
     * 决策数量
     */
    @Schema(description = "决策数量")
    private Integer number;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;


    /**
     * 不良处理的不良项目明细信息列表
     */
    @Schema(description = "不良处理的不良项目明细信息列表")
    private List<UnqualifiedItemInfo> unqualifiedItemInfoList;


    /**
     * 放行的SN列表
     */
    @Schema(description = "放行的SN列表")
    public List<String> releaseSnList;


    /**
     * 全检参数信息
     */
    @Schema(description = "全检参数信息")
    private FullInspectInfo fullInspectInfo;


    public Integer getResult() {
        return result;
    }

    public MrbProcessResultDTO setResult(Integer result) {
        this.result = result;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public MrbProcessResultDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public String getNote() {
        return note;
    }

    public MrbProcessResultDTO setNote(String note) {
        this.note = note;
        return this;
    }

    public List<String> getReleaseSnList() {
        return releaseSnList;
    }

    public MrbProcessResultDTO setReleaseSnList(List<String> releaseSnList) {
        this.releaseSnList = releaseSnList;
        return this;
    }

    public List<UnqualifiedItemInfo> getUnqualifiedItemInfoList() {
        return unqualifiedItemInfoList;
    }

    public MrbProcessResultDTO setUnqualifiedItemInfoList(List<UnqualifiedItemInfo> unqualifiedItemInfoList) {
        this.unqualifiedItemInfoList = unqualifiedItemInfoList;
        return this;
    }

    public FullInspectInfo getFullInspectInfo() {
        return fullInspectInfo;
    }

    public MrbProcessResultDTO setFullInspectInfo(FullInspectInfo fullInspectInfo) {
        this.fullInspectInfo = fullInspectInfo;
        return this;
    }

    /**
     * 不良处理的不良项目明细信息
     */
    public static class UnqualifiedItemInfo{

        /**
         * 不良项目ID
         */
        @Schema(description = "不良项目ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long unqualifiedItemId;

        /**
         * 不良项目名称
         */
        @Schema(description = "不良项目名称")
        private String unqualifiedItemName;

        /**
         * 不良项目编码
         */
        @Schema(description = "不良项目编码")
        private String unqualifiedItemCode;

        /**
         * 不良项目总数
         */
        @Schema(description = "不良项目总数")
        private Integer number;

        /**
         * SN列表
         */
        @Schema(description = "SN列表")
        public List<String> snList;

        public Long getUnqualifiedItemId() {
            return unqualifiedItemId;
        }

        public UnqualifiedItemInfo setUnqualifiedItemId(Long unqualifiedItemId) {
            this.unqualifiedItemId = unqualifiedItemId;
            return this;
        }

        public String getUnqualifiedItemName() {
            return unqualifiedItemName;
        }

        public UnqualifiedItemInfo setUnqualifiedItemName(String unqualifiedItemName) {
            this.unqualifiedItemName = unqualifiedItemName;
            return this;
        }

        public String getUnqualifiedItemCode() {
            return unqualifiedItemCode;
        }

        public UnqualifiedItemInfo setUnqualifiedItemCode(String unqualifiedItemCode) {
            this.unqualifiedItemCode = unqualifiedItemCode;
            return this;
        }

        public Integer getNumber() {
            return number;
        }

        public UnqualifiedItemInfo setNumber(Integer number) {
            this.number = number;
            return this;
        }

        public List<String> getSnList() {
            return snList;
        }

        public UnqualifiedItemInfo setSnList(List<String> snList) {
            this.snList = snList;
            return this;
        }
    }

    /**
     * 全检参数信息
     */
    @Schema(description = "全检参数信息")
    public static class FullInspectInfo{

        /**
         * 检测方案id
         */
        @Schema(description = "检测方案id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 检测方案编码
         */
        @Schema(description = "检测方案编码")
        private String code;

        /**
         * 检测方案名称
         */
        @Schema(description = "检测方案名称")
        private String name;

        /**
         * 全检SN列表
         */
        @Schema(description = "全检SN列表")
        public List<String> snList;

        public Long getId() {
            return id;
        }

        public FullInspectInfo setId(Long id) {
            this.id = id;
            return this;
        }

        public String getCode() {
            return code;
        }

        public FullInspectInfo setCode(String code) {
            this.code = code;
            return this;
        }

        public String getName() {
            return name;
        }

        public FullInspectInfo setName(String name) {
            this.name = name;
            return this;
        }

        public List<String> getSnList() {
            return snList;
        }

        public FullInspectInfo setSnList(List<String> snList) {
            this.snList = snList;
            return this;
        }
    }
}
