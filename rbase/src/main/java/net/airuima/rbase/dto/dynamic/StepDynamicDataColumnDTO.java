package net.airuima.rbase.dto.dynamic;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.AbstractDto;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
public class StepDynamicDataColumnDTO extends AbstractDto {

    /**
     * 动态数据ID
     */
    @Schema(description = "动态数据ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private StepDynamicDataDTO stepDynamicData;

    /**
     * 动态数据ID
     */
    @Schema(description = "动态元数据ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private DynamicDataColumnDTO dynamicDataColumn;

    /**
     * 表单展示顺序
     */
    @Schema(description = "表单展示顺序",requiredMode = Schema.RequiredMode.NOT_REQUIRED,type = "integer",format = "int32",maxLength = 11,defaultValue = "0")
    private int formOrder;

    public StepDynamicDataDTO getStepDynamicData() {
        return stepDynamicData;
    }

    public StepDynamicDataColumnDTO setStepDynamicData(StepDynamicDataDTO stepDynamicData) {
        this.stepDynamicData = stepDynamicData;
        return this;
    }

    public DynamicDataColumnDTO getDynamicDataColumn() {
        return dynamicDataColumn;
    }

    public StepDynamicDataColumnDTO setDynamicDataColumn(DynamicDataColumnDTO dynamicDataColumn) {
        this.dynamicDataColumn = dynamicDataColumn;
        return this;
    }

    public int getFormOrder() {
        return formOrder;
    }

    public StepDynamicDataColumnDTO setFormOrder(int formOrder) {
        this.formOrder = formOrder;
        return this;
    }
}
