package net.airuima.rbase.dto.quality;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.qms.VarietyDTO;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/5/4
 */
public class InspectionTaskDTO {

    /**
     * 工单
     */
    @Schema(description = "工单")
    private WorkSheet workSheet;

    /**
     * 子工单
     */
    @Schema(description = "子工单")
    private SubWorkSheet subWorkSheet;

    /**
     * 项目类型
     */
    @Schema(description = "项目类型")
    private VarietyDTO variety;

    /**
     * 检测类型：
     */
    @Schema(description = "检测类型")
    private Integer category;

    /**
     * 容器号
     */
    @Schema(description = "容器号")
    private String containerCode;

    /**
     * 工位
     */
    @Schema(description = "工位")
    private Step step;

    /**
     * 工位
     */
    @Schema(description = "工位")
    private WorkCell workCell;

    /**
     * 是否为子工单模式
     */
    @Schema(description = "是否为子工单模式")
    private Boolean subWsProductionMode = true;

    public InspectionTaskDTO() {
    }

    public InspectionTaskDTO(WorkSheet workSheet, VarietyDTO variety, Integer category, WorkCell workCell) {
        this.workSheet = workSheet;
        this.variety = variety;
        this.category = category;
        this.workCell = workCell;
        this.subWsProductionMode = Boolean.FALSE;
    }

    public InspectionTaskDTO(SubWorkSheet subWorkSheet, VarietyDTO variety, Integer category, WorkCell workCell) {
        this.subWorkSheet = subWorkSheet;
        this.variety = variety;
        this.category = category;
        this.workCell = workCell;
        this.subWsProductionMode = Boolean.TRUE;
    }

    public Step getStep() {
        return step;
    }

    public InspectionTaskDTO setStep(Step step) {
        this.step = step;
        return this;
    }

    public Boolean getSubWsProductionMode() {
        return subWsProductionMode;
    }

    public InspectionTaskDTO setSubWsProductionMode(Boolean subWsProductionMode) {
        this.subWsProductionMode = subWsProductionMode;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public InspectionTaskDTO setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public InspectionTaskDTO setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public InspectionTaskDTO setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public VarietyDTO getVariety() {
        return variety;
    }

    public InspectionTaskDTO setVariety(VarietyDTO variety) {
        this.variety = variety;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public InspectionTaskDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public InspectionTaskDTO setContainerCode(String containerCode) {
        this.containerCode = containerCode;
        return this;
    }
}
