package net.airuima.rbase.dto.dynamic;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.document.DocumentDTO;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 动态数据列数据DTO
 *
 * <AUTHOR>
 * @date 2022/11/18 9:25
 */
@Schema(description = "动态数据数据")
public class StepDynamicDataColumnGetDTO implements Serializable {
    /**
     * 动态数据id
     */
    @Schema(description = "动态数据id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dynamicDataId;

    /**
     * 动态数据名称
     */
    @Schema(description = "动态数据名称")
    private String dynamicDataName;

    /**
     * 动态数据编码
     */
    @Schema(description = "动态数据编码")
    private String dynamicDataCode;


    /**
     * 工序名称
     */
    @Schema(description = "工序名称")
    private String stepName;

    /**
     * 工序编码
     */
    @Schema(description = "工序编码")
    private String stepCode;


    /**
     * 动态元数据信息集合
     */
    @Schema(description = "动态元数据信息集合")
    private List<ColumnInfo> columnInfoList;


    /**
     * 动态元数据信息
     */
    @Schema(description = "动态元数据信息")
    public static class ColumnInfo implements Serializable {
        /**
         * 动态元数据编码
         */
        @Schema(description = "动态元数据编码")
        private String columnCode;

        /**
         * 动态元数据名称
         */
        @Schema(description = "动态元数据名称")
        private String columnName;

        /**
         * 动态元数据值
         */
        @Schema(description = "动态元数据值")
        private String columnValue;

        /**
         * 表格展示顺序
         */
        @Schema(description = "表格展示顺序")
        private Integer tableOrder;

        /**
         * 表单展示顺序
         */
        @Schema(description = "表单展示顺序")
        private Integer formOrder;

        /**
         * 字段类型
         */
        @Schema(description = "字段类型")
        private String category;

        /**
         * 前端组件
         */
        @Schema(description = "前端组件")
        private String widget;

        /**
         * 前端组件数据
         */
        @Schema(description = "前端组件数据")
        private String widgetData;

        /**
         * 判定结果(true:合格;false)
         * 数值类型的若在区间范围内则合格，反之不合格
         * 对于字符串类型、文件类型一律认为合格
         * 对于布尔值类型的则取用户的选项
         */
        @Schema(description = "判定结果(true:合格;false)(数值类型的若在区间范围内则合格，反之不合格,字符串、文件一律合格)")
        private boolean result = Boolean.TRUE;


        /**
         * 文件集合
         */
        @Schema(description = "文件集合")
        private List<DocumentDTO> documentDTOList;

        /**
         * 子动态元数据集合
         */
        @Schema(description = "子动态元数据集合")
        private List<ColumnInfo> columnInfoList;

        public String getWidgetData() {
            return widgetData;
        }

        public ColumnInfo setWidgetData(String widgetData) {
            this.widgetData = widgetData;
            return this;
        }

        public String getColumnCode() {
            return columnCode;
        }

        public ColumnInfo setColumnCode(String columnCode) {
            this.columnCode = columnCode;
            return this;
        }

        public String getColumnName() {
            return columnName;
        }

        public ColumnInfo setColumnName(String columnName) {
            this.columnName = columnName;
            return this;
        }

        public String getColumnValue() {
            return columnValue;
        }

        public ColumnInfo setColumnValue(String columnValue) {
            this.columnValue = columnValue;
            return this;
        }

        public Integer getTableOrder() {
            return tableOrder;
        }

        public ColumnInfo setTableOrder(Integer tableOrder) {
            this.tableOrder = tableOrder;
            return this;
        }

        public Integer getFormOrder() {
            return formOrder;
        }

        public ColumnInfo setFormOrder(Integer formOrder) {
            this.formOrder = formOrder;
            return this;
        }

        public String getCategory() {
            return category;
        }

        public ColumnInfo setCategory(String category) {
            this.category = category;
            return this;
        }

        public List<DocumentDTO> getDocumentDTOList() {
            return documentDTOList;
        }

        public ColumnInfo setDocumentDTOList(List<DocumentDTO> documentDTOList) {
            this.documentDTOList = documentDTOList;
            return this;
        }

        public boolean getResult() {
            return result;
        }

        public ColumnInfo setResult(boolean result) {
            this.result = result;
            return this;
        }

        public String getWidget() {
            return widget;
        }

        public ColumnInfo setWidget(String widget) {
            this.widget = widget;
            return this;
        }

        public List<ColumnInfo> getColumnInfoList() {
            return columnInfoList;
        }

        public ColumnInfo setColumnInfoList(List<ColumnInfo> columnInfoList) {
            this.columnInfoList = columnInfoList;
            return this;
        }
    }

    public Long getDynamicDataId() {
        return dynamicDataId;
    }

    public StepDynamicDataColumnGetDTO setDynamicDataId(Long dynamicDataId) {
        this.dynamicDataId = dynamicDataId;
        return this;
    }

    public String getStepName() {
        return stepName;
    }

    public StepDynamicDataColumnGetDTO setStepName(String stepName) {
        this.stepName = stepName;
        return this;
    }

    public String getStepCode() {
        return stepCode;
    }

    public StepDynamicDataColumnGetDTO setStepCode(String stepCode) {
        this.stepCode = stepCode;
        return this;
    }

    public String getDynamicDataName() {
        return dynamicDataName;
    }

    public StepDynamicDataColumnGetDTO setDynamicDataName(String dynamicDataName) {
        this.dynamicDataName = dynamicDataName;
        return this;
    }

    public String getDynamicDataCode() {
        return dynamicDataCode;
    }

    public StepDynamicDataColumnGetDTO setDynamicDataCode(String dynamicDataCode) {
        this.dynamicDataCode = dynamicDataCode;
        return this;
    }

    public List<ColumnInfo> getColumnInfoList() {
        return columnInfoList;
    }

    public StepDynamicDataColumnGetDTO setColumnInfoList(List<ColumnInfo> columnInfoList) {
        this.columnInfoList = columnInfoList;
        return this;
    }
    
}
