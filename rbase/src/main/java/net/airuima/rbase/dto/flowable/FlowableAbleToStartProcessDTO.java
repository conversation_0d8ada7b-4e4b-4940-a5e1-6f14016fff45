package net.airuima.rbase.dto.flowable;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-03-03
 */
@Schema(description = "用户可启动流程及待办任务列表DTO")
public class FlowableAbleToStartProcessDTO {
    @Schema(description = "用户可启动的流程列表")
    private List<ProcessDefinitionDTO> processDefinitionDtoList;
    @Schema(description = "用户当前待办任务列表")
    private List<TaskDTO> taskDtoList;

    public List<ProcessDefinitionDTO> getProcessDefinitionDtoList() {
        return processDefinitionDtoList;
    }

    public FlowableAbleToStartProcessDTO setProcessDefinitionDtoList(List<ProcessDefinitionDTO> processDefinitionDtoList) {
        this.processDefinitionDtoList = processDefinitionDtoList;
        return this;
    }

    public List<TaskDTO> getTaskDtoList() {
        return taskDtoList;
    }

    public FlowableAbleToStartProcessDTO setTaskDtoList(List<TaskDTO> taskDtoList) {
        this.taskDtoList = taskDtoList;
        return this;
    }
}
