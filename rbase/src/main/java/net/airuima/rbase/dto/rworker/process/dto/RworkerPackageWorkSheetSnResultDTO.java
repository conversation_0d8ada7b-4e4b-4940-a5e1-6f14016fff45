package net.airuima.rbase.dto.rworker.process.dto;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * SN请求工序时获取RMPS里的SN及对应工单号信息DTO
 * <AUTHOR>
 */
public class RworkerPackageWorkSheetSnResultDTO {

    /**
     * SN
     */
    private String sn;

    /**
     * (子)工单号
     */
    private String serialNumber;

    public String getSn() {
        return sn;
    }

    public RworkerPackageWorkSheetSnResultDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public RworkerPackageWorkSheetSnResultDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }
}
