package net.airuima.rbase.dto.maintain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Transient;
import net.airuima.dto.AbstractDto;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.dto.bom.MaterialDTO;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
public class MaintainMaterialExchangeDTO extends AbstractDto {

    /**
     * 维修分析历史记录
     */
    @Schema(description = "维修分析历史记录")
    private MaintainHistoryDTO maintainHistory;

    /**
     * 替换物料Id
     */
    @Schema(description = "替换物料Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long replaceMaterialId;

    private MaterialDTO replaceMaterialDto = new MaterialDTO();

    /**
     * 退料批次
     */
    @Schema(description = "退料批次")
    private String returnBatch;

    /**
     * 替换料批次
     */
    @Schema(description = "替换料批次")
    private String replaceBatch;

    /**
     * 退料物料Id
     */
    @Schema(description = "退料物料Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long returnMaterialId;


    private MaterialDTO returnMaterialDto = new MaterialDTO();

    /**
     * 替换数量
     */
    @Schema(description = "替换数量")
    private double number;

    public MaintainHistoryDTO getMaintainHistory() {
        return maintainHistory;
    }

    public MaintainMaterialExchangeDTO setMaintainHistory(MaintainHistoryDTO maintainHistory) {
        this.maintainHistory = maintainHistory;
        return this;
    }

    public Long getReplaceMaterialId() {
        return replaceMaterialId;
    }

    public MaintainMaterialExchangeDTO setReplaceMaterialId(Long replaceMaterialId) {
        this.replaceMaterialId = replaceMaterialId;
        return this;
    }

    public MaterialDTO getReplaceMaterialDto() {
        return replaceMaterialDto;
    }

    public MaintainMaterialExchangeDTO setReplaceMaterialDto(MaterialDTO replaceMaterialDto) {
        this.replaceMaterialDto = replaceMaterialDto;
        return this;
    }

    public String getReturnBatch() {
        return returnBatch;
    }

    public MaintainMaterialExchangeDTO setReturnBatch(String returnBatch) {
        this.returnBatch = returnBatch;
        return this;
    }

    public String getReplaceBatch() {
        return replaceBatch;
    }

    public MaintainMaterialExchangeDTO setReplaceBatch(String replaceBatch) {
        this.replaceBatch = replaceBatch;
        return this;
    }

    public Long getReturnMaterialId() {
        return returnMaterialId;
    }

    public MaintainMaterialExchangeDTO setReturnMaterialId(Long returnMaterialId) {
        this.returnMaterialId = returnMaterialId;
        return this;
    }

    public MaterialDTO getReturnMaterialDto() {
        return returnMaterialDto;
    }

    public MaintainMaterialExchangeDTO setReturnMaterialDto(MaterialDTO returnMaterialDto) {
        this.returnMaterialDto = returnMaterialDto;
        return this;
    }

    public double getNumber() {
        return number;
    }

    public MaintainMaterialExchangeDTO setNumber(double number) {
        this.number = number;
        return this;
    }
}
