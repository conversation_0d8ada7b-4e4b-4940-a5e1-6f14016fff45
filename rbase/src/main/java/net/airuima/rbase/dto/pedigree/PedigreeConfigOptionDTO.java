package net.airuima.rbase.dto.pedigree;

import net.airuima.rbase.domain.base.pedigree.*;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * <p>
 * 产品谱系-工序 配置项信息
 *
 * <AUTHOR>
 * @date 2021/1/14
 */
public class PedigreeConfigOptionDTO {
    /**
     * 产品谱系工序
     */
    private PedigreeStep pedigreeStep;
    /**
     * 产品谱系工序指标
     */
    private PedigreeStepSpecification pedigreeStepSpecification;
    /**
     * 产品谱系工序上料规则
     */
    private List<PedigreeStepMaterialRule> pedigreeStepMaterialRuleList;
    /**
     * 产品谱系工序不良项目
     */
    private List<PedigreeStepUnqualifiedItem> pedigreeStepUnqualifiedItemList;

    /**
     * 产品谱系工序检测项目列表
     */
    private List<PedigreeStepCheckItem> pedigreeStepCheckItems;
    /**
     * 产品谱系工序检测规则
     */
    private List<PedigreeStepCheckRule> pedigreeStepCheckRules;

    public PedigreeStep getPedigreeStep() {
        return pedigreeStep;
    }

    public PedigreeConfigOptionDTO setPedigreeStep(PedigreeStep pedigreeStep) {
        this.pedigreeStep = pedigreeStep;
        return this;
    }

    public PedigreeStepSpecification getPedigreeStepSpecification() {
        return pedigreeStepSpecification;
    }

    public PedigreeConfigOptionDTO setPedigreeStepSpecification(PedigreeStepSpecification pedigreeStepSpecification) {
        this.pedigreeStepSpecification = pedigreeStepSpecification;
        return this;
    }

    public List<PedigreeStepMaterialRule> getPedigreeStepMaterialRuleList() {
        return pedigreeStepMaterialRuleList;
    }

    public PedigreeConfigOptionDTO setPedigreeStepMaterialRuleList(List<PedigreeStepMaterialRule> pedigreeStepMaterialRuleList) {
        this.pedigreeStepMaterialRuleList = pedigreeStepMaterialRuleList;
        return this;
    }

    public List<PedigreeStepUnqualifiedItem> getPedigreeStepUnqualifiedItemList() {
        return pedigreeStepUnqualifiedItemList;
    }

    public PedigreeConfigOptionDTO setPedigreeStepUnqualifiedItemList(List<PedigreeStepUnqualifiedItem> pedigreeStepUnqualifiedItemList) {
        this.pedigreeStepUnqualifiedItemList = pedigreeStepUnqualifiedItemList;
        return this;
    }

    public List<PedigreeStepCheckItem> getPedigreeStepCheckItems() {
        return pedigreeStepCheckItems;
    }

    public PedigreeConfigOptionDTO setPedigreeStepCheckItems(List<PedigreeStepCheckItem> pedigreeStepCheckItems) {
        this.pedigreeStepCheckItems = pedigreeStepCheckItems;
        return this;
    }

    public List<PedigreeStepCheckRule> getPedigreeStepCheckRules() {
        return pedigreeStepCheckRules;
    }

    public PedigreeConfigOptionDTO setPedigreeStepCheckRules(List<PedigreeStepCheckRule> pedigreeStepCheckRules) {
        this.pedigreeStepCheckRules = pedigreeStepCheckRules;
        return this;
    }
}
