package net.airuima.rbase.dto.organization;

import net.airuima.dto.AbstractDto;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2020/12/15
 */
public class JobDTO extends AbstractDto implements Serializable {

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    public String getName() {
        return name;
    }

    public JobDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public JobDTO setCode(String code) {
        this.code = code;
        return this;
    }
}
