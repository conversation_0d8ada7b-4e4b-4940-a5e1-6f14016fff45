package net.airuima.rbase.dto.rworker.quality.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckItem;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.dto.bom.MeteringUnitDTO;
import net.airuima.rbase.dto.document.DocumentDTO;
import net.airuima.rbase.dto.qms.DefectDTO;
import net.airuima.rbase.util.MapperUtils;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/4/26
 */
@Schema
public class RworkerQualityInspectionPlanDTO implements Serializable {

    /**
     * 投产工单ID
     */
    @Schema(description = "投产工单ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long productWorkSheetId;

    /**
     * 检测方案id
     */
    @Schema(description = "检测方案id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 检测方案编码
     */
    @Schema(description = "检测方案编码")
    private String code;

    /**
     * 检测方案名称
     */
    @Schema(description = "检测方案名称")
    private String name;

    /**
     * 检测数量
     */
    @Schema(description = "检测数量")
    private Integer number;

    /**
     * 检测类型：0 ：首检，1：巡检
     */
    @Schema(description = "检测类型：0 ：首检，1：巡检")
    private Integer category;

    /**
     * 检测项目类型
     */
    @Schema(description = "检测项目类型")
    private VarietyDTO varietyDto;

    /**
     * 抽检方案
     */
    @Schema(description = "抽检方案")
    private SampleCaseDTO sampleCaseDto;

    /**
     * 检测项目列表
     */
    @Schema(description = "检测项目列表")
    private List<CheckItemDTO> checkItems;

    /**
     * 不良项目列表
     */
    @Schema(description = "不良项目列表")
    private List<UnqualifiedItemDTO> unqualifiedItems;

    /**
     * sn列表
     */
    @Schema(description = "sn列表")
    private List<String> snInfoList;

    /**
     * 工位列表
     */
    @Schema(description = "工位列表")
    private List<WorkCellDTO> workCellDtoList;

    /**
     * 缓存
     */
    @Schema(description = "缓存")
    private String cache;

    public RworkerQualityInspectionPlanDTO() {
    }

    public RworkerQualityInspectionPlanDTO(PedigreeStepCheckRule pedigreeStepCheckRule) {
        this.id = pedigreeStepCheckRule.getId();
        this.code = pedigreeStepCheckRule.getCode();
        this.name = pedigreeStepCheckRule.getName();
        this.category = pedigreeStepCheckRule.getCategory();
        this.varietyDto = ObjectUtils.isEmpty(pedigreeStepCheckRule.getVarietyObj())?null: MapperUtils.map(pedigreeStepCheckRule.getVarietyObj(),VarietyDTO.class);
        this.sampleCaseDto = ObjectUtils.isEmpty(pedigreeStepCheckRule.getSampleCase())?null:MapperUtils.map(pedigreeStepCheckRule.getSampleCase(),SampleCaseDTO.class);
    }

    public Long getProductWorkSheetId() {
        return productWorkSheetId;
    }

    public RworkerQualityInspectionPlanDTO setProductWorkSheetId(Long productWorkSheetId) {
        this.productWorkSheetId = productWorkSheetId;
        return this;
    }

    public List<String> getSnInfoList() {
        return snInfoList;
    }

    public RworkerQualityInspectionPlanDTO setSnInfoList(List<String> snInfoList) {
        this.snInfoList = snInfoList;
        return this;
    }

    public String getCache() {
        return cache;
    }

    public RworkerQualityInspectionPlanDTO setCache(String cache) {
        this.cache = cache;
        return this;
    }

    public Long getId() {
        return id;
    }

    public RworkerQualityInspectionPlanDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getCode() {
        return code;
    }

    public RworkerQualityInspectionPlanDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getName() {
        return name;
    }

    public RworkerQualityInspectionPlanDTO setName(String name) {
        this.name = name;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public RworkerQualityInspectionPlanDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public RworkerQualityInspectionPlanDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public VarietyDTO getVarietyDto() {
        return varietyDto;
    }

    public RworkerQualityInspectionPlanDTO setVarietyDto(VarietyDTO varietyDto) {
        this.varietyDto = varietyDto;
        return this;
    }

    public SampleCaseDTO getSampleCaseDto() {
        return sampleCaseDto;
    }

    public RworkerQualityInspectionPlanDTO setSampleCaseDto(SampleCaseDTO sampleCaseDto) {
        this.sampleCaseDto = sampleCaseDto;
        return this;
    }

    public List<CheckItemDTO> getCheckItems() {
        return checkItems;
    }

    public RworkerQualityInspectionPlanDTO setCheckItems(List<CheckItemDTO> checkItems) {
        this.checkItems = checkItems;
        return this;
    }

    public List<UnqualifiedItemDTO> getUnqualifiedItems() {
        return unqualifiedItems;
    }

    public RworkerQualityInspectionPlanDTO setUnqualifiedItems(List<UnqualifiedItemDTO> unqualifiedItems) {
        this.unqualifiedItems = unqualifiedItems;
        return this;
    }

    public List<WorkCellDTO> getWorkCellDtoList() {
        return workCellDtoList;
    }

    public RworkerQualityInspectionPlanDTO setWorkCellDtoList(List<WorkCellDTO> workCellDtoList) {
        this.workCellDtoList = workCellDtoList;
        return this;
    }

    @Schema(description = "项目类型")
    public static class VarietyDTO{
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 项目类型名称
         */
        @Schema(description = "项目类型名称")
        private String name;

        /**
         * 项目类型编码
         */
        @Schema(description = "项目类型编码")
        private String code;

        public VarietyDTO() {
            // 空构造，防止被覆盖
        }

        public Long getId() {
            return id;
        }

        public VarietyDTO setId(Long id) {
            this.id = id;
            return this;
        }

        public String getName() {
            return name;
        }

        public VarietyDTO setName(String name) {
            this.name = name;
            return this;
        }

        public String getCode() {
            return code;
        }

        public VarietyDTO setCode(String code) {
            this.code = code;
            return this;
        }
    }

    @Schema(description = "抽样方案")
    public static class SampleCaseDTO{
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 抽样方案名称
         */
        @NotNull
        @Schema(description = "抽样方案名称")
        private String name;

        /**
         * 抽样方案编码
         */
        @NotNull
        @Schema(description = "抽样方案编码")
        private String code;

        /**
         * 抽样类型:全检0/固定数量1/按百分比抽样2/按国标抽样3
         */
        @Schema(description = "抽样类型:全检0/固定数量1/按百分比抽样2/按国标抽样3")
        private Integer category;

        /**
         * 抽样数量
         */
        @Schema(description = "抽样数量")
        private Integer number;

        /**
         * 允收数ac
         */
        @Schema(description = "允收数ac")
        private Integer ac;

        /**
         * 抽样百分比
         */
        @Schema(description = "抽样百分比")
        private Double rate;

        /**
         * 合格百分比
         */
        @Schema(description = "合格百分比")
        private Double qualifiedRate;

        /**
         * 检验水平
         */
        @Schema(description = "检验水平")
        private String insolationLevel;

        /**
         * 接收质量限AQL
         */
        @Schema(description = "接收质量限AQL")
        private Double aql;

        public SampleCaseDTO() {
            // 空构造，防止被覆盖
        }


        public Long getId() {
            return id;
        }

        public SampleCaseDTO setId(Long id) {
            this.id = id;
            return this;
        }

        public String getName() {
            return name;
        }

        public SampleCaseDTO setName(String name) {
            this.name = name;
            return this;
        }

        public String getCode() {
            return code;
        }

        public SampleCaseDTO setCode(String code) {
            this.code = code;
            return this;
        }

        public Integer getCategory() {
            return category;
        }

        public SampleCaseDTO setCategory(Integer category) {
            this.category = category;
            return this;
        }

        public Integer getNumber() {
            return number;
        }

        public SampleCaseDTO setNumber(Integer number) {
            this.number = number;
            return this;
        }

        public Integer getAc() {
            return ac;
        }

        public SampleCaseDTO setAc(Integer ac) {
            this.ac = ac;
            return this;
        }

        public Double getRate() {
            return rate;
        }

        public SampleCaseDTO setRate(Double rate) {
            this.rate = rate;
            return this;
        }

        public Double getQualifiedRate() {
            return qualifiedRate;
        }

        public SampleCaseDTO setQualifiedRate(Double qualifiedRate) {
            this.qualifiedRate = qualifiedRate;
            return this;
        }


        public String getInsolationLevel() {
            return insolationLevel;
        }

        public SampleCaseDTO setInsolationLevel(String insolationLevel) {
            this.insolationLevel = insolationLevel;
            return this;
        }

        public Double getAql() {
            return aql;
        }

        public SampleCaseDTO setAql(Double aql) {
            this.aql = aql;
            return this;
        }
    }

    @Schema(description = "检测项目")
    public static class CheckItemDTO{

        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 检测项名称
         */
        @Schema(description = "检测项名称")
        private String name;
        /**
         * 检测项编码
         */
        @Schema(description = "检测项编码")
        private String code;

        /**
         * 合格范围(开闭区间或者OK)
         */
        @Schema(description = "合格范围(开闭区间或者OK)")
        private String qualifiedRange;

        /**
         * 是否管控其检查结果(0:不管控；1：管控)
         */
        @Schema(description = "是否管控其检查结果(0:不管控；1：管控)")
        private Boolean control;

        @Schema(description = "检验数据录入 0按抽检方案数 1 自定义抽检数量")
        private Integer inspectNumberCase;

        @Schema(description = "自定义的检验项目SN抽检个数")
        private Integer customizeInspectNumber;

        /**
         * 计量单位id
         */
        @Schema(description = "计量单位id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long unitId;

        /**
         * 计量单位
         */
        @Schema(description = "计量单位")
        private MeteringUnitDTO unitDto;

        /**
         * 项目类型
         */
        @Schema(description = "项目类型")
        private VarietyDTO varietyDto;

        /**
         * 检验方法:目测0/检测仪器1
         */
        @Schema(description = "检验方法:目测0/检测仪器1")
        private Integer inspectWay;

        /**
         * 分析方法:定性0/定量1
         */
        @Schema(description = "分析方法:定性0/定量1")
        private Integer analyseWay;

        /**
         * 检测仪器id
         */
        @Schema(description = "检测仪器")
        @JsonSerialize(using = ToStringSerializer.class)
        private String facility;

        /**
         * 关联文件
         */
        @Schema(description = "关联文件列表")
        List<DocumentDTO> documentDtos;

        /**
         * 缺陷原因列表
         */
        @Schema(description = "缺陷原因列表")
        List<DefectDTO> defects;

        public CheckItemDTO() {
        }

        public CheckItemDTO(PedigreeStepCheckItem pedigreeStepCheckItem,int sampleNumber) {
            net.airuima.rbase.dto.qms.CheckItemDTO checkItem = pedigreeStepCheckItem.getCheckItem();
            this.id = checkItem.getId();
            this.name = checkItem.getName();
            this.code = checkItem.getCode();
            this.qualifiedRange = pedigreeStepCheckItem.getQualifiedRange();
            this.control = pedigreeStepCheckItem.getControl();
            this.inspectNumberCase = pedigreeStepCheckItem.getInspectNumberCase();
            this.customizeInspectNumber = pedigreeStepCheckItem.getInspectNumberCase()== Constants.INT_ZERO?sampleNumber:Math.min(pedigreeStepCheckItem.getCustomizeInspectNumber(),sampleNumber);
            this.unitId = checkItem.getUnitId();
            this.unitDto = checkItem.getUnitDto();
            this.varietyDto = ObjectUtils.isEmpty(checkItem.getVarietyObj())?null:MapperUtils.map(checkItem.getVarietyObj(),VarietyDTO.class);
            this.inspectWay = checkItem.getInspectWay();
            this.analyseWay = checkItem.getAnalyseWay();
            this.facility = checkItem.getFacility();
        }

        public List<DefectDTO> getDefects() {
            return defects;
        }

        public CheckItemDTO setDefects(List<DefectDTO> defects) {
            this.defects = defects;
            return this;
        }

        public List<DocumentDTO> getDocumentDtos() {
            return documentDtos;
        }

        public CheckItemDTO setDocumentDtos(List<DocumentDTO> documentDtos) {
            this.documentDtos = documentDtos;
            return this;
        }

        public Long getId() {
            return id;
        }

        public CheckItemDTO setId(Long id) {
            this.id = id;
            return this;
        }

        public String getName() {
            return name;
        }

        public CheckItemDTO setName(String name) {
            this.name = name;
            return this;
        }

        public String getCode() {
            return code;
        }

        public CheckItemDTO setCode(String code) {
            this.code = code;
            return this;
        }

        public String getQualifiedRange() {
            return qualifiedRange;
        }

        public CheckItemDTO setQualifiedRange(String qualifiedRange) {
            this.qualifiedRange = qualifiedRange;
            return this;
        }

        public Boolean getControl() {
            return control;
        }

        public CheckItemDTO setControl(Boolean control) {
            this.control = control;
            return this;
        }

        public Long getUnitId() {
            return unitId;
        }

        public CheckItemDTO setUnitId(Long unitId) {
            this.unitId = unitId;
            return this;
        }


        public Integer getInspectWay() {
            return inspectWay;
        }

        public CheckItemDTO setInspectWay(Integer inspectWay) {
            this.inspectWay = inspectWay;
            return this;
        }

        public Integer getAnalyseWay() {
            return analyseWay;
        }

        public CheckItemDTO setAnalyseWay(Integer analyseWay) {
            this.analyseWay = analyseWay;
            return this;
        }


        public String getFacility() {
            return facility;
        }

        public CheckItemDTO setFacility(String facility) {
            this.facility = facility;
            return this;
        }

        public MeteringUnitDTO getUnitDto() {
            return unitDto;
        }

        public CheckItemDTO setUnitDto(MeteringUnitDTO unitDto) {
            this.unitDto = unitDto;
            return this;
        }

        public VarietyDTO getVarietyDto() {
            return varietyDto;
        }

        public CheckItemDTO setVarietyDto(VarietyDTO varietyDto) {
            this.varietyDto = varietyDto;
            return this;
        }

        public Integer getInspectNumberCase() {
            return inspectNumberCase;
        }

        public CheckItemDTO setInspectNumberCase(Integer inspectNumberCase) {
            this.inspectNumberCase = inspectNumberCase;
            return this;
        }

        public Integer getCustomizeInspectNumber() {
            return customizeInspectNumber;
        }

        public CheckItemDTO setCustomizeInspectNumber(Integer customizeInspectNumber) {
            this.customizeInspectNumber = customizeInspectNumber;
            return this;
        }
    }

    @Schema(description = "不良项目")
    public static class UnqualifiedItemDTO{
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;
        /**
         * 不良项编码
         */
        @Schema(description = "不良项编码")
        private String code;
        /**
         * 不良项名称
         */
        @Schema(description = "不良项名称")
        private String name;

        public UnqualifiedItemDTO() {
        }

        public UnqualifiedItemDTO(UnqualifiedItem unqualifiedItem) {
            this.id = unqualifiedItem.getId();
            this.code = unqualifiedItem.getCode();
            this.name = unqualifiedItem.getName();
        }

        public Long getId() {
            return id;
        }

        public UnqualifiedItemDTO setId(Long id) {
            this.id = id;
            return this;
        }

        public String getCode() {
            return code;
        }

        public UnqualifiedItemDTO setCode(String code) {
            this.code = code;
            return this;
        }

        public String getName() {
            return name;
        }

        public UnqualifiedItemDTO setName(String name) {
            this.name = name;
            return this;
        }
    }


    @Schema(description = "工位dto")
    public static class WorkCellDTO{

        /**
         * 工位id
         */
        @JsonSerialize(using = ToStringSerializer.class)
        @Schema(description = "工位id")
        private Long id;

        /**
         * 工位编码
         */
        @Schema(description = "工位编码")
        private String code;

        /**
         * 工位名称
         */
        @Schema(description = "工位名称")
        private String name;

        public WorkCellDTO() {
        }

        public WorkCellDTO(WorkCell workCell) {
            this.id = workCell.getId();
            this.code = workCell.getCode();
            this.name = workCell.getName();
        }

        public Long getId() {
            return id;
        }

        public WorkCellDTO setId(Long id) {
            this.id = id;
            return this;
        }

        public String getCode() {
            return code;
        }

        public WorkCellDTO setCode(String code) {
            this.code = code;
            return this;
        }

        public String getName() {
            return name;
        }

        public WorkCellDTO setName(String name) {
            this.name = name;
            return this;
        }
    }
}
