package net.airuima.rbase.dto.rworker.process.dto.general;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 工序保存时对应物料信息
 * <AUTHOR>
 * @date 2023/4/10
 */
@Schema(description = "工序物料信息")
public class MaterialSaveInfo implements Serializable {
    /**
     * 物料id
     */
    @Schema(description = "物料ID")
    private Long id;

    /**
     * 物料批次
     */
    @Schema(description = "物料批次")
    private String batch;

    /**
     * 是否核物料批次(true:是;false:否)
     */
    @Schema(description = "是否核物料批次(true:核对物料批次;false:不核对物料批次)")
    private Boolean isCheckMaterialBatch;

    /**
     * 是否扣除库存(true:是;false:否)
     */
    @Schema(description = "是否扣除库存(true:是;false:否)")
    private Boolean isDeduct;

    /**
     * 上料数量
     */
    @Schema(description = "上料数量")
    private Double number;

    /**
     * 上料类型(0:单只序列号;1:批次号)
     */
    @Schema(description = "上料类型(0:单只序列号;1:物料批次)")
    private Integer granularity;

    public Long getId() {
        return id;
    }

    public MaterialSaveInfo setId(Long id) {
        this.id = id;
        return this;
    }

    public String getBatch() {
        return batch;
    }

    public MaterialSaveInfo setBatch(String batch) {
        this.batch = batch;
        return this;
    }

    public Boolean getIsCheckMaterialBatch() {
        return isCheckMaterialBatch;
    }

    public MaterialSaveInfo setIsCheckMaterialBatch(Boolean checkMaterialBatch) {
        this.isCheckMaterialBatch = checkMaterialBatch;
        return this;
    }

    public Boolean getIsDeduct() {
        return isDeduct;
    }

    public MaterialSaveInfo setIsDeduct(Boolean deduct) {
        this.isDeduct = deduct;
        return this;
    }

    public Double getNumber() {
        return number;
    }

    public MaterialSaveInfo setNumber(Double number) {
        this.number = number;
        return this;
    }

    public Integer getGranularity() {
        return granularity;
    }

    public MaterialSaveInfo setGranularity(Integer granularity) {
        this.granularity = granularity;
        return this;
    }
}
