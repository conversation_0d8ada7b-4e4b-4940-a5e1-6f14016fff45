package net.airuima.rbase.dto.flowable;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-02-26
 */
@Schema(description = "接口调用结果信息")
public class FlowableResultDTO {

    @Schema(description = "结果状态:OK/KO")
    private String status;
    @Schema(description = "结果信息")
    private String message;

    public FlowableResultDTO(){

    }

    public FlowableResultDTO(String message){
        this.message = message;
    }

    public FlowableResultDTO(String status, String message){
        this.status = status;
        this.message = message;
    }

    public String getStatus() {
        return status;
    }

    public FlowableResultDTO setStatus(String status) {
        this.status = status;
        return this;
    }

    public String getMessage() {
        return message;
    }

    public FlowableResultDTO setMessage(String message) {
        this.message = message;
        return this;
    }
}
