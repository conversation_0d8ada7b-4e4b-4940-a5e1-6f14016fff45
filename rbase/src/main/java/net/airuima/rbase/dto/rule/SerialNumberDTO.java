package net.airuima.rbase.dto.rule;

import java.time.LocalDate;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2020-12-24
 */
public class SerialNumberDTO {
    private String code;
    private String prefixCode;
    private LocalDate customDate;
    private Long id;

    public SerialNumberDTO() {

    }

    public SerialNumberDTO(String code, LocalDate customDate, Long id) {
        this.code = code;
        this.customDate = customDate;
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public SerialNumberDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getPrefixCode() {
        return prefixCode;
    }

    public SerialNumberDTO setPrefixCode(String prefixCode) {
        this.prefixCode = prefixCode;
        return this;
    }

    public LocalDate getCustomDate() {
        return customDate;
    }

    public SerialNumberDTO setCustomDate(LocalDate customDate) {
        this.customDate = customDate;
        return this;
    }

    public Long getId() {
        return id;
    }

    public SerialNumberDTO setId(Long id) {
        this.id = id;
        return this;
    }
}
