package net.airuima.rbase.dto.rworker.process.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepMaterialRule;
import net.airuima.rbase.domain.procedure.batch.WsMaterial;
import net.airuima.rbase.domain.procedure.material.WsMaterialBatch;
import net.airuima.rbase.domain.procedure.material.WsWorkCellMaterialBatch;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.ocmes.GlueDetailDTO;
import net.airuima.rbase.dto.pedigree.SnRuleDTO;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * Rworker工单工序物料上料规则DTO
 *
 * <AUTHOR>
 * @date 2023/2/10
 */
@Schema(description = "Rworker工单工序物料上料规则DTO")
public class RworkerFeedingMaterialRuleGetDTO implements Serializable {

    /**
     * 工单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "工单ID")
    private Long workSheetId;

    /**
     * Rworker工序物料批次上料规则信息列表
     */
    @Schema(description = "Rworker工序物料批次上料规则信息列表")
    private List<FeedingMaterialBatchRuleInfo> feedingMaterialBatchRuleInfoList;

    /**
     * Rworker工序胶水上料规则信息列表
     */
    @Schema(description = "Rworker工序胶水上料规则信息列表")
    private List<FeedingGlueMaterialBatchRuleInfo> feedingGlueMaterialBatchRuleInfoList;

    /**
     * Rworker工序单支序列号上料规则信息列表
     */
    @Schema(description = "Rworker工序单支序列号上料规则信息列表")
    private List<FeedingSnRuleInfo> feedingSnRuleInfoList;

    public RworkerFeedingMaterialRuleGetDTO() {

    }

    public RworkerFeedingMaterialRuleGetDTO(List<FeedingMaterialBatchRuleInfo> feedingMaterialBatchRuleInfoList, List<FeedingSnRuleInfo> feedingSnRuleInfoList) {
        this.feedingMaterialBatchRuleInfoList = feedingMaterialBatchRuleInfoList;
        this.feedingSnRuleInfoList = feedingSnRuleInfoList;
    }

    public Long getWorkSheetId() {
        return workSheetId;
    }

    public RworkerFeedingMaterialRuleGetDTO setWorkSheetId(Long workSheetId) {
        this.workSheetId = workSheetId;
        return this;
    }

    public List<FeedingMaterialBatchRuleInfo> getFeedingMaterialBatchRuleInfoList() {
        return feedingMaterialBatchRuleInfoList;
    }

    public RworkerFeedingMaterialRuleGetDTO setFeedingMaterialBatchRuleInfoList(List<FeedingMaterialBatchRuleInfo> feedingMaterialBatchRuleInfoList) {
        this.feedingMaterialBatchRuleInfoList = feedingMaterialBatchRuleInfoList;
        return this;
    }

    public List<FeedingGlueMaterialBatchRuleInfo> getFeedingGlueMaterialBatchRuleInfoList() {
        return feedingGlueMaterialBatchRuleInfoList;
    }

    public RworkerFeedingMaterialRuleGetDTO setFeedingGlueMaterialBatchRuleInfoList(List<FeedingGlueMaterialBatchRuleInfo> feedingGlueMaterialBatchRuleInfoList) {
        this.feedingGlueMaterialBatchRuleInfoList = feedingGlueMaterialBatchRuleInfoList;
        return this;
    }

    public List<FeedingSnRuleInfo> getFeedingSnRuleInfoList() {
        return feedingSnRuleInfoList;
    }

    public RworkerFeedingMaterialRuleGetDTO setFeedingSnRuleInfoList(List<FeedingSnRuleInfo> feedingSnRuleInfoList) {
        this.feedingSnRuleInfoList = feedingSnRuleInfoList;
        return this;
    }

    /**
     * Rworker工序胶水上料规则信息
     */
    @Schema(description = "Rworker工序胶水上料规则信息")
    public static class FeedingGlueMaterialBatchRuleInfo implements Serializable {

        /**
         * 是否扣除库存(true:是;false:否)
         */
        @Schema(description = "是否扣除库存(true:是;false:否)")
        private Boolean isDeduct = false;

        /**
         * 是否核物料批次(true:是;false:否)
         */
        @Schema(description = "是否核物料批次(true:核对物料批次;false:不核对物料批次)")
        private Boolean isCheckMaterialBatch;

        /**
         * 胶水物料组信息列表
         */
        @Schema(description = "胶水物料组信息列表")
        private List<GlueMaterialGroupInfo> glueMaterialGroupInfoList;

        public Boolean getIsDeduct() {
            return isDeduct;
        }

        public FeedingGlueMaterialBatchRuleInfo setIsDeduct(Boolean deduct) {
            isDeduct = deduct;
            return this;
        }

        public Boolean getIsCheckMaterialBatch() {
            return isCheckMaterialBatch;
        }

        public FeedingGlueMaterialBatchRuleInfo setIsCheckMaterialBatch(Boolean checkMaterialBatch) {
            isCheckMaterialBatch = checkMaterialBatch;
            return this;
        }

        public List<GlueMaterialGroupInfo> getGlueMaterialGroupInfoList() {
            return glueMaterialGroupInfoList;
        }

        public FeedingGlueMaterialBatchRuleInfo setGlueMaterialGroupInfoList(List<GlueMaterialGroupInfo> glueMaterialGroupInfoList) {
            this.glueMaterialGroupInfoList = glueMaterialGroupInfoList;
            return this;
        }

        @Schema(description = "胶水物料组信息")
        public static class GlueMaterialGroupInfo implements Serializable {
            /**
             * 物料ID
             */
            @JsonSerialize(using = ToStringSerializer.class)
            @Schema(description = "物料ID")
            private Long id;

            /**
             * 物料编码
             */
            @Schema(description = "物料编码")
            private String code;

            /**
             * 物料名称
             */
            @Schema(description = "物料名称")
            private String name;

            /**
             * 主辅类型(1:主料;0:辅料)
             */
            @Schema(description = "主辅类型(1:主料;0:辅料)")
            private Integer materialCategory;

            /**
             * 是否为替代料(true:是;false:否)
             */
            @Schema(description = "是否为替代料(true:是;false:否)")
            private Boolean isReplaceMaterial;

            /**
             * 胶水可用批次信息列表
             */
            @Schema(description = "胶水可用批次信息列表")
            private List<GlueMaterialBatchInfo> glueMaterialBatchInfoList;

            public GlueMaterialGroupInfo() {
            }

            public GlueMaterialGroupInfo(MaterialDTO materialDTO, Boolean isReplaceMaterial) {
                this.id = materialDTO.getId();
                this.code = materialDTO.getCode();
                this.name = materialDTO.getName();
                this.materialCategory = materialDTO.getMaterialCategory();
                this.isReplaceMaterial = isReplaceMaterial;
            }

            public Long getId() {
                return id;
            }

            public GlueMaterialGroupInfo setId(Long id) {
                this.id = id;
                return this;
            }

            public String getCode() {
                return code;
            }

            public GlueMaterialGroupInfo setCode(String code) {
                this.code = code;
                return this;
            }

            public String getName() {
                return name;
            }

            public GlueMaterialGroupInfo setName(String name) {
                this.name = name;
                return this;
            }

            public Integer getMaterialCategory() {
                return materialCategory;
            }

            public GlueMaterialGroupInfo setMaterialCategory(Integer materialCategory) {
                this.materialCategory = materialCategory;
                return this;
            }

            public Boolean getIsReplaceMaterial() {
                return isReplaceMaterial;
            }

            public GlueMaterialGroupInfo setIsReplaceMaterial(Boolean replaceMaterial) {
                isReplaceMaterial = replaceMaterial;
                return this;
            }

            public List<GlueMaterialBatchInfo> getGlueMaterialBatchInfoList() {
                return glueMaterialBatchInfoList;
            }

            public GlueMaterialGroupInfo setGlueMaterialBatchInfoList(List<GlueMaterialBatchInfo> glueMaterialBatchInfoList) {
                this.glueMaterialBatchInfoList = glueMaterialBatchInfoList;
                return this;
            }

            /**
             * 胶水可用批次信息
             */
            @Schema(description = "胶水可用批次信息")
            public static class GlueMaterialBatchInfo implements Serializable {
                /**
                 * 胶水批次号
                 */
                @Schema(description = "胶水批次号")
                private String batch;

                /**
                 * 胶水批次失效时间
                 */
                @Schema(description = "胶水批次失效时间")
                private LocalDateTime expireTime;

                public GlueMaterialBatchInfo() {

                }

                public GlueMaterialBatchInfo(GlueDetailDTO glueDetailDTO) {
                    this.batch = glueDetailDTO.getGlueBatch();
                    this.expireTime = glueDetailDTO.getExpireDate();
                }

                public String getBatch() {
                    return batch;
                }

                public GlueMaterialBatchInfo setBatch(String batch) {
                    this.batch = batch;
                    return this;
                }

                public LocalDateTime getExpireTime() {
                    return expireTime;
                }

                public GlueMaterialBatchInfo setExpireTime(LocalDateTime expireTime) {
                    this.expireTime = expireTime;
                    return this;
                }
            }
        }

    }

    @Schema(description = "Rworker工序物料批次上料规则信息")
    public static class FeedingMaterialBatchRuleInfo implements Serializable {
        /**
         * 工序上料比例
         */
        @Schema(description = "工序上料比例")
        private Double proportion;

        /**
         * 工序上料总数
         * 管控工位库存且大于0时需要按照此数进行工位上料
         * 管控工位库存且等于0时需要进行工序扫料
         */
        @Schema(description = "工序上料总数（管控工位库存且大于0时需要进行工位上料）")
        private Double number;

        /**
         * 是否核物料批次(true:是;false:否)
         */
        @Schema(description = "是否核物料批次(true:核对物料批次;false:不核对物料批次)")
        private Boolean isCheckMaterialBatch;

        /**
         * 是否扣除库存(true:是;false:否)
         */
        @Schema(description = "是否扣除库存(true:是;false:否)")
        private Boolean isDeduct;

        /**
         * 工单物料组库存列表（管控工单库存时按此上料和防呆；管控工位库存时按此给工位上料及防呆）
         */
        @Schema(description = "工单物料组库存列表（管控工单库存时按此上料和防呆；管控工位库存时按此给工位上料及防呆）")
        private List<WsMaterialGroupInfo> wsMaterialGroupInfoList;

        /**
         * 工位物料组库存列表(主要是工位库存足够时进行工序扫料防呆)
         */
        @Schema(description = "工位物料组库存列表(主要是工位库存足够时进行工序扫料防呆)")
        private List<WsWorkCellMaterialGroupInfo> wsWorkCellMaterialGroupInfoList;

        public FeedingMaterialBatchRuleInfo() {
        }

        public FeedingMaterialBatchRuleInfo(PedigreeStepMaterialRule pedigreeStepMaterialRule) {
            this.proportion = pedigreeStepMaterialRule.getProportion();
            this.isCheckMaterialBatch = pedigreeStepMaterialRule.getIsCheckMaterialBatch();
            this.isDeduct = pedigreeStepMaterialRule.getIsDeduct();
        }

        public Double getProportion() {
            return proportion;
        }

        public FeedingMaterialBatchRuleInfo setProportion(Double proportion) {
            this.proportion = proportion;
            return this;
        }

        public Double getNumber() {
            return number;
        }

        public FeedingMaterialBatchRuleInfo setNumber(Double number) {
            this.number = number;
            return this;
        }

        public Boolean getIsCheckMaterialBatch() {
            return isCheckMaterialBatch;
        }

        public FeedingMaterialBatchRuleInfo setIsCheckMaterialBatch(Boolean checkMaterialBatch) {
            this.isCheckMaterialBatch = checkMaterialBatch;
            return this;
        }

        public Boolean getIsDeduct() {
            return isDeduct;
        }

        public FeedingMaterialBatchRuleInfo setIsDeduct(Boolean deduct) {
            this.isDeduct = deduct;
            return this;
        }

        public List<WsMaterialGroupInfo> getWsMaterialGroupInfoList() {
            return wsMaterialGroupInfoList;
        }

        public FeedingMaterialBatchRuleInfo setWsMaterialGroupInfoList(List<WsMaterialGroupInfo> wsMaterialGroupInfoList) {
            this.wsMaterialGroupInfoList = wsMaterialGroupInfoList;
            return this;
        }

        public List<WsWorkCellMaterialGroupInfo> getWsWorkCellMaterialGroupInfoList() {
            return wsWorkCellMaterialGroupInfoList;
        }

        public FeedingMaterialBatchRuleInfo setWsWorkCellMaterialGroupInfoList(List<WsWorkCellMaterialGroupInfo> wsWorkCellMaterialGroupInfoList) {
            this.wsWorkCellMaterialGroupInfoList = wsWorkCellMaterialGroupInfoList;
            return this;
        }

        @Schema(description = "工单物料组信息(原始物料与替代料视为一组)")
        public static class WsMaterialGroupInfo implements Serializable {
            /**
             * 物料ID
             */
            @JsonSerialize(using = ToStringSerializer.class)
            @Schema(description = "物料ID")
            private Long id;

            /**
             * 物料编码
             */
            @Schema(description = "物料编码")
            private String code;

            /**
             * 物料名称
             */
            @Schema(description = "物料名称")
            private String name;

            /**
             * 主辅类型(1:主料;0:辅料)
             */
            @Schema(description = "主辅类型(1:主料;0:辅料)")
            private Integer materialCategory;

            /**
             * 是否为替代料(true:是;false:否)
             */
            @Schema(description = "是否为替代料(true:是;false:否)")
            private Boolean isReplaceMaterial;

            /**
             * 可上物料批次列表信息
             */
            @Schema(description = "可上物料批次列表信息")
            private List<MaterialBatchInfo> materialBatchInfoList;

            public WsMaterialGroupInfo() {
            }

            public WsMaterialGroupInfo(WsMaterial wsMaterial) {
                this.id = wsMaterial.getMaterialId();
                this.code = wsMaterial.getMaterialDto().getCode();
                this.name = wsMaterial.getMaterialDto().getName();
                this.materialCategory = wsMaterial.getMaterialDto().getMaterialCategory();
                this.isReplaceMaterial = !wsMaterial.getMaterialId().equals(wsMaterial.getOriginMaterialId());
            }

            public WsMaterialGroupInfo(PedigreeStepMaterialRule pedigreeStepMaterialRule) {
                this.id = pedigreeStepMaterialRule.getMaterialId();
                this.code = pedigreeStepMaterialRule.getMaterialDto().getCode();
                this.name = pedigreeStepMaterialRule.getMaterialDto().getName();
                this.materialCategory = pedigreeStepMaterialRule.getMaterialDto().getMaterialCategory();
                this.isReplaceMaterial = Boolean.FALSE;
            }

            public Long getId() {
                return id;
            }

            public WsMaterialGroupInfo setId(Long id) {
                this.id = id;
                return this;
            }

            public String getCode() {
                return code;
            }

            public WsMaterialGroupInfo setCode(String code) {
                this.code = code;
                return this;
            }

            public String getName() {
                return name;
            }

            public WsMaterialGroupInfo setName(String name) {
                this.name = name;
                return this;
            }

            public Integer getMaterialCategory() {
                return materialCategory;
            }

            public WsMaterialGroupInfo setMaterialCategory(Integer materialCategory) {
                this.materialCategory = materialCategory;
                return this;
            }

            public Boolean getIsReplaceMaterial() {
                return isReplaceMaterial;
            }

            public WsMaterialGroupInfo setIsReplaceMaterial(Boolean replaceMaterial) {
                this.isReplaceMaterial = replaceMaterial;
                return this;
            }

            public List<MaterialBatchInfo> getMaterialBatchInfoList() {
                return materialBatchInfoList;
            }

            public WsMaterialGroupInfo setMaterialBatchInfoList(List<MaterialBatchInfo> materialBatchInfoList) {
                this.materialBatchInfoList = materialBatchInfoList;
                return this;
            }

            @Schema(description = "物料可用批次信息")
            public static class MaterialBatchInfo implements Serializable {
                /**
                 * 物料批次号
                 */
                @Schema(description = "物料批次号")
                private String batch;

                /**
                 * 剩余可上料库存
                 */
                @Schema(description = "剩余可上料库存")
                private Double number;

                public MaterialBatchInfo() {

                }

                public MaterialBatchInfo(WsMaterialBatch wsMaterialBatch) {
                    this.batch = wsMaterialBatch.getBatch();
                    this.number = wsMaterialBatch.getLeftNumber();
                }

                public MaterialBatchInfo(WsWorkCellMaterialBatch wsWorkCellMaterialBatch) {
                    this.batch = wsWorkCellMaterialBatch.getBatch();
                    this.number = wsWorkCellMaterialBatch.getLeftNumber();
                }

                public String getBatch() {
                    return batch;
                }

                public MaterialBatchInfo setBatch(String batch) {
                    this.batch = batch;
                    return this;
                }

                public Double getNumber() {
                    return number;
                }

                public MaterialBatchInfo setNumber(Double number) {
                    this.number = number;
                    return this;
                }
            }
        }

        @Schema(description = "工位物料组信息(原始物料与替代料视为一组)")
        public static class WsWorkCellMaterialGroupInfo extends WsMaterialGroupInfo implements Serializable {


            public WsWorkCellMaterialGroupInfo() {
            }

            public WsWorkCellMaterialGroupInfo(WsMaterial wsMaterial) {
                super.id = wsMaterial.getMaterialId();
                super.code = wsMaterial.getMaterialDto().getCode();
                super.name = wsMaterial.getMaterialDto().getName();
                super.materialCategory = wsMaterial.getMaterialDto().getMaterialCategory();
                super.isReplaceMaterial = !wsMaterial.getMaterialId().equals(wsMaterial.getOriginMaterialId());
            }

        }
    }

    @Schema(description = "Rworker工序单支序列号上料规则信息")
    public static class FeedingSnRuleInfo implements Serializable {
        /**
         * 物料ID
         */
        @JsonSerialize(using = ToStringSerializer.class)
        @Schema(description = "物料ID")
        private Long id;

        /**
         * 物料编码
         */
        @Schema(description = "物料编码")
        private String code;

        /**
         * 物料名称
         */
        @Schema(description = "物料名称")
        private String name;

        /**
         * 上料个数
         */
        @Schema(description = "上料个数")
        private Integer number;

        /**
         * 正则表达式
         */
        @Schema(description = "正则表达式")
        private List<List<SnRuleDTO>> serialNumberRuleList;

        public FeedingSnRuleInfo() {

        }

        public FeedingSnRuleInfo(PedigreeStepMaterialRule pedigreeStepMaterialRule) {
            this.id = pedigreeStepMaterialRule.getMaterialId();
            this.code = pedigreeStepMaterialRule.getMaterialDto().getCode();
            this.name = pedigreeStepMaterialRule.getMaterialDto().getName();
            this.number = pedigreeStepMaterialRule.getControlSnCount();
            this.serialNumberRuleList = pedigreeStepMaterialRule.getSerialNumberRule();
        }

        public Long getId() {
            return id;
        }

        public FeedingSnRuleInfo setId(Long id) {
            this.id = id;
            return this;
        }

        public String getCode() {
            return code;
        }

        public FeedingSnRuleInfo setCode(String code) {
            this.code = code;
            return this;
        }

        public String getName() {
            return name;
        }

        public FeedingSnRuleInfo setName(String name) {
            this.name = name;
            return this;
        }

        public Integer getNumber() {
            return number;
        }

        public FeedingSnRuleInfo setNumber(Integer number) {
            this.number = number;
            return this;
        }

        public List<List<SnRuleDTO>> getSerialNumberRuleList() {
            return serialNumberRuleList;
        }

        public FeedingSnRuleInfo setSerialNumberRuleList(List<List<SnRuleDTO>> serialNumberRuleList) {
            this.serialNumberRuleList = serialNumberRuleList;
            return this;
        }
    }
}
