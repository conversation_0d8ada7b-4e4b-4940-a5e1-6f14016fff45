package net.airuima.rbase.dto.flowable;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import java.util.Set;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-03-03
 */
@Schema(description = "流程节点完成返回信息DTO")
public class ProcessRunTimeEventDTO extends FlowableResultDTO {
    /**
     * 流程定义KEY
     */
    @Schema(description = "流程定义KEY")
    private String processDefinitionKey;

    /**
     * 业务ID
     */
    @Schema(description = "业务ID")
    private String businessKey;

    /**
     * 任务节点key
     */
    @Schema(description = "任务节点key")
    private String taskKey;

    /**
     * 任务名称
     */
    @Schema(description = "任务名称")
    private String taskName;

    /**
     * 连线key
     */
    @Schema(description = "连线key")
    private String sequenceKey;

    /**
     * 连线名称
     */
    @Schema(description = "连线名称")
    private String sequenceName;

    /**
     * 流程实例ID
     */
    @Schema(description = "流程实例ID")
    private String processInstanceId;

    /**
     * 下个用户任务节点列表
     */
    @Schema(description = "下个用户任务节点列表")
    private List<NextTodoUserTaskInfo> nextTodoUserTaskInfoList;


    public String getBusinessKey() {
        return businessKey;
    }

    public ProcessRunTimeEventDTO setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
        return this;
    }


    public String getProcessInstanceId() {
        return processInstanceId;
    }

    public ProcessRunTimeEventDTO setProcessInstanceId(String processInstanceId) {
        this.processInstanceId = processInstanceId;
        return this;
    }


    public String getTaskName() {
        return taskName;
    }

    public ProcessRunTimeEventDTO setTaskName(String taskName) {
        this.taskName = taskName;
        return this;
    }

    public String getSequenceName() {
        return sequenceName;
    }

    public ProcessRunTimeEventDTO setSequenceName(String sequenceName) {
        this.sequenceName = sequenceName;
        return this;
    }

    public String getProcessDefinitionKey() {
        return processDefinitionKey;
    }

    public ProcessRunTimeEventDTO setProcessDefinitionKey(String processDefinitionKey) {
        this.processDefinitionKey = processDefinitionKey;
        return this;
    }

    public String getTaskKey() {
        return taskKey;
    }

    public ProcessRunTimeEventDTO setTaskKey(String taskKey) {
        this.taskKey = taskKey;
        return this;
    }

    public String getSequenceKey() {
        return sequenceKey;
    }

    public ProcessRunTimeEventDTO setSequenceKey(String sequenceKey) {
        this.sequenceKey = sequenceKey;
        return this;
    }

    public List<NextTodoUserTaskInfo> getNextTodoUserTaskInfoList() {
        return nextTodoUserTaskInfoList;
    }

    public ProcessRunTimeEventDTO setNextTodoUserTaskInfoList(List<NextTodoUserTaskInfo> nextTodoUserTaskInfoList) {
        this.nextTodoUserTaskInfoList = nextTodoUserTaskInfoList;
        return this;
    }

    @Schema(description = "下个用户任务节点")
    public static class NextTodoUserTaskInfo{

        /**
         * 任务类型
         */
        @Schema(description = "任务类型")
        private String taskCategory;

        /**
         * 任务节点key
         */
        @Schema(description = "任务节点key")
        private String taskKey;

        /**
         * 任务名称
         */
        @Schema(description = "任务名称")
        private String taskName;

        /**
         * 处理人列表
         */
        @Schema(description = "处理人列表")
        private Set<String> assignUserList;

        public String getTaskCategory() {
            return taskCategory;
        }

        public NextTodoUserTaskInfo setTaskCategory(String taskCategory) {
            this.taskCategory = taskCategory;
            return this;
        }


        public String getTaskName() {
            return taskName;
        }

        public NextTodoUserTaskInfo setTaskName(String taskName) {
            this.taskName = taskName;
            return this;
        }

        public Set<String> getAssignUserList() {
            return assignUserList;
        }

        public NextTodoUserTaskInfo setAssignUserList(Set<String> assignUserList) {
            this.assignUserList = assignUserList;
            return this;
        }

        public String getTaskKey() {
            return taskKey;
        }

        public NextTodoUserTaskInfo setTaskKey(String taskKey) {
            this.taskKey = taskKey;
            return this;
        }
    }
}
