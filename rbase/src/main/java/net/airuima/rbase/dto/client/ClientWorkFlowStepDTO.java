package net.airuima.rbase.dto.client;

import net.airuima.rbase.dto.client.base.BaseClientDTO;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/10/20
 */
public class ClientWorkFlowStepDTO extends BaseClientDTO {

    List<MaintainAnalyseInfoDTO.WorkFlowStepInfo> workFlowStepInfos;

    public ClientWorkFlowStepDTO(String status, String message) {
        super(status, message);
    }

    public ClientWorkFlowStepDTO(String status) {
        super(status);
    }

    public ClientWorkFlowStepDTO(String status ,List<MaintainAnalyseInfoDTO.WorkFlowStepInfo> workFlowStepInfos) {
        super(status);
        this.workFlowStepInfos = workFlowStepInfos;
    }

    public List<MaintainAnalyseInfoDTO.WorkFlowStepInfo> getWorkFlowStepInfos() {
        return workFlowStepInfos;
    }

    public ClientWorkFlowStepDTO setWorkFlowStepInfo(List<MaintainAnalyseInfoDTO.WorkFlowStepInfo> workFlowStepInfo) {
        this.workFlowStepInfos = workFlowStepInfo;
        return this;
    }
}
