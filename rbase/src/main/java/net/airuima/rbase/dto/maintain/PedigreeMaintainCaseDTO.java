package net.airuima.rbase.dto.maintain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.SchemaProperty;
import jakarta.persistence.Column;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Transient;
import jakarta.validation.constraints.NotNull;
import net.airuima.dto.AbstractDto;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.dto.organization.ClientDTO;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
public class PedigreeMaintainCaseDTO extends AbstractDto {

    /**
     * 维修方案
     */
    @SchemaProperty(name = "维修方案")
    private MaintainCaseDTO maintainCase;

    /**
     * 产品谱系
     */
    @SchemaProperty(name = "产品谱系")
    private Pedigree pedigree;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long clientId;

    /**
     * 客户DTO
     */
    @Schema(description = "客户DTO")
    private ClientDTO clientDto = new ClientDTO();

    /**
     * 返修流程(0:返修工艺路线, 1:原工艺路线)
     */
    @Schema(description = "返修流程(0:返修工艺路线, 1:原工艺路线)", required = true)
    private int reworkCategory;

    /**
     * 流程框图
     */
    @Schema(description = "流程框图id")
    private WorkFlow workFlow;

    /**
     * 是否启用(0:禁用;1:启用)
     */
    @Schema(description = "是否启用(0:禁用;1:启用)")
    private boolean isEnable;

    public MaintainCaseDTO getMaintainCase() {
        return maintainCase;
    }

    public PedigreeMaintainCaseDTO setMaintainCase(MaintainCaseDTO maintainCase) {
        this.maintainCase = maintainCase;
        return this;
    }

    public Pedigree getPedigree() {
        return pedigree;
    }

    public PedigreeMaintainCaseDTO setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public Long getClientId() {
        return clientId;
    }

    public PedigreeMaintainCaseDTO setClientId(Long clientId) {
        this.clientId = clientId;
        return this;
    }

    public ClientDTO getClientDto() {
        return clientDto;
    }

    public PedigreeMaintainCaseDTO setClientDto(ClientDTO clientDto) {
        this.clientDto = clientDto;
        return this;
    }

    public int getReworkCategory() {
        return reworkCategory;
    }

    public PedigreeMaintainCaseDTO setReworkCategory(int reworkCategory) {
        this.reworkCategory = reworkCategory;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public PedigreeMaintainCaseDTO setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public PedigreeMaintainCaseDTO setIsEnable(boolean enable) {
        isEnable = enable;
        return this;
    }
}
