package net.airuima.rbase.dto.ocmes;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/10/28
 */
@Schema(description = "胶水批次信息")
public class GlueDetailDTO{

    /**
     * 胶水名称
     */
    @Schema(description = "胶水名称")
    private String glueName;
    /**
     * 胶水编号
     */
    @Schema(description = "胶水编号")
    private String glueCode;
    /**
     * 状态(0:未领用;1:已领用;2:已回收)
     */
    @Schema(description = "状态(0:未领用;1:已领用;2:已回收)")
    private Integer glueStatus;

    /**
     * 领用时间
     */
    @Schema(description = "领用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiveDate;
    /**
     * 领用人姓名
     */
    @Schema(description = "领用人姓名")
    private String receiverStaffName;
    /**
     * 胶水批次号
     */
    @Schema(description = "胶水批次号")
    private String glueBatch;

    /**
     * 胶水失效时间
     */
    @Schema(description = "胶水失效时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireDate;


    public String getGlueName() {
        return glueName;
    }

    public GlueDetailDTO setGlueName(String glueName) {
        this.glueName = glueName;
        return this;
    }

    public String getGlueCode() {
        return glueCode;
    }

    public GlueDetailDTO setGlueCode(String glueCode) {
        this.glueCode = glueCode;
        return this;
    }

    public Integer getGlueStatus() {
        return glueStatus;
    }

    public GlueDetailDTO setGlueStatus(Integer glueStatus) {
        this.glueStatus = glueStatus;
        return this;
    }

    public LocalDateTime getReceiveDate() {
        return receiveDate;
    }

    public GlueDetailDTO setReceiveDate(LocalDateTime receiveDate) {
        this.receiveDate = receiveDate;
        return this;
    }

    public String getReceiverStaffName() {
        return receiverStaffName;
    }

    public GlueDetailDTO setReceiverStaffName(String receiverStaffName) {
        this.receiverStaffName = receiverStaffName;
        return this;
    }

    public String getGlueBatch() {
        return glueBatch;
    }

    public GlueDetailDTO setGlueBatch(String glueBatch) {
        this.glueBatch = glueBatch;
        return this;
    }

    public LocalDateTime getExpireDate() {
        return expireDate;
    }

    public GlueDetailDTO setExpireDate(LocalDateTime expireDate) {
        this.expireDate = expireDate;
        return this;
    }
}
