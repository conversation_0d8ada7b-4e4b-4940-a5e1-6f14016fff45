package net.airuima.rbase.dto.pedigree;

import net.airuima.dto.AbstractDto;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.dto.process.StepDTO;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工序指标DTO
 *
 * <AUTHOR>
 * @date 2020/12/26
 */
public class PedigreeStepSpecificationDTO extends AbstractDto {
    /**
     * 产品谱系
     */
    private Pedigree pedigree;

    /**
     * 工序DTOList
     */
    private List<StepDTO> stepDtoList;

    public Pedigree getPedigree() {
        return pedigree;
    }

    public void setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
    }

    public List<StepDTO> getStepDtoList() {
        return stepDtoList;
    }

    public void setStepDtoList(List<StepDTO> stepDtoList) {
        this.stepDtoList = stepDtoList;
    }
}
