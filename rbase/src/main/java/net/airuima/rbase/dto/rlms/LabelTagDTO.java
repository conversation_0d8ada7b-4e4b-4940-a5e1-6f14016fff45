package net.airuima.rbase.dto.rlms;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.AbstractDto;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 标签类型 DTO
 *
 * <AUTHOR>
 * @date 2023-02-21
 */
@Schema(description = "标签类型DTO")
public class LabelTagDTO extends AbstractDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标签类型名称
     */
    @Schema(description = "标签类型名称", required = true)
    private String name;

    /**
     * 排序
     */
    @Schema(description = "排序", required = true)
    private Integer orderNo;

    /**
     * 记录时间
     */
    @Schema(description = "记录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime recordTime;

    /**
     * 是否启用(0:禁用;1:启用)
     */
    @Schema(description = "是否启用(0:禁用;1:启用)", required = true)
    private boolean isEnable;

    public String getName() {
        return name;
    }

    public LabelTagDTO setName(String name) {
        this.name = name;
        return this;
    }

    public Integer getOrderNo() {
        return orderNo;
    }

    public LabelTagDTO setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public LocalDateTime getRecordTime() {
        return recordTime;
    }

    public LabelTagDTO setRecordTime(LocalDateTime recordTime) {
        this.recordTime = recordTime;
        return this;
    }

    public boolean isEnable() {
        return isEnable;
    }

    public LabelTagDTO setEnable(boolean enable) {
        isEnable = enable;
        return this;
    }
}
