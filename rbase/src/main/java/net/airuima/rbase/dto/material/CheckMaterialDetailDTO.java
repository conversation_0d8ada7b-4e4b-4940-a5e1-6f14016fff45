package net.airuima.rbase.dto.material;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 核料信息DTO
 *
 * <AUTHOR>
 * @date 2021/1/12
 */
@Schema(description = "核料信息DTO")
public class CheckMaterialDetailDTO {

    /**
     * 核料详情id
     */
    @Schema(description = "核料详情id")
    @NotBlank
    private Long id;

    /**
     * 核料数量
     */
    @Schema(description = "核料数量")
    @NotBlank
    private Double num;

    public Long getId() {
        return id;
    }

    public CheckMaterialDetailDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public Double getNum() {
        return num;
    }

    public CheckMaterialDetailDTO setNum(Double num) {
        this.num = num;
        return this;
    }
}
