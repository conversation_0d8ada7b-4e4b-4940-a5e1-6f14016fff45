package net.airuima.rbase.dto.process;

import net.airuima.dto.AbstractDto;
import net.airuima.rbase.domain.base.process.WorkFlow;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 流程框图DTO
 *
 * <AUTHOR>
 * @date 2020/12/26
 */
public class WorkFlowDTO extends AbstractDto {
    /**
     * 工序DTOList
     */
    List<StepDTO> stepDtoList;
    /**
     * 名称
     */
    private String name;
    /**
     * 框图编码
     */
    private String code;
    /**
     * 是否启用(0:禁用;1:启用)
     */
    private boolean isEnable;
    /**
     * 流程框图类型(0:正常生产流程;1:返修方案流程)
     */
    private int category;

    public WorkFlowDTO() {

    }

    public WorkFlowDTO(WorkFlow workFlow){
        this.name = workFlow.getName();
        this.category = workFlow.getCategory();
        this.code = workFlow.getCode();
        this.isEnable = workFlow.getIsEnable();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
    }

    public int getCategory() {
        return category;
    }

    public void setCategory(int category) {
        this.category = category;
    }

    public List<StepDTO> getStepDtoList() {
        return stepDtoList;
    }

    public void setStepDtoList(List<StepDTO> stepDtoList) {
        this.stepDtoList = stepDtoList;
    }
}
