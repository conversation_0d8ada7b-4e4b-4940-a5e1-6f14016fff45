package net.airuima.rbase.dto.rule;

import java.time.LocalDate;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 * 流水号历史明细DTO
 * <AUTHOR>
 * @date 2022/1/11
 */
public class SerialNumberHistoryDTO {
    /**
     * 序列号生成规则编码
     */
    private String code;
    /**
     * 日期
     */
    private LocalDate customDate;
    /**
     * 生成的流水号
     */
    private String serialNumber;

    private String prefixCode;

    public SerialNumberHistoryDTO(){

    }

    public SerialNumberHistoryDTO(String code,LocalDate customDate,String serialNumber,String prefixCode){
        this.code = code;
        this.customDate = customDate;
        this.serialNumber = serialNumber;
        this.prefixCode = prefixCode;
    }

    public String getCode() {
        return code;
    }

    public SerialNumberHistoryDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public LocalDate getCustomDate() {
        return customDate;
    }

    public SerialNumberHistoryDTO setCustomDate(LocalDate customDate) {
        this.customDate = customDate;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public SerialNumberHistoryDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public String getPrefixCode() {
        return prefixCode;
    }

    public SerialNumberHistoryDTO setPrefixCode(String prefixCode) {
        this.prefixCode = prefixCode;
        return this;
    }
}
