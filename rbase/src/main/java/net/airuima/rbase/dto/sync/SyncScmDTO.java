package net.airuima.rbase.dto.sync;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *  同步客户/供应商信息 -》目前与（sap和erp都使用此dto）
 *
 * <AUTHOR>
 * @date 2021-06-03
 */
@Schema(description = "客户/供应商信息")
public class SyncScmDTO {
    /**
     * 主键ID
     */
    @Schema(description = "供应商ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 供应商/客户名称
     */
    @Schema(description = "供应商/客户名称")
    private String name;

    /**
     * 供应商/客户编码
     */
    @Schema(description = "供应商/客户编码")
    private String code;

    /**
     * 供应商/客户地址
     */
    @Schema(description = "供应商/客户地址")
    private String address;

    /**
     * 供应商/客户联系电话
     */
    @Schema(description = "供应商/客户联系电话")
    private String phoneNumber;

    /**
     * 类型(0:供应商;1:客户)
     */
    @Schema(description = "类型(0:供应商;1:客户)")
    private Integer category;

    /**
     * 同步类型(0:新增;1:修改;2:删除;3:禁用; 4:启用)
     */
    @Schema(description = "同步类型(0:新增;1:修改;2:删除;3:禁用; 4:启用)")
    private Integer operate;

    public Long getId() {
        return id;
    }

    public SyncScmDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getName() {
        return name;
    }

    public SyncScmDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public SyncScmDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getAddress() {
        return address;
    }

    public SyncScmDTO setAddress(String address) {
        this.address = address;
        return this;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public SyncScmDTO setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public SyncScmDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public Integer getOperate() {
        return operate;
    }

    public SyncScmDTO setOperate(Integer operate) {
        this.operate = operate;
        return this;
    }
}
