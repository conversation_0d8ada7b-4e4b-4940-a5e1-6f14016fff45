package net.airuima.rbase.dto.pedigree;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.StepGroup;
import net.airuima.rbase.dto.process.StepDTO;
import net.airuima.rbase.dto.process.WorkFlowDTO;

import java.util.List;

/**
 * 产品谱系及其关联的工艺路线,工序,工序组DTO
 * <AUTHOR>
 * @date 2022/10/19
 */
@Schema(description = "产品谱系及其关联的工艺路线,工序,工序组DTO")
public class PedigreeDetailDTO {
    /**
     * 产品谱系名称
     * <AUTHOR>
     * @date 2022/10/19
     */
    @Schema(description = "产品谱系名称")
    private Pedigree pedigree;

    /**
     * 工艺路线
     * <AUTHOR>
     * @date 2022/10/19
     */
    @Schema(description = "工艺路线")
    private List<WorkFlowDTO> workFlowDTOList;

    /**
     * 工序
     * <AUTHOR>
     * @date 2022/10/19
     */
    @Schema(description = "工序")
    private List<StepDTO> stepDTOList;

    /**
     * 工序组
     * <AUTHOR>
     * @date 2022/10/20
     */
    @Schema(description = "工序组")
    private List<StepGroup> stepGroupsList;

    public List<StepDTO> getStepDTOList() {
        return stepDTOList;
    }

    public PedigreeDetailDTO setStepDTOList(List<StepDTO> stepDTOList) {
        this.stepDTOList = stepDTOList;
        return this;
    }

    public List<StepGroup> getStepGroupsList() {
        return stepGroupsList;
    }

    public PedigreeDetailDTO setStepGroupsList(List<StepGroup> stepGroupsList) {
        this.stepGroupsList = stepGroupsList;
        return this;
    }

    public Pedigree getPedigree() {
        return pedigree;
    }

    public PedigreeDetailDTO setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public PedigreeDetailDTO(Pedigree pedigree, List<WorkFlowDTO> workFlowDTOList, List<StepDTO> stepDTOList, List<StepGroup> stepGroupsList) {
        this.pedigree = pedigree;
        this.workFlowDTOList = workFlowDTOList;
        this.stepDTOList = stepDTOList;
        this.stepGroupsList = stepGroupsList;
    }

    public List<WorkFlowDTO> getWorkFlowDTOList() {
        return workFlowDTOList;
    }

    public PedigreeDetailDTO setWorkFlowDTOList(List<WorkFlowDTO> workFlowDTOList) {
        this.workFlowDTOList = workFlowDTOList;
        return this;
    }

    public PedigreeDetailDTO() {
    }
}
