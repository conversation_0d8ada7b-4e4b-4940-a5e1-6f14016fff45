package net.airuima.rbase.dto.dynamic;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.Forbidden;
import net.airuima.dto.AbstractDto;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;

import java.util.HashSet;
import java.util.Set;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
public class StepDynamicDataDTO extends AbstractDto {

    /**
     * 动态数据名称
     */
    @Schema(description = "动态数据名称", requiredMode = Schema.RequiredMode.REQUIRED,type = "string",maxLength = 255)
    private String name;

    /**
     * 动态数据编码
     */
    @Schema(description = "动态数据编码", requiredMode = Schema.RequiredMode.REQUIRED,type = "string",maxLength = 255)
    private String code;

    /**
     * 描述信息
     */
    @Schema(description = "描述信息" ,requiredMode = Schema.RequiredMode.NOT_REQUIRED,type = "string",maxLength = 255)
    private String description;

    /**
     * 产品谱系ID
     */
    @Schema(description = "产品谱系", requiredMode = Schema.RequiredMode.REQUIRED,implementation = Pedigree.class)
    private Pedigree pedigree;

    /**
     * 工艺路线ID
     */
    @Schema(description = "工艺路线ID" , requiredMode = Schema.RequiredMode.NOT_REQUIRED,implementation = WorkFlow.class)
    private WorkFlow workFlow;

    /**
     * 工序ID
     */
    @Schema(description = "工序ID", requiredMode = Schema.RequiredMode.REQUIRED,implementation = Step.class)
    private Step step;

    /**
     * 关联类型(0:工单,1:子工单,2:容器,3:单支)
     */
    @Schema(description = "关联类型(0:工单,1:子工单,2:容器,3:单支)" ,requiredMode = Schema.RequiredMode.NOT_REQUIRED,type = "integer",format = "int32",maxLength = 11,defaultValue = "0")
    private int granularity;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用",requiredMode = Schema.RequiredMode.NOT_REQUIRED,type = "boolean",defaultValue = "true" ,example ="true：启用 false：禁用" )
    private boolean isEnable;

    /**
     * 可见工序
     */
    private Set<Step> stepList = new HashSet<>();

    public String getName() {
        return name;
    }

    public StepDynamicDataDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public StepDynamicDataDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public StepDynamicDataDTO setDescription(String description) {
        this.description = description;
        return this;
    }

    public Pedigree getPedigree() {
        return pedigree;
    }

    public StepDynamicDataDTO setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public StepDynamicDataDTO setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public StepDynamicDataDTO setStep(Step step) {
        this.step = step;
        return this;
    }

    public int getGranularity() {
        return granularity;
    }

    public StepDynamicDataDTO setGranularity(int granularity) {
        this.granularity = granularity;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public StepDynamicDataDTO setIsEnable(boolean enable) {
        isEnable = enable;
        return this;
    }

    public Set<Step> getStepList() {
        return stepList;
    }

    public StepDynamicDataDTO setStepList(Set<Step> stepList) {
        this.stepList = stepList;
        return this;
    }
}
