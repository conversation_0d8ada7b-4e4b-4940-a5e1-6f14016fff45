package net.airuima.rbase.dto.client;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.client.base.BaseClientDTO;

import java.time.LocalDateTime;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *RWorker获取易损件相关信息DTO
 *
 * <AUTHOR>
 * @date 2021/6/23
 */
@Schema(description = "RWorker获取易损件相关信息DTO")
public class ClientGetWearingPartInfoDTO extends BaseClientDTO {

    /**
     * 易损件ID
     */
    @Schema(description = "易损件ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long wearingPartId;

    /**
     * 易损件种类ID
     */
    @Schema(description = "易损件种类ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long wearingPartGroupId;

    /**
     * 易损件名称
     */
    @Schema(description = "易损件名称")
    private String name;


    /**
     * 易损件编码
     */
    @Schema(description = "易损件编码")
    private String code;

    /**
     *易损件使用类型（0:次数，1:时长）
     */
    @Schema(description = "易损件使用类型（0:次数，1:时长）")
    private Integer category;

    /**
     * 最大重置次数
     */
    @Schema(description = "最大重置次数")
    private Integer maxResetNumber;

    /**
     * 累计重置次数
     */
    @Schema(description = "累计重置次数")
    private Integer accumulateResetNumber;

    /**
     * 最大使用次数
     */
    @Schema(description = "最大使用次数")
    private Integer maxUseNumber;

    /**
     * 累计使用次数
     */
    @Schema(description = "累计使用次数")
    private Integer accumulateUseNumber;

    /**
     * 最大使用时长(秒为单位)
     */
    @Schema(description = "最大使用时长(秒为单位)")
    private Integer maxUseTime;

    /**
     * 累计使用时长(秒为单位)
     */
    @Schema(description = "累计使用时长(秒为单位)")
    private Integer accumulateUseTime;

    /**
     * 易损件使用状态（0可用，1在用，2超期，3报废）
     */
    @Schema(description = "易损件使用状态（0可用，1在用，2超期，3报废）")
    private Integer status;

    /**
     * 易损件重置方式（0:手动，1:自动）
     */
    @Schema(description = "易损件重置方式（0:手动，1:自动）")
    private Integer resetWay;

    /**
     * 失效期
     */
    @Schema(description = "失效期")
    private LocalDateTime expireDate;

    private WarningMessageDTO warningMessageDto;

    /**
     * 预警信息Dto
     */
    public static class WarningMessageDTO{

        /**
         * 预警 管控类型
         */
        @Schema(description = "预警 管控类型")
        private String controlCategory;

        /**
         * 预警 剩余次数
         */
        @Schema(description = "预警 剩余次数")
        private Integer residueNumber;

        /**
         * 预警 剩余时间
         */
        @Schema(description = "预警 剩余时间")
        private Integer residueTime;

        /**
         * 预警 剩余天数
         */
        @Schema(description = "预警 剩余天数")
        private String residueDay;

        /**
         * 预警信息Dto
         */
        public String getControlCategory() {
            return controlCategory;
        }

        public WarningMessageDTO setControlCategory(String controlCategory) {
            this.controlCategory = controlCategory;
            return this;
        }

        public Integer getResidueNumber() {
            return residueNumber;
        }

        public WarningMessageDTO setResidueNumber(Integer residueNumber) {
            this.residueNumber = residueNumber;
            return this;
        }

        public Integer getResidueTime() {
            return residueTime;
        }

        public WarningMessageDTO setResidueTime(Integer residueTime) {
            this.residueTime = residueTime;
            return this;
        }

        public String getResidueDay() {
            return residueDay;
        }

        public WarningMessageDTO setResidueDay(String residueDay) {
            this.residueDay = residueDay;
            return this;
        }
    }

    public ClientGetWearingPartInfoDTO(){

    }

    public ClientGetWearingPartInfoDTO(BaseClientDTO baseClientDto){
        this.setStatus(baseClientDto.getStatus());
        this.setMessage(baseClientDto.getMessage());
    }

    public Long getWearingPartId() {
        return wearingPartId;
    }

    public ClientGetWearingPartInfoDTO setWearingPartId(Long wearingPartId) {
        this.wearingPartId = wearingPartId;
        return this;
    }

    public String getName() {
        return name;
    }

    public ClientGetWearingPartInfoDTO setName(String name) {
        this.name = name;
        return this;
    }

    public Long getWearingPartGroupId() {
        return wearingPartGroupId;
    }

    public ClientGetWearingPartInfoDTO setWearingPartGroupId(Long wearingPartGroupId) {
        this.wearingPartGroupId = wearingPartGroupId;
        return this;
    }

    public String getCode() {
        return code;
    }

    public ClientGetWearingPartInfoDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public ClientGetWearingPartInfoDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public Integer getMaxResetNumber() {
        return maxResetNumber;
    }

    public ClientGetWearingPartInfoDTO setMaxResetNumber(Integer maxResetNumber) {
        this.maxResetNumber = maxResetNumber;
        return this;
    }

    public Integer getAccumulateResetNumber() {
        return accumulateResetNumber;
    }

    public ClientGetWearingPartInfoDTO setAccumulateResetNumber(Integer accumulateResetNumber) {
        this.accumulateResetNumber = accumulateResetNumber;
        return this;
    }

    public Integer getMaxUseNumber() {
        return maxUseNumber;
    }

    public ClientGetWearingPartInfoDTO setMaxUseNumber(Integer maxUseNumber) {
        this.maxUseNumber = maxUseNumber;
        return this;
    }

    public Integer getAccumulateUseNumber() {
        return accumulateUseNumber;
    }

    public ClientGetWearingPartInfoDTO setAccumulateUseNumber(Integer accumulateUseNumber) {
        this.accumulateUseNumber = accumulateUseNumber;
        return this;
    }

    public Integer getMaxUseTime() {
        return maxUseTime;
    }

    public ClientGetWearingPartInfoDTO setMaxUseTime(Integer maxUseTime) {
        this.maxUseTime = maxUseTime;
        return this;
    }

    public Integer getAccumulateUseTime() {
        return accumulateUseTime;
    }

    public ClientGetWearingPartInfoDTO setAccumulateUseTime(Integer accumulateUseTime) {
        this.accumulateUseTime = accumulateUseTime;
        return this;
    }

    public Integer getResetWay() {
        return resetWay;
    }

    public ClientGetWearingPartInfoDTO setResetWay(Integer resetWay) {
        this.resetWay = resetWay;
        return this;
    }

    public LocalDateTime getExpireDate() {
        return expireDate;
    }

    public ClientGetWearingPartInfoDTO setExpireDate(LocalDateTime expireDate) {
        this.expireDate = expireDate;
        return this;
    }

    public WarningMessageDTO getWarningMessageDto() {
        return warningMessageDto;
    }

    public ClientGetWearingPartInfoDTO setWarningMessageDto(WarningMessageDTO warningMessageDto) {
        this.warningMessageDto = warningMessageDto;
        return this;
    }
}
