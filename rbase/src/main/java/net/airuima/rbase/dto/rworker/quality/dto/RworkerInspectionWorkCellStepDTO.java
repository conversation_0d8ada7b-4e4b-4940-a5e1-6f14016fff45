package net.airuima.rbase.dto.rworker.quality.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/4/25
 */
@Schema(description = "获取待做工序及工位")
public class RworkerInspectionWorkCellStepDTO {

    /**
     * 工序dto
     */
    @Schema(description = "工序dto")
    private StepDTO stepDto;

    /**
     * 工位dto列表
     */
    @Schema(description = "工位dto列表")
    private List<WorkCellDTO> workCellDtos;

    public StepDTO getStepDto() {
        return stepDto;
    }

    public RworkerInspectionWorkCellStepDTO setStepDto(StepDTO stepDto) {
        this.stepDto = stepDto;
        return this;
    }

    public List<WorkCellDTO> getWorkCellDtos() {
        return workCellDtos;
    }

    public RworkerInspectionWorkCellStepDTO setWorkCellDtos(List<WorkCellDTO> workCellDtos) {
        this.workCellDtos = workCellDtos;
        return this;
    }

    @Schema(description = "工位dto")
    public static class WorkCellDTO{

        /**
         * 工位id
         */
        @JsonSerialize(using = ToStringSerializer.class)
        @Schema(description = "工位id")
        private Long id;

        /**
         * 工位编码
         */
        @Schema(description = "工位编码")
        private String code;

        /**
         * 工位名称
         */
        @Schema(description = "工位名称")
        private String name;

        public WorkCellDTO() {
        }

        public WorkCellDTO(WorkCell workCell) {
            this.id = workCell.getId();
            this.code = workCell.getCode();
            this.name = workCell.getName();
        }

        public Long getId() {
            return id;
        }

        public WorkCellDTO setId(Long id) {
            this.id = id;
            return this;
        }

        public String getCode() {
            return code;
        }

        public WorkCellDTO setCode(String code) {
            this.code = code;
            return this;
        }

        public String getName() {
            return name;
        }

        public WorkCellDTO setName(String name) {
            this.name = name;
            return this;
        }
    }

    @Schema(description = "工序dto")
    public static class StepDTO{
        /**
         * 工序id
         */
        @JsonSerialize(using = ToStringSerializer.class)
        @Schema(description = "工序id")
        private Long id;

        /**
         * 工序编码
         */
        @Schema(description = "工序id")
        private String code;

        /**
         * 工序名称
         */
        @Schema(description = "工序名称")
        private String name;

        public StepDTO() {
        }

        public StepDTO(Step step) {
            this.id = step.getId();
            this.code = step.getCode();
            this.name = step.getName();
        }

        public Long getId() {
            return id;
        }

        public StepDTO setId(Long id) {
            this.id = id;
            return this;
        }

        public String getCode() {
            return code;
        }

        public StepDTO setCode(String code) {
            this.code = code;
            return this;
        }

        public String getName() {
            return name;
        }

        public StepDTO setName(String name) {
            this.name = name;
            return this;
        }
    }
}
