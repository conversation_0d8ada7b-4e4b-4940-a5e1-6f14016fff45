package net.airuima.rbase.dto.dynamic;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 动态元数据定义表Domain
 *
 * <AUTHOR>
 * @date 2022-08-25
 */
@Schema(description = "新增子动态元数据DTO")
public class SubStepDynamicDataColumnAddDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 父级动态元数据ID
     */
    @Schema(description = "父级动态元数据ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;

    /**
     * 子级动态元数据集合
     */
    @Schema(description = "子级动态元数据集合")
    private List<SubStepDynamicDataColumnDTO> subStepDynamicDataColumnDTOList;

    /**
     * 子级动态元数据
     **/
    @Schema(description = "子级动态元数据集合")
    public static class SubStepDynamicDataColumnDTO implements Serializable{
        /**
         * ID
         */
        @Schema(description = "ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 名称
         */
        @Schema(description = "名称")
        private String name;

        /**
         * 编码
         */
        @Schema(description = "编码")
        private String code;

        /**
         * 表单展示顺序
         */
        @Schema(description = "表单展示顺序")
        private int formOrder;

        public SubStepDynamicDataColumnDTO() {
        }

        public SubStepDynamicDataColumnDTO(Long id, String name, String code, int formOrder) {
            this.id = id;
            this.name = name;
            this.code = code;
            this.formOrder = formOrder;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public int getFormOrder() {
            return formOrder;
        }

        public void setFormOrder(int formOrder) {
            this.formOrder = formOrder;
        }
    }

    public SubStepDynamicDataColumnAddDTO() {
    }

    public SubStepDynamicDataColumnAddDTO(Long parentId, List<SubStepDynamicDataColumnDTO> subStepDynamicDataColumnDTOList) {
        this.parentId = parentId;
        this.subStepDynamicDataColumnDTOList = subStepDynamicDataColumnDTOList;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public List<SubStepDynamicDataColumnDTO> getSubStepDynamicDataColumnDTOList() {
        return subStepDynamicDataColumnDTOList;
    }

    public void setSubStepDynamicDataColumnDTOList(List<SubStepDynamicDataColumnDTO> subStepDynamicDataColumnDTOList) {
        this.subStepDynamicDataColumnDTOList = subStepDynamicDataColumnDTOList;
    }
}
