package net.airuima.rbase.dto.document;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

/**
 * 文件关联DTO
 *
 * <AUTHOR>
 * @date 2022/8/10
 **/
@Schema(description = "文件关联DTO")
public class DocumentRelationDTO implements Serializable {
    /**
     * 系统名称
     */
    @Schema(description = "系统名称", required = true)
    private String serviceName;

    /**
     * 模块名称
     */
    @Schema(description = "模块名称")
    private String modeName;

    /**
     * 数据ID
     */
    @Schema(description = "数据ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long recordId;

    /**
     * 文件集合
     */
    @Schema(description = "文件集合", required = true)
    private List<Document> documentList;

    /**
     * 文档内部类
     **/
    @Schema(description = "文档内部类")
    public static class Document implements Serializable{
        /**
         * 文件基本信息ID
         */
        @Schema(description = "文件基本信息ID", required = true)
        @JsonSerialize(using = ToStringSerializer.class)
        private Long documentId;

        /**
         * 文件类型
         */
        @Schema(description = "文件类型")
        private Integer category;

        public Document(){

        }

        public Document(DocumentDTO documentDTO){
            this.documentId = documentDTO.getId();
            this.category = documentDTO.getCategory();
        }

        public Long getDocumentId() {
            return documentId;
        }

        public Document setDocumentId(Long documentId) {
            this.documentId = documentId;
            return this;
        }

        public Integer getCategory() {
            return category;
        }

        public Document setCategory(Integer category) {
            this.category = category;
            return this;
        }
    }

    public String getServiceName() {
        return serviceName;
    }

    public DocumentRelationDTO setServiceName(String serviceName) {
        this.serviceName = serviceName;
        return this;
    }

    public String getModeName() {
        return modeName;
    }

    public DocumentRelationDTO setModeName(String modeName) {
        this.modeName = modeName;
        return this;
    }

    public Long getRecordId() {
        return recordId;
    }

    public DocumentRelationDTO setRecordId(Long recordId) {
        this.recordId = recordId;
        return this;
    }

    public List<Document> getDocumentList() {
        return documentList;
    }

    public DocumentRelationDTO setDocumentList(List<Document> documentList) {
        this.documentList = documentList;
        return this;
    }
}
