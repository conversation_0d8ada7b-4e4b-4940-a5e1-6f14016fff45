package net.airuima.rbase.dto.client;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.constant.Constants;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-05-23
 */
@Schema(description = "烘烤取出保存参数")
public class BakeHistoryInfoDTO implements Serializable {

    /**
     * 箱号
     */
    @Schema(description = "箱号")
    private String caseNo;

    /**
     * 容器编码
     */
    @Schema(description = "容器编码")
    private String containerCode;

    /**
     * SN
     */
    @Schema(description = "SN")
    private String sn;
    /**
     * 数量
     */
    @Schema(description = "数量")
    private int number;
    /**
     * 时长范围
     */
    @Schema(description = "时长范围")
    private String durationRange;

    /**
     * 温度范围
     */
    @Schema(description = "温度范围")
    private String temperatureRange;

    /**
     * sn返修次数
     */
    @Schema(description = "sn返修次数")
    private Integer reworkTime = Constants.INT_ZERO;

    public Integer getReworkTime() {
        return reworkTime;
    }

    public BakeHistoryInfoDTO setReworkTime(Integer reworkTime) {
        this.reworkTime = reworkTime;
        return this;
    }


    public String getCaseNo() {
        return caseNo;
    }

    public BakeHistoryInfoDTO setCaseNo(String caseNo) {
        this.caseNo = caseNo;
        return this;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public BakeHistoryInfoDTO setContainerCode(String containerCode) {
        this.containerCode = containerCode;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public BakeHistoryInfoDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public BakeHistoryInfoDTO setNumber(int number) {
        this.number = number;
        return this;
    }

    public String getDurationRange() {
        return durationRange;
    }

    public BakeHistoryInfoDTO setDurationRange(String durationRange) {
        this.durationRange = durationRange;
        return this;
    }

    public String getTemperatureRange() {
        return temperatureRange;
    }

    public BakeHistoryInfoDTO setTemperatureRange(String temperatureRange) {
        this.temperatureRange = temperatureRange;
        return this;
    }
}
