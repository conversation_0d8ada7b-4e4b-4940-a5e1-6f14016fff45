package net.airuima.rbase.dto.client;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;

import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/5/23
 */
@Schema(description = "Rworker上传或者Excel导入保存台位校准数据参数DTO")
public class ClientCalibrateCheckResultSaveDTO {
    /**
     * 台位编号
     */
    @Schema(description = "台位编号")
    @NotEmpty
    @Excel(name = "台位编码", orderNum = "1")
    private String workCellCode;

    /**
     * 测试人员
     */
    @Schema(description = "测试人员")
    @NotEmpty
    @Excel(name = "测试员工编号", orderNum = "2")
    private String tester;

    /**
     * 测试日期
     */
    @Schema(description = "测试日期", example = "2022-05-23 12:32:44")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotEmpty
    @Excel(name = "测试日期", orderNum = "3", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime testTime;

    /**
     * 检测项目编码
     */
    @Schema(description = "检测项目编码")
    @Excel(name = "检测项目编码", orderNum = "4")
    private String checkItemCode;

    /**
     * 标准件子模块/通道
     */
    @Schema(description = "标准件子模块/通道")
    @Excel(name = "标准件子模块", orderNum = "5")
    private String subModule;

    /**
     * 标准件SN
     */
    @Schema(description = "标准件SN")
    @Excel(name = "标准件SN", orderNum = "6")
    private String sn;


    /**
     * 测试值
     */
    @Schema(description = "测试值")
    @Excel(name = "测试值", orderNum = "7")
    private String number;

    /**
     * 设备编码
     */
    @Schema(description = "设备编码")
    @Excel(name = "设备编码", orderNum = "8")
    private String facilityCode;

    /**
     * 校准程序
     **/
    @Schema(description = "校准程序")
    @NotEmpty
    @Excel(name = "校准程序", orderNum = "9")
    private ProcessEnum process;

    /**
     * 校准结果
     */
    @Schema(description = "校准结果")
    @Excel(name = "校准结果", orderNum = "10")
    private String result;

    /**
     * 校准单位
     */
    @Schema(description = "校准单位")
    @Excel(name = "校准单位", orderNum = "11")
    private String calibrateCompany;

    public ClientCalibrateCheckResultSaveDTO() {

    }

    public String getWorkCellCode() {
        return workCellCode;
    }

    public ClientCalibrateCheckResultSaveDTO setWorkCellCode(String workCellCode) {
        this.workCellCode = workCellCode;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public ClientCalibrateCheckResultSaveDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public LocalDateTime getTestTime() {
        return testTime;
    }

    public ClientCalibrateCheckResultSaveDTO setTestTime(LocalDateTime testTime) {
        this.testTime = testTime;
        return this;
    }

    public String getTester() {
        return tester;
    }

    public ClientCalibrateCheckResultSaveDTO setTester(String tester) {
        this.tester = tester;
        return this;
    }

    public String getCheckItemCode() {
        return checkItemCode;
    }

    public ClientCalibrateCheckResultSaveDTO setCheckItemCode(String checkItemCode) {
        this.checkItemCode = checkItemCode;
        return this;
    }

    public String getSubModule() {
        return subModule;
    }

    public ClientCalibrateCheckResultSaveDTO setSubModule(String subModule) {
        this.subModule = subModule;
        return this;
    }

    public String getNumber() {
        return number;
    }

    public ClientCalibrateCheckResultSaveDTO setNumber(String number) {
        this.number = number;
        return this;
    }

    public String getFacilityCode() {
        return facilityCode;
    }

    public void setFacilityCode(String facilityCode) {
        this.facilityCode = facilityCode;
    }

    public ProcessEnum getProcess() {
        return process;
    }

    public void setProcess(String process) {
        this.process = ProcessEnum.getByKey(process);
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getCalibrateCompany() {
        return calibrateCompany;
    }

    public void setCalibrateCompany(String calibrateCompany) {
        this.calibrateCompany = calibrateCompany;
    }

    /**
     * 校准程序枚举
     **/
    public enum ProcessEnum {
        NJ("内校", 0),
        WJ("外校", 1);

        /**
         * 校准程序
         */
        private String key;

        /**
         * 校准程序Code
         */
        private int value;

        ProcessEnum() {
        }

        ProcessEnum(String key, int value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public int getValue() {
            return value;
        }

        public void setValue(int value) {
            this.value = value;
        }

        /**
         * 根据Key，返回枚举
         *
         * @param key
         * @return : ProcessEnum
         * <AUTHOR>
         * @date 2022/7/22
         **/
        public static ProcessEnum getByKey(String key) {
            ProcessEnum result = null;
            for (ProcessEnum processEnum : values()) {
                if (processEnum.getKey().equals(key)) {
                    result = processEnum;
                    break;
                }
            }
            return result;
        }
    }
}
