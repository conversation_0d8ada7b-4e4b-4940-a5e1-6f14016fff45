package net.airuima.rbase.dto.maintain;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import net.airuima.dto.AbstractDto;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
public class MaintainCaseDTO extends AbstractDto {

    /**
     * 维系分析方案编码
     */
    @Schema(description = "维系分析方案编码")
    private String code;

    /**
     * 维系分析方案名称
     */
    @Schema(description = "维系分析方案名称")
    private String name;

    /**
     * 是否启用(0:禁用;1:启用)
     */
    @Schema(description = "是否启用(0:禁用;1:启用)")
    private boolean isEnable;

    public String getCode() {
        return code;
    }

    public MaintainCaseDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getName() {
        return name;
    }

    public MaintainCaseDTO setName(String name) {
        this.name = name;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public MaintainCaseDTO setIsEnable(boolean enable) {
        isEnable = enable;
        return this;
    }
}
