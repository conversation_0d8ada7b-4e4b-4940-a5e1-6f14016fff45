package net.airuima.rbase.dto.bom;

import net.airuima.config.annotation.Forbidden;
import net.airuima.dto.AbstractDto;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2020/12/15
 */
public class BomVersionDTO extends AbstractDto {

    /**
     * 版本名称
     */
    private String name;

    /**
     * 版本编码
     */
    private String code;

    /**
     * 版本定义信息(是否为采购节点|内/外做/外购|新规/流用等信息的定义)
     */
    private String info;

    /**
     * 备注
     */
    private String note;

    /**
     * 是否启用(0:否;1:是)
     */
    @Forbidden
    private Boolean isEnable;

    /**
     * BOM信息
     */
    private BomInfoDTO bomInfo;

    public String getName() {
        return name;
    }

    public BomVersionDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public BomVersionDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getInfo() {
        return info;
    }

    public BomVersionDTO setInfo(String info) {
        this.info = info;
        return this;
    }

    public String getNote() {
        return note;
    }

    public BomVersionDTO setNote(String note) {
        this.note = note;
        return this;
    }

    public Boolean getIsEnable() {
        return isEnable;
    }

    public BomVersionDTO setIsEnable(Boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    public BomInfoDTO getBomInfo() {
        return bomInfo;
    }

    public BomVersionDTO setBomInfo(BomInfoDTO bomInfo) {
        this.bomInfo = bomInfo;
        return this;
    }
}
