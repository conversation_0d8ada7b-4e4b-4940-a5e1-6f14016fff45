package net.airuima.rbase.dto.maintain;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.AbstractDto;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;

import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
public class MaintainHistoryDTO extends AbstractDto {


    /**
     * 维修分析方案
     */
    @Schema(description = "维修分析方案")
    private MaintainCaseDTO maintainCase;

    /**
     * 工单
     */
    @Schema(description = "工单")
    private WorkSheet workSheet;

    /**
     * 子工单
     */
    @Schema(description = "子工单")
    private SubWorkSheet subWorkSheet;

    /**
     * 容器详情
     */
    @Schema(description = "容器详情")
    private ContainerDetail containerDetail;

    /**
     * sn状态信息
     */
    @Schema(description = "sn状态信息")
    private SnWorkStatus snWorkStatus;


    /**
     * 维修数量
     */
    @Schema(description = "维修数量")
    private int number;


    /**
     * 处理方式：-1:待处理，0：报废，1：返工;2:放行，3：退库
     */
    @Schema(description = "处理方式：-1:待处理，0：报废，1：返工;2:放行；3：退库")
    private int result;

    /**
     * 最新维修状态：0：待分析, 1:待维修, 2:完成
     */
    @Schema(description = "最新维修状态：0：待分析, 1:待维修, 2:完成")
    private int status;

    /**
     * 维修开始时间
     */
    @Schema(description = "维修开始时间")
    private LocalDateTime startDate;

    /**
     * 维修结束时间
     */
    @Schema(description = "维修结束时间")
    private LocalDateTime endDate;


    /**
     * 不良项Id
     */

    @Schema(description = "不良项Id")
    private UnqualifiedItem unqualifiedItem;


    /**
     * 不良产生工序Id
     */
    @Schema(description = "不良产生工序Id")
    private Step step;


    /**
     * 维修原因
     */
    @Schema(description = "维修原因")
    private String reason;

    /**
     * 返修流程(0:返修工艺路线, 1:原工艺路线,2:多种返修路线)
     */
    @Schema(description = "返修流程(0:返修工艺路线, 1:原工艺路线,2:多种返修路线)", required = true)
    private int reworkCategory;

    /**
     * 开始返工的工序ID
     */
    @Schema(description = "开始返工的工序ID")
    private Step reWorkStep;

    /**
     * 是否需要替换料(0:否;1:是)
     */
    @Schema(description = "是否替换料(0:否;1:是)")
    private boolean isReplaceMaterial;

    public MaintainCaseDTO getMaintainCase() {
        return maintainCase;
    }

    public MaintainHistoryDTO setMaintainCase(MaintainCaseDTO maintainCase) {
        this.maintainCase = maintainCase;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public MaintainHistoryDTO setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public MaintainHistoryDTO setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public ContainerDetail getContainerDetail() {
        return containerDetail;
    }

    public MaintainHistoryDTO setContainerDetail(ContainerDetail containerDetail) {
        this.containerDetail = containerDetail;
        return this;
    }

    public SnWorkStatus getSnWorkStatus() {
        return snWorkStatus;
    }

    public MaintainHistoryDTO setSnWorkStatus(SnWorkStatus snWorkStatus) {
        this.snWorkStatus = snWorkStatus;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public MaintainHistoryDTO setNumber(int number) {
        this.number = number;
        return this;
    }

    public int getResult() {
        return result;
    }

    public MaintainHistoryDTO setResult(int result) {
        this.result = result;
        return this;
    }

    public int getStatus() {
        return status;
    }

    public MaintainHistoryDTO setStatus(int status) {
        this.status = status;
        return this;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public MaintainHistoryDTO setStartDate(LocalDateTime startDate) {
        this.startDate = startDate;
        return this;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public MaintainHistoryDTO setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
        return this;
    }

    public UnqualifiedItem getUnqualifiedItem() {
        return unqualifiedItem;
    }

    public MaintainHistoryDTO setUnqualifiedItem(UnqualifiedItem unqualifiedItem) {
        this.unqualifiedItem = unqualifiedItem;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public MaintainHistoryDTO setStep(Step step) {
        this.step = step;
        return this;
    }

    public String getReason() {
        return reason;
    }

    public MaintainHistoryDTO setReason(String reason) {
        this.reason = reason;
        return this;
    }

    public int getReworkCategory() {
        return reworkCategory;
    }

    public MaintainHistoryDTO setReworkCategory(int reworkCategory) {
        this.reworkCategory = reworkCategory;
        return this;
    }

    public Step getReWorkStep() {
        return reWorkStep;
    }

    public MaintainHistoryDTO setReWorkStep(Step reWorkStep) {
        this.reWorkStep = reWorkStep;
        return this;
    }

    public boolean getIsReplaceMaterial() {
        return isReplaceMaterial;
    }

    public MaintainHistoryDTO setIsReplaceMaterial(boolean replaceMaterial) {
        isReplaceMaterial = replaceMaterial;
        return this;
    }
}
