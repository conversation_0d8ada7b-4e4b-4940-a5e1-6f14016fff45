package net.airuima.rbase.dto.rfms;

import java.time.LocalDate;
import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 * 获取设备状态变更记录查询参数DTO
 * <AUTHOR>
 */
public class FacilityStatusChangeRequestDTO {

    /**
     * 记录日期
     */
    private LocalDate recordDate;

    /**
     * 设备ID列表
     */
    private List<Long> facilityIds;

    /**
     * 目标状态
     */
    private Integer targetStatus;


    /**
     * 是否获取满足条件的最新记录
     */
    private Boolean latest;

    public LocalDate getRecordDate() {
        return recordDate;
    }

    public FacilityStatusChangeRequestDTO setRecordDate(LocalDate recordDate) {
        this.recordDate = recordDate;
        return this;
    }


    public List<Long> getFacilityIds() {
        return facilityIds;
    }

    public FacilityStatusChangeRequestDTO setFacilityIds(List<Long> facilityIds) {
        this.facilityIds = facilityIds;
        return this;
    }

    public Integer getTargetStatus() {
        return targetStatus;
    }

    public FacilityStatusChangeRequestDTO setTargetStatus(Integer targetStatus) {
        this.targetStatus = targetStatus;
        return this;
    }

    public Boolean getLatest() {
        return latest;
    }

    public FacilityStatusChangeRequestDTO setLatest(Boolean latest) {
        this.latest = latest;
        return this;
    }
}
