package net.airuima.rbase.dto.control;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import net.airuima.dto.AbstractDto;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/7/13
 */
@Schema(description = "事件记录DTO")
public class EventRecordDTO extends AbstractDto implements Serializable {

    /**
     * 事件单号
     */
    @Schema(description = "事件单号", required = false)
    private String serialNumber;

    /**
     * 预警对象(-1:无;0:工位;1:子工单;2:工单;3:产品谱系;4:设备;5:员工;6:物料;7:工序;8:产线;9:车间)
     */
    @Schema(description = "预警对象(-1:无;0:工位;1:子工单;2:工单;3:产品谱系;4:设备;5:员工;6:物料;7:工序;8:产线;9:车间)")
    private Integer category;

    /**
     * 事件配置ID
     */
    @Schema(description = "事件配置ID", required = true)
    private EventConfigDTO eventConfig;

    /**
     * 处理状态(-1:审批中;0:未处理;1:已处理)
     */
    @Schema(description = "处理状态(-1:审批中;0:未处理;1:已处理)")
    private Integer status;

    /**
     * 事件级别(0:一般，1:严重，2:重大，3:特大)
     */
    @Schema(description = "事件级别(0:一般，1:严重，2:重大，3:特大)")
    private Integer level;

    /**
     * 事件紧急程度(0:紧急重要、1:紧急不重要、2:重要不紧急、3:不重要不紧急)
     */
    @Schema(description = "事件紧急程度(0:紧急重要、1:紧急不重要、2:重要不紧急、3:不重要不紧急)")
    private Integer urgency;

    /**
     * 事件描述
     */
    @Schema(description = "事件描述")
    private String description;

    /**
     * 预警对象编码
     */
    @Schema(description = "预警对象编码")
    private String warningTargetCode;

    /**
     * 记录日期时间
     */
    @Schema(description = "记录日期")
    private LocalDateTime recordTime;

    public LocalDateTime getRecordTime() {
        return recordTime;
    }

    public EventRecordDTO setRecordTime(LocalDateTime recordTime) {
        this.recordTime = recordTime;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public EventRecordDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public EventRecordDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public EventConfigDTO getEventConfig() {
        return eventConfig;
    }

    public EventRecordDTO setEventConfig(EventConfigDTO eventConfig) {
        this.eventConfig = eventConfig;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public EventRecordDTO setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public Integer getLevel() {
        return level;
    }

    public EventRecordDTO setLevel(Integer level) {
        this.level = level;
        return this;
    }

    public Integer getUrgency() {
        return urgency;
    }

    public EventRecordDTO setUrgency(Integer urgency) {
        this.urgency = urgency;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public EventRecordDTO setDescription(String description) {
        this.description = description;
        return this;
    }

    public String getWarningTargetCode() {
        return warningTargetCode;
    }

    public EventRecordDTO setWarningTargetCode(String warningTargetCode) {
        this.warningTargetCode = warningTargetCode;
        return this;
    }
}
