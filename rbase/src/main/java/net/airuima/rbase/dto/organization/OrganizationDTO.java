package net.airuima.rbase.dto.organization;

import net.airuima.config.annotation.Forbidden;
import net.airuima.dto.AbstractDto;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2020/12/15
 */
public class OrganizationDTO extends AbstractDto implements Serializable {

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 组织类型
     */
    private Integer orgType;

    /**
     * 父级组织id
     */
    private OrganizationDTO parentOrganization;

    /**
     * 父级组织类型
     */
    private Long parentType;

    /**
     * 公司地址
     */
    private String address;

    /**
     * ip
     */
    private String ip;

    /**
     * flag
     */
    private Integer flag;

    /**
     * 是否启用
     */
    @Forbidden
    private Boolean isEnable;

    public String getName() {
        return name;
    }

    public OrganizationDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public OrganizationDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public Integer getOrgType() {
        return orgType;
    }

    public OrganizationDTO setOrgType(Integer orgType) {
        this.orgType = orgType;
        return this;
    }

    public OrganizationDTO getParentOrganization() {
        return parentOrganization;
    }

    public OrganizationDTO setParentOrganization(OrganizationDTO parentOrganization) {
        this.parentOrganization = parentOrganization;
        return this;
    }

    public Long getParentType() {
        return parentType;
    }

    public OrganizationDTO setParentType(Long parentType) {
        this.parentType = parentType;
        return this;
    }

    public String getAddress() {
        return address;
    }

    public OrganizationDTO setAddress(String address) {
        this.address = address;
        return this;
    }

    public String getIp() {
        return ip;
    }

    public OrganizationDTO setIp(String ip) {
        this.ip = ip;
        return this;
    }

    public Integer getFlag() {
        return flag;
    }

    public OrganizationDTO setFlag(Integer flag) {
        this.flag = flag;
        return this;
    }

    public Boolean getIsEnable() {
        return isEnable;
    }

    public OrganizationDTO setIsEnable(Boolean enable) {
        isEnable = enable;
        return this;
    }
}
