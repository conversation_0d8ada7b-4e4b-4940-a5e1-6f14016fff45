package net.airuima.rbase.dto.sync;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.AbstractDto;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *  同步物料-》目前与（sap和erp都使用此dto）
 *
 * <AUTHOR>
 * @date 2021-06-04
 */
public class SyncMaterialDTO extends AbstractDto {

    /**
     * 物料名称
     */
    @Schema(description = "物料名称")
    private String name;

    /**
     * 物料编码
     */
    @Schema(description = "物料编码")
    private String code;

    /**
     * 主辅类型(1:主料;0:辅料)
     */
    @Schema(description = "主辅类型(1:主料;0:辅料)")
    private Integer materialCategory;


    /**
     * 物料组别(0:原材料;1:半成品;2:成品)
     */
    @Schema(description = "物料组别(0:原材料;1:半成品;2:成品)")
    private Integer materialGroup;

    /**
     * 规格型号
     */
    @Schema(description = "规格型号")
    private String specification;

    /**
     * 单位
     */
    @Schema(description = "单位")
    private String unit;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 同步类型(0:新增;1:修改;2:删除;3:禁用;4:启用)
     */
    @Schema(description = "同步类型(0:新增;1:修改;2:删除;3:禁用;4:启用)")
    private Integer operate;


    public String getName() {
        return name;
    }

    public SyncMaterialDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public SyncMaterialDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public Integer getMaterialCategory() {
        return materialCategory;
    }

    public SyncMaterialDTO setMaterialCategory(Integer materialCategory) {
        this.materialCategory = materialCategory;
        return this;
    }

    public Integer getMaterialGroup() {
        return materialGroup;
    }

    public SyncMaterialDTO setMaterialGroup(Integer materialGroup) {
        this.materialGroup = materialGroup;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public SyncMaterialDTO setSpecification(String specification) {
        this.specification = specification;
        return this;
    }

    public String getUnit() {
        return unit;
    }

    public SyncMaterialDTO setUnit(String unit) {
        this.unit = unit;
        return this;
    }

    public Integer getOperate() {
        return operate;
    }

    public SyncMaterialDTO setOperate(Integer operate) {
        this.operate = operate;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public SyncMaterialDTO setDescription(String description) {
        this.description = description;
        return this;
    }
}
