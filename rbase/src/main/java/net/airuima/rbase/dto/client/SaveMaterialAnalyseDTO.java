package net.airuima.rbase.dto.client;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/10/8
 */
@Schema(description = "Rworker保存维修分析记录信息")
public class SaveMaterialAnalyseDTO {
    /**
     * 维修分析类型：0：sn单支,1:容器
     */
    @Schema(description = "维修分析类型：0：sn单支,1:容器")
    private Integer maintainType;

    /**
     * 当前维修工序
     */
    @Schema(description = "当前维修工序")
    private Integer currentMaintainStep;

    /**
     * 维修分析方案Id
     */
    @Schema(description = "维修分析方案Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long maintainCaseId;

    /**
     * sn
     */
    @Schema(description = "sn")
    private String sn;

    /**
     * 容器编码
     */
    @Schema(description = "容器编码")
    private String containerCode;

    /**
     * 处理方式：0：报废，1：返工，3：退库
     */
    @Schema(description = "处理方式：0：报废，1：返工，3：退库")
    private Integer result;

    /**
     * 员工id
     */
    @Schema(description = "员工id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long staffId;

    /**
     * 维修时间
     */
    @Schema(description = "维修时间")
    private LocalDateTime recordDate;

    /**
     * 维修分析原因
     */
    @Schema(description = "维修分析原因")
    private String reason;

    /**
     * 退补料清单
     */
    @Schema(description = "退补料清单")
    private List<MaintainMaterialExchangeInfo> maintainMaterialExchangeInfos;

    /**
     * 返修工艺路线
     */
    @Schema(description = "返修工艺路线")
    private String workFlowCode;

    /**
     * 返修指定开始工序
     */
    @Schema(description = "返修指定开始工序")
    private String stepCode;

    public String getStepCode() {
        return stepCode;
    }

    public SaveMaterialAnalyseDTO setStepCode(String stepCode) {
        this.stepCode = stepCode;
        return this;
    }

    public String getWorkFlowCode() {
        return workFlowCode;
    }

    public SaveMaterialAnalyseDTO setWorkFlowCode(String workFlowCode) {
        this.workFlowCode = workFlowCode;
        return this;
    }

    public Long getMaintainCaseId() {
        return maintainCaseId;
    }

    public SaveMaterialAnalyseDTO setMaintainCaseId(Long maintainCaseId) {
        this.maintainCaseId = maintainCaseId;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public SaveMaterialAnalyseDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public SaveMaterialAnalyseDTO setContainerCode(String containerCode) {
        this.containerCode = containerCode;
        return this;
    }

    public Integer getResult() {
        return result;
    }

    public SaveMaterialAnalyseDTO setResult(Integer result) {
        this.result = result;
        return this;
    }

    public Long getStaffId() {
        return staffId;
    }

    public SaveMaterialAnalyseDTO setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public LocalDateTime getRecordDate() {
        return recordDate;
    }

    public SaveMaterialAnalyseDTO setRecordDate(LocalDateTime recordDate) {
        this.recordDate = recordDate;
        return this;
    }

    public String getReason() {
        return reason;
    }

    public SaveMaterialAnalyseDTO setReason(String reason) {
        this.reason = reason;
        return this;
    }

    public List<MaintainMaterialExchangeInfo> getMaintainMaterialExchangeInfos() {
        return maintainMaterialExchangeInfos;
    }

    public SaveMaterialAnalyseDTO setMaintainMaterialExchangeInfos(List<MaintainMaterialExchangeInfo> maintainMaterialExchangeInfos) {
        this.maintainMaterialExchangeInfos = maintainMaterialExchangeInfos;
        return this;
    }

    public Integer getMaintainType() {
        return maintainType;
    }

    public SaveMaterialAnalyseDTO setMaintainType(Integer maintainType) {
        this.maintainType = maintainType;
        return this;
    }

    public Integer getCurrentMaintainStep() {
        return currentMaintainStep;
    }

    public SaveMaterialAnalyseDTO setCurrentMaintainStep(Integer currentMaintainStep) {
        this.currentMaintainStep = currentMaintainStep;
        return this;
    }

    @Schema(description = "退补料信息")
    public static class MaintainMaterialExchangeInfo{
        @Schema(description = "退料物料详情")
        private MaterialBatchInfo returnMaterialInfo;
        @Schema(description = "替换料详情")
        private MaterialBatchInfo replaceMaterialInfo;

        public MaterialBatchInfo getReturnMaterialInfo() {
            return returnMaterialInfo;
        }

        public MaintainMaterialExchangeInfo setReturnMaterialInfo(MaterialBatchInfo returnMaterialInfo) {
            this.returnMaterialInfo = returnMaterialInfo;
            return this;
        }

        public MaterialBatchInfo getReplaceMaterialInfo() {
            return replaceMaterialInfo;
        }

        public MaintainMaterialExchangeInfo setReplaceMaterialInfo(MaterialBatchInfo replaceMaterialInfo) {
            this.replaceMaterialInfo = replaceMaterialInfo;
            return this;
        }
    }

    @Schema(description = "物料批次基础信息")
    public static class MaterialBatchInfo{
        @JsonSerialize(using = ToStringSerializer.class)
        @Schema(description = "物料id")
        private Long materialId;

        @Schema(description = "物料批次")
        private String materialBatch;

        @Schema(description = "数量")
        private Double number;

        public Long getMaterialId() {
            return materialId;
        }

        public MaterialBatchInfo setMaterialId(Long materialId) {
            this.materialId = materialId;
            return this;
        }

        public String getMaterialBatch() {
            return materialBatch;
        }

        public MaterialBatchInfo setMaterialBatch(String materialBatch) {
            this.materialBatch = materialBatch;
            return this;
        }

        public Double getNumber() {
            return number;
        }

        public MaterialBatchInfo setNumber(Double number) {
            this.number = number;
            return this;
        }
    }
}
