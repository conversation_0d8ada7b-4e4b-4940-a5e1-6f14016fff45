package net.airuima.rbase.dto.client;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "修改容器编码DTO")
public class UpdateContainerCodeDTO {

    @Schema(description = "容器id")
    private Long containerId;
    
    @Schema(description = "新容器编码")
    private String containerCode;

    @Schema(description = "子工单id")
    private Long subWorkSheetId;

    public Long getContainerId() {
        return containerId;
    }

    public UpdateContainerCodeDTO setContainerId(Long containerId) {
        this.containerId = containerId;
        return this;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public UpdateContainerCodeDTO setContainerCode(String containerCode) {
        this.containerCode = containerCode;
        return this;
    }

    public Long getSubWorkSheetId() {
        return subWorkSheetId;
    }

    public UpdateContainerCodeDTO setSubWorkSheetId(Long subWorkSheetId) {
        this.subWorkSheetId = subWorkSheetId;
        return this;
    }
}
