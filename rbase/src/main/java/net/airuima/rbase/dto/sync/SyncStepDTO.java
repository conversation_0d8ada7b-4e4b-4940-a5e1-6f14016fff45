package net.airuima.rbase.dto.sync;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/3/14
 */
@Schema(description = "工序同步DTO")
public class SyncStepDTO {

    /**
     * 工序id
     */
    @Schema(description = "工序id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 工序名称
     */
    @Schema(description = "工序名称")
    private String name;

    /**
     *工序编码 唯一值
     */
    @Schema(description = "工序编码")
    private String code;

    /**
     * 工序类型
     */
    @Schema(description = "工序类型")
    private Integer category;

    /**
     * 同步类型(0:新增;1:修改;2:删除;3:禁用;4:启用)
     */
    @Schema(description = "同步类型(0:新增;1:修改;2:删除;3:禁用;4:启用)")
    private Integer operate;

    public Long getId() {
        return id;
    }

    public SyncStepDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getName() {
        return name;
    }

    public SyncStepDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public SyncStepDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public SyncStepDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public Integer getOperate() {
        return operate;
    }

    public SyncStepDTO setOperate(Integer operate) {
        this.operate = operate;
        return this;
    }
}
