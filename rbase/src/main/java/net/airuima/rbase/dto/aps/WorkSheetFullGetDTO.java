package net.airuima.rbase.dto.aps;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.base.process.StepGroup;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.WsStep;

import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2020-12-24
 */
@Schema(description = "工单完整信息(包含工单信息、工序快照及投料单信息)")
public class WorkSheetFullGetDTO extends WorkSheetSimpleGetDTO {

    /**
     * 工单生产工序快照列表
     */
    @ArraySchema(schema = @Schema(description = "工单生产工序快照信息列表",implementation = StepBaseDTO.class))
    private List<StepBaseDTO> stepDtoList;

    /**
     * 工单投料单快照列表
     */
    @ArraySchema(schema = @Schema(description = "工单投料单快照信息列表",implementation = MaterialBaseDTO.class))
    private List<MaterialBaseDTO> wsMaterialDTOList;

    public WorkSheetFullGetDTO(){

    }

    public WorkSheetFullGetDTO(WorkSheet workSheet){
        super(workSheet);
    }
    
    public List<StepBaseDTO> getStepDtoList() {
        return stepDtoList;
    }

    public WorkSheetFullGetDTO setStepDtoList(List<StepBaseDTO> stepDtoList) {
        this.stepDtoList = stepDtoList;
        return this;
    }

    public List<MaterialBaseDTO> getWsMaterialDTOList() {
        return wsMaterialDTOList;
    }

    public WorkSheetFullGetDTO setWsMaterialDTOList(List<MaterialBaseDTO> wsMaterialDTOList) {
        this.wsMaterialDTOList = wsMaterialDTOList;
        return this;
    }

    public static class StepBaseDTO {
        /**
         * 工序主键ID
         */
        @JsonSerialize(using = ToStringSerializer.class)
        @Schema(description = "工序主键ID", type = "integer", format = "int64")
        private Long id;

        /**
         * 工序名称
         */
        @Schema(description = "工序名称", type = "string")
        private String name;

        /**
         * 工序编码
         */
        @Schema(description = "工序编码", type = "string")
        private String code;

        /**
         * 工序类型
         */
        @Schema(description = "工序类型", type = "int", format = "int32", example = "0:生产工序")
        private int category;

        /**
         * 工序所属工序组别
         */
        @Schema(description = "工序所属工序组别", implementation = StepGroupBaseDTO.class)
        private StepGroupBaseDTO stepGroup;

        /**
         * 前置工序列表，分号隔开
         */
        @Schema(description = "前置工序主键ID列表(id之间以分号隔开)", type = "string", example = "343434343,343432,343434")
        private String preStepId;

        /**
         * 后置工序列表，分号隔开
         */
        @Schema(description = "后置工序主键ID列表(id之间以分号隔开)", type = "string", example = "343434343,343432,343434,")
        private String afterStepId;


        /**
         * 工序请求模式
         */
        @Schema(description = "工序请求模式(0:工单请求;1:容器请求;2:单支请求)", type = "integer", format = "int32", example = "0")
        private Integer requestMode;

        /**
         * 管控模式(0:批量;1:单支)
         */
        @Schema(description = "生产管控模式(0:批量;1:单支)", type = "integer", format = "int32", example = "0")
        private Integer controlMode;

        /**
         * 生产是否管控物料(false:不管控;true:管控)
         */
        @Schema(description = "生产是否管控物料(false:否;true:是)", type = "boolean", example = "false")
        private Boolean isControlMaterial;

        /**
         * 是否绑定容器(0:否;1:是)
         */
        @Schema(description = "生产是否绑定容器(false:否;true:是)", type = "boolean", example = "false")
        private Boolean isBindContainer;


        /**
         * 工序投产比例
         */
        @Schema(description = "工序投产比例", type = "number", format = "double", example = "1.0")
        private Double inputRate;

        public StepBaseDTO() {

        }

        public StepBaseDTO(WsStep wsStep) {
            this.id = wsStep.getStep().getId();
            this.name = wsStep.getStep().getName();
            this.code = wsStep.getStep().getCode();
            this.category = wsStep.getCategory();
            this.stepGroup = Objects.nonNull(wsStep.getStep().getStepGroup()) ? new StepGroupBaseDTO(wsStep.getStep().getStepGroup()) : null;
            this.preStepId = wsStep.getPreStepId();
            this.afterStepId = wsStep.getAfterStepId();
            this.requestMode = wsStep.getRequestMode();
            this.controlMode = wsStep.getControlMode();
            this.isControlMaterial = wsStep.getIsControlMaterial();
            this.isBindContainer = wsStep.getIsBindContainer();
            this.inputRate = wsStep.getInputRate();
        }

        public Long getId() {
            return id;
        }

        public StepBaseDTO setId(Long id) {
            this.id = id;
            return this;
        }

        public String getName() {
            return name;
        }

        public StepBaseDTO setName(String name) {
            this.name = name;
            return this;
        }

        public String getCode() {
            return code;
        }

        public StepBaseDTO setCode(String code) {
            this.code = code;
            return this;
        }

        public int getCategory() {
            return category;
        }

        public StepBaseDTO setCategory(int category) {
            this.category = category;
            return this;
        }

        public StepGroupBaseDTO getStepGroup() {
            return stepGroup;
        }

        public StepBaseDTO setStepGroup(StepGroupBaseDTO stepGroup) {
            this.stepGroup = stepGroup;
            return this;
        }

        public String getPreStepId() {
            return preStepId;
        }

        public StepBaseDTO setPreStepId(String preStepId) {
            this.preStepId = preStepId;
            return this;
        }

        public String getAfterStepId() {
            return afterStepId;
        }

        public StepBaseDTO setAfterStepId(String afterStepId) {
            this.afterStepId = afterStepId;
            return this;
        }

        public Integer getRequestMode() {
            return requestMode;
        }

        public StepBaseDTO setRequestMode(Integer requestMode) {
            this.requestMode = requestMode;
            return this;
        }

        public Integer getControlMode() {
            return controlMode;
        }

        public StepBaseDTO setControlMode(Integer controlMode) {
            this.controlMode = controlMode;
            return this;
        }

        public Boolean getControlMaterial() {
            return isControlMaterial;
        }

        public StepBaseDTO setControlMaterial(Boolean controlMaterial) {
            isControlMaterial = controlMaterial;
            return this;
        }

        public Boolean getBindContainer() {
            return isBindContainer;
        }

        public StepBaseDTO setBindContainer(Boolean bindContainer) {
            isBindContainer = bindContainer;
            return this;
        }

        public Double getInputRate() {
            return inputRate;
        }

        public StepBaseDTO setInputRate(Double inputRate) {
            this.inputRate = inputRate;
            return this;
        }

        @Schema(description = "工序组别基础信息")
        public static class StepGroupBaseDTO {

            /**
             * 工序组别主键ID
             */
            @JsonSerialize(using = ToStringSerializer.class)
            @Schema(description = "工序组别主键ID", type = "integer", format = "int64")
            private Long id;

            /**
             * 工序组别主键名称
             */
            @Schema(description = "工序组别主键名称", type = "string")
            private String name;

            /**
             * 工序组别主键编码
             */
            @Schema(description = "工序组别主键编码", type = "string")
            private String code;

            public StepGroupBaseDTO() {
            }

            public StepGroupBaseDTO(StepGroup stepGroup) {
                this.id = stepGroup.getId();
                this.code = stepGroup.getCode();
                this.name = stepGroup.getName();
            }

            public Long getId() {
                return id;
            }

            public StepGroupBaseDTO setId(Long id) {
                this.id = id;
                return this;
            }

            public String getName() {
                return name;
            }

            public StepGroupBaseDTO setName(String name) {
                this.name = name;
                return this;
            }

            public String getCode() {
                return code;
            }

            public StepGroupBaseDTO setCode(String code) {
                this.code = code;
                return this;
            }
        }

    }

    public static class MaterialBaseDTO {

        /**
         * 物料主键ID
         */
        @JsonSerialize(using = ToStringSerializer.class)
        @Schema(description = "物料主键ID", type = "integer", format = "int64")
        private Long id;

        /**
         * 投料单主键ID
         */
        @JsonSerialize(using = ToStringSerializer.class)
        @Schema(description = "投料单主键ID", type = "integer", format = "int64")
        private Long wsMaterialId;

        /**
         * 物料名称
         */
        @Schema(description = "物料名称", type = "string")
        private String name;

        /**
         * 物料代码
         */
        @Schema(description = "物料代码", type = "string")
        private String code;

        /**
         * 物料规格型号
         */
        @Schema(description = "物料规格型号", type = "string")
        private String specification;

        /**
         * 物料数量
         */
        @Schema(description = "物料数量",type = "number",format = "double")
        private Double number;

        /**
         * 是否为倒冲物料
         */
        @Schema(description = "是否为倒冲物料")
        private Boolean backFlush;

        /**
         * 替代料列表
         */
        @ArraySchema(schema = @Schema(description = "替代料列表", implementation = MaterialBaseDTO.class))
        private List<MaterialBaseDTO> replaceMaterialInfoList;

        public Long getId() {
            return id;
        }

        public MaterialBaseDTO setId(Long id) {
            this.id = id;
            return this;
        }

        public Long getWsMaterialId() {
            return wsMaterialId;
        }

        public MaterialBaseDTO setWsMaterialId(Long wsMaterialId) {
            this.wsMaterialId = wsMaterialId;
            return this;
        }

        public String getName() {
            return name;
        }

        public MaterialBaseDTO setName(String name) {
            this.name = name;
            return this;
        }

        public String getCode() {
            return code;
        }

        public MaterialBaseDTO setCode(String code) {
            this.code = code;
            return this;
        }

        public String getSpecification() {
            return specification;
        }

        public MaterialBaseDTO setSpecification(String specification) {
            this.specification = specification;
            return this;
        }

        public Double getNumber() {
            return number;
        }

        public MaterialBaseDTO setNumber(Double number) {
            this.number = number;
            return this;
        }

        public Boolean getBackFlush() {
            return backFlush;
        }

        public MaterialBaseDTO setBackFlush(Boolean backFlush) {
            this.backFlush = backFlush;
            return this;
        }

        public List<MaterialBaseDTO> getReplaceMaterialInfoList() {
            return replaceMaterialInfoList;
        }

        public MaterialBaseDTO setReplaceMaterialInfoList(List<MaterialBaseDTO> replaceMaterialInfoList) {
            this.replaceMaterialInfoList = replaceMaterialInfoList;
            return this;
        }
    }
}
