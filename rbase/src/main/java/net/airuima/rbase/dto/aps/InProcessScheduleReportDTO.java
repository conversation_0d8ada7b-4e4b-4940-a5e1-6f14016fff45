package net.airuima.rbase.dto.aps;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.Instant;
import java.time.LocalDateTime;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/6/20
 */
public class InProcessScheduleReportDTO {

    /**
     *  投产工单
     */
    @Schema(description = "投产工单")
    private String serialNumber;
    /**
     * 产品谱系编码
     */
    @Schema(description = "产品谱系编码")
    private String pedigreeCode;
    /**
     * 产品谱系名称
     */
    @Schema(description = "产品谱系名称")
    private String pedigreeName;


    /**
     * 产品谱系规格型号
     */
    @Schema(description = "产品谱系规格型号")
    private String specification;
    /**
     * 下达时间
     */
    @Schema(description = "下达时间")
    private Instant createDate;
    /**
     * 计划开工时间
     */
    @Schema(description = "计划开工时间")
    private LocalDateTime planStartDate;
    /**
     * 计划完工时间
     */
    @Schema(description = "计划完工时间")
    private LocalDateTime planEndDate;
    /**
     * 工单投产数量
     */
    @Schema(description = "工单投产数量")
    private Integer number;
    /**
     * 工位编码
     */
    @Schema(description = "工位编码")
    private String workCellCode;
    /**
     * 工位名称
     */
    @Schema(description = "工位名称")
    private String workCellName;
    /**
     * 工序编码
     */
    @Schema(description = "工序编码")
    private String stepCode;
    /**
     * 工序名称
     */
    @Schema(description = "工序名称")
    private String stepName;
    /**
     * 工序工位投产数
     */
    @Schema(description = "工序工位投产数")
    private Long inputNumber;
    /**
     * 是否完成
     */
    @Schema(description = "是否完成")
    private Integer isFinish;

    public InProcessScheduleReportDTO() {
    }

    public InProcessScheduleReportDTO(String serialNumber, String pedigreeCode, String pedigreeName, Instant createDate, LocalDateTime planStartDate, LocalDateTime planEndDate, Integer number, String workCellCode, String workCellName, String stepCode, String stepName, Long inputNumber, Integer isFinish,String specification) {
        this.serialNumber = serialNumber;
        this.pedigreeCode = pedigreeCode;
        this.pedigreeName = pedigreeName;
        this.createDate = createDate;
        this.planStartDate = planStartDate;
        this.planEndDate = planEndDate;
        this.number = number;
        this.workCellCode = workCellCode;
        this.workCellName = workCellName;
        this.stepCode = stepCode;
        this.stepName = stepName;
        this.inputNumber = inputNumber;
        this.isFinish = isFinish;
        this.specification = specification;
    }

    public String getSpecification() {
        return specification;
    }

    public InProcessScheduleReportDTO setSpecification(String specification) {
        this.specification = specification;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public InProcessScheduleReportDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public String getPedigreeCode() {
        return pedigreeCode;
    }

    public InProcessScheduleReportDTO setPedigreeCode(String pedigreeCode) {
        this.pedigreeCode = pedigreeCode;
        return this;
    }

    public String getPedigreeName() {
        return pedigreeName;
    }

    public InProcessScheduleReportDTO setPedigreeName(String pedigreeName) {
        this.pedigreeName = pedigreeName;
        return this;
    }

    public Instant getCreateDate() {
        return createDate;
    }

    public InProcessScheduleReportDTO setCreateDate(Instant createDate) {
        this.createDate = createDate;
        return this;
    }

    public LocalDateTime getPlanStartDate() {
        return planStartDate;
    }

    public InProcessScheduleReportDTO setPlanStartDate(LocalDateTime planStartDate) {
        this.planStartDate = planStartDate;
        return this;
    }

    public LocalDateTime getPlanEndDate() {
        return planEndDate;
    }

    public InProcessScheduleReportDTO setPlanEndDate(LocalDateTime planEndDate) {
        this.planEndDate = planEndDate;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public InProcessScheduleReportDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public String getWorkCellCode() {
        return workCellCode;
    }

    public InProcessScheduleReportDTO setWorkCellCode(String workCellCode) {
        this.workCellCode = workCellCode;
        return this;
    }

    public String getWorkCellName() {
        return workCellName;
    }

    public InProcessScheduleReportDTO setWorkCellName(String workCellName) {
        this.workCellName = workCellName;
        return this;
    }

    public String getStepCode() {
        return stepCode;
    }

    public InProcessScheduleReportDTO setStepCode(String stepCode) {
        this.stepCode = stepCode;
        return this;
    }

    public String getStepName() {
        return stepName;
    }

    public InProcessScheduleReportDTO setStepName(String stepName) {
        this.stepName = stepName;
        return this;
    }

    public Long getInputNumber() {
        return inputNumber;
    }

    public InProcessScheduleReportDTO setInputNumber(Long inputNumber) {
        this.inputNumber = inputNumber;
        return this;
    }

    public Integer getIsFinish() {
        return isFinish;
    }

    public InProcessScheduleReportDTO setIsFinish(Integer isFinish) {
        this.isFinish = isFinish;
        return this;
    }
}
