package net.airuima.rbase.dto.base;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * mes基础信息返回类
 *
 * <AUTHOR>
 * @date 2021/12/21
 */
@Schema(description = "结果信息DTO")
public class BaseDTO {

    /**
     * 结果返回状态
     */
    @Schema(description = "OK/KO")
    private String status;

    /**
     * 结果返回消息
     */
    @Schema(description = "异常提醒信息")
    private String message;

    public BaseDTO() {
    }

    public BaseDTO(String status) {
        this.status = status;
    }

    public BaseDTO(String status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getStatus() {
        return status;
    }

    public BaseDTO setStatus(String status) {
        this.status = status;
        return this;
    }

    public String getMessage() {
        return message;
    }

    public BaseDTO setMessage(String message) {
        this.message = message;
        return this;
    }
}
