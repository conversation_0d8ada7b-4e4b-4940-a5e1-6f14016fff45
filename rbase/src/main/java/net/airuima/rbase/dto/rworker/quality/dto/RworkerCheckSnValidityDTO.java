package net.airuima.rbase.dto.rworker.quality.dto;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/5/6
 */
public class RworkerCheckSnValidityDTO {

    private Boolean result;

    private Boolean real;

    public RworkerCheckSnValidityDTO() {
    }

    public RworkerCheckSnValidityDTO(Boolean result, Boolean real) {
        this.result = result;
        this.real = real;
    }

    public Boolean getResult() {
        return result;
    }

    public RworkerCheckSnValidityDTO setResult(Boolean result) {
        this.result = result;
        return this;
    }

    public Boolean getReal() {
        return real;
    }

    public RworkerCheckSnValidityDTO setReal(Boolean real) {
        this.real = real;
        return this;
    }
}
