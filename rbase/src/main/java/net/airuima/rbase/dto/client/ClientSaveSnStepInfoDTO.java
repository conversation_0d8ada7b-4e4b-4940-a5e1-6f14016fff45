package net.airuima.rbase.dto.client;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.custom.CustomDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * Rwork保存 SN工序参数列表
 * <AUTHOR>
 * @date 2021-03-18
 */
@Schema(description = "Rwork单支SN保存工序参数")
public class ClientSaveSnStepInfoDTO extends CustomDTO {

    /**
     * 当前投产SN
     */
    @Schema(description = "SN")
    private String sn;

    /**
     * 当前SN返修次数
     */
    @Schema(description = "SN返修次数")
    private Integer reworkTime;

    /**
     * 结果(0不合格,1合格)
     */
    @Schema(description = "SN结果(0不合格,1合格)")
    private Integer result;

    /**
     * 是否人工复判
     */
    @Schema(description = "是否人工复判(默认true)")
    private Boolean isReInspect;

    /**
     * 当前子工单id
     */
    @Schema(description = "投产子工单ID")
    private Long subWsId;

    /**
     * 当前工序的id
     */
    @Schema(description = "投产子工序ID")
    private Long stepId;

    /**
     * 当前操作人id
     */
    @Schema(description = "操作人ID")
    private Long staffId;

    /**
     * 员工工位id
     */
    @Schema(description = "工位ID")
    private Long workCellId;

    /**
     * 流程框图ID
     */
    @Schema(description = "流程框图ID")
    private Long workFlowId;

    /**
     * 工序开始时间
     */
    @Schema(description = "工序开始时间")
    private LocalDateTime startTime;

    /**
     * 工序完成日期
     */
    @Schema(description = "工序完成时间")
    private LocalDateTime endTime;

    /**
     * 不良项目ID
     */
    @Schema(description = "不良项目ID")
    private Long unqualifiedItemId;

    /**
     * 物料批次列表
     */
    @Schema(description = "物料批次列表(包含特殊批次)")
    private List<MaterialBatchInfo> materialBatchInfoList;

    /**
     * 设备列表
     */
    @Schema(description = "设备列表")
    private List<EquipmentInfo> equipmentInfoList;
    /**
     * 防重复提交Token(采用32位uuid)
     */
    @Schema(description = "防重复提交Token")
    private String xsrfToken;

    /**
     * 烘烤温循老化类型(0:放入;1:取出)
     */
    @Schema(description = "烘烤温循老化类型(0:放入;1:取出)")
    private Integer bakeFlag;
    @Schema(description = "烘烤历史信息")
    private BakeHistoryInfoDTO bakeHistoryInfo;
    @Schema(description = "温循历史信息")
    private CycleBakeHistoryInfoDTO cycleBakeHistoryInfo;
    @Schema(description = "老化历史信息")
    private AgeingHistoryInfoDTO ageingHistoryInfo;

    public Integer getBakeFlag() {
        return bakeFlag;
    }

    public ClientSaveSnStepInfoDTO setBakeFlag(Integer bakeFlag) {
        this.bakeFlag = bakeFlag;
        return this;
    }

    public BakeHistoryInfoDTO getBakeHistoryInfo() {
        return bakeHistoryInfo;
    }

    public ClientSaveSnStepInfoDTO setBakeHistoryInfo(BakeHistoryInfoDTO bakeHistoryInfo) {
        this.bakeHistoryInfo = bakeHistoryInfo;
        return this;
    }

    public CycleBakeHistoryInfoDTO getCycleBakeHistoryInfo() {
        return cycleBakeHistoryInfo;
    }

    public ClientSaveSnStepInfoDTO setCycleBakeHistoryInfo(CycleBakeHistoryInfoDTO cycleBakeHistoryInfo) {
        this.cycleBakeHistoryInfo = cycleBakeHistoryInfo;
        return this;
    }

    public AgeingHistoryInfoDTO getAgeingHistoryInfo() {
        return ageingHistoryInfo;
    }

    public ClientSaveSnStepInfoDTO setAgeingHistoryInfo(AgeingHistoryInfoDTO ageingHistoryInfo) {
        this.ageingHistoryInfo = ageingHistoryInfo;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public ClientSaveSnStepInfoDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public Integer getResult() {
        return result;
    }

    public ClientSaveSnStepInfoDTO setResult(Integer result) {
        this.result = result;
        return this;
    }

    public Long getSubWsId() {
        return subWsId;
    }

    public ClientSaveSnStepInfoDTO setSubWsId(Long subWsId) {
        this.subWsId = subWsId;
        return this;
    }

    public Long getStepId() {
        return stepId;
    }

    public ClientSaveSnStepInfoDTO setStepId(Long stepId) {
        this.stepId = stepId;
        return this;
    }

    public Long getStaffId() {
        return staffId;
    }

    public ClientSaveSnStepInfoDTO setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public Long getWorkCellId() {
        return workCellId;
    }

    public ClientSaveSnStepInfoDTO setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
        return this;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public ClientSaveSnStepInfoDTO setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
        return this;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public ClientSaveSnStepInfoDTO setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
        return this;
    }

    public Long getUnqualifiedItemId() {
        return unqualifiedItemId;
    }

    public ClientSaveSnStepInfoDTO setUnqualifiedItemId(Long unqualifiedItemId) {
        this.unqualifiedItemId = unqualifiedItemId;
        return this;
    }

    public List<MaterialBatchInfo> getMaterialBatchInfoList() {
        return materialBatchInfoList;
    }

    public ClientSaveSnStepInfoDTO setMaterialBatchInfoList(List<MaterialBatchInfo> materialBatchInfoList) {
        this.materialBatchInfoList = materialBatchInfoList;
        return this;
    }

    public List<EquipmentInfo> getEquipmentInfoList() {
        return equipmentInfoList;
    }

    public ClientSaveSnStepInfoDTO setEquipmentInfoList(List<EquipmentInfo> equipmentInfoList) {
        this.equipmentInfoList = equipmentInfoList;
        return this;
    }

    public Integer getReworkTime() {
        return reworkTime;
    }

    public ClientSaveSnStepInfoDTO setReworkTime(Integer reworkTime) {
        this.reworkTime = reworkTime;
        return this;
    }

    public Boolean getIsReInspect() {
        return isReInspect;
    }

    public ClientSaveSnStepInfoDTO setIsReInspect(Boolean isReInspect) {
        this.isReInspect = isReInspect;
        return this;
    }

    public Long getWorkFlowId() {
        return workFlowId;
    }

    public ClientSaveSnStepInfoDTO setWorkFlowId(Long workFlowId) {
        this.workFlowId = workFlowId;
        return this;
    }

    public String getXsrfToken() {
        return xsrfToken;
    }

    public ClientSaveSnStepInfoDTO setXsrfToken(String xsrfToken) {
        this.xsrfToken = xsrfToken;
        return this;
    }

    /**
     * 常规物料批次信息
     */
    @Schema(description = "物料批次列表(包含常规批次和特殊批次)")
    public static class MaterialBatchInfo {

        /**
         * 物料批次
         */
        @Schema(description = "物料批次号")
        private String materialBatch;

        /**
         * 供应商编码
         */
        @Schema(description = "供应商编码")
        private String supplierCode;

        /**
         * 批次流水号
         */
        @Schema(description = "批次流水号")
        private String serial;

        /**
         * 物料id
         */
        @Schema(description = "物料ID")
        private Long materialId;

        public String getMaterialBatch() {
            return materialBatch;
        }

        public MaterialBatchInfo setMaterialBatch(String materialBatch) {
            this.materialBatch = materialBatch;
            return this;
        }

        public String getSerial() {
            return serial;
        }

        public MaterialBatchInfo setSerial(String serial) {
            this.serial = serial;
            return this;
        }

        public Long getMaterialId() {
            return materialId;
        }

        public MaterialBatchInfo setMaterialId(Long materialId) {
            this.materialId = materialId;
            return this;
        }

        public String getSupplierCode() {
            return supplierCode;
        }

        public MaterialBatchInfo setSupplierCode(String supplierCode) {
            this.supplierCode = supplierCode;
            return this;
        }
    }

    /**
     * 设备信息
     *
     * <AUTHOR>
     * @date 2021-01-18
     **/
    @Schema(description = "生产工序设备列表")
    public static class EquipmentInfo {
        /**
         * 设备ID
         */
        @Schema(description = "设备ID")
        private Long equipmentId;
        /**
         * 设备编码
         */
        @Schema(description = "设备编码")
        private String equipmentCode;
        /**
         * 设备名称
         */
        @Schema(description = "设备名称")
        private String equipmentName;

        public Long getEquipmentId() {
            return equipmentId;
        }

        public EquipmentInfo setEquipmentId(Long equipmentId) {
            this.equipmentId = equipmentId;
            return this;
        }

        public String getEquipmentCode() {
            return equipmentCode;
        }

        public EquipmentInfo setEquipmentCode(String equipmentCode) {
            this.equipmentCode = equipmentCode;
            return this;
        }

        public String getEquipmentName() {
            return equipmentName;
        }

        public EquipmentInfo setEquipmentName(String equipmentName) {
            this.equipmentName = equipmentName;
            return this;
        }
    }

}
