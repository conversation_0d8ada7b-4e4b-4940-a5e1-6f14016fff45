package net.airuima.rbase.dto.qms;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.AbstractDto;
import net.airuima.rbase.dto.bom.MeteringUnitDTO;
import net.airuima.rbase.dto.document.DocumentDTO;

import java.io.Serializable;
import java.util.List;

@Schema(name = "检测项目库(CheckItemDTO)", description = "检测项目库")
public class CheckItemDTO extends AbstractDto implements Serializable {

    /**
     * 检测项名称
     */
    @Schema(description = "检测项名称")
    private String name;

    /**
     * 检测项编码
     */
    @Schema(description = "检测项编码")
    private String code;

    /**
     * 合格范围(开闭区间或者OK)
     */
    @Schema(description = "合格范围(开闭区间或者OK)")
    private String qualifiedRange;
    /**
     * 检测项单位
     */
    @Schema(description = "检测项单位")
    private String unit;
    /**
     * 禁用启用(0:禁用;1:启用)
     */
    @Schema(description = "禁用启用(0:禁用;1:启用)")
    private boolean isEnable;

    /**
     * 项目类型
     */
    @Schema(description = "项目类型")
    private Integer variety;

    /**
     * 是否管控其检查结果(0:不管控；1：管控)
     */
    @Schema(description = "是否管控其检查结果(0:不管控；1：管控)")
    private boolean control;

    /**
     * 计量单位id
     */
    @Schema(description = "计量单位id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long unitId;

    @Schema(description = "计量单位")
    private MeteringUnitDTO unitDto;

    /**
     * 项目类型
     */
    @Schema(description = "项目类型")
    private VarietyDTO varietyObj;

    /**
     * 检验方法:目测0/检测仪器1
     */
    @Schema(description = "检验方法:目测0/检测仪器1")
    private int inspectWay;

    /**
     * 分析方法:定性0/定量1
     */
    @Schema(description = "分析方法:定性0/定量1")
    private int analyseWay;

    /**
     * 检测仪器
     */
    @Schema(description = "检测仪器")
    private String facility;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 绑定的文档列表
     */
    @Schema(description = "绑定的文档列表")
    private List<DocumentDTO> documentDTOList;

    public CheckItemDTO() {

    }

    public Integer getVariety() {
        return variety;
    }

    public CheckItemDTO setVariety(Integer variety) {
        this.variety = variety;
        return this;
    }

    public CheckItemDTO(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public CheckItemDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public CheckItemDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getQualifiedRange() {
        return qualifiedRange;
    }

    public CheckItemDTO setQualifiedRange(String qualifiedRange) {
        this.qualifiedRange = qualifiedRange;
        return this;
    }

    public String getUnit() {
        return unit;
    }

    public CheckItemDTO setUnit(String unit) {
        this.unit = unit;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public CheckItemDTO setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    public boolean getControl() {
        return control;
    }

    public CheckItemDTO setControl(boolean control) {
        this.control = control;
        return this;
    }

    public Long getUnitId() {
        return unitId;
    }

    public CheckItemDTO setUnitId(Long unitId) {
        this.unitId = unitId;
        return this;
    }

    public MeteringUnitDTO getUnitDto() {
        return unitDto;
    }

    public CheckItemDTO setUnitDto(MeteringUnitDTO unitDto) {
        this.unitDto = unitDto;
        return this;
    }

    public VarietyDTO getVarietyObj() {
        return varietyObj;
    }

    public CheckItemDTO setVarietyObj(VarietyDTO varietyObj) {
        this.varietyObj = varietyObj;
        return this;
    }

    public int getInspectWay() {
        return inspectWay;
    }

    public CheckItemDTO setInspectWay(int inspectWay) {
        this.inspectWay = inspectWay;
        return this;
    }

    public int getAnalyseWay() {
        return analyseWay;
    }

    public CheckItemDTO setAnalyseWay(int analyseWay) {
        this.analyseWay = analyseWay;
        return this;
    }

    public String getFacility() {
        return facility;
    }

    public CheckItemDTO setFacility(String facility) {
        this.facility = facility;
        return this;
    }

    public String getNote() {
        return note;
    }

    public CheckItemDTO setNote(String note) {
        this.note = note;
        return this;
    }

    public List<DocumentDTO> getDocumentDTOList() {
        return documentDTOList;
    }

    public CheckItemDTO setDocumentDTOList(List<DocumentDTO> documentDTOList) {
        this.documentDTOList = documentDTOList;
        return this;
    }
}
