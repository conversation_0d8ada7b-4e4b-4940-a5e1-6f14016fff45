package net.airuima.rbase.dto.quality;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 温湿度检测历史表DTO
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Schema(description = "温湿度检测历史表DTO")
public class HumitureCheckHistoryDTO implements Serializable {
    /**
     * 部门ID
     */
    @Schema(description = "部门ID", required = true)
    private Long organizationId;

    /**
     * 温度
     */
    @Schema(description = "温度", required = true)
    private BigDecimal temperature;

    /**
     * 湿度
     */
    @Schema(description = "湿度", required = true)
    private BigDecimal humidity;

    /**
     * 结果（0：不合格, 1：合格）
     */
    @Schema(description = "结果（0：不合格, 1：合格）", required = true)
    private Boolean result;

    /**
     * 检验人ID
     */
    @Schema(description = "检验人ID", required = true)
    private Long operatorId;

    /**
     * 检验日期
     */
    @Schema(description = "检验日期", required = true)
    private LocalDateTime recordDate;

    public BigDecimal getTemperature() {
        return temperature;
    }

    public void setTemperature(BigDecimal temperature) {
        this.temperature = temperature;
    }

    public BigDecimal getHumidity() {
        return humidity;
    }

    public void setHumidity(BigDecimal humidity) {
        this.humidity = humidity;
    }

    public boolean getResult() {
        return result;
    }

    public HumitureCheckHistoryDTO setResult(boolean result) {
        this.result = result;
        return this;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }


    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public LocalDateTime getRecordDate() {
        return recordDate;
    }

    public void setRecordDate(LocalDateTime recordDate) {
        this.recordDate = recordDate;
    }

}
