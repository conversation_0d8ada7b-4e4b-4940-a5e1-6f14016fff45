package net.airuima.rbase.dto.rlms;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *  获取引用动态前缀的模块DTO
 *
 * <AUTHOR>
 * @date 2022/5/16
 */
@Schema(description = "获取引用动态前缀的模块DTO")
public class ModuleConfigDTO {

    /**
     * 模块编码
     */
    @Schema(description = "模块编码")
    private String moduleCode;

    /**
     * 前缀类型(0:无前缀;1:固定前缀;2:动态前缀)
     */
    @Schema(description = "前缀类型(0:无前缀;1:固定前缀;2:动态前缀)")
    private Integer category;

    public String getModuleCode() {
        return moduleCode;
    }

    public ModuleConfigDTO setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public ModuleConfigDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }
}
