package net.airuima.rbase.dto.client;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.WsStep;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/2/1
 */
@Schema(description = "请求获取待做工序参数")
public class ClientGetTodoStepInfoDTO {

    /**
     * 原容器请求列表
     */
    @Schema(description = "原容器请求列表")
    private List<Long> requestContainerIds;
    /**
     * 绑定新容器id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "绑定新容器id")
    private Long bindContainerId;
    /**
     * subWorkSheet
     */
    @Schema(description = "子工单")
    private SubWorkSheet subWorkSheet;

    /**
     * 工单
     */
    @Schema(description = "工单")
    private WorkSheet workSheet;
    /**
     * wsStepList
     */
    @Schema(description = "工单工序快照")
    private List<WsStep> wsStepList;
    /**
     * 工位绑定的工序
     */
    @Schema(description = "工位绑定的工序")
    private List<Step> stepList;

    public ClientGetTodoStepInfoDTO() {
    }

    public ClientGetTodoStepInfoDTO(SubWorkSheet subWorkSheet, List<WsStep> wsStepList, List<Step> stepList) {
        this.subWorkSheet = subWorkSheet;
        this.wsStepList = wsStepList;
        this.stepList = stepList;
    }

    public ClientGetTodoStepInfoDTO(List<Long> requestContainerIds, Long bindContainerId, SubWorkSheet subWorkSheet, List<WsStep> wsStepList, List<Step> stepList) {
        this.requestContainerIds = requestContainerIds;
        this.bindContainerId = bindContainerId;
        this.subWorkSheet = subWorkSheet;
        this.wsStepList = wsStepList;
        this.stepList = stepList;
    }

    public ClientGetTodoStepInfoDTO(WorkSheet workSheet, List<WsStep> wsStepList, List<Step> stepList) {
        this.workSheet = workSheet;
        this.wsStepList = wsStepList;
        this.stepList = stepList;
    }

    public ClientGetTodoStepInfoDTO(WorkSheet workSheet,List<Long> requestContainerIds, Long bindContainerId, List<WsStep> wsStepList, List<Step> stepList) {
        this.requestContainerIds = requestContainerIds;
        this.bindContainerId = bindContainerId;
        this.workSheet = workSheet;
        this.wsStepList = wsStepList;
        this.stepList = stepList;
    }



    public List<Long> getRequestContainerIds() {
        return requestContainerIds;
    }

    public ClientGetTodoStepInfoDTO setRequestContainerIds(List<Long> requestContainerIds) {
        this.requestContainerIds = requestContainerIds;
        return this;
    }

    public Long getBindContainerId() {
        return bindContainerId;
    }

    public ClientGetTodoStepInfoDTO setBindContainerId(Long bindContainerId) {
        this.bindContainerId = bindContainerId;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public ClientGetTodoStepInfoDTO setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public List<WsStep> getWsStepList() {
        return wsStepList;
    }

    public ClientGetTodoStepInfoDTO setWsStepList(List<WsStep> wsStepList) {
        this.wsStepList = wsStepList;
        return this;
    }

    public List<Step> getStepList() {
        return stepList;
    }

    public ClientGetTodoStepInfoDTO setStepList(List<Step> stepList) {
        this.stepList = stepList;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public ClientGetTodoStepInfoDTO setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }
}
