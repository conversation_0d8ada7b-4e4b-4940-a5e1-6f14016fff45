package net.airuima.rbase.dto.wip;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.AbstractDto;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.dto.organization.StaffDTO;

import java.io.Serializable;
import java.util.Objects;

/**
 * 线边仓DTO
 */
@Schema(name = "线边仓(WipWarehouse)", description = "线边仓")
public class WipWarehouseDTO extends AbstractDto implements Serializable {

    /**
     * 线边仓编码
     */
    @Schema(description = "线边仓编码", required = true)
    private String code;

    /**
     * 线边仓名称
     */
    @Schema(description = "线边仓名称", required = true)
    private String name;

    /**
     * 生产线
     */
    @Schema(description = "生产线")
    private WorkLine workLine;

    /**
     * 负责人
     */
    @Schema(description = "负责人", required = true)
    private Long operatorId;

    /**
     * 操作人DTO
     */
    private StaffDTO operatorDto;

    /**
     * 是否启用(0:否;1:是)
     */
    @Schema(description = "是否启用(0:否;1:是)", required = true)
    private boolean isEnable;

    public String getCode() {
        return code;
    }

    public WipWarehouseDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getName() {
        return name;
    }

    public WipWarehouseDTO setName(String name) {
        this.name = name;
        return this;
    }

    public WorkLine getWorkLine() {
        return workLine;
    }

    public WipWarehouseDTO setWorkLine(WorkLine workLine) {
        this.workLine = workLine;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public WipWarehouseDTO setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public StaffDTO getOperatorDto() {
        return operatorDto;
    }

    public WipWarehouseDTO setOperatorDto(StaffDTO operatorDto) {
        this.operatorDto = operatorDto;
        return this;
    }

    public boolean isEnable() {
        return isEnable;
    }

    public WipWarehouseDTO setEnable(boolean enable) {
        isEnable = enable;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        WipWarehouseDTO that = (WipWarehouseDTO) o;
        return isEnable == that.isEnable && Objects.equals(code, that.code) && Objects.equals(name, that.name) && Objects.equals(workLine, that.workLine) && Objects.equals(operatorId, that.operatorId) && Objects.equals(operatorDto, that.operatorDto);
    }

    @Override
    public int hashCode() {
        return Objects.hash(code, name, workLine, operatorId, operatorDto, isEnable);
    }
}
