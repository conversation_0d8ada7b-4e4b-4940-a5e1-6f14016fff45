package net.airuima.rbase.dto.flowable;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/6/10
 */
@Schema(description = "用户可发起的流程DTO")
public class FlowProcessDefinitionDTO {
    /**
     * 流程定义ID
     */
    @Schema(description = "流程定义ID")
    private String processDefinitionId;

    /**
     * 流程定义KEY
     */
    @Schema(description = "流程定义KEY")
    private String processDefinitionKey;

    /**
     * 流程定义名称
     */
    @Schema(description = "流程定义名称")
    private String processDefinitionName;

    /**
     * 流程定义版本
     */
    @Schema(description = "流程定义版本")
    private Integer processDefinitionVersion;


    /**
     * 流程类型
     */
    @Schema(description = "流程类型")
    private String category;

    /**
     * 是否激活
     */
    @Schema(description = "是否激活")
    private Boolean isActive;

    public String getProcessDefinitionId() {
        return processDefinitionId;
    }

    public void setProcessDefinitionId(String processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
    }

    public String getProcessDefinitionKey() {
        return processDefinitionKey;
    }

    public void setProcessDefinitionKey(String processDefinitionKey) {
        this.processDefinitionKey = processDefinitionKey;
    }

    public String getProcessDefinitionName() {
        return processDefinitionName;
    }

    public void setProcessDefinitionName(String processDefinitionName) {
        this.processDefinitionName = processDefinitionName;
    }

    public Integer getProcessDefinitionVersion() {
        return processDefinitionVersion;
    }

    public void setProcessDefinitionVersion(Integer processDefinitionVersion) {
        this.processDefinitionVersion = processDefinitionVersion;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Boolean getActive() {
        return isActive;
    }

    public void setActive(Boolean active) {
        isActive = active;
    }
}
