package net.airuima.rbase.service.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.batch.ContainerDetailUnqualifiedItem;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.repository.procedure.batch.ContainerDetailUnqualifiedItemRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器工作详情不良明细表Service
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ContainerDetailUnqualifiedItemService extends CommonJpaService<ContainerDetailUnqualifiedItem> {
    private static final String CONTAINER_DETAIL_UNQUALIFIED_ITEM_ENTITY_GRAPH = "containerDetailUnqualifiedItemEntityGraph";
    private final ContainerDetailUnqualifiedItemRepository containerDetailUnqualifiedItemRepository;

    public ContainerDetailUnqualifiedItemService(ContainerDetailUnqualifiedItemRepository containerDetailUnqualifiedItemRepository) {
        this.containerDetailUnqualifiedItemRepository = containerDetailUnqualifiedItemRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<ContainerDetailUnqualifiedItem> find(Specification<ContainerDetailUnqualifiedItem> spec, Pageable pageable) {
        return containerDetailUnqualifiedItemRepository.findAll(spec, pageable,new NamedEntityGraph(CONTAINER_DETAIL_UNQUALIFIED_ITEM_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<ContainerDetailUnqualifiedItem> find(Specification<ContainerDetailUnqualifiedItem> spec) {
        return containerDetailUnqualifiedItemRepository.findAll(spec,new NamedEntityGraph(CONTAINER_DETAIL_UNQUALIFIED_ITEM_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<ContainerDetailUnqualifiedItem> findAll(Pageable pageable) {
        return containerDetailUnqualifiedItemRepository.findAll(pageable,new NamedEntityGraph(CONTAINER_DETAIL_UNQUALIFIED_ITEM_ENTITY_GRAPH));
    }

    /**
     * 根据容器详情ID获取不良项目信息
     * <AUTHOR>
     * @param containerDetailId   容器详情ID
     * @return List<ContainerDetailUnqualifiedItem>
     * @date 2021-04-15
     **/
    @Transactional(readOnly = true)
    public List<ContainerDetailUnqualifiedItem> findByContainerDetailId(Long containerDetailId){
        return containerDetailUnqualifiedItemRepository.findByContainerDetailIdAndDeleted(containerDetailId, Constants.LONG_ZERO);
    }

}
