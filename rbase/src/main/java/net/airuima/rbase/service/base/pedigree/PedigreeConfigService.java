package net.airuima.rbase.service.base.pedigree;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeConfig;
import net.airuima.rbase.repository.base.pedigree.PedigreeConfigRepository;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系属性Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PedigreeConfigService extends CommonJpaService<PedigreeConfig> implements PedigreeConfigInterface {

    private static final String PEDIGREE_CONFIG_ENTITY_GRAPH = "pedigreeConfigEntityGraph";

    private final PedigreeConfigRepository pedigreeConfigRepository;

    public PedigreeConfigService(PedigreeConfigRepository pedigreeConfigRepository) {
        this.pedigreeConfigRepository = pedigreeConfigRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PedigreeConfig> find(Specification<PedigreeConfig> spec, Pageable pageable) {
        return pedigreeConfigRepository.findAll(spec, pageable, new NamedEntityGraph(PEDIGREE_CONFIG_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public List<PedigreeConfig> find(Specification<PedigreeConfig> spec) {
        return pedigreeConfigRepository.findAll(spec, new NamedEntityGraph(PEDIGREE_CONFIG_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PedigreeConfig> findAll(Pageable pageable) {
        return pedigreeConfigRepository.findAll(pageable, new NamedEntityGraph(PEDIGREE_CONFIG_ENTITY_GRAPH));
    }


    /**
     * 通过产品谱系主键ID查询产品谱系配置
     * @param pedigreeId 产品谱系主键ID
     * @return net.airuima.rbase.domain.base.pedigree.PedigreeConfig 产品谱系配置
     */
    @Override
    public PedigreeConfig findByPedigreeId(Long pedigreeId) {
        return pedigreeConfigRepository.findByPedigreeIdAndDeleted(pedigreeId, Constants.LONG_ZERO).orElse(null);
    }

    /**
     * 通过产品谱系名称或者编码和查询数量是否启用是否复用sn查找产品谱系列表
     * @param text 产品谱系名称或者编码
     * @param size 询数量
     * @param isEnable 是否启用
     * @param isReuseSn 是否复用sn
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.Pedigree> 产品谱系列表
     */
    @Override
    public List<Pedigree> findByPedigreeCodeOrPedigreeNameAndIsReuseSn(String text, Integer size, Boolean isEnable, Boolean isReuseSn) {
        List<Pedigree> pedigreeList = Optional.ofNullable(pedigreeConfigRepository
                .findByPedigreeCodeOrPedigreeNameAndIsReuseSn(text, isReuseSn, PageRequest.of(Constants.INT_ZERO, size))).map(Slice::getContent).orElse(null);
        if (!CollectionUtils.isEmpty(pedigreeList)) {
            return null != isEnable ? pedigreeList.stream().filter(pedigree -> isEnable == pedigree.getIsEnable()).collect(Collectors.toList()) : pedigreeList;
        }
        return pedigreeList;
    }

    /**
     * 启用/禁用指定产品谱系配置
     * @param pedigreeConfigId 产品谱系配置主键ID
     */
    @Override
    public void enableByPedigreeConfigId(Long pedigreeConfigId) {
        PedigreeConfig pedigreeConfig = pedigreeConfigRepository.findById(pedigreeConfigId).orElseThrow(() -> new ResponseException("error.pedigreeConfigNotExist", "产品谱系属性不存在"));
        pedigreeConfig.setIsEnable(!pedigreeConfig.getIsEnable());
        pedigreeConfigRepository.save(pedigreeConfig);
    }
}
