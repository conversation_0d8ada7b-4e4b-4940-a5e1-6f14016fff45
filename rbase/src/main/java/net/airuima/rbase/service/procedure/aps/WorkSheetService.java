package net.airuima.rbase.service.procedure.aps;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import com.google.common.base.Joiner;
import jakarta.persistence.criteria.Expression;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.query.QueryCondition;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.client.feign.flowable.FlowableFeign;
import net.airuima.rbase.client.feign.flowable.ProcessDefinitionFeign;
import net.airuima.rbase.client.feign.flowable.ProcessInstanceFeign;
import net.airuima.rbase.client.feign.flowable.ProcessTaskFeign;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.domain.procedure.aps.SaleOrder;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.CustomPedigreeStep;
import net.airuima.rbase.domain.procedure.batch.WsMaterial;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.dto.aps.*;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.bom.BomDTO;
import net.airuima.rbase.dto.bom.BomInfoDTO;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.digiwin.WorkSheetMaterialDTO;
import net.airuima.rbase.dto.flowable.FlowableResultDTO;
import net.airuima.rbase.dto.flowable.FlowableTaskCompleteDTO;
import net.airuima.rbase.dto.flowable.ProcessRunTimeEventDTO;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.dto.organization.OrganizationDTO;
import net.airuima.rbase.dto.process.StepDTO;
import net.airuima.rbase.dto.process.WorkFlowDTO;
import net.airuima.rbase.proxy.bom.RbaseBomProxy;
import net.airuima.rbase.proxy.bom.RbaseMaterialProxy;
import net.airuima.rbase.proxy.organization.RbaseClientProxy;
import net.airuima.rbase.proxy.organization.RbaseOrganizationProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeConfigRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.repository.base.scene.WorkLineRepository;
import net.airuima.rbase.repository.procedure.aps.*;
import net.airuima.rbase.repository.procedure.batch.*;
import net.airuima.rbase.repository.procedure.material.WsCheckMaterialRepository;
import net.airuima.rbase.repository.procedure.material.WsMaterialBatchRepository;
import net.airuima.rbase.service.base.process.WorkFlowStepService;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.procedure.material.IWsMaterialService;
import net.airuima.rbase.service.report.api.IWorkSheetStepStatisticsService;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.rbase.util.TransactionUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rbase.web.rest.procedure.aps.dto.SaleOrderProcessDTO;
import net.airuima.service.CommonJpaService;
import net.airuima.util.BeanUtil;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseException;
import net.airuima.util.SecurityUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CachePut;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产总工单Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkSheetService extends CommonJpaService<WorkSheet> {
    private static final String WORK_SHEET_ENTITY_GRAPH = "workSheetEntityGraph";
    private static final String FLOWABLE_IS_REDEFINE_PROCESS = "isReDefineProcess";
    private static final String FLOWABLE_IS_APPROVED = "isApproved";
    private static final String FLOWABLE_REAPPLY = "reApply";
    private static final String WORK_SHEET_NOT_EXIST_MSG = "生产工单不存在";
    private static final String WORK_SHEET_NOT_EXIST = "workSheetNotExist";

    private static final String IS_OVERDUE = "isOverdue";
    private static final String ACTUAL_END_DATE = "actualEndDate";
    private static final String PLAN_END_DATE = "planEndDate";

    private final WorkSheetRepository workSheetRepository;
    private final WsStepRepository wsStepRepository;
    private final WsMaterialRepository wsMaterialRepository;
    @Autowired
    private RbaseOrganizationProxy rbaseOrganizationProxy;
    @Autowired
    private RbaseMaterialProxy rbaseMaterialProxy;
    private final PedigreeConfigRepository pedigreeConfigRepository;
    private final PedigreeRepository pedigreeRepository;
    private final BatchWorkDetailRepository batchWorkDetailRepository;
    private final SubWorkSheetRepository subWorkSheetRepository;
    private final StepRepository stepRepository;
    private final WorkFlowRepository workFlowRepository;
    private final CommonService commonService;
    private final CustomPedigreeStepRepository customPedigreeStepRepository;
    private final WsMaterialBatchRepository wsMaterialBatchRepository;
    private final SaleOrderRepository saleOrderRepository;
    @Autowired
    private FlowableFeign flowableFeign;
    @Autowired
    private WsReworkRepository wsReworkRepository;
    @Autowired
    private WorkFlowStepService workFlowStepService;
    @Autowired
    private WorkLineRepository workLineRepository;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private ProcessInstanceFeign processInstanceFeign;
    @Autowired
    private ProcessDefinitionFeign processDefinitionFeign;
    @Autowired
    private ProcessTaskFeign processTaskFeign;
    @Autowired
    private WsCheckMaterialRepository wsCheckMaterialRepository;
    @Autowired
    private SubWorkSheetService subWorkSheetService;
    @Autowired
    private RbaseClientProxy rbaseClientProxy;

    @Autowired
    private IWorkSheetStepStatisticsService[] workSheetStepStatisticsServices;
    @Autowired
    private CascadeWorkSheetRepository cascadeWorkSheetRepository;
    @Autowired
    private RbaseBomProxy rbaseBomProxy;

    public WorkSheetService(WorkSheetRepository workSheetRepository, WsStepRepository wsStepRepository, WsMaterialRepository wsMaterialRepository,
                              PedigreeConfigRepository pedigreeConfigRepository, PedigreeRepository pedigreeRepository,
                            BatchWorkDetailRepository batchWorkDetailRepository, SubWorkSheetRepository subWorkSheetRepository, StepRepository stepRepository, WorkFlowRepository workFlowRepository, CommonService commonService,
                            CustomPedigreeStepRepository customPedigreeStepRepository, WsMaterialBatchRepository wsMaterialBatchRepository, SaleOrderRepository saleOrderRepository) {
        this.workSheetRepository = workSheetRepository;
        this.wsStepRepository = wsStepRepository;
        this.wsMaterialRepository = wsMaterialRepository;
        this.pedigreeConfigRepository = pedigreeConfigRepository;
        this.pedigreeRepository = pedigreeRepository;
        this.batchWorkDetailRepository = batchWorkDetailRepository;
        this.subWorkSheetRepository = subWorkSheetRepository;
        this.stepRepository = stepRepository;
        this.workFlowRepository = workFlowRepository;
        this.commonService = commonService;
        this.customPedigreeStepRepository = customPedigreeStepRepository;
        this.wsMaterialBatchRepository = wsMaterialBatchRepository;
        this.saleOrderRepository = saleOrderRepository;
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<WorkSheet> find(Specification<WorkSheet> spec, Pageable pageable) {
        return workSheetRepository.findAll(spec, pageable, new NamedEntityGraph(WORK_SHEET_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public List<WorkSheet> find(Specification<WorkSheet> spec) {
        return workSheetRepository.findAll(spec, new NamedEntityGraph(WORK_SHEET_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<WorkSheet> findAll(Pageable pageable) {
        return workSheetRepository.findAll(pageable, new NamedEntityGraph(WORK_SHEET_ENTITY_GRAPH));
    }


    /**
     * 保存工单定制工序
     *
     * @param workSheet    新增工单参数
     * @param workFlow     工艺流程框图数据
     * @param pedigree     产品谱系数据
     * @param workSheetDto 新增工单请求参数
     **/
    public void saveWsStepInfo(WorkSheet workSheet, WorkFlow workFlow, Pedigree pedigree, WorkSheetDTO workSheetDto) {
        //获取系统配置的投产粒度(子工单或者工单),只有投产粒度为子工单时才会进行分单
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
        this.saveWsSteps(subWsProductionMode, workSheet, workFlow, pedigree, workSheetDto.getStepDtoList());
        if (subWsProductionMode) {
            //自动划分子工单(放在WorkSheetService里面会存在事务未完成导致生成子工单号失败)
            PedigreeConfig pedigreeConfig = commonService.findPedigreeConfig(workSheet.getPedigree());
            subWorkSheetService.autoGenerateSubWorkSheet(workSheet.getId(), Optional.ofNullable(workSheet.getWorkLine()).map(WorkLine::getId).orElse(null), workSheet.getPlanStartDate(), workSheet.getPlanEndDate(),
                        null != pedigreeConfig && pedigreeConfig.getIsEnable() && pedigreeConfig.getSplitNumber() > Constants.INT_ZERO ? pedigreeConfig.getSplitNumber() : workSheet.getNumber(), workSheetDto.getIsAutoGenerateSubWs());
        }else {
            //初始化更新生产在制看板数据
            workSheetStepStatisticsServices[0].initWorkSheetStepStatisticsInfo(workSheet,null,subWsProductionMode);
        }
    }

    /**
     * 保存产品谱系最新下单流程
     *
     * @param pedigreeConfig 产品谱系配置
     * @param wsStepList     定制工序列表
     * @param workFlow       流程框图
     */
    public void savePedigreeSteps(PedigreeConfig pedigreeConfig, List<WsStep> wsStepList, WorkFlow workFlow) {
        if (pedigreeConfig != null && pedigreeConfig.getWorkFlowType() == Constants.INT_TWO) {
            List<CustomPedigreeStep> customPedigreeStepList = new ArrayList<>();
            if (ValidateUtils.isValid(wsStepList)) {
                customPedigreeStepRepository.deleteByPedigreeId(pedigreeConfig.getPedigree().getId());
                wsStepList.forEach(wsStep -> {
                    CustomPedigreeStep customPedigreeStep = new CustomPedigreeStep();
                    customPedigreeStep.setStep(wsStep.getStep())
                            .setPedigree(pedigreeConfig.getPedigree())
                            .setAfterStepId(wsStep.getAfterStepId())
                            .setPreStepId(wsStep.getPreStepId())
                            .setCategory(wsStep.getCategory())
                            .setControlMode(wsStep.getControlMode())
                            .setIsBindContainer(wsStep.getIsBindContainer())
                            .setIsControlMaterial(wsStep.getIsControlMaterial())
                            .setRequestMode(wsStep.getRequestMode())
                            .setWorkFlow(workFlow);
                    customPedigreeStepList.add(customPedigreeStep);
                });
                customPedigreeStepRepository.saveAll(customPedigreeStepList);
                pedigreeConfig.setWorkFlowType(Constants.INT_ONE);
                pedigreeConfigRepository.save(pedigreeConfig);
            }
        }
    }


    /**
     * 保存工单定制工序
     *
     * @param workSheet   总工单
     * @param stepDtoList 定制工序列表
     * <AUTHOR>
     * @date 2021-01-29
     **/
    @CachePut(cacheNames = {"wsStepQueryCache"},key = "#workSheet.id")
    public List<WsStep> saveWsSteps(Boolean subWsProductMode, WorkSheet workSheet, WorkFlow workFlow, Pedigree pedigree, List<StepDTO> stepDtoList) {
        List<WsStep> wsSteps = Lists.newArrayList();
        //保存生产定制工序信息
        if (ValidateUtils.isValid(stepDtoList)) {
            stepDtoList.forEach(stepDto -> {
                Step step = stepRepository.getReferenceById(stepDto.getId());
                StepDTO stepConfigDto = commonService.findPedigreeStepConfig(workSheet.getClientId(),pedigree, workFlow, step);
                if (null == stepConfigDto) {
                    throw new ResponseException("error.stepConfigNotExist", "不存在工序(" + step.getCode() + ")配置");
                }
                if (stepConfigDto.getInputRate() == Constants.DOUBLE_ZERRO) {
                    throw new ResponseException("error.stepConfigInputRateZorro", "工序(" + step.getCode() + ")配置的投产比例为0");
                }
                WsStep wsStep = new WsStep();
                wsStep.setStep(net.airuima.rbase.util.MapperUtils.map(stepDto, Step.class))
                        .setAfterStepId(stepDto.getAfterStepId())
                        .setPreStepId(stepDto.getPreStepId())
                        .setCategory(stepConfigDto.getCategory())
                        .setControlMode(stepConfigDto.getControlMode())
                        .setIsBindContainer(stepConfigDto.getIsBindContainer())
                        .setIsControlMaterial(stepConfigDto.getIsControlMaterial())
                        .setRequestMode(stepConfigDto.getRequestMode())
                        .setInputRate(stepConfigDto.getInputRate())
                        .setWorkFlow(workSheet.getWorkFlow())
                        .setWorkSheet(workSheet);
                wsSteps.add(wsStep);
            });
        }
        //工单投产粒度时需要更新工单的工序个数
        if (!subWsProductMode && !CollectionUtils.isEmpty(wsSteps)) {
            workSheet.setStepNumber(wsSteps.size());
            workSheetRepository.save(workSheet);
        }
        return wsStepRepository.saveAll(wsSteps);
    }


    /**
     * 验证工单是否合法
     *
     * @param workSheetId  工单id
     * @param serialNumber 工单号
     * @param inputNumber  投产数
     * @return BaseDTO
     */
    public BaseDTO validateWorkSheet(Long workSheetId, String serialNumber, int inputNumber) {
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findBySerialNumberAndDeleted(serialNumber, net.airuima.constant.Constants.LONG_ZERO);
        boolean isIllegalSerialNumber = (null == workSheetId && workSheetOptional.isPresent())
                || (null != workSheetId && workSheetOptional.isPresent() && !workSheetOptional.get().getId().equals(workSheetId));
        //验证总工单号是否合法
        if (isIllegalSerialNumber) {
            return new BaseDTO(net.airuima.constant.Constants.KO, "工单号已存在");
        }
        if((workSheetOptional.isPresent() && workSheetOptional.get().getStatus() == ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName())
                || (workSheetOptional.isPresent() && workSheetOptional.get().getStatus() == ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName())){
            return new BaseDTO(net.airuima.constant.Constants.KO, "工单已结单");
        }
        //修改总工单时验证子工单总数是否超过修改后的总工单投产数
        Long number = subWorkSheetRepository.statsNumberByWorkSheetIdAndDeleted(workSheetId, net.airuima.constant.Constants.LONG_ZERO);
        if (null != number && number.intValue() > inputNumber) {
            return new BaseDTO(net.airuima.constant.Constants.KO, "修改的工单投产数小于已分单子工单数投产数");
        }
        if(workSheetOptional.isPresent() && null != number && number.intValue() == inputNumber){
            WorkSheet workSheet = workSheetOptional.get();
            workSheet.setGenerateSubWsStatus(net.airuima.constant.Constants.INT_TWO);
            workSheetRepository.save(workSheet);
        }
        if(workSheetOptional.isPresent() && null != number && number.intValue() < inputNumber){
            WorkSheet workSheet = workSheetOptional.get();
            workSheet.setGenerateSubWsStatus(net.airuima.constant.Constants.INT_ONE);
            workSheetRepository.save(workSheet);
        }
        return new BaseDTO(net.airuima.constant.Constants.OK);
    }

    /**
     * 验证工序是否都存在配置
     *
     * @param workSheetDto
     * @return HttpHeaders
     * <AUTHOR>
     * @date 2021-04-29
     **/
    @Transactional(readOnly = true)
    public void validateStepInfo(WorkSheetDTO workSheetDto) {
        if (!ValidateUtils.isValid(workSheetDto.getStepDtoList())) {
            throw new ResponseException("error.stepNotExist", "工序不可为空");
        }
        Pedigree pedigree = pedigreeRepository.getReferenceById(workSheetDto.getPedigree().getId());
        WorkFlow workFlow = workFlowRepository.getReferenceById(workSheetDto.getWorkFlow().getId());
        List<String> notExistConfigList = new ArrayList<>();
        for (StepDTO stepDto : workSheetDto.getStepDtoList()) {
            Step step = stepRepository.getReferenceById(stepDto.getId());
            StepDTO stepConfigDto = commonService.findPedigreeStepConfig(workSheetDto.getClientId(), pedigree, workFlow, step);
            if (null == stepConfigDto) {
                notExistConfigList.add(step.getCode());
            }
        }
        if (!CollectionUtils.isEmpty(notExistConfigList)) {
            throw new ResponseException("error.stepConfigNotExist", "工序[" + Joiner.on(",").join(notExistConfigList) + "]配置不存在");
        }
    }

    /**
     * 修改工单状态
     *
     * @param id     工单ID
     * @param status 0:暂停;1:恢复
     * @return ResponseEntity<WorkSheet>
     * <AUTHOR>
     * @date 2021-07-22
     **/
    public WorkSheet updateStatus(Long id, Integer status) {
        WorkSheet workSheet = workSheetRepository.getReferenceById(id);
        return this.updateWorkSheetStatus(workSheet, status);
    }

    /**
     * 通过工单号修改工单状态,
     * 增加乐观锁重试机制，只能加在起始函数上，例如：不能加在updateWorkSheetStatus函数上，否则失效
     *
     * @param serialNumber 工单号
     * @param status       0:暂停;1:恢复
     * @return WorkSheet
     * <AUTHOR>
     * @date 2023-03-13
     **/
//    @Retryable(value = ObjectOptimisticLockingFailureException.class,maxAttempts = 4,backoff = @Backoff(delay = 2000L,multiplier = 1.5))
    public WorkSheet updateStatusBySerialNumber(String serialNumber, Integer status) {
        WorkSheet workSheet = workSheetRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO).orElse(new WorkSheet());
        return this.updateWorkSheetStatus(workSheet, status);
    }

    /**
     * 修改工单状态
     *
     * @param workSheet 工单
     * @param status    0:暂停;1:恢复
     * @return ResponseEntity<WorkSheet>
     * <AUTHOR>
     * @date 2021-07-22
     **/
    public WorkSheet updateWorkSheetStatus(WorkSheet workSheet, Integer status) {
        // 只有投产中、已暂停才能暂停、恢复
        if (workSheet.getStatus() != ConstantsEnum.WORK_SHEET_STATIC_DEVOTE.getCategoryName() && workSheet.getStatus() != ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName() && workSheet.getStatus() != ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName()) {
            throw new ResponseException("error.workSheetStatusIsNotCreatedOrPause", "工单状态不是投产中、已暂停");
        }
        workSheet.setStatus(status);
        workSheetRepository.saveAndFlush(workSheet);
        return workSheet;
    }

    /**
     * 通过总工单ID获取工单及投料单、定制工序信息
     *
     * @param id 总工单ID
     * @return WorkSheetResDTO
     * <AUTHOR>
     * @date 2021-03-31
     **/
    @Transactional(readOnly = true)
    public WorkSheetFullGetDTO findBaseInfoByWorkSheetId(Long id) {
        AtomicReference<WorkSheetFullGetDTO> workSheetDtoAtomicReference = new AtomicReference<>();
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findByIdAndDeleted(id, Constants.LONG_ZERO);
        workSheetOptional.ifPresent(workSheet -> {
            //获取总工单基础信息
            workSheetDtoAtomicReference.set(new WorkSheetFullGetDTO(workSheet));
            //获取总工单定制工序信息
            List<WsStep> wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            if (ValidateUtils.isValid(wsStepList)) {
                List<WorkSheetFullGetDTO.StepBaseDTO> stepDtoList = Lists.newArrayList();
                if (ValidateUtils.isValid(wsStepList)) {
                    wsStepList.forEach(wsStep -> {
                        WorkSheetFullGetDTO.StepBaseDTO stepDTO = new WorkSheetFullGetDTO.StepBaseDTO(wsStep);
                        stepDtoList.add(stepDTO);
                    });
                }
                workSheetDtoAtomicReference.get().setStepDtoList(stepDtoList);
            }
            //获取总工单投料单信息
            List<WsMaterial> wsMaterialList = wsMaterialRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            List<WorkSheetFullGetDTO.MaterialBaseDTO> wsMaterialDTOList = Lists.newArrayList();

            Optional.ofNullable(wsMaterialList).ifPresent(wsMaterials -> {
                List<WorkSheetFullGetDTO.MaterialBaseDTO> materialDtoList = Lists.newArrayList();
                List<WsMaterial> originWsMaterialList = wsMaterials.stream().filter(wsMaterial -> wsMaterial.getMaterialId().equals(wsMaterial.getOriginMaterialId())).collect(Collectors.toList());
                originWsMaterialList.forEach(wsMaterial -> {
                    WorkSheetFullGetDTO.MaterialBaseDTO materialDto = net.airuima.rbase.util.MapperUtils.map(wsMaterial.getMaterialDto(), WorkSheetFullGetDTO.MaterialBaseDTO.class);
                    materialDto.setWsMaterialId(wsMaterial.getId());
                    materialDto.setBackFlush(wsMaterial.getBackFlush());
                    materialDto.setNumber(wsMaterial.getNumber());
                    materialDtoList.add(materialDto);
                });
                materialDtoList.forEach(materialDTO -> {
                    List<WsMaterial> replaceWsMaterialList = wsMaterialList.stream().filter(wsMaterial -> wsMaterial.getOriginMaterialId().equals(materialDTO.getId()) && !wsMaterial.getMaterialId().equals(materialDTO.getId())).collect(Collectors.toList());
                    if (ValidateUtils.isValid(replaceWsMaterialList)) {
                        List<WorkSheetFullGetDTO.MaterialBaseDTO> replaceMaterialDtoList = Lists.newArrayList();
                        replaceWsMaterialList.forEach(replaceWsMaterial -> {
                            WorkSheetFullGetDTO.MaterialBaseDTO replaceMaterialInfo = net.airuima.rbase.util.MapperUtils.map(replaceWsMaterial.getMaterialDto(), WorkSheetFullGetDTO.MaterialBaseDTO.class);
                            replaceMaterialInfo.setNumber(replaceWsMaterial.getNumber());
                            replaceMaterialInfo.setWsMaterialId(replaceWsMaterial.getId());
                            replaceMaterialDtoList.add(replaceMaterialInfo);
                        });
                        materialDTO.setReplaceMaterialInfoList(replaceMaterialDtoList);
                    }
                });
                materialDtoList.forEach(materialInfo -> {
                    wsMaterialDTOList.add(materialInfo);
                });
                workSheetDtoAtomicReference.get().setWsMaterialDTOList(wsMaterialDTOList);
            });
        });
        return workSheetDtoAtomicReference.get();
    }

    /**
     * 通过总工单号模糊查询总工单列表
     *
     * @param serialNumber 总工单号
     * @param size         列数
     * @return List<WorkSheet>
     * <AUTHOR>
     * @date 2021-04-25
     **/
    @Transactional(readOnly = true)
    public List<WorkSheet> findPageBySerialNumber(String serialNumber, Integer size) {
        if (StringUtils.isEmpty(serialNumber)) {
            return Lists.newArrayList();
        }
        return Optional.ofNullable(workSheetRepository.findPageBySerialNumber(serialNumber, PageRequest.of(Constants.INT_ZERO, size))).map(Slice::getContent).orElse(null);
    }

    /**
     * 保存同步erp工单投料单信息
     *
     * @param workSheet         工单
     * @param wsMaterialDtoList 投料单信息
     * @return void
     * <AUTHOR>
     * @date 2021/8/3
     */
    public void saveErpWsMaterial(WorkSheet workSheet, List<WorkSheetMaterialDTO.WsMaterialAdaptDTO> wsMaterialDtoList) {
        List<WsMaterial> wsMaterials = Lists.newArrayList();
        //投料数量直接沿用工单的投产数，而不是传递过来的（预计用量erp），因为存在非必填情况;
        //直接用投料单，就不会根据 用料比例*工单投产数（可能存在bom中没有的物料，在投料单中） 来获取物料对应的投产数量
        if (ValidateUtils.isValid(wsMaterialDtoList)) {
            wsMaterialDtoList.forEach(digiwinWSDto -> {
                WsMaterial wsMaterial = new WsMaterial();
                MaterialDTO originMaterial = rbaseMaterialProxy.findByCodeAndDeleted(digiwinWSDto.getOriginMaterialCode(),Constants.LONG_ZERO).orElse(null);
                MaterialDTO material = null;
                if (StringUtils.isNotBlank(digiwinWSDto.getMaterialCode())) {
                    material = rbaseMaterialProxy.findByCodeAndDeleted(digiwinWSDto.getMaterialCode(),Constants.LONG_ZERO).orElse(null);
                }
                wsMaterial.setMaterialId(material != null ? material.getId() : originMaterial.getId())
                        .setNumber(workSheet.getNumber())
                        .setOriginMaterialId(originMaterial.getId())
                        .setBackFlush(Optional.ofNullable(digiwinWSDto.getBackFlush()).orElse(Boolean.FALSE))
                        .setWorkSheet(workSheet);
                wsMaterials.add(wsMaterial);
            });
        }
        wsMaterialRepository.saveAll(wsMaterials);
    }

    /**
     * SAP退料结单更新总工单以及子工单信息
     * 各个子工单的合格数以最新完成的工序的合格数为准
     *
     * @param workSheet 总工单
     * @return void
     * <AUTHOR>
     * @date 2021-06-03
     **/
    public void finishWorkSheetWithReturnMaterial(WorkSheet workSheet) {
        List<SubWorkSheet> subWorkSheetList = subWorkSheetRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(subWorkSheetList)) {
            subWorkSheetList.forEach(subWorkSheet -> {
                Optional<BatchWorkDetail> batchWorkDetailOptional = batchWorkDetailRepository.findTop1BySubWorkSheetIdAndDeletedOrderByIdDesc(subWorkSheet.getId(), Constants.LONG_ZERO);
                batchWorkDetailOptional.ifPresent(batchWorkDetail -> {
                    subWorkSheet.setQualifiedNumber(batchWorkDetail.getQualifiedNumber()).setStatus(Constants.ABNORMAL_CLOSED).setActualEndDate(LocalDateTime.now()).setUnqualifiedNumber(subWorkSheet.getNumber() - batchWorkDetail.getQualifiedNumber());
                });
            });
            int qualifiedNumber = subWorkSheetList.stream().mapToInt(SubWorkSheet::getQualifiedNumber).sum();
            workSheet.setQualifiedNumber(qualifiedNumber).setUnqualifiedNumber(workSheet.getNumber() - qualifiedNumber).setStatus(Constants.ABNORMAL_CLOSED).setActualEndDate(LocalDateTime.now());
        }
    }

    /**
     * 通过工单编码获取 工单信息
     *
     * @param serialNumber 工单号
     * @return WorkSheetMaterialDTO
     * <AUTHOR>
     * @date 2021/8/9
     */
    public WorkSheetMaterialDTO getWorksheetMaterial(String serialNumber) {
        WorkSheetMaterialDTO workSheetMaterialDto = new WorkSheetMaterialDTO();

        WorkSheet workSheet = workSheetRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO).orElse(null);
        if (workSheet == null) {
            return null;
        }
        OrganizationDTO organizationDTO = rbaseOrganizationProxy.findByIdAndDeleted(workSheet.getOrganizationId(),Constants.LONG_ZERO);
        workSheetMaterialDto.setId(workSheet.getId())
                .setSerialNumber(workSheet.getSerialNumber())
                .setCategory(workSheet.getCategory())
                .setOrganizationCode(Objects.nonNull(organizationDTO) && Objects.nonNull(organizationDTO.getCode()) ? organizationDTO.getCode() : null)
                .setMaterialCode(workSheet.getBomInfoDto().getMaterial().getCode())
                .setBomCode(workSheet.getBomInfoDto().getCode())
                .setNumber(workSheet.getNumber())
                .setPlanStartDate(workSheet.getPlanStartDate())
                .setPlanEndDate(workSheet.getPlanEndDate());

        List<WsMaterial> wsMaterialList = wsMaterialRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        List<WorkSheetMaterialDTO.WsMaterialAdaptDTO> wsMaterialAdaptDtoList = Lists.newArrayList();

        wsMaterialList.forEach(wsMaterial -> {
            WorkSheetMaterialDTO.WsMaterialAdaptDTO wsMaterialAdaptDto = new WorkSheetMaterialDTO.WsMaterialAdaptDTO();
            wsMaterialAdaptDto.setNumber(wsMaterial.getNumber())
                    .setOriginMaterialCode(wsMaterial.getOriginMaterialDto().getCode())
                    .setWorkSheetId(wsMaterial.getWorkSheet().getId())
                    .setMaterialCode(wsMaterial.getMaterialDto().getCode());
            wsMaterialAdaptDtoList.add(wsMaterialAdaptDto);
        });
        return workSheetMaterialDto.setWsMaterialDtoList(wsMaterialAdaptDtoList);
    }

    /**
     * 修改总工单备注
     *
     * @param id   工单id
     * @param note 备注
     * @return BaseDTO
     * <AUTHOR>
     * @date 2022/1/18
     */
    public BaseDTO updateWsNote(Long id, String note) {
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findByIdAndDeleted(id, Constants.LONG_ZERO);

        if (!workSheetOptional.isPresent()) {
            return new BaseDTO(Constants.KO, "IsNotWorkSheet");
        }
        workSheetOptional.get().setNote(note);
        workSheetRepository.save(workSheetOptional.get());
        return new BaseDTO(Constants.OK);
    }

    /**
     * 删除总工单
     *
     * @param id 总工单id
     */
    public void deleteWorkSheet(Long id) {
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findByIdAndDeleted(id, Constants.LONG_ZERO);
        if (!workSheetOptional.isPresent()) {
            throw new ResponseException("error.workSheetIsNull", "工单不存在");
        }
        WorkSheet workSheet = workSheetOptional.get();
        //对于已经开工(暂停)或者完成的工单，无法删除
        if (workSheet.getStatus() > Constants.CREATED) {
            //工单已经开工(暂停)或者完成
            throw new ResponseException("error.workSheetIsStartOrFinish", "工单已开工");
        }
        //验证工单是否已经领料，领料则无法删除
        Long number = wsMaterialBatchRepository.countByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        if (number != null && number > 0) {
            //工单已经领料
            throw new ResponseException("error.workSheetHasMaterial", "工单已领料");
        }
        Long wsCheckMaterialNumber = wsCheckMaterialRepository.countByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        if (wsCheckMaterialNumber != null && wsCheckMaterialNumber > 0) {
            //工单已经存在核料单
            throw new ResponseException("error.wsCheckMaterialIsExist", "工单已经存在核料单，请先删除核料单");
        }
        //删除工单投料单
        List<WsMaterial> wsMaterials = wsMaterialRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(wsMaterials)) {
            wsMaterialRepository.deleteAll(wsMaterials);
        }
        //删除工单工艺快照
        wsStepRepository.deleteByWorkSheetId(workSheet.getId());
        //删除总工单
        workSheet.setDeleted(workSheet.getId());
        workSheetRepository.save(workSheet);
        //删除所有子工单
        List<SubWorkSheet> subWorkSheetList = subWorkSheetRepository
                .findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(subWorkSheetList)) {
            subWorkSheetRepository.deleteAll(subWorkSheetList);
        }
    }

    /**
     * BOM审批
     *
     * @param id                      工单ID
     * @param flowableTaskCompleteDTO 工作流任务DTO
     * @return : void
     * <AUTHOR>
     * @date 2022/9/26
     **/
    public void bomApproval(Long id, FlowableTaskCompleteDTO flowableTaskCompleteDTO) {
        //1. 后端通过“产品谱系配置管理”获取“是否需要重新定制流程框图”isCustomWorkFlow标识
        WorkSheet workSheet = workSheetRepository.findByIdAndDeleted(id, Constants.LONG_ZERO).orElseThrow(() -> new ResponseException(WORK_SHEET_NOT_EXIST, WORK_SHEET_NOT_EXIST_MSG));
        PedigreeConfig pedigreeConfig = pedigreeConfigRepository.findByPedigreeIdAndDeleted(workSheet.getPedigree().getId(), Constants.LONG_ZERO).orElse(null);
        if (null == pedigreeConfig || pedigreeConfig.getIsCustomWorkFlow()) {
            // 1.1 同意且需要定制框图
            customProcess(workSheet.getProcessInstanceId(), Boolean.TRUE, flowableTaskCompleteDTO);
        } else {
            // 1.2 同意且采用默认型号流程
            // 通过工单的产品谱系查询“产品谱系最新工艺路线快照”中是否存在历史记录
            List<CustomPedigreeStep> customPedigreeStepList = customPedigreeStepRepository.findByPedigreeIdAndDeleted(pedigreeConfig.getPedigree().getId(), Constants.LONG_ZERO);
            if (CollectionUtils.isEmpty(customPedigreeStepList)) {
                // 1.2.1 不存在历史记录，则isCustomWorkFlow改为true, Feign调用 Flowable“任务完成处理接口”，进入定制工艺路线
                customProcess(workSheet.getProcessInstanceId(), Constants.TRUE, flowableTaskCompleteDTO);
            } else {
                // 1.2.2 存在历史记录
                // 将“产品谱系最新工艺路线快照”中的工序信息更新至“工单快照”中
                wsStepRepository.deleteByWorkSheetId(workSheet.getId());
                List<WsStep> wsStepList = customPedigreeStepList.stream().map(cps -> {
                    WsStep wsStep = new WsStep();
                    BeanUtils.copyProperties(cps, wsStep);
                    wsStep.setWorkSheet(workSheet).setWorkFlow(workSheet.getWorkFlow());
                    return wsStep;
                }).collect(Collectors.toList());
                wsStepRepository.saveAll(wsStepList);
                // Feign调用 Flowable“任务完成处理接口”，结束流程
                customProcess(workSheet.getProcessInstanceId(), Boolean.FALSE, flowableTaskCompleteDTO);
                // 后端将WorkSheet工单表status状态改为0已下单（RabbitMQ接收消息并处理）
            }
        }
    }

    /**
     * 进入定制工艺路线流程
     *
     * @param processInstanceId       流程实例ID
     * @param isCustomWorkFlow        是否需要重新定制流程框图(0:否;1:是)
     * @param flowableTaskCompleteDTO 工作流任务DTO
     * @return : void
     * <AUTHOR>
     * @date 2022/9/26
     **/
    public void customProcess(String processInstanceId, Boolean isCustomWorkFlow, FlowableTaskCompleteDTO flowableTaskCompleteDTO) {
        FlowableTaskCompleteDTO flowableTaskCompleteDto = new FlowableTaskCompleteDTO().setTaskId(flowableTaskCompleteDTO.getTaskId()).setComment(flowableTaskCompleteDTO.getComment())
                .setProcessInstanceId(processInstanceId).setLoginName(flowableTaskCompleteDTO.getLoginName());
        Map<String, Object> parameter = new HashMap<String, Object>();
        parameter.put(FLOWABLE_IS_APPROVED, flowableTaskCompleteDTO.getParameter().get(FLOWABLE_IS_APPROVED));
        parameter.put(FLOWABLE_IS_REDEFINE_PROCESS, isCustomWorkFlow);
        flowableTaskCompleteDto.setParameter(parameter);
        if (ValidateUtils.isValid(flowableTaskCompleteDTO.getAttachmentList())) {
            flowableTaskCompleteDto.setAttachmentList(net.airuima.rbase.util.MapperUtils.mapAll(flowableTaskCompleteDTO.getAttachmentList(), FlowableTaskCompleteDTO.AttachmentInfo.class));
        }
        // Feign调用 Flowable“任务完成处理接口”，进入定制工艺路线
        ResponseEntity<FlowableResultDTO> taskResponseEntity = processTaskFeign.completeTask(flowableTaskCompleteDto);
        FlowableResultDTO flowableResultDto = taskResponseEntity.getBody();
        if (org.springframework.util.ObjectUtils.isEmpty(flowableResultDto) || flowableResultDto.getStatus().equals(Constants.KO)) {
            throw new ResponseException("error.bomApprovalFailed", org.springframework.util.ObjectUtils.isEmpty(flowableResultDto) ? "调用工作流失败" : flowableResultDto.getMessage());
        }
    }


    /**
     * 工单下单流程处理类
     *
     * @param processRunTimeEventDto 流程节点完成返回信息DTO
     * @return : void
     * <AUTHOR>
     * @date 2022/9/27
     **/
    public void flowableSequenceListener(ProcessRunTimeEventDTO processRunTimeEventDto) {
        if (Constants.FLOWABLE_WORKSHEET_APS_CANCEL_KEY.equals(processRunTimeEventDto.getTaskKey())
                || Constants.FLOWABLE_WORKSHEET_PROCESS_CANCEL_KEY.equals(processRunTimeEventDto.getTaskKey())) {
            //放弃更换BOM 或 放弃定制工艺流程
            workSheetRepository.updateStatusBySerialNumber(processRunTimeEventDto.getBusinessKey(), Constants.CANCEL);

        } else if (Constants.FLOWABLE_WORKSHEET_PROCESS_APPROVE_KEY.equals(processRunTimeEventDto.getTaskKey())
                || Constants.FLOWABLE_WORKSHEET_BIND_DEFAULT_PROCESS_KEY.equals(processRunTimeEventDto.getTaskKey())) {
            //同意工艺流程审批 或 同意且采用默认型号流程时，将WorkSheet工单表status状态改为0已下单
            workSheetRepository.updateStatusBySerialNumber(processRunTimeEventDto.getBusinessKey(), Constants.INT_ZERO);
        }
    }

    /**
     * 定制工艺路线
     *
     * @param workSheetDTO 工单DTO
     * @return : org.springframework.http.ResponseEntity<java.lang.Void>
     * <AUTHOR>
     * @date 2022/9/28
     **/
    public void remakeProcess(WorkSheetDTO workSheetDTO) {
        //验证
        validateRemakeProcess(workSheetDTO);
        //1. 重新保存某工单下所有“生产工单工艺路线快照”中的数据
        WorkSheet workSheet = workSheetRepository.findByIdAndDeleted(workSheetDTO.getId(), Constants.LONG_ZERO).orElseThrow(() -> new ResponseException(WORK_SHEET_NOT_EXIST, WORK_SHEET_NOT_EXIST_MSG));
        List<StepDTO> stepDTOList = workSheetDTO.getStepDtoList();
        List<Long> stepIdList = stepDTOList.stream().map(i -> i.getId()).collect(Collectors.toList());
        List<Step> stepList = stepRepository.findByIdInAndDeleted(stepIdList, Constants.LONG_ZERO);
        if (stepList.size() < stepIdList.size()) {
            throw new ResponseException("error.StepNotExist", "工序不存在");
        }
        Map<Long, Step> stepMap = stepList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        // 1.1 删除“生产工单工艺路线快照”旧数据
        wsStepRepository.deleteByWorkSheetId(workSheet.getId());
        // 1.2 组装并保存“生产工单工艺路线快照”新数据
        List<WsStep> wsStepList = stepDTOList.stream().map(i -> {
            Step step = stepMap.get(i.getId());
            //获取前置节点集合
            String beforeIdStr = stepDTOList.stream().filter(j -> StringUtils.isNotBlank(j.getAfterStepId()) && j.getAfterStepId().equals(String.valueOf(i.getId()))).map(j -> String.valueOf(j.getId())).collect(Collectors.joining(Constants.STR_COMMA));
            //根据产品谱系与工序获取工序管控配置
            StepDTO stepConfigDto = commonService.findPedigreeStepConfig(workSheet.getClientId(),workSheet.getPedigree(), workSheet.getWorkFlow(), step);
            if (org.springframework.util.ObjectUtils.isEmpty(stepConfigDto)) {
                throw new ResponseException("error.StepConfigNotExist", "工序配置未找到");
            }
            return new WsStep().setWorkSheet(workSheet).setPreStepId(beforeIdStr).setAfterStepId(i.getAfterStepId()).setStep(step)
                    .setCategory(stepConfigDto.getCategory()).setRequestMode(stepConfigDto.getRequestMode()).setControlMode(stepConfigDto.getControlMode())
                    .setIsControlMaterial(stepConfigDto.getIsControlMaterial()).setIsBindContainer(stepConfigDto.getIsBindContainer()).setInputRate(stepConfigDto.getInputRate()).setWorkFlow(workSheet.getWorkFlow());
        }).collect(Collectors.toList());
        wsStepRepository.saveAll(wsStepList);

        //2. Feign调用Flowable“任务完成处理接口”，进入工艺路线审批
        String starter = SecurityUtils.getCurrentUserLogin().orElse(StringUtils.EMPTY);
        FlowableTaskCompleteDTO flowableTaskCompleteDto = new FlowableTaskCompleteDTO().setTaskId(workSheetDTO.getTaskId()).setComment(workSheetDTO.getNote())
                .setProcessInstanceId(workSheet.getProcessInstanceId()).setLoginName(starter);
        Map<String, Object> parameter = new HashMap<String, Object>();
        parameter.put(FLOWABLE_REAPPLY, Boolean.TRUE);
        flowableTaskCompleteDto.setParameter(parameter);
        if (ValidateUtils.isValid(workSheetDTO.getAttachmentList())) {
            flowableTaskCompleteDto.setAttachmentList(net.airuima.rbase.util.MapperUtils.mapAll(workSheetDTO.getAttachmentList(), FlowableTaskCompleteDTO.AttachmentInfo.class));
        }
        ResponseEntity<FlowableResultDTO> taskResponseEntity = processTaskFeign.completeTask(flowableTaskCompleteDto);
        FlowableResultDTO flowableResultDto = taskResponseEntity.getBody();
        if (org.springframework.util.ObjectUtils.isEmpty(flowableResultDto) || flowableResultDto.getStatus().equals(Constants.KO)) {
            throw new ResponseException("error.remakeProcessFailed", org.springframework.util.ObjectUtils.isEmpty(flowableResultDto) ? "调用审批流失败" : flowableResultDto.getMessage());
        }
    }

    /**
     * 定制工艺路线验证
     *
     * @param workSheetDTO
     * @return : void
     * <AUTHOR>
     * @date 2022/10/8
     **/
    public void validateRemakeProcess(WorkSheetDTO workSheetDTO) {
        //1. 工序集合中不可存在分叉
        List<StepDTO> stepDTOList = workSheetDTO.getStepDtoList().stream().filter(i -> i.getAfterStepId().contains(",")).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(stepDTOList)) {
            throw new ResponseException("error.StepBifurcateExist", "工序集合出现分叉");
        }
        //2. 工序集合结束节点只可为一个
        List<StepDTO> stepDTOEndList = workSheetDTO.getStepDtoList().stream().filter(i -> StringUtils.isBlank(i.getAfterStepId())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(stepDTOEndList) && stepDTOEndList.size() > 1) {
            throw new ResponseException("error.StepEndNodeMultiple", "工序集合结束节点存在多个");
        }
    }

    /**
     * 工艺路线审批
     *
     * @param id                      工单ID
     * @param flowableTaskCompleteDTO 工作流任务完成DTO
     * <AUTHOR>
     * @date 2022/9/28
     **/
    public void processApproval(Long id, FlowableTaskCompleteDTO flowableTaskCompleteDTO) {
        WorkSheet workSheet = workSheetRepository.findByIdAndDeleted(id, Constants.LONG_ZERO).orElseThrow(() -> new ResponseException(WORK_SHEET_NOT_EXIST, WORK_SHEET_NOT_EXIST_MSG));
        //1. 将当前工单下所有“生产工单工艺路线快照”数据存储到“产品谱系最新工艺路线快照”中
        //1.1 删除指定产品谱系的”产品谱系最新工艺路线快照“旧数据
        customPedigreeStepRepository.deleteByPedigreeId(workSheet.getPedigree().getId());
        //1.2 组装并保存”产品谱系最新工艺路线快照“新数据
        List<WsStep> wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        List<CustomPedigreeStep> customPedigreeStepList = wsStepList.stream().map(i -> {
            return new CustomPedigreeStep().setPedigree(workSheet.getPedigree()).setStep(i.getStep()).setPreStepId(i.getPreStepId()).setAfterStepId(i.getAfterStepId())
                    .setWorkFlow(workSheet.getWorkFlow()).setCategory(i.getCategory()).setRequestMode(i.getRequestMode()).setControlMode(i.getControlMode())
                    .setIsControlMaterial(i.getIsControlMaterial()).setIsBindContainer(i.getIsBindContainer()).setInputRate(i.getInputRate());
        }).collect(Collectors.toList());
        customPedigreeStepRepository.saveAll(customPedigreeStepList);

        //2. 异步处理，Feign调用Flowable“任务完成处理接口”，结束流程
        TransactionUtils.afterCommitAsyncExecute(Executors.newSingleThreadExecutor(), () -> {
            String starter = SecurityUtils.getCurrentUserLogin().orElse(StringUtils.EMPTY);
            FlowableTaskCompleteDTO flowableTaskCompleteDto = new FlowableTaskCompleteDTO().setTaskId(flowableTaskCompleteDTO.getTaskId()).setComment(flowableTaskCompleteDTO.getComment())
                    .setProcessInstanceId(workSheet.getProcessInstanceId()).setLoginName(starter);
            Map<String, Object> parameter = new HashMap<String, Object>();
            parameter.put(FLOWABLE_IS_APPROVED, Boolean.TRUE);
            flowableTaskCompleteDto.setParameter(parameter);
            if (ValidateUtils.isValid(flowableTaskCompleteDTO.getAttachmentList())) {
                flowableTaskCompleteDto.setAttachmentList(net.airuima.rbase.util.MapperUtils.mapAll(flowableTaskCompleteDTO.getAttachmentList(), FlowableTaskCompleteDTO.AttachmentInfo.class));
            }
            ResponseEntity<FlowableResultDTO> taskResponseEntity = processTaskFeign.completeTask(flowableTaskCompleteDto);
            FlowableResultDTO flowableResultDto = taskResponseEntity.getBody();
            if (org.springframework.util.ObjectUtils.isEmpty(flowableResultDto) || flowableResultDto.getStatus().equals(Constants.KO)) {
                throw new ResponseException("error.processApprovalFailed", org.springframework.util.ObjectUtils.isEmpty(flowableResultDto) ? "调用审批流失败" : flowableResultDto.getMessage());
            }
        });
    }

    /**
     * 修改工单状态为取消
     *
     * @param id 工单ID
     * <AUTHOR>
     * @date 2022-12-28
     **/
    public void cancel(Long id) {
        WorkSheet workSheet = workSheetRepository.findByIdAndDeleted(id, Constants.LONG_ZERO).orElseThrow(() -> new ResponseException(WORK_SHEET_NOT_EXIST, WORK_SHEET_NOT_EXIST_MSG));
        if (workSheet.getStatus() != ConstantsEnum.WORK_SHEET_STATIC_DEVOTE.getCategoryName()) {
            throw new ResponseException("error.workSheetStatusIsNotCreated", "工单状态不是已下单");
        }
        // 如果存在关联销售订单，则对应的投产数减少
        SaleOrder saleOrder = workSheet.getSaleOrder();
        if (saleOrder != null) {
            if (saleOrder.getProductionQuantity() - workSheet.getNumber() < 0) {
                throw new ResponseException("error.saleOrderProductionQuantityIsError", "订单投产数小于工单投产数");
            }
            saleOrder.setProductionQuantity(saleOrder.getProductionQuantity() - workSheet.getNumber());
            saleOrderRepository.save(saleOrder);
        }
        List<SubWorkSheet> subWorkSheetList = subWorkSheetRepository.findByWorkSheetIdAndStatusLessThanAndDeleted(workSheet.getId(), ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(subWorkSheetList)) {
            // 更新子工单为取消
            subWorkSheetList.forEach(subWorkSheet -> {
                subWorkSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_CANCEL.getCategoryName());
                subWorkSheetRepository.save(subWorkSheet);
            });
            //更新工单的子工单个数以及完成个数(包括正常、异常)
            subWorkSheetService.updateWorkSheetSubWsNumberInfo(workSheet);
        }
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
        //更新在制看板数据
        workSheetStepStatisticsServices[0].deleteWorkSheetStepStatisticsInfo(workSheet,subWorkSheetList,subWsProductionMode);
        workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_CANCEL.getCategoryName());
        workSheetRepository.save(workSheet);
    }

    /**
     * 通过工单ID获取子工单及返工单工单进度
     *
     * @param workSheetId 正常单ID
     * @return List<WsSubProgressDTO>
     * <AUTHOR>
     * @date 2022-12-29
     **/
    public List<WorkSheetProgressDTO> findProgressByWorkSheetIdAndDeleted(Long workSheetId) {
        WorkSheet workSheet = workSheetRepository.findByIdAndDeleted(workSheetId, Constants.LONG_ZERO).orElse(null);
        if (null == workSheet) {
            return Collections.emptyList();
        }
        List<WorkSheetProgressDTO> progressDtoList = new ArrayList<>();
        List<SubWorkSheet> subWorkSheetList = subWorkSheetRepository.findByWorkSheetIdAndDeleted(workSheetId, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(subWorkSheetList)) {
            subWorkSheetList.forEach(subWorkSheet -> {
                WorkSheetProgressDTO wsSubWsDto = net.airuima.rbase.util.MapperUtils.map(subWorkSheet, WorkSheetProgressDTO.class);
                wsSubWsDto.setCategory(workSheet.getCategory());
                if (subWorkSheet.getWorkLine() != null) {
                    wsSubWsDto.setWorkLineName(subWorkSheet.getWorkLine().getName());
                    wsSubWsDto.setWorkLineCode(subWorkSheet.getWorkLine().getCode());
                }
                wsSubWsDto.setFinish(subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName()
                        || subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName());
                wsSubWsDto.setFinishNumber(subWorkSheet.getQualifiedNumber() + subWorkSheet.getUnqualifiedNumber());
                progressDtoList.add(wsSubWsDto);
            });
        }
        List<WsRework> wsReworks = wsReworkRepository.findByOriginalWorkSheetIdInAndDeleted(Collections.singletonList(workSheetId), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(wsReworks)) {
            wsReworks.forEach(wsRework -> {
                WorkSheetProgressDTO wsSubWsDto = net.airuima.rbase.util.MapperUtils.map(wsRework.getReworkWorkSheet(), WorkSheetProgressDTO.class);
                if (wsRework.getReworkWorkSheet().getWorkLine() != null) {
                    wsSubWsDto.setWorkLineName(wsRework.getReworkWorkSheet().getWorkLine().getName());
                    wsSubWsDto.setWorkLineCode(wsRework.getReworkWorkSheet().getWorkLine().getCode());
                }
                wsSubWsDto.setFinish(wsRework.getReworkWorkSheet().getStatus() == ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName());
                wsSubWsDto.setFinishNumber(wsRework.getReworkWorkSheet().getQualifiedNumber() + wsRework.getReworkWorkSheet().getUnqualifiedNumber());
                progressDtoList.add(wsSubWsDto);
            });
        }
        //增加级联下级工单返回也作为父级工单进度详情内容
        List<WorkSheet> subordinateWorkSheetList = cascadeWorkSheetRepository.findSubordinateWorkSheetBySuperiorWorkSheetId(workSheetId,Constants.LONG_ZERO);
        if(!CollectionUtils.isEmpty(subordinateWorkSheetList)){
            subordinateWorkSheetList.forEach(subordinateWorkSheet->{
                WorkSheetProgressDTO wsSubWsDto = net.airuima.rbase.util.MapperUtils.map(subordinateWorkSheet, WorkSheetProgressDTO.class);
                wsSubWsDto.setCategory(subordinateWorkSheet.getCategory());
                if (subordinateWorkSheet.getWorkLine() != null) {
                    wsSubWsDto.setWorkLineName(subordinateWorkSheet.getWorkLine().getName());
                    wsSubWsDto.setWorkLineCode(subordinateWorkSheet.getWorkLine().getCode());
                }
                wsSubWsDto.setFinish(subordinateWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName()
                        || subordinateWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName());
                wsSubWsDto.setFinishNumber(subordinateWorkSheet.getQualifiedNumber() + subordinateWorkSheet.getUnqualifiedNumber());
                progressDtoList.add(wsSubWsDto);
            });
        }
        return progressDtoList;
    }

    /**
     * 获取元数据 是否有 逾期查询条件
     *
     * @param qcs
     * @return
     */
    public AtomicReference<Boolean> getDelay(List<QueryCondition> qcs) {
        AtomicReference<Boolean> delay = new AtomicReference<>(null);
        // 不管 delay 到底有没有值 都要删除 查询的元数据
        if (ValidateUtils.isValid(qcs)) {
            qcs.stream().filter(queryCondition -> queryCondition.getFieldName().equals(IS_OVERDUE)).findFirst().ifPresent(queryCondition -> {
                if (queryCondition.getFieldValue() != null && StringUtils.isNotBlank(String.valueOf(queryCondition.getFieldValue()))) {
                    delay.set(Boolean.parseBoolean(String.valueOf(queryCondition.getFieldValue())));
                }
                qcs.remove(queryCondition);
            });
        }
        return delay;
    }

    /**
     * 添加逾期查询条件
     *
     * @param delay
     * @return
     */
    public Specification<WorkSheet> addSpecification(AtomicReference<Boolean> delay) {
        return (root, criteriaQuery, cb) -> {
            // 添加逾期查询条件
            if (Boolean.TRUE.equals(delay.get())) {
                Expression<LocalDateTime> actualEndDate = root.get(ACTUAL_END_DATE);
                Expression<LocalDateTime> currentTime = cb.currentTimestamp().as(LocalDateTime.class);
                Expression<LocalDateTime> targetTime = cb.coalesce(actualEndDate, currentTime);
                return cb.lessThan(root.get(PLAN_END_DATE), targetTime);
            }
            // 添加 没有逾期 查询条件
            return cb.greaterThan(root.get(PLAN_END_DATE), root.get(ACTUAL_END_DATE));
        };
    }

    /**
     * 通过销售订单ID获取工单列表
     *
     * @param id 销售订单ID
     * @return 工单列表
     */
    public SaleOrderProcessDTO findBySaleOrderId(Long id) {
        SaleOrder saleOrder = saleOrderRepository.findByIdAndDeleted(id, Constants.LONG_ZERO)
                .orElseThrow(() -> new ResponseException("IdNull", "销售订单不存在"));

        SaleOrderProcessDTO saleOrderProcessDTO = new SaleOrderProcessDTO();
        List<WorkSheet> workSheets = workSheetRepository.findBySaleOrderIdAndDeleted(id, Constants.LONG_ZERO);
        if(CollectionUtils.isEmpty(workSheets)){
            return saleOrderProcessDTO;
        }
        //销售订单进度：根据成品工单及关联的半成品工单、权重进行汇总统计
        BigDecimal totalSaleOrderProcess = NumberUtils.divide(workSheets.stream().mapToInt(WorkSheet::getQualifiedNumber).sum(),saleOrder.getNumber(),Constants.INT_TWO);
        List<WorkSheetSimpleGetDTO> workSheetSimpleGetDTOList = workSheets.stream().map(WorkSheetSimpleGetDTO::new).toList();
        //获取可能的下级工单列表
        for (WorkSheetSimpleGetDTO workSheetSimpleGetDTO: workSheetSimpleGetDTOList) {
            //工单所占销售订单中的权重
            List<WorkSheet> subordinateWorkSheetList = cascadeWorkSheetRepository.findSubordinateWorkSheetBySuperiorWorkSheetId(workSheetSimpleGetDTO.getId(),Constants.LONG_ZERO);
            if(!CollectionUtils.isEmpty(subordinateWorkSheetList)){
                List<WorkSheetSimpleGetDTO> subordinateWorkSheetDTOList = Lists.newArrayList();
                for (WorkSheet subordinateWorkSheet : subordinateWorkSheetList) {
                    WorkSheetSimpleGetDTO workSheetSimpleGetDto = new WorkSheetSimpleGetDTO(subordinateWorkSheet);
                    //半成品工单在成品工单中的权重
                    BigDecimal subordinateWorkSheetProcess = NumberUtils.divide(subordinateWorkSheet.getQualifiedNumber(), subordinateWorkSheet.getNumber(), Constants.INT_TWO);
                    workSheetSimpleGetDto.setProgress(subordinateWorkSheetProcess);
                    subordinateWorkSheetDTOList.add(workSheetSimpleGetDto);
                }
                BigDecimal wsWeight = NumberUtils.divide(subordinateWorkSheetList.stream().mapToInt(WorkSheet::getQualifiedNumber).sum(),workSheetSimpleGetDTO.getNumber(),Constants.INT_TWO);
                //成品工单在销售订单的进度
                workSheetSimpleGetDTO.setProgress(wsWeight)
                        .setSubordinateWorkSheetDTOList(subordinateWorkSheetDTOList);
            }
        }
        saleOrderProcessDTO.setWorkSheetSimpleGetDTOList(workSheetSimpleGetDTOList).setProgress(totalSaleOrderProcess);
        return saleOrderProcessDTO;
    }


    /**
     * 工单信息导入
     *
     * @param file     导入文件
     * @param response 响应数据
     * @return BaseDTO
     */
    public BaseDTO importTableExcel(MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams importParams = new ImportParams();
        List<WorkSheetImportDTO> workSheetImportDtoList = ExcelImportUtil.importExcel(file.getInputStream(), WorkSheetImportDTO.class, importParams);

        if (!ValidateUtils.isValid(workSheetImportDtoList)) {
            return new BaseDTO(Constants.KO);
        }

        //去掉工单号重复数据
        workSheetImportDtoList = workSheetImportDtoList.stream().distinct().toList();

        //获取产品谱系
        List<Pedigree> pedigrees = pedigreeRepository.findByCodeInAndDeleted(workSheetImportDtoList.stream().map(WorkSheetImportDTO::getPedigreeCode).distinct().collect(Collectors.toList()), Constants.LONG_ZERO);

        //部门
        List<OrganizationDTO> organizationDtoList = rbaseOrganizationProxy.findByCodeInAndDeleted(workSheetImportDtoList.stream().map(WorkSheetImportDTO::getOrganizationCode).distinct().collect(Collectors.toList()),Constants.LONG_ZERO);

        //客户
        List<ClientDTO> clientDtoList = rbaseClientProxy.findByCodeIn(workSheetImportDtoList.stream().map(WorkSheetImportDTO::getClientCode).collect(Collectors.toList()));

        //工艺路线
        List<WorkFlow> workFlowList = workFlowRepository.findByCodeInAndDeleted(workSheetImportDtoList.stream().map(WorkSheetImportDTO::getWorkFlowCode).collect(Collectors.toList()), Constants.LONG_ZERO);


        //产线
        List<WorkLine> workLineList = workLineRepository.findByCodeInAndDeleted(workSheetImportDtoList.stream().map(WorkSheetImportDTO::getWorkLineCode).collect(Collectors.toList()), Constants.LONG_ZERO);

        if ((!ValidateUtils.isValid(pedigrees) || !ValidateUtils.isValid(organizationDtoList) || !ValidateUtils.isValid(workFlowList) || !ValidateUtils.isValid(workLineList)) ||
                !ValidateUtils.isValid(workSheetImportDtoList.stream().map(WorkSheetImportDTO::getSerialNumber).collect(Collectors.toList()))) {
            throw new ResponseException("error.RequiredDataError", "必填数据列为空");
        }

        Map<String, Pedigree> pedigreeMap = pedigrees.stream()
                .collect(Collectors.toMap(Pedigree::getCode, pedigree -> pedigree));

        Map<String, OrganizationDTO> organizationMap = organizationDtoList.stream()
                .collect(Collectors.toMap(OrganizationDTO::getCode, organizationDTO -> organizationDTO));

        Map<String, WorkFlow> workFlowMap = workFlowList.stream()
                .collect(Collectors.toMap(WorkFlow::getCode, workFlow -> workFlow));

        Map<String, WorkLine> workLineMap = workLineList.stream()
                .collect(Collectors.toMap(WorkLine::getCode, workLine -> workLine));

        Map<String, ClientDTO> clientMap = ValidateUtils.isValid(clientDtoList) ? clientDtoList.stream()
                .collect(Collectors.toMap(ClientDTO::getCode, clientDto -> clientDto)) : new HashMap<>();

        int numThreads = 4; // 设置线程数量，根据系统资源进行调整
        ExecutorService executorService = new ThreadPoolExecutor(
                numThreads, numThreads, 0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>()
        );
        List<WorkSheetImportDTO> errorWorkSheetImportDTOList = Collections.synchronizedList(new ArrayList<>());

        // 使用多线程并行导入工作表数据
        List<CompletableFuture<Void>> futures = workSheetImportDtoList.stream()
                .map(workSheetImportDto -> CompletableFuture.runAsync(() -> {
                    processWorkSheetImport(workSheetImportDto, errorWorkSheetImportDTOList, pedigreeMap, organizationMap, workFlowMap, workLineMap, clientMap);
                }, executorService))
                .collect(Collectors.toList());

        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        allOf.join(); // 等待所有任务完成
        executorService.shutdown();

        return exportImportWorkSheetData(errorWorkSheetImportDTOList, workSheetImportDtoList.size(), file, response);
    }

    private void processWorkSheetImport(WorkSheetImportDTO workSheetImportDto, List<WorkSheetImportDTO> errorWorkSheetImportDTOList,
                                        Map<String, Pedigree> pedigreeMap,
                                        Map<String, OrganizationDTO> organizationMap,
                                        Map<String, WorkFlow> workFlowMap,
                                        Map<String, WorkLine> workLineMap,
                                        Map<String, ClientDTO> clientMap) {
        WorkSheet workSheet = new WorkSheet();
        try {
            // 验证工单基础数据并添加基础
            BaseDTO baseDto = validWorkSheetExcelBaseData(workSheetImportDto, workSheet, pedigreeMap, organizationMap, workFlowMap, workLineMap, clientMap);
            if (baseDto.getStatus().equals(Constants.KO)) {
                errorWorkSheetImportDTOList.add(workSheetImportDto.setMessage(baseDto.getMessage()));
                return;
            }
            // 验证工单投产下交情况
            baseDto = validateWorkSheet(workSheet.getId(), workSheet.getSerialNumber(), workSheet.getNumber());
            if (baseDto.getStatus().equals(Constants.KO)) {
                errorWorkSheetImportDTOList.add(workSheetImportDto.setMessage(baseDto.getMessage()));
                return;
            }
            //修改总工单时验证工单是否已投产
            if (batchWorkDetailRepository.countBySubWorkSheetWorkSheetIdAndDeleted(workSheet.getId(), net.airuima.constant.Constants.LONG_ZERO) > net.airuima.constant.Constants.LONG_ZERO) {
                errorWorkSheetImportDTOList.add(workSheetImportDto.setMessage("工单已投产"));
                return;
            }
            // 验证工单工艺路线
            List<StepDTO> stepDtoList = validateWorkSheetExcelStepInfo(workSheet);
            // 保存工单信息
            WorkSheet finalWorkSheet = workSheetRepository.save(workSheet);
            List<BomDTO> bomDtoList = rbaseBomProxy.findByBomInfoId(finalWorkSheet.getBomInfoId());
            // 保存工单投料单
            BeanUtil.getHighestPrecedenceBean(IWsMaterialService.class).saveWsMaterial(finalWorkSheet, bomDtoList);
            // 保存工序快照，并分子工单
            saveExcelWsStepInfo(workSheet, workSheet.getWorkFlow(), workSheet.getPedigree(), stepDtoList);
        } catch (Exception e) {
            errorWorkSheetImportDTOList.add(workSheetImportDto.setMessage(e.getMessage()));
        }
    }


    /**
     * 验证工单基础数据并添加基础
     *
     * @param workSheetImportDto 导入工单数据
     * @param workSheet          工单
     * @param pedigreeMap        产品谱系
     * @param organizationMap    组织架构
     * @param workFlowMap        工艺路线
     * @param workLineMap        产线
     * @param clientMap          客户
     * @return BaseDTO
     */
    private BaseDTO validWorkSheetExcelBaseData(WorkSheetImportDTO workSheetImportDto, WorkSheet workSheet,
                                                Map<String, Pedigree> pedigreeMap,
                                                Map<String, OrganizationDTO> organizationMap,
                                                Map<String, WorkFlow> workFlowMap,
                                                Map<String, WorkLine> workLineMap,
                                                Map<String, ClientDTO> clientMap) {

        if (workSheetImportDto.getPriority() == null) {
            return new BaseDTO(Constants.KO, "优先级不能为空");
        }
        if (!ValidateUtils.isValid(workSheetImportDto.getOrganizationCode())) {
            return new BaseDTO(Constants.KO, "部门编码不能为空");
        }
        if (!ValidateUtils.isValid(workSheetImportDto.getSerialNumber())) {
            return new BaseDTO(Constants.KO, "工单号不能为空");
        }
        if (workSheetImportDto.getCategory() == null) {
            return new BaseDTO(Constants.KO, "工单类型不能为空");
        }
        if (workSheetImportDto.getNumber() == null) {
            return new BaseDTO(Constants.KO, "投产数不能为空");
        }
        if (workSheetImportDto.getPlanStartDate() == null) {
            return new BaseDTO(Constants.KO, "计划生产日期不能为空");
        }
        if (workSheetImportDto.getPlanEndDate() == null) {
            return new BaseDTO(Constants.KO, "计划完成日期不能为空");
        }
        if (workSheetImportDto.getDeliveryDate() == null) {
            return new BaseDTO(Constants.KO, "交付日期不能为空");
        }
        if (!ValidateUtils.isValid(workSheetImportDto.getPedigreeCode())) {
            return new BaseDTO(Constants.KO, "产品谱系编码不能为空");
        }
        if (!ValidateUtils.isValid(workSheetImportDto.getBomInfoCode())) {
            return new BaseDTO(Constants.KO, "物料清单编码不能为空");
        }
        if (!ValidateUtils.isValid(workSheetImportDto.getWorkFlowCode())) {
            return new BaseDTO(Constants.KO, "工艺路线编码不能为空");
        }
        if (!ValidateUtils.isValid(workSheetImportDto.getWorkLineCode())) {
            return new BaseDTO(Constants.KO, "生产线编码不能为空");
        }

        workSheet.setPriority(workSheetImportDto.getPriority())
                .setSerialNumber(workSheetImportDto.getSerialNumber())
                .setNumber(workSheetImportDto.getNumber())
                .setPlanStartDate(workSheetImportDto.getPlanStartDate())
                .setPlanEndDate(workSheetImportDto.getPlanEndDate())
                .setDeliveryDate(workSheetImportDto.getDeliveryDate())
                .setCategory(workSheetImportDto.getCategory());
        Pedigree pedigree = pedigreeMap.get(workSheetImportDto.getPedigreeCode());
        if (pedigree == null) {
            return new BaseDTO(Constants.KO, "产品谱系编码不存在");
        }
        workSheet.setPedigree(pedigree);
        if (ValidateUtils.isValid(workSheetImportDto.getBomInfoCode())) {
            BomInfoDTO bomInfoDto = rbaseBomProxy.findByCode(workSheetImportDto.getBomInfoCode(),null);
            if (bomInfoDto == null || bomInfoDto.getId() == null) {
                return new BaseDTO(Constants.KO, "物料清单编码不存在");
            }
            workSheet.setBomInfoId(bomInfoDto.getId());
        }
        WorkFlow workFlow = workFlowMap.get(workSheetImportDto.getWorkFlowCode());
        if (workFlow == null) {
            return new BaseDTO(Constants.KO, "工艺路线编码不存在");
        }
        workSheet.setWorkFlow(workFlow);
        //
        OrganizationDTO organizationDto = organizationMap.get(workSheetImportDto.getOrganizationCode());
        if (organizationDto == null) {
            return new BaseDTO(Constants.KO, "部门编码不存在");
        }
        workSheet.setOrganizationId(organizationDto.getId());
        if (clientMap != null && ValidateUtils.isValid(workSheetImportDto.getClientCode())) {
            ClientDTO clientDto = clientMap.get(workSheetImportDto.getClientCode());
            if (clientDto == null) {
                return new BaseDTO(Constants.KO, "客户编码不存在");
            }
            workSheet.setClientId(clientDto.getId());
        }
        WorkLine workLine = workLineMap.get(workSheetImportDto.getWorkLineCode());
        if (workLine == null) {
            return new BaseDTO(Constants.KO, "生产线编码不存在");
        }
        workSheet.setWorkLine(workLine);

        return new BaseDTO(Constants.OK);
    }

    @Transactional(readOnly = true)
    public List<StepDTO> validateWorkSheetExcelStepInfo(WorkSheet workSheet) {

        WorkFlowDTO workFlowDto = workFlowStepService.findByWorkFlowId(workSheet.getWorkFlow().getId());


        if (!ValidateUtils.isValid(workFlowDto.getStepDtoList())) {
            throw new ResponseException("error.stepNotExist", "工艺路线工序未配置空");
        }
        List<StepDTO> stepDtoList = Lists.newArrayList();
        Pedigree pedigree = workSheet.getPedigree();
        WorkFlow workFlow = workSheet.getWorkFlow();
        List<String> notExistConfigList = new ArrayList<>();
        for (StepDTO stepDto : workFlowDto.getStepDtoList()) {
            Step step = stepRepository.getReferenceById(stepDto.getId());
            StepDTO stepConfigDto = commonService.findPedigreeStepConfig(workSheet.getClientId(),pedigree, workFlow, step);
            if (null == stepConfigDto) {
                notExistConfigList.add(step.getCode());
            } else {
                stepDtoList.add(stepConfigDto.setAfterStepId(stepDto.getAfterStepId()).setPreStepId(stepDto.getPreStepId()));
            }
        }
        if (!CollectionUtils.isEmpty(notExistConfigList)) {
            throw new ResponseException("error.stepConfigNotExist", "工序配置[" + Joiner.on(",").join(notExistConfigList) + "]不存在");
        }
        return stepDtoList;
    }

    /**
     * 保存工单定制工序
     *
     * @param workSheet 新增工单参数
     * @param workFlow  工艺流程框图数据
     * @param pedigree  产品谱系数据
     **/
    public void saveExcelWsStepInfo(WorkSheet workSheet, WorkFlow workFlow, Pedigree pedigree, List<StepDTO> stepDTOList) {
        //获取系统配置的投产粒度(子工单或者工单),只有投产粒度为子工单时才会进行分单
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
        this.saveExcelWsSteps(subWsProductionMode, workSheet, workFlow, pedigree, stepDTOList);
        if (subWsProductionMode) {
            //自动划分子工单(放在WorkSheetService里面会存在事务未完成导致生成子工单号失败)
            PedigreeConfig pedigreeConfig = commonService.findPedigreeConfig(workSheet.getPedigree());
            subWorkSheetService.autoGenerateSubWorkSheet(workSheet.getId(), Optional.ofNullable(workSheet.getWorkLine()).map(WorkLine::getId).orElse(null), workSheet.getPlanStartDate(), workSheet.getPlanEndDate(),
                    null != pedigreeConfig && pedigreeConfig.getSplitNumber() > Constants.INT_ZERO ? pedigreeConfig.getSplitNumber() : Constants.INT_TWO_HUNDRED, Boolean.TRUE);
        }
    }

    /**
     * 保存工单定制工序
     *
     * @param workSheet   总工单
     * @param stepDtoList 定制工序列表
     * <AUTHOR>
     * @date 2021-01-29
     **/
    public List<WsStep> saveExcelWsSteps(Boolean subWsProductMode, WorkSheet workSheet, WorkFlow workFlow, Pedigree pedigree, List<StepDTO> stepDtoList) {
        List<WsStep> wsSteps = Lists.newArrayList();
        //保存生产定制工序信息
        if (ValidateUtils.isValid(stepDtoList)) {
            stepDtoList.forEach(stepDto -> {
                WsStep wsStep = new WsStep();
                wsStep.setStep(net.airuima.rbase.util.MapperUtils.map(stepDto, Step.class))
                        .setAfterStepId(stepDto.getAfterStepId())
                        .setPreStepId(stepDto.getPreStepId())
                        .setCategory(stepDto.getCategory())
                        .setControlMode(stepDto.getControlMode())
                        .setIsBindContainer(stepDto.getIsBindContainer())
                        .setIsControlMaterial(stepDto.getIsControlMaterial())
                        .setRequestMode(stepDto.getRequestMode())
                        .setInputRate(stepDto.getInputRate() == Constants.DOUBLE_ZERRO ? Constants.INT_ONE : stepDto.getInputRate())
                        .setWorkSheet(workSheet);
                wsSteps.add(wsStep);
            });
            wsStepRepository.deleteByWorkSheetId(workSheet.getId());
            wsStepRepository.saveAll(wsSteps);
        }
        //工单投产粒度时需要更新工单的工序个数
        if (!subWsProductMode && !CollectionUtils.isEmpty(wsSteps)) {
            workSheet.setStepNumber(wsSteps.size());
            workSheetRepository.save(workSheet);
        }
        return wsSteps;
    }


    /**
     * 响应返回给客户端错误数据
     *
     * @param errorWorkSheetImportDTOList 有问题数据数据
     * @param number                      导入个数
     * @param file                        导入文件
     * @param response                    响应
     */
    public BaseDTO exportImportWorkSheetData(List<WorkSheetImportDTO> errorWorkSheetImportDTOList, Integer number, MultipartFile file, HttpServletResponse response) throws Exception {
        if (ValidateUtils.isValid(errorWorkSheetImportDTOList)) {
            ExportParams exportParams = new ExportParams();
            exportParams.setType(ExcelType.XSSF);
            exportParams.setFreezeCol(Constants.INT_TWO);
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, WorkSheetImportDTO.class, errorWorkSheetImportDTOList);

            String originalFilename = ValidateUtils.isValid(file.getOriginalFilename()) ? file.getOriginalFilename() : "工单信息";
            response.setContentType(originalFilename.contains("xlsx") ? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" : "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(originalFilename, "utf-8"));
            response.setStatus(HttpStatus.BAD_REQUEST.value());
            response.setHeader("X-app-alert", "app.import.failure");
            String errorMessage = "上传数据" + number + "条,导入成功" + (number - errorWorkSheetImportDTOList.size()) + "条,导入失败" + errorWorkSheetImportDTOList.size() + "条,请检查下载的文件,检查失败的详细原因";
            response.setHeader(HeaderUtil.APP_PARAMS, URLEncoder.encode(errorMessage, "UTF-8"));
            response.setHeader(HeaderUtil.APP_ERROR_MESSAGE, URLEncoder.encode(errorMessage, "UTF-8"));
            workbook.write(response.getOutputStream());
            return new BaseDTO(Constants.KO);
        }
        return new BaseDTO(Constants.OK);
    }
}

