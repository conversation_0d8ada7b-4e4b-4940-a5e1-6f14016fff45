package net.airuima.rbase.service.procedure.aps;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.domain.procedure.aps.CascadeWorkSheet;
import net.airuima.rbase.domain.procedure.aps.SaleOrder;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.aps.SaleOrderIssuedDTO;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.bom.BomDTO;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.dto.process.StepDTO;
import net.airuima.rbase.dto.process.WorkFlowDTO;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.dto.sync.SyncResultDTO;
import net.airuima.rbase.dto.sync.SyncSaleOrderDTO;
import net.airuima.rbase.integrate.rmps.IWorkSheetSyncMpsService;
import net.airuima.rbase.integrate.rwms.IShipmentOrderSyncWmsService;
import net.airuima.rbase.proxy.bom.RbaseBomProxy;
import net.airuima.rbase.proxy.organization.RbaseClientProxy;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.repository.base.scene.WorkLineRepository;
import net.airuima.rbase.repository.procedure.aps.CascadeWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.SaleOrderRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.service.base.process.WorkFlowStepService;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.procedure.material.IWsMaterialService;
import net.airuima.rbase.util.MapperUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 销售订单Service
 *
 * <AUTHOR>
 * @date 2022-12-19
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SaleOrderService extends CommonJpaService<SaleOrder> {

    private final String SALE_ORDER_ENTITY_GRAPH = "saleOrderEntityGraph";
    private final SaleOrderRepository saleOrderRepository;
    private final WorkLineRepository workLineRepository;
    private final WorkSheetRepository workSheetRepository;
    private final WorkFlowRepository workFlowRepository;
    private final StepRepository stepRepository;
    private final PedigreeRepository pedigreeRepository;
    @Autowired
    private WorkSheetService workSheetService;
    @Autowired
    private WorkFlowStepService workFlowStepService;
    @Autowired
    private SubWorkSheetService subWorkSheetService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private RbaseClientProxy rbaseClientProxy;
    @Autowired
    private IWsMaterialService[] wsMaterialServices;
    @Autowired
    private IWorkSheetSyncMpsService[] workSheetSyncMpsFeignClients;
    @Autowired
    private IShipmentOrderSyncWmsService[] shipmentOrderSyncWmsServices;
    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;
    @Autowired
    private RbaseBomProxy rbaseBomProxy;
    @Autowired
    private CascadeWorkSheetRepository cascadeWorkSheetRepository;


    public SaleOrderService(SaleOrderRepository saleOrderRepository, WorkLineRepository workLineRepository, WorkSheetRepository workSheetRepository, WorkFlowRepository workFlowRepository
            , StepRepository stepRepository, PedigreeRepository pedigreeRepository) {
        this.saleOrderRepository = saleOrderRepository;
        this.workLineRepository = workLineRepository;
        this.workSheetRepository = workSheetRepository;
        this.workFlowRepository = workFlowRepository;
        this.stepRepository = stepRepository;
        this.pedigreeRepository = pedigreeRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<SaleOrder> find(Specification<SaleOrder> spec, Pageable pageable) {
        return saleOrderRepository.findAll(spec, pageable, new NamedEntityGraph(SALE_ORDER_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<SaleOrder> find(Specification<SaleOrder> spec) {
        return saleOrderRepository.findAll(spec, new NamedEntityGraph(SALE_ORDER_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<SaleOrder> findAll(Pageable pageable) {
        return saleOrderRepository.findAll(pageable, new NamedEntityGraph(SALE_ORDER_ENTITY_GRAPH));
    }

    /**
     * 订单下发
     *
     * @param saleOrderIssuedDTO 订单下发
     * @date 2022-12-21
     * <AUTHOR>
     */
    public BaseDTO orderIssued(SaleOrderIssuedDTO saleOrderIssuedDTO) {
        if (LocalDate.now().isAfter(saleOrderIssuedDTO.getPlanStartDate()) || LocalDate.now().isAfter(saleOrderIssuedDTO.getPlanEndDate())){
            throw new ResponseException("error.planEndDateOrStartDateLessThanCurrentTime", "计划时间不能小于当前时间");
        }
        Optional<SaleOrder> saleOrder = saleOrderRepository.findByIdAndDeleted(saleOrderIssuedDTO.getSaleOrderId(), Constants.LONG_ZERO);
        if (saleOrder.isEmpty()){
            throw new ResponseException("error.saleOrderNotExist", "销售订单不存在");
        }
        SaleOrder order = saleOrder.get();
        Optional<WorkLine> workLine = workLineRepository.findByIdAndDeleted(saleOrderIssuedDTO.getWorkLineId(), Constants.LONG_ZERO);
        if (workLine.isEmpty()){
            throw new ResponseException("error.workLineNotExist", "生产线不存在");
        }
        WorkFlow workFlow = workFlowRepository.getReferenceById(saleOrderIssuedDTO.getWorkFlowId());
        WorkFlowDTO workFlowDTO = workFlowStepService.findByWorkFlowId(workFlow.getId());
        if(CollectionUtils.isEmpty(workFlowDTO.getStepDtoList())){
            throw new ResponseException("error.workFlowStepNotExist", "工艺路线【"+workFlow.getName()+"】未绑定工序");
        }
        for (StepDTO stepDto : workFlowDTO.getStepDtoList()) {
            Step step = stepRepository.getReferenceById(stepDto.getId());
            StepDTO stepConfigDto = commonService.findPedigreeStepConfig(order.getClientId(), order.getPedigree(), workFlow, step);
            if (null == stepConfigDto) {
                throw new ResponseException("error.stepConfigNotExist", step.getCode() +"工序配置不存在");
            }
        }
        List<WorkSheet> workSheets = new ArrayList<>();
        if (ValidateUtils.isValid(saleOrderIssuedDTO.getWorkSheetInfos())) {
            String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
            boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
            PedigreeConfig pedigreeConfig = commonService.findPedigreeConfig(order.getPedigree());
            saleOrderIssuedDTO.getWorkSheetInfos().forEach(workSheetInfo -> {
                String serialNumber = workSheetInfo.getSerialNumber();
                if (!ValidateUtils.isValid(serialNumber)) {
                    SerialNumberDTO serialNumberDTO = new SerialNumberDTO(Constants.KEY_SERIAL_NUMBER_NORMAL_WORK_SHEET, null, workLine.get().getOrganizationId());
                    serialNumber = rbaseSerialNumberProxy.generate(serialNumberDTO);
                }
                Optional<WorkSheet> workSheetOptional = workSheetRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO);
                if (workSheetOptional.isPresent()){
                    throw new ResponseException("error.serialNumberIsExist", "工单号已存在");
                }
                WorkSheet workSheet = new WorkSheet();
                workSheet.setSerialNumber(serialNumber);
                workSheet.setNumber(workSheetInfo.getNumber());
                workSheet.setClientId(order.getClientId());
                workSheet.setPlanStartDate(saleOrderIssuedDTO.getPlanStartDate().atStartOfDay());
                workSheet.setPlanEndDate(saleOrderIssuedDTO.getPlanEndDate().atStartOfDay());
                workSheet.setCategory(Constants.INT_ONE);
                workSheet.setStatus(Constants.INT_ZERO);
                workSheet.setNote(workSheetInfo.getNote());
                workSheet.setOrganizationId(workLine.get().getOrganizationId());
                workSheet.setBomInfoId(saleOrderIssuedDTO.getBomInfoId());
                workSheet.setPedigree(order.getPedigree());
                workSheet.setPriority(order.getPriority());
                workSheet.setWorkFlow(workFlow);
                workSheet.setWorkLine(workLine.get());
                workSheet.setGenerateSubWsStatus(Constants.INT_ZERO);
                workSheet.setDownGradeNumber(Constants.INT_ZERO);
                workSheet.setDeleted(Constants.LONG_ZERO);
                workSheet.setSaleOrder(order).setDeliveryDate(order.getDeliveryDate());
                WorkSheet fianlWorkSheet = workSheetRepository.save(workSheet);
                workSheetService.saveWsSteps(subWsProductionMode,workSheet,workFlow,workSheet.getPedigree(),workFlowDTO.getStepDtoList());
                List<BomDTO> bomDtoList = rbaseBomProxy.findByBomInfoId(saleOrderIssuedDTO.getBomInfoId());
                wsMaterialServices[0].saveWsMaterial(fianlWorkSheet,bomDtoList);
                order.setProductionQuantity(order.getProductionQuantity() + workSheetInfo.getNumber());
                //如果分单配置存在且子工单投产则自动分单
                if (null != pedigreeConfig && pedigreeConfig.getIsEnable() && subWsProductionMode && pedigreeConfig.getSplitNumber() > Constants.INT_ZERO) {
                    subWorkSheetService.autoGenerateSubWorkSheet(workSheet.getId(), Optional.ofNullable(workSheet.getWorkLine()).map(WorkLine::getId).orElse(null), workSheet.getPlanStartDate(), workSheet.getPlanEndDate(),
                            pedigreeConfig.getSplitNumber(), Boolean.TRUE);
                }
                //保存可能的级联的下级工单信息
                List<WorkSheet> subordinateWorkSheetList = this.saveCascadeWorkSheet(subWsProductionMode,fianlWorkSheet,workSheetInfo.getCascadeWorkSheetInfoList());
                workSheets.add(fianlWorkSheet);
                if(!CollectionUtils.isEmpty(subordinateWorkSheetList)){
                    workSheets.addAll(subordinateWorkSheetList);
                }
            });
        }
        order.setPlanStartDate(saleOrderIssuedDTO.getPlanStartDate());
        order.setPlanEndDate(saleOrderIssuedDTO.getPlanEndDate());
        saleOrderRepository.save(order);
        if (ValidateUtils.isValid(workSheets)){
            for (WorkSheet workSheet : workSheets) {
                // 同步工单信息到MPS
                workSheetSyncMpsFeignClients[0].syncMpsWorkSheet(workSheet.getId());
                // 同步工单信息到WMS
                shipmentOrderSyncWmsServices[0].syncShipmentOrder(workSheet);
            }
        }
        return new BaseDTO(Constants.OK);
    }

    /**
     * 保存级联下单的下级工单信息
     * @param subWsProductionMode 投产模式
     * @param superiorWorkSheet 上级工单
     * @param cascadeWorkSheetInfoList 下级工单下单参数列表
     * @return List<WorkSheet> 下级工单列表
     */
    public List<WorkSheet> saveCascadeWorkSheet(Boolean subWsProductionMode,WorkSheet superiorWorkSheet,
                                                List<SaleOrderIssuedDTO.WorkSheetInfo.CascadeWorkSheetInfo> cascadeWorkSheetInfoList){
        if(CollectionUtils.isEmpty(cascadeWorkSheetInfoList)){
            return null;
        }
        List<WorkSheet> subordinateWorkSheet = new ArrayList<>();
        cascadeWorkSheetInfoList.forEach(cascadeWorkSheetInfo -> {
            String serialNumber = cascadeWorkSheetInfo.getSerialNumber();
            WorkLine workLine = workLineRepository.getReferenceById(cascadeWorkSheetInfo.getWorkLineId());
            if (!ValidateUtils.isValid(serialNumber)) {
                SerialNumberDTO serialNumberDTO = new SerialNumberDTO(Constants.KEY_SERIAL_NUMBER_NORMAL_WORK_SHEET, null, workLine.getOrganizationId());
                serialNumber = rbaseSerialNumberProxy.generate(serialNumberDTO);
            }
            Optional<WorkSheet> workSheetOptional = workSheetRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO);
            if (workSheetOptional.isPresent()){
                throw new ResponseException("error.serialNumberIsExist", "工单号已存在");
            }
            Pedigree pedigree = pedigreeRepository.getReferenceById(cascadeWorkSheetInfo.getPedigreeId());
            WorkFlow workFlow = workFlowRepository.getReferenceById(cascadeWorkSheetInfo.getWorkFlowId());
            WorkFlowDTO workFlowDTO = workFlowStepService.findByWorkFlowId(workFlow.getId());
            if(CollectionUtils.isEmpty(workFlowDTO.getStepDtoList())){
                throw new ResponseException("error.workFlowStepNotExist", "工艺路线【"+workFlow.getName()+"未绑定工序");
            }
            for (StepDTO stepDto : workFlowDTO.getStepDtoList()) {
                Step step = stepRepository.getReferenceById(stepDto.getId());
                StepDTO stepConfigDto = commonService.findPedigreeStepConfig(null, pedigree, workFlow, step);
                if (null == stepConfigDto) {
                    throw new ResponseException("error.stepConfigNotExist", step.getCode() +"工序配置不存在");
                }
            }
            PedigreeConfig pedigreeConfig = commonService.findPedigreeConfig(pedigree);
            WorkSheet workSheet = new WorkSheet();
            workSheet.setSerialNumber(serialNumber);
            workSheet.setNumber(cascadeWorkSheetInfo.getNumber());
            workSheet.setClientId(null);
            workSheet.setPlanStartDate(cascadeWorkSheetInfo.getPlanStartDate().atStartOfDay());
            workSheet.setPlanEndDate(cascadeWorkSheetInfo.getPlanEndDate().atStartOfDay());
            workSheet.setCategory(Constants.INT_ONE);
            workSheet.setStatus(Constants.INT_ZERO);
            workSheet.setOrganizationId(workLine.getOrganizationId());
            workSheet.setBomInfoId(cascadeWorkSheetInfo.getBomInfoId());
            workSheet.setPedigree(pedigree);
            workSheet.setPriority(superiorWorkSheet.getPriority());
            workSheet.setWorkFlow(workFlow);
            workSheet.setWorkLine(workLine);
            workSheet.setGenerateSubWsStatus(Constants.INT_ZERO);
            workSheet.setDownGradeNumber(Constants.INT_ZERO);
            workSheet.setDeleted(Constants.LONG_ZERO);
            workSheet.setDeliveryDate(superiorWorkSheet.getDeliveryDate());
            WorkSheet fianlWorkSheet = workSheetRepository.save(workSheet);
            workSheetService.saveWsSteps(subWsProductionMode,workSheet,workFlow,workSheet.getPedigree(),workFlowDTO.getStepDtoList());
            List<BomDTO> bomDtoList = rbaseBomProxy.findByBomInfoId(cascadeWorkSheetInfo.getBomInfoId());
            wsMaterialServices[0].saveWsMaterial(fianlWorkSheet,bomDtoList);
            //如果分单配置存在且子工单投产则自动分单
            if (null != pedigreeConfig && pedigreeConfig.getIsEnable() && subWsProductionMode && pedigreeConfig.getSplitNumber() > Constants.INT_ZERO) {
                subWorkSheetService.autoGenerateSubWorkSheet(workSheet.getId(), Optional.ofNullable(workSheet.getWorkLine()).map(WorkLine::getId).orElse(null), workSheet.getPlanStartDate(), workSheet.getPlanEndDate(),
                        pedigreeConfig.getSplitNumber(), Boolean.TRUE);
            }
            CascadeWorkSheet cascadeWorkSheet = new CascadeWorkSheet(superiorWorkSheet,workSheet);
            cascadeWorkSheet.setDeleted(Constants.LONG_ZERO);
            cascadeWorkSheetRepository.save(cascadeWorkSheet);
            subordinateWorkSheet.add(fianlWorkSheet);
        });
        return subordinateWorkSheet;
    }

    /**
     * 新增销售订单
     *
     * @param instance 参数
     * @return SaleOrder
     */
    public SaleOrder saveInstance(SaleOrder instance) {
        this.validate(instance);
        instance.setDeleted(Constants.LONG_ZERO);
        return saleOrderRepository.save(instance);
    }

    /**
     * 修改销售订单
     *
     * @param instance 参数
     * @return SaleOrder
     */
    public SaleOrder updateInstance(SaleOrder instance) {
        return saveInstance(instance);
    }

    /**
     * 验证销售订单
     *
     * @param instance 参数
     */

    public void validate(SaleOrder instance){
        if (LocalDate.now().isAfter(instance.getDeliveryDate())){
            throw new ResponseException("error.deliveryDateIsOverNow", "交付日期必须大于等于当前日期");
        }
    }

    /**
     * 根据订单号模糊查询
     *
     * @param text 根据订单号模糊查询
     * @param size 返回数据个数
     * @return List<SaleOrder>
     * <AUTHOR>
     * @date 2022/12/29
     */
    @Transactional(readOnly = true)
    public List<SaleOrder> bySerialNumberAndPedigreeId(String text, Long pedigreeId, int size) {
        return Optional.ofNullable(saleOrderRepository.bySerialNumberAndPedigreeId(text, pedigreeId, PageRequest.of(Constants.INT_ZERO, size))).map(Slice::getContent).orElse(null);
    }

    /**
     * 同步订单
     *
     * @param syncSaleOrderList
     * <AUTHOR>
     * @date 2023/3/17
     */
    public List<SyncResultDTO> syncSaleOrder(List<SyncSaleOrderDTO> syncSaleOrderList) {
        List<SyncResultDTO> syncResultDTOList = Lists.newArrayList();
        if (ValidateUtils.isValid(syncSaleOrderList)) {
            syncSaleOrderList.forEach(syncSaleOrder -> {
                SyncResultDTO syncResultDto = new SyncResultDTO(syncSaleOrder.getId(), Constants.INT_ONE, "");
                Optional<SaleOrder> saleOrderOptional = saleOrderRepository.findBySerialNumberAndDeleted(syncSaleOrder.getSerialNumber(), Constants.LONG_ZERO);
                //订单删除-已下发订单不能删除
                if (syncSaleOrder.getOperate() == Constants.INT_TWO) {
                    if (saleOrderOptional.isEmpty()) {
                        syncResultDTOList.add(syncResultDto.setStatus(Constants.INT_TWO).setMessage("订单编号不存在"));
                        return;
                    }
                    SaleOrder saleOrder = saleOrderOptional.get();
                    List<WorkSheet> workSheets = workSheetRepository.findBySaleOrderIdAndDeleted(saleOrder.getId(), Constants.LONG_ZERO);
                    if (ValidateUtils.isValid(workSheets)) {
                        syncResultDTOList.add(syncResultDto.setStatus(Constants.INT_TWO).setMessage("订单编号已下发工单不能删除"));
                        return;
                    }
                    saleOrderRepository.logicDelete(saleOrder);
                }
                //新增
                if (syncSaleOrder.getOperate() == Constants.INT_ZERO) {
                    if (saleOrderOptional.isPresent()) {
                        syncResultDTOList.add(syncResultDto.setStatus(Constants.INT_TWO).setMessage("销售订单已存在"));
                        return;
                    }
                    SaleOrder saleOrder = MapperUtils.map(syncSaleOrder, SaleOrder.class);
                    saleOrder.setId(null);
                    if (!ObjectUtils.isEmpty(syncSaleOrder.getPedigreeCode())) {
                        Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByCodeAndDeleted(syncSaleOrder.getPedigreeCode(), Constants.LONG_ZERO);
                        saleOrder.setPedigree(pedigreeOptional.orElse(null));
                    }
                    if (!ObjectUtils.isEmpty(syncSaleOrder.getClientCode())) {
                        ClientDTO client = rbaseClientProxy.findByCodeAndDeleted(syncSaleOrder.getClientCode(),Constants.LONG_ZERO);
                        if (!ObjectUtils.isEmpty(client)) {
                            saleOrder.setClientId(client.getId());
                        }
                    }
                    saleOrderRepository.save(saleOrder);
                }
                //修改-目前修改只能修改时间订单下单时间
                if (syncSaleOrder.getOperate() == Constants.INT_ONE) {
                    if (!saleOrderOptional.isPresent()) {
                        syncResultDTOList.add(syncResultDto.setStatus(Constants.INT_TWO).setMessage("订单编号不存在"));
                        return;
                    }
                    SaleOrder saleOrder = saleOrderOptional.get().setDeliveryDate(syncSaleOrder.getDeliveryDate());
                    saleOrderRepository.save(saleOrder);
                }
                syncResultDTOList.add(syncResultDto);
            });
        }
        return syncResultDTOList;
    }

}
