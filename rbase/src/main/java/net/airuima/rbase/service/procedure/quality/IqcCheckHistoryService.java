package net.airuima.rbase.service.procedure.quality;

import com.google.common.collect.Lists;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.SampleCaseCategoryEnum;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckItem;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule;
import net.airuima.rbase.domain.procedure.quality.IqcCheckHistory;
import net.airuima.rbase.domain.procedure.quality.IqcCheckHistoryDetail;
import net.airuima.rbase.dto.bom.MaterialAttributeDTO;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.qms.CheckItemDTO;
import net.airuima.rbase.dto.qms.DefectDTO;
import net.airuima.rbase.dto.qms.GbtDetailDTO;
import net.airuima.rbase.dto.qms.SampleCaseDTO;
import net.airuima.rbase.dto.quality.MrbApplicantRequestDTO;
import net.airuima.rbase.dto.quality.MrbProcessResultDTO;
import net.airuima.rbase.proxy.bom.RbaseMaterialProxy;
import net.airuima.rbase.proxy.qms.*;
import net.airuima.rbase.proxy.rqms.RbaseMrbProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepCheckItemRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepCheckRuleRepository;
import net.airuima.rbase.repository.procedure.quality.IqcCheckHistoryDetailRepository;
import net.airuima.rbase.repository.procedure.quality.IqcCheckHistoryRepository;
import net.airuima.rbase.util.ToolUtils;
import net.airuima.rbase.web.rest.procedure.quality.dto.IqcCheckDTO;
import net.airuima.rbase.web.rest.procedure.quality.dto.IqcCheckDealDTO;
import net.airuima.rbase.web.rest.procedure.quality.dto.IqcCheckHistoryDetailDTO;
import net.airuima.rbase.web.rest.procedure.quality.dto.IqcCheckPreviewDTO;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 来料检验Service
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class IqcCheckHistoryService extends CommonJpaService<IqcCheckHistory> {

    private final IqcCheckHistoryRepository iqcCheckHistoryRepository;


    @Autowired
    private RbaseMaterialProxy rbaseMaterialProxy;

    @Autowired
    private RbaseCheckItemProxy checkItemRepository;

    @Autowired
    private RbaseDefectProxy defectRepository;

    @Autowired
    private IqcCheckHistoryDetailRepository iqcCheckHistoryDetailRepository;

    @Autowired
    private PedigreeStepCheckRuleRepository pedigreeStepCheckRuleRepository;

    @Autowired
    private PedigreeStepCheckItemRepository pedigreeStepCheckItemRepository;

    @Autowired
    private RbaseSamplingStrategyProxy samplingStrategyServiceImpl;

    @Autowired
    private RbaseDefectCheckItemProxy defectCheckItemRepository;

    @Autowired
    private RbaseMrbProxy rbaseMrbProxy;
    @Autowired
    private RbaseGbtDetailProxy gbtDetailRepository;

    public IqcCheckHistoryService(IqcCheckHistoryRepository iqcCheckHistoryRepository) {
        this.iqcCheckHistoryRepository = iqcCheckHistoryRepository;
    }


    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<IqcCheckHistory> find(Specification<IqcCheckHistory> spec, Pageable pageable) {
        return iqcCheckHistoryRepository.findAll(spec, pageable);
    }


    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<IqcCheckHistory> find(Specification<IqcCheckHistory> spec) {
        return iqcCheckHistoryRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<IqcCheckHistory> findAll(Pageable pageable) {
        return null;
    }


    /**
     * 保存来料检验
     *
     * @param entity 来料检验参数
     * @return net.airuima.rbase.domain.procedure.quality.IqcCheckHistory 来料检验
     */
    public IqcCheckHistory saveInstance(IqcCheckHistory entity) {
        String serialNumber = entity.getSerialNumber();
        Optional<IqcCheckHistory> iqcCheckHistoryOptional = iqcCheckHistoryRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO);
        if (iqcCheckHistoryOptional.isPresent()) {
            throw new ResponseException("error.iqcCheckHistoryExist", "来料检验已存在");
        }
        List<Long> attributeIdList = getAttributeList(entity.getMaterialId());
        List<PedigreeStepCheckRule> pedigreeStepCheckRuleList = pedigreeStepCheckRuleRepository.findByCategoryAndAttributeIdAndMaterialIdAndSupplierIdAndClientIdAndDeleted(Constants.INT_FIVE,
                attributeIdList,
                entity.getMaterialId(),
                entity.getSupplierId(),
                entity.getClientId(),
                Constants.LONG_ZERO);
        if(CollectionUtils.isEmpty(pedigreeStepCheckRuleList)){
            throw new ResponseException("error.iqcCheckRuleNotExist", "质检方案不存在");
        }
        calculateValidTime(entity);
        return this.save(entity);
    }

    /**
     * 计算有效期
     *
     * @param entity 来料检验参数
     */
    private void calculateValidTime(IqcCheckHistory entity) {
        MaterialDTO materialDto = rbaseMaterialProxy.findByIdAndDeleted(entity.getMaterialId(),Constants.LONG_ZERO);
        if (Objects.nonNull(materialDto)) {
            // 设置有效期
            int expireDay = Optional.of(materialDto.getExpireDay()).orElse(Constants.INT_ZERO);
            entity.setValidTime(entity.getProductionTime().plusDays(expireDay));
        }
    }

    /**
     * 更新来料检验
     *
     * @param entity 来料检验参数
     * @return net.airuima.rbase.domain.procedure.quality.IqcCheckHistory 来料检验
     */
    public IqcCheckHistory updateInstance(IqcCheckHistory entity) {
        if (entity.getId() == null) {
            throw new ResponseException("error.iqcCheckHistoryNotExist", "来料检验不存在");
        }
        calculateValidTime(entity);
        return this.save(entity);
    }

    /**
     * 执行来料检验
     *
     * @param iqcCheckDto 执行来料检验参数
     */
    public void check(IqcCheckDTO iqcCheckDto) {
        // 来料检验单id
        Long id = iqcCheckDto.getId();
        Optional<IqcCheckHistory> iqcCheckHistoryOptional = iqcCheckHistoryRepository.findByIdAndDeleted(id, Constants.LONG_ZERO);
        if (iqcCheckHistoryOptional.isEmpty()) {
            throw new ResponseException("error.iqcCheckHistoryNotExist", "来料检验不存在");
        }
        Long checkRuleId = iqcCheckDto.getCheckRuleId();
        if (Objects.isNull(checkRuleId)) {
            throw new ResponseException("error.iqcCheckRuleNotExist", "质检方案不存在");
        }
        Optional<PedigreeStepCheckRule> pedigreeStepCheckRuleOptional = pedigreeStepCheckRuleRepository.findByIdAndDeleted(checkRuleId, Constants.LONG_ZERO);
        if (pedigreeStepCheckRuleOptional.isEmpty()) {
            throw new ResponseException("error.iqcCheckRuleNotExist", "质检方案不存在");
        }
        IqcCheckHistory iqcCheckHistory = iqcCheckHistoryOptional.get();
        List<IqcCheckHistoryDetailDTO> detailList = iqcCheckDto.getDetailList();
        if (CollectionUtils.isEmpty(detailList)) {
            throw new ResponseException("error.iqcCheckDetailNotExist", "来料检验详情不存在");
        }
        List<IqcCheckHistoryDetail> iqcCheckHistoryDetailList = Lists.newArrayList();
        detailList.forEach(detail -> {
            IqcCheckHistoryDetail iqcCheckHistoryDetail = new IqcCheckHistoryDetail();
            Long checkItemId = detail.getCheckItemId();
            CheckItemDTO checkItem = checkItemRepository.findByIdAndDeleted(checkItemId, Constants.LONG_ZERO);
            Long defectId = detail.getDefectId();
            DefectDTO defect = null;
            if (Objects.nonNull(defectId)) {
                defect = defectRepository.findByIdAndDeleted(defectId, Constants.LONG_ZERO).orElse(null);
            }
            // 计算检测结果
            // 判断是否是OK/NG
            if (Constants.OK.equals(checkItem.getQualifiedRange())) {
                iqcCheckHistoryDetail.setResult(Constants.OK.equals(detail.getCheckData()));
            }
            // 判断范围是否合格
            if (Objects.nonNull(checkItem.getQualifiedRange()) && !Constants.OK.equals(checkItem.getQualifiedRange())) {
                iqcCheckHistoryDetail.setResult(ToolUtils.compareInterval(checkItem.getQualifiedRange(), detail.getCheckData()));
            }
            iqcCheckHistoryDetail.setCheckHistory(iqcCheckHistory)
                    .setCheckItem(checkItem)
                    .setControl(checkItem.getControl())
                    .setCheckData(detail.getCheckData())
                    .setQualifiedRange(checkItem.getQualifiedRange())
                    .setDefect(defect)
                    .setSn(detail.getSn());
            iqcCheckHistoryDetailList.add(iqcCheckHistoryDetail);
        });
//        // 是否管控 有一个不管控则不管控
//        boolean control = iqcCheckHistoryDetailList.stream().map(IqcCheckHistoryDetail::getControl).allMatch(Boolean.TRUE::equals);
//        // 如果管控则需要判断结果
//        if (control) {
//            // 全部合格 则合格
//            boolean result = iqcCheckHistoryDetailList.stream().map(IqcCheckHistoryDetail::getResult).allMatch(Boolean.TRUE::equals);
//            iqcCheckHistory.setResult(result);
//            // 管控 自动计算不合格的数量
//           List<IqcCheckHistoryDetail> unqualifiedDetails = iqcCheckHistoryDetailList.stream().filter(d -> Boolean.FALSE.equals(d.getResult())).toList();
//           long count = CollectionUtils.isEmpty(unqualifiedDetails) ? Constants.LONG_ZERO:unqualifiedDetails.stream().map(IqcCheckHistoryDetail::getSn).collect(Collectors.toSet()).size();
//            iqcCheckHistory.setUnqualifiedNumber(BigDecimal.valueOf(count));
//        } else {
//            // 不管控 则直接取用户填的结果
//            iqcCheckHistory.setResult(iqcCheckDto.getResult());
//            iqcCheckHistory.setUnqualifiedNumber(Optional.ofNullable(iqcCheckDto.getUnqualifiedNumber()).orElse(BigDecimal.ZERO));
//        }
        iqcCheckHistory.setResult(iqcCheckDto.getResult());
        iqcCheckHistory.setUnqualifiedNumber(Optional.ofNullable(iqcCheckDto.getUnqualifiedNumber()).orElse(BigDecimal.ZERO));
        iqcCheckHistory
                .setDealerId(iqcCheckDto.getOperatorId())
                .setOperatorId(iqcCheckDto.getOperatorId())
                .setNote(iqcCheckDto.getNote())
                .setDealWay(Optional.ofNullable(iqcCheckDto.getDealWay()).orElse(Constants.INT_ZERO))
                .setStatus(Constants.INT_ONE)
                .setCache(null)
                .setPedigreeStepCheckRule(pedigreeStepCheckRuleOptional.get());
        // 如果检验合格 则处理方式回填通过
        if (Boolean.TRUE.equals(iqcCheckHistory.getResult())) {
            iqcCheckHistory.setDealWay(Constants.INT_ONE);
        }
        BigDecimal number = Optional.ofNullable(iqcCheckHistory.getNumber()).orElse(BigDecimal.ZERO);
        BigDecimal unqualifiedNumber = Optional.ofNullable(iqcCheckHistory.getUnqualifiedNumber()).orElse(BigDecimal.ZERO);
        iqcCheckHistory.setQualifiedNumber(number.subtract(unqualifiedNumber)).setCheckTime(LocalDateTime.now());
        ;
        // 保存来料检验
        this.save(iqcCheckHistory);
        //  保存来料检验详情
        iqcCheckHistoryDetailList.forEach(detail -> detail.setCheckHistory(iqcCheckHistory));
        iqcCheckHistoryDetailRepository.saveAll(iqcCheckHistoryDetailList);
        //如果处理方式是MRB评审则需要发送MRB评审请求
        if(iqcCheckHistory.getDealWay() == Constants.INT_FIVE){
            this.applicantMrb(id,iqcCheckDto.getReason());
        }
    }

    /**
     * 预览来料检验
     *
     * @param serialNumber 来料检验单号
     * @param checkRuleId  质检方案ID
     * @return net.airuima.rbase.web.rest.procedure.quality.dto.IqcCheckPreviewDTO 来料检验预览
     */
    @Transactional(readOnly = true)
    public IqcCheckPreviewDTO preview(String serialNumber, Long checkRuleId) {
        Optional<IqcCheckHistory> iqcCheckHistoryOptional = iqcCheckHistoryRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO);
        if (iqcCheckHistoryOptional.isEmpty()) {
            throw new ResponseException("error.iqcCheckHistoryNotExist", "来料检验不存在");
        }
        IqcCheckHistory iqcCheckHistory = iqcCheckHistoryOptional.get();
        IqcCheckPreviewDTO iqcCheckPreviewDTO = new IqcCheckPreviewDTO();
        Long materialId = iqcCheckHistory.getMaterialId();
        List<Long> attributeIdList = getAttributeList(materialId);
        // 待检验 从质检方案获取检验项目
        if (Constants.INT_ZERO == iqcCheckHistory.getStatus()) {
            if (Objects.isNull(checkRuleId)) {
                autoGetCheckRule(attributeIdList, iqcCheckHistory, iqcCheckPreviewDTO);
            } else {
                selectCheckRule(checkRuleId, iqcCheckHistory, iqcCheckPreviewDTO);
            }
        }
        // 已质检的 直接查询质检详情
        if (Constants.INT_ONE == iqcCheckHistory.getStatus()) {
            List<IqcCheckHistoryDetail> iqcCheckHistoryDetailList = iqcCheckHistoryDetailRepository.findByCheckHistoryIdAndDeleted(iqcCheckHistory.getId(), Constants.LONG_ZERO);
            iqcCheckPreviewDTO.setIqcCheckHistory(iqcCheckHistory)
                    .setPedigreeStepCheckRuleList(Lists.newArrayList(iqcCheckHistory.getPedigreeStepCheckRule()))
                    .setDetailList(iqcCheckHistoryDetailList);
        }
        return iqcCheckPreviewDTO;
    }


    /**
     * 获取抽检sn个数
     *
     * @param iqcCheckHistory       来料检验单
     * @param sampleCase 抽样方案
     * @return java.lang.Integer 抽检sn个数
     */
    private Integer getSnCount(IqcCheckHistory iqcCheckHistory, SampleCaseDTO sampleCase,
                               IqcCheckHistoryDetail iqcCheckHistoryDetail) {
        if (Objects.isNull(sampleCase)) {
            return Constants.INT_ZERO;
        }
        // 抽检sn个数
        Integer snCount = 0;
        // 来料数量
        int number = iqcCheckHistory.getNumber().intValue();
        int category = sampleCase.getCategory();
        if (SampleCaseCategoryEnum.ALL_CHECK.getCategory().equals(category)) {
          return samplingStrategyServiceImpl.fullInspection(sampleCase, number);
        }
        if (SampleCaseCategoryEnum.SPECIFY_QUANTITY.getCategory().equals(category)) {
            return samplingStrategyServiceImpl.fixedSizeSampling(sampleCase, number);
        }
        if (SampleCaseCategoryEnum.PERCENTAGE.getCategory().equals(category)) {
            return samplingStrategyServiceImpl.percentageSampling(sampleCase, number);
        }
        if (SampleCaseCategoryEnum.GBT.getCategory().equals(category)) {
            Optional<GbtDetailDTO> gbtDetailOptional =
                    gbtDetailRepository.findByGbtIdAndInsolationLevelAndAqlAndDeleted(sampleCase.getGbt().getId(), sampleCase.getInsolationLevel(), sampleCase.getAql(), number,Constants.LONG_ZERO);
            if (gbtDetailOptional.isPresent()){
                iqcCheckHistoryDetail.setGbtDetail(gbtDetailOptional.get());
                return gbtDetailOptional.get().getNumber();
            }
            throw new ResponseException("error.nationalStandardSamplingIsNoteExist", "当前投产数未匹配到合适国标");
        }
        return snCount;
    }

    /**
     * 手动选择质检方案
     *
     * @param checkRuleId        质检方案ID
     * @param iqcCheckHistory    来料检验单
     * @param iqcCheckPreviewDTO 来料检验预览
     */
    private void selectCheckRule(Long checkRuleId, IqcCheckHistory iqcCheckHistory, IqcCheckPreviewDTO iqcCheckPreviewDTO) {
        // 匹配到多个质检单 选择其中一个
        PedigreeStepCheckRule pedigreeStepCheckRule = pedigreeStepCheckRuleRepository.findByIdAndDeleted(checkRuleId, Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(pedigreeStepCheckRule)) {
            throw new ResponseException("error.iqcCheckRuleNotExist", "质检方案不存在");
        }
        // 按照方案抽检的SN个数

        List<IqcCheckHistoryDetail> iqcCheckHistoryDetailList = Lists.newArrayList();
        List<PedigreeStepCheckItem> pedigreeStepCheckItemList = pedigreeStepCheckItemRepository.findByPedigreeStepCheckRuleIdAndDeleted(checkRuleId, Constants.LONG_ZERO);
        if (!CollectionUtils.isEmpty(pedigreeStepCheckItemList)) {
            pedigreeStepCheckItemList.forEach(pedigreeStepCheckItem -> {
                IqcCheckHistoryDetail iqcCheckHistoryDetail = new IqcCheckHistoryDetail();
                SampleCaseDTO sampleCase = Objects.nonNull(pedigreeStepCheckItem.getSampleCase()) ?
                        pedigreeStepCheckItem.getSampleCase():pedigreeStepCheckRule.getSampleCase();
                int sampleCaseNumber =  getSnCount(iqcCheckHistory, sampleCase,iqcCheckHistoryDetail);
                iqcCheckHistoryDetail.setCheckHistory(iqcCheckHistory)
                        .setCheckItem(pedigreeStepCheckItem.getCheckItem())
                        .setQualifiedRange(pedigreeStepCheckItem.getQualifiedRange())
                        .setSampleCase(sampleCase)
                        .setDefect(null)
                        .setCheckNumber(pedigreeStepCheckItem.getInspectNumberCase() == Constants.INT_ZERO ? sampleCaseNumber : Math.min(pedigreeStepCheckItem.getCustomizeInspectNumber(), sampleCaseNumber))
                        .setControl(pedigreeStepCheckItem.getControl())
                        .setCheckItemDefectList(defectCheckItemRepository.findByCheckItemId(pedigreeStepCheckItem.getCheckItem().getId()))
                        .setSn(null);
                if(pedigreeStepCheckItem.getInspectNumberCase() == Constants.INT_TWO){
                    iqcCheckHistoryDetail.setCheckNumber(Math.min(iqcCheckHistory.getNumber().intValue(), sampleCaseNumber));
                }
                iqcCheckHistoryDetailList.add(iqcCheckHistoryDetail);
            });
        }
        iqcCheckHistory.setPedigreeStepCheckRule(pedigreeStepCheckRule);
        iqcCheckPreviewDTO.setIqcCheckHistory(iqcCheckHistory)
                .setPedigreeStepCheckRuleList(Lists.newArrayList(pedigreeStepCheckRule))
                .setDetailList(iqcCheckHistoryDetailList);

    }

    /**
     * 自动获取质检方案
     *
     * @param attributeIdList    物料属性列表
     * @param iqcCheckHistory    来料检验单
     * @param iqcCheckPreviewDTO 来料检验预览
     */
    private void autoGetCheckRule(List<Long> attributeIdList, IqcCheckHistory iqcCheckHistory, IqcCheckPreviewDTO iqcCheckPreviewDTO) {
        List<IqcCheckHistoryDetail> iqcCheckHistoryDetailList = Lists.newArrayList();
        // 查询所有符合条件的质检方案
        List<PedigreeStepCheckRule> pedigreeStepCheckRuleList = pedigreeStepCheckRuleRepository.findByCategoryAndAttributeIdAndMaterialIdAndSupplierIdAndClientIdAndDeleted(Constants.INT_FIVE,
                attributeIdList,
                iqcCheckHistory.getMaterialId(),
                iqcCheckHistory.getSupplierId(),
                iqcCheckHistory.getClientId(),
                Constants.LONG_ZERO);
        if (!CollectionUtils.isEmpty(pedigreeStepCheckRuleList)) {
            //根据优先级排序 获取优先级最高的质检方案
            Optional<PedigreeStepCheckRule> pedigreeStepCheckRuleOptional = pedigreeStepCheckRuleList.stream()
                    .min(Comparator.comparingInt(p -> p.getPriorityElementConfig().getPriority()));
            // 使用优先级最高的质检方案
            PedigreeStepCheckRule firstPedigreeStepCheckRule = pedigreeStepCheckRuleOptional.get();
            // 只有一个的时候 直接选择
            iqcCheckHistory.setPedigreeStepCheckRule(firstPedigreeStepCheckRule);
            iqcCheckPreviewDTO.setPedigreeStepCheckRuleList(List.of(firstPedigreeStepCheckRule));
            List<PedigreeStepCheckItem> pedigreeStepCheckItemList = pedigreeStepCheckItemRepository.findByPedigreeStepCheckRuleIdAndDeleted(firstPedigreeStepCheckRule.getId(), Constants.LONG_ZERO);
            if (!CollectionUtils.isEmpty(pedigreeStepCheckItemList)) {
                pedigreeStepCheckItemList.forEach(pedigreeStepCheckItem -> {
                    SampleCaseDTO sampleCase = Objects.nonNull(pedigreeStepCheckItem.getSampleCase()) ?
                            pedigreeStepCheckItem.getSampleCase():firstPedigreeStepCheckRule.getSampleCase();
                    IqcCheckHistoryDetail iqcCheckHistoryDetail = new IqcCheckHistoryDetail();
                    int sampleCaseNumber =  getSnCount(iqcCheckHistory, sampleCase,iqcCheckHistoryDetail);
                    iqcCheckHistoryDetail.setCheckHistory(iqcCheckHistory)
                            .setCheckItem(pedigreeStepCheckItem.getCheckItem())
                            .setSampleCase(sampleCase)
                            .setQualifiedRange(pedigreeStepCheckItem.getQualifiedRange())
                            .setDefect(null)
                            .setCheckNumber(pedigreeStepCheckItem.getInspectNumberCase() == Constants.INT_ZERO ? sampleCaseNumber : Math.min(pedigreeStepCheckItem.getCustomizeInspectNumber(),sampleCaseNumber))
                            .setControl(pedigreeStepCheckItem.getControl())
                            .setCheckItemDefectList(defectCheckItemRepository.findByCheckItemId(pedigreeStepCheckItem.getCheckItem().getId()))
                            .setSn(null);
                    if(pedigreeStepCheckItem.getInspectNumberCase() == Constants.INT_TWO){
                        iqcCheckHistoryDetail.setCheckNumber(Math.min(iqcCheckHistory.getNumber().intValue(), sampleCaseNumber));
                    }
                    iqcCheckHistoryDetailList.add(iqcCheckHistoryDetail);
                });
            }
        } else {
            // 没有找到质检方案
            throw new ResponseException("error.iqcCheckRuleNotExist", "质检方案不存在");
        }
        iqcCheckPreviewDTO.setIqcCheckHistory(iqcCheckHistory)
                .setDetailList(iqcCheckHistoryDetailList);
    }

    /**
     * 获取属性列表
     *
     * @param materialId 物料id
     * @return java.util.List<java.lang.Long>  属性列表
     */
    private List<Long> getAttributeList(Long materialId) {
        if (Objects.isNull(materialId)) {
            return null;
        }
        List<Long> attributeIdList = null;
        MaterialDTO materialDTO;
        materialDTO = rbaseMaterialProxy.findByIdAndDeleted(materialId,Constants.LONG_ZERO);
        if (Objects.nonNull(materialDTO) && Objects.nonNull(materialDTO.getId())) {
            Set<MaterialAttributeDTO> materialAttributeDTOSet = materialDTO.getMaterialAttribute();
            if (!CollectionUtils.isEmpty(materialAttributeDTOSet)) {
                attributeIdList = materialAttributeDTOSet.stream().map(MaterialAttributeDTO::getId).toList();
                if (CollectionUtils.isEmpty(attributeIdList)) {
                    attributeIdList = null;
                }
            }
        }
        return attributeIdList;
    }

    /**
     * 来料检验处理
     *
     * @param iqcCheckDealDTO 来料检验处理参数
     */
    public void deal(IqcCheckDealDTO iqcCheckDealDTO) {
        // 来料检验单id
        Long id = iqcCheckDealDTO.getId();
        Optional<IqcCheckHistory> iqcCheckHistoryOptional = iqcCheckHistoryRepository.findByIdAndDeleted(id, Constants.LONG_ZERO);
        if (iqcCheckHistoryOptional.isEmpty()) {
            throw new ResponseException("error.iqcCheckHistoryNotExist", "来料检验不存在");
        }
        // 设置处理方式和处理人
        IqcCheckHistory iqcCheckHistory = iqcCheckHistoryOptional.get();
        if (Boolean.TRUE.equals(iqcCheckHistory.getResult())) {
            throw new ResponseException("error.iqcCheckHistoryDealError", "来料检验已合格");
        }
        if (Objects.nonNull(iqcCheckHistory.getDealWay()) && Constants.INT_ZERO != iqcCheckHistory.getDealWay()) {
            throw new ResponseException("error.iqcCheckHistoryDealError", "来料检验已处理");
        }
        iqcCheckHistory.setDealWay(iqcCheckDealDTO.getDealWay())
                .setDealerId(iqcCheckDealDTO.getDealerId());
        this.save(iqcCheckHistory);
        //如果处理方式是MRB评审则需要发送MRB评审请求
        if(iqcCheckHistory.getDealWay() == Constants.INT_FIVE){
            this.applicantMrb(id,iqcCheckDealDTO.getReason());
        }
    }

    /**
     * 待做的来料检验单
     *
     * @return java.util.List<net.airuima.rbase.domain.procedure.quality.IqcCheckHistory> 来料检验单
     */
    @Transactional(readOnly = true)
    public List<IqcCheckHistory> todo() {
        return iqcCheckHistoryRepository.findByStatusAndDeleted(Constants.INT_ZERO, Constants.LONG_ZERO);
    }

    /**
     *  发送MRB评审请求
     * @param iqcCheckHistoryId IQC检验历史ID
     */
    public void applicantMrb(Long iqcCheckHistoryId,String reason){
        IqcCheckHistory iqcCheckHistory = iqcCheckHistoryRepository.findByIdAndDeleted(iqcCheckHistoryId,Constants.LONG_ZERO).orElse(null);
        if(Objects.isNull(iqcCheckHistory) || iqcCheckHistory.getDealWay() != Constants.INT_FIVE){
            return;
        }
        List<IqcCheckHistoryDetail> iqcCheckHistoryDetailList = iqcCheckHistoryDetailRepository.findByCheckHistoryIdAndDeleted(iqcCheckHistoryId,Constants.LONG_ZERO);
        if(CollectionUtils.isEmpty(iqcCheckHistoryDetailList)){
            return;
        }
        MrbApplicantRequestDTO mrbApplicantRequestDTO = new MrbApplicantRequestDTO(iqcCheckHistory);
        mrbApplicantRequestDTO.setReason(reason);
        MrbApplicantRequestDTO.MrbApplicantRequestDetailDTO mrbApplicantRequestDetailDTO =  new MrbApplicantRequestDTO.MrbApplicantRequestDetailDTO();
        Map<String,List<IqcCheckHistoryDetail>> detailGroup = iqcCheckHistoryDetailList.stream().collect(Collectors.groupingBy(IqcCheckHistoryDetail::getSn));
        mrbApplicantRequestDetailDTO.setPopulation(iqcCheckHistory.getNumber().intValue())
                .setSample(detailGroup.size())
                .setUnqualifiedNumber(Constants.INT_ZERO)
                .setUnqualifiedDescription(iqcCheckHistory.getNote());
        detailGroup.forEach((sn,detailList)->{
            if(detailList.stream().anyMatch(detail->!detail.getResult())){
                mrbApplicantRequestDetailDTO.setUnqualifiedNumber(mrbApplicantRequestDetailDTO.getUnqualifiedNumber()+Constants.INT_ONE);
            }
        });
        List<MrbApplicantRequestDTO.MrbApplicantRequestDetailDTO.InspectInfo> inspectInfoList = new ArrayList<>();
        detailGroup.forEach((sn,checkHistoryDetails)->{
            MrbApplicantRequestDTO.MrbApplicantRequestDetailDTO.InspectInfo inspectInfo = new MrbApplicantRequestDTO.MrbApplicantRequestDetailDTO.InspectInfo();
            inspectInfo.setSn(sn);
            List<String> defects = new ArrayList<>();
            List<MrbApplicantRequestDTO.MrbApplicantRequestDetailDTO.InspectInfo.InspectItemInfo> inspectItemInfoList = new ArrayList<>();
            checkHistoryDetails.forEach(checkHistoryDetail -> {
                if(Objects.nonNull(checkHistoryDetail.getDefect())){
                    defects.add(checkHistoryDetail.getDefect().getName());
                }
                MrbApplicantRequestDTO.MrbApplicantRequestDetailDTO.InspectInfo.InspectItemInfo inspectItemInfo = new MrbApplicantRequestDTO.MrbApplicantRequestDetailDTO.InspectInfo.InspectItemInfo();
                inspectItemInfo.setCode(checkHistoryDetail.getCheckItem().getCode())
                        .setName(checkHistoryDetail.getCheckItem().getName())
                        .setCheckData(checkHistoryDetail.getCheckData())
                        .setResult(checkHistoryDetail.getResult())
                        .setQualifiedRange(checkHistoryDetail.getQualifiedRange());
                inspectItemInfoList.add(inspectItemInfo);
            });
            inspectInfo.setDefectList(defects).setInspectItemInfoList(inspectItemInfoList);
            inspectInfoList.add(inspectInfo);
        });
        mrbApplicantRequestDetailDTO.setInspectInfoList(inspectInfoList);
        mrbApplicantRequestDTO.setSourceInspectInfo(mrbApplicantRequestDetailDTO);
        rbaseMrbProxy.custom(mrbApplicantRequestDTO);
        iqcCheckHistory.setDealWay(Constants.INT_FIVE).setDeleted(Constants.LONG_ZERO);
        iqcCheckHistoryRepository.save(iqcCheckHistory);
    }

    /**
     * MRB评审完成时需要保存结果数据
     * @param serialNumber IQC单据号
     * @param mrbProcessResultDTOList MRB处理结果
     */
    public void mrbProcessIqcResult(String serialNumber, List<MrbProcessResultDTO> mrbProcessResultDTOList){
        iqcCheckHistoryRepository.findBySerialNumberAndDeleted(serialNumber,Constants.LONG_ZERO).ifPresent(history->{
            history.setMrbProcessResultInfoList(mrbProcessResultDTOList).setDeleted(Constants.LONG_ZERO);
            this.save(history);
        });
    }

    /**
     * MRB取消评审时需要更新IQC处理状态为待处理
     * @param serialNumber IQC单据号
     */
    public void mrbUpdateIqcStatusWhenCanceled(String serialNumber){
        iqcCheckHistoryRepository.findBySerialNumberAndDeleted(serialNumber,Constants.LONG_ZERO).ifPresent(history->{
            history.setDealWay(Constants.INT_ZERO).setDeleted(Constants.LONG_ZERO);
            this.save(history);
        });
    }
}
