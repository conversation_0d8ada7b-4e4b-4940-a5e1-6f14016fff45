package net.airuima.rbase.service.base.process;

import com.google.common.collect.Lists;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeStep;
import net.airuima.rbase.domain.base.pedigree.PedigreeWorkFlow;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.process.WorkFlowStep;
import net.airuima.rbase.dto.process.StepDTO;
import net.airuima.rbase.dto.process.WorkFlowDTO;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.dto.sync.SyncResultDTO;
import net.airuima.rbase.dto.sync.SyncWorkFlowDTO;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeReworkWorkFlowRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeWorkFlowRepository;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.repository.base.process.WorkFlowStepRepository;
import net.airuima.rbase.service.base.pedigree.PedigreeStepService;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rbase.web.rest.base.process.dto.WorkFlowCloneDTO;
import net.airuima.rbase.web.rest.base.process.dto.WorkFlowCreateDTO;
import net.airuima.service.CommonJpaService;
import net.airuima.util.HeaderUtil;
import net.airuima.util.MapperUtils;
import net.airuima.util.ResponseException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工艺流程框图Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkFlowService extends CommonJpaService<WorkFlow> {

    private final WorkFlowRepository workFlowRepository;

    private final WorkFlowStepRepository workFlowStepRepository;

    @Autowired
    private PedigreeWorkFlowRepository pedigreeWorkFlowRepository;

    @Autowired
    private PedigreeReworkWorkFlowRepository pedigreeReworkWorkFlowRepository;

    @Autowired
    private PedigreeRepository pedigreeRepository;

    @Autowired
    private StepRepository stepRepository;

    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;

    @Autowired
    private PedigreeStepService pedigreeStepService;

    @Autowired
    private PedigreeStepRepository pedigreeStepRepository;

    @Autowired
    private CommonService commonService;


    public WorkFlowService(WorkFlowRepository workFlowRepository, WorkFlowStepRepository workFlowStepRepository) {
        this.workFlowRepository = workFlowRepository;
        this.workFlowStepRepository = workFlowStepRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WorkFlow> find(Specification<WorkFlow> spec, Pageable pageable) {
        return workFlowRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<WorkFlow> find(Specification<WorkFlow> spec) {
        return workFlowRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WorkFlow> findAll(Pageable pageable) {
        return workFlowRepository.findAll(pageable);
    }


    /**
     * 新增流程框图，同时需要绑定工序
     *
     * @param workFlowDto 流程框图DTO
     * @return
     */
    public ResponseEntity<WorkFlow> saveInstance(WorkFlowDTO workFlowDto) throws URISyntaxException {
        WorkFlow workFlow = net.airuima.rbase.util.MapperUtils.map(workFlowDto, WorkFlow.class);
        //保存流程框图
        WorkFlow entity = this.save(workFlow);
        //绑定工序
        saveWorkFlowStep(new HashSet<>(workFlowDto.getStepDtoList()), entity);
        return ResponseEntity.created(new URI("/api/work-flows/" + entity.getId())).headers(HeaderUtil.createdAlert(StringUtils.uncapitalize(WorkFlow.class.getSimpleName()), entity.getId().toString())).body(entity);
    }

    /**
     * 修改流程框图，同时需要修改绑定的工序
     *
     * @param workFlowDto 流程框图DTO
     * @return
     */
    public ResponseEntity<WorkFlow> updateInstance(WorkFlowDTO workFlowDto) throws URISyntaxException {
        //若工序发生变化则需要删除工艺路线中不存在的工序配置
        List<WorkFlowStep> workFlowStepList = workFlowStepRepository.findByWorkFlowIdAndDeleted(workFlowDto.getId(), net.airuima.constant.Constants.LONG_ZERO);
        if(!CollectionUtils.isEmpty(workFlowStepList) && !CollectionUtils.isEmpty(workFlowDto.getStepDtoList())){
            List<Long> stepIds = workFlowDto.getStepDtoList().stream().map(StepDTO::getId).toList();
            List<WorkFlowStep> deletedWorkFlowStepList = workFlowStepList.stream().filter(workFlowStep -> !stepIds.contains(workFlowStep.getStep().getId())).toList();
            if(!CollectionUtils.isEmpty(deletedWorkFlowStepList)){
                List<Long> deleteStepIds = deletedWorkFlowStepList.stream().map(workFlowStep -> workFlowStep.getStep().getId()).toList();
                pedigreeStepRepository.logicDeleteByOnlyWorkFlowIdAndStepIdIn(workFlowDto.getId(),deleteStepIds);
            }
        }
        WorkFlow workFlow = MapperUtils.map(workFlowDto, WorkFlow.class);
        //保存流程框图
        workFlow.setDeleted(Constants.LONG_ZERO);
        WorkFlow entity = workFlowRepository.save(workFlow);
        //先物理删除之前的流程框图绑定的工序，然后重新绑定工序
        workFlowStepRepository.physicallyDeletedByWorkFlowId(workFlow.getId());
        //流程框图重新绑定工序
        this.saveWorkFlowStep(new HashSet<>(workFlowDto.getStepDtoList()), entity);
        return ResponseEntity.ok().headers(HeaderUtil.updatedAlert(StringUtils.uncapitalize(WorkFlow.class.getSimpleName()), entity.getId().toString())).body(entity);
    }

    /**
     * 流程框图绑定工序
     *
     * @param stepDtoList 工序DTOList
     * @param workFlow    流程框图
     */
    private void saveWorkFlowStep(Set<StepDTO> stepDtoList, WorkFlow workFlow) {
        stepDtoList.forEach(stepDto -> {
            Step step = net.airuima.rbase.util.MapperUtils.map(stepDto, Step.class);
            WorkFlowStep workFlowStep = workFlowStepRepository.findByWorkFlowIdAndStepIdAndDeleted(workFlow.getId(),step.getId(),Constants.LONG_ZERO).orElse(new WorkFlowStep());
            if(Objects.isNull(workFlowStep.getId())){
                workFlowStep.setWorkFlow(workFlow)
                        .setStep(step);
            }
            workFlowStep .setPreStepId(stepDto.getPreStepId())
                    .setAfterStepId(stepDto.getAfterStepId());
            workFlowStep.setDeleted(Constants.LONG_ZERO);
            workFlowStepRepository.save(workFlowStep);
        });
    }


    /**
     * 根据框图编码或者名称获取启用的框图列表
     *
     * @param text         框图编码或者名称
     * @param size         返回条数
     * @param isEnable     是否启用
     * @param categoryList 工艺路线类型
     * @return
     */
    public List<WorkFlow> findByCodeOrName(String text, Integer size, List<Integer> categoryList, Boolean isEnable) {
        Page<WorkFlow> page = null;
        if (CollectionUtils.isEmpty(categoryList)) {
            page = workFlowRepository.findByNameOrCode(StringUtils.isNotBlank(text)?text:null, Boolean.TRUE,PageRequest.of(Constants.INT_ZERO, size));
        } else {
            page = workFlowRepository.findByCodeOrNameAndCategoryList(StringUtils.isNotBlank(text)?text:null, categoryList, Boolean.TRUE,PageRequest.of(Constants.INT_ZERO, size));
        }
        return Optional.ofNullable(page).map(Slice::getContent).orElse(null);
    }

    /**
     * 通过产品谱系id和客户id获取工艺路线列表
     *
     * @param pedigreeId 产品谱系id
     * @param clientId 客户od
     * @return java.util.List< net.airuima.rbase.domain.base.process.WorkFlow> 工艺路线集合
     */
    @Transactional(readOnly = true)
    public Set<WorkFlow> findByPedigreeIdAndClientId(Long pedigreeId, Long clientId) {
        Set<WorkFlow> resultList = new HashSet<>();
        Pedigree pedigree = pedigreeRepository.getReferenceById(pedigreeId);
        // 得到工艺路线列表
        List<WorkFlow> workFlowList = commonService.findPedigreeWorkFlowAndClientId(pedigree, clientId,Boolean.FALSE);
        List<WorkFlow> reworkWorkFlowList = commonService.findPedigreeReworkWorkFlows(pedigree, clientId,Boolean.FALSE);
        // 合并结果集
        if(null!=workFlowList && workFlowList.size()>0) {
            resultList.addAll(workFlowList);
        }
        if(null!=reworkWorkFlowList && reworkWorkFlowList.size()>0) {
            resultList.addAll(reworkWorkFlowList);
        }
        return resultList;
    }

    /**
     * 新增工艺路线
     *
     * @param entity 新增工艺路线DTO
     * @return : net.airuima.rbase.domain.base.process.WorkFlow
     * <AUTHOR>
     * @date 2022/12/13
     **/
    public WorkFlow create(WorkFlowCreateDTO entity) {
        WorkFlow workFlow = new WorkFlow();
        //如果未手动输入编码则按照编码规则自动生成，如果手动输入则判断唯一性
        if (StringUtils.isBlank(entity.getCode())) {
            SerialNumberDTO serialNumberDto = new SerialNumberDTO(Constants.KEY_WORK_FLOW_CODE, null, null);
            workFlow.setCode(rbaseSerialNumberProxy.generate(serialNumberDto));
        } else {
            Optional<WorkFlow> workFlowOptional = workFlowRepository.findByCode(entity.getCode());
            if (workFlowOptional.isPresent()) {
                throw new ResponseException("error.CodeIsExist", "编码已存在");
            }
            workFlow.setCode(entity.getCode());
        }
        workFlow.setName(entity.getName()).setCategory(entity.getCategory()).setIsEnable(Boolean.TRUE);
        workFlowRepository.save(workFlow);
        return workFlow;
    }

    /**
     * 启用/禁用指定工艺路线
     *
     * @param workFlowId 工艺路线ID
     * @return : org.springframework.http.ResponseEntity<java.lang.Void>
     * <AUTHOR>
     * @date 2022/12/13
     **/
    public void enableByWorkFlowId(Long workFlowId) {
        WorkFlow workFlow = workFlowRepository.findByIdAndDeleted(workFlowId, Constants.LONG_ZERO).orElseThrow(()->new ResponseException("error.workFlowNotExist", "工艺流程框图不存在"));
        workFlow.setIsEnable(!workFlow.getIsEnable());
        workFlowRepository.save(workFlow);
    }

    /**
     * 工艺路线同步
     * @param syncWorkFlowList 同步工艺路线数据
     * <AUTHOR>
     * @date  2023/3/15
     */
    public List<SyncResultDTO> syncWorkFlow(List<SyncWorkFlowDTO> syncWorkFlowList) {
        List<SyncResultDTO> syncResultDTOList = Lists.newArrayList();

        if (ValidateUtils.isValid(syncWorkFlowList)){
            syncWorkFlowList.forEach(syncWorkFlow -> {
                SyncResultDTO syncResultDTO = new SyncResultDTO(syncWorkFlow.getId(),Constants.INT_ONE,"");
                //新增工艺路线
                if (syncWorkFlow.getOperate() == Constants.INT_ZERO){
                    SyncResultDTO syncResultDto = addWorkFlow(syncWorkFlow);
                    if (!ObjectUtils.isEmpty(syncResultDto)){
                        syncResultDTOList.add(syncResultDto);
                        return;
                    }
                }
                Optional<WorkFlow> workFlowOptional = workFlowRepository.findByCode(syncWorkFlow.getCode());
                //修改工艺路线
                if (syncWorkFlow.getOperate() == Constants.INT_ONE){
                    if (!workFlowOptional.isPresent()){
                        syncResultDTOList.add(syncResultDTO.setStatus(Constants.INT_TWO).setMessage("工艺路线不存在"));
                        return;
                    }
                    //删除工艺路线工序
                    workFlowStepRepository.physicallyDeletedByWorkFlowId(workFlowOptional.get().getId());
                    //添加
                    SyncResultDTO syncResultDto = addWorkFlow(syncWorkFlow);
                    if (!ObjectUtils.isEmpty(syncResultDto)){
                        syncResultDTOList.add(syncResultDto);
                        return;
                    }
                }
                //删除工艺路线
                if (syncWorkFlow.getOperate() == Constants.INT_TWO){
                    if (!workFlowOptional.isPresent()){
                        syncResultDTOList.add(syncResultDTO.setStatus(Constants.INT_TWO).setMessage("工艺路线不存在"));
                        return;
                    }
                    Pedigree pedigree = pedigreeRepository.findByCodeAndDeleted(syncWorkFlow.getPedigreeCode(), Constants.LONG_ZERO).orElse(null);
                    deletedWorkFlow(workFlowOptional.get().getId(),pedigree);
                }
                syncResultDTOList.add(syncResultDTO);
            });
        }
        return syncResultDTOList;
    }

    /**
     * 删除工艺路线相关的基础信息
     * @param workFlowId
     * <AUTHOR>
     * @date  2023/3/15
     * @return void
     */
    private void deletedWorkFlow(Long workFlowId,Pedigree pedigree){
        //删除工艺路线对应的产品谱系工艺路线
        if (ObjectUtils.isEmpty(pedigree)){
            pedigreeWorkFlowRepository.deleteByPedigreeIdAndWorkFlowIdAndClientId(null,workFlowId,null );
        }else {
            pedigreeWorkFlowRepository.deleteByPedigreeIdAndWorkFlowIdAndClientId(pedigree.getId(),workFlowId, null);
        }
        //删除工艺路线工序
        workFlowStepRepository.physicallyDeletedByWorkFlowId(workFlowId);
        //删除工艺路线
        workFlowRepository.logicDelete(workFlowId);
    }

    /**
     * 同步新增工艺路线
      * @param syncWorkFlow 同步工艺路线参数
     * <AUTHOR>
     * @date  2023/3/15
     * @return net.airuima.rbase.dto.sync.SyncResultDTO
     */
    private SyncResultDTO addWorkFlow(SyncWorkFlowDTO syncWorkFlow){
        Map<String,Step> stepMap = new HashMap<>();
        //验证存在工艺路线工序配置中工序是否都存在
        if (ValidateUtils.isValid(syncWorkFlow.getWorkFlowStepInfoList())){
            List<String> stepCodes = syncWorkFlow.getWorkFlowStepInfoList().stream().filter(syncStep -> !ObjectUtils.isEmpty(syncStep.getStepCode())).map(SyncWorkFlowDTO.WorkFlowStepInfo::getStepCode).collect(Collectors.toList());
            List<Step> stepList = stepRepository.findByCodeInAndDeleted(stepCodes, Constants.LONG_ZERO);
            if (!ValidateUtils.isValid (stepList) || (ValidateUtils.isValid(stepList) && stepCodes.size() != stepList.size())){
                return new SyncResultDTO(syncWorkFlow.getId(), Constants.INT_TWO,"工序不存在");
            }else {
                stepCodes.forEach(code -> {
                    stepMap.put(code,stepList.stream().filter(step -> code.equals(step.getCode())).findFirst().get());
                });
            }
        }
        //保存工艺路线
        WorkFlow workFlow = workFlowRepository.findByCode(syncWorkFlow.getCode()).orElse(new WorkFlow());
        workFlow.setCode(syncWorkFlow.getCode()).setName(syncWorkFlow.getName())
                .setCategory(syncWorkFlow.getCategory()).setIsEnable(Constants.TRUE);
        WorkFlow finalWorkFlow = workFlowRepository.save(workFlow);
        //保存产品谱系工艺路线
        if (ValidateUtils.isValid(syncWorkFlow.getPedigreeCode())){
            Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByCodeAndDeleted(syncWorkFlow.getPedigreeCode(), Constants.LONG_ZERO);
            pedigreeOptional.ifPresent(pedigree -> {
                PedigreeWorkFlow pedigreeWorkFlow = pedigreeWorkFlowRepository.findByPedigreeIdAndWorkFlowIdAndClientIdAndDeleted(pedigree.getId(), workFlow.getId(),null, Constants.LONG_ZERO).orElse(new PedigreeWorkFlow());
                pedigreeWorkFlow.setWorkFlow(finalWorkFlow).setPedigree(pedigree).setIsEnable(Constants.TRUE);
                pedigreeWorkFlowRepository.save(pedigreeWorkFlow);
            });
        }
        //保存工艺路线工序
        if (ValidateUtils.isValid(stepMap)){
            List<WorkFlowStep> workFlowSteps = Lists.newArrayList();
            for (int index = Constants.INT_ZERO; index < syncWorkFlow.getWorkFlowStepInfoList().size(); index++) {
                SyncWorkFlowDTO.WorkFlowStepInfo workFlowStepInfo = syncWorkFlow.getWorkFlowStepInfoList().get(index);
                WorkFlowStep workFlowStep = new WorkFlowStep();
                workFlowStep.setWorkFlow(workFlow).setStep(stepMap.get(workFlowStepInfo.getStepCode()));
                //添加前置工序
                if (index != Constants.INT_ZERO){
                    SyncWorkFlowDTO.WorkFlowStepInfo preWorkFlowStepInfo = syncWorkFlow.getWorkFlowStepInfoList().get(index-Constants.INT_ONE);
                    workFlowStep.setPreStepId(String.valueOf(stepMap.get(preWorkFlowStepInfo.getStepCode()).getId()));
                }
                //添加后置工序
                if (index + Constants.INT_ONE < syncWorkFlow.getWorkFlowStepInfoList().size()){
                    SyncWorkFlowDTO.WorkFlowStepInfo afterWorkFlowStepInfo = syncWorkFlow.getWorkFlowStepInfoList().get(index+Constants.INT_ONE);
                    workFlowStep.setAfterStepId(String.valueOf(stepMap.get(afterWorkFlowStepInfo.getStepCode()).getId()));
                }
                workFlowSteps.add(workFlowStep);
            }
           workFlowStepRepository.saveAll(workFlowSteps);
        }
        return null;
    }

    /**
     * 工艺路线克隆
     *
     * @param workFlowCloneDto 工艺路线克隆参数
     * @return net.airuima.rbase.domain.base.process.WorkFlow 工艺路线
     */
    public WorkFlow cloneWorkFlow(WorkFlowCloneDTO workFlowCloneDto)  {
        Long workFlowId = workFlowCloneDto.getWorkFlowId();
        Optional<WorkFlow> workFlowOptional = workFlowRepository.findByCodeAndDeleted(workFlowCloneDto.getCode(), Constants.LONG_ZERO);
        if(workFlowOptional.isPresent()){
            throw new ResponseException("error.WorkFlowCodeIsExists", "工艺路线编码已存在");
        }
        WorkFlow workFlow = new WorkFlow();
        BeanUtils.copyProperties(workFlowCloneDto,workFlow);
        workFlow.setIsEnable(Boolean.TRUE);
        //保存工艺路线
        WorkFlow cloneWorkFlow = workFlowRepository.save(workFlow);
        //克隆绑定工序
        List<WorkFlowStep> workFlowStepList = workFlowStepRepository.findByWorkFlowIdAndDeleted(workFlowId, Constants.LONG_ZERO);
        if(!CollectionUtils.isEmpty(workFlowStepList)){
            List<WorkFlowStep> cloneWorkFlowSteps = Lists.newLinkedList();
            workFlowStepList.forEach(i -> {
                WorkFlowStep workFlowStep = new WorkFlowStep();
                BeanUtils.copyProperties(i, workFlowStep);
                workFlowStep.setWorkFlow(cloneWorkFlow).setId(null);
                cloneWorkFlowSteps.add(workFlowStep);
            });
            workFlowStepRepository.saveAll(cloneWorkFlowSteps);
        }
        // 克隆工序配置信息
        List<PedigreeStep> stepConfigList = pedigreeStepRepository.findByClientIdAndPedigreeIdAndWorkFlowIdAndEnableAndDeleted(null,null,workFlowId,Boolean.TRUE,Constants.LONG_ZERO);
        List<PedigreeStep> cloneStepConfigList = Lists.newLinkedList();
        if(!CollectionUtils.isEmpty(stepConfigList)){
            stepConfigList.forEach(s -> {
                PedigreeStep stepConfig = new PedigreeStep();;
                BeanUtils.copyProperties(s, stepConfig);
                stepConfig.setWorkFlow(cloneWorkFlow).setId(null);
                cloneStepConfigList.add(stepConfig);
            });
            pedigreeStepRepository.saveAll(cloneStepConfigList);
        }
        return cloneWorkFlow;
    }

    /**
     * 通过编码和是否启用查询对应工艺路线
     * @param code 编码
     * @param isEnable 是否启用
     * @return net.airuima.rbase.domain.base.process.WorkFlow 工艺路线
     */
    @Transactional(readOnly = true)
    public WorkFlow findByCode(String code, Boolean isEnable) {
        WorkFlow workFlow = workFlowRepository.findByCodeAndDeleted(code, Constants.LONG_ZERO).orElse(null);
        if(Objects.nonNull(workFlow) && Objects.nonNull(isEnable)){
            return workFlow.getIsEnable() == isEnable ? workFlow : null;
        }
        return workFlow;
    }


}
