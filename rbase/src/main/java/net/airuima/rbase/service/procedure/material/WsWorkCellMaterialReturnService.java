package net.airuima.rbase.service.procedure.material;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import com.google.common.collect.Lists;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.WipLedgerOperationEnum;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.material.WsMaterialBatch;
import net.airuima.rbase.domain.procedure.material.WsWorkCellMaterialBatch;
import net.airuima.rbase.domain.procedure.material.WsWorkCellMaterialReturn;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.material.MaterialBatchAbatementDTO;
import net.airuima.rbase.dto.material.RollBackMaterialDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.material.WsMaterialBatchRepository;
import net.airuima.rbase.repository.procedure.material.WsWorkCellMaterialBatchRepository;
import net.airuima.rbase.repository.procedure.material.WsWorkCellMaterialReturnRepository;
import net.airuima.rbase.service.wip.PIWipLedgerService;
import net.airuima.service.CommonJpaService;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.rbase.util.ValidateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.klock.annotation.Klock;
import org.springframework.boot.autoconfigure.klock.model.LockTimeoutStrategy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/3/28
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WsWorkCellMaterialReturnService extends CommonJpaService<WsWorkCellMaterialReturn> {
    private static final String WS_WORK_CELL_MATERIAL_RETURN_ENTITY_GRAPH = "WsWorkCellMaterialReturnEntityGraph";
    @Autowired
    private WsWorkCellMaterialReturnRepository wsWorkCellMaterialReturnRepository;

    @Autowired
    private WsWorkCellMaterialBatchRepository wsWorkCellMaterialBatchRepository;

    @Autowired
    private WsMaterialBatchRepository wsMaterialBatchRepository;

    @Autowired
    private WsMaterialReturnService wsMaterialReturnService;

    @Autowired
    private WsCheckMaterialDetailService wsCheckMaterialDetailService;

    @Autowired
    private WorkSheetRepository workSheetRepository;

    @Autowired
    private PIWipLedgerService wipLedgerServices;

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WsWorkCellMaterialReturn> find(Specification<WsWorkCellMaterialReturn> spec, Pageable pageable) {
        return wsWorkCellMaterialReturnRepository.findAll(spec,pageable,new NamedEntityGraph(WS_WORK_CELL_MATERIAL_RETURN_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<WsWorkCellMaterialReturn> find(Specification<WsWorkCellMaterialReturn> spec) {
        return wsWorkCellMaterialReturnRepository.findAll(spec,new NamedEntityGraph(WS_WORK_CELL_MATERIAL_RETURN_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WsWorkCellMaterialReturn> findAll(Pageable pageable) {
        return wsWorkCellMaterialReturnRepository.findAll(pageable,new NamedEntityGraph(WS_WORK_CELL_MATERIAL_RETURN_ENTITY_GRAPH));
    }

    /**
     * 保存工单工位退料信息
     * @param rollBackMaterialDto 工单工位退料信息DTO
     * <AUTHOR>
     * @date  2022/3/28
     * @return BaseDTO
     */
    @Transactional(rollbackFor = Exception.class)
    @Klock(
            keys = {"#rollBackMaterialDto.workSheetId", "#rollBackMaterialDto.workCellId","#rollBackMaterialDto.operatorId"},
            waitTime = 60,
            leaseTime = 60,
            lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST)
    public BaseDTO saveWsWorkCellMaterialReturn(RollBackMaterialDTO rollBackMaterialDto){

        List<MaterialBatchAbatementDTO> materialBatchAbatementDtoList = Lists.newArrayList();
        //验证回退工位物料数据
        BaseDTO baseDto = wsMaterialReturnService.validateMaterialReturn(rollBackMaterialDto, materialBatchAbatementDtoList);

        if (Constants.KO.equals(baseDto.getStatus())){
            return baseDto;
        }

        if (ValidateUtils.isValid(materialBatchAbatementDtoList)){
            materialBatchAbatementDtoList.forEach(materialBatchAbatementDto -> {
                WsWorkCellMaterialBatch wsWorkCellMaterialBatch = materialBatchAbatementDto.getWsWorkCellMaterialBatch();
                //修改工位库存数量
                wsWorkCellMaterialBatch.setLeftNumber(NumberUtils.subtract(wsWorkCellMaterialBatch.getLeftNumber(),materialBatchAbatementDto.getNumber()).doubleValue());
                wsWorkCellMaterialBatchRepository.save(wsWorkCellMaterialBatch);

                //将工位上退物料的按工单库存数量从小到大的退回到工单上面去
                List<WsMaterialBatch> wsMaterialBatchList = wsMaterialBatchRepository.findByWorkSheetIdAndMaterialIdAndBatchAndDeletedOrderByLeftNumberAsc(wsWorkCellMaterialBatch.getWorkSheet().getId(), wsWorkCellMaterialBatch.getMaterialId(), wsWorkCellMaterialBatch.getBatch(), Constants.LONG_ZERO);
                if (!ValidateUtils.isValid(wsMaterialBatchList)){
                    //不核物料批次
                    wsMaterialBatchList = wsMaterialBatchRepository.findByWorkSheetIdAndMaterialIdAndDeletedOrderByLeftNumberAsc(wsWorkCellMaterialBatch.getWorkSheet().getId(), wsWorkCellMaterialBatch.getMaterialId(), Constants.LONG_ZERO);
                }
                if (ValidateUtils.isValid(wsMaterialBatchList)){
                    Optional<WorkSheet> workSheetOptional = workSheetRepository.findByIdAndDeleted(rollBackMaterialDto.getWorkSheetId(), Constants.LONG_ZERO);
                    for (WsMaterialBatch wsMaterialBatch : wsMaterialBatchList) {
                        // 处理线边仓工单库存、新增台账
                        wipLedgerServices.processWarehouseAndSaveLedger(workSheetOptional.get().getWorkLine(),wsMaterialBatch,materialBatchAbatementDto.getNumber(), WipLedgerOperationEnum.WIP_LEDGER_OPERATION_RETURN.getCategory(),rollBackMaterialDto.getOperatorId());
                        if (wsMaterialBatch.getNumber() >= NumberUtils.add(wsMaterialBatch.getLeftNumber(),materialBatchAbatementDto.getNumber()).doubleValue()){
                            wsMaterialBatchRepository.save(wsMaterialBatch.setLeftNumber(NumberUtils.add(wsMaterialBatch.getLeftNumber(),materialBatchAbatementDto.getNumber()).doubleValue()));
                            break;
                        }else {
                            //工单领料总数 < 需要退的数量 + 剩余数量
                            // 剩余回退数量 = 工位回退数量-（工单领料数-剩余数）
                            materialBatchAbatementDto.setNumber(NumberUtils.subtract(materialBatchAbatementDto.getNumber(),NumberUtils.subtract(wsMaterialBatch.getNumber(),wsMaterialBatch.getLeftNumber()).doubleValue()).doubleValue());
                            wsMaterialBatchRepository.save(wsMaterialBatch.setLeftNumber(wsMaterialBatch.getNumber()));
                        }
                    }
                }
                //添加工单退料记录
                WsWorkCellMaterialReturn wsWorkCellMaterialReturn = new WsWorkCellMaterialReturn(wsWorkCellMaterialBatch.getWorkSheet(),wsWorkCellMaterialBatch.getWorkCell(),wsWorkCellMaterialBatch.getMaterialId(), rollBackMaterialDto.getOperatorId(), wsWorkCellMaterialBatch.getBatch(),materialBatchAbatementDto.getNumber());
                wsWorkCellMaterialReturnRepository.save(wsWorkCellMaterialReturn);
            });
        }
        return new BaseDTO(Constants.OK,"saveWsWorkCellMaterialReturnSuccess");
    }
}
