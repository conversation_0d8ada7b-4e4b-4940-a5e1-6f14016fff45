package net.airuima.rbase.service.procedure.batch.api;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.service.procedure.batch.dto.ContainerDetailReplaceDTO;
import net.airuima.rbase.web.rest.procedure.batch.dto.RollBackDTO;
import net.airuima.web.rest.errors.BadRequestAlertException;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@FuncDefault
public interface IRollbackStepService {

    /**
     * 通过批量工作详情ID删除记录
     *
     * @param rollBackDto 工序回退参数
     * @return net.airuima.rbase.service.procedure.batch.dto.ContainerDetailReplaceDTO  被占用的容器详情
     **/
    default ContainerDetailReplaceDTO deleteBatchWorkDetailById(RollBackDTO rollBackDto) {
        return new ContainerDetailReplaceDTO(Constants.OK, StringUtils.EMPTY);
    }

    /**
     * 通过批量详情进行物料库存回退
     * @param batchWorkDetail 批量工序生产详情
     */
    default void returnStepMaterial(BatchWorkDetail batchWorkDetail){

    }

    /**
     * 根据容器详情ID删除数据
     *
     * @param rollBackDto 容器回退参数
     * @return net.airuima.rbase.service.procedure.batch.dto.ContainerDetailReplaceDTO  被占用的容器详情
     * <AUTHOR>
     * @date 2021-08-27
     **/
    default ContainerDetailReplaceDTO deleteContainerDetailById(RollBackDTO rollBackDto){return new ContainerDetailReplaceDTO(Constants.OK,StringUtils.EMPTY);}

    /**
     * 回退容情详情物料库存
     * @param containerDetail 容器生产详情
     */
    default void returnContainerMaterial(ContainerDetail containerDetail){

    }

    /**
     * 回退sn详情
     * @param id        sn详情主键ID
     * @param note      备注
     * @param staffId   操作员工主键ID
     * <AUTHOR>
     * @date  2022/11/11
     */
    @FuncInterceptor("Single")
    default void deleteSnWorkDetailById(Long id, String note, Long staffId) throws BadRequestAlertException {

    }

    /**
     * 通过 容器详情id 或者工序批量工作详情id 验证是否存在上一道容器被占用
     *
     * @param id 容器详情id 或者工序批量工作详情id
     * @return net.airuima.rbase.service.procedure.batch.dto.ContainerDetailReplaceDTO  被占用的容器详情
     */
    @FuncInterceptor("Container")
    default ContainerDetailReplaceDTO checkContainerOccupation(Long id){
        return new ContainerDetailReplaceDTO(Constants.OK, "CheckSucceed");
    }

    /**
     *  回退修改sn状态
     * @param snWorkDetail sn详情
     * @param deleteByBatchDetail  是否为删除容器详情或者批量详情处删除SN详情=>批量详情删除时无需根据单个SN去回退工单合格数
     * @param singleSnOnlineRepair 是否单支在线反修
     */
    default void updateSnWorkStatus(SnWorkDetail snWorkDetail, boolean deleteByBatchDetail, boolean singleSnOnlineRepair) {

    }

    /**
     * 回退前置工序容器详情的流转数量
     *
     * @param deleteContainerDetail      当前待删除的容器详情
     * @param reverseContainerDetailList 需要回退的前置工序容器详情
     * @param deletedSnWorkDetailList    待删除的容器详情对应的SN工作详情列表
     * @return void
     * <AUTHOR>
     * @date 2021-08-27
     **/
    default void reverseContainerDetail(ContainerDetail deleteContainerDetail, List<ContainerDetail> reverseContainerDetailList, List<SnWorkDetail> deletedSnWorkDetailList) {

    }

    /**
     * 外协工序 以及外协前置工序禁止回退
     *
     * @param currWsStep 工序快照
     */
    @FuncInterceptor("StepOem")
    default void validStepOemRollbackStep(WsStep currWsStep) {

    }
}
