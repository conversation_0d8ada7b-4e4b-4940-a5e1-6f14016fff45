package net.airuima.rbase.service.base.process;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import jakarta.servlet.http.HttpServletRequest;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.process.WorkFlowStep;
import net.airuima.dto.ExportDTO;
import net.airuima.rbase.dto.process.StepDTO;
import net.airuima.rbase.dto.process.WorkFlowDTO;
import net.airuima.query.QueryCondition;
import net.airuima.query.QueryConditionParser;
import net.airuima.query.SearchFilter;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.repository.base.process.WorkFlowStepRepository;
import net.airuima.service.CommonJpaService;
import net.airuima.rbase.util.MapperUtils;
import net.airuima.util.ResponseException;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rbase.web.rest.base.process.dto.WorkFlowStepImportDTO;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 流程框图工序Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkFlowStepService extends CommonJpaService<WorkFlowStep> {

    private final StepRepository stepRepository;
    private final WorkFlowStepRepository workFlowStepRepository;
    private final WorkFlowRepository workFlowRepository;

    public WorkFlowStepService(WorkFlowStepRepository workFlowStepRepository, WorkFlowRepository workFlowRepository, StepRepository stepRepository) {
        this.workFlowStepRepository = workFlowStepRepository;
        this.workFlowRepository = workFlowRepository;
        this.stepRepository = stepRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WorkFlowStep> find(Specification<WorkFlowStep> spec, Pageable pageable) {
        return workFlowStepRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<WorkFlowStep> find(Specification<WorkFlowStep> spec) {
        return workFlowStepRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WorkFlowStep> findAll(Pageable pageable) {
        return workFlowStepRepository.findAll(pageable);
    }


    /**
     * 根据流程框图id查询流程框图工序
     *
     * @param workFlowId 流程框图id
     * @return 流程框图DTO
     */
    @Transactional(readOnly = true)
    public WorkFlowDTO findByWorkFlowId(Long workFlowId) {
        WorkFlow workFlow = workFlowRepository.findByIdAndDeleted(workFlowId, Constants.LONG_ZERO).orElseGet(WorkFlow::new);
        WorkFlowDTO workFlowDto = MapperUtils.map(workFlow, WorkFlowDTO.class);
        List<StepDTO> stepDtoList = Lists.newArrayList();
        List<WorkFlowStep> workFlowSteps = workFlowStepRepository.findStepByWorkFlowIdAndDeleted(workFlowId, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(workFlowSteps)) {
            workFlowSteps.forEach(workFlowStep -> {
                StepDTO stepDto = MapperUtils.map(workFlowStep.getStep(), StepDTO.class);
                stepDto.setAfterStepId(workFlowStep.getAfterStepId());
                stepDto.setPreStepId(workFlowStep.getPreStepId());
                stepDto.setInputRate(Constants.INT_ONE);
                stepDtoList.add(stepDto);
            });
        }
        workFlowDto.setStepDtoList(stepDtoList);
        return workFlowDto;
    }

    /**
     * 验证数据的合法性
     *
     * @param workFlowId                工艺路线id
     * @param excelFile                 导入excel
     * @param workFlowStepImportDtoList 导入excel
     * @param workFlowList              工艺路线列表
     * @return net.airuima.rbase.dto.base.BaseDTO
     * <AUTHOR>
     * @date 2021/12/21
     */
    public void validateData(Long workFlowId, MultipartFile excelFile, List<WorkFlowStepImportDTO> workFlowStepImportDtoList, List<WorkFlow> workFlowList) {
        //验证excel合法性，并获取到excel中的信息
        validateExcel(excelFile, workFlowStepImportDtoList);
        //根据工艺路线id是否存在判断是单个还是批量导入
        if (null != workFlowId) {
            //判断工艺路线中工序是否存在重复工序
            if (workFlowStepImportDtoList.size() != workFlowStepImportDtoList.stream().map(WorkFlowStepImportDTO::getStepCode).toList().stream().distinct().count()) {
                throw new ResponseException("error.repeatStep", "工艺路线中存在重复的工序");
            }
            //判断工艺路线是否存在
            Optional<WorkFlow> workFlowOptional = workFlowRepository.findByIdAndDeleted(workFlowId, Constants.LONG_ZERO);
            if (workFlowOptional.isEmpty()) {
                throw new ResponseException("error.workFlowNotExist", "工艺路线在系统中不存在");
            } else {
                if (!workFlowOptional.get().getIsEnable()) {
                    throw new ResponseException("error.workFlowDisabled", "工艺路线已禁用");
                }
                workFlowList.add(workFlowOptional.get());
            }
        } else {
            //根据工艺路线进行分组（批量导入）
            Map<String, List<WorkFlowStepImportDTO>> mapWorkFlowStepImportDto = workFlowStepImportDtoList.stream().collect(Collectors.groupingBy(WorkFlowStepImportDTO::getWorkFlowCode));
            List<String> workFlowCodeList = new ArrayList<>(mapWorkFlowStepImportDto.keySet());
            workFlowList.addAll(workFlowRepository.findByCodeInAndDeleted(workFlowCodeList, Constants.LONG_ZERO));
            //检验所有的工艺路线是否存在
            if ((!ValidateUtils.isValid(workFlowList)) || (ValidateUtils.isValid(workFlowCodeList) && workFlowList.size() != workFlowCodeList.size())) {
                throw new ResponseException("error.workFlowNotExist", "工艺路线在系统中不存在");
            }
            if (workFlowList.stream().anyMatch(workFlow -> !workFlow.getIsEnable())) {
                throw new ResponseException("error.workFlowDisabled", "工艺路线已禁用");
            }
            for (List<WorkFlowStepImportDTO> workFlowStepImportDtos : mapWorkFlowStepImportDto.values()) {
                //判断Excel中的工序是否存在重复工序
                if (workFlowStepImportDtos.size() != workFlowStepImportDtos.stream().map(WorkFlowStepImportDTO::getStepCode).toList().stream().distinct().count()) {
                    throw new ResponseException("error.repeatStep", "工艺路线中存在重复的工序");
                }
            }
        }
    }


    /**
     * 验证excel合法性，并获取excel中的信息
     *
     * @param excelFile                 传输的excel文件
     * @param workFlowStepImportDtoList 获取excel文件中对应的dto
     * @return ResponseEntity<Void>
     * <AUTHOR>
     * @date 2021/12/15
     */
    private void validateExcel(MultipartFile excelFile, List<WorkFlowStepImportDTO> workFlowStepImportDtoList) {
        //判断文件是不是空
        if (excelFile.isEmpty()) {
            throw new ResponseException("error.pleaseUploadTheFile", "文件为空");
        }
        //获得上传的excel文件名
        String fileName = excelFile.getOriginalFilename();
        //获取上传的excel文件名后缀
        String fileSuffix = fileName.substring(fileName.lastIndexOf(Constants.STR_POINT));
        //判断是不是excel文件
        if (!Constants.EXCEL_XLSX.equals(fileSuffix) && !Constants.EXCEL_XLS.equals(fileSuffix)) {
            throw new ResponseException("error.isNotExcelFile", "导入的文件不是Excel");
        }
        //上面验证通过，那么就将文件映射到集合Dto
        ImportParams params = new ImportParams();
        ExcelImportResult<WorkFlowStepImportDTO> excelImportResult = null;
        try{
            excelImportResult = ExcelImportUtil.importExcelMore(excelFile.getInputStream(), WorkFlowStepImportDTO.class, params);
        }catch (Exception e){
            throw new ResponseException("error.importFailed", e.getMessage());
        }
        //去掉excel为空项次
        if (ValidateUtils.isValid(excelImportResult.getList())) {
            workFlowStepImportDtoList.addAll(excelImportResult.getList().stream().filter(WorkFlowStepImportDTO::isExist).toList());
        }
        //判断映射完成的集合有没有数据
        if (!ValidateUtils.isValid(workFlowStepImportDtoList)) {
            throw new ResponseException("error.excelIsEmpty", "Excel内容为空");
        }
    }

    /**
     * 导入excel表数据与工艺路线自动进行组合 （单个导入）
     *
     * @param workFlowId 工艺路线的id
     * @param excelFile  excel文件
     * <AUTHOR>
     */
    public void saveWorkFlowStep(Long workFlowId, MultipartFile excelFile){

        List<WorkFlowStepImportDTO> workFlowStepImportDtoList = new ArrayList<>();
        List<WorkFlow> workFlowList = new ArrayList<>();
        //验证数据的合法性
       this.validateData(workFlowId, excelFile, workFlowStepImportDtoList, workFlowList);
        LinkedList<WorkFlowStep> workFlowStepList = new LinkedList<>();
        StringBuilder errorMessage = new StringBuilder();
        //验证工序
        validateStep(errorMessage, workFlowList.get(Constants.INT_ZERO), workFlowStepImportDtoList, workFlowStepList);
        //工艺路线工序的逻辑删除与保存
        deletedAndSaveWorkFlowStep(errorMessage.toString(), workFlowStepList, workFlowList);
    }

    /**
     * 导入excel表数据与工艺路线自动进行组合 （批量导入）
     *
     * @param excelFile excel文件
     * <AUTHOR>
     */
    public void saveWorkFlowSteps(MultipartFile excelFile) throws Exception {
        List<WorkFlowStepImportDTO> workFlowStepImportDtoList = new ArrayList<>();
        List<WorkFlow> workFlowList = new ArrayList<>();
        //验证数据的合法性
      this.validateData(null, excelFile, workFlowStepImportDtoList, workFlowList);

        LinkedList<WorkFlowStep> workFlowStepList = new LinkedList<>();
        StringBuilder errorMessage = new StringBuilder();

        //根据工艺路线进行分组（批量导入）
        Map<String, List<WorkFlowStepImportDTO>> mapWorkFlowStepImportDto = workFlowStepImportDtoList.stream().collect(Collectors.groupingBy(WorkFlowStepImportDTO::getWorkFlowCode));
        //根据工艺路线分批次经行，工艺路线工序流程追加数据
        mapWorkFlowStepImportDto.forEach((workFlowCode, workFlowStepImportDtos) -> {
            WorkFlow workFlow = workFlowList.stream().filter(newWorkFlow -> workFlowCode.equals(newWorkFlow.getCode())).findFirst().orElseGet(null);
            //验证工序
            validateStep(errorMessage, workFlow, workFlowStepImportDtos, workFlowStepList);
        });
        //工艺路线工序的逻辑删除与保存
       deletedAndSaveWorkFlowStep(errorMessage.toString(), workFlowStepList, workFlowList);
    }

    /**
     * 收集的工艺路线工序列表
     *
     * @param errorMessage              异常信息
     * @param workFlow                  工艺路线
     * @param workFlowStepImportDtoList excel信息
     * @param workFlowStepList          待收集的工艺路线工序列表
     * @return void
     * <AUTHOR>
     * @date 2021/12/15
     */
    private void validateStep(StringBuilder errorMessage, WorkFlow workFlow, List<WorkFlowStepImportDTO> workFlowStepImportDtoList, LinkedList<WorkFlowStep> workFlowStepList) {
        workFlowStepImportDtoList.forEach(WorkFlowStep -> {
            //有序添加工序
            validateStepAndSortAdd(workFlow, WorkFlowStep, workFlowStepList);
        });
    }

    /**
     * 验证导入的工序编码是否合规及组装实体类参数
     *
     * @param workFlow              工艺路线实体
     * @param workFlowStepImportDto 当前工序
     * @param workFlowStepList      组装数据后的实体列表
     * @return String
     * <AUTHOR>
     * @date 2021-09-17
     **/
    private void validateStepAndSortAdd(WorkFlow workFlow, WorkFlowStepImportDTO workFlowStepImportDto, LinkedList<WorkFlowStep> workFlowStepList) {
        WorkFlowStep workFlowStep = new WorkFlowStep().setWorkFlow(workFlow).setPreStepId(Constants.EMPTY).setAfterStepId(Constants.EMPTY);
        Optional<Step> step = stepRepository.findByCode(workFlowStepImportDto.getStepCode());
        if (step.isEmpty()) {
            throw new ResponseException("error.stepCodeNotExist","工序编码:" + workFlowStepImportDto.getStepCode() + "不存在");
        }
        if (!step.get().getIsEnable()) {
            throw new ResponseException("error.stepCodeDisabled","工序编码:" + workFlowStepImportDto.getStepCode() + "已禁用");
        }
        if (workFlowStepList.isEmpty() || !workFlowStepList.stream().map(WorkFlowStep::getWorkFlow).toList().contains(workFlow)) {
            workFlowStepList.add(workFlowStep.setStep(step.get()));
            return ;
        }
        // 找到当前工序的前置工序
        Step proStep = workFlowStepList.getLast().getStep();
        // 给上一条工序添加后置工序为后置工序
        workFlowStepList.getLast().setAfterStepId(step.get().getId().toString());
        // 给当前工序的前置工序添加前置工序
        workFlowStepList.add(workFlowStep.setStep(step.get()).setPreStepId(proStep.getId().toString()));
    }

    /**
     * 删除工艺路线工序和添加工艺路线工序
     *
     * @param errorMessage
     * @param workFlowStepList 工艺路线工序列表
     * @param workFlowList     工艺路线列表
     * @return void
     * <AUTHOR>
     * @date 2021/12/15
     */
    private void deletedAndSaveWorkFlowStep(String errorMessage, LinkedList<WorkFlowStep> workFlowStepList, List<WorkFlow> workFlowList) {
        // 删除当前工艺路线存在流程图
        workFlowStepRepository.logicDelete(workFlowStepRepository.findByWorkFlowIdInAndDeleted(workFlowList.stream().map(WorkFlow::getId).collect(Collectors.toList()), Constants.LONG_ZERO));
        // 往数据库里面添加数据
        workFlowStepRepository.saveAll(workFlowStepList);
    }


    /**
     * 通过工艺路线ID和工序ID获取前置工序数据
     *
     * @param workFlowId 工艺路线ID
     * @param stepId     工序ID
     * @return List
     */
    @Transactional(readOnly = true)
    public List<Step> findPreStep(Long workFlowId, Long stepId) {
        List<Step> stepList = null;
        List<WorkFlowStep> workFlowStepList = workFlowStepRepository.findStepByWorkFlowIdAndDeleted(workFlowId, Constants.LONG_ZERO);
        List<Long> preStepIdList = Lists.newArrayList();
        // 2022-8-15新增工序时长管控支持当前工序时长管控，当前工序也可以进行配置
        preStepIdList.add(stepId);
        // 递归获取前置工序
        dealStep(workFlowStepList, stepId, preStepIdList);
        if (ValidateUtils.isValid(preStepIdList)) {
            stepList = stepRepository.findByIdInAndDeleted(preStepIdList, Constants.LONG_ZERO);
        }
        return stepList;
    }

    /**
     * 递归获取前置工序
     *
     * @param workFlowStepList 流程框图工序
     * @param stepId           当工序ID
     * @param preStepIdList    前置工序ID
     */
    public void dealStep(List<WorkFlowStep> workFlowStepList, Long stepId, List<Long> preStepIdList) {
        // 通过当前ID获取已存在存在流程框图的工序
        Optional<WorkFlowStep> workFlowStepOptional = workFlowStepList.stream().filter(workFlowStep -> workFlowStep.getStep().getId().equals(stepId)).findFirst();
        // 判断当前工序有没有前置工序
        if (workFlowStepOptional.isPresent() && ValidateUtils.isValid(workFlowStepOptional.get().getPreStepId())) {
            // 前置工序进行分割
            List<Long> perStepIds = Arrays.stream(workFlowStepOptional.get().getPreStepId().split(Constants.STR_COMMA)).map(Long::parseLong).collect(Collectors.toList());
            perStepIds.forEach(perStep -> {
                preStepIdList.add(perStep);
                dealStep(workFlowStepList, perStep, preStepIdList);
            });
        }
    }

    /**
     * 递归获取后置工序
     *
     * @param workFlowStepList 流程框图工序
     * @param stepId           当工序ID
     * @param afterStepIdList  后置工序ID
     */
    public void dealAfterStep(List<WorkFlowStep> workFlowStepList, Long stepId, List<Long> afterStepIdList) {
        // 通过当前ID获取已存在存在流程框图的工序
        WorkFlowStep workFlowStep = workFlowStepList.stream().filter(i -> Objects.nonNull(stepId) && i.getStep().getId().equals(stepId)).findFirst().orElse(null);
        // 判断当前工序有没有后置工序
        if (Objects.nonNull(workFlowStep) && ValidateUtils.isValid(workFlowStep.getAfterStepId())) {
            // 后置工序进行分割
            List<Long> afterStepIds = Arrays.stream(workFlowStep.getAfterStepId().split(Constants.STR_COMMA)).map(Long::parseLong).collect(Collectors.toList());
            afterStepIds.forEach(afterStep -> {
                afterStepIdList.add(afterStep);
                dealAfterStep(workFlowStepList, afterStep, afterStepIdList);
            });

        }
    }


    /**
     * 根据条件搜索，获取有序工艺路线工序配置信息
     *
     * @param pageable 分页数据
     * @param qcs      搜索条件
     * @param filters  过滤条件
     * @param request  请求
     * @return Page<WorkFlowStep>
     * <AUTHOR>
     * @date 2022/10/26
     */
    public Page<WorkFlowStep> searchQuery(Pageable pageable, List<QueryCondition> qcs, List<SearchFilter> filters, HttpServletRequest request) {
        Specification<WorkFlowStep> spec = QueryConditionParser.buildSpecificationWithClassName(WorkFlowStep.class.getName(), qcs, filters, null);

        QueryCondition workFlowQueryCondition = qcs.stream().filter(queryCondition -> "workFlow.code".equals(queryCondition.getFieldName()) && net.airuima.util.ValidateUtils.isValid((String) queryCondition.getFieldValue())).findFirst()
                .orElse(qcs.stream().filter(queryCondition -> "workFlow.id".equals(queryCondition.getFieldName()) && net.airuima.util.ValidateUtils.isValid((String) queryCondition.getFieldValue())).findFirst().orElse(null));
        QueryCondition stepQueryCondition = qcs.stream().filter(queryCondition -> "step.code".equals(queryCondition.getFieldName()) && net.airuima.util.ValidateUtils.isValid((String) queryCondition.getFieldValue())).findFirst()
                .orElse(qcs.stream().filter(queryCondition -> "step.id".equals(queryCondition.getFieldName()) && net.airuima.util.ValidateUtils.isValid((String) queryCondition.getFieldValue())).findFirst().orElse(null));
        if (Objects.nonNull(workFlowQueryCondition)) {

            List<WorkFlowStep> workFlowSteps = this.find(spec);
            if (!ValidateUtils.isValid(workFlowSteps)) {
                return new PageImpl(Collections.EMPTY_LIST);
            }
            List<WorkFlowStep> workFlowStepList = Lists.newArrayList();
            if (Objects.nonNull(stepQueryCondition)) {
                workFlowStepList.addAll(workFlowSteps);
            } else {
                //排序工序
                this.findTreeWorkFlowStep(workFlowSteps, null, workFlowStepList);
            }
            int totalSize = workFlowStepList.size();
            return PageableExecutionUtils.getPage(this.getWorkFlowStepList(workFlowStepList, request), pageable, () -> totalSize);
        }

        return workFlowStepRepository.findAll(spec, pageable);
    }

    /**
     * 对于工艺路线工序配置，有序排序
     *
     * @param workFlowSteps
     * @param parentIds
     * @param newWorkFlowSteps
     * @return void
     * <AUTHOR>
     * @date 2022/10/26
     */
    public void findTreeWorkFlowStep(List<WorkFlowStep> workFlowSteps, List<Long> parentIds, List<WorkFlowStep> newWorkFlowSteps) {
        List<WorkFlowStep> currStageWorkFlowSteps = null;
        if (!ValidateUtils.isValid(workFlowSteps)) {
            return;
        }
        //区分前置工序为空与前置工序不为空的情形
        if (ValidateUtils.isValid(parentIds)) {
            List<Long> finalParentIds = parentIds;
            currStageWorkFlowSteps = workFlowSteps.stream().filter(workFlowStep -> finalParentIds.stream().anyMatch(parentId -> workFlowStep.getPreStepId().contains(parentId.toString()))).collect(Collectors.toList());
        } else {
            currStageWorkFlowSteps = workFlowSteps.stream().filter(workFlowStep -> StringUtils.isBlank(workFlowStep.getPreStepId())).collect(Collectors.toList());
        }
        newWorkFlowSteps.addAll(currStageWorkFlowSteps);
        parentIds = Lists.newArrayList();
        parentIds.addAll(newWorkFlowSteps.stream().map(workFlowStep -> workFlowStep.getStep().getId()).collect(Collectors.toList()));
        workFlowSteps.removeAll(currStageWorkFlowSteps);
        findTreeWorkFlowStep(workFlowSteps, parentIds, newWorkFlowSteps);
    }

    /**
     * 有序工序快照 手动分页
     *
     * @param workFlowSteps 有序工序快照
     * @param request
     * @return List<WorkFlowStep>
     * <AUTHOR>
     * @date 2022/10/26
     */
    public List<WorkFlowStep> getWorkFlowStepList(List<WorkFlowStep> workFlowSteps, HttpServletRequest request) {
        int pageSize = Integer.parseInt(request.getParameter("size"));
        int currentPage = Integer.parseInt(request.getParameter("page"));
        List<WorkFlowStep> workFlowStepList = Lists.newArrayList();
        for (int index = pageSize * currentPage; index < pageSize + pageSize * currentPage && index < workFlowSteps.size(); index++) {
            workFlowStepList.add(workFlowSteps.get(index));
        }
        return workFlowStepList;
    }

    /**
     * 获取导出数据，存在单个工艺路线条件，可按顺序导出
     *
     * @param exportDTO 导出搜索条件dto
     * @param filters   默认过滤参数
     * @return List<WorkFlowStep>
     * <AUTHOR>
     * @date 2022/10/26
     */
    public List<WorkFlowStep> exportTableExcel(ExportDTO exportDTO, List<SearchFilter> filters) {
        Specification<WorkFlowStep> spec = QueryConditionParser.buildSpecificationWithClassName(WorkFlowStep.class.getName(), exportDTO.getQcs(), filters, null);

        Optional<QueryCondition> workFlowQueryCondition = exportDTO.getQcs().stream().filter(queryCondition -> "workFlow.code".equals(queryCondition.getFieldName()) && queryCondition.getFieldValue() != null).findFirst();
        List<WorkFlowStep> workFlowSteps = this.find(spec);
        List<WorkFlowStep> workFlowStepList = Lists.newArrayList();
        //存在工艺路线排序导出
        if (workFlowQueryCondition.isPresent() && ValidateUtils.isValid(workFlowSteps)) {
            this.findTreeWorkFlowStep(workFlowSteps, null, workFlowStepList);
            return workFlowStepList;
        } else {
            return workFlowSteps;
        }
    }

    /**
     * 根据工艺路线查询对应的工序
     *
     * @param workFlowId 工艺路线id
     * @return java.util.List<net.airuima.rbase.domain.base.process.Step> 工序集合
     */
    @Transactional(readOnly = true)
    public List<Step> findStepByWorkFlow(long workFlowId) {
        List<Step> stepList = new ArrayList<>();
        List<WorkFlowStep> workFlowStepList = workFlowStepRepository.findByWorkFlowIdAndDeleted(workFlowId, net.airuima.constant.Constants.LONG_ZERO);
        if(CollectionUtils.isEmpty(workFlowStepList)){
            return stepList;
        }
        //工艺路线工序排序返回
        List<WorkFlowStep> sortWorkFlowStepList = Lists.newArrayList();
        this.findTreeWorkFlowStep(workFlowStepList,null,sortWorkFlowStepList);
        sortWorkFlowStepList.forEach(workFlowStep -> {
            stepList.add(workFlowStep.getStep());
        });
        return stepList;
    }

    /**
     * 获取当前工序之后的工序
     *
     * @param workFlowId 工艺路线id
     * @param stepId     工序id
     *                   java.util.List<net.airuima.rbase.domain.base.process.Step> 工序集合
     */
    @Transactional(readOnly = true)
    public List<Step> findByAfterStep(Long workFlowId, Long stepId) {
        List<Step> stepList = Lists.newArrayList();
        List<WorkFlowStep> workFlowStepList = workFlowStepRepository.findStepByWorkFlowIdAndDeleted(workFlowId, Constants.LONG_ZERO);
        if (!CollectionUtils.isEmpty(workFlowStepList)) {
            List<Long> afterStepIdList = Lists.newArrayList();
            // 递归获取后置工序
            dealAfterStep(workFlowStepList, stepId, afterStepIdList);
            if (!CollectionUtils.isEmpty(afterStepIdList)) {
                stepList = stepRepository.findByIdInAndDeleted(afterStepIdList, Constants.LONG_ZERO);
            }
        }
        return stepList;
    }
}
