package net.airuima.rbase.service.procedure.aps;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.constant.OperationEnum;
import net.airuima.rbase.constant.WsEnum;
import net.airuima.rbase.domain.base.pedigree.PedigreeConfig;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.Container;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.domain.procedure.single.WorkSheetSn;
import net.airuima.rbase.dto.aps.SubWorkSheetDTO;
import net.airuima.rbase.dto.aps.SubWorkSheetSimpleGetDTO;
import net.airuima.rbase.dto.aps.WsSubWsDTO;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.digiwin.DigiwinSubWorkSheetCompleteDTO;
import net.airuima.rbase.dto.rule.SerialNumberConfigDTO;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.repository.base.scene.WorkLineRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WsReworkRepository;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.rbase.repository.procedure.batch.ContainerDetailRepository;
import net.airuima.rbase.repository.procedure.batch.ContainerRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepRepository;
import net.airuima.rbase.repository.procedure.single.WorkSheetSnRepository;
import net.airuima.rbase.service.base.serialnumber.ISerialNumberGenerate;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.procedure.aps.plugin.IWorkSheetCompleteService;
import net.airuima.rbase.service.procedure.batch.WsStepWorkCellService;
import net.airuima.rbase.service.report.api.IWorkSheetStatisticsService;
import net.airuima.rbase.service.report.api.IWorkSheetStepStatisticsService;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseException;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产子工单Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SubWorkSheetService extends CommonJpaService<SubWorkSheet> {
    private static final String SUB_WORK_SHEET_ENTITY_GRAPH = "subWorkSheetEntityGraph";
    private final SubWorkSheetRepository subWorkSheetRepository;
    private final WorkSheetRepository workSheetRepository;
    private final BatchWorkDetailRepository batchWorkDetailRepository;
    private final WorkLineRepository workLineRepository;
    private final CommonService commonService;

    @Autowired
    private WsReworkRepository wsReworkRepository;
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private WorkFlowRepository workFlowRepository;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private ContainerRepository containerRepository;
    @Autowired
    private WsStepWorkCellService wsStepWorkCellService;
    @Autowired
    private IWorkSheetCompleteService[] workSheetCompleteService;
    @Autowired
    private ISerialNumberGenerate[] serialNumberGenerate;
    @Autowired
    private IWorkSheetStatisticsService[] workSheetStatisticsServices;
    @Autowired
    private IWorkSheetStepStatisticsService[] workSheetStepStatisticsServices;
    @Autowired
    private WorkSheetSnRepository workSheetSnRepository;
    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;

    public SubWorkSheetService(SubWorkSheetRepository subWorkSheetRepository, WorkSheetRepository workSheetRepository, BatchWorkDetailRepository batchWorkDetailRepository,
                               WorkLineRepository workLineRepository, CommonService commonService) {
        this.subWorkSheetRepository = subWorkSheetRepository;
        this.workSheetRepository = workSheetRepository;
        this.batchWorkDetailRepository = batchWorkDetailRepository;
        this.workLineRepository = workLineRepository;
        this.commonService = commonService;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<SubWorkSheet> find(Specification<SubWorkSheet> spec, Pageable pageable) {
        return subWorkSheetRepository.findAll(spec, pageable, new NamedEntityGraph(SUB_WORK_SHEET_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<SubWorkSheet> find(Specification<SubWorkSheet> spec) {
        return subWorkSheetRepository.findAll(spec, new NamedEntityGraph(SUB_WORK_SHEET_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<SubWorkSheet> findAll(Pageable pageable) {
        return subWorkSheetRepository.findAll(pageable, new NamedEntityGraph(SUB_WORK_SHEET_ENTITY_GRAPH));
    }

    /**
     * 根据总工单ID获取对应的子工单列表
     *
     * @param workSheetId 总工单ID
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.aps.SubWorkSheet>
     * <AUTHOR>
     **/
    @Transactional(readOnly = true)
    public WsSubWsDTO findByWorkSheetIdAndDeleted(Long workSheetId, Long deleted) {
        WorkSheet workSheet = workSheetRepository.findByIdAndDeleted(workSheetId, deleted).orElse(null);
        if (null == workSheet) {
            return null;
        }
        WsSubWsDTO wsSubWsDto = new WsSubWsDTO(workSheet);
        List<SubWorkSheet> subWorkSheetList = subWorkSheetRepository.findByWorkSheetIdAndStatusNotInAndDeleted(workSheetId, Arrays.asList(ConstantsEnum.WORK_SHEET_STATIC_CANCEL.getCategoryName(), ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName()), deleted);
        int generatedNumber = ValidateUtils.isValid(subWorkSheetList) ? subWorkSheetList.stream().mapToInt(SubWorkSheet::getNumber).sum() : Constants.INT_ZERO;
        wsSubWsDto.setLeftNumber(wsSubWsDto.getNumber() - generatedNumber).setSubWorkSheetList(net.airuima.util.ValidateUtils.isValid(subWorkSheetList) ? subWorkSheetList.stream().map(SubWorkSheetSimpleGetDTO::new).toList() : new ArrayList<>());
        PedigreeConfig pedigreeConfig = commonService.findPedigreeConfig(workSheet.getPedigree());
        wsSubWsDto.setSplitNumber(null != pedigreeConfig ? pedigreeConfig.getSplitNumber() : Constants.INT_ONE);
        return wsSubWsDto;
    }

    /**
     * 手动新增批量工单
     *
     * @param subWorkSheetDtoList 新增批量子工单参数
     * @return ResponseEntity<ResponseContent < Void>>
     * <AUTHOR>
     * @date 2021-04-28
     **/
    public BaseDTO saveInstance(List<SubWorkSheetDTO> subWorkSheetDtoList) {
        WorkSheet workSheet = workSheetRepository.findByIdAndDeleted(subWorkSheetDtoList.get(Constants.INT_ZERO).getWorkSheetId(), Constants.LONG_ZERO).orElse(null);
        if(Objects.isNull(workSheet.getWorkLine())){
            throw new ResponseException("error.workLineNotBeNull", "工单生产线不可为空");
        }
        if(Objects.isNull(workSheet.getWorkFlow())){
            throw new ResponseException("error.workFlowNotBeNull", "工单工艺路线不可为空");
        }
        //验证当前工单是否具备分单的必要条件
        this.validateWorkSheet(workSheet);
        List<SubWorkSheet> subWorkSheetList = Lists.newArrayList();
        //带id和不带id总和
        int subWorkSheetCount = subWorkSheetDtoList.size();
        //工单最大生成子工单数
        Integer maxRangeSerialNumber = this.rangeSerialNumber(Constants.KEY_SERIAL_NUMBER_NORMAL_SUB_WORK_SHEET);
        //子工单创建不能超过最大限制
        if (maxRangeSerialNumber < subWorkSheetCount) {
            return new BaseDTO(Constants.KO, "SubWorkSheetExceedMax");
        }
        //统计当前工单对应划分的所有子工单投产数量
        long sumSubWorkSheet = subWorkSheetDtoList.stream().map(SubWorkSheetDTO::getNumber).count();
        if (sumSubWorkSheet > workSheet.getNumber()) {
            return new BaseDTO(Constants.KO, "numberOverSumWorkSheet");
        }
        Long stepNum =  wsStepRepository.countByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        int stepNumber = Objects.nonNull(stepNum)?stepNum.intValue(): net.airuima.constant.Constants.INT_ZERO;
        //获取当前工单已存在的工单号
        List<String> serialNumberList = subWorkSheetDtoList.stream().map(SubWorkSheetDTO::getSerialNumber).filter(StringUtils::isNotBlank).toList();
        for (SubWorkSheetDTO subWorkSheetDto : subWorkSheetDtoList) {
            //新增子工单编码（自动生成）
            if (null == subWorkSheetDto.getId()) {
                SerialNumberDTO serialNumberDto = serialNumberGenerate[0].getSerialNumberByWorkSheet(Constants.KEY_SERIAL_NUMBER_NORMAL_SUB_WORK_SHEET, workSheet.getId());
                serialNumberDto.setPrefixCode(workSheet.getSerialNumber());
                //如果生成的子工单号已存在则再重新生成一个
                if (ValidateUtils.isValid(serialNumberList)) {
                    for (; ; ) {
                        if (StringUtils.isNotBlank(subWorkSheetDto.getSerialNumber())) {
                            break;
                        }
                        String serialNumber = rbaseSerialNumberProxy.generate(serialNumberDto);
                        if (!serialNumberList.contains(serialNumber)) {
                            subWorkSheetDto.setSerialNumber(serialNumber);
                            break;
                        }
                    }
                } else {
                    //第一次创建子工单
                    if (StringUtils.isBlank(subWorkSheetDto.getSerialNumber())) {
                        subWorkSheetDto.setSerialNumber(rbaseSerialNumberProxy.generate(serialNumberDto));
                    }
                }
                BaseDTO baseDto = this.validateSubWorkSheet(subWorkSheetDto, workSheet);
                if (Constants.KO.equals(baseDto.getStatus())) {
                    return baseDto;
                }
            }
        }
        for (SubWorkSheetDTO subWorkSheetDto : subWorkSheetDtoList) {
            SubWorkSheet subWorkSheet = null != subWorkSheetDto.getId() ? subWorkSheetRepository.getReferenceById(subWorkSheetDto.getId()) : new SubWorkSheet();
            Long producedCount = Objects.nonNull(subWorkSheet) ? batchWorkDetailRepository.countBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO) : Constants.LONG_ZERO;
            if (null != subWorkSheetDto.getId() && Objects.nonNull(subWorkSheet) && producedCount > Constants.LONG_ZERO && subWorkSheet.getNumber() != subWorkSheetDto.getNumber()) {
                throw new ResponseException("error.subWorkSheetAlreadyProduced", "子工单(" + subWorkSheet.getSerialNumber() + ")已投产，不可修改数量");
            }
            if (null == subWorkSheetDto.getId()) {
                subWorkSheet = net.airuima.rbase.util.MapperUtils.map(subWorkSheetDto, SubWorkSheet.class);
                subWorkSheet.setWorkSheet(workSheet)
                        .setWorkFlow(workSheet.getWorkFlow())
                        .setWorkLine(workSheet.getWorkLine())
                        .setPriority(workSheet.getPriority())
                        .setStepNumber(stepNumber)
                        .setPlanEndDate(null != subWorkSheetDto.getPlanEndDate() ? subWorkSheetDto.getPlanEndDate() : workSheet.getPlanEndDate())
                        .setDeleted(Constants.LONG_ZERO);
            }
            subWorkSheet.setPlanStartDate(subWorkSheetDto.getPlanStartDate()).setPlanEndDate(null != subWorkSheetDto.getPlanEndDate() ? subWorkSheetDto.getPlanEndDate() : workSheet.getPlanEndDate()).setNumber(subWorkSheetDto.getNumber()).setNote(subWorkSheetDto.getNote()).setSerialNumber(subWorkSheetDto.getSerialNumber());
            subWorkSheetList.add(subWorkSheet);
        }
        List<SubWorkSheet> finalSubWorkSheetList = subWorkSheetRepository.saveAll(subWorkSheetList);
        //添加子工单工序指定的工位信息
        wsStepWorkCellService.addSubWorkSheetStepWorkCell(finalSubWorkSheetList, subWorkSheetDtoList);
        Long number = subWorkSheetRepository.statsNumberByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        workSheet.setGenerateSubWsStatus(number == Constants.LONG_ZERO ? Constants.INT_ZERO : number < workSheet.getNumber() ? Constants.INT_ONE : Constants.INT_TWO);
        //更新工单的子工单个数以及完成个数(包括正常、异常)
        this.updateWorkSheetSubWsNumberInfo(workSheet);
        //初始化更新工序在制看板数据
        workSheetStepStatisticsServices[0].initWorkSheetStepStatisticsInfo(workSheet, subWorkSheetList, Boolean.TRUE);
        workSheetRepository.save(workSheet);
        return new BaseDTO(Constants.OK);
    }

    /**
     * 获取配置的最大范围 值
     *
     * @param code
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2021/11/16
     */
    public Integer rangeSerialNumber(String code) {
        SerialNumberConfigDTO serialNumberConfigDTO = rbaseSerialNumberProxy.findBySerialCodeAndDeleted(code,Constants.LONG_ZERO).orElse(new SerialNumberConfigDTO());
        String serialRange = serialNumberConfigDTO.getSerialRange();
        return Arrays.stream(serialRange.split(Constants.HYPHEN))
                .toList().stream().map(Integer::parseInt)
                .max(Comparator.comparingInt(Integer::intValue)).orElse(null);
    }

    /**
     * 分单时，验证当前工单是否具备分单的必要条件
     *
     * @param workSheet 总工单
     */
    public void validateWorkSheet(WorkSheet workSheet) {
        //工单不存在
        if (null == workSheet) {
            throw new ResponseException("error.WorkSheetNotExist", "工单不存在");
        }
        //产品型号不存在
        if (null == workSheet.getPedigree()) {
            throw new ResponseException("error.PedigreeNotExist", "产品型号不存在");
        }
        //工艺路线不存在
        if (null == workSheet.getWorkFlow()) {
            throw new ResponseException("error.WorkFlowNotExist", "工艺路线不存在");
        }
        //物料清单不存在
        if (null == workSheet.getBomInfoId()) {
            throw new ResponseException("error.BomInfoNotExist", "物料清单不存在");
        }
    }

    /**
     * 保存或者修改子工单验证数量等信息是否合规
     *
     * @param subWorkSheetDto 子工单Dto
     * @param workSheet       总工单
     * @return BaseDTO
     */
    public BaseDTO validateSubWorkSheet(SubWorkSheetDTO subWorkSheetDto, WorkSheet workSheet) {
        // 判断子工单号是否合法
        Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findBySerialNumberAndDeleted(subWorkSheetDto.getSerialNumber(), Constants.LONG_ZERO);
        boolean isValidSerialNumber = (null == subWorkSheetDto.getId() && !subWorkSheetOptional.isPresent())
                || (null != subWorkSheetDto.getId() && subWorkSheetOptional.isPresent() && subWorkSheetOptional.get().getId().equals(subWorkSheetDto.getId()));
        if (!isValidSerialNumber) {
            return new BaseDTO(Constants.KO, "serialNumberExist");
        }
        // 判断修改的子工单是否已投产
        Long producedCount = batchWorkDetailRepository.countBySubWorkSheetIdAndDeleted(subWorkSheetDto.getId(), Constants.LONG_ZERO);
        if (null != subWorkSheetDto.getId() && producedCount > Constants.LONG_ZERO) {
            return new BaseDTO(Constants.KO, "subWorkSheetAlreadyProduced");
        }
        return new BaseDTO(Constants.OK);
    }

    /**
     * 自动生成子工单
     *
     * @param workSheetId     总工单ID
     * @param workLineId      生产线ID
     * @param planStartDate   计划开工日期
     * @param planEndDate     计划完工日期
     * @param singleNumber    单个子工单数量
     * @param isGenerateSubWs 是否自动生成子工单
     */
    public SubWorkSheet autoGenerateSubWorkSheet(Long workSheetId, Long workLineId, LocalDateTime planStartDate, LocalDateTime planEndDate, int singleNumber, Boolean isGenerateSubWs) {
        if (null != isGenerateSubWs && isGenerateSubWs) {
            Long number = subWorkSheetRepository.statsNumberByWorkSheetIdAndDeleted(workSheetId, Constants.LONG_ZERO);
            WorkSheet workSheet = workSheetRepository.getReferenceById(workSheetId);
            if(Objects.isNull(workSheet.getWorkLine())){
                throw new ResponseException("error.workLineNotBeNull", "工单生产线不可为空");
            }
            if(Objects.isNull(workSheet.getWorkFlow())){
                throw new ResponseException("error.workFlowNotBeNull", "工单工艺路线不可为空");
            }
            //工单的状态，若在审批中则不自动分单
            if (Constants.APPROVE == workSheet.getStatus()) {
                return null;
            }
            //验证当前工单是否具备分单的必要条件
            this.validateWorkSheet(workSheet);
            WorkLine workLine = null == workLineId ? null : workLineRepository.getReferenceById(workLineId);
            int leftNumber = workSheet.getNumber() - (null != number ? number.intValue() : Constants.INT_ZERO);
            if (leftNumber <= Constants.INT_ZERO) {
                return null;
            }
            int subWorkSheetSize = leftNumber / singleNumber;
            if (leftNumber % singleNumber > Constants.INT_ZERO) {
                subWorkSheetSize++;
            }
            //获取已存在的子工单
            List<SubWorkSheet> subWorkSheetList = subWorkSheetRepository.findByWorkSheetIdAndDeleted(workSheetId, Constants.LONG_ZERO);
            if (ValidateUtils.isValid(subWorkSheetList)) {
                Integer maxNumber = this.rangeSerialNumber(Constants.KEY_SERIAL_NUMBER_NORMAL_SUB_WORK_SHEET);
                if (maxNumber < subWorkSheetSize + subWorkSheetList.size()) {
                    return null;
                }
            }
            SerialNumberDTO serialNumberDto = serialNumberGenerate[0].getSerialNumberByWorkSheet(Constants.KEY_SERIAL_NUMBER_NORMAL_SUB_WORK_SHEET, workSheet.getId());
            serialNumberDto.setPrefixCode(workSheet.getSerialNumber());
            SubWorkSheet lastSubWorkSheet = null;
            Long stepNum = wsStepRepository.countByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            int stepNumber = Objects.nonNull(stepNum)?stepNum.intValue(): net.airuima.constant.Constants.INT_ZERO;
            List<SubWorkSheet> savedSubWorkSheetList = new ArrayList<>();
            while (subWorkSheetSize > Constants.INT_ZERO) {
                SubWorkSheet subWorkSheet = new SubWorkSheet();
                subWorkSheet.setWorkSheet(workSheet)
                        .setWorkFlow(workSheet.getWorkFlow())
                        .setWorkLine(workLine)
                        .setPriority(workSheet.getPriority())
                        .setPlanStartDate(planStartDate)
                        .setPlanEndDate(null != planEndDate ? planEndDate : workSheet.getPlanEndDate())
                        .setNumber(Math.min(singleNumber, leftNumber)).setStepNumber(stepNumber);
                if (ValidateUtils.isValid(subWorkSheetList)) {
                    for (; ; ) {
                        String generateSerialNumber = rbaseSerialNumberProxy.generate(serialNumberDto);
                        if (!subWorkSheetList.stream().map(SubWorkSheet::getSerialNumber).toList().contains(generateSerialNumber)) {
                            subWorkSheet.setSerialNumber(generateSerialNumber);
                            break;
                        }
                    }
                } else {
                    subWorkSheet.setSerialNumber(rbaseSerialNumberProxy.generate(serialNumberDto));
                }
                lastSubWorkSheet = subWorkSheetRepository.save(subWorkSheet);
                savedSubWorkSheetList.add(lastSubWorkSheet);
                leftNumber -= singleNumber;
                subWorkSheetSize--;
            }
            //更新工单的子工单个数以及完成个数(包括正常、异常)
            this.updateWorkSheetSubWsNumberInfo(workSheet);
            //初始化更新工序在制看板数据
            workSheetStepStatisticsServices[0].initWorkSheetStepStatisticsInfo(workSheet, savedSubWorkSheetList, Boolean.TRUE);
            workSheet.setGenerateSubWsStatus(Constants.INT_TWO);
            workSheetRepository.save(workSheet);
            return lastSubWorkSheet;
        }
        return null;
    }


    /**
     * 通过子工单号模糊查询子工单列表
     *
     * @param serialNumber 子工单号
     * @param size         列数
     * @return List<WorkSheet>
     * <AUTHOR>
     * @date 2021-04-25
     **/
    @Transactional(readOnly = true)
    public List<SubWorkSheet> findPageBySerialNumber(String serialNumber, Integer size) {
        return Optional.ofNullable(subWorkSheetRepository.findPageBySerialNumber(serialNumber, PageRequest.of(Constants.INT_ZERO, size))).map(Slice::getContent).orElse(null);
    }

    /**
     * 上传子工单，修改上传状态 推送工单完工信息
     *
     * @param subWorkSheetIdList 子工单id列表
     * @return ResponseEntity<Void>
     * <AUTHOR>
     * @date 2021/8/11
     */
    public ResponseEntity<Void> subWorkSheetComplete(List<Long> subWorkSheetIdList) {
        String errMsg = workSheetCompleteService[0].subWorkSheetComplete(subWorkSheetIdList);
        if (StringUtils.isNotBlank(errMsg)) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(SubWorkSheet.class.getSimpleName()), "errMsg", errMsg.toString())).build();
        }
        return ResponseEntity.ok().headers(HeaderUtil.updatedAlert(StringUtils.uncapitalize(SubWorkSheet.class.getSimpleName()), "")).build();
    }

    /**
     * 通过子工单 转化总工单 上传到erp 修改上传状态
     *
     * @param subWorkSheet 子工单信息
     * @return void
     * <AUTHOR>
     * @date 2021/8/11
     */
    public void erpWsComplete(List<DigiwinSubWorkSheetCompleteDTO.WorkSheetCompleteDTO> dwList, SubWorkSheet subWorkSheet) {

        DigiwinSubWorkSheetCompleteDTO.WorkSheetCompleteDTO dw = new DigiwinSubWorkSheetCompleteDTO.WorkSheetCompleteDTO();
        WorkSheet workSheet = workSheetRepository.findBySerialNumberAndDeleted(subWorkSheet.getWorkSheet().getSerialNumber(), Constants.LONG_ZERO).orElse(null);
        if (workSheet != null) {
            //正常单
            if (workSheet.getCategory() == Constants.INT_ONE) {
                dw.setSerialNumber(workSheet.getSerialNumber());
            }
            //返修单
            if (workSheet.getCategory() == Constants.NEGATIVE_ONE) {
                WsRework wsRework = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO).orElse(null);
                if (wsRework != null) {
                    dw.setSerialNumber(wsRework.getOriginalWorkSheet().getSerialNumber());
                }
            }
            dw.setQualifiedNumber(subWorkSheet.getQualifiedNumber()).setUnqualifiedNumber(subWorkSheet.getUnqualifiedNumber());
            //收集工单完工信息
            dwList.add(dw);
        }
    }

    /**
     * 通用CRUD修改子工单
     *
     * @param entity
     * @return ResponseEntity<SubWorkSheet>
     * <AUTHOR>
     * @date 2021-11-17
     **/
    public ResponseEntity<SubWorkSheet> updateEntity(SubWorkSheet entity) {
        SubWorkSheet subWorkSheet = subWorkSheetRepository.getReferenceById(entity.getId());
        subWorkSheet.setWorkSheet(null == entity.getWorkSheet() ? subWorkSheet.getWorkSheet() : workSheetRepository.getReferenceById(entity.getWorkSheet().getId()))
                .setWorkFlow(null == entity.getWorkFlow() ? subWorkSheet.getWorkFlow() : workFlowRepository.getReferenceById(entity.getWorkFlow().getId()))
                .setWorkLine(null == entity.getWorkLine() ? subWorkSheet.getWorkLine() : workLineRepository.getReferenceById(entity.getWorkLine().getId()))
                .setActualEndDate(subWorkSheet.getActualEndDate())
                .setActualStartDate(subWorkSheet.getActualStartDate())
                .setStatus(entity.getStatus())
                .setNumber(entity.getNumber())
                .setPriority(entity.getPriority())
                .setQualifiedNumber(entity.getQualifiedNumber())
                .setSerialNumber(null == entity.getSerialNumber() ? subWorkSheet.getSerialNumber() : entity.getSerialNumber())
                .setNote(null == entity.getNote() ? subWorkSheet.getNote() : entity.getNote());
        subWorkSheetRepository.save(subWorkSheet);
        WorkSheet workSheet = subWorkSheet.getWorkSheet();
        //更新工单的子工单个数以及完成个数(包括正常、异常)
        this.updateWorkSheetSubWsNumberInfo(workSheet);
        //初始化更新工序在制看板数据
        workSheetStepStatisticsServices[0].initWorkSheetStepStatisticsInfo(workSheet, List.of(subWorkSheet), Boolean.TRUE);
        workSheetRepository.save(workSheet);
        return ResponseEntity.ok().headers(HeaderUtil.updatedAlert(StringUtils.uncapitalize(SubWorkSheet.class.getSimpleName()), entity.getId().toString())).body(subWorkSheet);
    }


    /**
     * 通过子工单的id修改子工单的状态
     *
     * @param status         状态
     * @param subWorkSheetId 子工单的id
     * @date 2022-04-15
     * <AUTHOR>
     */
    public void syncStatus(Long subWorkSheetId, Integer status) {
        if (status != null) {
            subWorkSheetRepository.updateStatus(status, subWorkSheetId, Constants.LONG_ZERO);
        }
    }

    /**
     * 修改子工单状态 (事件机制、flowable 回调)
     *
     * @param serialNumber 子工单号
     * @param status       1: 投产中；2: 暂停
     * <AUTHOR>
     * @date 2023/4/3
     */
    //@Retryable(value = ObjectOptimisticLockingFailureException.class,maxAttempts = 4,backoff = @Backoff(delay = 2000L,multiplier = 1.5))
    public void updateStatusBySerialNumber(String serialNumber, Integer status) {
        Optional<SubWorkSheet> subWorkSheet = subWorkSheetRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO);
        subWorkSheet.ifPresent(workSheet -> subWorkSheetRepository.save(workSheet.setStatus(status)));
    }

    /**
     * 中途结单
     *
     * @param subWorkSheetDto 结单信息
     * @return ResponseEntity<Void>
     * <AUTHOR>
     * @date 2021/12/8
     */
    public BaseDTO forceFinishWorkSheet(SubWorkSheetDTO subWorkSheetDto) {
        WorkSheet workSheet = workSheetRepository.findBySerialNumberAndDeleted(subWorkSheetDto.getSerialNumber(), Constants.LONG_ZERO).orElse(null);
        if (null == workSheet) {
            return new BaseDTO(Constants.KO, "WorkSheetNotFind");
        }
        // 只有投产中、已暂停才能中途结单
        if (workSheet.getStatus() != ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName() && workSheet.getStatus() != ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName()) {
            return new BaseDTO(Constants.KO, "WorkSheetIsNotProducingOrPause");
        }
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
        //原始工单的合格数
        int originWorkSheetQualifiedNumber = workSheet.getQualifiedNumber();
        // 通过总工单ID及完成状态获取子工单列表
        List<SubWorkSheet> subWorkSheetList = subWorkSheetRepository.findByWorkSheetIdAndStatusLessThanAndDeleted(workSheet.getId(), Constants.FINISH, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(subWorkSheetList)) {
            // 更新子工单的信息
            subWorkSheetList.forEach(subWorkSheet -> syncSubWorkSheetInfo(subWorkSheet, subWorkSheetDto.getStatementReason()));
            //更新工单的子工单个数以及完成个数(包括正常、异常)
            this.updateWorkSheetSubWsNumberInfo(workSheet);
        } else {
            List<WsStep> wsSteps = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            WsStep lastWsStep = wsSteps.stream().filter(wsStep -> StringUtils.isBlank(wsStep.getAfterStepId())).findFirst().orElse(null);
            if (null != lastWsStep) {
                BatchWorkDetail batchWorkDetail = batchWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), lastWsStep.getStep().getId(), Constants.LONG_ZERO).orElse(new BatchWorkDetail());
                workSheet.setQualifiedNumber(batchWorkDetail.getQualifiedNumber() + workSheet.getReworkQualifiedNumber()).setUnqualifiedNumber(workSheet.getNumber() - workSheet.getQualifiedNumber());
            }
        }
        workSheet.setStatementReason(subWorkSheetDto.getStatementReason()).setActualEndDate(LocalDateTime.now())
                .setStatus(ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName());
        workSheetRepository.save(workSheet);
        //若返工单异常结单需要更新对应正常单的数据
        if (workSheet.getCategory() == WsEnum.ONLINE_RE_WS.getCategory()) {
            wsReworkRepository.findByReworkWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO).ifPresent(wsRework -> {
                WorkSheet originWorkSheet = wsRework.getOriginalWorkSheet();
                originWorkSheet.setReworkQualifiedNumber(originWorkSheet.getReworkQualifiedNumber() - originWorkSheetQualifiedNumber).setQualifiedNumber(originWorkSheet.getQualifiedNumber() - originWorkSheetQualifiedNumber).setUnqualifiedNumber(originWorkSheet.getUnqualifiedNumber() + originWorkSheetQualifiedNumber);
                originWorkSheet.setQualifiedNumber(originWorkSheet.getQualifiedNumber() + workSheet.getQualifiedNumber()).setUnqualifiedNumber(originWorkSheet.getUnqualifiedNumber() - workSheet.getQualifiedNumber()).setReworkQualifiedNumber(originWorkSheet.getReworkQualifiedNumber() + workSheet.getQualifiedNumber());
                workSheetRepository.save(originWorkSheet);
            });
        }
        // 中途结单 更新工单统计表
        workSheetStatisticsServices[0].updateWorkSheetNumber(workSheet.getId(), LocalDate.now(), workSheet.getQualifiedNumber(), workSheet.getUnqualifiedNumber(), OperationEnum.ADD);
        // 中途结单更新在制看板
        workSheetStepStatisticsServices[0].deleteWorkSheetStepStatisticsInfo(workSheet, subWorkSheetList, subWsProductionMode);
        return new BaseDTO(Constants.OK);
    }

    /**
     * 中途结单(flowable方法回调)
     *
     * @param serialNumber
     * @param statementReason
     * @return
     */
    public BaseDTO processScrap(String serialNumber, String statementReason) {
        return this.forceFinishWorkSheet(new SubWorkSheetDTO().setSerialNumber(serialNumber).setStatementReason(statementReason));
    }

    /**
     * 子工单中途结单
     *
     * @param subWorkSheetDto 子工单DTO
     * @date 2022-04-15
     * <AUTHOR>
     */
    public BaseDTO forceFinishSubWorkSheet(SubWorkSheetDTO subWorkSheetDto) {
        SubWorkSheet subWorkSheet = subWorkSheetRepository.findByIdAndDeleted(subWorkSheetDto.getId(), Constants.LONG_ZERO).orElse(null);
        if (subWorkSheet == null) {
            return new BaseDTO(Constants.KO, "subWorkSheetNotFind");
        }
        if (subWorkSheet.getStatus() != ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName() && subWorkSheet.getStatus() != ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName()) {
            return new BaseDTO(Constants.KO, "subWorkSheetIsNotProducingOrPause");
        }
        syncSubWorkSheetInfo(subWorkSheet, subWorkSheetDto.getStatementReason());
        // 中途结单 更新工单统计表
        WorkSheet workSheet = subWorkSheet.getWorkSheet();
        //更新工单的子工单个数以及完成个数(包括正常、异常)
        this.updateWorkSheetSubWsNumberInfo(workSheet);
        //更新在制看板数据
        workSheetStepStatisticsServices[0].deleteWorkSheetStepStatisticsInfo(workSheet, List.of(subWorkSheet), Boolean.TRUE);
        workSheetRepository.save(workSheet);
        workSheetStatisticsServices[0].updateWorkSheetNumber(workSheet.getId(), LocalDate.now(), workSheet.getQualifiedNumber(), workSheet.getUnqualifiedNumber(), OperationEnum.ADD);
        return new BaseDTO(Constants.OK);
    }

    /**
     * 更新子工单的信息
     *
     * @param subWorkSheet    子工单
     * @param statementReason 原因
     */
    public void syncSubWorkSheetInfo(SubWorkSheet subWorkSheet, String statementReason) {
        // 子工单有对应的容器详情全部要变为解绑状态
        List<ContainerDetail> containerDetailList = containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndStatusAndDeleted(subWorkSheet.getId(), Constants.INT_ONE, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(containerDetailList)) {
            // 更新容器详情的状态
            List<Long> ids = containerDetailList.stream().map(ContainerDetail::getId).collect(Collectors.toList());
            containerDetailRepository.updateStatus(Constants.INT_ZERO, ids, Constants.LONG_ZERO);
            // 更新容器本身的状态
            List<Long> containerIds = containerDetailList.stream().map(ContainerDetail::getContainer).filter(Container::getStatus).map(Container::getId).collect(Collectors.toList());
            if (ValidateUtils.isValid(containerIds)) {
                containerRepository.updateStatus(false, containerIds, Constants.LONG_ZERO);
            }
        }
        // 非完成或非异常结单的子工单获取最后一个工序的批量详情的合格数作为子工单的合格数
        if (subWorkSheet.getStatus() != ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName()
                && subWorkSheet.getStatus() != ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName()) {
            List<WsStep> wsSteps = wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
            if (CollectionUtils.isEmpty(wsSteps)) {
                wsSteps = wsStepRepository.findByWorkSheetIdAndDeleted(subWorkSheet.getWorkSheet().getId(), Constants.LONG_ZERO);
            }
            WsStep lastWsStep = wsSteps.stream().filter(wsStep -> StringUtils.isBlank(wsStep.getAfterStepId())).findFirst().orElse(null);
            if (null != lastWsStep) {
                WorkSheet workSheet = subWorkSheet.getWorkSheet();
                workSheet.setQualifiedNumber(workSheet.getQualifiedNumber() - subWorkSheet.getQualifiedNumber()).setUnqualifiedNumber(workSheet.getUnqualifiedNumber() - subWorkSheet.getUnqualifiedNumber());
                BatchWorkDetail batchWorkDetail = batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), lastWsStep.getStep().getId(), Constants.LONG_ZERO).orElse(new BatchWorkDetail());
                subWorkSheet.setQualifiedNumber(batchWorkDetail.getQualifiedNumber()).setUnqualifiedNumber(subWorkSheet.getNumber() - subWorkSheet.getQualifiedNumber());
                workSheet.setQualifiedNumber(workSheet.getQualifiedNumber() + subWorkSheet.getQualifiedNumber()).setUnqualifiedNumber(workSheet.getUnqualifiedNumber() + subWorkSheet.getUnqualifiedNumber());
                workSheetRepository.save(workSheet);
            }
        }
        subWorkSheet.setStatementReason(statementReason).setActualEndDate(LocalDateTime.now());
        // 未投产的子工单更新为取消状态
        if (subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_DEVOTE.getCategoryName()) {
            subWorkSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_CANCEL.getCategoryName());
        }
        //投产中或者暂停的子工单异常结单
        if (subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName() || subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_PAUSE.getCategoryName()) {
            subWorkSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName());
        }
        subWorkSheetRepository.save(subWorkSheet);
    }

    /**
     * 通过产线ID、完成状态获取子工单
     *
     * @param workLineId 生产线ID
     * @param status     状态
     * @return List<SubWorkSheet>
     **/
    public List<SubWorkSheet> findByWorkLineIdAndStatusLessThanAndDeleted(Long workLineId, Integer status) {
        return subWorkSheetRepository.findByWorkLineIdAndStatusLessThanAndDeleted(workLineId, status, Constants.LONG_ZERO);
    }

    /**
     * 修改子工单状态为取消
     *
     * @param id 子工单ID
     * <AUTHOR>
     * @date 2023-01-11
     **/
    public void cancel(Long id) {
        Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findByIdAndDeleted(id, Constants.LONG_ZERO);
        if (subWorkSheetOptional.isEmpty()) {
            throw new ResponseException("error.subWorkSheetNotExist", "生产子工单不存在");
        }
        SubWorkSheet subWorkSheet = subWorkSheetOptional.get();
        if (subWorkSheet.getStatus() != ConstantsEnum.WORK_SHEET_STATIC_DEVOTE.getCategoryName()) {
            throw new ResponseException("error.subWorkSheetStatusIsNotCreated", "子工单状态不是已下单");
        }
        subWorkSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_CANCEL.getCategoryName());
        subWorkSheetRepository.save(subWorkSheet);
        // 更新工单的分单状态
        WorkSheet workSheet = subWorkSheet.getWorkSheet();
        //更新工单的子工单个数以及完成个数(包括正常、异常)
        this.updateWorkSheetSubWsNumberInfo(workSheet);
        long number = Optional.ofNullable(subWorkSheetRepository.statsNumberByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO)).orElse(Constants.LONG_ZERO);
        workSheet.setGenerateSubWsStatus(number == Constants.LONG_ZERO ? Constants.INT_ZERO : number < workSheet.getNumber() ? Constants.INT_ONE : Constants.INT_TWO);
        //更新在制看板信息
        workSheetStepStatisticsServices[0].deleteWorkSheetStepStatisticsInfo(workSheet, List.of(subWorkSheet), Boolean.TRUE);
        workSheetRepository.save(workSheet);
    }

    /**
     * 更新工单的子工单个数以及完成个数(包括正常、异常)
     *
     * @param workSheet 工单
     */
    public void updateWorkSheetSubWsNumberInfo(WorkSheet workSheet) {
        //计算非取消子工单的个数
        Long devoteNum = subWorkSheetRepository.countByWorkSheetIdAndStatusNotAndDeleted(workSheet.getId(), ConstantsEnum.WORK_SHEET_STATIC_CANCEL.getCategoryName(), net.airuima.constant.Constants.LONG_ZERO);
        Long compNum = subWorkSheetRepository.countFinishNumberByWorkSheetIdAndDeleted(workSheet.getId(), net.airuima.constant.Constants.LONG_ZERO);
        int devoteNumber = Objects.nonNull(devoteNum)?devoteNum.intValue(): net.airuima.constant.Constants.INT_ZERO;
        //计算异常结单及正常结单的子工单个数
        int compNumber = Objects.nonNull(compNum)?compNum.intValue(): net.airuima.constant.Constants.INT_ZERO;
        List<WsRework> wsReworkList = wsReworkRepository.findByOriginalWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        if (!CollectionUtils.isEmpty(wsReworkList)) {
            devoteNum = subWorkSheetRepository.countByWorkSheetIdInAndStatusNotAndDeleted(wsReworkList.stream().map(wsRework -> wsRework.getReworkWorkSheet().getId()).collect(Collectors.toList()), ConstantsEnum.WORK_SHEET_STATIC_CANCEL.getCategoryName(), net.airuima.constant.Constants.LONG_ZERO);
            devoteNumber = devoteNumber + (Objects.nonNull(devoteNum)?devoteNum.intValue(): net.airuima.constant.Constants.INT_ZERO);
            compNum = subWorkSheetRepository.countFinishNumberByWorkSheetIdInAndDeleted(wsReworkList.stream().map(wsRework -> wsRework.getReworkWorkSheet().getId()).collect(Collectors.toList()), net.airuima.constant.Constants.LONG_ZERO);
            compNumber = compNumber + (Objects.nonNull(compNum)?compNum.intValue(): net.airuima.constant.Constants.INT_ZERO);
        }
        workSheet.setStepNumber(devoteNumber).setStepCompNumber(compNumber);
        //如果是返工单则需要重新计算原始工单的子工单个数及完成个数
        if (workSheet.getCategory() == WsEnum.ONLINE_RE_WS.getCategory()) {
            Optional<WsRework> wsReworkOptional = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(workSheet.getId(), net.airuima.constant.Constants.LONG_ZERO);
            wsReworkOptional.ifPresent(wsRework -> {
                WorkSheet originWorkSheet = wsRework.getOriginalWorkSheet();
                //计算非取消子工单的个数
                Long devoteNumTemp = subWorkSheetRepository.countByWorkSheetIdAndStatusNotAndDeleted(originWorkSheet.getId(), ConstantsEnum.WORK_SHEET_STATIC_CANCEL.getCategoryName(), net.airuima.constant.Constants.LONG_ZERO);
                int devoteOriginWorkSheetNumber = Objects.nonNull(devoteNumTemp)?devoteNumTemp.intValue(): net.airuima.constant.Constants.INT_ZERO;
                //计算异常结单及正常结单的子工单个数
                Long compNumTemp = subWorkSheetRepository.countFinishNumberByWorkSheetIdAndDeleted(originWorkSheet.getId(), net.airuima.constant.Constants.LONG_ZERO);
                int compOriginWorkSheetNumber = Objects.nonNull(compNumTemp)?compNumTemp.intValue(): net.airuima.constant.Constants.INT_ZERO;
                List<WsRework> wsReworks = wsReworkRepository.findByOriginalWorkSheetIdAndDeleted(originWorkSheet.getId(), net.airuima.constant.Constants.LONG_ZERO);
                if (!CollectionUtils.isEmpty(wsReworks)) {
                    devoteNumTemp = subWorkSheetRepository.countByWorkSheetIdInAndStatusNotAndDeleted(wsReworks.stream().map(wsReworkTemp -> wsReworkTemp.getReworkWorkSheet().getId()).collect(Collectors.toList()), ConstantsEnum.WORK_SHEET_STATIC_CANCEL.getCategoryName(), net.airuima.constant.Constants.LONG_ZERO);
                    devoteOriginWorkSheetNumber = devoteOriginWorkSheetNumber + (Objects.nonNull(devoteNumTemp)?devoteNumTemp.intValue(): net.airuima.constant.Constants.INT_ZERO);
                    compNumTemp =subWorkSheetRepository.countFinishNumberByWorkSheetIdInAndDeleted(wsReworks.stream().map(wsReworkTemp -> wsReworkTemp.getReworkWorkSheet().getId()).collect(Collectors.toList()), net.airuima.constant.Constants.LONG_ZERO);
                    compOriginWorkSheetNumber = compOriginWorkSheetNumber +  (Objects.nonNull(compNumTemp)?compNumTemp.intValue(): net.airuima.constant.Constants.INT_ZERO);
                }
                originWorkSheet.setStepNumber(devoteOriginWorkSheetNumber).setStepCompNumber(compOriginWorkSheetNumber);
                workSheetRepository.save(originWorkSheet);
            });
        }
    }

    /**
     * 删除子工单时，需要删除对应的工单状态记录
     *
     * @param id 子工单id
     */
    public void logicDeleteById(Long id) {
        BatchWorkDetail batchWorkDetail = batchWorkDetailRepository.findTop1BySubWorkSheetIdAndDeletedOrderByIdDesc(id, Constants.LONG_ZERO).orElse(null);
        if(Objects.nonNull(batchWorkDetail)){
            throw new ResponseException("error.subWorkSheetAlreadyProduced", "子工单(" + batchWorkDetail.getSubWorkSheet().getSerialNumber() + ")已投产，不可删除");
        }
        WorkSheetSn workSheetSn = workSheetSnRepository.findTop1BySubWorkSheetIdAndDeleted(id, Constants.LONG_ZERO);
        if(Objects.nonNull(workSheetSn)){
            throw new ResponseException("error.subWorkSheetBindSn", "子工单(" + workSheetSn.getSubWorkSheet().getSerialNumber() + ")已预绑定SN，不可删除");
        }
        Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findByIdAndDeleted(id, Constants.LONG_ZERO);
        subWorkSheetOptional.ifPresent(subWorkSheet -> {
            workSheetStepStatisticsServices[0].deleteWorkSheetStepStatisticsInfo(subWorkSheet.getWorkSheet(), Collections.singletonList(subWorkSheet), Boolean.TRUE);
            subWorkSheetRepository.logicDelete(subWorkSheet);
        });
    }

    /**
     * 子工单编码获取子工单
     *
     * @param serialNumber 子工单编码
     * @return SubWorkSheet
     */
    public SubWorkSheet findBySerialNumber(String serialNumber) {
        return subWorkSheetRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO).orElse(null);
    }
}
