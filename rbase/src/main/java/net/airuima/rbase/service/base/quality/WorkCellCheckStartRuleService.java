package net.airuima.rbase.service.base.quality;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.constant.Constants;
import net.airuima.dto.ExportDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.WorkCellStartCheckEnum;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.quality.WorkCellCheckStartRule;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.dto.bom.MaterialAttributeDTO;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.dto.organization.SupplierDTO;
import net.airuima.rbase.dto.qms.VarietyDTO;
import net.airuima.rbase.proxy.bom.RbaseBomProxy;
import net.airuima.rbase.proxy.bom.RbaseMaterialProxy;
import net.airuima.rbase.proxy.organization.RbaseClientProxy;
import net.airuima.rbase.proxy.organization.RbaseSupplierProxy;
import net.airuima.rbase.proxy.qms.RbaseVarietyProxy;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.repository.base.quality.WorkCellCheckStartRuleRepository;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.web.rest.base.quality.dto.WorkCellCheckStartRuleImportDTO;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import net.airuima.util.ValidateUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 工位检测配置Service
 * <AUTHOR>
 * @date 2021-03-22
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkCellCheckStartRuleService extends CommonJpaService<WorkCellCheckStartRule> {
    private static final String WORK_CELL_CHECK_START_RULE_ENTITY_GRAPH = "workCellCheckStartRuleEntityGraph";
    private final WorkCellCheckStartRuleRepository workCellCheckConfigRepository;

    @Autowired
    private RbaseVarietyProxy varietyRepository;

    @Autowired
    private WorkCellRepository workCellRepository;

    @Autowired
    private WorkFlowRepository workFlowRepository;

    @Autowired
    private StepRepository stepRepository;

    @Autowired
    private RbaseMaterialProxy rbaseMaterialProxy;

    @Autowired
    private RbaseSupplierProxy rbaseSupplierProxy;

    @Autowired
    private RbaseClientProxy rbaseClientProxy;
    @Autowired
    private RbaseBomProxy rbaseBomProxy;

    public WorkCellCheckStartRuleService(WorkCellCheckStartRuleRepository workCellCheckConfigRepository) {
        this.workCellCheckConfigRepository = workCellCheckConfigRepository;
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<WorkCellCheckStartRule> find(Specification<WorkCellCheckStartRule> spec, Pageable pageable) {
        return workCellCheckConfigRepository.findAll(spec,pageable,new NamedEntityGraph(WORK_CELL_CHECK_START_RULE_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public List<WorkCellCheckStartRule> find(Specification<WorkCellCheckStartRule> spec) {
        return workCellCheckConfigRepository.findAll(spec,new NamedEntityGraph(WORK_CELL_CHECK_START_RULE_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<WorkCellCheckStartRule> findAll(Pageable pageable) {
        return workCellCheckConfigRepository.findAll(pageable,new NamedEntityGraph(WORK_CELL_CHECK_START_RULE_ENTITY_GRAPH));
    }

    /**
     * 保存规则数据
     * @param entity 待更新数据
     */
    public WorkCellCheckStartRule saveInstance(WorkCellCheckStartRule entity){
        Long varietyObjId = null == entity.getVarietyObj()?null:entity.getVarietyObj().getId();
        Optional<WorkCellCheckStartRule> workCellCheckStartRuleOptional = Optional.empty();
        // 首检和巡检
        if(entity.getCategory() == WorkCellStartCheckEnum.FIRST_INSPECTION.getCategory() || entity.getCategory() == WorkCellStartCheckEnum.IPQC_INSPECTION.getCategory()){
            workCellCheckStartRuleOptional = workCellCheckConfigRepository.findByWorkCellIdAndCategoryAndFlagAndVarietyIdAndDeleted(entity.getWorkCell().getId(), entity.getCategory(),entity.getFlag(),varietyObjId,Constants.LONG_ZERO);
            return saveWorkCellCheckStartRule(entity, workCellCheckStartRuleOptional);
        }
        // 抽检和终检
        if(entity.getCategory() == WorkCellStartCheckEnum.SIMPLE_INSPECTION.getCategory() || entity.getCategory() == WorkCellStartCheckEnum.LAST_INSPECTION.getCategory() || entity.getCategory() == WorkCellStartCheckEnum.LQC_INSPECTION.getCategory()){
            workCellCheckStartRuleOptional = workCellCheckConfigRepository.findByWorkFlowIdAndStepIdAndVarietyIdAndCategoryAndDeleted(entity.getWorkFlow().getId(),entity.getStep().getId(),varietyObjId,entity.getCategory(),Constants.LONG_ZERO);
            return saveWorkCellCheckStartRule(entity, workCellCheckStartRuleOptional);
        }
        // 来料检
        if (entity.getCategory() == WorkCellStartCheckEnum.IQC_INSPECTION.getCategory()) {
            if (Objects.nonNull(entity.getId())) {
                Optional<WorkCellCheckStartRule> ruleOptional = workCellCheckConfigRepository.findByCategoryAndTargetAndAttributeIdAndMaterialIdAndSupplierIdAndClientIdAndDeleted(entity.getCategory(), entity.getTarget(), entity.getAttributeId(), entity.getMaterialId(), entity.getSupplierId(), entity.getClientId(), Constants.LONG_ZERO);
                // 没有找到该组合 直接添加
                if (ruleOptional.isEmpty()) {
                    return this.save(entity);
                }
                // 已经存在该组合 提示
                if (!ruleOptional.get().getId().equals(entity.getId())) {
                    throw new ResponseException("error.workCellCheckStartRuleRepeat", "记录已存在,请勿重复添加");
                } else {
                    this.save(entity);
                }
            }
        }
        return entity;
    }

    /**
     * 批量创建检验发起规则
     * @param entity  待创建规则
     * @return java.util.List<net.airuima.rbase.domain.base.quality.WorkCellCheckStartRule> 检测发起规则
     */
    public List<WorkCellCheckStartRule> bathCreate(WorkCellCheckStartRule entity) {
        List<WorkCellCheckStartRule> list = Lists.newArrayList();
        // 来料检
        if(entity.getCategory() == WorkCellStartCheckEnum.IQC_INSPECTION.getCategory()){
            // 修改检验规则
            List<Long> materialIdList = entity.getMaterialIdList();
            if (CollectionUtils.isEmpty(materialIdList)) {
                return  list;
            }
            materialIdList.forEach(materialId -> {
                Optional<WorkCellCheckStartRule> ruleOptional = workCellCheckConfigRepository.findByCategoryAndTargetAndAttributeIdAndMaterialIdAndSupplierIdAndClientIdAndDeleted(entity.getCategory(), entity.getTarget(), entity.getAttributeId(), materialId, entity.getSupplierId(), entity.getClientId(), Constants.LONG_ZERO);
                // 没有找到该组合 直接添加
                if (ruleOptional.isEmpty()) {
                    WorkCellCheckStartRule workCellCheckStartRule = new WorkCellCheckStartRule();
                    BeanUtils.copyProperties(entity, workCellCheckStartRule);
                    workCellCheckStartRule.setMaterialId(materialId).setDeleted(Constants.LONG_ZERO);
                    workCellCheckConfigRepository.save(workCellCheckStartRule);
                    list.add(workCellCheckStartRule);
                }
                // 已经存在该组合 提示
                if (ruleOptional.isPresent() && !ruleOptional.get().getId().equals(entity.getId())) {
                    throw new ResponseException("error.workCellCheckStartRuleRepeat", "记录已存在,请勿重复添加");
                }
            });
            return list;

        }
        return null;
    }

    /**
     * 保存检测发起规则
     * @param entity 待保存规则
     * @param workCellCheckStartRuleOptional 已存在规则
     * @return net.airuima.rbase.domain.base.quality.WorkCellCheckStartRule 检测发起规则
     */
    private WorkCellCheckStartRule saveWorkCellCheckStartRule(WorkCellCheckStartRule entity, Optional<WorkCellCheckStartRule> workCellCheckStartRuleOptional) {
        if(null == entity.getId() &&  workCellCheckStartRuleOptional.isPresent()){
            throw new ResponseException("error.workCellCheckStartRuleRepeat", "记录已存在,请勿重复添加");
        }
        if(workCellCheckStartRuleOptional.isPresent() && !workCellCheckStartRuleOptional.get().getId().equals(entity.getId())){
            throw new ResponseException("error.workCellCheckStartRuleRepeat", "记录已存在,请勿重复添加");
        }
        return this.save(entity);
    }


    /**
     * 检测发起规则导入
     *
     * @param file 检测发起规则导入数据文件
     */
    public void customImport(MultipartFile file) throws Exception {
        ImportParams params = new ImportParams();
        params.setTitleRows(0);
        params.setHeadRows(1);
        ExcelImportResult<WorkCellCheckStartRuleImportDTO> excelImportResult = ExcelImportUtil.importExcelMore(file.getInputStream(), WorkCellCheckStartRuleImportDTO.class, params);
        // 得到导入文件转换成的列表数据
        List<WorkCellCheckStartRuleImportDTO> sampleCaseImportDTOList = excelImportResult.getList();
        // 记录excel行数【计数器】
        AtomicInteger count = new AtomicInteger(Constants.INT_ONE);
        List<WorkCellCheckStartRule> workCellCheckStartRuleList = new ArrayList<>();
        // 处理表格数据
        for (WorkCellCheckStartRuleImportDTO importDTO : sampleCaseImportDTOList) {
            WorkCellCheckStartRule workCellCheckStartRule = new WorkCellCheckStartRule(importDTO);
            //验证表格数据，并赋值
            validTableExcel(importDTO,count,workCellCheckStartRule);
            if(Objects.nonNull(workCellCheckStartRule)){
                workCellCheckStartRuleList.add(workCellCheckStartRule);
            }
            count.getAndIncrement();
        }
        // 保存数据
        if(ValidateUtils.isValid(workCellCheckStartRuleList)) {
            workCellCheckStartRuleList.forEach(workCellCheckStartRule -> {
                if(workCellCheckStartRule.getCategory() != Constants.INT_FIVE){
                    Long varietyObjId = null == workCellCheckStartRule.getVarietyObj()?null:workCellCheckStartRule.getVarietyObj().getId();
                    Optional<WorkCellCheckStartRule> workCellCheckStartRuleOptional = workCellCheckStartRule.getCategory() == WorkCellStartCheckEnum.FIRST_INSPECTION.getCategory() || workCellCheckStartRule.getCategory() == WorkCellStartCheckEnum.IPQC_INSPECTION.getCategory()
                            ?workCellCheckConfigRepository.findByWorkCellIdAndCategoryAndFlagAndVarietyIdAndDeleted(workCellCheckStartRule.getWorkCell().getId(), workCellCheckStartRule.getCategory(),workCellCheckStartRule.getFlag(),varietyObjId,Constants.LONG_ZERO)
                            :workCellCheckConfigRepository.findByWorkFlowIdAndStepIdAndVarietyIdAndCategoryAndDeleted(workCellCheckStartRule.getWorkFlow().getId(),workCellCheckStartRule.getStep().getId(),varietyObjId,workCellCheckStartRule.getCategory(),Constants.LONG_ZERO);
                    workCellCheckStartRuleOptional.ifPresent(cellCheckStartRule -> workCellCheckStartRule.setId(cellCheckStartRule.getId()));
                    this.save(workCellCheckStartRule);
                }
                if(workCellCheckStartRule.getCategory() == Constants.INT_FIVE && Objects.nonNull(workCellCheckStartRule)){
                    Optional<WorkCellCheckStartRule> ruleOptional = workCellCheckConfigRepository.findByCategoryAndTargetAndAttributeIdAndMaterialIdAndSupplierIdAndClientIdAndDeleted(workCellCheckStartRule.getCategory(), workCellCheckStartRule.getTarget(), workCellCheckStartRule.getAttributeId(), workCellCheckStartRule.getMaterialId(),workCellCheckStartRule.getSupplierId(), workCellCheckStartRule.getClientId(), Constants.LONG_ZERO);
                    // 没有找到该组合 直接添加
                    if(ruleOptional.isEmpty()){
                        this.save(workCellCheckStartRule);
                    }
                }

            });
        }
    }

    /**
     * 验证表格数据，并赋值
     * @param workCellCheckStartRuleImportDto 导入数据
     * @param count 行号
     * @param workCellCheckStartRule 发起检测规则
     * <AUTHOR>
     * @date  2023/5/18
     * @return void
     */
    private void validTableExcel(WorkCellCheckStartRuleImportDTO workCellCheckStartRuleImportDto,AtomicInteger count,WorkCellCheckStartRule workCellCheckStartRule){
        // 校验数据
        Integer category = workCellCheckStartRuleImportDto.getCategory();
        if (ObjectUtils.isEmpty(category)){
            throw new ResponseException("error.categoryIsNull", "第【" + count.get() + "】行出现问题，质检类型为空，请检查！！！");
        }
        if(category != Constants.INT_FIVE){
            // 是否首检/巡检
            boolean tag = category.equals(Constants.INT_ZERO) || category.equals(Constants.INT_ONE);
            // 设置发起类型
            workCellCheckStartRule.setTarget(tag ? Constants.INT_ZERO : Constants.INT_ONE);
            //检测项目类型
            if(ValidateUtils.isValid(workCellCheckStartRuleImportDto.getVarietyCode())){
                VarietyDTO variety =
                        varietyRepository.findByCodeAndIsEnableAndDeleted(workCellCheckStartRuleImportDto.getVarietyCode(), Boolean.TRUE, Constants.LONG_ZERO);
                if(ObjectUtils.isEmpty(variety)){
                    throw new ResponseException("error.varietyCodeNotExist", "第【" + count.get() + "】行出现问题，项目类型编码不存在，请检查！！！");
                }
                if(!variety.getIsEnable()){
                    throw new ResponseException("error.varietyCodeDisabled", "第【" + count.get() + "】行出现问题，项目类型已禁用！！！");
                }
                workCellCheckStartRule.setVarietyObj(variety);
            }
            if(tag){
                validFirstOrIpqcInspection(workCellCheckStartRuleImportDto, count, workCellCheckStartRule, category);
            }else {
                validOtherInspection(workCellCheckStartRuleImportDto, count, workCellCheckStartRule, category);
            }
        }
        if(category == Constants.INT_FIVE){
            String attributeCode = workCellCheckStartRuleImportDto.getAttributeCode();
            String clientCode = workCellCheckStartRuleImportDto.getClientCode();
            String materialCode = workCellCheckStartRuleImportDto.getMaterialCode();
            String supplierCode = workCellCheckStartRuleImportDto.getSupplierCode();
            workCellCheckStartRule.setTarget(Constants.INT_TWO);
            workCellCheckStartRule.setIsEnable(workCellCheckStartRuleImportDto.getIsEnable());
            VarietyDTO variety =
                    varietyRepository.findByCodeAndIsEnableAndDeleted(workCellCheckStartRuleImportDto.getVarietyCode(), Boolean.TRUE, Constants.LONG_ZERO);
            workCellCheckStartRule.setVarietyObj(variety);
            // 获取物料属性id
            Long attributeId = null;
            if(ValidateUtils.isValid(attributeCode)){
               attributeId =  Optional.ofNullable(rbaseBomProxy.findByCode(attributeCode)).map(MaterialAttributeDTO::getId).orElse(null);
            }
            // 获取物料id
            Long materialId = null;
            if(ValidateUtils.isValid(materialCode)){
                materialId =  rbaseMaterialProxy.findByCodeAndDeleted(materialCode,Constants.LONG_ZERO).map(MaterialDTO::getId).orElse(null);
            }
            // 获取供应商id
            Long supplierId = null;
            if(ValidateUtils.isValid(supplierCode)){
                supplierId =  Optional.ofNullable(rbaseSupplierProxy.findByCodeAndDeleted(supplierCode,Constants.LONG_ZERO)).map(SupplierDTO::getId).orElse(null);
            }
            Long clientId = null;
            if(ValidateUtils.isValid(clientCode)){
                clientId   =  Optional.ofNullable(rbaseClientProxy.findByCodeAndDeleted(clientCode,Constants.LONG_ZERO)).map(ClientDTO::getId).orElse(null);
            }
            if(materialId == null && attributeId == null && supplierId == null && clientId == null){
                workCellCheckStartRule = null;
            }else{
                workCellCheckStartRule.setAttributeId(attributeId).setMaterialId(materialId).setClientId(clientId).setSupplierId(supplierId);
            }


        }

    }

    /**
     * 验证表格数据
     * @param workCellCheckStartRuleImportDto 导入数据
     * @param count 行号
     * @param workCellCheckStartRule 发起检测规则
     * @param category 质检类型
     */
    private void validOtherInspection(WorkCellCheckStartRuleImportDTO workCellCheckStartRuleImportDto, AtomicInteger count, WorkCellCheckStartRule workCellCheckStartRule, Integer category) {
        Optional<WorkFlow> optionalWorkFlow = workFlowRepository.findByCodeAndDeleted(workCellCheckStartRuleImportDto.getWorkFlowCode(), Constants.LONG_ZERO);
        if(! optionalWorkFlow.isPresent()){
            throw new ResponseException("error.workFlowCodeNotExist", "第【" + count.get() + "】行出现问题，工艺路线不存在，请检查！！！");
        }
        if(!optionalWorkFlow.get().getIsEnable()){
            throw new ResponseException("error.workFlowDisabled", "第【" + count.get() + "】行出现问题，工艺路线已禁用！！！");
        }
        workCellCheckStartRule.setWorkFlow(optionalWorkFlow.get());
        Optional<Step> optionalStep = stepRepository.findByCode(workCellCheckStartRuleImportDto.getStepCode());
        if(! optionalStep.isPresent()){
            throw new ResponseException("error.stepCodeNotExist", "第【" + count.get() + "】行出现问题，工序不存在，请检查！！！");
        }
        if(!optionalStep.get().getIsEnable()){
            throw new ResponseException("error.stepDisabled", "第【" + count.get() + "】行出现问题，工序已禁用！！！");
        }
        workCellCheckStartRule.setStep(optionalStep.get());
        workCellCheckConfigRepository.findByWorkFlowIdAndStepIdAndVarietyIdAndCategoryAndDeleted(optionalWorkFlow.get().getId(), optionalStep.get().getId(), ObjectUtils.isEmpty(workCellCheckStartRule.getVarietyObj()) ? null : workCellCheckStartRule.getVarietyObj().getId(), category, Constants.LONG_ZERO)
                .ifPresent(wcsr -> {
                    workCellCheckStartRule.setId(wcsr.getId());
                    workCellCheckStartRule.setDeleted(Constants.LONG_ZERO);
                });
    }

    /**
     * 验证表格数据
     * @param workCellCheckStartRuleImportDto 导入数据
     * @param count 行号
     * @param workCellCheckStartRule 发起检测规则
     * @param category 质检类型
     */
    private void validFirstOrIpqcInspection(WorkCellCheckStartRuleImportDTO workCellCheckStartRuleImportDto, AtomicInteger count, WorkCellCheckStartRule workCellCheckStartRule, Integer category) {
        if(!ValidateUtils.isValid(workCellCheckStartRuleImportDto.getWorkCellCode())){
            throw new ResponseException("error.workCellCodeIsNull", "第【" + count.get() + "】行出现问题，工位为空，请检查！！！");
        }
        Optional<WorkCell> optionalWorkCell = workCellRepository.findByCodeAndDeleted(workCellCheckStartRuleImportDto.getWorkCellCode(), Constants.LONG_ZERO);
        if(! optionalWorkCell.isPresent()){
            throw new ResponseException("error.workCellCodeNotExist", "第【" + count.get() + "】行出现问题，工位不存在，请检查！！！");
        }
        if(!optionalWorkCell.get().getIsEnable()){
            throw new ResponseException("error.workCellCodeDisabled", "第【" + count.get() + "】行出现问题，工位已禁用！！！");
        }
        workCellCheckStartRule.setWorkCell(optionalWorkCell.get());
        // 检测时机校验
        if(ObjectUtils.isEmpty(workCellCheckStartRuleImportDto.getFlag())){
            throw new ResponseException("error.flagIsNull", "第【" + count.get() + "】行出现问题，检测时机为空，请检查！！！");
        }
        Integer flag = workCellCheckStartRuleImportDto.getFlag();
        if(flag.equals(Constants.INT_THREE) && ObjectUtils.isEmpty(workCellCheckStartRuleImportDto.getDuration())){
            throw new ResponseException("error.durationIsNull", "第【" + count.get() + "】行出现问题，检测周期为空，请检查！！！");
        } else if(flag.equals(Constants.INT_FOUR) && ObjectUtils.isEmpty(workCellCheckStartRuleImportDto.getSpecifyTime())){
            throw new ResponseException("error.specifyTimeIsNull", "第【" + count.get() + "】行出现问题，固定时间为空，请检查！！！");
        }
        workCellCheckConfigRepository.findByWorkCellIdAndCategoryAndFlagAndVarietyIdAndDeleted(workCellCheckStartRule.getWorkCell().getId(), category, flag, ObjectUtils.isEmpty(workCellCheckStartRule.getVarietyObj()) ? null : workCellCheckStartRule.getVarietyObj().getId(), Constants.LONG_ZERO)
                .ifPresent(wcsr -> {
                    workCellCheckStartRule.setId(wcsr.getId());
                    workCellCheckStartRule.setDeleted(Constants.LONG_ZERO);
                });
    }

    /**
     * 检测发起规则导出
     * @param response                      响应对象
     */
    public void exportTableExcel(List<WorkCellCheckStartRule> workCellCheckStartRuleList, ExportDTO exportDTO, HttpServletResponse response) throws IOException {
        if (ValidateUtils.isValid(workCellCheckStartRuleList)){
            List<WorkCellCheckStartRuleImportDTO> workCellCheckStartRuleImportDTOList = workCellCheckStartRuleList.stream().map(WorkCellCheckStartRuleImportDTO::new).collect(Collectors.toList());
            // 写入数据
            ExportParams exportParams = new ExportParams(null, exportDTO.getExcelTitle(), ExcelType.XSSF);
            if(org.apache.commons.lang3.StringUtils.isNotBlank(exportDTO.getExcelType()) && exportDTO.getExcelType().equals("xls")){
                exportParams = new ExportParams(null, exportDTO.getExcelTitle(), ExcelType.HSSF);
            }
            String prefix = org.apache.commons.lang3.StringUtils.isNotBlank(exportDTO.getExcelType()) && exportDTO.getExcelType().equals("xls")?".xls":".xlsx";
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, WorkCellCheckStartRuleImportDTO.class, workCellCheckStartRuleImportDTOList);
            String fileName = URLEncoder.encode(exportDTO.getExcelTitle() + prefix, StandardCharsets.UTF_8);
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);
            ServletOutputStream out = response.getOutputStream();
            workbook.write(out);
            workbook.close();
            out.close();
        }
    }


}
