package net.airuima.rbase.service.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.constant.StepCategoryEnum;
import net.airuima.rbase.domain.base.pedigree.*;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.process.WorkFlowStep;
import net.airuima.rbase.domain.base.quality.StepWarningStandard;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.base.quality.UnqualifiedItemWarningStandard;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.base.wearingpart.PedigreeStepWearingPartGroup;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.rbase.domain.procedure.batch.CustomPedigreeStep;
import net.airuima.rbase.domain.procedure.batch.WsStep;
import net.airuima.rbase.dto.process.StepDTO;
import net.airuima.rbase.dto.rule.DictionaryDTO;
import net.airuima.rbase.dto.skill.SkillDTO;
import net.airuima.rbase.proxy.rule.RbaseDictionaryProxy;
import net.airuima.rbase.proxy.rule.RbaseSysCodeProxy;
import net.airuima.rbase.proxy.skill.RbaseSkillProxy;
import net.airuima.rbase.repository.base.pedigree.*;
import net.airuima.rbase.repository.base.process.WorkFlowStepRepository;
import net.airuima.rbase.repository.base.quality.StepWarningStandardRepository;
import net.airuima.rbase.repository.base.quality.UnqualifiedItemWarningStandardRepository;
import net.airuima.rbase.repository.procedure.aps.WsReworkRepository;
import net.airuima.rbase.repository.procedure.batch.CustomPedigreeStepRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepUnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.quality.InspectTaskRepository;
import net.airuima.rbase.service.base.pedigree.PedigreeStepService;
import net.airuima.rbase.service.base.pedigree.PedigreeStepUnqualifiedItemService;
import net.airuima.rbase.service.base.wearingpart.PedigreeStepWearingPartGroupService;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.util.ResponseException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 通用Service工具类
 *
 * <AUTHOR>
 * @date 2021-01-12
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class CommonService {
    private static String DIC_PREFIX = "dic_";
    private final PedigreeRepository pedigreeRepository;
    private final PedigreeStepRepository pedigreeStepRepository;
    private final PedigreeWorkFlowRepository pedigreeWorkFlowRepository;
    private final PedigreeStepMaterialRuleRepository pedigreeStepMaterialRuleRepository;
    private final PedigreeConfigRepository pedigreeConfigRepository;
    private final UnqualifiedItemWarningStandardRepository unqualifiedItemWarningStandardRepository;
    private final StepWarningStandardRepository stepWarningStandardRepository;
    private final PedigreeStepCheckRuleRepository pedigreeStepCheckRuleRepository;
    private final PedigreeSnReuseConfigRepository pedigreeSnReuseConfigRepository;
    private final PedigreeReworkWorkFlowRepository pedigreeReworkWorkFlowRepository;
    private final CustomPedigreeStepRepository customPedigreeStepRepository;
    private final PedigreeStepSpecificationRepository pedigreeStepSpecificationRepository;
    @Autowired
    private PedigreeStepIntervalConfigRepository pedigreeStepIntervalConfigRepository;
    @Autowired
    private WsStepUnqualifiedItemRepository wsStepUnqualifiedItemRepository;
    @Autowired
    private WsReworkRepository wsReworkRepository;
    @Autowired
    private RbaseSkillProxy rbaseSkillProxy;
    @Autowired
    private RbaseSysCodeProxy rbaseSysCodeProxy;
    @Autowired
    private PedigreeStepWearingPartGroupService pedigreeStepWearingPartGroupService;
    @Autowired
    private RbaseDictionaryProxy rbaseDictionaryProxy;
    @Autowired
    private InspectTaskRepository inspectTaskRepository;
    @Autowired
    private WorkFlowStepRepository workFlowStepRepository;
    @Autowired
    private PedigreeStepService pedigreeStepService;

    @Autowired
    private WsStepRepository wsStepRepository;

    @Autowired
    private PedigreeStepUnqualifiedItemService pedigreeStepUnqualifiedItemService;

    private final PedigreeStepUnqualifiedItemRepository pedigreeStepUnqualifiedItemRepository;


    public CommonService(PedigreeRepository pedigreeRepository,
                         PedigreeStepRepository pedigreeStepRepository,
                         PedigreeWorkFlowRepository pedigreeWorkFlowRepository,
                         PedigreeStepMaterialRuleRepository pedigreeStepMaterialRuleRepository,
                         PedigreeConfigRepository pedigreeConfigRepository,
                         UnqualifiedItemWarningStandardRepository unqualifiedItemWarningStandardRepository,
                         StepWarningStandardRepository stepWarningStandardRepository,
                         PedigreeStepCheckRuleRepository pedigreeStepCheckRuleRepository,
                         PedigreeSnReuseConfigRepository pedigreeSnReuseConfigRepository,
                         PedigreeReworkWorkFlowRepository pedigreeReworkWorkFlowRepository,
                         CustomPedigreeStepRepository customPedigreeStepRepository,
                         PedigreeStepSpecificationRepository pedigreeStepSpecificationRepository,
                         PedigreeStepUnqualifiedItemRepository pedigreeStepUnqualifiedItemRepository) {
        this.pedigreeRepository = pedigreeRepository;
        this.pedigreeStepRepository = pedigreeStepRepository;
        this.pedigreeWorkFlowRepository = pedigreeWorkFlowRepository;
        this.pedigreeStepMaterialRuleRepository = pedigreeStepMaterialRuleRepository;
        this.pedigreeConfigRepository = pedigreeConfigRepository;
        this.unqualifiedItemWarningStandardRepository = unqualifiedItemWarningStandardRepository;
        this.stepWarningStandardRepository = stepWarningStandardRepository;
        this.pedigreeStepCheckRuleRepository = pedigreeStepCheckRuleRepository;
        this.pedigreeSnReuseConfigRepository = pedigreeSnReuseConfigRepository;
        this.pedigreeReworkWorkFlowRepository = pedigreeReworkWorkFlowRepository;
        this.customPedigreeStepRepository = customPedigreeStepRepository;
        this.pedigreeStepSpecificationRepository = pedigreeStepSpecificationRepository;
        this.pedigreeStepUnqualifiedItemRepository = pedigreeStepUnqualifiedItemRepository;
    }

    /**
     * 获取系统配置的投产粒度(子工单或者工单)
     * @return boolean
     */
    public boolean subWsProductionMode(){
        DictionaryDTO dictionaryDTO = rbaseDictionaryProxy.findByCodeAndDeleted(Constants.KEY_PRODUCTION_MODE,Constants.LONG_ZERO).orElse(null);
        boolean subWsProductionMode = Objects.isNull(dictionaryDTO) || Boolean.parseBoolean(dictionaryDTO.getData());
        return subWsProductionMode;
    }

    /**
     * 根据客户代码 产品谱系与工序获取工序管控配置
     *
     * @param clientId 客户代码
     * @param pedigree 产品谱系
     * @param step     工序
     * @return StepDTO
     * <AUTHOR>
     * @date 2021-01-12
     **/
    @Transactional(readOnly = true)
    public StepDTO findPedigreeStepConfig(Long clientId, Pedigree pedigree, WorkFlow workFlow, Step step) {
        //递归获取产品谱系所有父级ID集合
        List<Long> pedigreeIdList = this.getAllParent(pedigree);
        if (org.springframework.util.CollectionUtils.isEmpty(pedigreeIdList)) {
            return null;
        }
        List<PedigreeStep> pedigreeSteps = pedigreeStepRepository.findByPedigreeIdInAndWorkFlowIdAndStepIdAndClientIdAndEnableAndDeleted(pedigreeIdList, workFlow.getId(), step.getId(),clientId, Boolean.TRUE, Constants.LONG_ZERO);
        if (org.springframework.util.CollectionUtils.isEmpty(pedigreeSteps)) {
            return null;
        }
        List<PedigreeStep> pedigreeStepListSort = new ArrayList<PedigreeStep>();
        //1. 获取优先级排序最高集合
        PedigreeStep pedigreeStep = pedigreeSteps.stream().sorted(Comparator.comparing(i -> i.getPriorityElementConfig().getPriority())).findFirst().orElseThrow(() -> new ResponseException("error.pedigreeStepNotExist", "工序配置不存在"));
        pedigreeSteps = pedigreeSteps.stream().filter(i -> i.getPriorityElementConfig().getPriority() == pedigreeStep.getPriorityElementConfig().getPriority()).collect(Collectors.toList());
        if (pedigreeSteps.size() == Constants.INT_ONE) {
            return new StepDTO(pedigreeSteps.get(Constants.INT_ZERO));
        }
        //2. 对产品谱系进行排序（有可能产品谱系为空）
        List<PedigreeStep> pedigreeStepListPedigreeNull = pedigreeSteps.stream().filter(i -> Objects.isNull(i.getPedigree())).collect(Collectors.toList());
        List<PedigreeStep> pedigreeStepListPedigreeNotNull = pedigreeSteps.stream().filter(i -> !Objects.isNull(i.getPedigree())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(pedigreeStepListPedigreeNull)) {
            pedigreeStepListSort.addAll(pedigreeStepListPedigreeNull);
        }
        if (!CollectionUtils.isEmpty(pedigreeStepListPedigreeNotNull)) {
            //根据产品谱系类型进行正序排序（此时“预警条件”优先级均相同）
            int maxPedigreeLevel = pedigreeStepListPedigreeNotNull.stream().mapToInt(i -> i.getPedigree().getType()).max().getAsInt();
            pedigreeStepListPedigreeNotNull = pedigreeStepListPedigreeNotNull.stream().filter(i -> i.getPedigree().getType()==maxPedigreeLevel).collect(Collectors.toList());
            pedigreeStepListSort.addAll(pedigreeStepListPedigreeNotNull);
        }
        if (!CollectionUtils.isEmpty(pedigreeStepListSort)) {
            return new StepDTO(pedigreeStepListSort.get(Constants.INT_ZERO));
        }
        return null;
    }


    @Transactional(readOnly = true)
    public PedigreeStep findPedigreeStep(Long clientId, Pedigree pedigree, WorkFlow workFlow, Step step) {
        //递归获取产品谱系所有父级ID集合
        List<Long> pedigreeIdList = this.getAllParent(pedigree);
        if (org.springframework.util.CollectionUtils.isEmpty(pedigreeIdList)) {
            return null;
        }
        List<PedigreeStep> pedigreeSteps = pedigreeStepRepository.findByPedigreeIdInAndWorkFlowIdAndStepIdAndClientIdAndEnableAndDeleted(pedigreeIdList, workFlow.getId(), step.getId(), clientId,Boolean.TRUE, Constants.LONG_ZERO);
        if (org.springframework.util.CollectionUtils.isEmpty(pedigreeSteps)) {
            return null;
        }
        List<PedigreeStep> pedigreeStepListSort = new ArrayList<PedigreeStep>();
        //1. 获取优先级排序最高集合
        PedigreeStep pedigreeStep = pedigreeSteps.stream().sorted(Comparator.comparing(i -> i.getPriorityElementConfig().getPriority())).findFirst().orElseThrow(() -> new ResponseException("error.pedigreeStepNotExist", "工序配置不存在"));
        pedigreeSteps = pedigreeSteps.stream().filter(i -> i.getPriorityElementConfig().getPriority() == pedigreeStep.getPriorityElementConfig().getPriority()).collect(Collectors.toList());
        if (pedigreeSteps.size() == Constants.INT_ONE) {
            return pedigreeSteps.get(Constants.INT_ZERO);
        }
        //2. 对产品谱系进行排序（有可能产品谱系为空）
        List<PedigreeStep> pedigreeStepListPedigreeNull = pedigreeSteps.stream().filter(i -> Objects.isNull(i.getPedigree())).collect(Collectors.toList());
        List<PedigreeStep> pedigreeStepListPedigreeNotNull = pedigreeSteps.stream().filter(i -> !Objects.isNull(i.getPedigree())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(pedigreeStepListPedigreeNull)) {
            pedigreeStepListSort.addAll(pedigreeStepListPedigreeNull);
        }
        if (!CollectionUtils.isEmpty(pedigreeStepListPedigreeNotNull)) {
            //根据产品谱系类型进行正序排序（此时“预警条件”优先级均相同）
            int maxPedigreeLevel = pedigreeStepListPedigreeNotNull.stream().mapToInt(i -> i.getPedigree().getType()).max().getAsInt();
            pedigreeStepListPedigreeNotNull = pedigreeStepListPedigreeNotNull.stream().filter(i -> i.getPedigree().getType()==maxPedigreeLevel).collect(Collectors.toList());
            pedigreeStepListSort.addAll(pedigreeStepListPedigreeNotNull);
        }
        if (!CollectionUtils.isEmpty(pedigreeStepListSort)) {
            return pedigreeStepListSort.get(Constants.INT_ZERO);
        }
        return null;
    }

    /**
     * 获取产品谱系对应工序的不良项目信息
     * 1,优先获取产品谱系配置的工序不良,2,最后才会获取工序默认的不良信息
     *
     * @param pedigree 产品谱系
     * @param workFlowId 工艺路线
     * @param stepId     工序
     * @param clientId 客户id
     * @return List<UnqualifiedItem>
     * <AUTHOR>
     * @date 2021-01-12
     **/
    @Transactional(readOnly = true)
    public List<UnqualifiedItem> findPedigreeStepUnqualifiedItem(Pedigree pedigree, Long workFlowId, Long stepId, Long clientId) {
        List<Pedigree> pedigrees = Lists.newArrayList();
        List<Pedigree> parentPedigrees = Lists.newArrayList();
        this.findParentPedigree(pedigree, parentPedigrees);
        pedigrees.add(pedigree);
        if (ValidateUtils.isValid(parentPedigrees)) {
            pedigrees.addAll(parentPedigrees);
        }
        List<Long> pedigreeIdList = pedigrees.stream().map(Pedigree::getId).collect(Collectors.toList());
        List<PedigreeStepUnqualifiedItem> pedigreeStepUnqualifiedItemList = pedigreeStepUnqualifiedItemRepository.findByPedigreeIdInAndWorkFlowIdAndStepIdAndAndClientIdAndIsEnableAndDeleted(
                pedigreeIdList, workFlowId, stepId, clientId, Boolean.TRUE, Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(pedigreeStepUnqualifiedItemList)) {
            return Lists.newArrayList();
        }
        List<PedigreeStepUnqualifiedItem> pedigreeStepUnqualifiedItemListSort = new ArrayList<PedigreeStepUnqualifiedItem>();
        //1. 获取优先级排序最高集合
        PedigreeStepUnqualifiedItem pedigreeStepUnqualifiedItem = pedigreeStepUnqualifiedItemList.stream().sorted(Comparator.comparing(i -> i.getPriorityElementConfig().getPriority())).findFirst().orElseThrow(() -> new ResponseException("error.pedigreeStepUnqualifiedItemNotExist", "工序不良项目配置不存在"));
        pedigreeStepUnqualifiedItemList = pedigreeStepUnqualifiedItemList.stream().filter(i -> i.getPriorityElementConfig().getPriority() == pedigreeStepUnqualifiedItem.getPriorityElementConfig().getPriority()).collect(Collectors.toList());
        if (pedigreeStepUnqualifiedItemList.size() == Constants.INT_ONE) {
            return pedigreeStepUnqualifiedItemList.stream().map(PedigreeStepUnqualifiedItem::getUnqualifiedItem).distinct().collect(Collectors.toList());
        }
        //2. 对产品谱系进行排序（有可能产品谱系为空）
        List<PedigreeStepUnqualifiedItem> pedigreeStepUnqualifiedItemListPedigreeNull = pedigreeStepUnqualifiedItemList.stream().filter(i -> Objects.isNull(i.getPedigree())).collect(Collectors.toList());
        List<PedigreeStepUnqualifiedItem> pedigreeStepUnqualifiedItemListPedigreeNotNull = pedigreeStepUnqualifiedItemList.stream().filter(i -> !Objects.isNull(i.getPedigree())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(pedigreeStepUnqualifiedItemListPedigreeNull)) {
            pedigreeStepUnqualifiedItemListSort.addAll(pedigreeStepUnqualifiedItemListPedigreeNull);
        }
        if (!CollectionUtils.isEmpty(pedigreeStepUnqualifiedItemListPedigreeNotNull)) {
            int maxPedigreeLevel = pedigreeStepUnqualifiedItemListPedigreeNotNull.stream().mapToInt(pedigreeStepUnqualifiedItem1 -> pedigreeStepUnqualifiedItem1.getPedigree().getType()).max().getAsInt();
            //根据产品谱系类型进行正序排序（此时“预警条件”优先级均相同）
            pedigreeStepUnqualifiedItemListPedigreeNotNull = pedigreeStepUnqualifiedItemListPedigreeNotNull.stream().filter(i->i.getPedigree().getType()==maxPedigreeLevel).collect(Collectors.toList());
            pedigreeStepUnqualifiedItemListSort.addAll(pedigreeStepUnqualifiedItemListPedigreeNotNull);
        }
        if (!CollectionUtils.isEmpty(pedigreeStepUnqualifiedItemListSort)) {
            return pedigreeStepUnqualifiedItemListSort.stream().map(PedigreeStepUnqualifiedItem::getUnqualifiedItem).distinct().collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 按照优先级获取产品谱系工序技术指标和sop
     *
     * @param pedigreeId 产品谱系主键id
     * @param workFlowId 工艺路线主键id
     * @param stepId     工序主键id
     * @param clientId 客户主键id
     * @return PedigreeStepSpecification
     */
    @Transactional(readOnly = true)
    public PedigreeStepSpecification findPedigreeStepSpecification(Long pedigreeId, Long workFlowId, Long stepId, Long clientId) {
        Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByIdAndDeleted(pedigreeId, Constants.LONG_ZERO);
        if (!pedigreeOptional.isPresent()) {
            return null;
        }
        Pedigree pedigree = pedigreeOptional.get();
        List<Pedigree> pedigrees = Lists.newArrayList();
        List<Pedigree> parentPedigrees = Lists.newArrayList();
        this.findParentPedigree(pedigree, parentPedigrees);
        pedigrees.add(pedigree);
        if (ValidateUtils.isValid(parentPedigrees)) {
            pedigrees.addAll(parentPedigrees);
        }
        List<Long> pedigreeIdList = pedigrees.stream().map(Pedigree::getId).toList();
        List<PedigreeStepSpecification> pedigreeStepSpecificationList = pedigreeStepSpecificationRepository.findByPedigreeIdInAndWorkFlowIdAndStepIdAndClientIdAndDeleted(pedigreeIdList,workFlowId, stepId , clientId, Constants.LONG_ZERO);
        if(CollectionUtils.isEmpty(pedigreeStepSpecificationList)){
            return null;
        }
        // 只有一个直接返回
        if(pedigreeStepSpecificationList.size() == Constants.INT_ONE){
            return pedigreeStepSpecificationList.get(Constants.INT_ZERO);
        }
        //  获取优先级排序最高集合
        PedigreeStepSpecification pedigreeStepSpecification = pedigreeStepSpecificationList.stream().sorted(Comparator.comparing(i -> i.getPriorityElementConfig().getPriority())).findFirst()
                .orElseThrow(() -> new ResponseException("error.pedigreeStepSpecificationNotExist","产品谱系工序技术指标不存在"));
        pedigreeStepSpecificationList = pedigreeStepSpecificationList.stream().filter(i -> i.getPriorityElementConfig().getPriority() == pedigreeStepSpecification.getPriorityElementConfig().getPriority()).toList();
        // 只有一个直接返回
        if (pedigreeStepSpecificationList.size() == Constants.INT_ONE) {
            return pedigreeStepSpecificationList.get(Constants.INT_ZERO);
        }
        List<PedigreeStepSpecification> pedigreeStepSpecificationListSort = new ArrayList<>();
        // 对产品谱系进行排序（有可能产品谱系为空）
        List<PedigreeStepSpecification> pedigreeStepSpecificationListPedigreeNull = pedigreeStepSpecificationList.stream().filter(i -> Objects.isNull(i.getPedigree())).toList();
        List<PedigreeStepSpecification> pedigreeStepSpecificationListPedigreeNotNull = pedigreeStepSpecificationList.stream().filter(i -> !Objects.isNull(i.getPedigree())).toList();
        if (!CollectionUtils.isEmpty(pedigreeStepSpecificationListPedigreeNull)) {
            pedigreeStepSpecificationListSort.addAll(pedigreeStepSpecificationListPedigreeNull);
        }
        if (!CollectionUtils.isEmpty(pedigreeStepSpecificationListPedigreeNotNull)) {
            //根据产品谱系类型进行正序排序（此时“预警条件”优先级均相同）
            int maxPedigreeLevel = pedigreeStepSpecificationListPedigreeNotNull.stream().mapToInt(i -> i.getPedigree().getType()).max().getAsInt();
            pedigreeStepSpecificationListPedigreeNotNull =pedigreeStepSpecificationListPedigreeNotNull.stream().filter(i -> i.getPedigree().getType()==maxPedigreeLevel).collect(Collectors.toList());
            pedigreeStepSpecificationListSort.addAll(pedigreeStepSpecificationListPedigreeNotNull);
        }
        if (!CollectionUtils.isEmpty(pedigreeStepSpecificationListSort)) {
            return pedigreeStepSpecificationListSort.get(Constants.INT_ZERO);
        }
        return null;
    }


    /**
     * 获取产品谱系对应的工序上料规则
     *
     * @param clientId   客户代码
     * @param pedigreeId 产品谱系
     * @param stepId     工序
     * @param workFlowId 工艺路线
     * @return List<PedigreeStepMaterialRule>
     * <AUTHOR>
     * @date 2021-01-14
     **/
    @Transactional(readOnly = true)
    public List<PedigreeStepMaterialRule> findPedigreeStepMaterialRule(Long clientId, Long pedigreeId, Long workFlowId, Long stepId) {
        Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByIdAndDeleted(pedigreeId, Constants.LONG_ZERO);
        if (!pedigreeOptional.isPresent()) {
            return Lists.newArrayList();
        }
        Pedigree pedigree = pedigreeOptional.get();
        //递归获取产品谱系所有父级ID集合
        List<Long> pedigreeIdList = this.getAllParent(pedigree);
        if (org.springframework.util.CollectionUtils.isEmpty(pedigreeIdList)) {
            return Lists.newArrayList();
        }
        Map<Integer, List<Pedigree>> pedigreeMap = this.findParentPedigreeGroupByLevel(pedigree);
        List<PedigreeStepMaterialRule> pedigreeStepMaterialRules = pedigreeStepMaterialRuleRepository.findByPedigreeIdInAndWorkFlowIdAndStepIdAndClientIdAndDeleted(pedigreeIdList, workFlowId, stepId, clientId, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(pedigreeStepMaterialRules)) {
            return Lists.newArrayList();
        }

        List<PedigreeStepMaterialRule> pedigreeStepMaterialRuleListSort = new ArrayList<PedigreeStepMaterialRule>();
        //1. 获取优先级排序最高集合
        PedigreeStepMaterialRule pedigreeStepMaterialRule = pedigreeStepMaterialRules.stream().sorted(Comparator.comparing(i -> i.getPriorityElementConfig().getPriority())).findFirst().orElseThrow(() -> new ResponseException("error.pedigreeStepMaterialRuleNotExist", "上料规则不存在"));
        pedigreeStepMaterialRules = pedigreeStepMaterialRules.stream().filter(i -> i.getPriorityElementConfig().getPriority() == pedigreeStepMaterialRule.getPriorityElementConfig().getPriority()).collect(Collectors.toList());
        if (pedigreeStepMaterialRules.size() == Constants.INT_ONE) {
            return pedigreeStepMaterialRules;
        }
        //2. 对产品谱系进行排序（有可能产品谱系为空）
        List<PedigreeStepMaterialRule> pedigreeStepMaterialRuleListPedigreeNull = pedigreeStepMaterialRules.stream().filter(i -> Objects.isNull(i.getPedigree())).collect(Collectors.toList());
        List<PedigreeStepMaterialRule> pedigreeStepMaterialRuleListPedigreeNotNull = pedigreeStepMaterialRules.stream().filter(i -> !Objects.isNull(i.getPedigree())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(pedigreeStepMaterialRuleListPedigreeNull)) {
            pedigreeStepMaterialRuleListSort.addAll(pedigreeStepMaterialRuleListPedigreeNull);
        }
        if (!CollectionUtils.isEmpty(pedigreeStepMaterialRuleListPedigreeNotNull)) {
            //根据产品谱系类型进行正序排序（此时“预警条件”优先级均相同）
            int maxPedigreeLevel = pedigreeStepMaterialRuleListPedigreeNotNull.stream().mapToInt(i -> i.getPedigree().getType()).max().getAsInt();
            pedigreeStepMaterialRuleListPedigreeNotNull = pedigreeStepMaterialRuleListPedigreeNotNull.stream().filter(i -> i.getPedigree().getType()==maxPedigreeLevel).collect(Collectors.toList());
            pedigreeStepMaterialRuleListSort.addAll(pedigreeStepMaterialRuleListPedigreeNotNull);
        }
        return pedigreeStepMaterialRuleListSort;
    }

    /**
     * 根据优先级顺序获取产品谱系定制工序
     *
     * @param pedigree 产品谱系
     * @return List<CustomPedigreeStep>
     * <AUTHOR>
     * @date 2021/9/27
     */
    @Transactional(readOnly = true)
    public List<CustomPedigreeStep> findCustomPedigreeStep(Pedigree pedigree) {
        Map<Integer, List<Pedigree>> pedigreeMap = findParentPedigreeGroupByLevel(pedigree);
        for (Integer level : pedigreeMap.keySet()) {
            for (Pedigree pedigreeTemp : pedigreeMap.get(level)) {
                List<CustomPedigreeStep> customPedigreeSteps = customPedigreeStepRepository.findByPedigreeIdAndDeleted(pedigreeTemp.getId(), Constants.LONG_ZERO);
                if (ValidateUtils.isValid(customPedigreeSteps)) {
                    return customPedigreeSteps;
                }
            }
        }
        return null;
    }


    /**
     * 根据产品谱系和客户id获取最小层级定义的流程框图
     *
     * @param pedigree 产品谱系
     * @param clientId 客户id
     * @param highestPriority 是否取优先级最高的工艺路线默认否
     * @return java.util.List<net.airuima.rbase.domain.base.process.WorkFlow> 工艺路线集合
     **/
    @Transactional(readOnly = true)
    public List<WorkFlow> findPedigreeWorkFlowAndClientId(Pedigree pedigree, Long clientId,Boolean highestPriority) {
        if(Objects.nonNull(highestPriority) && !highestPriority){
            List<PedigreeWorkFlow> pedigreeWorkFlowList = pedigreeWorkFlowRepository.findAllByPedigreeIdAndClientIdAndIsEnableAndDeleted(pedigree.getId(),clientId,Boolean.TRUE,Constants.LONG_ZERO);
            if(CollectionUtils.isNotEmpty(pedigreeWorkFlowList)){
                return new ArrayList(pedigreeWorkFlowList.stream().map(PedigreeWorkFlow::getWorkFlow).collect(Collectors.toSet()));
            }
            return Collections.emptyList();
        }
        List<Pedigree> pedigrees = Lists.newArrayList();
        List<Pedigree> parentPedigrees = Lists.newArrayList();
        this.findParentPedigree(pedigree, parentPedigrees);
        pedigrees.add(pedigree);
        if (ValidateUtils.isValid(parentPedigrees)) {
            pedigrees.addAll(parentPedigrees);
        }
        //1.首先按照层级进行分组
        Map<Integer, List<Pedigree>> groupByTypeMap = pedigrees.stream().collect(Collectors.groupingBy(Pedigree::getType));
        //2.分组后按照层级降序排序返回结果
        Map<Integer, List<Pedigree>> pedigreeMap = groupByTypeMap.entrySet().stream().sorted(Map.Entry.<Integer, List<Pedigree>>comparingByKey().reversed())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldVal, newVal) -> oldVal, LinkedHashMap::new));
        List<PedigreeWorkFlow> pedigreeWorkFlowList = pedigreeWorkFlowRepository.findByPedigreeIdInAndClientIdAndIsEnableAndDeleted(pedigrees.stream().map(Pedigree::getId).collect(Collectors.toList()), clientId, Boolean.TRUE, Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(pedigreeWorkFlowList)) {
            return Collections.emptyList();
        }
        List<PedigreeWorkFlow> pedigreeWorkFlowListSort = new ArrayList<PedigreeWorkFlow>();
        //1. 获取优先级排序最高集合
        PedigreeWorkFlow pedigreeWorkFlow = pedigreeWorkFlowList.stream().sorted(Comparator.comparing(i -> i.getPriorityElementConfig().getPriority())).findFirst().orElseThrow(() -> new ResponseException("error.pedigreeWorkFlowNotExist", "产品谱系工艺路线不存在"));
        pedigreeWorkFlowList = pedigreeWorkFlowList.stream().filter(i -> i.getPriorityElementConfig().getPriority() == pedigreeWorkFlow.getPriorityElementConfig().getPriority()).collect(Collectors.toList());
        if (pedigreeWorkFlowList.size() == Constants.INT_ONE) {
            return pedigreeWorkFlowList.stream().map(PedigreeWorkFlow::getWorkFlow).distinct().collect(Collectors.toList());
        }
        //2. 对产品谱系进行排序（有可能产品谱系为空）
        List<PedigreeWorkFlow> pedigreeWorkFlowListPedigreeNull = pedigreeWorkFlowList.stream().filter(i -> Objects.isNull(i.getPedigree())).collect(Collectors.toList());
        List<PedigreeWorkFlow> pedigreeWorkFlowListPedigreeNotNull = pedigreeWorkFlowList.stream().filter(i -> !Objects.isNull(i.getPedigree())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(pedigreeWorkFlowListPedigreeNull)) {
            pedigreeWorkFlowListSort.addAll(pedigreeWorkFlowListPedigreeNull);
        }
        if (!CollectionUtils.isEmpty(pedigreeWorkFlowListPedigreeNotNull)) {
            //根据产品谱系类型进行正序排序（此时“预警条件”优先级均相同）
            pedigreeWorkFlowListPedigreeNotNull = pedigreeWorkFlowListPedigreeNotNull.stream().sorted(Comparator.comparing(i -> i.getPedigree().getType())).collect(Collectors.toList());
            pedigreeWorkFlowListSort.addAll(pedigreeWorkFlowListPedigreeNotNull);
        }
        if (!CollectionUtils.isEmpty(pedigreeWorkFlowListSort)) {
            return pedigreeWorkFlowListSort.stream().map(PedigreeWorkFlow::getWorkFlow).distinct().collect(Collectors.toList());
        }
        return Collections.emptyList();
    }


    /**
     * 根据产品谱系id和客户id查找生效的工艺路线
     *
     * @param pedigreeId 产品谱系id
     * @param clientId   客户id
     * @return java.util.List<net.airuima.rbase.domain.base.process.WorkFlow> 工艺路线集合
     */
    @Transactional(readOnly = true)
    public List<WorkFlow> findWorkFlowByPedigreeIdAndClientId(Long pedigreeId, Long clientId) {
        List<PedigreeWorkFlow> pedigreeWorkFlowList = pedigreeWorkFlowRepository.findByPedigreeIdAndClientIdAndIsEnableAndDeleted
                (pedigreeId, clientId, Boolean.TRUE, Constants.LONG_ZERO);
        return pedigreeWorkFlowList.stream().map(PedigreeWorkFlow::getWorkFlow).distinct().collect(Collectors.toList());
    }


    /**
     * 通过产品谱系和不良组别ID获取返修流程
     *
     * @param pedigree           产品谱系
     * @param unqualifiedGroupId 不良组别ID
     * @param clientId           客户id
     * @return net.airuima.rbase.domain.base.process.WorkFlow 工艺路线
     **/
    @Transactional(readOnly = true)
    public WorkFlow findPedigreeReworkWorkFlow(Pedigree pedigree, Long unqualifiedGroupId, Long clientId) {
        List<Pedigree> pedigrees = Lists.newArrayList();
        List<Pedigree> parentPedigrees = Lists.newArrayList();
        this.findParentPedigree(pedigree, parentPedigrees);
        pedigrees.add(pedigree);
        if (ValidateUtils.isValid(parentPedigrees)) {
            pedigrees.addAll(parentPedigrees);
        }
        //1.首先按照层级进行分组
        Map<Integer, List<Pedigree>> groupByTypeMap = pedigrees.stream().collect(Collectors.groupingBy(Pedigree::getType));
        //2.分组后按照层级降序排序返回结果
        Map<Integer, List<Pedigree>> pedigreeMap = groupByTypeMap.entrySet().stream().sorted(Map.Entry.<Integer, List<Pedigree>>comparingByKey().reversed())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldVal, newVal) -> oldVal, LinkedHashMap::new));
        List<PedigreeReworkWorkFlow> pedigreeReworkWorkFlowList = pedigreeReworkWorkFlowRepository.findByPedigreeIdInAndUnqualifiedGroupIdAndClientIdAndIsEnableAndDeleted(pedigrees.stream().map(Pedigree::getId).collect(Collectors.toList()), unqualifiedGroupId, clientId, Boolean.TRUE, Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(pedigreeReworkWorkFlowList)) {
            return null;
        }
        List<PedigreeReworkWorkFlow> pedigreeWorkFlowListSort = new ArrayList<PedigreeReworkWorkFlow>();
        //1. 获取优先级排序最高集合
        PedigreeReworkWorkFlow pedigreeReworkWorkFlow = pedigreeReworkWorkFlowList.stream().sorted(Comparator.comparing(i -> i.getPriorityElementConfig().getPriority())).findFirst().orElseThrow(() -> new ResponseException("error.pedigreeReworkWorkFlowNotExist", "产品谱系返工工艺路线不存在"));
        pedigreeReworkWorkFlowList = pedigreeReworkWorkFlowList.stream().filter(i -> i.getPriorityElementConfig().getPriority() == pedigreeReworkWorkFlow.getPriorityElementConfig().getPriority()).collect(Collectors.toList());
        if (pedigreeReworkWorkFlowList.size() == Constants.INT_ONE) {
            return pedigreeReworkWorkFlowList.get(Constants.INT_ZERO).getWorkFlow();
        }
        //2. 对产品谱系进行排序（有可能产品谱系为空）
        List<PedigreeReworkWorkFlow> pedigreeWorkFlowListPedigreeNull = pedigreeReworkWorkFlowList.stream().filter(i -> Objects.isNull(i.getPedigree())).collect(Collectors.toList());
        List<PedigreeReworkWorkFlow> pedigreeWorkFlowListPedigreeNotNull = pedigreeReworkWorkFlowList.stream().filter(i -> !Objects.isNull(i.getPedigree())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(pedigreeWorkFlowListPedigreeNull)) {
            pedigreeWorkFlowListSort.addAll(pedigreeWorkFlowListPedigreeNull);
        }
        if (!CollectionUtils.isEmpty(pedigreeWorkFlowListPedigreeNotNull)) {
            //根据产品谱系类型进行正序排序（此时“预警条件”优先级均相同）
            int maxPedigreeLevel = pedigreeWorkFlowListPedigreeNotNull.stream().mapToInt(i -> i.getPedigree().getType()).max().getAsInt();
            pedigreeWorkFlowListPedigreeNotNull = pedigreeWorkFlowListPedigreeNotNull.stream().filter(i -> i.getPedigree().getType()==maxPedigreeLevel).collect(Collectors.toList());
            pedigreeWorkFlowListSort.addAll(pedigreeWorkFlowListPedigreeNotNull);
        }
        if (!CollectionUtils.isEmpty(pedigreeWorkFlowListSort)) {
            return pedigreeWorkFlowListSort.get(Constants.INT_ZERO).getWorkFlow();
        }
        return null;
    }


    /**
     * 通过产品谱系和不良组别ID和客户id获取返工流程
     *
     * @param pedigree           产品谱系
     * @param unqualifiedGroupId 不良组别ID
     * @param clientId           客户id
     * @return java.util.Set<net.airuima.rbase.domain.base.process.WorkFlow> 工艺功能Set
     **/
    @Transactional(readOnly = true)
    public Set<WorkFlow> findReworkWorkFlowByPedigreeAndUnqualifiedGroupIdAndClientId(Pedigree pedigree, Long unqualifiedGroupId, Long clientId) {
        List<Pedigree> pedigrees = Lists.newArrayList();
        List<Pedigree> parentPedigrees = Lists.newArrayList();
        this.findParentPedigree(pedigree, parentPedigrees);
        pedigrees.add(pedigree);
        if (ValidateUtils.isValid(parentPedigrees)) {
            pedigrees.addAll(parentPedigrees);
        }
        //1.首先按照层级进行分组
        Map<Integer, List<Pedigree>> groupByTypeMap = pedigrees.stream().collect(Collectors.groupingBy(Pedigree::getType));
        //2.分组后按照层级降序排序返回结果
        Map<Integer, List<Pedigree>> pedigreeMap = groupByTypeMap.entrySet().stream().sorted(Map.Entry.<Integer, List<Pedigree>>comparingByKey().reversed())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldVal, newVal) -> oldVal, LinkedHashMap::new));
        List<PedigreeReworkWorkFlow> pedigreeReworkWorkFlowList = pedigreeReworkWorkFlowRepository.findByPedigreeIdInAndUnqualifiedGroupIdAndClientIdAndIsEnableAndDeleted(pedigrees.stream().map(Pedigree::getId).collect(Collectors.toList()), unqualifiedGroupId, clientId, Boolean.TRUE, Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(pedigreeReworkWorkFlowList)) {
            return Collections.emptySet();
        }

        List<PedigreeReworkWorkFlow> pedigreeWorkFlowListSort = new ArrayList<PedigreeReworkWorkFlow>();
        //1. 获取优先级排序最高集合
        PedigreeReworkWorkFlow pedigreeReworkWorkFlow = pedigreeReworkWorkFlowList.stream().sorted(Comparator.comparing(i -> i.getPriorityElementConfig().getPriority())).findFirst().orElseThrow(() -> new ResponseException("error.pedigreeReworkWorkFlowNotExist", "产品谱系返工工艺路线不存在"));
        pedigreeReworkWorkFlowList = pedigreeReworkWorkFlowList.stream().filter(i -> i.getPriorityElementConfig().getPriority() == pedigreeReworkWorkFlow.getPriorityElementConfig().getPriority()).collect(Collectors.toList());
        if (pedigreeReworkWorkFlowList.size() == Constants.INT_ONE) {
            return pedigreeReworkWorkFlowList.stream().map(PedigreeReworkWorkFlow::getWorkFlow).collect(Collectors.toSet());
        }
        //2. 对产品谱系进行排序（有可能产品谱系为空）
        List<PedigreeReworkWorkFlow> pedigreeWorkFlowListPedigreeNull = pedigreeReworkWorkFlowList.stream().filter(i -> Objects.isNull(i.getPedigree())).collect(Collectors.toList());
        List<PedigreeReworkWorkFlow> pedigreeWorkFlowListPedigreeNotNull = pedigreeReworkWorkFlowList.stream().filter(i -> !Objects.isNull(i.getPedigree())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(pedigreeWorkFlowListPedigreeNull)) {
            pedigreeWorkFlowListSort.addAll(pedigreeWorkFlowListPedigreeNull);
        }
        if (!CollectionUtils.isEmpty(pedigreeWorkFlowListPedigreeNotNull)) {
            //根据产品谱系类型进行正序排序（此时“预警条件”优先级均相同）
            pedigreeWorkFlowListPedigreeNotNull = pedigreeWorkFlowListPedigreeNotNull.stream().sorted(Comparator.comparing(i -> i.getPedigree().getType())).collect(Collectors.toList());
            pedigreeWorkFlowListSort.addAll(pedigreeWorkFlowListPedigreeNotNull);
        }
        if (!CollectionUtils.isEmpty(pedigreeWorkFlowListSort)) {
            return pedigreeWorkFlowListSort.stream().map(PedigreeReworkWorkFlow::getWorkFlow).collect(Collectors.toSet());
        }
        return Collections.emptySet();
    }


    /**
     * 通过产品谱系和客户id获取向上获取返修流程列表
     *
     * @param pedigree 产品谱系
     * @param clientId 客户id
     * @param highestPriority 是否取优先级最高，默认否
     * @return java.util.List<net.airuima.rbase.domain.base.process.WorkFlow> 工艺路线
     **/
    @Transactional(readOnly = true)
    public List<WorkFlow> findPedigreeReworkWorkFlows(Pedigree pedigree, Long clientId,Boolean highestPriority) {
        if(Objects.nonNull(highestPriority) && !highestPriority){
            List<PedigreeReworkWorkFlow> pedigreeReworkWorkFlowList = pedigreeReworkWorkFlowRepository.findAllByPedigreeIdAndClientIdAndIsEnableAndDeleted(pedigree.getId(),clientId,Boolean.TRUE,Constants.LONG_ZERO);
            if(CollectionUtils.isEmpty(pedigreeReworkWorkFlowList)){
                return Collections.emptyList();
            }
            return new ArrayList(pedigreeReworkWorkFlowList.stream().map(PedigreeReworkWorkFlow::getWorkFlow).collect(Collectors.toSet()));
        }
        List<Pedigree> pedigrees = Lists.newArrayList();
        List<Pedigree> parentPedigrees = Lists.newArrayList();
        this.findParentPedigree(pedigree, parentPedigrees);
        pedigrees.add(pedigree);
        if (ValidateUtils.isValid(parentPedigrees)) {
            pedigrees.addAll(parentPedigrees);
        }
        //1.首先按照层级进行分组
        Map<Integer, List<Pedigree>> groupByTypeMap = pedigrees.stream().collect(Collectors.groupingBy(Pedigree::getType));
        //2.分组后按照层级降序排序返回结果
        Map<Integer, List<Pedigree>> pedigreeMap = groupByTypeMap.entrySet().stream().sorted(Map.Entry.<Integer, List<Pedigree>>comparingByKey().reversed())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldVal, newVal) -> oldVal, LinkedHashMap::new));
        List<PedigreeReworkWorkFlow> pedigreeReworkWorkFlowList = pedigreeReworkWorkFlowRepository.findByPedigreeIdInAndIsEnableAndDeleted(pedigrees.stream().map(Pedigree::getId).collect(Collectors.toList()), Boolean.TRUE, Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(pedigreeReworkWorkFlowList)) {
            return Collections.emptyList();
        }
        List<PedigreeReworkWorkFlow> pedigreeWorkFlowListSort = new ArrayList<PedigreeReworkWorkFlow>();
        //1. 获取优先级排序最高集合
        PedigreeReworkWorkFlow pedigreeReworkWorkFlow = pedigreeReworkWorkFlowList.stream().sorted(Comparator.comparing(i -> i.getPriorityElementConfig().getPriority())).findFirst().orElseThrow(() -> new ResponseException("error.pedigreeReworkWorkFlowNotExist", "产品谱系返工工艺路线不存在"));
        pedigreeReworkWorkFlowList = pedigreeReworkWorkFlowList.stream().filter(i -> i.getPriorityElementConfig().getPriority() == pedigreeReworkWorkFlow.getPriorityElementConfig().getPriority()).collect(Collectors.toList());
        if (pedigreeReworkWorkFlowList.size() == Constants.INT_ONE) {
            return pedigreeReworkWorkFlowList.stream().map(PedigreeReworkWorkFlow::getWorkFlow).collect(Collectors.toList());
        }
        //2. 对产品谱系进行排序（有可能产品谱系为空）
        List<PedigreeReworkWorkFlow> pedigreeWorkFlowListPedigreeNull = pedigreeReworkWorkFlowList.stream().filter(i -> Objects.isNull(i.getPedigree())).collect(Collectors.toList());
        List<PedigreeReworkWorkFlow> pedigreeWorkFlowListPedigreeNotNull = pedigreeReworkWorkFlowList.stream().filter(i -> !Objects.isNull(i.getPedigree())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(pedigreeWorkFlowListPedigreeNull)) {
            pedigreeWorkFlowListSort.addAll(pedigreeWorkFlowListPedigreeNull);
        }
        if (!CollectionUtils.isEmpty(pedigreeWorkFlowListPedigreeNotNull)) {
            //根据产品谱系类型进行正序排序（此时“预警条件”优先级均相同）
            pedigreeWorkFlowListPedigreeNotNull = pedigreeWorkFlowListPedigreeNotNull.stream().sorted(Comparator.comparing(i -> i.getPedigree().getType())).collect(Collectors.toList());
            pedigreeWorkFlowListSort.addAll(pedigreeWorkFlowListPedigreeNotNull);
        }
        if (!CollectionUtils.isEmpty(pedigreeWorkFlowListSort)) {
            return pedigreeWorkFlowListSort.stream().map(PedigreeReworkWorkFlow::getWorkFlow).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }


    /**
     * 获取产品谱系复用配置信息
     *
     * @param pedigree
     * @return List<PedigreeReuseConfig>
     * <AUTHOR>
     * @date 2021-03-19
     **/
    @Transactional(readOnly = true)
    public List<PedigreeSnReuseConfig> findPedigreeSnReuseConfig(Pedigree pedigree) {
        List<Pedigree> pedigrees = Lists.newArrayList();
        List<Pedigree> parentPedigrees = Lists.newArrayList();
        this.findParentPedigree(pedigree, parentPedigrees);
        pedigrees.add(pedigree);
        if (ValidateUtils.isValid(parentPedigrees)) {
            pedigrees.addAll(parentPedigrees);
        }
        List<Long> pedigreeIdList = pedigrees.stream().map(Pedigree::getId).toList();
        List<PedigreeSnReuseConfig> pedigreeReuseConfigs =pedigreeSnReuseConfigRepository.findByPedigreeIdInAndDeleted(pedigreeIdList,Constants.LONG_ZERO);
        if(CollectionUtils.isEmpty(pedigreeReuseConfigs)){
            return new ArrayList<>();
        }
        int maxPedigreeLevel = pedigreeReuseConfigs.stream().mapToInt(i -> i.getPedigree().getType()).max().getAsInt();
        return pedigreeReuseConfigs.stream().filter(i -> i.getPedigree().getType()==maxPedigreeLevel).collect(Collectors.toList());
    }

    /**
     * 获取不良项目预警标准
     *
     * @param subWorkSheet    子工单
     * @param pedigree        产品谱系
     * @param step            工序
     * @param unqualifiedItem 不良项目
     * @return UnqualifiedItemWaringStandard
     * <AUTHOR>
     * @date 2021-01-20
     **/
    @Transactional(readOnly = true)
    public UnqualifiedItemWarningStandard findUnqualifiedItemWaringStandard(SubWorkSheet subWorkSheet, Pedigree pedigree, Step step, UnqualifiedItem unqualifiedItem, Integer finishNumber, Double unQualifiedRate) {
        WorkSheet workSheet = subWorkSheet.getWorkSheet();
        //获取定制工序中工艺路线
        WorkFlow workFlow = this.findSnapshotWorkFlow(workSheet, subWorkSheet, step);
        //递归获取产品谱系所有父级ID集合
        List<Long> pedigreeIdList = this.getAllParent(pedigree);
        // 查出规则集合
        List<UnqualifiedItemWarningStandard> unqualifiedItemWarningStandardList = unqualifiedItemWarningStandardRepository.findAllStandardByElementOrderByPriorityAndPedigree(pedigreeIdList, workSheet.getId(), workSheet.getCategory(), step.getStepGroup() != null ? step.getStepGroup().getId() : null, step.getId(),
                workFlow.getId(), workSheet.getClientId() != null ? workSheet.getClientId() : null, unqualifiedItem.getId(), unqualifiedItem.getUnqualifiedGroup() != null ? unqualifiedItem.getUnqualifiedGroup().getId() : null, Constants.INT_ONE, Constants.LONG_ZERO);
        // 排序
        List<UnqualifiedItemWarningStandard> unqualifiedItemWarningStandardListSort = getUnqualifiedItemWarningStandardListSort(unqualifiedItemWarningStandardList);
        //优先级模式（优先级相对最高的预警规则。）
        return CollectionUtils.isEmpty(unqualifiedItemWarningStandardListSort) ? null : unqualifiedItemWarningStandardListSort.get(unqualifiedItemWarningStandardListSort.size() - Constants.INT_ONE);
    }

    /**
     * 查找定制工序的工艺路线
     *
     * @param workSheet    工单
     * @param subWorkSheet 子工单
     * @param step         工序
     * @return net.airuima.rbase.domain.base.process.WorkFlow 工艺路线
     */
    public WorkFlow findSnapshotWorkFlow(WorkSheet workSheet, SubWorkSheet subWorkSheet, Step step) {
        Optional<WsStep> currentWsStepOptional = Optional.empty();
        if (Objects.nonNull(subWorkSheet) && Objects.nonNull(step)) {
            currentWsStepOptional = wsStepRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), step.getId(), Constants.LONG_ZERO);
            workSheet = subWorkSheet.getWorkSheet();
        }
        if (!currentWsStepOptional.isPresent() && Objects.nonNull(step) && Objects.nonNull(workSheet)) {
            currentWsStepOptional = wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), step.getId(), Constants.LONG_ZERO);
        }
        if (currentWsStepOptional.isPresent() && null != currentWsStepOptional.get().getWorkFlow()) {
            return currentWsStepOptional.get().getWorkFlow();
        }
        if (null != subWorkSheet && null != subWorkSheet.getWorkFlow()) {
            return subWorkSheet.getWorkFlow();
        }
        return workSheet.getWorkFlow();
    }

    /**
     * 对不良预警规则进行排序，1. 优先级排序，2. 产品谱系排序
     *
     * @param unqualifiedItemWarningStandardList
     * @return : void
     * <AUTHOR>
     * @date 2022/11/1
     **/
    public List<UnqualifiedItemWarningStandard> getUnqualifiedItemWarningStandardListSort(List<UnqualifiedItemWarningStandard> unqualifiedItemWarningStandardList) {
        List<UnqualifiedItemWarningStandard> unqualifiedItemWarningStandardListSort = new ArrayList<UnqualifiedItemWarningStandard>();
        if (unqualifiedItemWarningStandardList.size() > 1) {
            //1. 获取优先级排序最高集合
            UnqualifiedItemWarningStandard unqualifiedItemWarningStandard = unqualifiedItemWarningStandardList.stream().sorted(Comparator.comparing(i -> i.getPriorityElementConfig().getPriority())).findFirst().orElseThrow(() -> new ResponseException("error.unqualifiedItemWarningStandardNotExist", "产品谱系工序不良项目预警标准不存在"));
            unqualifiedItemWarningStandardList = unqualifiedItemWarningStandardList.stream().filter(i -> i.getPriorityElementConfig().getPriority() == unqualifiedItemWarningStandard.getPriorityElementConfig().getPriority()).collect(Collectors.toList());
            if (unqualifiedItemWarningStandardList.size() > 1) {
                //2. 对产品谱系进行排序（有可能产品谱系为空）
                List<UnqualifiedItemWarningStandard> unqualifiedItemWarningStandardListPedigreeNull = unqualifiedItemWarningStandardList.stream().filter(i -> Objects.isNull(i.getPedigree())).collect(Collectors.toList());
                List<UnqualifiedItemWarningStandard> unqualifiedItemWarningStandardListPedigreeNotNull = unqualifiedItemWarningStandardList.stream().filter(i -> !Objects.isNull(i.getPedigree())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(unqualifiedItemWarningStandardListPedigreeNull)) {
                    unqualifiedItemWarningStandardListSort.addAll(unqualifiedItemWarningStandardListPedigreeNull);
                }
                if (!CollectionUtils.isEmpty(unqualifiedItemWarningStandardListPedigreeNotNull)) {
                    //根据产品谱系类型进行正序排序（此时“预警条件”优先级均相同）
                    unqualifiedItemWarningStandardListPedigreeNotNull = unqualifiedItemWarningStandardListPedigreeNotNull.stream().sorted(Comparator.comparing(i -> i.getPedigree().getType())).collect(Collectors.toList());
                    unqualifiedItemWarningStandardListSort.addAll(unqualifiedItemWarningStandardListPedigreeNotNull);
                }
            } else {
                unqualifiedItemWarningStandardListSort.addAll(unqualifiedItemWarningStandardList);
            }
        } else if (unqualifiedItemWarningStandardList.size() == 1) {
            unqualifiedItemWarningStandardListSort.addAll(unqualifiedItemWarningStandardList);
        }
        return unqualifiedItemWarningStandardListSort;
    }

    /**
     * 通过产品谱系、工序获取工序的合格率预警标准
     *
     * @param pedigree 产品谱系
     * @param step     工序
     * @return StepWaringStandard
     * <AUTHOR>
     * @date 2021-01-20
     **/
    @Transactional(readOnly = true)
    public StepWarningStandard findStepWaringStandard(Pedigree pedigree, WorkFlow workFlow, Step step) {
        Map<Integer, List<Pedigree>> pedigreeMap = this.findParentPedigreeGroupByLevel(pedigree);
        for (Integer level : pedigreeMap.keySet()) {
            for (Pedigree pedigreeTemp : pedigreeMap.get(level)) {
                Optional<StepWarningStandard> stepWaringStandardOptional = stepWarningStandardRepository.findByPedigreeIdAndWorkFlowIdAndStepIdAndDeleted(pedigreeTemp.getId(),
                        workFlow.getId(), step.getId(), Constants.LONG_ZERO);
                if (stepWaringStandardOptional.isPresent()) {
                    return stepWaringStandardOptional.get();
                }
            }
        }
        return null;
    }

    /**
     * 通过产品谱系、工序、工艺路线获取产品谱系工序间隔配置
     *
     * @param pedigree 产品谱系
     * @param step     工序
     * @param workFlow 工艺路线
     * @return List<PedigreeStepIntervalConfig>
     */
    @Transactional(readOnly = true)
    public List<PedigreeStepIntervalConfig> findPedigreeStepIntervalConfig(Pedigree pedigree, Step step, WorkFlow workFlow,List<WsStep> wsStepList) {
        List<Pedigree> pedigrees = Lists.newArrayList();
        List<Pedigree> parentPedigrees = Lists.newArrayList();
        this.findParentPedigree(pedigree, parentPedigrees);
        pedigrees.add(pedigree);
        if (net.airuima.util.ValidateUtils.isValid(parentPedigrees)) {
            pedigrees.addAll(parentPedigrees);
        }
        List<Long> pedigreeIdList = pedigrees.stream().map(Pedigree::getId).toList();
        List<PedigreeStepIntervalConfig> pedigreeStepIntervalConfigList = pedigreeStepIntervalConfigRepository.findByPedigreeIdInAndWorkFlowIdAndStepIdAndDeleted(pedigreeIdList,workFlow.getId(),step.getId(), net.airuima.constant.Constants.LONG_ZERO);
        if(CollectionUtils.isEmpty(pedigreeStepIntervalConfigList)){
            return new ArrayList<>();
        }
        LinkedList<PedigreeStepIntervalConfig> matchedPedigreeStepIntervalConfigList = new LinkedList<>();
        //查看当前工序快照有多少个工艺段(因为存在维修分析会进行拼接工艺段)
        Set<WorkFlow> workFlows = wsStepList.stream().map(WsStep::getWorkFlow).collect(Collectors.toSet());
        //优先取产品谱系+工艺路线匹配的配置
        List<PedigreeStepIntervalConfig> pedigreeStepIntervalConfigs = pedigreeStepIntervalConfigList.stream().filter(pedigreeStepIntervalConfig -> Objects.nonNull(pedigreeStepIntervalConfig.getPedigree() )&& Objects.nonNull(pedigreeStepIntervalConfig.getWorkFlow())).toList();
        if(CollectionUtils.isNotEmpty(pedigreeStepIntervalConfigs)){
            int maxPedigreeLevel = pedigreeStepIntervalConfigs.stream().mapToInt(i -> i.getPedigree().getType()).max().getAsInt();
            List<PedigreeStepIntervalConfig> matchedPedigreeStepIntervalConfigs = pedigreeStepIntervalConfigs.stream().filter(i -> i.getPedigree().getType() == maxPedigreeLevel).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(workFlows) || workFlows.size() == net.airuima.constant.Constants.INT_ONE){
                return matchedPedigreeStepIntervalConfigs;
            }
            matchedPedigreeStepIntervalConfigList.addAll(matchedPedigreeStepIntervalConfigs);
        }
        //其次优先取工艺路线匹配的配置
        pedigreeStepIntervalConfigs = pedigreeStepIntervalConfigList.stream().filter(pedigreeStepIntervalConfig -> Objects.isNull(pedigreeStepIntervalConfig.getPedigree()) && Objects.nonNull(pedigreeStepIntervalConfig.getWorkFlow())).toList();
        if(CollectionUtils.isNotEmpty(pedigreeStepIntervalConfigs)){
            if(CollectionUtils.isEmpty(workFlows) || workFlows.size() == net.airuima.constant.Constants.INT_ONE){
                return pedigreeStepIntervalConfigs;
            }
            matchedPedigreeStepIntervalConfigList.addAll(pedigreeStepIntervalConfigs);
        }
        //再其次优先取产品谱系匹配的配置
        pedigreeStepIntervalConfigs = pedigreeStepIntervalConfigList.stream().filter(pedigreeStepIntervalConfig -> Objects.isNull(pedigreeStepIntervalConfig.getWorkFlow()) && Objects.nonNull(pedigreeStepIntervalConfig.getPedigree())).toList();
        if(CollectionUtils.isNotEmpty(pedigreeStepIntervalConfigs)){
            int maxPedigreeLevel = pedigreeStepIntervalConfigs.stream().mapToInt(i -> i.getPedigree().getType()).max().getAsInt();
            List<PedigreeStepIntervalConfig> matchedPedigreeStepIntervalConfigs = pedigreeStepIntervalConfigs.stream().filter(i -> i.getPedigree().getType() == maxPedigreeLevel).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(workFlows) || workFlows.size() == net.airuima.constant.Constants.INT_ONE){
                return matchedPedigreeStepIntervalConfigs;
            }
            matchedPedigreeStepIntervalConfigList.addAll(matchedPedigreeStepIntervalConfigs);
        }
        //最后采取直接工序的配置
        pedigreeStepIntervalConfigs = pedigreeStepIntervalConfigList.stream().filter(pedigreeStepIntervalConfig -> Objects.isNull(pedigreeStepIntervalConfig.getWorkFlow()) && Objects.isNull(pedigreeStepIntervalConfig.getPedigree())).toList();
        if(CollectionUtils.isNotEmpty(pedigreeStepIntervalConfigs)){
            if(CollectionUtils.isEmpty(workFlows) || workFlows.size() == net.airuima.constant.Constants.INT_ONE){
                return pedigreeStepIntervalConfigs;
            }
            matchedPedigreeStepIntervalConfigList.addAll(pedigreeStepIntervalConfigs);
        }
        Map<Long,PedigreeStepIntervalConfig> preStepGroupMap = new HashMap<>();
        matchedPedigreeStepIntervalConfigList.forEach(matchedPedigreeStepIntervalConfig->{
            if(!preStepGroupMap.containsKey(matchedPedigreeStepIntervalConfig.getPreStep().getId())){
                preStepGroupMap.put(matchedPedigreeStepIntervalConfig.getPreStep().getId(),matchedPedigreeStepIntervalConfig);
            }
        });
        return preStepGroupMap.values().stream().toList();
    }

    /**
     * 获取易损件使用规则信息
     * 1. 产品谱系编码+ 工序编码+deleted+工艺路线编码+工位
     * 2. 产品谱系编码+ 工序编码+deleted+工艺路线编码
     * 3. 产品谱系编码+ 工序编码+deleted+工位
     * 4. 产品谱系编码+ 工序编码+deleted
     *
     * @param pedigree 产品谱系
     * @param workFlow 工艺路线
     * @param step     工序
     * @return List<PedigreeStepWearingPartGroup>
     * <AUTHOR>
     */
    public List<PedigreeStepWearingPartGroup> findPedigreeWorkFlowStepByWearingGroupInfo(Pedigree pedigree, WorkFlow workFlow, Step step, WorkCell workCell) {
        return pedigreeStepWearingPartGroupService.findPedigreeWorkFlowStepByWearingGroupInfo(pedigree, workFlow, step, workCell);
    }

    /**
     * 获取检测规则
     *
     * @param pedigree 产品谱系
     * @param step     工序
     * @param category 类型
     * @return PedigreeStepCheckRule
     * <AUTHOR>
     * @date 2021-03-23
     **/
    @Transactional(readOnly = true)
    public PedigreeStepCheckRule findPedigreeStepCheckRule(WorkSheet workSheet, SubWorkSheet subWorkSheet, Pedigree pedigree, Step step, Integer category, Long workCellId, Long varietyId) {
        //递归获取产品谱系所有父级ID集合
        List<Long> pedigreeIdList = this.getAllParent(pedigree);
        //获取定制工序中工艺路线
        WorkFlow workFlow = this.findSnapshotWorkFlow(workSheet, subWorkSheet, step);
        // 查出规则集合
        List<PedigreeStepCheckRule> pedigreeStepCheckRuleList = pedigreeStepCheckRuleRepository.findAllStandardByElementOrderByPriorityAndPedigree(pedigreeIdList, workSheet.getId(), workSheet.getCategory(), ObjectUtils.isEmpty(step.getStepGroup()) ? null : step.getStepGroup().getId(), step.getId(), workFlow.getId(), workSheet.getClientId(), workCellId, category, varietyId, Constants.LONG_ZERO);
        //去掉已失效的规则
        pedigreeStepCheckRuleList = pedigreeStepCheckRuleList.stream().filter(PedigreeStepCheckRule::getIsEnable).filter(pedigreeStepCheckRule -> LocalDate.now().isBefore(pedigreeStepCheckRule.getExpiryDate())).collect(Collectors.toList());
        //过滤项目类型
        if (!ObjectUtils.isEmpty(varietyId) && ValidateUtils.isValid(pedigreeStepCheckRuleList)) {
            pedigreeStepCheckRuleList = pedigreeStepCheckRuleList.stream().filter(pedigreeStepCheckRule -> !ObjectUtils.isEmpty(pedigreeStepCheckRule.getVarietyObj()))
                    .filter(pedigreeStepCheckRule -> pedigreeStepCheckRule.getVarietyObj().getId().equals(varietyId)).collect(Collectors.toList());
        }
        // 排序
        List<PedigreeStepCheckRule> pedigreeStepCheckRuleListSort = getPedigreeStepCheckRuleListSort(pedigreeStepCheckRuleList);
        return CollectionUtils.isEmpty(pedigreeStepCheckRuleListSort) ? null : pedigreeStepCheckRuleListSort.get(pedigreeStepCheckRuleListSort.size() - Constants.INT_ONE);
    }


    /**
     * 对检测规则进行排序，1. 优先级排序，2. 产品谱系排序
     *
     * @param pedigreeStepCheckRuleList
     * @return : void
     * <AUTHOR>
     * @date 2022/11/1
     **/
    public List<PedigreeStepCheckRule> getPedigreeStepCheckRuleListSort(List<PedigreeStepCheckRule> pedigreeStepCheckRuleList) {
        List<PedigreeStepCheckRule> pedigreeStepCheckRuleListSort = new ArrayList<PedigreeStepCheckRule>();
        if (pedigreeStepCheckRuleList.size() > 1) {
            //1. 获取优先级排序最高集合
            PedigreeStepCheckRule pedigreeStepCheckRule = pedigreeStepCheckRuleList.stream().sorted(Comparator.comparing(i -> i.getPriorityElementConfig().getPriority())).findFirst().orElseThrow(() -> new ResponseException("error.pedigreeStepCheckRuleNotExist", "产品谱系工序检测规则不存在"));
            pedigreeStepCheckRuleList = pedigreeStepCheckRuleList.stream().filter(i -> i.getPriorityElementConfig().getPriority() == pedigreeStepCheckRule.getPriorityElementConfig().getPriority()).collect(Collectors.toList());
            if (pedigreeStepCheckRuleList.size() > 1) {
                //2. 对产品谱系进行排序（有可能产品谱系为空）
                List<PedigreeStepCheckRule> pedigreeStepCheckRuleListPedigreeNull = pedigreeStepCheckRuleList.stream().filter(i -> Objects.isNull(i.getPedigree())).collect(Collectors.toList());
                List<PedigreeStepCheckRule> pedigreeStepCheckRuleListPedigreeNotNull = pedigreeStepCheckRuleList.stream().filter(i -> !Objects.isNull(i.getPedigree())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(pedigreeStepCheckRuleListPedigreeNull)) {
                    pedigreeStepCheckRuleListSort.addAll(pedigreeStepCheckRuleListPedigreeNull);
                }
                if (!CollectionUtils.isEmpty(pedigreeStepCheckRuleListPedigreeNotNull)) {
                    //根据产品谱系类型进行正序排序（此时“预警条件”优先级均相同）
                    int maxPedigreeLevel = pedigreeStepCheckRuleListPedigreeNotNull.stream().mapToInt(i -> i.getPedigree().getType()).max().getAsInt();
                    pedigreeStepCheckRuleListPedigreeNotNull = pedigreeStepCheckRuleListPedigreeNotNull.stream().filter(i -> i.getPedigree().getType()==maxPedigreeLevel).collect(Collectors.toList());
                    pedigreeStepCheckRuleListSort.addAll(pedigreeStepCheckRuleListPedigreeNotNull);
                }
            } else {
                pedigreeStepCheckRuleListSort.addAll(pedigreeStepCheckRuleList);
            }
        } else if (pedigreeStepCheckRuleList.size() == 1) {
            pedigreeStepCheckRuleListSort.addAll(pedigreeStepCheckRuleList);
        }
        return pedigreeStepCheckRuleListSort;
    }

    /**
     * 递归获取产品谱系配置
     *
     * @param pedigree 产品谱系
     * @return PedigreeConfig
     * <AUTHOR>
     * @date 2021-01-15
     **/
    public PedigreeConfig findPedigreeConfig(Pedigree pedigree) {
        List<Pedigree> pedigrees = Lists.newArrayList();
        List<Pedigree> parentPedigrees = Lists.newArrayList();
        this.findParentPedigree(pedigree, parentPedigrees);
        pedigrees.add(pedigree);
        if (ValidateUtils.isValid(parentPedigrees)) {
            pedigrees.addAll(parentPedigrees);
        }
        List<Long> pedigreeIdList = pedigrees.stream().map(Pedigree::getId).toList();
        List<PedigreeConfig> pedigreeConfigs = pedigreeConfigRepository.findByPedigreeIdInAndDeleted(pedigreeIdList,Constants.LONG_ZERO);
        if(CollectionUtils.isEmpty(pedigreeConfigs)){
            return null;
        }
        int maxPedigreeLevel = pedigreeConfigs.stream().mapToInt(i -> i.getPedigree().getType()).max().getAsInt();
        return pedigreeConfigs.stream().filter(i -> i.getPedigree().getType() == maxPedigreeLevel).collect(Collectors.toList()).get(Constants.INT_ZERO);
    }

    /**
     * 以谱系层级降序排序进行分组获取各层级的产品谱系
     *
     * @param pedigree 产品谱系
     * @return Map<Integer, List < Pedigree>>
     * <AUTHOR>
     * @date 2021-01-12
     **/
    @Transactional(readOnly = true)
    public Map<Integer, List<Pedigree>> findAllPedigreeGroupByLevel(Pedigree pedigree) {
        List<Pedigree> pedigrees = Lists.newArrayList();
        List<Pedigree> childPedigrees = Lists.newArrayList();
        List<Pedigree> parentPedigrees = Lists.newArrayList();
        this.findChildPedigree(pedigree, childPedigrees);
        this.findParentPedigree(pedigree, parentPedigrees);
        pedigrees.add(pedigree);
        if (ValidateUtils.isValid(childPedigrees)) {
            pedigrees.addAll(childPedigrees);
        }
        if (ValidateUtils.isValid(parentPedigrees)) {
            pedigrees.addAll(parentPedigrees);
        }
        //1.首先按照层级进行分组
        Map<Integer, List<Pedigree>> groupByTypeMap = pedigrees.stream().collect(Collectors.groupingBy(Pedigree::getType));
        //2.分组后按照层级降序排序返回结果
        return groupByTypeMap.entrySet().stream().sorted(Map.Entry.<Integer, List<Pedigree>>comparingByKey().reversed())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldVal, newVal) -> oldVal, LinkedHashMap::new));

    }

    /**
     * 以谱系层级降序排序进行分组获取父级(包含自身)的产品谱系
     *
     * @param pedigree
     * @return Map<Integer, List < Pedigree>>
     * <AUTHOR>
     * @date 2021-07-07
     **/
    @Transactional(readOnly = true)
    public Map<Integer, List<Pedigree>> findParentPedigreeGroupByLevel(Pedigree pedigree) {
        List<Pedigree> pedigrees = Lists.newArrayList();
        List<Pedigree> parentPedigrees = Lists.newArrayList();
        this.findParentPedigree(pedigree, parentPedigrees);
        pedigrees.add(pedigree);
        if (ValidateUtils.isValid(parentPedigrees)) {
            pedigrees.addAll(parentPedigrees);
        }
        //1.首先按照层级进行分组
        Map<Integer, List<Pedigree>> groupByTypeMap = pedigrees.stream().collect(Collectors.groupingBy(Pedigree::getType));
        //2.分组后按照层级降序排序返回结果
        return groupByTypeMap.entrySet().stream().sorted(Map.Entry.<Integer, List<Pedigree>>comparingByKey().reversed())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldVal, newVal) -> oldVal, LinkedHashMap::new));
    }

    /**
     * 以谱系层级降序排序进行分组获取子级(包含自身)的产品谱系
     *
     * @param pedigree
     * @return Map<Integer, List < Pedigree>>
     * <AUTHOR>
     * @date 2021-07-07
     **/
    @Transactional(readOnly = true)
    public Map<Integer, List<Pedigree>> findChildPedigreeGroupByLevel(Pedigree pedigree) {
        List<Pedigree> pedigrees = Lists.newArrayList();
        List<Pedigree> childPedigrees = Lists.newArrayList();
        this.findChildPedigree(pedigree, childPedigrees);
        pedigrees.add(pedigree);
        if (ValidateUtils.isValid(childPedigrees)) {
            pedigrees.addAll(childPedigrees);
        }
        //1.首先按照层级进行分组
        Map<Integer, List<Pedigree>> groupByTypeMap = pedigrees.stream().collect(Collectors.groupingBy(Pedigree::getType));
        //2.分组后按照层级降序排序返回结果
        return groupByTypeMap.entrySet().stream().sorted(Map.Entry.<Integer, List<Pedigree>>comparingByKey().reversed())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldVal, newVal) -> oldVal, LinkedHashMap::new));
    }

    /**
     *
     * 递归获取谱系的儿子节点
     *
     * @param pedigree       产品谱系
     * @param childPedigrees 节点列表
     * <AUTHOR>
     * @date 2021-01-12
     **/
    @Transactional(readOnly = true)
    public void findChildPedigree(Pedigree pedigree, List<Pedigree> childPedigrees) {
        List<Pedigree> pedigreeList = pedigreeRepository.findByParentIdAndDeleted(pedigree.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(pedigreeList)) {
            childPedigrees.addAll(pedigreeList);
            pedigreeList.forEach(pedigree1 -> findChildPedigree(pedigree1, childPedigrees));
        }
    }

    /**
     * 递归获取谱系的父亲节点数据
     *
     * @param pedigree        产品谱系
     * @param parentPedigrees 节点列表
     * <AUTHOR>
     * @date 2021-01-12
     **/
    @Transactional(readOnly = true)
    public void findParentPedigree(Pedigree pedigree, List<Pedigree> parentPedigrees) {
        if (null != pedigree.getParent()) {
                parentPedigrees.add(pedigree.getParent());
                findParentPedigree(pedigree.getParent(), parentPedigrees);
        }
    }

    /**
     * 递归获取产品谱系所有父级ID集合
     *
     * @param pedigree
     * @return : java.util.List<java.lang.Long>
     * <AUTHOR>
     * @date 2022/10/21
     **/
    public List<Long> getAllParent(Pedigree pedigree) {
        List<Long> pedigreeIdList = new ArrayList<Long>();
        if (!Objects.isNull(pedigree)) {
            pedigreeIdList.add(pedigree.getId());
            pedigreeIdList.addAll(getAllParent(pedigree.getParent()));
        }
        return pedigreeIdList;
    }

    /**
     * 获取产品谱系最高层级配置
     * @return
     */
    public int getPedigreeMaxLevel(){
        DictionaryDTO dictionaryDTO = rbaseDictionaryProxy.findByCodeAndDeleted(Constants.KEY_PEDIGREE_LEVEL,Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(dictionaryDTO)) {
            throw new ResponseException("error.pedigreeLevelNotConfig", "产品谱系层级未设置");
        }
        List<Map<String, String>> pedigreeLevelList = JSON.parseObject(dictionaryDTO.getData(), new TypeReference<>() {
        });
        if (!net.airuima.util.ValidateUtils.isValid(pedigreeLevelList)) {
            throw new ResponseException("error.pedigreeLevelNotConfig", "产品谱系层级未设置");
        }
        //获得最高层级
        return pedigreeLevelList.stream().mapToInt(map -> Integer.parseInt(map.get("key"))).min().orElseThrow(() -> new RuntimeException("获得产品谱系最高层级失败"));
    }


    public int getPedigreeMinLevel(){
        DictionaryDTO dictionaryDTO = rbaseDictionaryProxy.findByCodeAndDeleted(Constants.KEY_PEDIGREE_LEVEL,Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(dictionaryDTO)) {
            throw new ResponseException("error.pedigreeLevelNotConfig", "产品谱系层级未设置");
        }
        List<Map<String, String>> pedigreeLevelList = JSON.parseObject(dictionaryDTO.getData(), new TypeReference<>() {
        });
        if (!net.airuima.util.ValidateUtils.isValid(pedigreeLevelList)) {
            throw new ResponseException("error.pedigreeLevelNotConfig", "产品谱系层级未设置");
        }
        //获得最低层级
        return pedigreeLevelList.stream().mapToInt(map -> Integer.parseInt(map.get("key"))).max().orElseThrow(() -> new RuntimeException("获得产品谱系最高层级失败"));
    }

    public List<Long> getMinLevelPedigreeIds(Pedigree pedigree){
        List<Pedigree> childPedigreeList = new ArrayList<>();
        int minLevel = this.getPedigreeMinLevel();
        if(pedigree.getType() == minLevel){
            childPedigreeList.add(pedigree);
        }else {
            this.findChildPedigree(pedigree,childPedigreeList);
        }
        return childPedigreeList.stream().filter(pedigree1 -> pedigree1.getType() == minLevel).map(Pedigree::getId).toList();
    }

    /**
     * 获取当前定制工序的所有父亲节点的定制工序
     *
     * @param wsStepList       工单定制工序列表
     * @param parentWsStepList 父节点所有定制工序
     * @param currWsStep       当前定制工序
     * @return void
     * <AUTHOR>
     * @date 2021-01-18
     **/
    @Transactional(readOnly = true)
    public void findParentWsStep(List<WsStep> wsStepList, List<WsStep> parentWsStepList, WsStep currWsStep) {
        if (ValidateUtils.isValid(currWsStep.getPreStepId())) {
            List<Long> preWsStepId = Arrays.stream(currWsStep.getPreStepId().split(Constants.STR_COMMA)).map(Long::parseLong).collect(Collectors.toList());
            for (Long stepId : preWsStepId) {
                Optional<WsStep> wsStepOptional = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(stepId)).findFirst();
                wsStepOptional.ifPresent(parentWsStepList::add);
                if (wsStepOptional.isPresent() && ValidateUtils.isValid(wsStepOptional.get().getPreStepId())) {
                    this.findParentWsStep(wsStepList, parentWsStepList, wsStepOptional.get());
                }
            }
        }
    }

    /**
     * 获取当前定制工序的所有子节点的定制工序
     *
     * @param wsStepList      工单定制工序列表
     * @param childWsStepList 父节点所有定制工序
     * @param currWsStep      当前定制工序
     */
    public void findChildWsStep(List<WsStep> wsStepList, List<WsStep> childWsStepList, WsStep currWsStep) {
        if (ValidateUtils.isValid(currWsStep.getAfterStepId())) {
            List<Long> afterWsStepId = Arrays.stream(currWsStep.getAfterStepId().split(Constants.STR_COMMA)).map(Long::parseLong).collect(Collectors.toList());
            for (Long stepId : afterWsStepId) {
                Optional<WsStep> wsStepOptional = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(stepId)).findFirst();
                wsStepOptional.ifPresent(childWsStepList::add);
                if (wsStepOptional.isPresent() && ValidateUtils.isValid(wsStepOptional.get().getAfterStepId())) {
                    this.findChildWsStep(wsStepList, childWsStepList, wsStepOptional.get());
                }
            }
        }
    }


    /**
     * 获取当前工单的最原始正常工单
     *
     * @param workSheet
     * @return WorkSheet
     * <AUTHOR>
     * @date 2022/9/30
     */
    @Transactional(readOnly = true)
    public WorkSheet findNormalWorkSheet(WorkSheet workSheet) {
        Optional<WsRework> wsReworkOptional = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        if (wsReworkOptional.isPresent()) {
            return findNormalWorkSheet(wsReworkOptional.get().getOriginalWorkSheet());
        } else {
            return workSheet;
        }
    }

    /**
     * 获取产品谱系对应的工序技能
     *
     * @param pedigree 产品谱系
     * @param step     工序
     * @param workFlow 工艺路线
     * @return List<PedigreeStepMaterialRule>
     * <AUTHOR>
     * @date 2022-11-21
     **/
    public List<SkillDTO> findPedigreeStepSkills(Pedigree pedigree, WorkFlow workFlow, Step step) {
        return rbaseSkillProxy.findPedigreeStepSkills(pedigree, workFlow, step);
    }

    /**
     * 获取是否配置产品谱系不良项目
     *
     * @param
     * @return boolean
     * <AUTHOR>
     * @date 2022/11/3
     */
    public boolean openPedigreeStepUnqualifiedItem() {
        String openPedigreeStepUnqualifiedItem = rbaseSysCodeProxy.findByCode("pedigree_step_unqualifiedItem");
        if (ValidateUtils.isValid(openPedigreeStepUnqualifiedItem)) {
            return Boolean.getBoolean(openPedigreeStepUnqualifiedItem);
        }
        return Boolean.TRUE;
    }

    /**
     * 获取系统配置的设定的一天开始结束时间，获取当前记录日期
     *
     * @return java.time.LocalDate
     * <AUTHOR>
     * @date 2022/12/5
     */
    public LocalDate staffWorkRecordDate() {
        String result = rbaseSysCodeProxy.findByCode(Constants.KEY_WORK_DAY_TIME);
        LocalDate recordDate = LocalDate.now();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(result)) {
            List<Map<String, String>> resultMaps = JSON.parseObject(result, new TypeReference<List<Map<String, String>>>() {
            });
            Map<String, LocalTime> workDayTimeMap = new HashMap<>();
            resultMaps.forEach(resultMap -> {
                workDayTimeMap.put(resultMap.get("key"), LocalTime.parse(resultMap.get("value")));
            });
            LocalTime startTime = workDayTimeMap.get("start");
            LocalTime endTime = workDayTimeMap.get("end");
            //如果结束时间小于开始时间则认为跨零点,若当前时间小时小于等于结束时间小时则日期为头一天
            if (endTime.isBefore(startTime) && LocalDateTime.now().getHour() <= endTime.getHour()) {
                recordDate = recordDate.minusDays(Constants.INT_ONE);
            }
        }
        return recordDate;
    }

    /**
     * 生产过程批次管控级别(0:不管控物料库存;1:验证总工单物料库存;2:验证工位物料库存)
     *
     * @param
     * @return int
     * <AUTHOR>
     * @date 2021-05-21
     **/
    public int getMaterialControlLevel() {
        //生产过程批次管控级别(0:不管控物料库存;1:验证总工单物料库存;2:验证工位物料库存)
        int materialControlLevel = ConstantsEnum.MATERIAL_NOT_CONTROL_LEVEL.getCategoryName();
        DictionaryDTO dictionaryDTO = rbaseDictionaryProxy.findByCodeAndDeleted(Constants.MATERIAL_INVENTORY_LEVEL,Constants.LONG_ZERO).orElse(null);
        String result = Objects.isNull(dictionaryDTO) ? null:dictionaryDTO.getData();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(result)) {
            List<Map<String, String>> materialInventoryControlConfigs = JSON.parseObject(result, new TypeReference<List<Map<String, String>>>() {
            });
            Map<String, String> materialInventoryControlConfig = (materialInventoryControlConfigs.stream().filter(config -> Boolean.parseBoolean(config.get("value")))).findFirst().orElse(null);
            materialControlLevel = null != materialInventoryControlConfig ? Integer.parseInt(materialInventoryControlConfig.get("key")) : Constants.INT_ZERO;
        }
        return materialControlLevel;
    }

    /**
     * 获取数据字典值
     * @param code
     * @return
     */
    public String getDictionaryData(String code){
        DictionaryDTO dictionaryDTO = rbaseDictionaryProxy.findByCodeAndDeleted(code,Constants.LONG_ZERO).orElse(null);
        return Objects.isNull(dictionaryDTO) ? null:dictionaryDTO.getData();
    }

    /**
     * 定制流程排序
     *
     * @param wsStepList 定制流程
     * @return List<List < WsStep>>
     */
    public List<List<WsStep>> dealWsStep(List<WsStep> wsStepList) {
        //查找第一个并列工序
        List<WsStep> firstStepList = wsStepList.stream().filter(wsStep ->
                !ValidateUtils.isValid(wsStep.getPreStepId())).collect(Collectors.toList());
        List<List<WsStep>> lists = new ArrayList<>();
        orderByStep(wsStepList, firstStepList, lists, Constants.INT_ZERO);
        return lists;
    }

    public List<WsStep> findOnlineSnReworkStep(WorkFlow workFlow, WorkSheet workSheet) {
        List<WorkFlowStep> workFlowSteps = workFlowStepRepository.findByWorkFlowIdAndDeleted(workFlow.getId(), Constants.LONG_ZERO);
        List<WsStep> wsSteps = Lists.newArrayList();
        workFlowSteps.forEach(workFlowStep -> {
            WsStep wsStep = new WsStep();
            wsStep.setStep(workFlowStep.getStep()).setWorkSheet(workSheet).setPreStepId(workFlowStep.getPreStepId()).setAfterStepId(workFlowStep.getAfterStepId()).setCategory(workFlowStep.getStep().getCategory());
            StepDTO stepDTO = this.findPedigreeStepConfig(workSheet.getClientId(), workSheet.getPedigree(), workFlow, wsStep.getStep());
            if (Objects.isNull(stepDTO)) {
                throw new ResponseException("error.stepConfigNotExist", "工序(" + wsStep.getStep().getCode() + ")配置不存在");
            }
            wsStep.setWorkFlow(workFlow).setRequestMode(stepDTO.getRequestMode()).setControlMode(stepDTO.getControlMode()).setIsBindContainer(stepDTO.getIsBindContainer()).setIsControlMaterial(stepDTO.getIsControlMaterial()).setInputRate(stepDTO.getInputRate());
            wsSteps.add(wsStep);
        });
        return wsSteps;
    }

    /**
     * 流程排序递归查找
     *
     * @param baseWsStep 基础流程
     * @param stepList   当前待排序流程
     * @param lists      结果
     * @param level      层级
     */
    public void orderByStep(List<WsStep> baseWsStep, List<WsStep> stepList, List<List<WsStep>> lists, Integer level) {
        if (ValidateUtils.isValid(stepList)) {
            lists.add(stepList);
            List<WsStep> nextWsStepList = new ArrayList<>();
            for (WsStep wsStep : stepList) {
                if (ValidateUtils.isValid(wsStep.getAfterStepId())) {
                    nextWsStepList.addAll(baseWsStep.stream().filter(currWsStep ->
                            Arrays.asList(wsStep.getAfterStepId().split(Constants.STR_COMMA))
                                    .contains(currWsStep.getStep().getId().toString())).collect(Collectors.toList()));
                }
            }
            if (ValidateUtils.isValid(nextWsStepList)) {
                orderByStep(baseWsStep, nextWsStepList, lists, ++level);
            }
        }
    }


    /**
     * 验证当前工序是否为烘烤温循老化工序
     *
     * @param step
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2022/12/19
     */
    public Boolean validDateBakeCycleBakeAgeingStep(WsStep currWsStep) {
        if (currWsStep.getCategory() == StepCategoryEnum.PUT_IN_BAKE_STEP.getStatus() || currWsStep.getCategory() == StepCategoryEnum.PULL_OUT_BAKE_STEP.getStatus()
                || currWsStep.getCategory() == StepCategoryEnum.PUT_IN_CYCLE_BAKE_STEP.getStatus() || currWsStep.getCategory() == StepCategoryEnum.PULL_OUT_CYCLE_BAKE_STEP.getStatus() ||
                currWsStep.getCategory() == StepCategoryEnum.PUT_IN_AGEING_STEP.getStatus() || currWsStep.getCategory() == StepCategoryEnum.PULL_OUT_AGEING_STEP.getStatus()) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 获取当前投产工单 与 工序 的下一道工序
     *
     * @param workSheet    工单
     * @param subWorkSheet 子工单
     * @param step         当前工序
     * @return WsStep 下一道工序
     */
    public WsStep getNextWsStep(WorkSheet workSheet, SubWorkSheet subWorkSheet, Step step) {
        Optional<WsStep> currentWsStepOptional = Objects.nonNull(subWorkSheet) ? wsStepRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), step.getId(), Constants.LONG_ZERO)
                : wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), step.getId(), Constants.LONG_ZERO);
        if (currentWsStepOptional.isEmpty() && Objects.nonNull(subWorkSheet)) {
            currentWsStepOptional = wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getWorkSheet().getId(), step.getId(), Constants.LONG_ZERO);
        }
        if (currentWsStepOptional.isEmpty()) {
            throw new ResponseException("currentWsStepNotFound", "当前工序快照不存在");
        }
        WsStep currentWsStep = currentWsStepOptional.get();
        if (!ValidateUtils.isValid(currentWsStep.getAfterStepId())) {
            return null;
        }

        String afterStepId = Arrays.stream(StringUtils.split(currentWsStep.getAfterStepId(), Constants.STR_COMMA)).findFirst().get();
        Optional<WsStep> afterWsStepOptional = Objects.nonNull(currentWsStep.getSubWorkSheet()) ? wsStepRepository.findBySubWorkSheetIdAndStepIdAndDeleted(currentWsStep.getSubWorkSheet().getId(), Long.parseLong(afterStepId), Constants.LONG_ZERO)
                : wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(currentWsStep.getWorkSheet().getId(), Long.parseLong(afterStepId), Constants.LONG_ZERO);

        if (afterWsStepOptional.isEmpty()) {
            throw new ResponseException("afterWsStepNotFound", "后置工序快照不存在");
        }
        return afterWsStepOptional.get();
    }
}
