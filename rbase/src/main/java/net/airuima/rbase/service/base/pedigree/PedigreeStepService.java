package net.airuima.rbase.service.base.pedigree;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.*;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeStep;
import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.proxy.organization.RbaseClientProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepRepository;
import net.airuima.rbase.repository.base.priority.PriorityElementConfigRepository;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.service.base.priority.PriorityElementConfigService;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.util.ExcelUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.*;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工序配置Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PedigreeStepService extends CommonJpaService<PedigreeStep> {
    private final String PEDIGREE_STEP_GRAPH = "pedigreeStepEntityGraph";
    private final PedigreeStepRepository pedigreeStepRepository;

    private final PedigreeRepository pedigreeRepository;

    @Autowired
    private PriorityElementConfigRepository priorityElementConfigRepository;

    @Autowired
    private WorkFlowRepository workFlowRepository;

    @Autowired
    private StepRepository stepRepository;

    @Autowired
    private CommonService commonService;

    @Autowired
    private PriorityElementConfigService priorityElementConfigService;
    @Autowired
    private RbaseClientProxy rbaseClientProxy;


    public PedigreeStepService(PedigreeRepository pedigreeRepository, PedigreeStepRepository pedigreeStepRepository) {
        this.pedigreeRepository = pedigreeRepository;
        this.pedigreeStepRepository = pedigreeStepRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<PedigreeStep> find(Specification<PedigreeStep> spec, Pageable pageable) {
        return pedigreeStepRepository.findAll(spec, pageable,new NamedEntityGraph(PEDIGREE_STEP_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<PedigreeStep> find(Specification<PedigreeStep> spec) {
        return pedigreeStepRepository.findAll(spec,new NamedEntityGraph(PEDIGREE_STEP_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<PedigreeStep> findAll(Pageable pageable) {
        return pedigreeStepRepository.findAll(pageable,new NamedEntityGraph(PEDIGREE_STEP_GRAPH));
    }

    /**
     * 通用的保存前需要验证单支请求模式下不可批量生产及绑定容器
     *
     * @param pedigreeStep 工序配置
     */
    public void saveInstance(PedigreeStep pedigreeStep) {
        if (pedigreeStep.getRequestMode() == ConstantsEnum.SN_REQUEST_MODE.getCategoryName() && pedigreeStep.getControlMode() == ConstantsEnum.BATCH_CONTROL_MODE.getCategoryName()) {
            throw new ResponseException("error.singleRequestModeNotAbleToBatchProduct", "工序单支请求模式下不可配置批量管控模式");
        }
        if (pedigreeStep.getRequestMode() == ConstantsEnum.SN_REQUEST_MODE.getCategoryName() && pedigreeStep.getIsBindContainer()) {
            throw new ResponseException("error.singleRequestModeNotAbleToBatchProduct", "工序单支请求模式下不可绑定容器");
        }
        if(Objects.isNull(pedigreeStep.getPriorityElementConfig()) || Objects.isNull(pedigreeStep.getPriorityElementConfig().getId())){
            // 获取条件优先级
            pedigreeStep.setPriorityElementConfig(getPedigreeStepPriority(pedigreeStep.getClientId(),
                    Objects.nonNull(pedigreeStep.getPedigree())?pedigreeStep.getPedigree().getId():null,
                    Objects.nonNull(pedigreeStep.getWorkFlow())?pedigreeStep.getWorkFlow().getId():null));
        }
        // 校验配置是否重复
        validatePedigreeStep(pedigreeStep);
        if (Objects.isNull(pedigreeStep.getPriorityElementConfig())) {
            throw new ResponseException("error.NoMatchCombination", "没有匹配的组合条件");
        }
        Optional<PriorityElementConfig> priorityElementConfigOptional = priorityElementConfigRepository.findByIdAndDeleted(pedigreeStep.getPriorityElementConfig().getId(), Constants.LONG_ZERO);
        if (priorityElementConfigOptional.isEmpty()) {
            throw new ResponseException("error.NoMatchCombination", "没有匹配的组合条件");
        }
        pedigreeStep.setPriorityElementConfig(priorityElementConfigOptional.get());
        pedigreeStepRepository.save(pedigreeStep);
        this.save(pedigreeStep);
    }

    /**
     * 校验工序配置是否已经存在
     *
     * @param pedigreeStep 工序配置
     */
    private void validatePedigreeStep(PedigreeStep pedigreeStep) {
        //客户代码
        Long clientId = null != pedigreeStep.getClientId() ? pedigreeStep.getClientId():null;
        // 工序id
        Long stepId = null != pedigreeStep.getStep() ? pedigreeStep.getStep().getId() : null;
        // 产品谱系
        Long pedigreeId = null != pedigreeStep.getPedigree() ? pedigreeStep.getPedigree().getId():null;
        // 工艺路线
        Long workFlowId = null != pedigreeStep.getWorkFlow() ? pedigreeStep.getWorkFlow().getId():null;
        // 获取不良项目配置
        PedigreeStep queryPedigreeStep = pedigreeStepRepository.findByClientIdAndPedigreeIdAndWorkFlowIdAndStepIdAndDeleted(clientId,pedigreeId,workFlowId,stepId,Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(pedigreeStep.getId()) && Objects.nonNull(queryPedigreeStep)) {
            throw new ResponseException("error.PedigreeStepExists", "工序配置已经存在");
        }
        if(Objects.nonNull(pedigreeStep.getId()) && Objects.nonNull(queryPedigreeStep) && !pedigreeStep.getId().equals(queryPedigreeStep.getId())){
            throw new ResponseException("error.PedigreeStepExists", "工序配置已经存在");
        }
    }


    /**
     * 查询产品谱系配置所有工序
     *
     * @param pedigreeId 产品谱系id
     * @param keyword    检索关键字
     * @return 产品谱系工序
     */
    @Transactional(readOnly = true)
    public List<Step> findByPedigreeIdAndWorkFlowLikeAndClientId(Long pedigreeId, String keyword, Long clientId) {
        if (StringUtils.isNotBlank(keyword)) {
            return pedigreeStepRepository.findStepByPedigreeIdAndKeywordAndClientId(pedigreeId, keyword, clientId);
        }
        return pedigreeStepRepository.findStepByPedigreeIdAndDeletedAndClientId(pedigreeId, Constants.LONG_ZERO, clientId);
    }

    /**
     * 查询产品谱系配置所有工序(包括父级)
     *
     * @param pedigreeId 产品谱系id
     * @param keyword    检索关键字
     * @return 产品谱系工序
     */
    public Set<Step> findLastStepByPedigreeIdAndWorkFlowLikeAndClientId(Long pedigreeId, String keyword, Long clientId) {
        Set<Step> stepList = new HashSet<>();
        findParentPedigreeStep(stepList, pedigreeId, keyword, clientId);
        return stepList;
    }

    /**
     * 递归获取父级绑定工序
     *
     * @param stepList   工序列表
     * @param pedigreeId 产品谱系id
     */
    private void findParentPedigreeStep(Set<Step> stepList, Long pedigreeId, String keyword, Long clientId) {
        Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByIdAndDeleted(pedigreeId, 0L);
        pedigreeOptional.ifPresent(pedigree -> {
            if (StringUtils.isNotBlank(keyword)) {
                List<Step> steps = pedigreeStepRepository.findStepByPedigreeIdAndKeywordAndClientId(pedigreeId, keyword, clientId);
                stepList.addAll(steps);
            } else {
                List<Step> steps = pedigreeStepRepository.findStepByPedigreeIdAndDeletedAndClientId(pedigreeId, Constants.LONG_ZERO, clientId);
                stepList.addAll(steps);
            }
            Long parentId = pedigreeOptional.map(Pedigree::getParent).map(Pedigree::getId).orElse(null);
            if (parentId == null) {
                return;
            }
            findParentPedigreeStep(stepList, parentId, keyword, clientId);
        });
    }

    /**
     * 重写service save添加投产比例
     *
     * @param entity
     * @return S
     * <AUTHOR>
     * @date 2023/2/15
     */
    @Override
    public <S extends PedigreeStep> S save(S entity) {
        entity.setInputRate(entity.getInputRate() == Constants.DOUBLE_ZERRO ? Constants.INT_ONE : entity.getInputRate());
        return (S) super.save((PedigreeStep) entity);
    }

    /**
     * 根据工艺路线id和工序id获取有效的工序配置
     *
     * @param workFlowId 工艺路线id
     * @param stepId     工序id
     * @return net.airuima.rbase.domain.base.pedigree.PedigreeStep 工序配置
     */
    public PedigreeStep getValidPedigreeStep(Long workFlowId, Long stepId) {
        return pedigreeStepRepository.findByClientIdAndPedigreeIdAndWorkFlowIdAndStepIdAndEnableAndDeleted(null, null, workFlowId, stepId, Constants.TRUE, Constants.LONG_ZERO).orElse(null);
    }

    /**
     * 通过产品谱系ID、工艺路线ID、工序ID、客户ID查询工序配置
     * @param pedigreeId 产品谱系id
     * @param workFlowId 工艺路线id
     * @param stepId 工序ID
     * @param clientId 客户ID
     * @return PedigreeStep
     */
    public PedigreeStep findByPedigreeIdAndWorkFlowIdAndStepIdAndClientId(Long pedigreeId, Long workFlowId, Long stepId, Long clientId){
        return pedigreeStepRepository.findByClientIdAndPedigreeIdAndWorkFlowIdAndStepIdAndDeleted(clientId,pedigreeId,workFlowId,stepId, net.airuima.constant.Constants.LONG_ZERO).orElse(null);
    }


    /**
     * 根据工艺路线id查找工序配置(克隆工艺路线的工序使用)
     *
     * @param workFlowId 工艺路线id
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStep> 工序配置
     */
    @Transactional(readOnly = true)
    public List<PedigreeStep> findByWorkFlowId(Long workFlowId) {
        return pedigreeStepRepository.findByClientIdAndPedigreeIdAndWorkFlowIdAndEnableAndDeleted(null,null,workFlowId,Boolean.TRUE,Constants.LONG_ZERO);
    }

    /**
     * 工序配置导入
     *
     * @param file excel文件
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object> 行数据
     */
    public List<Map<String, Object>> importPedigreeStepExcel(MultipartFile file) {
        // 解析 excel 文件
        List<Map<String, Object>> rowList = ExcelUtils.parseExcel(file);
        List<Map<String, Object>> illegalDataList = new ArrayList<>();
        // 对每一行数据进行处理
        rowList.forEach(row -> {
            try {
                //获取客户代码并转为对象
                String clientCode = String.valueOf(row.get(PedigreeStepExcelConstants.CLIENT_CODE));
                ClientDTO clientDto = StringUtils.isEmpty(clientCode) || "null".equals(clientCode) ? null : rbaseClientProxy.findByCodeAndDeleted(clientCode,Constants.LONG_ZERO);
                if ((!StringUtils.isEmpty(clientCode) && !"null".equals(clientCode)) && (clientDto == null || clientDto.getId() == null)) {
                    throw new ResponseException("error.PedigreeStepErrorPedigreeNotFoundError", "原因客户编码不存在:" + clientCode);
                }
                // 获取产品谱系编码并转换为对象
                String pedigreeCode = String.valueOf(row.get(PedigreeStepExcelConstants.PEDIGREE_CODE));
                Pedigree pedigree = StringUtils.isEmpty(pedigreeCode) || "null".equals(pedigreeCode) ? null : pedigreeRepository.findByCodeAndDeleted(pedigreeCode, Constants.LONG_ZERO)
                        .orElseThrow(() -> new ResponseException( "PedigreeStepErrorPedigreeNotFoundError","原因产品谱系不存在"));
                // 获取工艺路线编码并转换为对象
                String workFlowCode = String.valueOf(row.get(PedigreeStepExcelConstants.WORK_FLOW_CODE));
                WorkFlow workFlow = StringUtils.isEmpty(workFlowCode) || "null".equals(workFlowCode) ? null : workFlowRepository.findByCodeAndDeleted(workFlowCode, Constants.LONG_ZERO)
                        .orElseThrow(() -> new ResponseException("error.PedigreeStepErrorWorkFlowNotFoundError", "原因工艺路线不存在" ));
                // 获取工序编码并转换为对象
                String stepCode = String.valueOf(row.get(PedigreeStepExcelConstants.STEP_CODE));
                Step step = null;
                if (StringUtils.isEmpty(stepCode) || "null".equals(stepCode)) {
                    throw new ResponseException("error.PedigreeStepUnqualifiedItemErrorStepNotFoundError", "导入Excel失败,数据有误, 原因工序不存在");
                } else {
                    step = stepRepository.findByCodeAndDeleted(stepCode, Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.PedigreeStepUnqualifiedItemErrorStepNotFoundError", "原因工序不存在"));
                }    // 获取请求模式
                if(null != pedigree && !pedigree.getIsEnable()){
                    throw new ResponseException("error.pedigreeDisabled", "产品谱系["+pedigree.getCode()+"]已禁用");
                }
                if(null != step && !step.getIsEnable()){
                    throw new ResponseException("error.stepDisabled", "工序["+step.getCode()+"]已禁用");
                }
                if(null != workFlow && !workFlow.getIsEnable()){
                    throw new ResponseException("error.workFlowDisabled", "工艺路线["+workFlow.getCode()+"]已禁用");
                }
                Integer requestMode = RequestModeEnum.getCodeByMode(String.valueOf(String.valueOf(row.get(PedigreeStepExcelConstants.REQUEST_MODE))));
                // 获取管控模式
                Integer controlMode = ControlModeEnum.getCodeByMode(String.valueOf(String.valueOf(row.get(PedigreeStepExcelConstants.CONTROL_MODE))));
                // 获取是否管控物料
                Boolean isControlMaterial = String.valueOf(row.get(PedigreeStepExcelConstants.IS_CONTROL_MATERIAL)).equals("是") ? Boolean.TRUE : Boolean.FALSE;
                // 获取是否绑定容器
                Boolean isBindContainer = String.valueOf(row.get(PedigreeStepExcelConstants.IS_BIND_CONTAINER)).equals("是") ? Boolean.TRUE : Boolean.FALSE;
                // 获取投产比例
                Double inputRate = Double.valueOf(String.valueOf(row.get(PedigreeStepExcelConstants.INPUT_RATE)));
                // 获取标准工时
                BigDecimal standardTime = "null".equals(String.valueOf(row.get(PedigreeStepExcelConstants.STANDARD_TIME))) ? new BigDecimal("0") : new BigDecimal(String.valueOf(row.get(PedigreeStepExcelConstants.STANDARD_TIME)));
                // 获取工序理论日产出数量
                Long standardDailyOutput = "null".equals(String.valueOf(row.get(PedigreeStepExcelConstants.STANDARD_DAILY_OUTPUT))) ? Constants.LONG_ZERO : Long.parseLong(String.valueOf(row.get(PedigreeStepExcelConstants.STANDARD_DAILY_OUTPUT)));
                // 获取是否启用
                Boolean isEnable = String.valueOf(row.get(PedigreeStepExcelConstants.IS_ENABLE)).equals("是") ? Boolean.TRUE : Boolean.FALSE;
                // 保存
                // 客户id
                Long clientId = Optional.ofNullable(clientDto).map(ClientDTO::getId).orElse(null);
                // 工序id
                Long stepId = Optional.ofNullable(step).map(Step::getId).orElse(null);
                // 产品谱系
                Long pedigreeId = Optional.ofNullable(pedigree).map(Pedigree::getId).orElse(null);
                // 工艺路线
                Long workFlowId = Optional.ofNullable(workFlow).map(WorkFlow::getId).orElse(null);
                // 查询配置是否存在
                PedigreeStep queryPedigreeStep = pedigreeStepRepository.findByClientIdAndPedigreeIdAndWorkFlowIdAndStepIdAndDeleted(clientId, pedigreeId, workFlowId, stepId,Constants.LONG_ZERO).orElse(null);
                // 获取条件优先级
                PriorityElementConfig pedigreeStepPriority = getPedigreeStepPriority(clientId, pedigreeId, workFlowId);
                // 保存工序配置
                savePedigreeStep(clientId, pedigree, workFlow, step, requestMode, controlMode, isControlMaterial, isBindContainer, inputRate, standardTime, standardDailyOutput, isEnable, queryPedigreeStep, pedigreeStepPriority);
            } catch (Exception e) {
                e.printStackTrace();
                row.put("错误信息", e.getMessage());
                illegalDataList.add(row);
            }
        });
        return illegalDataList;
    }

    /**
     * 保存工序配置
     *
     * @param pedigree             产品谱系
     * @param workFlow             工艺路线
     * @param step                 工序
     * @param requestMode          请求模式
     * @param controlMode          管控模式
     * @param isControlMaterial    是否管控物料
     * @param isBindContainer      是否绑定容器
     * @param inputRate            投产比例
     * @param standardTime         标注工时
     * @param standardDailyOutput  工序理论日产出数量
     * @param isEnable             是否启用
     * @param queryPedigreeStep    查询的工序配置
     * @param pedigreeStepPriority 条件优先级
     */
    private void savePedigreeStep(Long clientId, Pedigree pedigree, WorkFlow workFlow, Step step, Integer requestMode, Integer controlMode, Boolean isControlMaterial, Boolean isBindContainer, Double inputRate, BigDecimal standardTime, Long standardDailyOutput, Boolean isEnable, PedigreeStep queryPedigreeStep, PriorityElementConfig pedigreeStepPriority) {
        // 存在则覆盖 否则更新
        if (Objects.nonNull(queryPedigreeStep)) {
            queryPedigreeStep.setPriorityElementConfig(pedigreeStepPriority)
                    .setRequestMode(requestMode)
                    .setControlMode(controlMode)
                    .setIsControlMaterial(isControlMaterial)
                    .setIsBindContainer(isBindContainer)
                    .setInputRate(inputRate)
                    .setStandardTime(standardTime)
                    .setStandardDailyOutput(standardDailyOutput)
                    .setEnable(isEnable);
            pedigreeStepRepository.save(queryPedigreeStep);
        } else {
            PedigreeStep pedigreeStep = new PedigreeStep();
            pedigreeStep
                    .setClientId(clientId)
                    .setPedigree(pedigree)
                    .setWorkFlow(workFlow)
                    .setStep(step)
                    .setPriorityElementConfig(pedigreeStepPriority)
                    .setRequestMode(requestMode)
                    .setControlMode(controlMode)
                    .setIsControlMaterial(isControlMaterial)
                    .setIsBindContainer(isBindContainer)
                    .setInputRate(inputRate)
                    .setStandardTime(standardTime)
                    .setStandardDailyOutput(standardDailyOutput)
                    .setEnable(isEnable);
            pedigreeStepRepository.save(pedigreeStep);
        }
    }

    /**
     * 获取工序条件优先级配置
     *
     * @param clientId   客户代码id
     * @param pedigreeId 产品谱系id
     * @param workFlowId 工艺路线id
     * @return net.airuima.rbase.domain.base.priority.PriorityElementConfig 优先级配置
     */
    public PriorityElementConfig getPedigreeStepPriority(Long clientId, Long pedigreeId, Long workFlowId) {
        List<Integer> combination = new ArrayList<>();
        if (Objects.nonNull(pedigreeId)) {
            combination.add(Constants.PEDIGREE_ELEMENT);
        }
        if (Objects.nonNull(workFlowId)) {
            combination.add(Constants.WORKFLOW_ELEMENT);
        }
        if (Objects.nonNull(clientId)) {
            combination.add(Constants.CLIENT_ELEMENT);
        }
        combination.add(Constants.STEP_ELEMENT);
        //校验是否已有相同条件
        PriorityElementConfig priorityElementConfig = priorityElementConfigService.findUniquePriorityElementConfig(Constants.INT_THREE,combination);
        if (Objects.isNull(priorityElementConfig)) {
            throw new ResponseException("error.NoMatchCombination", "没有匹配的组合条件");
        }
        return priorityElementConfig;
    }

    /**
     * 删除工序配置
     *
     * @param id 工序配置id
     */
    public void deleteEntity(Long id) {
        pedigreeStepRepository.deleteByPedigreeStepIdAndDeleted(id);
    }


    /**
     * 详情重写
     *
     * @param id 工序配置ID
     * @return net.airuima.rbase.domain.base.pedigree.PedigreeStep 工序配置
     */
    public PedigreeStep get(Long id) {
        return pedigreeStepRepository.findByIdAndDeleted(id, Constants.LONG_ZERO).orElse(null);
    }
}
