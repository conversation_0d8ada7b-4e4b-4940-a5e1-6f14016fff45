package net.airuima.rbase.service.procedure.single;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.rbase.domain.procedure.single.SnUnqualifiedItem;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.repository.procedure.single.SnUnqualifiedItemRepository;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 单支生产过程产生不良记录Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SnUnqualifiedItemService extends CommonJpaService<SnUnqualifiedItem> {
    private static final String SN_UNQUALIFIED_ITEM_ENTITY_GRAPH = "snUnqualifiedItemEntityGraph";
    private final SnUnqualifiedItemRepository snUnqualifiedItemRepository;

    public SnUnqualifiedItemService(SnUnqualifiedItemRepository snUnqualifiedItemRepository) {
        this.snUnqualifiedItemRepository = snUnqualifiedItemRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<SnUnqualifiedItem> find(Specification<SnUnqualifiedItem> spec, Pageable pageable) {
        return snUnqualifiedItemRepository.findAll(spec, pageable,new NamedEntityGraph(SN_UNQUALIFIED_ITEM_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<SnUnqualifiedItem> find(Specification<SnUnqualifiedItem> spec) {
        return snUnqualifiedItemRepository.findAll(spec,new NamedEntityGraph(SN_UNQUALIFIED_ITEM_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<SnUnqualifiedItem> findAll(Pageable pageable) {
        return snUnqualifiedItemRepository.findAll(pageable,new NamedEntityGraph(SN_UNQUALIFIED_ITEM_ENTITY_GRAPH));
    }

}
