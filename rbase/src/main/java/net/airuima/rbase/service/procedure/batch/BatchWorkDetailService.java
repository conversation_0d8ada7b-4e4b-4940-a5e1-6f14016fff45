package net.airuima.rbase.service.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.*;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.rbase.domain.procedure.batch.*;
import net.airuima.rbase.domain.procedure.material.WsMaterialBatch;
import net.airuima.rbase.domain.procedure.material.WsWorkCellMaterialBatch;
import net.airuima.rbase.domain.procedure.report.StaffPerform;
import net.airuima.rbase.domain.procedure.single.SnUnqualifiedItem;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.batch.PreContainerDetailInfo;
import net.airuima.rbase.dto.maintain.MaintainHistoryDetailDTO;
import net.airuima.rbase.proxy.maintain.RbaseMaintainHistoryDetailProxy;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WsReworkRepository;
import net.airuima.rbase.repository.procedure.batch.*;
import net.airuima.rbase.repository.procedure.material.WsMaterialBatchRepository;
import net.airuima.rbase.repository.procedure.material.WsWorkCellMaterialBatchRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformUnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.single.*;
import net.airuima.rbase.repository.procedure.wearingpart.BatchWorkDetailWearingPartRepository;
import net.airuima.rbase.repository.procedure.wearingpart.ContainerDetailWearingPartRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.ocmes.BakeCycleBakeAgeingModelService;
import net.airuima.rbase.service.procedure.aps.SubWorkSheetService;
import net.airuima.rbase.service.procedure.aps.api.IProductionPlanService;
import net.airuima.rbase.service.procedure.batch.api.IBatchWorkDetailService;
import net.airuima.rbase.service.procedure.batch.api.IRollbackStepService;
import net.airuima.rbase.service.procedure.batch.dto.ContainerDetailReplaceDTO;
import net.airuima.rbase.service.procedure.batch.dto.MaterialBatchDTO;
import net.airuima.rbase.service.procedure.batch.impl.PIRworkerCacheService;
import net.airuima.rbase.service.procedure.quality.CheckHistoryService;
import net.airuima.rbase.service.procedure.single.SnWorkDetailService;
import net.airuima.rbase.service.report.api.IWorkSheetStatisticsService;
import net.airuima.rbase.util.MapperUtils;
import net.airuima.rbase.util.NumberUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rbase.web.rest.procedure.batch.dto.BatchWorkDetailGetDTO;
import net.airuima.service.CommonJpaService;
import net.airuima.util.BeanUtil;
import net.airuima.util.FuncKeyUtil;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 批量工序生产详情Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class BatchWorkDetailService extends CommonJpaService<BatchWorkDetail> implements IBatchWorkDetailService {
    private static final String BATCH_WORK_DETAIL_ENTITY_GRAPH = "batchWorkDetailEntityGraph";
    private final BatchWorkDetailRepository batchWorkDetailRepository;
    private final SubWorkSheetRepository subWorkSheetRepository;
    private final WsStepRepository wsStepRepository;
    @Autowired
    private SnWorkDetailRepository snWorkDetailRepository;
    @Autowired
    private WsStepUnqualifiedItemRepository wsStepUnqualifiedItemRepository;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private SnWorkStatusRepository snWorkStatusRepository;
    @Autowired
    private ContainerDetailMaterialBatchRepository containerDetailMaterialBatchRepository;
    @Autowired
    private ContainerDetailFacilityRepository containerDetailFacilityRepository;
    @Autowired
    private ContainerDetailUnqualifiedItemRepository containerDetailUnqualifiedItemRepository;
    @Autowired
    private SnWorkDetailMaterialBatchRepository snWorkDetailMaterialBatchRepository;
    @Autowired
    private SnWorkDetailFacilityRepository snWorkDetailFacilityRepository;
    @Autowired
    private SnUnqualifiedItemRepository snUnqualifiedItemRepository;
    @Autowired
    private BatchWorkDetailMaterialBatchRepository batchWorkDetailMaterialBatchRepository;
    @Autowired
    private RollBackHistoryRepository rollBackHistoryRepository;
    @Autowired
    private ContainerDetailService containerDetailService;
    @Autowired
    private BatchWorkDetailFacilityRepository batchWorkDetailFacilityRepository;
    @Autowired
    private WsMaterialBatchRepository wsMaterialBatchRepository;
    @Autowired
    private WsWorkCellMaterialBatchRepository wsWorkCellMaterialBatchRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private SnWorkDetailService snWorkDetailService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private StaffPerformRepository staffPerformRepository;
    @Autowired
    private StaffPerformUnqualifiedItemRepository staffPerformUnqualifiedItemRepository;
    @Autowired
    private BatchWorkDetailWearingPartRepository batchWorkDetailWearingPartRepository;
    @Autowired
    private ContainerDetailWearingPartRepository containerDetailWearingPartRepository;
    @Autowired
    private BakeCycleBakeAgeingModelService[] bakeCycleBakeAgeingModelServices;
    @Autowired
    private IProductionPlanService[] productionPlanServices;
    @Autowired
    private IWorkSheetStatisticsService[] workSheetStatisticsServices;
    @Autowired
    private WsReworkRepository wsReworkRepository;
    @Autowired
    private SubWorkSheetService subWorkSheetService;
    @Autowired
    private PIRworkerCacheService rworkerCacheServices;
    @Autowired
    private IRollbackStepService[] iRollbackStepServices;
    @Autowired
    private StepRepository stepRepository;
    @Autowired
    private RbaseMaintainHistoryDetailProxy rbaseMaintainHistoryDetailProxy;
    @Autowired
    private CheckHistoryService checkHistoryService;

    public BatchWorkDetailService(BatchWorkDetailRepository batchWorkDetailRepository, SubWorkSheetRepository subWorkSheetRepository, WsStepRepository wsStepRepository) {
        this.batchWorkDetailRepository = batchWorkDetailRepository;
        this.subWorkSheetRepository = subWorkSheetRepository;
        this.wsStepRepository = wsStepRepository;
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<BatchWorkDetail> find(Specification<BatchWorkDetail> spec, Pageable pageable) {
        return batchWorkDetailRepository.findAll(spec, pageable, new NamedEntityGraph(BATCH_WORK_DETAIL_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public List<BatchWorkDetail> find(Specification<BatchWorkDetail> spec) {
        return batchWorkDetailRepository.findAll(spec, new NamedEntityGraph(BATCH_WORK_DETAIL_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<BatchWorkDetail> findAll(Pageable pageable) {
        return batchWorkDetailRepository.findAll(pageable, new NamedEntityGraph(BATCH_WORK_DETAIL_ENTITY_GRAPH));
    }

    /**
     * 根据子工单ID根据层级获取已做或未做的工序详情
     *
     * @param subWsId 子工单ID
     * @return List<BatchWorkDetail> 工序生产详情集合
     * <AUTHOR>
     * @date 2021-03-31
     **/
    @Transactional(readOnly = true)
    public List<BatchWorkDetailGetDTO.SubWorkSheetBatchWorkDetailDTO> findProcessStepBySubWsId(Long subWsId) {
        SubWorkSheet subWorkSheet = subWorkSheetRepository.findByIdAndDeleted(subWsId, Constants.LONG_ZERO).orElse(null);
        if (null == subWorkSheet) {
            return Collections.emptyList();
        }
        //优先获取子工单的定制工序，再次获取总工单的定制工序
        List<WsStep> wsStepList = wsStepRepository.findBySubWorkSheetIdAndDeleted(subWsId, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(wsStepList)) {
            wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(subWorkSheet.getWorkSheet().getId(), Constants.LONG_ZERO);
        }
        if (!ValidateUtils.isValid(wsStepList)) {
            return Collections.emptyList();
        }
        List<WsStep> originWsStepList = net.airuima.rbase.util.MapperUtils.mapAll(wsStepList,WsStep.class);
        //定制工序转工序集合
        List<Step> stepList = wsStepToStepList(wsStepList);
        if (!ValidateUtils.isValid(stepList)) {
            return Collections.emptyList();
        }
        //通过子工单id查找工单生产详情
        List<BatchWorkDetail> batchWorkDetails = batchWorkDetailRepository.findBySubWorkSheetIdAndDeleted(subWsId, Constants.LONG_ZERO);
        //组合工序+批量生产详情数据
        Map<Long,List<WsStep>> wsStepGroupMap = originWsStepList.stream().collect(Collectors.groupingBy(wsStep -> wsStep.getStep().getId()));
        List<BatchWorkDetail> batchWorkDetailListFinal = getStepAndBatchWorkDetailList(stepList,wsStepGroupMap, batchWorkDetails, subWorkSheet.getWorkSheet(), subWorkSheet);
        return net.airuima.rbase.util.MapperUtils.mapAll(batchWorkDetailListFinal,BatchWorkDetailGetDTO.SubWorkSheetBatchWorkDetailDTO.class);

    }

    /**
     * 根据总工单ID获取工序生产详情信息
     *
     * @param wsId 总工单ID
     * @return List<BatchWorkDetail> 工序生产详情集合
     **/
    @Transactional(readOnly = true)
    public List<BatchWorkDetailGetDTO.WorkSheetBatchWorkDetailDTO> findProcessStepByWsId(Long wsId) {
        //通过id查找总工单
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findByIdAndDeleted(wsId, Constants.LONG_ZERO);
        if (!workSheetOptional.isPresent()) {
            return Collections.emptyList();
        }
        WorkSheet workSheet = workSheetOptional.get();
        //获取总工单的定制工序
        List<WsStep> wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(wsId, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(wsStepList)) {
            return Collections.emptyList();
        }
        List<WsStep> originWsStepList = net.airuima.rbase.util.MapperUtils.mapAll(wsStepList,WsStep.class);
        //定制工序转工序集合
        List<Step> stepList = wsStepToStepList(wsStepList);
        if (!ValidateUtils.isValid(stepList)) {
            return Collections.emptyList();
        }
        //通过总工单id查找工单生产详情
        List<BatchWorkDetail> batchWorkDetails = batchWorkDetailRepository.findByWorkSheetIdAndDeleted(wsId, Constants.LONG_ZERO);
        //组合工序+批量生产详情数据
        Map<Long,List<WsStep>> wsStepGroupMap = originWsStepList.stream().collect(Collectors.groupingBy(wsStep -> wsStep.getStep().getId()));
        List<BatchWorkDetail> batchWorkDetailListFinal = getStepAndBatchWorkDetailList(stepList, wsStepGroupMap,batchWorkDetails, workSheet, null);
        return MapperUtils.mapAll(batchWorkDetailListFinal,BatchWorkDetailGetDTO.WorkSheetBatchWorkDetailDTO.class);

    }

    /**
     * 定制工序转工序集合
     *
     * @param wsStepList
     * @return : java.util.List<net.airuima.rbase.domain.base.process.Step>
     * <AUTHOR>
     * @date 2022/11/9
     **/
    public List<Step> wsStepToStepList(List<WsStep> wsStepList) {
        List<Step> stepList = Lists.newArrayList();
        List<WsStep> finalWsStepList = Lists.newArrayList();
        finalWsStepList.addAll(wsStepList);
        this.findTreeStep(wsStepList, null, stepList, finalWsStepList);
        return stepList;
    }

    /**
     * 组合工序+批量生产详情数据
     *
     * @param stepList
     * @param batchWorkDetailList
     * @param workSheet           工单
     * @param subWorkSheet        子工单
     * @return List<BatchWorkDetail> 工序生产详情集合
     * <AUTHOR>
     * @date 2022/11/9
     **/
    public List<BatchWorkDetail> getStepAndBatchWorkDetailList(List<Step> stepList,Map<Long,List<WsStep>> wsStepGroupMap, List<BatchWorkDetail> batchWorkDetailList, WorkSheet workSheet, SubWorkSheet subWorkSheet) {
        // 已做的工序详情则直接加入数组，反之则new一个BatchWorkDetail
        List<BatchWorkDetail> allBatchWorkDetails = Lists.newArrayList();
        stepList.forEach(step -> {
            WsStep wsStep = wsStepGroupMap.get(step.getId()).get(Constants.INT_ZERO);
            if (ValidateUtils.isValid(wsStep.getAfterStepId())){
                List<Long> afterStepIdList = Arrays.stream(wsStep.getAfterStepId().split(",")).map(Long::parseLong).toList();
                List<Step> steps = stepRepository.findByIdInAndDeleted(afterStepIdList, Constants.LONG_ZERO);
                if (ValidateUtils.isValid(steps)){
                    step.setCustom1(steps.stream().map(Step::getCode).collect(Collectors.joining(",")));
                }
            }
            if (!ValidateUtils.isValid(batchWorkDetailList)) {
                BatchWorkDetail batchWorkDetail = new BatchWorkDetail().setStep(step).setWorkSheet(workSheet).setSubWorkSheet(subWorkSheet);
                batchWorkDetail.setWorkFlowId(Objects.nonNull(wsStep.getWorkFlow())?wsStep.getWorkFlow().getId():workSheet.getWorkFlow().getId());
                allBatchWorkDetails.add(batchWorkDetail);
            } else {
                Optional<BatchWorkDetail> batchWorkDetailOptional = batchWorkDetailList.stream().filter(batchWorkDetail -> batchWorkDetail.getStep().getId().equals(step.getId())).findFirst();
                BatchWorkDetail batchWorkDetail = batchWorkDetailOptional.orElseGet(() -> new BatchWorkDetail().setStep(step).setWorkSheet(workSheet).setSubWorkSheet(subWorkSheet));
                batchWorkDetail.setWorkFlowId(Objects.nonNull(wsStep.getWorkFlow())?wsStep.getWorkFlow().getId():workSheet.getWorkFlow().getId());
                allBatchWorkDetails.add(batchWorkDetail);
            }
        });
        return allBatchWorkDetails;
    }


    /**
     * 递归获取同层级工序信息
     *
     * @param wsStepList
     * @param parentIds
     * @param stepList
     * @return void
     * <AUTHOR>
     * @date 2021-03-31
     **/
    public void findTreeStep(List<WsStep> wsStepList, List<Long> parentIds, List<Step> stepList, List<WsStep> finalWsStepList) {
        List<WsStep> currStageWsSteps = null;
        if (!ValidateUtils.isValid(wsStepList)) {
            return;
        }
        //区分前置工序为空与前置工序不为空的情形
        if (ValidateUtils.isValid(parentIds)) {
            List<Long> finalParentIds = parentIds;
            currStageWsSteps = wsStepList.stream().filter(wsStep -> finalParentIds.stream().anyMatch(parentId -> wsStep.getPreStepId().contains(parentId.toString()))).collect(Collectors.toList());
        } else {
            currStageWsSteps = wsStepList.stream().filter(wsStep -> StringUtils.isBlank(wsStep.getPreStepId())).collect(Collectors.toList());
        }
        stepList.addAll(currStageWsSteps.stream().map(WsStep::getStep).collect(Collectors.toList()));
        //添加下一道工序编码
        orderStep(finalWsStepList, currStageWsSteps, stepList);
        parentIds = Lists.newArrayList();
        parentIds.addAll(stepList.stream().map(Step::getId).collect(Collectors.toList()));
        wsStepList.removeAll(currStageWsSteps);
        findTreeStep(wsStepList, parentIds, stepList, finalWsStepList);
    }

    /**
     * 添加有序的工序的下一道工序标记
     *
     * @param wsStepList    工序快照列表
     * @param currWsSteps   当前待排序工序
     * @param orderStepList 已排序工序
     * @return void
     * <AUTHOR>
     * @date 2022/2/18
     */
    public void orderStep(List<WsStep> wsStepList, List<WsStep> currWsSteps, List<Step> orderStepList) {
        currWsSteps.stream().forEach(currWsStep -> {
            if (ValidateUtils.isValid(currWsStep.getAfterStepId())) {
                List<Long> afterSteps = Arrays.stream(currWsStep.getAfterStepId().split(Constants.STR_COMMA)).map(Long::parseLong).collect(Collectors.toList());
                if (ValidateUtils.isValid(afterSteps)) {
                    List<WsStep> afterWsSteps = wsStepList.stream().filter(wsStep -> afterSteps.contains(wsStep.getStep().getId())).collect(Collectors.toList());
                    orderStepList.stream().filter(orderStep -> currWsStep.getStep().getId().equals(orderStep.getId()))
                            .forEach(orderStep -> orderStep.setCustom1(afterWsSteps.stream().map(wsStep -> wsStep.getStep().getCode()).collect(Collectors.toList()).toString()));
                }
            }
        });
    }

    /**
     * 验证回退工序（容器的合法性） 返回对应的占用容器详情
     *
     * @param batchWorkDetail 工作详情
     * @param containerDetail 容器详情
     * @return ContainerDetailReplaceDTO
     * <AUTHOR>
     * @date 2022/2/18
     */
    public ContainerDetailReplaceDTO validateContainer(BatchWorkDetail batchWorkDetail, ContainerDetail containerDetail) {
        WorkSheet workSheet = null != batchWorkDetail.getSubWorkSheet() ? batchWorkDetail.getSubWorkSheet().getWorkSheet() : batchWorkDetail.getWorkSheet();
        SubWorkSheet subWorkSheet = batchWorkDetail.getSubWorkSheet();

        // 如果工单或者子工单存在缓存，则不让回退
        String mode = commonService.getDictionaryData(Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
        rworkerCacheServices.validateCacheWhenRequestTodoStep(
                subWsProductionMode ? Constants.INT_ZERO : Constants.INT_ONE,
                subWsProductionMode ? batchWorkDetail.getSubWorkSheet().getSerialNumber() : batchWorkDetail.getWorkSheet().getSerialNumber()
        );

        //已完成的工单不允许删除容器详情信息
        if (null != subWorkSheet && subWorkSheet.getStatus() >= Constants.FINISH) {
            return new ContainerDetailReplaceDTO(Constants.KO, "子工单已完成");
        }
        if (workSheet.getStatus() >= Constants.FINISH) {
            return new ContainerDetailReplaceDTO(Constants.KO, "工单已完成");
        }
        //定制工序不存在不允许删除容器详情信息
        WsStep currWsStep = null != subWorkSheet? wsStepRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), batchWorkDetail.getStep().getId(), Constants.LONG_ZERO).orElse(null):null;
        //定制工序不存在不允许删除容器详情信息
        if(null == currWsStep) {
            currWsStep = wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), batchWorkDetail.getStep().getId(), Constants.LONG_ZERO).orElse(null);
        }
        if (null == currWsStep) {
            return new ContainerDetailReplaceDTO(Constants.KO, "工序快照不存在");
        }
        //外协工序 以及外协前置工序禁止回退
        iRollbackStepServices[0].validStepOemRollbackStep(currWsStep);
        //回退验证质检历史是否存在未处理的抽检终检历史任务
        checkHistoryService.validRollBackCheckHistory(subWorkSheet,workSheet,currWsStep.getStep());
        //验证后置工序是否已经录入(containerDetail == null 说明是工序验证，容器验证不需要)
        if (StringUtils.isNotBlank(currWsStep.getAfterStepId())) {
            ContainerDetailReplaceDTO containerDetailReplaceDTO = BeanUtil.getHighestPrecedenceBean(IBatchWorkDetailService.class).validAfterStep(currWsStep, batchWorkDetail, containerDetail);
            if (!ObjectUtils.isEmpty(containerDetailReplaceDTO)) {
                return containerDetailReplaceDTO;
            }
        }
        //验证每个SN最后完成的工序是否为当前工序(区分：工序回退，以及 容器回退)
        List<SnWorkDetail> snWorkDetailList = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(containerDetail)) {
            snWorkDetailList = snWorkDetailRepository.findByContainerDetailIdAndDeleted(containerDetail.getId(), Constants.LONG_ZERO);
        } else if (null != subWorkSheet) {
            snWorkDetailList = snWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), batchWorkDetail.getStep().getId(), Constants.LONG_ZERO);
        } else {
            snWorkDetailList = snWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), batchWorkDetail.getStep().getId(), Constants.LONG_ZERO);
        }
        if (ValidateUtils.isValid(snWorkDetailList)) {
            boolean isSnLatestStepIllegal = snWorkDetailList.stream().anyMatch(snWorkDetail -> {
                Optional<SnWorkStatus> snWorkStatusOptional = snWorkStatusRepository.findBySnAndDeleted(snWorkDetail.getSn(), Constants.LONG_ZERO);
                return snWorkStatusOptional.filter(snWorkStatus -> !snWorkStatus.getLatestSnWorkDetail().getId().equals(snWorkDetail.getId())).isPresent();
            });
            if (isSnLatestStepIllegal) {
                //工单SN最后生产工序非当前工序
                return new ContainerDetailReplaceDTO(Constants.KO, "SN生产状态中最新完成的工序非当前待删除的工序");
            }
        }

        //验证当前工序是否存在所产生的不合格项目已生成在线返修单
        BaseDTO resultDto = verifyReworkOrderCreation(workSheet, subWorkSheet, batchWorkDetail.getStep(),containerDetail);
        if (Constants.KO.equals(resultDto.getStatus())){
            return new ContainerDetailReplaceDTO(resultDto.getStatus(),resultDto.getMessage());
        }
        if (ValidateUtils.isValid(currWsStep.getPreStepId())) {
            List<Long> preStepIdList = Arrays.stream(currWsStep.getPreStepId().split(Constants.STR_COMMA)).map(Long::parseLong).collect(Collectors.toList());

            //上道工序的工作详情
            List<BatchWorkDetail> preBatchWorkDetailList = null != subWorkSheet ? batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdInAndDeleted(subWorkSheet.getId(), preStepIdList, Constants.LONG_ZERO)
                    : batchWorkDetailRepository.findByWorkSheetIdAndStepIdInAndDeleted(workSheet.getId(), preStepIdList, Constants.LONG_ZERO);
            //上道工序的容器详情(处于解绑状态的容器详情)
            List<ContainerDetail> preContainerDetails = containerDetailRepository.findByBatchWorkDetailIdInAndStatusDeleted(preBatchWorkDetailList.stream().map(BatchWorkDetail::getId).collect(Collectors.toList()), ConstantsEnum.UNBIND.getCategoryName(), Constants.LONG_ZERO);
            if (ValidateUtils.isValid(preContainerDetails)) {
                //获取上道工序已使用的容器，在其他地方绑定的容器详情信息
                List<ContainerDetail> allContainerDetails = containerDetailRepository.findByContainerIdInAndStatusAndDeleted(preContainerDetails.stream().map(preContainerDetail -> preContainerDetail.getContainer().getId()).collect(Collectors.toList()), ConstantsEnum.BINDING.getCategoryName(), Constants.LONG_ZERO);
                //剔除掉原容器转原容器
                if (null != containerDetail && StringUtils.isNotBlank(containerDetail.getPreContainerCodeList())) {
                    allContainerDetails = allContainerDetails.stream().filter(containerDetailOptional -> !containerDetailOptional.getContainerCode().equals(containerDetail.getContainerCode())).collect(Collectors.toList());
                    //获取到当前容器对应的上道流转被占容器号
                    List<String> preContainerDetailCodes = Arrays.stream(containerDetail.getPreContainerCodeList().split(Constants.STR_SEMICOLON)).map(String::valueOf).collect(Collectors.toList());
                    if (net.airuima.util.ValidateUtils.isValid(preContainerDetailCodes)) {
                        allContainerDetails = allContainerDetails.stream().filter(containerDetailOptional -> preContainerDetailCodes.contains(containerDetailOptional.getContainerCode())).collect(Collectors.toList());
                    }
                } else {
                    List<ContainerDetail> containerDetailList = containerDetailRepository.findByBatchWorkDetailIdAndDeleted(batchWorkDetail.getId(), Constants.LONG_ZERO);
                    allContainerDetails = allContainerDetails.stream().filter(containerDetails -> !containerDetailList.stream().map(ContainerDetail::getId).collect(Collectors.toList()).contains(containerDetails.getId())).collect(Collectors.toList());
                    List<ContainerDetail> finalPreContainerDetails = preContainerDetails;
                    allContainerDetails = allContainerDetails.stream().filter(containerDetails ->
                            !finalPreContainerDetails.stream()
                                    .filter(preContainerDetail -> preContainerDetail.getStatus() == ConstantsEnum.BINDING.getCategoryName())
                                    .map(ContainerDetail::getId).toList().contains(containerDetails.getId())).collect(Collectors.toList());

                }
                //返回的容器详情为当前工序的上一道工序容器详情
                if (ValidateUtils.isValid(allContainerDetails)) {
                    List<ContainerDetail> finalAllContainerDetails = allContainerDetails;
                    preContainerDetails = preContainerDetails.stream().filter(preContainerDetail -> finalAllContainerDetails.stream().map(ContainerDetail::getContainerCode).collect(Collectors.toList()).contains(preContainerDetail.getContainerCode())).collect(Collectors.toList());
                    return new ContainerDetailReplaceDTO(Constants.OK, "ContainerOccupied", preContainerDetails);

                }
            }
        }
        return new ContainerDetailReplaceDTO(Constants.OK, "CheckSucceed");
    }

    /**
     * 验证当前工序是否生成返工单
     *
     * @param   workSheet 工单
     * @param   subWorkSheet 子工单
     * @param   step 工序
     * @return  BaseDTO
     */
    public BaseDTO verifyReworkOrderCreation(WorkSheet workSheet, SubWorkSheet subWorkSheet, Step step,ContainerDetail containerDetail){

        //验证当前工序是否存在所产生的不合格项目已生成在线返修单（批量模式下界面生成返工单）

        List<WsStepUnqualifiedItem> wsStepUnqualifiedItemList =  Objects.nonNull(subWorkSheet)?
                wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndFlagAndDeleted(subWorkSheet.getId(), step.getId(), Constants.TRUE, Constants.LONG_ZERO):
                wsStepUnqualifiedItemRepository.findByWorkSheetIdAndStepIdAndFlagAndDeleted(workSheet.getId(), step.getId(), Constants.TRUE, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(wsStepUnqualifiedItemList) && Objects.isNull(containerDetail)) {
            //工序对应不合格项目已生成在线返修单
            return new BaseDTO(Constants.KO, "工序已经生成返工单");
        }

        if (ValidateUtils.isValid(wsStepUnqualifiedItemList) && Objects.nonNull(containerDetail)){
            List<ContainerDetailUnqualifiedItem> containerDetailUnqualifiedItems = containerDetailUnqualifiedItemRepository.findByContainerDetailIdAndDeleted(containerDetail.getId(), net.airuima.constant.Constants.LONG_ZERO);
            if (net.airuima.util.ValidateUtils.isValid(containerDetailUnqualifiedItems)
                    && (containerDetailUnqualifiedItems.stream().map(ContainerDetailUnqualifiedItem::getUnqualifiedItem)
                    .anyMatch(unqualifiedItem -> wsStepUnqualifiedItemList.stream().map(WsStepUnqualifiedItem::getUnqualifiedItem).anyMatch(wsUnqualifiedItem ->
                            wsUnqualifiedItem.getId().equals(unqualifiedItem.getId()))))) {
                return new BaseDTO(Constants.KO, "工序中容器已经生成返工单");
            }
        }

        //验证工单工序是否在维修分析生成返工单
        if (Objects.nonNull(containerDetail)){
            List<MaintainHistoryDetailDTO> maintainHistoryDetails = rbaseMaintainHistoryDetailProxy.findByMaintainHistoryContainerDetailIdAndDeletedAndWsReworkIsNotNull(containerDetail.getId(), Constants.LONG_ZERO);
            if (ValidateUtils.isValid(maintainHistoryDetails)){
                return new BaseDTO(Constants.KO, "工序中容器已经生成返工单");
            }
        }else {
            List<MaintainHistoryDetailDTO> maintainHistoryDetails = Objects.nonNull(subWorkSheet)?
                    rbaseMaintainHistoryDetailProxy.findByMaintainHistorySubWorkSheetIdAndMaintainHistoryStepIdAndDeletedAndWsReworkIsNotNull(subWorkSheet.getId(), step.getId(),Constants.LONG_ZERO):
                    rbaseMaintainHistoryDetailProxy.findByMaintainHistoryWorkSheetIdAndMaintainHistoryStepIdAndDeletedAndWsReworkIsNotNull(workSheet.getId(), step.getId(),Constants.LONG_ZERO);
            if (ValidateUtils.isValid(maintainHistoryDetails)){
                return new BaseDTO(Constants.KO, "工序已经生成返工单");
            }
        }
        return new BaseDTO(Constants.OK);
    }

    /**
     * 验证后置工序是否已经录入(containerDetail == null 说明是工序验证，容器验证不需要)
     *
     * @param currWsStep      定制工序
     * @param batchWorkDetail 批量详情
     * @param containerDetail 容器详情
     * @return : net.airuima.rbase.service.procedure.batch.dto.ContainerDetailReplaceDTO 被占用的容器详情
     * <AUTHOR>
     * @date 2023/4/7
     **/
    @Override
    public ContainerDetailReplaceDTO validAfterStep(WsStep currWsStep, BatchWorkDetail batchWorkDetail, ContainerDetail containerDetail) {
        List<Long> afterStepIds = Arrays.stream(currWsStep.getAfterStepId().split(Constants.STR_COMMA)).map(Long::parseLong).collect(Collectors.toList());
        List<BatchWorkDetail> afterBatchWorkDetailList = null != batchWorkDetail.getSubWorkSheet() ? batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdInAndDeleted(batchWorkDetail.getSubWorkSheet().getId(), afterStepIds, net.airuima.constant.Constants.LONG_ZERO)
                : batchWorkDetailRepository.findByWorkSheetIdAndStepIdInAndDeleted(batchWorkDetail.getWorkSheet().getId(), afterStepIds, Constants.LONG_ZERO);
        if (ValidateUtils.isValid(afterBatchWorkDetailList) && null == containerDetail) {
            //后置工序生产数据未删除
            return new ContainerDetailReplaceDTO(Constants.KO, "存在后置工序未删除");
        }
        //验证防止容器回退时，跳工序回退
        if (ValidateUtils.isValid(afterBatchWorkDetailList) && null != containerDetail) {
            if (containerDetail.getStatus() == ConstantsEnum.UNBIND.getCategoryName()) {
                return new ContainerDetailReplaceDTO(Constants.KO, "容器已解绑，不可删除");
            }
            //合格数不等于待流转数，说明，容器部分下交不能回退
            if (containerDetail.getTransferNumber() != containerDetail.getQualifiedNumber()) {
                return new ContainerDetailReplaceDTO(Constants.KO, "容器已解绑，不可删除");
            }
            if (afterBatchWorkDetailList.stream().anyMatch(afterBatchWorkDetail -> afterBatchWorkDetail.getFinish() == net.airuima.constant.Constants.INT_ONE)) {
                //后置工序工序已完成不能回退当前容器
                return new ContainerDetailReplaceDTO(net.airuima.constant.Constants.KO, "存在后置工序未删除");
            }
        }
        return null;
    }

    /**
     * 通过 容器详情id 或者工序批量工作详情id 验证是否存在上一道容器被占用
     *
     * @param id 容器详情id 或者工序批量工作详情id
     * @return ContainerDetailReplaceDTO
     * <AUTHOR>
     * @date 2022/2/18
     */
    public ContainerDetailReplaceDTO checkContainerOccupation(Long id) {
        BatchWorkDetail batchWorkDetail = batchWorkDetailRepository.findByIdAndDeleted(id,Constants.LONG_ZERO);

        if (null == batchWorkDetail) {
            //容器回退
            ContainerDetail containerDetail = containerDetailRepository.findByIdAndDeleted(id,Constants.LONG_ZERO);
            if (null == containerDetail) {
                return new ContainerDetailReplaceDTO(Constants.KO, "IdIsNot");
            }
            return validateContainer(containerDetail.getBatchWorkDetail(), containerDetail);
        } else {
            //工序回退
            return validateContainer(batchWorkDetail, null);
        }
    }

    /**
     * 回退员工产能统计表数据
     *
     * @param staffPerforms 员工产量统计列表
     * @return void
     * <AUTHOR>
     * @date 2022/12/5
     */
    public void staffPerformDeleteByIds(List<StaffPerform> staffPerforms) {
        if (ValidateUtils.isValid(staffPerforms)) {
            staffPerforms.forEach(staffPerform -> {
                //删除产能不良详情
                staffPerformUnqualifiedItemRepository.deleteByStaffPerformId(staffPerform.getId(), Constants.LONG_ZERO);
                staffPerformRepository.logicDelete(staffPerform);
            });
        }
    }

    /**
     * 当前回退的是最后一个工序时，更新工单和子工单合格数与不合格数
     *
     * @param batchWorkDetail 批量工序生产详情
     * @return : void
     * <AUTHOR>
     * @date 2022/10/14
     **/
    public void updateWorkSheetAndSubWorkSheet(BatchWorkDetail batchWorkDetail) {
        //优先通过子工单+工序查询
        WsStep wsStepBySubWorkSheet = null != batchWorkDetail.getSubWorkSheet() ? wsStepRepository.findBySubWorkSheetIdAndStepIdAndDeleted(batchWorkDetail.getSubWorkSheet().getId(), batchWorkDetail.getStep().getId(), Constants.LONG_ZERO).orElse(null) : null;
        if (ObjectUtils.isEmpty(wsStepBySubWorkSheet)) {
            //通过工单+工序查询
            wsStepBySubWorkSheet = wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(null != batchWorkDetail.getSubWorkSheet() ? batchWorkDetail.getSubWorkSheet().getWorkSheet().getId() : batchWorkDetail.getWorkSheet().getId(), batchWorkDetail.getStep().getId(), Constants.LONG_ZERO).orElse(null);
        }
        //若为最终工序
        if (!ObjectUtils.isEmpty(wsStepBySubWorkSheet) && StringUtils.isBlank(wsStepBySubWorkSheet.getAfterStepId())) {
            //更新总工单合格数与不合格数
            SubWorkSheet subWorkSheet = null != batchWorkDetail.getSubWorkSheet() ? batchWorkDetail.getSubWorkSheet() : null;
            WorkSheet workSheet = null != subWorkSheet ? subWorkSheet.getWorkSheet() : batchWorkDetail.getWorkSheet();
            workSheet.setQualifiedNumber(workSheet.getQualifiedNumber() - batchWorkDetail.getQualifiedNumber());
            workSheet.setUnqualifiedNumber(workSheet.getUnqualifiedNumber() - batchWorkDetail.getUnqualifiedNumber());
            workSheetRepository.save(workSheet);
            //更新子工单合格数与不合格数为
            if (null != subWorkSheet) {
                subWorkSheet.setQualifiedNumber(subWorkSheet.getQualifiedNumber() - batchWorkDetail.getQualifiedNumber());
                subWorkSheet.setUnqualifiedNumber(subWorkSheet.getUnqualifiedNumber() - batchWorkDetail.getUnqualifiedNumber());
                subWorkSheetRepository.save(subWorkSheet);
                //更新工单的子工单个数以及完成个数(包括正常、异常)
                subWorkSheetService.updateWorkSheetSubWsNumberInfo(workSheet);
            }
            //更新生产计划表和工单统计表 回退
            updateProductionPlanAndWorkSheetStatistics(batchWorkDetail, workSheet);
        }
    }

    /**
     * 更新生产计划表和工单统计表 回退
     *
     * @param batchWorkDetail 批量工序生产详情
     * @param workSheet       工单
     */
    private void updateProductionPlanAndWorkSheetStatistics(BatchWorkDetail batchWorkDetail, WorkSheet workSheet) {
        // 产品谱系id
        Long pedigreeId = Optional.ofNullable(workSheet).map(w -> w.getPedigree()).map(p -> p.getId()).orElse(null);
        // 产线id
        Long workLineId = Optional.ofNullable(workSheet).map(w -> w.getWorkLine()).map(p -> p.getId()).orElse(null);
        //更新生产计划表 生产线 回退
        if (Objects.nonNull(pedigreeId) && Objects.nonNull(workLineId)) {
            productionPlanServices[0].updateWorkLineActualNumber(pedigreeId, workLineId, batchWorkDetail.getQualifiedNumber(), OperationEnum.MINUS);
        }
        // 更新工单统计表 回退
        if (Objects.nonNull(workSheet)) {
            workSheetStatisticsServices[0].updateWorkSheetNumber(workSheet.getId(), LocalDate.now(), batchWorkDetail.getQualifiedNumber(), batchWorkDetail.getUnqualifiedNumber(), OperationEnum.MINUS);
        }
    }


    /**
     * 删除SN工作详情及关联数据
     *
     * @param snWorkDetailList SN工作详情列表
     * @return void
     * <AUTHOR>
     * @date 2021-06-13
     **/
    public void deleteSnWorkDetail(WsStep currWsStep,List<SnWorkDetail> snWorkDetailList) {
        if (ValidateUtils.isValid(snWorkDetailList)) {
            snWorkDetailList.forEach(snWorkDetail -> {
                snWorkDetailService.rollbackSnWorkDetail(snWorkDetail, Boolean.TRUE, Boolean.FALSE);
                List<String> containerCodeList = Lists.newArrayList();
                if (snWorkDetail.getContainerDetail() != null) {
                    containerCodeList.add(snWorkDetail.getContainerDetail().getContainerCode());
                    String preContainerCodes = snWorkDetail.getContainerDetail().getPreContainerCodeList();
                    if (StringUtils.isNotBlank(preContainerCodes)) {
                        containerCodeList.addAll(Arrays.asList(preContainerCodes.split(Constants.STR_COMMA)));
                    }
                }
                int reworkTime = net.airuima.constant.Constants.INT_ZERO;
                Optional<SnWorkStatus> snWorkStatusOptional = snWorkStatusRepository.findBySnAndDeleted(snWorkDetail.getSn(), net.airuima.constant.Constants.LONG_ZERO);

                //取出工序 - 开不良 扣减一次 返修数查询，因为 放入时没有开不良
                if(snWorkDetail.getStep().getCategory() > Constants.INT_TWO && snWorkDetail.getStep().getCategory() % Constants.INT_TWO == Constants.INT_ONE && snWorkDetail.getResult() != Constants.INT_ONE){
                    if (snWorkStatusOptional.isPresent()){
                        reworkTime = snWorkStatusOptional.get().getReworkTime() - Constants.INT_ONE;
                    }
                }else {
                    if (snWorkStatusOptional.isPresent()){
                        reworkTime = snWorkStatusOptional.get().getReworkTime();
                    }
                }
                //删除烘烤温循老化记录
                bakeCycleBakeAgeingModelServices[0].logicDeletedBakeCycleBakeAgeingHistory(snWorkDetail.getWorkSheet(), snWorkDetail.getSubWorkSheet(), currWsStep, containerCodeList, snWorkDetail.getSn(),reworkTime);
            });
        }
    }

    /**
     * 删除容器工作详情列表，若有前置容器则需要更新前置容器详情的剩余下交数和状态
     *
     * @param containerDetailList 待删除的容器工作详情列表
     * @return void
     * <AUTHOR>
     * @date 2021-06-13
     **/
    public void deleteContainerWorkDetail(WsStep currWsStep,List<ContainerDetail> containerDetailList) {
        if (ValidateUtils.isValid(containerDetailList)) {
            containerDetailList.forEach(containerDetail -> {
                List<PreContainerDetailInfo> preContainerDetailInfoList = containerDetail.getPreContainerDetailInfoList();
                if (ValidateUtils.isValid(preContainerDetailInfoList)) {
                    //更新前置容器详情生产数据信息
                    List<ContainerDetail> preContainerDetailList = containerDetailRepository.findByIdInAndDeleted(preContainerDetailInfoList.stream().map(PreContainerDetailInfo::getPreContainerDetailId).collect(Collectors.toList()), Constants.LONG_ZERO);
                    if (ValidateUtils.isValid(preContainerDetailList)) {
                        //因为可能存在分叉类型的工序，所以以批量工序详情分组，然后每组按照从合格数进行排序
                        Map<BatchWorkDetail, List<ContainerDetail>> preContainerDetailMap = preContainerDetailList.stream().collect(Collectors.groupingBy(ContainerDetail::getBatchWorkDetail));
                        preContainerDetailMap.forEach((key, value) -> {
                            List<ContainerDetail> reverseContainerDetailList = value.stream().sorted(Comparator.comparing(ContainerDetail::getQualifiedNumber)).collect(Collectors.toList());
                            List<SnWorkDetail> snWorkDetailList = snWorkDetailRepository.findByContainerDetailIdAndDeleted(containerDetail.getId(), Constants.LONG_ZERO);
                            iRollbackStepServices[0].reverseContainerDetail(containerDetail, reverseContainerDetailList, snWorkDetailList);
                        });
                    }
                }
                //批量逻辑删除容器详情批次信息
                containerDetailMaterialBatchRepository.batchDeleteByContainerDetailId(containerDetail.getId());
                //批量逻辑删除容器详情设备信息
                containerDetailFacilityRepository.batchDeleteByContainerDetailId(containerDetail.getId());
                //批量逻辑删除容器详情不良信息
                containerDetailUnqualifiedItemRepository.batchDeleteByContainerDetailId(containerDetail.getId());
                //批量逻辑删除容器详情易损件信息
                containerDetailWearingPartRepository.batchDeleteByBatchWorkDetailId(containerDetail.getId());
                //删除容器对应的烘烤温循老化记录
                List<String> containerCodeList = Lists.newArrayList();
                containerCodeList.add(containerDetail.getContainerCode());
                String preContainerCodes = containerDetail.getPreContainerCodeList();
                if (StringUtils.isNotBlank(preContainerCodes)) {
                    containerCodeList.addAll(Arrays.asList(preContainerCodes.split(Constants.STR_COMMA)));
                }
                bakeCycleBakeAgeingModelServices[0].logicDeletedBakeCycleBakeAgeingHistory(containerDetail.getBatchWorkDetail().getWorkSheet(), containerDetail.getBatchWorkDetail().getSubWorkSheet(),currWsStep, containerCodeList, null,Constants.INT_ZERO);
            });
        }
    }

    /**
     * 回退前置工序容器详情的流转数量
     *
     * @param deleteContainerDetail      当前待删除的容器详情
     * @param reverseContainerDetailList 需要回退的前置工序容器详情
     * @param deletedSnWorkDetailList    待删除的容器详情对应的SN工作详情列表
     * @return void
     * <AUTHOR>
     * @date 2021-08-27
     **/
    public void reverseContainerDetail(ContainerDetail deleteContainerDetail, List<ContainerDetail> reverseContainerDetailList, List<SnWorkDetail> deletedSnWorkDetailList) {
        int inputNumber = deleteContainerDetail.getInputNumber();
        for (ContainerDetail reverseContainerDetail : reverseContainerDetailList) {
            List<SnWorkDetail> reverseSnWorkDetailList = snWorkDetailRepository.findByContainerDetailIdAndDeleted(reverseContainerDetail.getId(), Constants.LONG_ZERO);
            //如果当前待删除容器详情和前置工序容器详情都存在工序则按照SN交叉比对进行回退数据，反之则按照从小到大依次回退数据
            if (ValidateUtils.isValid(deletedSnWorkDetailList) && ValidateUtils.isValid(reverseSnWorkDetailList)) {
                long reverseNumber = deletedSnWorkDetailList.stream().map(SnWorkDetail::getSn).collect(Collectors.toList()).stream().filter(sn -> reverseSnWorkDetailList.stream().map(SnWorkDetail::getSn).collect(Collectors.toList()).stream().anyMatch(sn::equals)).count();
                reverseContainerDetail.setTransferNumber(reverseContainerDetail.getTransferNumber() + (int) reverseNumber);
                inputNumber = inputNumber - (int) reverseNumber;
            } else if (inputNumber > Constants.INT_ZERO) {
                int reverseNumber = deleteContainerDetail.getPreContainerDetailInfoList().stream().filter(preContainerDetail -> preContainerDetail.getPreContainerDetailId().equals(reverseContainerDetail.getId())).findFirst().map(PreContainerDetailInfo::getNumber).orElse(Constants.INT_ZERO);
                reverseContainerDetail.setTransferNumber(reverseContainerDetail.getTransferNumber() + reverseNumber);
                //回退后置容器流转列表
                String[] afterContainerCodeList = reverseContainerDetail.getAfterContainerCodeList() != null ? reverseContainerDetail.getAfterContainerCodeList().split(Constants.STR_SEMICOLON) : null;
                if (ValidateUtils.isValid(afterContainerCodeList)) {
                    List<String> afterContainerCodes = Arrays.stream(afterContainerCodeList).filter(afterContainerCode -> !afterContainerCode.equals(deleteContainerDetail.getContainerCode())).collect(Collectors.toList());
                    reverseContainerDetail.setAfterContainerCodeList(StringUtils.join(afterContainerCodes, Constants.STR_SEMICOLON));
                }
                inputNumber = inputNumber > reverseNumber ? inputNumber - reverseNumber : Constants.INT_ZERO;
            }
            reverseContainerDetail.setStatus(ConstantsEnum.BINDING.getCategoryName());
            reverseContainerDetail.setUnbindTime(null);
            containerDetailRepository.save(reverseContainerDetail);
        }
    }

    /**
     * 逻辑删除批量工作详情及关联信息
     *
     * @param batchWorkDetail 批量工作详情
     * @return void
     * <AUTHOR>
     * @date 2021-06-13
     **/
    public void deleteBatchWorkDetail(BatchWorkDetail batchWorkDetail) {
        //批量逻辑删除物料批次信息
        batchWorkDetailMaterialBatchRepository.batchDeleteByBatchWorkDetailId(batchWorkDetail.getId());
        //批量逻辑删除易损件信息
        batchWorkDetailWearingPartRepository.batchDeleteByBatchWorkDetailId(batchWorkDetail.getId());
        //批量删除设备信息
        batchWorkDetailFacilityRepository.batchDeleteByBatchWorkDetailId(batchWorkDetail.getId());
        //批量删除工单工序不良汇总信息
        if (null != batchWorkDetail.getSubWorkSheet()) {
            wsStepUnqualifiedItemRepository.batchDeleteBySubWorkSheetIdAndsAndStepId(batchWorkDetail.getSubWorkSheet().getId(), batchWorkDetail.getStep().getId());
        } else {
            wsStepUnqualifiedItemRepository.batchDeleteByWorkSheetIdAndsAndStepId(batchWorkDetail.getWorkSheet().getId(), batchWorkDetail.getStep().getId());
        }
        batchWorkDetail.setDeleted(batchWorkDetail.getId());
        batchWorkDetailRepository.save(batchWorkDetail);
    }

    /**
     * 保存退料批次信息
     *
     * @param materialBatchDto 退料信息
     * @return void
     * <AUTHOR>
     * @date 2022/4/17
     */
    public void saveMaterialBatch(MaterialBatchDTO materialBatchDto) {
        WorkSheet workSheet = workSheetRepository.findByIdAndDeleted(materialBatchDto.getWsId(), Constants.LONG_ZERO).orElse(null);
        if (workSheet.getCategory() == WsEnum.ONLINE_RE_WS.getCategory()) {
            WsRework wsRework = wsReworkRepository.findByReworkWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO).orElse(null);
            if (null != wsRework) {
                workSheet = wsRework.getOriginalWorkSheet();
            }
        }
        //工单退料
        if (ConstantsEnum.MATERIAL_WORKSHEET_CONTROL_LEVEL.getCategoryName() == materialBatchDto.getType()) {
            Optional<WsMaterialBatch> wsMaterialBatchOptional = wsMaterialBatchRepository.findByWorkSheetIdAndMaterialIdAndBatchAndDeleted(workSheet.getId(), materialBatchDto.getMaterialId(), materialBatchDto.getBatch(), Constants.LONG_ZERO);
            //当核批次时，没有批次号，在上料时会随便输入一个批次，此时就需要用工单+物料+批次为null去当前工单物料批次信息
            if (!wsMaterialBatchOptional.isPresent()) {
                wsMaterialBatchRepository.findByWorkSheetIdAndMaterialIdAndBatchAndDeleted(workSheet.getId(), materialBatchDto.getMaterialId(), null, Constants.LONG_ZERO);
            }
            if (wsMaterialBatchOptional.isPresent()) {
                WsMaterialBatch wsMaterialBatch = wsMaterialBatchOptional.get();
                wsMaterialBatch.setLeftNumber(NumberUtils.add(wsMaterialBatch.getLeftNumber(), materialBatchDto.getNumber()).doubleValue());
                wsMaterialBatchRepository.save(wsMaterialBatch);
            }
        }
        //工单工位退料
        if (ConstantsEnum.MATERIAL_WORK_CELL_CONTROL_LEVEL.getCategoryName() == materialBatchDto.getType()) {
            Optional<WsWorkCellMaterialBatch> wsWorkCellMaterialBatchOptional = wsWorkCellMaterialBatchRepository.findByWorkSheetIdAndWorkCellIdAndMaterialIdAndBatchAndDeleted(workSheet.getId(), materialBatchDto.getWorkCellId(), materialBatchDto.getMaterialId(), materialBatchDto.getBatch(), Constants.LONG_ZERO);
            if (wsWorkCellMaterialBatchOptional.isPresent()) {
                WsWorkCellMaterialBatch wsWorkCellMaterialBatch = wsWorkCellMaterialBatchOptional.get();
                wsWorkCellMaterialBatch.setLeftNumber(NumberUtils.add(wsWorkCellMaterialBatch.getLeftNumber(), materialBatchDto.getNumber()).doubleValue());
                wsWorkCellMaterialBatchRepository.save(wsWorkCellMaterialBatch);
            }
        }
    }

    /**
     * sn详情回退 需要扣减纯单只直接累加在（子）工单上面的不合格数
     *
     * @param snWorkDetail        sn详情
     * @param snWorkStatus        sn状态
     * @param snUnqualifiedItem   sn不良项
     * @param deleteByBatchDetail 是否为删除容器详情或者批量详情处删除SN详情
     * @return void
     * <AUTHOR>
     * @date 2022/11/17
     */
    public void updateSnWorkDetailByWs(SnWorkDetail snWorkDetail, SnWorkStatus snWorkStatus, SnUnqualifiedItem snUnqualifiedItem, boolean deleteByBatchDetail, boolean singleSnOnlineRepair) {
        boolean isRworkerWeb = FuncKeyUtil.checkApi(FuncKeyConstants.RWORKER_WEB);
        if (deleteByBatchDetail && !isRworkerWeb) {
            return;
        }
        SubWorkSheet subWorkSheet = snWorkDetail.getSubWorkSheet();
        WorkSheet workSheet = null != subWorkSheet ? subWorkSheet.getWorkSheet() : snWorkDetail.getWorkSheet();
        processSnWorkInfo(snWorkDetail, snWorkStatus, snUnqualifiedItem, subWorkSheet, workSheet, singleSnOnlineRepair);

    }

    /**
     * 纯单支模式
     *
     * @param snWorkDetail      sn详情
     * @param snWorkStatus      sn状态
     * @param snUnqualifiedItem sn不良项
     * @param subWorkSheet      子工单
     * @param workSheet         工单
     */
    private void processSnWorkInfo(SnWorkDetail snWorkDetail, SnWorkStatus snWorkStatus, SnUnqualifiedItem snUnqualifiedItem, SubWorkSheet subWorkSheet, WorkSheet workSheet, boolean singleSnOnlineRepair) {
        //sn详情不合格，以及存在 不良记录 sn状态为报废 或者 为 维修分析 需要将不合格扣减一
        if (snWorkDetail.getResult() == Constants.INT_ZERO && !singleSnOnlineRepair) {
            if (null != subWorkSheet) {
                subWorkSheet.setUnqualifiedNumber(subWorkSheet.getUnqualifiedNumber() - Constants.INT_ONE).setActualEndDate(null).setStatus(Constants.PRODUCING);
            }
            workSheet.setUnqualifiedNumber(workSheet.getUnqualifiedNumber() - Constants.INT_ONE).setActualEndDate(null).setStatus(Constants.PRODUCING);
        }
        //sn详情合格，sn状态为合格 合格数扣减一
        if (snWorkStatus.getStatus() == SnWorkStatusEnum.QUALIFIED.getStatus()) {
            if (null != subWorkSheet && !singleSnOnlineRepair) {
                subWorkSheet.setQualifiedNumber(subWorkSheet.getQualifiedNumber() - Constants.INT_ONE).setActualEndDate(null).setStatus(Constants.PRODUCING);
            }
            workSheet.setQualifiedNumber(workSheet.getQualifiedNumber() - Constants.INT_ONE).setActualEndDate(null).setStatus(Constants.PRODUCING);
            //如果是返工合格则需要扣除返工合格数
            if (singleSnOnlineRepair) {
                workSheet.setReworkQualifiedNumber(workSheet.getReworkQualifiedNumber() - Constants.INT_ONE);
                workSheet.setUnqualifiedNumber(workSheet.getUnqualifiedNumber()+Constants.INT_ONE);
            }
        }
        if (null != subWorkSheet) {
            subWorkSheetRepository.save(subWorkSheet);
        }
        workSheetRepository.save(workSheet);
    }

}
