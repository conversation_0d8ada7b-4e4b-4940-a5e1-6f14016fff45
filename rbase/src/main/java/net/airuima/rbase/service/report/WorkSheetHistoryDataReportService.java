package net.airuima.rbase.service.report;

import com.google.common.collect.Lists;
import jakarta.servlet.http.HttpServletResponse;
import jodd.util.CollectionUtil;
import net.airuima.constant.Constants;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.base.scene.WorkCellStaff;
import net.airuima.rbase.domain.base.wearingpart.WearingPart;
import net.airuima.rbase.domain.procedure.aps.SaleOrder;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.rbase.domain.procedure.batch.*;
import net.airuima.rbase.domain.procedure.material.WsCheckMaterialDetail;
import net.airuima.rbase.domain.procedure.material.WsMaterialReturn;
import net.airuima.rbase.domain.procedure.quality.IqcCheckHistory;
import net.airuima.rbase.domain.procedure.quality.UnqualifiedEvent;
import net.airuima.rbase.domain.procedure.report.StaffPerform;
import net.airuima.rbase.domain.procedure.single.SnRework;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.domain.procedure.wearingpart.BatchWorkDetailWearingPart;
import net.airuima.rbase.domain.procedure.wearingpart.ContainerDetailWearingPart;
import net.airuima.rbase.domain.procedure.wearingpart.WearingPartResetHistory;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.control.EventRecordDTO;
import net.airuima.rbase.dto.maintain.MaintainHistoryDetailDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.rbase.dto.rfms.FacilityDTO;
import net.airuima.rbase.dto.rfms.FacilityLedgerDTO;
import net.airuima.rbase.dto.skill.StaffSkillDTO;
import net.airuima.rbase.proxy.bom.RbaseMaterialProxy;
import net.airuima.rbase.proxy.event.RbaseEventProxy;
import net.airuima.rbase.proxy.maintain.RbaseMaintainHistoryDetailProxy;
import net.airuima.rbase.proxy.organization.RbaseStaffProxy;
import net.airuima.rbase.proxy.rfms.RbaseFacilityLedgerProxy;
import net.airuima.rbase.proxy.rfms.RbaseFacilityProxy;
import net.airuima.rbase.proxy.skill.RbaseSkillProxy;
import net.airuima.rbase.repository.base.scene.WorkCellStaffRepository;
import net.airuima.rbase.repository.base.wearingpart.WearingPartRepository;
import net.airuima.rbase.repository.procedure.aps.SaleOrderRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WsReworkRepository;
import net.airuima.rbase.repository.procedure.batch.*;
import net.airuima.rbase.repository.procedure.material.WsCheckMaterialDetailRepository;
import net.airuima.rbase.repository.procedure.material.WsMaterialReturnRepository;
import net.airuima.rbase.repository.procedure.quality.IqcCheckHistoryRepository;
import net.airuima.rbase.repository.procedure.quality.UnqualifiedEventRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformRepository;
import net.airuima.rbase.repository.procedure.single.SnReworkRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkStatusRepository;
import net.airuima.rbase.repository.procedure.wearingpart.BatchWorkDetailWearingPartRepository;
import net.airuima.rbase.repository.procedure.wearingpart.ContainerDetailWearingPartRepository;
import net.airuima.rbase.repository.procedure.wearingpart.WearingPartResetHistoryRepository;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rbase.web.rest.report.dto.worksheethistorydata.*;
import net.airuima.rbase.web.rest.report.dto.worksheethistorydata.enums.OrderTypeEnum;
import net.airuima.util.ResponseException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;

/**
 * 工单履历报表 service
 *
 * <AUTHOR>
 * @version 1.8.0
 * @since 1.8.0
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkSheetHistoryDataReportService {

    @Autowired
    private SaleOrderRepository saleOrderRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private WsReworkRepository wsReworkRepository;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private SnWorkDetailRepository snWorkDetailRepository;
    @Autowired
    private BatchWorkDetailMaterialBatchRepository batchWorkDetailMaterialBatchRepository;
    @Autowired
    private RbaseMaterialProxy rbaseMaterialProxy;
    @Autowired
    private ContainerDetailMaterialBatchRepository containerDetailMaterialBatchRepository;
    @Autowired
    private RbaseFacilityProxy rbaseFacilityProxy;
    @Autowired
    private BatchWorkDetailFacilityRepository batchWorkDetailFacilityRepository;
    @Autowired
    private ContainerDetailFacilityRepository containerDetailFacilityRepository;
    @Autowired
    private BatchWorkDetailWearingPartRepository batchWorkDetailWearingPartRepository;
    @Autowired
    private ContainerDetailWearingPartRepository containerDetailWearingPartRepository;
    @Autowired
    private RbaseStaffProxy rbaseStaffProxy;
    @Autowired
    private StaffPerformRepository staffPerformRepository;
    @Autowired
    private UnqualifiedEventRepository unqualifiedEventRepository;
    @Autowired
    private RbaseEventProxy rbaseEventProxy;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private WsStepUnqualifiedItemRepository wsStepUnqualifiedItemRepository;
    @Autowired
    private RollBackHistoryRepository rollBackHistoryRepository;
    @Autowired
    private ContainerRepository containerRepository;
    @Autowired
    private RbaseMaintainHistoryDetailProxy rbaseMaintainHistoryDetailProxy;
    @Autowired
    private SnWorkStatusRepository snWorkStatusRepository;
    @Autowired
    private SnReworkRepository snReworkRepository;
    @Autowired
    private IqcCheckHistoryRepository iqcCheckHistoryRepository;
    @Autowired
    private WsCheckMaterialDetailRepository wsCheckMaterialDetailRepository;
    @Autowired
    private WsMaterialReturnRepository wsMaterialReturnRepository;
    @Autowired
    private RbaseFacilityLedgerProxy rbaseFacilityLedgerProxy;
    @Autowired
    private RbaseSkillProxy rbaseSkillProxy;
    @Autowired
    private WorkCellStaffRepository workCellStaffRepository;
    @Autowired
    private WearingPartRepository wearingPartRepository;
    @Autowired
    private WearingPartResetHistoryRepository wearingPartResetHistoryRepository;
    @Autowired
    private CommonService commonService;

    /**
     * 获取订单列表
     *
     * @param orderListRequestDto 订单列表请求DTO
     * <AUTHOR>
     * @since 1.8.0
     */
    public OrderListDTO getOrderList(OrderListRequestDTO orderListRequestDto) {
        OrderTypeEnum orderTypeEnum = OrderTypeEnum.fo(orderListRequestDto.getType());
        return switch (orderTypeEnum) {
            case SALES_ORDER -> handleSalesOrder(orderListRequestDto.getCode());
            case WORK_SHEET -> handleWorkSheet(orderListRequestDto.getCode(), OrderTypeEnum.WORK_SHEET.getCode());
            case SUB_WORK_SHEET -> handleSubWorkSheet(orderListRequestDto.getCode());
            case CONTAINER -> handleContainer(orderListRequestDto);
            case SN -> handleSn(orderListRequestDto);
            case MATERIAL -> handleMaterial(orderListRequestDto);
            case EQUIPMENT -> handleEquipment(orderListRequestDto);
            case CONSUMABLE -> handleConsumable(orderListRequestDto);
            case STAFF -> handleStaff(orderListRequestDto);
            default -> throw new IllegalArgumentException("不支持的审批类型: " + orderTypeEnum);
        };
    }

    /**
     * 获取销售订单信息
     */
    public OrderListDTO handleSalesOrder(String code) {
        Optional<SaleOrder> saleOrderOptional = saleOrderRepository.findBySerialNumberAndDeleted(code, Constants.LONG_ZERO);
        if (saleOrderOptional.isEmpty()) {
            return null;
        }
        SaleOrder saleOrder = saleOrderOptional.get();
        OrderListDTO orderListDTO = new OrderListDTO();
        orderListDTO.setCode(saleOrder.getSerialNumber())
                .setType(OrderTypeEnum.SALES_ORDER.getCode());
        List<WorkSheet> workSheetList = workSheetRepository.findBySaleOrderIdAndDeleted(saleOrder.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(workSheetList)) {
            orderListDTO.setOrderListDTOList(workSheetList.stream().map(workSheet -> handleWorkSheet(workSheet.getSerialNumber(), OrderTypeEnum.WORK_SHEET.getCode())).toList());
        }
        return orderListDTO;
    }

    /**
     * 获取工单信息
     */
    public OrderListDTO handleWorkSheet(String code, int type) {
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findBySerialNumberAndDeleted(code, Constants.LONG_ZERO);
        if (workSheetOptional.isEmpty()) {
            return null;
        }
        WorkSheet workSheet = workSheetOptional.get();
        OrderListDTO orderListDTO = new OrderListDTO();
        orderListDTO.setCode(code)
                .setType(type)
                .setOrderListDTOList(Lists.newArrayList());
        // 获取返工单信息
        List<WsRework> wsReworkList = wsReworkRepository.findByOriginalWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(wsReworkList)) {
            orderListDTO.getOrderListDTOList().addAll(wsReworkList.stream().map(wsRework -> handleWorkSheet(wsRework.getReworkWorkSheet().getSerialNumber(), OrderTypeEnum.WS_REWORK.getCode())).toList());
        }

        // 获取子工单信息
        List<SubWorkSheet> subWorkSheetList = subWorkSheetRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(subWorkSheetList)) {
            orderListDTO.getOrderListDTOList().addAll(subWorkSheetList.stream().map(subWorkSheet -> handleSubWorkSheet(subWorkSheet.getSerialNumber())).toList());
        }
        return orderListDTO;
    }

    /**
     * 获取子工单信息
     */
    public OrderListDTO handleSubWorkSheet(String code) {
        Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findBySerialNumberAndDeleted(code, Constants.LONG_ZERO);
        if (subWorkSheetOptional.isEmpty()) {
            return null;
        }
        OrderListDTO orderListDTO = new OrderListDTO();
        orderListDTO.setCode(code).setType(OrderTypeEnum.SUB_WORK_SHEET.getCode());
        return orderListDTO;
    }

    /**
     * 获取容器信息
     */
    private OrderListDTO handleContainer(OrderListRequestDTO orderListRequestDto) {
        List<ContainerDetail> containerDetailList = containerDetailRepository.findByContainerCodeAndRecordDate(orderListRequestDto.getCode(), orderListRequestDto.getStartTime(), orderListRequestDto.getEndTime());
        if (ValidateUtils.isValid(containerDetailList)) {
            return null;
        }
        OrderListDTO orderListDTO = new OrderListDTO();
        orderListDTO.setCode(orderListRequestDto.getCode())
                .setType(OrderTypeEnum.CONTAINER.getCode())
                .setOrderListDTOList(Lists.newArrayList());
        List<String> workSheetList = containerDetailList.stream().map(containerDetail -> containerDetail.getBatchWorkDetail().getWorkSheet().getSerialNumber()).distinct().toList();
        orderListDTO.getOrderListDTOList().addAll(workSheetList.stream().map(s -> handleWorkSheet(s, OrderTypeEnum.WORK_SHEET.getCode())).toList());
        return orderListDTO;
    }

    /**
     * 获取SN信息
     */
    private OrderListDTO handleSn(OrderListRequestDTO orderListRequestDto) {
        List<SnWorkDetail> snWorkDetailList = snWorkDetailRepository.findBySnAndStartDateAndEndDate(orderListRequestDto.getCode(), orderListRequestDto.getStartTime(), orderListRequestDto.getEndTime());
        if (ValidateUtils.isValid(snWorkDetailList)) {
            return null;
        }
        OrderListDTO orderListDTO = new OrderListDTO();
        orderListDTO.setCode(orderListRequestDto.getCode())
                .setType(OrderTypeEnum.SN.getCode())
                .setOrderListDTOList(Lists.newArrayList());
        List<String> workSheetList = snWorkDetailList.stream().map(snWorkDetail -> snWorkDetail.getWorkSheet().getSerialNumber()).distinct().toList();
        orderListDTO.getOrderListDTOList().addAll(workSheetList.stream().map(s -> handleWorkSheet(s, OrderTypeEnum.WORK_SHEET.getCode())).toList());
        return orderListDTO;
    }

    /**
     * 获取物料信息
     */
    private OrderListDTO handleMaterial(OrderListRequestDTO orderListRequestDto) {
        Optional<MaterialDTO> materialOptional = rbaseMaterialProxy.findByCodeAndDeleted(orderListRequestDto.getCode(), Constants.LONG_ZERO);
        if (materialOptional.isEmpty()) {
            return null;
        }
        MaterialDTO material = materialOptional.get();
        // 批量
        List<BatchWorkDetailMaterialBatch> batchWorkDetailMaterialBatchList = batchWorkDetailMaterialBatchRepository.findByMaterialIdAndStartDateAndEndDate(material.getId(), orderListRequestDto.getStartTime(), orderListRequestDto.getEndTime());
        // 容器
        List<ContainerDetailMaterialBatch> containerDetailMaterialBatchList = containerDetailMaterialBatchRepository.findByMaterialIdAndStartDateAndEndDate(material.getId(), orderListRequestDto.getStartTime(), orderListRequestDto.getEndTime());
        OrderListDTO orderListDTO = new OrderListDTO();
        orderListDTO.setCode(orderListRequestDto.getCode())
                .setType(OrderTypeEnum.MATERIAL.getCode())
                .setOrderListDTOList(Lists.newArrayList());
        List<String> workSheetList = new ArrayList<>();
        if (ValidateUtils.isValid(batchWorkDetailMaterialBatchList)) {
            if (StringUtils.isNotBlank(orderListRequestDto.getBatch())) {
                workSheetList.addAll(batchWorkDetailMaterialBatchList.stream()
                        .filter(batchWorkDetailMaterialBatch -> Objects.equals(batchWorkDetailMaterialBatch.getMaterialBatch(), orderListRequestDto.getBatch()))
                        .map(batchWorkDetailMaterialBatch -> batchWorkDetailMaterialBatch.getBatchWorkDetail().getWorkSheet().getSerialNumber())
                        .distinct().toList());
            } else {
                workSheetList.addAll(batchWorkDetailMaterialBatchList.stream().map(batchWorkDetailMaterialBatch -> batchWorkDetailMaterialBatch.getBatchWorkDetail().getWorkSheet().getSerialNumber()).distinct().toList());
            }
        }
        if (ValidateUtils.isValid(containerDetailMaterialBatchList)) {
            if (StringUtils.isNotBlank(orderListRequestDto.getBatch())) {
                workSheetList.addAll(containerDetailMaterialBatchList.stream()
                        .filter(containerDetailMaterialBatch -> Objects.equals(containerDetailMaterialBatch.getBatch(), orderListRequestDto.getBatch()))
                        .map(containerDetailMaterialBatch -> containerDetailMaterialBatch.getContainerDetail().getBatchWorkDetail().getWorkSheet().getSerialNumber())
                        .distinct().toList());
            } else {
                workSheetList.addAll(containerDetailMaterialBatchList.stream().map(containerDetailMaterialBatch -> containerDetailMaterialBatch.getContainerDetail().getBatchWorkDetail().getWorkSheet().getSerialNumber()).distinct().toList());
            }
        }
        if (ValidateUtils.isValid(workSheetList)) {
            workSheetList = workSheetList.stream().distinct().toList();
            orderListDTO.getOrderListDTOList().addAll(workSheetList.stream().map(s -> handleWorkSheet(s, OrderTypeEnum.WORK_SHEET.getCode())).toList());
        }
        return orderListDTO;
    }

    /**
     * 获取设备信息
     */
    private OrderListDTO handleEquipment(OrderListRequestDTO orderListRequestDto) {
        FacilityDTO facilityDTO = rbaseFacilityProxy.findByCodeAndDeleted(orderListRequestDto.getCode(), Constants.LONG_ZERO);
        if (facilityDTO == null) {
            return null;
        }
        // 批次
        List<BatchWorkDetailFacility> batchWorkDetailFacilityList = batchWorkDetailFacilityRepository.findByFacilityIdAndStartDateAndEndDate(facilityDTO.getId(), orderListRequestDto.getStartTime(), orderListRequestDto.getEndTime());
        // 容器
        List<ContainerDetailFacility> containerDetailFacilityList = containerDetailFacilityRepository.findByFacilityIdAndStartDateAndEndDate(facilityDTO.getId(), orderListRequestDto.getStartTime(), orderListRequestDto.getEndTime());
        OrderListDTO orderListDTO = new OrderListDTO();
        orderListDTO.setCode(orderListRequestDto.getCode())
                .setType(OrderTypeEnum.EQUIPMENT.getCode())
                .setOrderListDTOList(Lists.newArrayList());
        List<String> workSheetList = new ArrayList<>();
        if (ValidateUtils.isValid(batchWorkDetailFacilityList)) {
            workSheetList.addAll(batchWorkDetailFacilityList.stream().map(batchWorkDetailFacility -> batchWorkDetailFacility.getBatchWorkDetail().getWorkSheet().getSerialNumber()).distinct().toList());
        }
        if (ValidateUtils.isValid(containerDetailFacilityList)) {
            workSheetList.addAll(containerDetailFacilityList.stream().map(batchWorkDetailFacility -> batchWorkDetailFacility.getContainerDetail().getBatchWorkDetail().getWorkSheet().getSerialNumber()).distinct().toList());
        }
        if (ValidateUtils.isValid(workSheetList)) {
            workSheetList = workSheetList.stream().distinct().toList();
            orderListDTO.getOrderListDTOList().addAll(workSheetList.stream().map(s -> handleWorkSheet(s, OrderTypeEnum.WORK_SHEET.getCode())).toList());
        }
        return orderListDTO;
    }

    /**
     * 获取易损件信息
     */
    private OrderListDTO handleConsumable(OrderListRequestDTO orderListRequestDto) {
        OrderListDTO orderListDTO = new OrderListDTO();
        orderListDTO.setCode(orderListRequestDto.getCode())
                .setType(OrderTypeEnum.CONSUMABLE.getCode())
                .setOrderListDTOList(Lists.newArrayList());
        List<BatchWorkDetailWearingPart> batchWorkDetailWearingPartList = batchWorkDetailWearingPartRepository.findByWearingPartCodeAndStartDateAndEndDate(orderListRequestDto.getCode(), orderListRequestDto.getStartTime(), orderListRequestDto.getEndTime());
        List<ContainerDetailWearingPart> containerDetailWearingPartList = containerDetailWearingPartRepository.findByWearingPartCodeAndStartDateAndEndDate(orderListRequestDto.getCode(), orderListRequestDto.getStartTime(), orderListRequestDto.getEndTime());
        List<String> workSheetList = new ArrayList<>();
        if (ValidateUtils.isValid(batchWorkDetailWearingPartList)) {
            workSheetList.addAll(batchWorkDetailWearingPartList.stream().map(batchWorkDetailWearingPart -> batchWorkDetailWearingPart.getBatchWorkDetail().getWorkSheet().getSerialNumber()).distinct().toList());
        }
        if (ValidateUtils.isValid(containerDetailWearingPartList)) {
            workSheetList.addAll(containerDetailWearingPartList.stream().map(containerDetailWearingPart -> containerDetailWearingPart.getContainerDetail().getBatchWorkDetail().getWorkSheet().getSerialNumber()).distinct().toList());
        }
        if (ValidateUtils.isValid(workSheetList)) {
            workSheetList = workSheetList.stream().distinct().toList();
            orderListDTO.getOrderListDTOList().addAll(workSheetList.stream().map(s -> handleWorkSheet(s, OrderTypeEnum.WORK_SHEET.getCode())).toList());
            return orderListDTO;
        }
        return orderListDTO;
    }

    /**
     * 获取员工信息
     */
    private OrderListDTO handleStaff(OrderListRequestDTO orderListRequestDto) {
        Optional<StaffDTO> staffOptional = rbaseStaffProxy.findByCodeAndDeleted(orderListRequestDto.getCode(), Constants.LONG_ZERO);
        if (staffOptional.isEmpty()) {
            return null;
        }
        StaffDTO staff = staffOptional.get();
        OrderListDTO orderListDTO = new OrderListDTO();
        orderListDTO.setCode(orderListRequestDto.getCode())
                .setType(OrderTypeEnum.STAFF.getCode())
                .setOrderListDTOList(Lists.newArrayList());
        List<StaffPerform> staffPerformList = staffPerformRepository.findByStaffIdAndStartDateAndEndDate(staff.getId(), orderListRequestDto.getStartTime(), orderListRequestDto.getEndTime());
        if (ValidateUtils.isValid(staffPerformList)) {
            List<String> workSheetList = staffPerformList.stream().map(staffPerform -> staffPerform.getWorkSheet().getSerialNumber()).distinct().toList();
            orderListDTO.getOrderListDTOList().addAll(workSheetList.stream().map(s -> handleWorkSheet(s, OrderTypeEnum.WORK_SHEET.getCode())).toList());
        }
        return orderListDTO;
    }

    /**
     * 获取销售订单履历
     *
     * @param code 销售订单编码
     * <AUTHOR>
     * @since 1.8.0
     */
    public SaleOrderHistoryDTO getSaleOrderHistory(String code) {
        Optional<SaleOrder> saleOrderOptional = saleOrderRepository.findBySerialNumberAndDeleted(code, Constants.LONG_ZERO);
        if (saleOrderOptional.isEmpty()) {
            return null;
        }
        SaleOrder saleOrder = saleOrderOptional.get();
        SaleOrderHistoryDTO saleOrderHistoryDTO = new SaleOrderHistoryDTO();
        saleOrderHistoryDTO.setSaleOrder(saleOrder);
        List<SaleOrderHistoryDTO.HistoryDTO> historyDTOList = new ArrayList<>();
        historyDTOList.add(new SaleOrderHistoryDTO.HistoryDTO(Constants.INT_ZERO, saleOrder.getCreatedDate().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime(), null, null, null));
        // 通过销售订单获取工单信息
        List<WorkSheet> workSheetList = workSheetRepository.findBySaleOrderIdAndDeleted(saleOrder.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(workSheetList)) {
            workSheetList.forEach(workSheet -> historyDTOList.add(new SaleOrderHistoryDTO.HistoryDTO(Constants.INT_ONE, workSheet.getCreatedDate().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime(), null, workSheet, null)));
        }
        historyDTOList.sort(Comparator.comparing(SaleOrderHistoryDTO.HistoryDTO::getLocalDateTime));
        saleOrderHistoryDTO.setHistoryDTOList(historyDTOList);
        return saleOrderHistoryDTO;
    }

    /**
     * 获取工单履历
     *
     * @param code 工单编码
     * <AUTHOR>
     * @since 1.8.0
     */
    public WorkSheetHistoryDTO getWorkSheetHistory(String code) {
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findBySerialNumberAndDeleted(code, Constants.LONG_ZERO);
        if (workSheetOptional.isEmpty()) {
            return null;
        }
        WorkSheet workSheet = workSheetOptional.get();
        WorkSheetHistoryDTO workSheetHistoryDTO = new WorkSheetHistoryDTO();
        workSheetHistoryDTO.setWorkSheet(workSheet);

        //获取系统配置的投产粒度(子工单或者工单)
        String mode = commonService.getDictionaryData(net.airuima.rbase.constant.Constants.KEY_PRODUCTION_MODE);
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
        if (subWsProductionMode) {
            return this.dealHistoryDTO(workSheetHistoryDTO);
        }
        return this.dealStepHistoryDTO(workSheetHistoryDTO);
    }

    /**
     * 工单投产工单履历
     *
     * @param workSheetHistoryDTO 工单履历DTO
     */
    private WorkSheetHistoryDTO dealStepHistoryDTO(WorkSheetHistoryDTO workSheetHistoryDTO) {
        WorkSheet workSheet = workSheetHistoryDTO.getWorkSheet();
        // 获取子工单批量详情
        List<BatchWorkDetail> batchWorkDetailList = batchWorkDetailRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(batchWorkDetailList)) {
            return null;
        }
        List<WorkSheetHistoryDTO.StepHistoryDTO> stepHistoryDTOList = Lists.newArrayList();

        List<Long> batchWorkDetailIds = batchWorkDetailList.stream().map(BatchWorkDetail::getId).toList();
        List<Long> stepIds = batchWorkDetailList.stream().map(BatchWorkDetail::getStep).map(Step::getId).toList();

        // 获取工序设备
        List<BatchWorkDetailFacility> batchWorkDetailFacilityList = batchWorkDetailFacilityRepository.findByBatchWorkDetailIdInAndDeleted(batchWorkDetailIds, Constants.LONG_ZERO);
        // 获取工序物料
        List<BatchWorkDetailMaterialBatch> batchWorkDetailMaterialBatchList = batchWorkDetailMaterialBatchRepository.findByBatchWorkDetailIdInAndDeleted(batchWorkDetailIds, Constants.LONG_ZERO);
        // 获取工序不良
        List<WsStepUnqualifiedItem> wsStepUnqualifiedItemList = wsStepUnqualifiedItemRepository.findByWorkSheetIdAndStepIdInAndDeleted(workSheet.getId(), stepIds, Constants.LONG_ZERO);
        // 获取工序易损件
        List<BatchWorkDetailWearingPart> batchWorkDetailWearingPartList = batchWorkDetailWearingPartRepository.findByBatchWorkDetailIdInAndDeleted(batchWorkDetailIds, Constants.LONG_ZERO);
        // 获取回退历史
        List<RollBackHistory> rollBackHistoryList = rollBackHistoryRepository.findByWorkSheetIdAndStepIdInAndDeleted(workSheet.getId(), stepIds, Constants.LONG_ZERO);


        batchWorkDetailList.forEach(batchWorkDetail -> {
            WorkSheetHistoryDTO.StepHistoryDTO historyDTO = new WorkSheetHistoryDTO.StepHistoryDTO();
            historyDTO.setType(Constants.INT_ZERO)
                    .setBatchWorkDetail(batchWorkDetail)
                    .setLocalDateTime(batchWorkDetail.getEndDate())
                    .setStaffDTOList(Lists.newArrayList(batchWorkDetail.getOperatorDto()));
            // 获取动态数据
            if (batchWorkDetail.getStepDynamicDataColumnGetDTO() != null) {
                historyDTO.setStepDynamicDataColumnGetDTOList(Lists.newArrayList(batchWorkDetail.getStepDynamicDataColumnGetDTO()));
            }
            // 获取工序设备
            if (ValidateUtils.isValid(batchWorkDetailFacilityList)) {
                List<FacilityDTO> facilityDTOList = batchWorkDetailFacilityList.stream()
                        .filter(value -> Objects.equals(value.getBatchWorkDetail().getId(), batchWorkDetail.getId()))
                        .map(BatchWorkDetailFacility::getFacilityDto)
                        .distinct().toList();
                if (ValidateUtils.isValid(facilityDTOList)) {
                    historyDTO.setFacilityDTOList(facilityDTOList);
                }
            }
            // 获取工序物料
            if (ValidateUtils.isValid(batchWorkDetailMaterialBatchList)) {
                List<BatchWorkDetailMaterialBatch> batches = batchWorkDetailMaterialBatchList.stream()
                        .filter(value -> Objects.equals(value.getBatchWorkDetail().getId(), batchWorkDetail.getId())).toList();
                if (ValidateUtils.isValid(batches)) {
                    historyDTO.setBatchWorkDetailMaterialBatchList(batches);
                }
            }
            // 获取工序不良
            if (ValidateUtils.isValid(wsStepUnqualifiedItemList)) {
                List<UnqualifiedItem> unqualifiedItemList = wsStepUnqualifiedItemList.stream()
                        .filter(value -> Objects.equals(value.getStep().getId(), batchWorkDetail.getStep().getId()))
                        .map(WsStepUnqualifiedItem::getUnqualifiedItem)
                        .distinct().toList();
                if (ValidateUtils.isValid(unqualifiedItemList)) {
                    historyDTO.setUnqualifiedItemList(unqualifiedItemList);
                }
            }
            // 获取工序易损件
            if (ValidateUtils.isValid(batchWorkDetailWearingPartList)) {
                List<WearingPart> wearingPartList = batchWorkDetailWearingPartList.stream()
                        .filter(value -> Objects.equals(value.getBatchWorkDetail().getId(), batchWorkDetail.getId()))
                        .map(BatchWorkDetailWearingPart::getWearingPart)
                        .distinct().toList();
                if (ValidateUtils.isValid(wearingPartList)) {
                    historyDTO.setWearingPartList(wearingPartList);
                }
            }
            stepHistoryDTOList.add(historyDTO);

            // 获取回退历史
            List<RollBackHistory> backHistoryList = rollBackHistoryList.stream().filter(rollBackHistory -> Objects.equals(rollBackHistory.getStep().getId(), batchWorkDetail.getStep().getId())).toList();
            if (ValidateUtils.isValid(backHistoryList)) {
                rollBackHistoryList.forEach(rollBackHistory -> {
                    SubWorkSheetHistoryDTO.HistoryDTO rollBackhistoryDTO = new SubWorkSheetHistoryDTO.HistoryDTO();
                    rollBackhistoryDTO.setType(Constants.INT_ONE)
                            .setLocalDateTime(rollBackHistory.getCreatedDate().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime())
                            .setBatchWorkDetail(batchWorkDetail);
                    stepHistoryDTOList.add(historyDTO);
                });
            }
        });
        stepHistoryDTOList.sort(Comparator.comparing(WorkSheetHistoryDTO.StepHistoryDTO::getLocalDateTime));
        workSheetHistoryDTO.setStepHistoryDTOList(stepHistoryDTOList);
        return workSheetHistoryDTO;
    }

    /**
     * 子工单投产工单履历
     *
     * @param workSheetHistoryDTO 工单履历DTO
     */
    private WorkSheetHistoryDTO dealHistoryDTO(WorkSheetHistoryDTO workSheetHistoryDTO) {
        WorkSheet workSheet = workSheetHistoryDTO.getWorkSheet();
        List<WorkSheetHistoryDTO.HistoryDTO> historyDTOList = new ArrayList<>();
        historyDTOList.add(new WorkSheetHistoryDTO.HistoryDTO(Constants.INT_ZERO, null, null, workSheet.getCreatedDate().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime(), null));
        // 获取工单子工单数据
        List<SubWorkSheet> subWorkSheetList = subWorkSheetRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(subWorkSheetList)) {
            subWorkSheetList.forEach(subWorkSheet -> historyDTOList.add(new WorkSheetHistoryDTO.HistoryDTO(Constants.INT_ONE, null, subWorkSheet, subWorkSheet.getCreatedDate().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime(), null)));
        }
        // 获取工单暂停恢复数据
        List<UnqualifiedEvent> unqualifiedEventList = unqualifiedEventRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(unqualifiedEventList)) {
            unqualifiedEventList.forEach(unqualifiedEvent -> {
                historyDTOList.add(new WorkSheetHistoryDTO.HistoryDTO(Constants.INT_TWO, "预警停线暂停工单", null, unqualifiedEvent.getRecordTime(), null));
                if (unqualifiedEvent.getStatus() == Constants.INT_ONE) {
                    historyDTOList.add(new WorkSheetHistoryDTO.HistoryDTO(Constants.INT_THREE, "预警停线恢复工单", null, unqualifiedEvent.getDealTime(), null));
                }
            });
        }
        List<EventRecordDTO> eventRecordDTOList = rbaseEventProxy.findAllByWarningTargetCode(workSheet.getSerialNumber());
        if (ValidateUtils.isValid(eventRecordDTOList)) {
            eventRecordDTOList.stream().filter(eventRecordDTO -> eventRecordDTO.getCategory() == Constants.INT_TWO).forEach(eventRecordDTO -> {
                historyDTOList.add(new WorkSheetHistoryDTO.HistoryDTO(Constants.INT_TWO, "手动发起暂停工单", null, eventRecordDTO.getRecordTime(), null));
                if (eventRecordDTO.getLastModifiedDate() != null) {
                    historyDTOList.add(new WorkSheetHistoryDTO.HistoryDTO(Constants.INT_THREE, "手动发起恢复工单", null, eventRecordDTO.getLastModifiedDate().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime(), null));
                }
            });
        }
        historyDTOList.sort(Comparator.comparing(WorkSheetHistoryDTO.HistoryDTO::getLocalDateTime));
        workSheetHistoryDTO.setHistoryDTOList(historyDTOList);
        return workSheetHistoryDTO;
    }

    /**
     * 获取子工单履历
     *
     * @param code 子工单编码
     * <AUTHOR>
     * @since 1.8.0
     */
    public SubWorkSheetHistoryDTO getSubWorkSheetHistory(String code) {
        Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findBySerialNumberAndDeleted(code, Constants.LONG_ZERO);
        if (subWorkSheetOptional.isEmpty()) {
            return null;
        }
        SubWorkSheet subWorkSheet = subWorkSheetOptional.get();
        SubWorkSheetHistoryDTO subWorkSheetHistoryDTO = new SubWorkSheetHistoryDTO();
        subWorkSheetHistoryDTO.setSubWorkSheet(subWorkSheet)
                .setHistoryDTOList(Lists.newArrayList());
        // 获取子工单批量详情
        List<BatchWorkDetail> batchWorkDetailList = batchWorkDetailRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(batchWorkDetailList)) {
            return null;
        }
        List<SubWorkSheetHistoryDTO.HistoryDTO> historyDTOList = Lists.newArrayList();
        List<Long> batchWorkDetailIds = batchWorkDetailList.stream().map(BatchWorkDetail::getId).toList();
        List<Long> stepIds = batchWorkDetailList.stream().map(BatchWorkDetail::getStep).map(Step::getId).toList();
        // 获取工序设备
        List<BatchWorkDetailFacility> batchWorkDetailFacilityList = batchWorkDetailFacilityRepository.findByBatchWorkDetailIdInAndDeleted(batchWorkDetailIds, Constants.LONG_ZERO);
        // 获取工序物料
        List<BatchWorkDetailMaterialBatch> batchWorkDetailMaterialBatchList = batchWorkDetailMaterialBatchRepository.findByBatchWorkDetailIdInAndDeleted(batchWorkDetailIds, Constants.LONG_ZERO);
        // 获取工序不良
        List<WsStepUnqualifiedItem> wsStepUnqualifiedItemList = wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdInAndDeleted(subWorkSheet.getId(), stepIds, Constants.LONG_ZERO);
        // 获取工序易损件
        List<BatchWorkDetailWearingPart> batchWorkDetailWearingPartList = batchWorkDetailWearingPartRepository.findByBatchWorkDetailIdInAndDeleted(batchWorkDetailIds, Constants.LONG_ZERO);
        // 获取回退历史
        List<RollBackHistory> rollBackHistoryList = rollBackHistoryRepository.findBySubWorkSheetIdAndStepIdInAndDeleted(subWorkSheet.getId(), stepIds, Constants.LONG_ZERO);


        batchWorkDetailList.forEach(batchWorkDetail -> {
            SubWorkSheetHistoryDTO.HistoryDTO historyDTO = new SubWorkSheetHistoryDTO.HistoryDTO();
            historyDTO.setType(Constants.INT_ZERO)
                    .setBatchWorkDetail(batchWorkDetail)
                    .setLocalDateTime(batchWorkDetail.getEndDate())
                    .setStaffDTOList(Lists.newArrayList(batchWorkDetail.getOperatorDto()));
            // 获取动态数据
            if (batchWorkDetail.getStepDynamicDataColumnGetDTO() != null) {
                historyDTO.setStepDynamicDataColumnGetDTOList(Lists.newArrayList(batchWorkDetail.getStepDynamicDataColumnGetDTO()));
            }
            // 获取工序设备
            if (ValidateUtils.isValid(batchWorkDetailFacilityList)) {
                List<FacilityDTO> facilityDTOList = batchWorkDetailFacilityList.stream()
                        .filter(value -> Objects.equals(value.getBatchWorkDetail().getId(), batchWorkDetail.getId()))
                        .map(BatchWorkDetailFacility::getFacilityDto)
                        .distinct().toList();
                if (ValidateUtils.isValid(facilityDTOList)) {
                    historyDTO.setFacilityDTOList(facilityDTOList);
                }
            }
            // 获取工序物料
            if (ValidateUtils.isValid(batchWorkDetailMaterialBatchList)) {
                List<BatchWorkDetailMaterialBatch> batches = batchWorkDetailMaterialBatchList.stream()
                        .filter(value -> Objects.equals(value.getBatchWorkDetail().getId(), batchWorkDetail.getId())).toList();
                if (ValidateUtils.isValid(batches)) {
                    historyDTO.setBatchWorkDetailMaterialBatchList(batches);
                }
            }
            // 获取工序不良
            if (ValidateUtils.isValid(wsStepUnqualifiedItemList)) {
                List<UnqualifiedItem> unqualifiedItemList = wsStepUnqualifiedItemList.stream()
                        .filter(value -> Objects.equals(value.getStep().getId(), batchWorkDetail.getStep().getId()))
                        .map(WsStepUnqualifiedItem::getUnqualifiedItem)
                        .distinct().toList();
                if (ValidateUtils.isValid(unqualifiedItemList)) {
                    historyDTO.setUnqualifiedItemList(unqualifiedItemList);
                }
            }
            // 获取工序易损件
            if (ValidateUtils.isValid(batchWorkDetailWearingPartList)) {
                List<WearingPart> wearingPartList = batchWorkDetailWearingPartList.stream()
                        .filter(value -> Objects.equals(value.getBatchWorkDetail().getId(), batchWorkDetail.getId()))
                        .map(BatchWorkDetailWearingPart::getWearingPart)
                        .distinct().toList();
                if (ValidateUtils.isValid(wearingPartList)) {
                    historyDTO.setWearingPartList(wearingPartList);
                }
            }
            historyDTOList.add(historyDTO);

            // 获取回退历史
            List<RollBackHistory> backHistoryList = rollBackHistoryList.stream().filter(rollBackHistory -> Objects.equals(rollBackHistory.getStep().getId(), batchWorkDetail.getStep().getId())).toList();
            if (ValidateUtils.isValid(backHistoryList)) {
                rollBackHistoryList.forEach(rollBackHistory -> {
                    SubWorkSheetHistoryDTO.HistoryDTO rollBackhistoryDTO = new SubWorkSheetHistoryDTO.HistoryDTO();
                    rollBackhistoryDTO.setType(Constants.INT_ONE)
                            .setLocalDateTime(rollBackHistory.getCreatedDate().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime())
                            .setBatchWorkDetail(batchWorkDetail);
                    historyDTOList.add(historyDTO);
                });
            }
        });
        historyDTOList.sort(Comparator.comparing(SubWorkSheetHistoryDTO.HistoryDTO::getLocalDateTime));
        subWorkSheetHistoryDTO.setHistoryDTOList(historyDTOList);
        return subWorkSheetHistoryDTO;
    }

    /**
     * 获取容器履历
     *
     * @param code 容器编码
     * <AUTHOR>
     * @since 1.8.0
     */
    public ContainerHistoryDTO getContainerHistory(String code) {
        Optional<Container> containerOptional = containerRepository.findByCodeAndDeleted(code, Constants.LONG_ZERO);
        if (containerOptional.isEmpty()) {
            return null;
        }
        Container container = containerOptional.get();
        ContainerHistoryDTO containerHistoryDTO = new ContainerHistoryDTO();
        containerHistoryDTO.setContainer(container);

        List<ContainerHistoryDTO.HistoryDTO> historyDTOList = Lists.newArrayList();
        // 获取容器详情
        List<ContainerDetail> containerDetailList = containerDetailRepository.findByContainerIdAndDeleted(container.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(containerDetailList)) {
            containerDetailList.forEach(containerDetail -> {
                if (containerDetail.getBindTime() != null) {
                    ContainerHistoryDTO.HistoryDTO historyDTO = new ContainerHistoryDTO.HistoryDTO();
                    historyDTO.setType(Constants.INT_ZERO)
                            .setSubWorkSheet(containerDetail.getBatchWorkDetail().getSubWorkSheet())
                            .setLocalDateTime(containerDetail.getBindTime());
                    historyDTOList.add(historyDTO);
                }
                if (containerDetail.getUnbindTime() != null) {
                    ContainerHistoryDTO.HistoryDTO historyDTO = new ContainerHistoryDTO.HistoryDTO();
                    historyDTO.setType(Constants.INT_ONE)
                            .setSubWorkSheet(containerDetail.getBatchWorkDetail().getSubWorkSheet())
                            .setLocalDateTime(containerDetail.getUnbindTime());
                    historyDTOList.add(historyDTO);
                }
            });
        }
        // 获取维修分析详情
        List<MaintainHistoryDetailDTO> maintainHistoryDetailDTOList = rbaseMaintainHistoryDetailProxy.findByMaintainHistoryContainerDetailContainerIdAndDeleted(container.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(maintainHistoryDetailDTOList)) {
            maintainHistoryDetailDTOList.forEach(maintainHistoryDTO -> {
                if (maintainHistoryDTO.getWsRework() != null) {
                    ContainerHistoryDTO.HistoryDTO historyDTO = new ContainerHistoryDTO.HistoryDTO();
                    historyDTO.setType(Constants.INT_TWO)
                            .setWorkSheet(maintainHistoryDTO.getWsRework().getReworkWorkSheet())
                            .setLocalDateTime(maintainHistoryDTO.getRecordDate());
                    historyDTOList.add(historyDTO);
                }
            });
        }
        historyDTOList.sort(Comparator.comparing(ContainerHistoryDTO.HistoryDTO::getLocalDateTime));
        containerHistoryDTO.setHistoryDTOList(historyDTOList);
        return containerHistoryDTO;
    }

    /**
     * 获取Sn履历
     *
     * @param code sn编码
     * <AUTHOR>
     * @since 1.8.0
     */
    public SnHistoryDTO getSnHistory(String code) {
        Optional<SnWorkStatus> snWorkStatusOptional = snWorkStatusRepository.findBySnAndDeleted(code, Constants.LONG_ZERO);
        if (snWorkStatusOptional.isEmpty()) {
            return null;
        }
        SnWorkStatus snWorkStatus = snWorkStatusOptional.get();
        SnHistoryDTO snHistoryDTO = new SnHistoryDTO();
        snHistoryDTO.setSnWorkStatus(snWorkStatus);
        List<SnHistoryDTO.HistoryDTO> historyDTOList = Lists.newArrayList();
        if (snWorkStatus.getStartDate() != null) {
            SnHistoryDTO.HistoryDTO historyDTO = new SnHistoryDTO.HistoryDTO();
            historyDTO.setType(Constants.INT_ZERO)
                    .setLocalDateTime(snWorkStatus.getStartDate())
                    .setSubWorkSheet(snWorkStatus.getSubWorkSheet());
        }
        if (snWorkStatus.getEndDate() != null) {
            SnHistoryDTO.HistoryDTO historyDTO = new SnHistoryDTO.HistoryDTO();
            historyDTO.setType(Constants.INT_THREE)
                    .setLocalDateTime(snWorkStatus.getEndDate())
                    .setSubWorkSheet(snWorkStatus.getSubWorkSheet());
        }
        List<MaintainHistoryDetailDTO> maintainHistoryDetailDTOList = rbaseMaintainHistoryDetailProxy.findByMaintainHistorySnWorkStatusIdAndDeleted(snWorkStatus.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(maintainHistoryDetailDTOList)) {
            maintainHistoryDetailDTOList.forEach(maintainHistoryDTO -> {
                if (maintainHistoryDTO.getWsRework() != null) {
                    SnHistoryDTO.HistoryDTO historyDTO = new SnHistoryDTO.HistoryDTO();
                    historyDTO.setType(Constants.INT_TWO)
                            .setWorkSheet(maintainHistoryDTO.getWsRework().getReworkWorkSheet())
                            .setLocalDateTime(maintainHistoryDTO.getRecordDate());
                    historyDTOList.add(historyDTO);
                }
            });
        }
        List<SnRework> snReworkList = snReworkRepository.findBySnWorkStatusIdAndDeleted(snWorkStatus.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(snReworkList)) {
            snReworkList.forEach(snRework -> {
                if (snRework.getWsRework() != null) {
                    SnHistoryDTO.HistoryDTO historyDTO = new SnHistoryDTO.HistoryDTO();
                    historyDTO.setType(Constants.INT_ONE)
                            .setWorkSheet(snRework.getWsRework().getReworkWorkSheet())
                            .setLocalDateTime(snRework.getCreatedDate().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime());
                    historyDTOList.add(historyDTO);
                }
            });
        }
        historyDTOList.sort(Comparator.comparing(SnHistoryDTO.HistoryDTO::getLocalDateTime));
        snHistoryDTO.setHistoryDTOList(historyDTOList);
        return snHistoryDTO;
    }

    /**
     * 获取物料履历
     *
     * @param orderListRequestDTO 请求参数
     * <AUTHOR>
     * @since 1.8.0
     */
    public MaterialHistoryDTO getMaterialHistory(OrderListRequestDTO orderListRequestDTO) {
        Optional<MaterialDTO> materialDTOOptional = rbaseMaterialProxy.findByCodeAndDeleted(orderListRequestDTO.getCode(), Constants.LONG_ZERO);
        if (materialDTOOptional.isEmpty()) {
            return null;
        }
        MaterialDTO materialDTO = materialDTOOptional.get();
        MaterialHistoryDTO materialHistoryDTO = new MaterialHistoryDTO();
        materialHistoryDTO.setMaterialDTO(materialDTO)
                .setLot(orderListRequestDTO.getBatch());
        List<MaterialHistoryDTO.HistoryDTO> historyDTOList = Lists.newArrayList();
        // 获取来料检验
        List<IqcCheckHistory> iqcCheckHistoryList = iqcCheckHistoryRepository.findByMaterialIdAndDeleted(materialDTO.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(iqcCheckHistoryList)) {
            if (StringUtils.isNotBlank(orderListRequestDTO.getBatch())) {
                iqcCheckHistoryList = iqcCheckHistoryList.stream().filter(iqcCheckHistory -> Objects.equals(iqcCheckHistory.getLot(), orderListRequestDTO.getBatch())).toList();
            }
            iqcCheckHistoryList.forEach(iqcCheckHistory -> {
                MaterialHistoryDTO.HistoryDTO historyDTO = new MaterialHistoryDTO.HistoryDTO();
                historyDTO.setType(Constants.INT_ZERO).setIqcCheckHistory(iqcCheckHistory).setLocalDateTime(iqcCheckHistory.getCheckTime());
                historyDTOList.add(historyDTO);
            });
        }
        // 获取领料
        List<WsCheckMaterialDetail> wsCheckMaterialDetailList = wsCheckMaterialDetailRepository.findByMaterialIdAndDeleted(materialDTO.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(wsCheckMaterialDetailList)) {
            wsCheckMaterialDetailList.forEach(wsCheckMaterialDetail -> {
                MaterialHistoryDTO.HistoryDTO historyDTO = new MaterialHistoryDTO.HistoryDTO();
                historyDTO.setType(Constants.INT_TWO).setWsCheckMaterialDetail(wsCheckMaterialDetail).setLocalDateTime(wsCheckMaterialDetail.getCreatedDate().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime());
                historyDTOList.add(historyDTO);
            });
        }
        // 获取退料
        List<WsMaterialReturn> wsMaterialReturnList = wsMaterialReturnRepository.findByMaterialIdAndDeleted(materialDTO.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(wsMaterialReturnList)) {
            wsMaterialReturnList.forEach(wsMaterialReturn -> {
                MaterialHistoryDTO.HistoryDTO historyDTO = new MaterialHistoryDTO.HistoryDTO();
                historyDTO.setType(Constants.INT_THREE).setWsMaterialReturn(wsMaterialReturn).setLocalDateTime(wsMaterialReturn.getRecordDate());
                historyDTOList.add(historyDTO);
            });
        }
        historyDTOList.sort(Comparator.comparing(MaterialHistoryDTO.HistoryDTO::getLocalDateTime));
        materialHistoryDTO.setHistoryDTOList(historyDTOList);
        return materialHistoryDTO;
    }

    /**
     * 获取设备履历
     *
     * @param code 设备编码
     * <AUTHOR>
     * @since 1.8.0
     */
    public FacilityHistoryDTO getFacilityHistory(String code) {
        FacilityDTO facilityDTO = rbaseFacilityProxy.findByCodeAndDeleted(code, Constants.LONG_ZERO);
        if (facilityDTO == null) {
            return null;
        }
        FacilityHistoryDTO facilityHistoryDTO = new FacilityHistoryDTO();
        facilityHistoryDTO.setFacilityDTO(facilityDTO);
        List<FacilityHistoryDTO.HistoryDTO> historyDTOList = Lists.newArrayList();
        FacilityHistoryDTO.HistoryDTO historyDTO = new FacilityHistoryDTO.HistoryDTO();
        historyDTO.setType(Constants.INT_ZERO).setLocalDateTime(facilityDTO.getCreatedDate().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime());
        historyDTOList.add(historyDTO);
        // 获取保养信息
        List<FacilityLedgerDTO> facilityLedgerDTOList = rbaseFacilityLedgerProxy.findByFacilityIdAndDeleted(facilityDTO.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(facilityLedgerDTOList)) {
            facilityLedgerDTOList.forEach(facilityLedgerDTO -> {
                FacilityHistoryDTO.HistoryDTO newHistoryDTO = new FacilityHistoryDTO.HistoryDTO();
                newHistoryDTO.setNote(facilityLedgerDTO.getNote())
                        .setLocalDateTime(facilityLedgerDTO.getRecordDate());
                if (facilityLedgerDTO.getCategory() == Constants.INT_FOUR) {
                    newHistoryDTO.setType(Constants.INT_ONE);
                    historyDTOList.add(historyDTO);
                }
                if (facilityLedgerDTO.getCategory() == Constants.INT_FIVE) {
                    newHistoryDTO.setType(Constants.INT_TWO);
                    historyDTOList.add(historyDTO);
                }
                if (facilityLedgerDTO.getCategory() == Constants.INT_SEVEN) {
                    newHistoryDTO.setType(Constants.INT_THREE);
                    historyDTOList.add(historyDTO);
                }
            });
        }
        historyDTOList.sort(Comparator.comparing(FacilityHistoryDTO.HistoryDTO::getLocalDateTime));
        facilityHistoryDTO.setHistoryDTOList(historyDTOList);
        return facilityHistoryDTO;
    }

    /**
     * 获取易损件履历
     *
     * @param wearingPartHistoryRequestDTO 易损件履历请求DTO
     * <AUTHOR>
     * @since 1.8.0
     */
    public WearingPartHistoryDTO getWearingPartHistory(WearingPartHistoryRequestDTO wearingPartHistoryRequestDTO) {
        Optional<WearingPart> wearingPartOptional = wearingPartRepository.findByCodeAndSerialNumberAndDeleted(wearingPartHistoryRequestDTO.getCode(), wearingPartHistoryRequestDTO.getSerialNumber(), Constants.LONG_ZERO);
        if (wearingPartOptional.isEmpty()) {
            return null;
        }
        WearingPart wearingPart = wearingPartOptional.get();
        WearingPartHistoryDTO wearingPartHistoryDTO = new WearingPartHistoryDTO();
        wearingPartHistoryDTO.setWearingPart(wearingPart);
        List<WearingPartHistoryDTO.HistoryDTO> historyDTOList = Lists.newArrayList();
        // 创建
        WearingPartHistoryDTO.HistoryDTO creatHistoryDTO = new WearingPartHistoryDTO.HistoryDTO();
        creatHistoryDTO.setType(Constants.INT_ZERO).setLocalDateTime(wearingPart.getCreatedDate().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime());
        historyDTOList.add(creatHistoryDTO);
        // 报废
        if (wearingPart.getStatus() == 3) {
            WearingPartHistoryDTO.HistoryDTO scrapHistoryDTO = new WearingPartHistoryDTO.HistoryDTO();
            creatHistoryDTO.setType(Constants.INT_THREE).setLocalDateTime(wearingPart.getLastModifiedDate().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime());
            historyDTOList.add(scrapHistoryDTO);
        }
        // 使用
        List<BatchWorkDetailWearingPart> batchWorkDetailWearingPartList = batchWorkDetailWearingPartRepository.findByWearingPartIdAndDeleted(wearingPart.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(batchWorkDetailWearingPartList)) {
            batchWorkDetailWearingPartList.forEach(batchWorkDetailWearingPart -> {
                WearingPartHistoryDTO.HistoryDTO scrapHistoryDTO = new WearingPartHistoryDTO.HistoryDTO();
                creatHistoryDTO.setType(Constants.INT_ONE)
                        .setBatchWorkDetailWearingPart(batchWorkDetailWearingPart)
                        .setLocalDateTime(batchWorkDetailWearingPart.getCreatedDate().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime());
                historyDTOList.add(scrapHistoryDTO);
            });
        }
        // 重置
        List<WearingPartResetHistory> wearingPartResetHistoryList = wearingPartResetHistoryRepository.findByWearingPartIdAndDeleted(wearingPart.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(wearingPartResetHistoryList)) {
            wearingPartResetHistoryList.forEach(wearingPartResetHistory -> {
                WearingPartHistoryDTO.HistoryDTO scrapHistoryDTO = new WearingPartHistoryDTO.HistoryDTO();
                creatHistoryDTO.setType(Constants.INT_TWO)
                        .setWearingPartResetHistory(wearingPartResetHistory)
                        .setLocalDateTime(wearingPartResetHistory.getCreatedDate().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime());
                historyDTOList.add(scrapHistoryDTO);
            });
        }
        historyDTOList.sort(Comparator.comparing(WearingPartHistoryDTO.HistoryDTO::getLocalDateTime));
        wearingPartHistoryDTO.setHistoryDTOList(historyDTOList);
        return wearingPartHistoryDTO;
    }

    /**
     * 获取员工履历
     *
     * @param code 员工编码
     * <AUTHOR>
     * @since 1.8.0
     */
    public StaffHistoryDTO getStaffHistory(String code) {
        Optional<StaffDTO> staffDTOOptional = rbaseStaffProxy.findByCodeAndDeleted(code, Constants.LONG_ZERO);
        if (staffDTOOptional.isEmpty()) {
            return null;
        }
        StaffDTO staffDTO = staffDTOOptional.get();
        StaffHistoryDTO staffHistoryDTO = new StaffHistoryDTO();
        staffHistoryDTO.setStaff(staffDTO);
        List<StaffHistoryDTO.HistoryDTO> historyDTOList = Lists.newArrayList();
        if (staffDTO.getEntryDay() != null) {
            StaffHistoryDTO.HistoryDTO historyDTO = new StaffHistoryDTO.HistoryDTO();
            historyDTO.setType(Constants.INT_ZERO).setLocalDateTime(staffDTO.getEntryDay().atTime(LocalTime.of(0, 0, 0)));
            historyDTOList.add(historyDTO);
        }
        if (staffDTO.getLeaveDay() != null) {
            StaffHistoryDTO.HistoryDTO historyDTO = new StaffHistoryDTO.HistoryDTO();
            historyDTO.setType(Constants.INT_THREE).setLocalDateTime(staffDTO.getLeaveDay().atTime(LocalTime.of(0, 0, 0)));
            historyDTOList.add(historyDTO);
        }
        // 获取技能
        List<StaffSkillDTO> staffSkillDTOList = rbaseSkillProxy.findAllSkillByStaffIdAndDeleted(staffDTO.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(staffSkillDTOList)) {
            staffSkillDTOList.forEach(staffSkillDTO -> {
                StaffHistoryDTO.HistoryDTO newHistoryDTO = new StaffHistoryDTO.HistoryDTO();
                newHistoryDTO.setType(Constants.INT_ONE)
                        .setSkill(staffSkillDTO.getSkill())
                        .setLocalDateTime(staffSkillDTO.getCreatedDate().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime());
                historyDTOList.add(newHistoryDTO);
            });
        }
        // 获取工位
        List<WorkCellStaff> workCellStaffList = workCellStaffRepository.findWorkCellStaffByStaffId(staffDTO.getId());
        if (ValidateUtils.isValid(workCellStaffList)) {
            workCellStaffList.forEach(workCellStaff -> {
                StaffHistoryDTO.HistoryDTO newHistoryDTO = new StaffHistoryDTO.HistoryDTO();
                newHistoryDTO.setType(Constants.INT_TWO)
                        .setWorkCell(workCellStaff.getWorkCell())
                        .setLocalDateTime(workCellStaff.getCreatedDate().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime());
                historyDTOList.add(newHistoryDTO);
            });
        }
        historyDTOList.sort(Comparator.comparing(StaffHistoryDTO.HistoryDTO::getLocalDateTime));
        staffHistoryDTO.setHistoryDTOList(historyDTOList);
        return staffHistoryDTO;
    }

    public void export(List<WorkSheetHistoryExportDTO> workSheetHistoryExportDTOList, HttpServletResponse response) {
        if (CollectionUtils.isEmpty(workSheetHistoryExportDTOList)) {
            throw new ResponseException("error.FacilityPlanDownHistoryExists", "暂无导出工单数据");
        }
        OrderListDTO orderList = this.getOrderList(orderListRequestDTO);
        List<String> workSheetCodeList = Lists.newArrayList();
        getWorkSheetCodeList(orderList, workSheetCodeList);
    }

    private void getWorkSheetCodeList(OrderListDTO orderList, List<String> workSheetCodeList) {
        if (!ValidateUtils.isValid(orderList.getOrderListDTOList())) {
            if (Lists.newArrayList(OrderTypeEnum.WORK_SHEET.getCode(), OrderTypeEnum.SUB_WORK_SHEET.getCode(), OrderTypeEnum.WS_REWORK.getCode()).contains(orderList.getType())) {

            }
            return;
        }
    }
}