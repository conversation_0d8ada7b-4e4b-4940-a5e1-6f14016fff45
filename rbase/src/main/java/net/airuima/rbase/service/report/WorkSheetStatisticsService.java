package net.airuima.rbase.service.report;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.OperationEnum;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.report.WorkSheetStatistics;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.report.WorkSheetStatisticsRepository;
import net.airuima.service.CommonJpaService;
import net.airuima.rbase.service.report.api.IWorkSheetStatisticsService;
import org.springframework.boot.autoconfigure.klock.annotation.Klock;
import org.springframework.boot.autoconfigure.klock.model.LockTimeoutStrategy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单产量统计 Service
 *
 * <AUTHOR>
 * @date 2023/07/03
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkSheetStatisticsService extends CommonJpaService<WorkSheetStatistics> implements IWorkSheetStatisticsService {

    private final WorkSheetStatisticsRepository workSheetStatisticsRepository;

    private final WorkSheetRepository workSheetRepository;

    public WorkSheetStatisticsService(WorkSheetStatisticsRepository workSheetStatisticsRepository, WorkSheetRepository workSheetRepository) {
        this.workSheetStatisticsRepository = workSheetStatisticsRepository;
        this.workSheetRepository = workSheetRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WorkSheetStatistics> find(Specification<WorkSheetStatistics> spec, Pageable pageable) {
        return workSheetStatisticsRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<WorkSheetStatistics> find(Specification<WorkSheetStatistics> spec) {
        return workSheetStatisticsRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<WorkSheetStatistics> findAll(Pageable pageable) {
        return workSheetStatisticsRepository.findAll(pageable);
    }


    /**
     * 更新工单统计表
     *
     * @param workSheetId       工单id
     * @param recordDate        记录时间
     * @param qualifiedNumber   合格数
     * @param unQualifiedNumber 不合格数
     * @param operationEnum     运算符
     */
    @Klock(keys = {"#workSheetId", "#recordDate"}, waitTime = 60, leaseTime = 60, lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST)
    @Override
    public void updateWorkSheetNumber(Long workSheetId, LocalDate recordDate, Integer qualifiedNumber, Integer unQualifiedNumber, OperationEnum operationEnum) {
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findByIdAndDeleted(workSheetId, Constants.LONG_ZERO);
        if (workSheetOptional.isEmpty()) {
            return;
        }
        WorkSheet workSheet = workSheetOptional.get();
        Optional<WorkSheetStatistics> workSheetStatisticsOptional = workSheetStatisticsRepository.findByRecordDateAndWorkSheetIdAndDeleted(recordDate, workSheetId, Constants.LONG_ZERO);
        //不存在则新增
        if (workSheetStatisticsOptional.isEmpty()) {
            WorkSheetStatistics workSheetStatistics = new WorkSheetStatistics();
            workSheetStatistics.setRecordDate(recordDate)
                    .setInputNumber(Optional.of(workSheet.getNumber()).orElse(Constants.INT_ZERO))
                    .setWorkSheet(workSheetOptional.get())
                    .setQualifiedNumber(Optional.ofNullable(qualifiedNumber).orElse(Constants.INT_ZERO))
                    .setUnqualifiedNumber(Optional.ofNullable(unQualifiedNumber).orElse(Constants.INT_ZERO));
            workSheetStatisticsRepository.save(workSheetStatistics);
            return;
        }
        WorkSheetStatistics workSheetStatistics = workSheetStatisticsOptional.get();
        // 原来的合格数
        Integer originQualifiedNumber = Optional.of(workSheetStatistics).map(WorkSheetStatistics::getQualifiedNumber).orElse(Constants.INT_ZERO);
        // 原来的不合格数
        Integer originUnQualifiedNumber = Optional.of(workSheetStatistics).map(WorkSheetStatistics::getUnqualifiedNumber).orElse(Constants.INT_ZERO);
        // 加
        if (OperationEnum.ADD.equals(operationEnum)) {
            workSheetStatistics.setQualifiedNumber(originQualifiedNumber + qualifiedNumber)
                    .setUnqualifiedNumber(originUnQualifiedNumber + unQualifiedNumber);
        }
        //减
        if (OperationEnum.MINUS.equals(operationEnum)) {
            workSheetStatistics.setQualifiedNumber(originQualifiedNumber - qualifiedNumber)
                    .setUnqualifiedNumber(originUnQualifiedNumber - unQualifiedNumber);
        }
        workSheetStatisticsRepository.save(workSheetStatistics);

    }
}
