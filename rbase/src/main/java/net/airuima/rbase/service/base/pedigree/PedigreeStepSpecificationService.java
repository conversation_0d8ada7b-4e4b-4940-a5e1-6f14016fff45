package net.airuima.rbase.service.base.pedigree;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.PedigreeStepSpecificationExcelConstants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepSpecification;
import net.airuima.rbase.domain.base.pedigree.PedigreeWorkFlow;
import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.dto.document.DocumentDTO;
import net.airuima.rbase.dto.document.DocumentRelationDTO;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.proxy.document.RbaseDocumentProxy;
import net.airuima.rbase.proxy.organization.RbaseClientProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.repository.base.pedigree.PedigreeStepSpecificationRepository;
import net.airuima.rbase.repository.base.priority.PriorityElementConfigRepository;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.service.base.priority.PriorityElementConfigService;
import net.airuima.rbase.util.ExcelUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rbase.web.rest.base.pedigree.dto.PedigreeStepSpecificationImportDTO;
import net.airuima.rbase.web.rest.base.pedigree.dto.PedigreeStepSpecificationImportErrorDTO;
import net.airuima.service.CommonJpaService;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseException;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工序指标Service
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PedigreeStepSpecificationService extends CommonJpaService<PedigreeStepSpecification> {
    private static final String PEDIGREE_STEP_SPECIFICATION_ENTITY_GRAPH = "pedigreeStepSpecificationEntityGraph";
    private final PedigreeStepSpecificationRepository pedigreeStepSpecificationRepository;

    @Value("${spring.application.name}")
    private String applicationName;
    @Autowired
    private RbaseDocumentProxy rbaseDocumentProxy;

    @Autowired
    private PriorityElementConfigRepository priorityElementConfigRepository;

    @Autowired
    private PedigreeRepository pedigreeRepository;

    @Autowired
    private WorkFlowRepository workFlowRepository;

    @Autowired
    private StepRepository stepRepository;

    @Autowired
    private PriorityElementConfigService priorityElementConfigService;

    @Autowired
    private RbaseClientProxy rbaseClientProxy;

    public PedigreeStepSpecificationService(PedigreeStepSpecificationRepository pedigreeStepSpecificationRepository) {
        this.pedigreeStepSpecificationRepository = pedigreeStepSpecificationRepository;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<PedigreeStepSpecification> find(Specification<PedigreeStepSpecification> spec, Pageable pageable) {
        return pedigreeStepSpecificationRepository.findAll(spec, pageable, new NamedEntityGraph(PEDIGREE_STEP_SPECIFICATION_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<PedigreeStepSpecification> find(Specification<PedigreeStepSpecification> spec) {
        return pedigreeStepSpecificationRepository.findAll(spec, new NamedEntityGraph(PEDIGREE_STEP_SPECIFICATION_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<PedigreeStepSpecification> findAll(Pageable pageable) {
        return pedigreeStepSpecificationRepository.findAll(pageable, new NamedEntityGraph(PEDIGREE_STEP_SPECIFICATION_ENTITY_GRAPH));
    }


    /**
     * 保存产品谱系工序指标
     *
     * @param entity 保存产品谱系工序指标参数
     * @return net.airuima.rbase.dto.base.BaseResultDTO 结果信息
     */
    public BaseResultDTO saveInstance(PedigreeStepSpecification entity) {
        if(Objects.isNull(entity.getPriorityElementConfig())){
            // 工序指标
            entity.setPriorityElementConfig(getPedigreeStepSpecificationPriority(Objects.nonNull(entity.getPedigree())?entity.getPedigree().getId():null,
                    Objects.nonNull(entity.getWorkFlow())?entity.getWorkFlow().getId():null, Objects.nonNull(entity.getStep())?entity.getStep().getId():null,
                    Objects.nonNull(entity.getClientId())?entity.getClientId():null));
        }
        if (Objects.isNull(entity.getPriorityElementConfig())) {
            return new BaseResultDTO(Constants.KO, "没有匹配的组合条件", PedigreeWorkFlow.class.getSimpleName(), "error.noMatchCombination");
        }
        Optional<PriorityElementConfig> priorityElementConfigOptional = priorityElementConfigRepository.findByIdAndDeleted(entity.getPriorityElementConfig().getId(), Constants.LONG_ZERO);
        if (priorityElementConfigOptional.isEmpty()) {
            return new BaseResultDTO(Constants.KO, "没有匹配的组合条件", PedigreeWorkFlow.class.getSimpleName(), "error.noMatchCombination");
        }
        BaseResultDTO baseResultDTO = checkPedigreeStepSpecification(entity);
        if (Objects.nonNull(baseResultDTO)) {
            return baseResultDTO;
        }
        entity.setDeleted(Constants.LONG_ZERO);
        pedigreeStepSpecificationRepository.save(entity);
        //文件关联
        DocumentRelationDTO documentRelationDTO = new DocumentRelationDTO().setServiceName(StringUtils.upperCase(applicationName)).setRecordId(entity.getId());
        if (!CollectionUtils.isEmpty(entity.getDocumentDTOList())) {
            List<DocumentRelationDTO.Document> documentList = entity.getDocumentDTOList().stream().map(i -> new DocumentRelationDTO.Document().setDocumentId(i.getId())).toList();
            documentRelationDTO.setDocumentList(documentList);
        }
        rbaseDocumentProxy.relation(documentRelationDTO);
        return new BaseResultDTO(Constants.OK, entity);
    }

    /**
     * 校验产品谱系工序指标
     *
     * @param pedigreeStepSpecification 产品谱系工序指标
     * @return net.airuima.rbase.dto.base.BaseResultDTO 结果信息
     */
    private BaseResultDTO checkPedigreeStepSpecification(PedigreeStepSpecification pedigreeStepSpecification) {
        // 产品谱系
        Long pedigreeId = null != pedigreeStepSpecification.getPedigree() ? pedigreeStepSpecification.getPedigree().getId() : null;
        // 工艺路线
        Long workFlowId = null != pedigreeStepSpecification.getWorkFlow() ? pedigreeStepSpecification.getWorkFlow().getId() : null;
        // 工序
        Long stepId = null != pedigreeStepSpecification.getStep() ? pedigreeStepSpecification.getStep().getId() : null;
        //客户代码
        Long clientId = null != pedigreeStepSpecification.getClientId() ? pedigreeStepSpecification.getClientId() : null;
        // 查询配置
        PedigreeStepSpecification queryPedigreeStepSpecification = pedigreeStepSpecificationRepository.findInfoByPedigreeIdAndWorkFlowIdAndStepIdAndClientIdAndDeleted(pedigreeId, workFlowId, stepId, clientId, Constants.LONG_ZERO).orElse(null);
        // 新增时校验配置是否已存在
        if (Objects.isNull(pedigreeStepSpecification.getId()) && Objects.nonNull(queryPedigreeStepSpecification)) {
            return new BaseResultDTO(Constants.KO, "产品谱系工序指标已存在", StringUtils.uncapitalize(PedigreeWorkFlow.class.getSimpleName()), "PedigreeStepSpecificationExists");
        }
        // 更新时校验配置
        if (Objects.nonNull(pedigreeStepSpecification.getId()) && Objects.nonNull(queryPedigreeStepSpecification) && !queryPedigreeStepSpecification.getId().equals(pedigreeStepSpecification.getId())) {
            return new BaseResultDTO(Constants.KO, "产品谱系工序指标已存在", StringUtils.uncapitalize(PedigreeWorkFlow.class.getSimpleName()), "PedigreeStepSpecificationExists");
        }
        return null;
    }

    /**
     * 根据条件获取工序SOP
     * @param pedigreeId   产品谱系ID
     * @param clientId 客户ID
     * @param workFlowId   工艺路线ID
     * @param stepId       工序ID
     * @return PedigreeStepSpecification
     */
    public PedigreeStepSpecification findAllByPedigreeIdAndClientIdAndWorkFlowIdAndStepId(Long pedigreeId, Long clientId, Long workFlowId, Long stepId) {
        return pedigreeStepSpecificationRepository.findInfoByPedigreeIdAndWorkFlowIdAndStepIdAndClientIdAndDeleted(pedigreeId, workFlowId, stepId, clientId, net.airuima.constant.Constants.LONG_ZERO).orElse(null);
    }

    /**
     * 详情重写
     *
     * @param id 产品谱系工序指标ID
     * @return : ResponseEntity<PedigreeStepSpecification>
     * <AUTHOR>
     * @date 2022/8/18
     **/
    public PedigreeStepSpecification get(Long id) {
        PedigreeStepSpecification pedigreeStepSpecification = pedigreeStepSpecificationRepository.findByIdAndDeleted(id, Constants.LONG_ZERO);

        //查询文件集合
        List<DocumentDTO> documentDTOList = rbaseDocumentProxy.getByRecordId(pedigreeStepSpecification.getId());
        if (!CollectionUtils.isEmpty(documentDTOList)) {
            pedigreeStepSpecification.setDocumentDTOList(documentDTOList);
        }
        return pedigreeStepSpecification;
    }

    /**
     * 产品谱系工序指标导入 重写支持优先级配置
     *
     * @param file          导入文件
     * @param response      响应
     */
    public void importPedigreeStepSpecification(MultipartFile file, HttpServletResponse response) throws Exception{

        ImportParams params = new ImportParams();
        params.setTitleRows(0);
        params.setHeadRows(2);

        List<PedigreeStepSpecificationImportDTO> pedigreeStepSpecificationImportDTOList = ExcelImportUtil.importExcel(file.getInputStream(), PedigreeStepSpecificationImportDTO.class, params);

        if (CollectionUtils.isEmpty(pedigreeStepSpecificationImportDTOList)) {
            throw new ResponseException("excelImportDataNull","导入Excel失败,数据为空");
        }
        //产品谱系
        List<String> pedigreeCodeList = pedigreeStepSpecificationImportDTOList.stream().map(PedigreeStepSpecificationImportDTO::getPedigreeCode)
                .filter(Objects::nonNull).distinct().toList();

        //工艺路线
        List<String> workflowCodeList = pedigreeStepSpecificationImportDTOList.stream().map(PedigreeStepSpecificationImportDTO::getWorkflowCode)
                .filter(Objects::nonNull).distinct().toList();

        //工序
        List<String> stepCodeList = pedigreeStepSpecificationImportDTOList.stream().map(PedigreeStepSpecificationImportDTO::getStepCode)
                .filter(Objects::nonNull).distinct().toList();

        //客户
        List<String> clientCodeList = pedigreeStepSpecificationImportDTOList.stream().map(PedigreeStepSpecificationImportDTO::getClientCode)
                .filter(Objects::nonNull).distinct().toList();

        //查询产品谱系
        List<Pedigree> pedigreeList = pedigreeRepository.findByCodeInAndDeleted(pedigreeCodeList, Constants.LONG_ZERO);
        Map<String, Pedigree> pedigreeMap = new HashMap<>();
        if (ValidateUtils.isValid(pedigreeCodeList)) {
            pedigreeMap.putAll(pedigreeList.stream().collect(Collectors.toMap(Pedigree::getCode, i -> i)));
        }
        //查询工艺路线
        List<WorkFlow> workFlowList = workFlowRepository.findByCodeInAndDeleted(workflowCodeList, Constants.LONG_ZERO);
        Map<String, WorkFlow> workFlowMap = new HashMap<>();
        if (ValidateUtils.isValid(workflowCodeList)) {
            workFlowMap.putAll(workFlowList.stream().collect(Collectors.toMap(WorkFlow::getCode, i -> i)));
        }
        //查询工序
        List<Step> stepList = stepRepository.findByCodeInAndDeleted(stepCodeList, Constants.LONG_ZERO);
        Map<String, Step> stepMap = new HashMap<>();
        if (ValidateUtils.isValid(stepCodeList)) {
            stepMap.putAll(stepList.stream().collect(Collectors.toMap(Step::getCode, i -> i)));
        }
        //查询客户
        List<ClientDTO> clientDTOList = rbaseClientProxy.findByCodeIn(clientCodeList);
        Map<String, ClientDTO> clientMap = new HashMap<>();
        if (ValidateUtils.isValid(clientCodeList)) {
            clientMap.putAll(clientDTOList.stream().collect(Collectors.toMap(ClientDTO::getCode, i -> i)));
        }

        // 校验数据
        List<PedigreeStepSpecificationImportErrorDTO> errorList = new ArrayList<>();
        AtomicInteger successCount = new AtomicInteger(Constants.INT_ZERO);
        AtomicInteger errorCount = new AtomicInteger(Constants.INT_ZERO);

        pedigreeStepSpecificationImportDTOList.forEach(pedigreeStepSpecificationImportDTO -> {
            //产品谱系
            Pedigree pedigree = null;
            String pedigreeCode = pedigreeStepSpecificationImportDTO.getPedigreeCode();
            if (ValidateUtils.isValid(pedigreeCode)) {
                pedigree = pedigreeMap.get(pedigreeCode);
                if (Objects.isNull(pedigree)) {
                    errorList.add(new PedigreeStepSpecificationImportErrorDTO(pedigreeStepSpecificationImportDTO,"导入Excel失败,数据有误, 原因产品谱系不存在"));
                    errorCount.incrementAndGet();
                    return;
                }
            }
            //工艺路线
            WorkFlow workFlow = null;
            String workflowCode = pedigreeStepSpecificationImportDTO.getWorkflowCode();
            if (ValidateUtils.isValid(workflowCode)) {
                workFlow = workFlowMap.get(workflowCode);
                if (Objects.isNull(workFlow)) {
                    errorList.add(new PedigreeStepSpecificationImportErrorDTO(pedigreeStepSpecificationImportDTO,"导入Excel失败,数据有误, 原因工艺路线不存在"));
                    errorCount.incrementAndGet();
                    return;
                }
            }
            Step step = null;
            String stepCode = pedigreeStepSpecificationImportDTO.getStepCode();
            if (ValidateUtils.isValid(stepCode)) {
                step = stepMap.get(stepCode);
                if (Objects.isNull(step)) {
                    errorList.add(new PedigreeStepSpecificationImportErrorDTO(pedigreeStepSpecificationImportDTO,"导入Excel失败,数据有误, 原因工序不存在"));
                    errorCount.incrementAndGet();
                    return;
                }
            }
            ClientDTO clientDTO = null;
            String clientCode = pedigreeStepSpecificationImportDTO.getClientCode();
            if (ValidateUtils.isValid(clientCode)) {
                clientDTO = clientMap.get(clientCode);
                if (Objects.isNull(clientDTO)) {
                    errorList.add(new PedigreeStepSpecificationImportErrorDTO(pedigreeStepSpecificationImportDTO,"导入Excel失败,数据有误, 原因客户编码不存在"));
                    errorCount.incrementAndGet();
                    return;
                }
            }

            // 客户id
            Long clientId = Optional.ofNullable(clientDTO).map(ClientDTO::getId).orElse(null);
            // 工序id
            Long stepId = Optional.ofNullable(step).map(Step::getId).orElse(null);
            // 产品谱系
            Long pedigreeId = Optional.ofNullable(pedigree).map(Pedigree::getId).orElse(null);
            // 工艺路线
            Long workFlowId = Optional.ofNullable(workFlow).map(WorkFlow::getId).orElse(null);

            // 查询工艺路线是否已经存在
            PedigreeStepSpecification queryPedigreeStepSpecification = pedigreeStepSpecificationRepository.findInfoByPedigreeIdAndWorkFlowIdAndStepIdAndClientIdAndDeleted(pedigreeId, workFlowId, stepId, clientId, Constants.LONG_ZERO).orElse(null);
            // 工序指标
            PriorityElementConfig priorityElementConfig = getPedigreeStepSpecificationPriority(pedigreeId, workFlowId, stepId, clientId);
            if (priorityElementConfig == null) {
                errorList.add(new PedigreeStepSpecificationImportErrorDTO(pedigreeStepSpecificationImportDTO,"导入Excel失败, 数据有误, 该组合条件优先级未配置"));
                errorCount.incrementAndGet();
                return;
            }
            // 保存配置
            if (Objects.isNull(queryPedigreeStepSpecification)) {
                PedigreeStepSpecification pedigreeStepSpecification = new PedigreeStepSpecification();
                pedigreeStepSpecification.setPedigree(pedigree).setClientId(clientId).setWorkFlow(workFlow).setPriorityElementConfig(priorityElementConfig).setStep(step).setDeleted(Constants.LONG_ZERO);
                if (ValidateUtils.isValid(pedigreeStepSpecificationImportDTO.getForeignDocumentCodeDtos())){
                    List<String> documentList = pedigreeStepSpecificationImportDTO.getForeignDocumentCodeDtos().stream()
                            .map(PedigreeStepSpecificationImportDTO.ForeignDocumentCodeDTO::getDocumentCode).filter(document -> ValidateUtils.isValid(document) && !document.equals("null")).distinct().toList();
                    if (ValidateUtils.isValid(documentList)) {
                        pedigreeStepSpecification.setForeignDocumentCodeList(documentList);
                    }
                }
                pedigreeStepSpecificationRepository.save(pedigreeStepSpecification);
            }else{
                queryPedigreeStepSpecification.setPedigree(pedigree).setClientId(clientId).setWorkFlow(workFlow).setPriorityElementConfig(priorityElementConfig).setStep(step);
                if (ValidateUtils.isValid(pedigreeStepSpecificationImportDTO.getForeignDocumentCodeDtos())){
                    List<String> documentList = pedigreeStepSpecificationImportDTO.getForeignDocumentCodeDtos().stream()
                            .map(PedigreeStepSpecificationImportDTO.ForeignDocumentCodeDTO::getDocumentCode).filter(document -> ValidateUtils.isValid(document) && !document.equals("null")).distinct().toList();
                    if (ValidateUtils.isValid(documentList)) {
                        queryPedigreeStepSpecification.setForeignDocumentCodeList(documentList);
                    }
                }
                pedigreeStepSpecificationRepository.save(queryPedigreeStepSpecification);
            }
            successCount.incrementAndGet();
        });

        // 导出错误信息
        exportErrorImportData(successCount.get(),errorCount.get(),errorList, response,file);

    }

    public void exportErrorImportData(int successCount,int errorCount,List<PedigreeStepSpecificationImportErrorDTO> errorList,HttpServletResponse response,MultipartFile file) throws IOException {
        if (!ValidateUtils.isValid(errorList)){
            return;
        }
        ExportParams exportParams = new ExportParams();
        exportParams.setType(Objects.requireNonNull(file.getOriginalFilename()).contains("xlsx") ? ExcelType.XSSF : ExcelType.HSSF);
        // 将工作簿写入字节数组中
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, PedigreeStepSpecificationImportErrorDTO.class, errorList);
        response.setContentType(file.getOriginalFilename().contains("xlsx") ? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" : "application/vnd.ms-excel");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(file.getOriginalFilename(), "utf-8"));
        response.setStatus(HttpStatus.BAD_REQUEST.value());
        response.setHeader("X-app-alert", "app.import.failure");
        String errorMessage = "上传数据" + (successCount+errorCount) + "条,导入成功" + (successCount) + "条,导入失败" + errorCount + "条,请检查下载的文件,检查失败的详细原因";
        response.setHeader(HeaderUtil.APP_PARAMS, URLEncoder.encode(errorMessage, "UTF-8"));
        response.setHeader(HeaderUtil.APP_ERROR_MESSAGE, URLEncoder.encode(errorMessage, "UTF-8"));
        response.setHeader("X-app-alert", "app.import.failure");
        workbook.write(response.getOutputStream());
    }


    /**
     * 获取产品谱系工序指标条件优先级配置
     *
     * @param pedigreeId 产品谱系主键id
     * @param workFlowId 工艺路线主键id
     * @param stepId     工序主键id
     * @param clientId   客户主键id
     * @return net.airuima.rbase.domain.base.priority.PriorityElementConfig 优先级配置
     */
    public PriorityElementConfig getPedigreeStepSpecificationPriority(Long pedigreeId, Long workFlowId, Long stepId, Long clientId) {
        List<Integer> combination = new ArrayList<>();
        if (Objects.nonNull(pedigreeId)) {
            combination.add(Constants.PEDIGREE_ELEMENT);
        }
        if (Objects.nonNull(workFlowId)) {
            combination.add(Constants.WORKFLOW_ELEMENT);
        }
        if (Objects.nonNull(stepId)) {
            combination.add(Constants.STEP_ELEMENT);
        }
        if (Objects.nonNull(clientId)) {
            combination.add(Constants.CLIENT_ELEMENT);
        }
        return priorityElementConfigService.findUniquePriorityElementConfig(Constants.INT_FOURTEEN, combination);
    }

    /**
     * 导出重写
     */
    public void exportExcel(List<PedigreeStepSpecification> pedigreeStepSpecifications,
                            HttpServletResponse response,
                            String excelTitle) throws IOException { // 1. 明确指定异常类型
        // 初始化数据列表
        List<PedigreeStepSpecificationImportDTO> dataList = new ArrayList<>();
        // 2. 改进空数据初始化逻辑
        if (ValidateUtils.isValid(pedigreeStepSpecifications)) {
            dataList.addAll(pedigreeStepSpecifications.stream().map(PedigreeStepSpecificationImportDTO::new).toList());
        } else {
            // 创建包含完整结构的空数据
            PedigreeStepSpecificationImportDTO emptyDTO = new PedigreeStepSpecificationImportDTO();
            emptyDTO.setForeignDocumentCodeDtos(new ArrayList<>()); // 确保集合初始化
            dataList.add(emptyDTO);
        }
        // 4. 增强导出参数配置
        ExportParams exportParams = new ExportParams();
        exportParams.setType(ExcelType.XSSF);

        Workbook  workbook = ExcelExportUtil.exportExcel(exportParams, PedigreeStepSpecificationImportDTO.class, dataList);
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(excelTitle +".xlsx", StandardCharsets.UTF_8));
        response.setHeader("message", "export!");
        OutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
    }


}
