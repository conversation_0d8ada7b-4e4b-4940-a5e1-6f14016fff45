package net.airuima.rbase.repository.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.batch.ContainerDetailFacility;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.batch.ContainerDetailMaterialBatch;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器设备生产详情Repository
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Repository
public interface ContainerDetailFacilityRepository extends LogicDeleteableRepository<ContainerDetailFacility>,
        EntityGraphJpaSpecificationExecutor<ContainerDetailFacility>, EntityGraphJpaRepository<ContainerDetailFacility, Long> {

    /**
     * 通过容器详情主键ID，设备ID获取唯一记录
     *
     * @param containerDetailId 容器详情主键ID
     * @param facilityId       设备主键ID
     * @param deleted           逻辑删除
     * @return jjava.util.Optional<net.airuima.rbase.domain.procedure.batch.ContainerDetailFacility> 容器设备生产详情
     * <AUTHOR>
     * @date 2021-01-19
     **/
    @DataFilter(isSkip = true)
    Optional<ContainerDetailFacility> findByContainerDetailIdAndFacilityIdAndDeleted(Long containerDetailId, Long facilityId, Long deleted);

    /**
     *
     *  通过容器详情主键ID获取容器详情设备列表
     * @param containerDetailId 容器详情主键ID
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.batch.ContainerDetailFacility> 容器设备生产详情列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<ContainerDetailFacility> findByContainerDetailIdAndDeleted(Long containerDetailId, Long deleted);

    /**
     *
     * 通过批量详情主键ID获取容器详情设备列表
     * @param batchWorkDetailId 批量详情主键ID
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.batch.ContainerDetailFacility> 容器设备生产详情列表
     */
    @DataFilter(isSkip = true)
    List<ContainerDetailFacility> findByContainerDetailBatchWorkDetailIdAndDeleted(Long batchWorkDetailId,Long deleted);

    /**
     * 通过设备主键Id及日期获取详情数据 分页
     * <AUTHOR>
     * @param equipmentId 设备主键ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param deleted  逻辑删除
     * @param pageable  分页
     * @return java.util.List<net.airuima.rbase.domain.procedure.batch.ContainerDetailFacility> 容器设备生产详情列表
     * @date 2021-03-24
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    Page<ContainerDetailFacility> findByFacilityIdAndCreatedDateGreaterThanEqualAndCreatedDateLessThanEqualAndDeleted(Long equipmentId, Instant startDate, Instant endDate, Long deleted, Pageable pageable);

    /**
     * 根据容器详情主键ID删除设备信息
     * <AUTHOR>
     * @param containerDetailId    容器详情主键ID
     * @return void
     * @date 2021-06-13
     **/
    @Modifying
    @Query("update ContainerDetailFacility cde set cde.deleted=cde.id where cde.containerDetail.id=?1")
    void batchDeleteByContainerDetailId(Long containerDetailId);

    /**
     * 根据批次详情主键主键id 与 设备主键id 获取 当前设备详情使用列表
     * @param batchDetailId 批次详情主键id
     * @param facilityId 设备主键id
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.batch.ContainerDetailFacility> 容器设备生产详情列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select cdf from ContainerDetailFacility cdf where cdf.containerDetail.batchWorkDetail.id = ?1 and " +
            "cdf.facilityId = ?2 and cdf.deleted = ?3")
    List<ContainerDetailFacility> findByContainerDetailBatchWorkDetailIdAndFacilityIdAndDeleted(Long batchDetailId, Long facilityId, Long deleted);

    /**
     * 通过设备id获取在一定范围内使用该设备的工单
     *
     * @param facilityId 设备id
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkDetail> 容器设备生产详情列表
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    @Query("select c from ContainerDetailFacility c where c.facilityId = ?1 and c.containerDetail.recordDate >= ?2 and c.containerDetail.recordDate <= ?3 and c.deleted = 0L")
    List<ContainerDetailFacility> findByFacilityIdAndStartDateAndEndDate(Long facilityId, LocalDateTime startDate, LocalDateTime endDate);
}
