package net.airuima.rbase.repository.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器生产详情Repository
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Repository
public interface ContainerDetailRepository extends LogicDeleteableRepository<ContainerDetail>,
        EntityGraphJpaSpecificationExecutor<ContainerDetail>, EntityGraphJpaRepository<ContainerDetail, Long> {


    /**
     * 通过容器主键ID、子工单主键ID，容器状态获取最新一条记录
     *
     * @param containerId    容器主键ID
     * @param subWorkSheetId 子工单主键ID
     * @param status         状态
     * @param deleted        删除标识
     * @return java.util.Optional<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情
     * <AUTHOR>
     * @date 2021-01-14
     **/
    @DataFilter(isSkip = true)
    Optional<ContainerDetail> findTop1ByContainerIdAndBatchWorkDetailSubWorkSheetIdAndStatusAndDeletedOrderByIdDesc(Long containerId, Long subWorkSheetId, Integer status, Long deleted);

    /**
     * 通过容器主键ID、子工单主键ID，容器状态获取最新一条记录
     *
     * @param containerId    容器主键ID
     * @param subWorkSheetId 子工单主键ID
     * @param status         状态
     * @param deleted        删除标识
     * @return java.util.Optional<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情
     * <AUTHOR>
     * @date 2021-01-14
     **/
    @DataFilter(isSkip = true)
    Optional<ContainerDetail> findTop1ByBatchWorkDetailSubWorkSheetIdAndContainerIdAndStatusAndDeletedOrderByIdDesc(Long subWorkSheetId,Long containerId, Integer status, Long deleted);


    /**
     * 通过容器主键ID、工单主键ID，容器状态获取最新一条记录
     *
     * @param containerId    容器主键ID
     * @param workSheetId 工单主键ID
     * @param status         状态
     * @param deleted        删除标识
     * @return java.util.Optional<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情
     * <AUTHOR>
     * @date 2021-01-14
     **/
    @DataFilter(isSkip = true)
    Optional<ContainerDetail> findTop1ByContainerIdAndBatchWorkDetailWorkSheetIdAndStatusAndDeletedOrderByIdDesc(Long containerId, Long workSheetId, Integer status, Long deleted);




    /**
     * 通过容器主键ID、工单主键ID，容器状态获取最新一条记录
     *
     * @param containerId    容器主键ID
     * @param workSheetId 工单主键ID
     * @param status         状态
     * @param deleted        删除标识
     * @return java.util.Optional<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情
     * <AUTHOR>
     * @date 2021-01-14
     **/
    @DataFilter(isSkip = true)
    Optional<ContainerDetail> findTop1ByBatchWorkDetailWorkSheetIdAndContainerIdAndStatusAndDeletedOrderByIdDesc(Long workSheetId,Long containerId, Integer status, Long deleted);


    /**
     * 通过容器编码、容器状态获取最新一条记录
     *
     * @param containerCode 容器编码
     * @param status        容器详情状态
     * @param deleted       删除标识
     * @return java.util.Optional<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情
     * @throws
     * <AUTHOR>
     * @date 2021-01-12
     **/
    @DataFilter(isSkip = true)
    Optional<ContainerDetail> findTop1ByContainerCodeAndStatusAndDeletedOrderByIdDesc(String containerCode, Integer status, Long deleted);

    /**
     * 通过批量详情主键ID，容器主键ID，绑定状态获取容器详情
     *
     * @param batchDetailId 批量详情主键ID
     * @param containerId   容器主键ID
     * @param status        绑定状态
     * @param deleted       逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情
     * <AUTHOR>
     * @date 2021-01-19
     **/
    @DataFilter(isSkip = true)
    Optional<ContainerDetail> findTop1ByBatchWorkDetailIdAndContainerIdAndStatusAndDeletedOrderByIdDesc(Long batchDetailId, Long containerId, Integer status, Long deleted);

    /**
     * 通过子工单主键ID、容器主键ID获取容器详情列表
     *
     * @param subWorkSheetId 子工单主键ID
     * @param containerId    容器主键ID
     * @param deleted        删除标识
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     * @throws
     * <AUTHOR>
     * @date 2021-01-12
     **/
    @DataFilter(isSkip = true)
    List<ContainerDetail> findByBatchWorkDetailSubWorkSheetIdAndContainerIdAndDeletedOrderByIdDesc(Long subWorkSheetId, Long containerId, Long deleted);


    /**
     * 根据容器主键ID列表、子工单主键ID以及状态获取容器详情列表
     *
     * @param containerIds   容器主键ID列表
     * @param subWorkSheetId 子工单主键ID
     * @param status         绑定与解绑状态
     * @param deleted        删除标识
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     * <AUTHOR>
     * @date 2021-01-14
     **/
    @DataFilter(isSkip = true)
    List<ContainerDetail> findByContainerIdInAndBatchWorkDetailSubWorkSheetIdAndStatusAndDeletedOrderByIdDesc(List<Long> containerIds, Long subWorkSheetId, Integer status, Long deleted);


    /**
     * 根据容器主键ID列表、子工单主键ID以及状态获取容器详情列表
     *
     * @param containerIds   容器主键ID列表
     * @param subWorkSheetId 子工单主键ID
     * @param status         绑定与解绑状态
     * @param deleted        删除标识
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     * <AUTHOR>
     * @date 2021-01-14
     **/
    @DataFilter(isSkip = true)
    List<ContainerDetail> findByBatchWorkDetailSubWorkSheetIdAndContainerIdInAndStatusAndDeletedOrderByIdDesc(Long subWorkSheetId,List<Long> containerIds, Integer status, Long deleted);


    /**
     * 根据容器主键ID列表、子工单主键ID获取容器详情列表
     * @param containerIds 容器主键ID列表
     * @param subWorkSheetId 子工单主键ID
     * @param deleted 删除标识
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     */
    @DataFilter(isSkip = true)
    List<ContainerDetail> findByContainerIdInAndBatchWorkDetailSubWorkSheetIdAndDeletedOrderByIdDesc(List<Long> containerIds, Long subWorkSheetId, Long deleted);



    /**
     * 根据容器主键ID列表、子工单主键ID获取容器详情列表
     * @param containerIds 容器主键ID列表
     * @param subWorkSheetId 子工单主键ID
     * @param deleted 删除标识
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     */
    @DataFilter(isSkip = true)
    List<ContainerDetail> findByBatchWorkDetailSubWorkSheetIdAndContainerIdInAndDeletedOrderByIdDesc(Long subWorkSheetId,List<Long> containerIds, Long deleted);


    /**
     * 根据容器主键ID列表、工单I主键D获取容器详情列表
     * @param containerIds 容器主键ID列表
     * @param workSheetId  工单主键ID
     * @param deleted 删除标识
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     */
    @DataFilter(isSkip = true)
    List<ContainerDetail> findByContainerIdInAndBatchWorkDetailWorkSheetIdAndDeletedOrderByIdDesc(List<Long> containerIds, Long workSheetId, Long deleted);

    /**
     * 根据容器主键ID列表、工单I主键D获取容器详情列表
     * @param containerIds 容器主键ID列表
     * @param workSheetId  工单主键ID
     * @param deleted 删除标识
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     */
    @DataFilter(isSkip = true)
    List<ContainerDetail> findByBatchWorkDetailWorkSheetIdAndContainerIdInAndDeletedOrderByIdDesc(Long workSheetId,List<Long> containerIds, Long deleted);


    /**
     * 根据容器主键ID列表、工单主键ID以及状态获取容器详情列表
     *
     * @param containerIds   容器主键ID列表
     * @param workSheetId 工单主键ID
     * @param status         绑定与解绑状态
     * @param deleted        删除标识
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     **/
    @DataFilter(isSkip = true)
    List<ContainerDetail> findByContainerIdInAndBatchWorkDetailWorkSheetIdAndStatusAndDeletedOrderByIdDesc(List<Long> containerIds, Long workSheetId, Integer status, Long deleted);

    /**
     * 根据容器主键ID列表、工单主键ID以及状态获取容器详情列表
     *
     * @param containerIds   容器主键ID列表
     * @param workSheetId 工单主键ID
     * @param status         绑定与解绑状态
     * @param deleted        删除标识
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     **/
    @DataFilter(isSkip = true)
    List<ContainerDetail> findByBatchWorkDetailWorkSheetIdAndContainerIdInAndStatusAndDeletedOrderByIdDesc(Long workSheetId, List<Long> containerIds,Integer status, Long deleted);

    /**
     * 通过子工单主键ID、容器主键ID列表获取所有以生产容器详情
     * <AUTHOR>
     * @param subWorkSheetId 子工单主键ID
     * @param containerIds 容器主键ID
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     * @date 2021-04-20
     **/
    @DataFilter(isSkip = true)
    List<ContainerDetail> findByBatchWorkDetailSubWorkSheetIdAndContainerIdInAndDeleted(Long subWorkSheetId, List<Long> containerIds,Long deleted);

    /**
     * 通过子工单主键ID、容器主键ID获取最新容器详情数据
     * <AUTHOR>
     * @param subWorkSheetId 子工单主键ID
     * @param containerId 容器主键ID
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     * @date 2021-08-27
     **/
    @DataFilter(isSkip = true)
    ContainerDetail findTop1ByBatchWorkDetailSubWorkSheetIdAndContainerIdAndDeletedOrderByIdDesc(Long subWorkSheetId,Long containerId,Long deleted);


    /**
     * 通过工单主键ID、容器主键ID获取最新容器详情数据
     * <AUTHOR>
     * @param workSheetId 工单主键ID
     * @param containerId 容器主键ID
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     **/
    @DataFilter(isSkip = true)
    ContainerDetail findTop1ByBatchWorkDetailWorkSheetIdAndContainerIdAndDeletedOrderByIdDesc(Long workSheetId,Long containerId,Long deleted);

    /**
     * 通过容器详情I主键D列表解绑所有绑定容器
     *
     * @param containerDetailIdList    容器详情主键ID列表
     * @param unbindTime 解绑时间
     */
    @Modifying
    @Query("update ContainerDetail detail set detail.status=0,detail.unbindTime=?2,detail.transferNumber=0 where detail.id in(?1)")
    void unbindAllContainerByContainerDetailIds(List<Long> containerDetailIdList,LocalDateTime unbindTime);



    /**
     * 根据批量生产详情主键ID获取容器详情
     * <AUTHOR>
     * @param batchDetailId 批量生产详情主键ID
     * @param deleted    逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     * @date 2021-06-13
     **/
    @DataFilter(isSkip = true)
    @EntityGraph(value = "containerDetailEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    List<ContainerDetail> findByBatchWorkDetailIdAndDeleted(Long batchDetailId, Long deleted);


    @EntityGraph(value = "containerDetailEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    @Query("select cd from ContainerDetail  cd where  cd.batchWorkDetail.id=?1 and cd.deleted=?2")
    List<ContainerDetail> findByBatchWorkDetailIdAndDeletedWhenDataFilter(Long batchDetailId, Long deleted);

    /**
     * 通过工序批量生成详情id列表 获取对应的容器详情列表
     * @param batchWorkDetailIds 工序批量详情列表id
     * @param deleted  逻辑删除
     * <AUTHOR>
     * @date  2022/1/25
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     */
    @DataFilter(isSkip = true)
    @Query("from ContainerDetail cd where cd.batchWorkDetail.id in ?1 and cd.status = ?2 and cd.deleted = ?3")
    List<ContainerDetail> findByBatchWorkDetailIdInAndStatusDeleted(List<Long> batchWorkDetailIds,Integer status,Long deleted);

    /**
     * 通过容器号列表与容器绑定状态 获取对应的容器详情列表
     * @param containerCodes 容器编码列表
     * @param status 容器状态
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/1/25
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     */
    @DataFilter(isSkip = true)
    List<ContainerDetail> findByContainerCodeInAndStatusAndDeleted(List<String> containerCodes,Integer status ,Long deleted);

    /**
     * 通过容器号列表与容器绑定状态 获取对应的容器详情列表
     * @param containerIds 容器id列表
     * @param status 容器状态
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/1/25
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     */
    @DataFilter(isSkip = true)
    @Query("from ContainerDetail cd where cd.container.id in ?1 and cd.status = ?2 and cd.deleted = ?3")
    List<ContainerDetail> findByContainerIdInAndStatusAndDeleted(List<Long> containerIds,Integer status,Long deleted);

    /**
     * 通过容器编码获取绑定状态的容器
     * @param containerCode 容器编码
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     */
    @DataFilter(isSkip = true)
    @EntityGraph(value = "containerDetailEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    ContainerDetail findTop1ByContainerCodeAndStatusAndDeleted(String containerCode,int status,Long deleted);


    /**
     * 通过容器主键ID获取绑定状态的容器
     * @param containerId 容器主键ID
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     */
    @DataFilter(isSkip = true)
    @Query("from ContainerDetail cd where cd.container.id = ?1 and cd.status = 1 and cd.deleted = ?2")
    ContainerDetail findBindingContainerDetailByContainerIdAndDeleted(Long containerId,Long deleted);

    /**
     * 根据容器详情主键ID列表获取数据
     * <AUTHOR>
     * @param idList 主键id列表
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     * @date 2021-06-13
     **/
    @DataFilter(isSkip = true)
    List<ContainerDetail> findByIdInAndDeleted(List<Long> idList,Long deleted);

    /**
     * 通过批量详情主键ID删除容器详情
     * <AUTHOR>
     * @param batchWorkDetailId     批量详情ID
     * @return void
     * @date 2021-06-13
     **/
    @Modifying
    @Query("update ContainerDetail cd set cd.deleted=cd.id where cd.batchWorkDetail.id=?1")
    void batchDeleteByBatchWorkDetailId(Long batchWorkDetailId);

    /**
     * 通过请求容器列表获取到最新的 容器详情
     * @param containerCodeList 请求容器列表
     * @param status 容器状态
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2021/9/8
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     */
    @DataFilter(isSkip = true)
    List<ContainerDetail> findTop1ByContainerCodeInAndStatusAndDeletedOrderByIdDesc(List<String> containerCodeList,Integer status, Long deleted);

    /**
     * 获取子工单所有投产容器
     *
     * @param subWorkSheetIds
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     */
    @DataFilter(isSkip = true)
    @Query("from ContainerDetail where deleted = 0L and status = 1 and batchWorkDetail.subWorkSheet.id in ?1")
    List<ContainerDetail> findBySubWorkSheetIdIn(List<Long> subWorkSheetIds);

    /**
     * 获取子工单对应的容器详情列表
     * @param subWorkSheetId  子工单主键id
     * <AUTHOR>
     * @date  2022/2/8
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     */
    @DataFilter(isSkip = true)
    @Query("from ContainerDetail where deleted = 0L  and batchWorkDetail.subWorkSheet.id = ?1")
    List<ContainerDetail> findBySubWorkSheetId(Long subWorkSheetId);

    /**
     * 获取工单对应的容器详情列表
     * @param workSheetId 工单ID
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     */
    @DataFilter(isSkip = true)
    @Query("from ContainerDetail where deleted = 0L  and batchWorkDetail.workSheet.id = ?1")
    List<ContainerDetail> findByWorkSheetId(Long workSheetId);

    /**
     * 通过子工单、工序获取绑定时间最早的一条容器详情
     *
     * @param subWorkSheetId 子工单主键ID
     * @param stepId 工序主键ID
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情
     */
    @DataFilter(isSkip = true)
    Optional<ContainerDetail> findTop1ByBatchWorkDetailSubWorkSheetIdAndBatchWorkDetailStepIdAndDeletedOrderByBindTime(Long subWorkSheetId, Long stepId, Long deleted);

    /**
     * 通过子工单ID、绑定状态获取容器详情列表
     *
     * @param subWorkSheetId 子工单
     * @param status 绑定状态(0:解绑;1:绑定)
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/4/18
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     */
    @DataFilter(isSkip = true)
    List<ContainerDetail> findByBatchWorkDetailSubWorkSheetIdAndStatusAndDeleted(Long subWorkSheetId, int status, Long deleted);

    /**
     * 通过工单主键ID、绑定状态获取容器详情列表
     *
     * @param workSheetId 工单主键Id
     * @param status 绑定状态(0:解绑;1:绑定)
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     */
    @DataFilter(isSkip = true)
    List<ContainerDetail> findByBatchWorkDetailWorkSheetIdAndStatusAndDeleted(Long workSheetId, int status, Long deleted);

    /**
     * 通过子工单的ids、状态来修改容器详情的状态
     *
     * @param status 绑定状态(0:解绑;1:绑定)
     * @param ids 子工单主键集合
     * @param deleted 逻辑删除
     */
    @Modifying(clearAutomatically = true)
    @Query("update ContainerDetail set status = ?1 where id in ?2 and deleted = ?3")
    void updateStatus(int status, List<Long> ids, Long deleted);

    /**
     * 通过主键ID和删除标识查询容器工序详情
     *
     * @param id     主键 ID
     * @param deleted 删除标识
     * @return net.airuima.domain.procedure.batch.ContainerDetail 容器生产详情
     * <AUTHOR>
     * @date 2022/8/1
     **/
    @DataFilter(isSkip = true)
    ContainerDetail findByIdAndDeleted(Long id, Long deleted);

    /**
     * 根据容器主键id以及容器详情容器状态 获取最新一条容器详情记录
     * @param containerId 容器主键id
     * @param status 容器绑定状态：绑定
     * @param maintainStatus 容器生产状态：待维修
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/10/9
     * @return java.util.Optional<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情
     */
    @DataFilter(isSkip = true)
    Optional<ContainerDetail> findTop1ByContainerIdAndStatusAndMaintainStatusAndDeletedOrderByIdDesc(Long containerId,int status,int maintainStatus,Long deleted);

    /**
     * 根据容器编码列表以及绑定状态和维修状态获取容器详情列表
     * @param containerCodes 容器编码列表
     * @param status 绑定状态
     * @param maintainStatus 维修分析状态
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/10/11
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     */
    @DataFilter(isSkip = true)
    List<ContainerDetail> findByContainerCodeInAndStatusAndMaintainStatusAndDeleted(List<String> containerCodes,int status,int maintainStatus,Long deleted);

    /**
     * 通过容器主键id 获取最新一条容器详情记录
     * @param containerId 容器主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/11/28
     * @return java.util.Optional<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情
     */
    @DataFilter(isSkip = true)
    Optional<ContainerDetail> findTop1ByContainerIdAndDeletedOrderByIdDesc(Long containerId,Long deleted);

    /**
     * 通过子工单流水号容器编码获取容器详情
     *
     * @param subWorkSheetSerialNumber 子工单流水号
     * @param containerCode 容器编码
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<ContainerDetail> findByBatchWorkDetailSubWorkSheetSerialNumberAndContainerCodeAndDeleted(String subWorkSheetSerialNumber, String containerCode, Long deleted);


    /**
     * 通过子工单和容器主键ID和工序类型获取最新的一条容器详情
     * @param subWorkSheetId 子工单主键ID
     * @param containerIds 容器主键ID
     * @param Category  工序类型
     * @param deleted  逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情
     */
    @DataFilter(isSkip = true)
    Optional<ContainerDetail> findTop1ByBatchWorkDetailSubWorkSheetIdAndContainerIdInAndBatchWorkDetailStepCategoryAndDeletedOrderByIdDesc(Long subWorkSheetId,List<Long> containerIds,int Category,Long deleted);

    /**
     * 通过批量详情主键ID集合查询所有容器详情
     *
     * @param batchWorkDetailIdList
     * @param deleted
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     * <AUTHOR>
     * @date 2023/4/21
     **/
    @DataFilter(isSkip = true)
    List<ContainerDetail> findByBatchWorkDetailIdInAndDeleted(List<Long> batchWorkDetailIdList, Long deleted);

    /**
     * 通过工单+工序集合查询容器详情
     *
     * @param workSheetId            工单主键ID列表
     * @param stepIdList             工序主键ID集合
     * @param batchWorkDetailDeleted 批量详情删除标识
     * @param containerDetailDeleted 容器详情删除标识
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     * <AUTHOR>
     * @date 2023/4/21
     **/
    @DataFilter(isSkip = true)
    List<ContainerDetail> findByBatchWorkDetailWorkSheetIdAndBatchWorkDetailStepIdInAndBatchWorkDetailDeletedAndDeleted(Long workSheetId, List<Long> stepIdList, Long batchWorkDetailDeleted, Long containerDetailDeleted);

    /**
     * 通过工单主键ID+容器主键ID集合+绑定状态+删除标识查询容器详情
     *
     * @param workSheetId 工单主键主键ID
     * @param containerIdList 容器主键ID列表
     * @param status 状态
     * @param deleted 删除标识
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     * <AUTHOR>
     * @date 2023/4/23
     **/
    @DataFilter(isSkip = true)
    List<ContainerDetail> findByBatchWorkDetailWorkSheetIdAndContainerIdInAndStatusAndDeleted(Long workSheetId, List<Long> containerIdList, Integer status, Long deleted);

    /**
     * 通过工单ID + 容器ID集合 + 容器状态 + 删除标识 查询容器详情
     *
     * @param workSheetId 工单主键ID
     * @param containerIdList 容器ID列表
     * @param status 状态
     * @param batchWorkDetailDeleted 批量详情删除标识
     * @param deleted 删除标识
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     * <AUTHOR>
     * @date 2023/4/25
     **/
    @DataFilter(isSkip = true)
    List<ContainerDetail> findByBatchWorkDetailWorkSheetIdAndContainerIdInAndStatusAndBatchWorkDetailDeletedAndDeleted(Long workSheetId, List<Long> containerIdList, Integer status, Long batchWorkDetailDeleted, Long deleted);

    /**
     * 通过工单ID + 工序ID集合 + 状态 + 删除标识查询容器详情集合
     *
     * @param workSheetId 工单主键ID
     * @param stepIdIn 工序主键id列表
     * @param status 状态
     * @param batchWorkDetailDeleted 批量详情删除标识
     * @param deleted                删除标识
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     * <AUTHOR>
     * @date 2023/4/25
     **/
    @DataFilter(isSkip = true)
    List<ContainerDetail> findByBatchWorkDetailWorkSheetIdAndBatchWorkDetailStepIdInAndStatusAndBatchWorkDetailDeletedAndDeleted(Long workSheetId, List<Long> stepIdIn, Integer status, Long batchWorkDetailDeleted, Long deleted);

    /**
     * 通过工单ID+工序ID+容器ID+删除标识查询容器详情
     *
     * @param workSheetId            工单主键ID
     * @param stepId                 工序主键ID
     * @param containerId            容器主键ID
     * @param batchWorkDetailDeleted 批量详情删除标识
     * @param deleted                删除标识
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     * <AUTHOR>
     * @date 2023/4/28
     **/
    @DataFilter(isSkip = true)
    List<ContainerDetail> findByBatchWorkDetailWorkSheetIdAndBatchWorkDetailStepIdAndContainerIdInAndBatchWorkDetailDeletedAndDeleted(Long workSheetId, Long stepId, List<Long> containerId, Long batchWorkDetailDeleted, Long deleted);

    /**
     * 通过工单主键ID + 工序主键ID + 容器CODE集合 + 删除标识 查询容器详情集合
     *
     * @param workSheetId            工单主键ID
     * @param stepIdList             工序主键ID
     * @param containerCodeList      容器CODE集合
     * @param batchWorkDetailDeleted 批量删除标识
     * @param deleted                删除标识
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     * <AUTHOR>
     * @date 2023/5/4
     **/
    @DataFilter(isSkip = true)
    List<ContainerDetail> findByBatchWorkDetailWorkSheetIdAndBatchWorkDetailStepIdInAndContainerCodeInAndBatchWorkDetailDeletedAndDeleted(Long workSheetId, List<Long> stepIdList, List<String> containerCodeList, Long batchWorkDetailDeleted, Long deleted);

    /**
     * 通过子工单主键id 工序主键id 容器号获取 容器详情唯一一条记录
     * @param subWorksheetId 子工单主键id
     * @param stepId 工序主键id
     * @param containerCode 容器号
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2023/5/6
     * @return java.util.Optional<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情
     */
    @DataFilter(isSkip = true)
    Optional<ContainerDetail> findByBatchWorkDetailSubWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(Long subWorksheetId,Long stepId,String containerCode,Long deleted);

    /**
     * 通过工单主键id 工序主键id 容器号获取 容器详情唯一一条记录
     * @param workSheetId 工单主键id
     * @param stepId 工序id
     * @param containerCode 容器号
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2023/5/6
     * @return java.util.Optional<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情
     */
    @DataFilter(isSkip = true)
    Optional<ContainerDetail> findByBatchWorkDetailWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(Long workSheetId,Long stepId,String containerCode,Long deleted);

    /**
     * 通过容器CODE+删除标识查询对应的工单集合
     *
     * @param ContainerCode 容器编码
     * @param deleted       删除标识
     * @return : java.util.List<net.airuima.domain.procedure.aps.WorkSheet> 工单列表
     * <AUTHOR>
     * @date 2023/6/12
     **/
    @EntityGraph(value = "containerDetailEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    @Query("select distinct c.batchWorkDetail.workSheet from ContainerDetail c where c.containerCode = ?1 and c.deleted = ?2")
    List<WorkSheet> findWorkSheetByContainerCodeAndDeleted(String ContainerCode, Long deleted);

    /**
     * 通过容器CODE+删除标识 查询对应子工单集合
     *
     * @param ContainerCode 容器编码
     * @param deleted       删除标识
     * @return : java.util.List<net.airuima.domain.procedure.aps.SubWorkSheet> 子工单列表
     * <AUTHOR>
     * @date 2023/6/12
     **/
    @EntityGraph(value = "containerDetailEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    @Query("select distinct c.batchWorkDetail.subWorkSheet from ContainerDetail c where c.containerCode = ?1 and c.deleted = ?2")
    List<SubWorkSheet> findSubWorkSheetByContainerCodeAndDeleted(String ContainerCode, Long deleted);


    /**
     * 通过子工单 工序 获取当前工序中的容器详情
     * @param subWorksheetId 子工单主键id
     * @param stepId 工序主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2023/6/13
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     */
    @DataFilter(isSkip = true)
    List<ContainerDetail> findByBatchWorkDetailSubWorkSheetIdAndBatchWorkDetailStepIdAndDeleted(Long subWorksheetId,Long stepId,Long deleted);


    /**
     * 通过子工单 工序集合 获取容器详情
     * @param subWorksheetId 子工单主键id
     * @param stepIdList 工序集合
     * @param deleted 删除标记
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器详情集合
     */
    @DataFilter(isSkip = true)
    @Query("""
            select c from ContainerDetail c
            where c.batchWorkDetail.subWorkSheet.id = ?1 and c.batchWorkDetail.step.id in ?2 and c.container.id = ?3 and c.deleted = ?4""")
    List<ContainerDetail> findByBatchWorkDetailSubWorkSheetIdAndBatchWorkDetailStepIdInAndContainerIdAndDeleted(Long subWorksheetId,List<Long> stepIdList, Long containerId,Long deleted);



    /**
     * 通过工单 工序集合 获取容器详情
     * @param worksheetId 工单主键id
     * @param stepIdList 工序集合
     * @param deleted 删除标记
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器详情集合
     */
    @DataFilter(isSkip = true)
    @Query("""
            select c from ContainerDetail c
            where c.batchWorkDetail.workSheet.id = ?1 and c.batchWorkDetail.step.id in ?2 and c.container.id = ?3 and c.deleted = ?4""")
    List<ContainerDetail> findByBatchWorkDetailWorkSheetIdAndBatchWorkDetailStepIdInAndContainerIdAndDeleted(Long worksheetId, List<Long> stepIdList, Long containerId,Long deleted);

    /**
     * 通过容器编码获取在一定范围内使用该容器得工单
     *
     * @param containerCode 容器编码
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器详情集合
     */
    @DataFilter(isSkip = true)
    @Query("select c from ContainerDetail c where c.container.code = ?1 and c.recordDate >= ?2 and c.recordDate <= ?3 and c.deleted = 0L")
    List<ContainerDetail> findByContainerCodeAndRecordDate(String containerCode, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 通过容器主键ID获取绑定状态的容器
     *
     * @param containerId 容器主键ID
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情列表
     */
    @DataFilter(isSkip = true)
    List<ContainerDetail> findByContainerIdAndDeleted(Long containerId,Long deleted);
}
