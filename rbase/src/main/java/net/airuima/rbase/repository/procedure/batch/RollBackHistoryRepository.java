package net.airuima.rbase.repository.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.procedure.batch.RollBackHistory;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/2/9
 */
@Repository
public interface RollBackHistoryRepository extends LogicDeleteableRepository<RollBackHistory>,
        EntityGraphJpaSpecificationExecutor<RollBackHistory>, EntityGraphJpaRepository<RollBackHistory, Long> {

    /**
     * 通过 子工单id、工序id、逻辑删除 获取回退历史信息
     *
     * @param subWorkSheetId 子工单id
     * @param stepId         工序id
     * @param deleted        逻辑删除
     * @return List<RollBackHistory>
     * <AUTHOR>
     * @since 1.8.0
     */
    List<RollBackHistory> findBySubWorkSheetIdAndStepIdAndDeleted(Long subWorkSheetId, Long stepId, Long deleted);

    /**
     * 通过 子工单id、工序id、逻辑删除 获取回退历史信息
     *
     * @param subWorkSheetId 子工单id
     * @param stepIds         工序id
     * @param deleted        逻辑删除
     * @return List<RollBackHistory>
     * <AUTHOR>
     * @since 1.8.0
     */
    List<RollBackHistory> findBySubWorkSheetIdAndStepIdInAndDeleted(Long subWorkSheetId, List<Long> stepIds, Long deleted);

    /**
     * 通过 工单id、工序id、逻辑删除 获取回退历史信息
     *
     * @param workSheetId 工单id
     * @param stepId      工序id
     * @param deleted     逻辑删除
     * @return List<RollBackHistory>
     * <AUTHOR>
     * @since 1.8.0
     */
    List<RollBackHistory> findByWorkSheetIdAndStepIdAndDeleted(Long workSheetId, Long stepId, Long deleted);

    /**
     * 通过 工单id、工序id、逻辑删除 获取回退历史信息
     *
     * @param workSheetId 工单id
     * @param stepIds      工序id
     * @param deleted     逻辑删除
     * @return List<RollBackHistory>
     * <AUTHOR>
     * @since 1.8.0
     */
    List<RollBackHistory> findByWorkSheetIdAndStepIdInAndDeleted(Long workSheetId, List<Long> stepIds, Long deleted);
}
