package net.airuima.rbase.repository.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetailFacility;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetailMaterialBatch;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 批量生产详情设备Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface BatchWorkDetailFacilityRepository extends LogicDeleteableRepository<BatchWorkDetailFacility>,
        EntityGraphJpaSpecificationExecutor<BatchWorkDetailFacility>, EntityGraphJpaRepository<BatchWorkDetailFacility, Long> {

    /**
     * 通过批量详情主键ID、设备主键ID获取唯一记录
     *
     * @param batchWorkDetailId 批量详情I主键D
     * @param facilityId       设备主键ID
     * @param deleted           逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.batch.BatchWorkDetailFacility> 批量生产详情设备
     * <AUTHOR>
     * @date 2021-01-19
     **/
    @DataFilter(isSkip = true)
    Optional<BatchWorkDetailFacility> findByBatchWorkDetailIdAndFacilityIdAndDeleted(Long batchWorkDetailId, Long facilityId, Long deleted);

    /**
     * 通过生产详情主键id数组查询设备记录
     *
     * @param idList  生产详情主键id数组
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.batch.BatchWorkDetailFacility> 批量生产详情设备列表
     */
    @DataFilter(isSkip = true)
    List<BatchWorkDetailFacility> findByBatchWorkDetailIdInAndDeleted(List<Long> idList, Long deleted);

    /**
     * 通过生产详情主键id查询设备记录
     *
     * @param id  生产详情主键id
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.batch.BatchWorkDetailFacility> 批量生产详情设备列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<BatchWorkDetailFacility> findByBatchWorkDetailIdAndDeleted(Long id, Long deleted);

    /**
     * 通过设备主键Id及日期获取详情数据 分页
     * <AUTHOR>
     * @param equipmentId 设备主键ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param deleted  逻辑删除
     * @param pageable  分页
     * @return java.util.List<net.airuima.rbase.domain.procedure.batch.BatchWorkDetailFacility> 批量生产详情设备列表
     * @date 2021-03-24
     **/
    @EntityGraph(value = "batchWorkDetailEquipmentEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    @Query("select bd  from BatchWorkDetailFacility bd where  bd.batchWorkDetail.subWorkSheet is not null and bd.facilityId = ?1 and (bd.createdDate >= ?2 and bd.createdDate <= ?3) and bd.deleted = ?4 and bd.batchWorkDetail.deleted = 0")
    Page<BatchWorkDetailFacility> findBySubWorkSheetAndFacilityIdAndCreatedDateGreaterThanEqualAndCreatedDateLessThanEqualAndDeleted(Long equipmentId, Instant startDate, Instant endDate, Long deleted, Pageable pageable);

    /**
     * 通过设备主键Id及日期获取详情数据 分页
     * <AUTHOR>
     * @param equipmentId 设备主键ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param deleted  逻辑删除
     * @param pageable  分页
     * @return java.util.List<net.airuima.rbase.domain.procedure.batch.BatchWorkDetailFacility> 批量生产详情设备列表
     * @date 2021-03-24
     **/
    @EntityGraph(value = "batchWorkDetailEquipmentEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    @Query("select bd  from BatchWorkDetailFacility bd where bd.batchWorkDetail.workSheet is not null and bd.facilityId = ?1 and (bd.createdDate >= ?2 and bd.createdDate <= ?3) and bd.deleted = ?4 and bd.batchWorkDetail.deleted = 0")
    Page<BatchWorkDetailFacility> findByWorkSheetAndFacilityIdAndCreatedDateGreaterThanEqualAndCreatedDateLessThanEqualAndDeleted(Long equipmentId, Instant startDate, Instant endDate, Long deleted, Pageable pageable);

    /**
     * 通过批量工作详情主键ID删除设备信息
     * <AUTHOR>
     * @param batchWorkDetailId     批量工作详情主键ID
     * @date 2021-06-13
     **/
    @Modifying
    @Query("update BatchWorkDetailFacility bamb set bamb.deleted=bamb.id where bamb.batchWorkDetail.id=?1 and bamb.deleted=0L")
    void batchDeleteByBatchWorkDetailId(Long batchWorkDetailId);


    /**
     *
     * 通过批量工作详情主键Id及设备主键ID删除设备信息
     * @param batchWorkDetailId 批量工作详情主键ID
     * @param facilityId 设备主键ID
     * @return void
     */
    @Modifying
    @Query("update BatchWorkDetailFacility bamb set bamb.deleted=bamb.id where bamb.batchWorkDetail.id=?1 and bamb.facilityId=?2 and bamb.deleted=0L")
    void batchDeleteByBatchWorkDetailIdAndFacilityId(Long batchWorkDetailId,Long facilityId);


    /**
     * 通过设备id 以及创建时间获取当前时间内总数
     * @param facilityId 设备id
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param deleted 逻辑删除
     * @return java.lang.Long 数量
     */
    @EntityGraph(value = "batchWorkDetailEquipmentEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select count(id) from BatchWorkDetailFacility where batchWorkDetail.subWorkSheet is not null and facilityId = ?1 and (createdDate >= ?2 and createdDate <= ?3) and deleted = ?4 and batchWorkDetail.deleted = 0")
    Long countBySubWorkSheetAndFacilityIdAndCreatedDateAndDeleted(Long facilityId, Instant startDate, Instant endDate, Long deleted);

    /**
     * 通过设备id 以及创建时间获取当前时间内总数
     * @param facilityId 设备id
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param deleted 逻辑删除
     * @return java.lang.Long 数量
     */
    @DataFilter(isSkip = true)
    @Query("select count(id) from BatchWorkDetailFacility where batchWorkDetail.workSheet is not null and facilityId = ?1 and (createdDate >= ?2 and createdDate <= ?3) and deleted = ?4 and batchWorkDetail.deleted = 0")
    Long countByWorkSheetAndFacilityIdAndCreatedDateAndDeleted(Long facilityId, Instant startDate, Instant endDate, Long deleted);

    /**
     * 通过设备id 以及创建时间获取当前时间设备信息
     * @param facilityId 设备id
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.batch.BatchWorkDetailFacility> 批量生产详情设备列表
     */
    @EntityGraph(value = "batchWorkDetailEquipmentEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    @Query("select bwdf from BatchWorkDetailFacility bwdf where bwdf.facilityId = ?1 and (bwdf.createdDate >= ?2 and bwdf.createdDate <= ?3) and bwdf.deleted = ?4 and bwdf.batchWorkDetail.deleted = 0 and bwdf.batchWorkDetail.subWorkSheet is not null")
    List<BatchWorkDetailFacility> findBySubWorkSheetAndFacilityIdAndCreatedDateBetween(Long facilityId, Instant startDate, Instant endDate, Long deleted);

    /**
     * 通过设备id 以及创建时间获取当前时间设备信息
     * @param facilityId 设备id
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param deleted 逻辑删除
     ** @return java.util.List<net.airuima.rbase.domain.procedure.batch.BatchWorkDetailFacility> 批量生产详情设备列表
     */
    @EntityGraph(value = "batchWorkDetailEquipmentEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    @Query("select bwdf from BatchWorkDetailFacility bwdf where bwdf.facilityId = ?1 and (bwdf.createdDate >= ?2 and bwdf.createdDate <= ?3) and bwdf.deleted = ?4 and bwdf.batchWorkDetail.deleted = 0 and bwdf.batchWorkDetail.workSheet is not null")
    List<BatchWorkDetailFacility> findByWorkSheetAndFacilityIdAndCreatedDateBetween(Long facilityId, Instant startDate, Instant endDate, Long deleted);

    /**
     * 通过设备id获取在一定范围内使用该设备的工单
     *
     * @param facilityId 设备id
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkDetail> 批量生产详情设备列表
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    @Query("select b from BatchWorkDetailFacility b where b.facilityId = ?1 and b.batchWorkDetail.startDate >= ?2 and b.batchWorkDetail.endDate <= ?3 and b.deleted = 0L")
    List<BatchWorkDetailFacility> findByFacilityIdAndStartDateAndEndDate(Long facilityId, LocalDateTime startDate, LocalDateTime endDate);
}
