package net.airuima.rbase.repository.base.quality;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.base.quality.WorkCellCheckStartRule;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 工位检测配置Repository
 * <AUTHOR>
 * @date 2021-03-22
 */
@Repository
public interface WorkCellCheckStartRuleRepository extends LogicDeleteableRepository<WorkCellCheckStartRule>,
        EntityGraphJpaSpecificationExecutor<WorkCellCheckStartRule>, EntityGraphJpaRepository<WorkCellCheckStartRule, Long> {

    /**
     * 通过工位主键ID，配置类型获取工位检测时机
     *
     * @param workCellId 工位主键ID
     * @param category   配置类型
     * @param enable 是否启用
     * @param deleted    逻辑删除
     * @return java.util.List<net.airuima.domain.base.quality.WorkCellCheckStartRule> 工位检测配置列表
     * <AUTHOR>
     * @date 2021-03-23
     **/
    @FetchMethod
    @DataFilter(isSkip = true)
    List<WorkCellCheckStartRule> findByWorkCellIdAndCategoryAndIsEnableAndDeleted(Long workCellId, Integer category,Boolean enable, Long deleted);

    /**
     * 通过工位主键ID，配置类型、时机获取工位检测时机
     *
     * @param workCellId 工位主键ID
     * @param category   配置类型
     * @param flag       时机
     * @param deleted    逻辑删除
     * @return java.util.Optional<net.airuima.domain.base.quality.WorkCellCheckStartRule> 工位检测配置
     * <AUTHOR>
     * @date 2021-03-23
     **/
    @FetchMethod
    @DataFilter(isSkip = true)
    Optional<WorkCellCheckStartRule> findByWorkCellIdAndCategoryAndFlagAndIsEnableAndDeleted(Long workCellId, Integer category, Integer flag, Boolean enable, Long deleted);

    /**
     * 通过工位主键D，配置类型、时机获取工位检测时机
     *
     * @param workCellId 工位主键ID
     * @param category   配置类型
     * @param flag       时机
     * @param variety    项目类型
     * @param deleted    逻辑删除
     * @return java.util.Optional<net.airuima.domain.base.quality.WorkCellCheckStartRule> 工位检测配置
     * <AUTHOR>
     **/
    @FetchMethod
    @DataFilter(isSkip = true)
    Optional<WorkCellCheckStartRule> findByWorkCellIdAndCategoryAndFlagAndVarietyAndIsEnableAndDeleted(Long workCellId, Integer category, Integer flag, Integer variety, Boolean enable, Long deleted);

    /**
     * 通过工位主键ID，配置类型、时机获取工位检测时机 （请求下交保存）
     *
     * @param workCellId 工位主键ID
     * @param category   配置类型
     * @param flag       时机
     * @param varietyId  项目类型
     * @param deleted    逻辑删除
     * @return java.util.Optional<net.airuima.domain.base.quality.WorkCellCheckStartRule> 工位检测配置
     * <AUTHOR>
     **/
    @FetchMethod
    @DataFilter(isSkip = true)
    Optional<WorkCellCheckStartRule> findByWorkCellIdAndCategoryAndFlagAndVarietyIdAndDeleted(Long workCellId, Integer category, Integer flag, Long varietyId, Long deleted);

    /**
     * 通过工位主键ID，配置类型、时机获取工位检测时机 （请求获取规则 isEnable）
     *
     * @param workCellId 工位主键ID
     * @param category   配置类型
     * @param flag       时机
     * @param varietyId  项目类型
     * @param enable  是否启用
     * @param deleted    逻辑删除
     * @return java.util.Optional<net.airuima.domain.base.quality.WorkCellCheckStartRule> 工位检测配置
     * <AUTHOR>
     **/
    @FetchMethod
    @DataFilter(isSkip = true)
    Optional<WorkCellCheckStartRule> findByWorkCellIdAndCategoryAndFlagAndVarietyIdAndIsEnableAndDeleted(Long workCellId, Integer category, Integer flag, Long varietyId, Boolean enable, Long deleted);


    /**
     * 获取抽检终检检测规则
     * @param workFlowId 工艺路线主键id
     * @param stepId 工序主键id
     * @param isEnable 是否启用
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @Date  2023/5/9
     * @return java.util.List<net.airuima.domain.base.quality.WorkCellCheckStartRule> 工位检测配置列表
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    List<WorkCellCheckStartRule> findByWorkFlowIdAndStepIdAndIsEnableAndDeleted(Long workFlowId, Long stepId,Boolean isEnable,Long deleted);

    /**
     * 项目类型主键id、检测类型、工序主键id、工艺路线主键id、删除标识查询工位检测配置
     * @param workFlowId    工艺路线主键id
     * @param stepId        工序主键id
     * @param varietyId     项目类型主键id
     * @param category      检测类型
     * @param deleted       删除标识
     * @return java.util.Optional<net.airuima.domain.base.quality.WorkCellCheckStartRule>工位检测配置
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    Optional<WorkCellCheckStartRule> findByWorkFlowIdAndStepIdAndVarietyIdAndCategoryAndDeleted(Long workFlowId, Long stepId, Long varietyId, Integer category, Long deleted);

    /**
     * 查找来料检发起规则
     * @param category 类型
     * @param target 目标值
     * @param attributeId  物料属性id
     * @param materialId 物料id
     * @param supplierId 供应商id
     * @param clientId 客户id
     * @param deleted 删除标识
     * @return java.util.Optional<net.airuima.domain.base.quality.WorkCellCheckStartRule> 检测配置
     */
    @FetchMethod
    @Query("""
            select w from WorkCellCheckStartRule w
            where w.category = ?1 and w.target = ?2 and  ((coalesce(?3, null) is null and w.attributeId is null) or w.attributeId = ?3) and ((coalesce(?4, null) is null and w.materialId is null) or w.materialId = ?4) and  ((coalesce(?5, null) is null and w.supplierId is null) or w.supplierId = ?5)  and  ((coalesce(?6, null) is null and w.clientId is null) or w.clientId = ?6) and w.deleted = ?7""")
    @DataFilter(isSkip = true)
    Optional<WorkCellCheckStartRule> findByCategoryAndTargetAndAttributeIdAndMaterialIdAndSupplierIdAndClientIdAndDeleted(int category, int target, Long attributeId, Long materialId, Long supplierId, Long clientId, Long deleted);


}
