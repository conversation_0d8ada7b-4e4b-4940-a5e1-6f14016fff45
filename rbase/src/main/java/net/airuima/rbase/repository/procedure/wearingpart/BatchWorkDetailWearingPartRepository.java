package net.airuima.rbase.repository.procedure.wearingpart;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetailFacility;
import net.airuima.rbase.domain.procedure.wearingpart.BatchWorkDetailWearingPart;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/6/23
 */
@Repository
public interface BatchWorkDetailWearingPartRepository extends LogicDeleteableRepository<BatchWorkDetailWearingPart>,
        EntityGraphJpaSpecificationExecutor<BatchWorkDetailWearingPart>, EntityGraphJpaRepository<BatchWorkDetailWearingPart, Long> {

    /**
     * 通过批量详情主键ID、易损件主键ID获取唯一记录
     *
     * @param batchWorkDetailId 批量详情主键ID
     * @param wearingPartId 易损件主键Id
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.wearingpart.BatchWorkDetailWearingPart> 批量生产详情易损件
     * <AUTHOR>
     */
    @DataFilter(isSkip = true)
    Optional<BatchWorkDetailWearingPart> findByBatchWorkDetailIdAndWearingPartIdAndDeleted(Long batchWorkDetailId, Long wearingPartId, Long deleted);

    /**
     *
     * 通过批量详情主键ID删除详情易损件
     * @param batchWorkDetailId 批量详情主键ID
     * @return void
     */
    @Modifying
    @Query("update BatchWorkDetailWearingPart bamb set bamb.deleted=bamb.id where bamb.batchWorkDetail.id=?1 and bamb.deleted=0L")
    void batchDeleteByBatchWorkDetailId(Long batchWorkDetailId);

    /**
     *
     * 通过批量详情主键ID及易损件主键ID删除详情易损件
     * @param batchWorkDetailId 批量详情主键ID
     * @param wearingPartId  易损件主键ID
     * @return void
     */
    @Modifying
    @Query("update BatchWorkDetailWearingPart bamb set bamb.deleted=bamb.id where bamb.batchWorkDetail.id=?1 and bamb.wearingPart.id=?2 and bamb.deleted=0L")
    void batchDeleteByBatchWorkDetailIdAndWearingPartId(Long batchWorkDetailId,Long wearingPartId);

    /**
     *  易损件主键id 获取批量生产列表 分页获取，在开始和结束得范围区间内
     * @param wearingPartId 易损件主键id
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param deleted 逻辑删除
     * @param pageable 分页
     * <AUTHOR>
     * @date  2021/8/26
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.procedure.wearingpart.BatchWorkDetailWearingPart> 批量生产详情易损件分页
     */
    @EntityGraph(value = "batchWorkDetailWearingPartEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    @Query("select bd  from BatchWorkDetailWearingPart bd where bd.batchWorkDetail.subWorkSheet is not null and bd.wearingPart.id = ?1 and (bd.createdDate >= ?2 and bd.createdDate <= ?3) and bd.deleted = ?4 and bd.batchWorkDetail.deleted = 0")
    Page<BatchWorkDetailWearingPart> findBySubWorkSheetAndWearingPartIdAndCreatedDateGreaterThanEqualAndCreatedDateLessThanEqualAndDeleted(Long wearingPartId, Instant startDate, Instant endDate,Long deleted,Pageable pageable);

    /**
     *  易损件主键id 获取批量生产列表 分页获取，在开始和结束得范围区间内
     * @param wearingPartId 易损件主键id
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param deleted 逻辑删除
     * @param pageable 分页
     * <AUTHOR>
     * @date  2021/8/26
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.procedure.wearingpart.BatchWorkDetailWearingPart> 批量生产详情易损件分页
     */
    @EntityGraph(value = "batchWorkDetailWearingPartEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    @Query("select bd  from BatchWorkDetailWearingPart bd where bd.batchWorkDetail.workSheet is not null and bd.wearingPart.id = ?1 and (bd.createdDate >= ?2 and bd.createdDate <= ?3) and bd.deleted = ?4 and bd.batchWorkDetail.deleted = 0")
    Page<BatchWorkDetailWearingPart> findByWorkSheetAndWearingPartIdAndCreatedDateGreaterThanEqualAndCreatedDateLessThanEqualAndDeleted(Long wearingPartId, Instant startDate, Instant endDate,Long deleted,Pageable pageable);


    /**
     * 根据批量详情主键ID+删除标识查询批量详情易损件集合
     *
     * @param batchWorkDetailId 批量详情主键ID
     * @param deleted           删除标识
     * @return java.util.List<net.airuima.rbase.domain.procedure.wearingpart.BatchWorkDetailWearingPart> 批量生产详情易损件列表
     * <AUTHOR>
     * @date 2023/6/9
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<BatchWorkDetailWearingPart> findByBatchWorkDetailIdAndDeleted(Long batchWorkDetailId, Long deleted);

    /**
     * 根据批量详情主键ID+删除标识查询批量详情易损件集合
     *
     * @param batchWorkDetailIds 批量详情主键ID
     * @param deleted           删除标识
     * @return java.util.List<net.airuima.rbase.domain.procedure.wearingpart.BatchWorkDetailWearingPart> 批量生产详情易损件列表
     * <AUTHOR>
     * @date 2023/6/9
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<BatchWorkDetailWearingPart> findByBatchWorkDetailIdInAndDeleted(List<Long> batchWorkDetailIds, Long deleted);


    /**
     * 通过易损件主键id 以及创建时间获取当前时间内总数
     * @param wearingPartId 易损件主键id
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param deleted 逻辑删除
     * @return java.lang.Long  当前时间内总数
     */
    @EntityGraph(value = "batchWorkDetailWearingPartEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select count(id) from BatchWorkDetailWearingPart where batchWorkDetail.workSheet is not null and wearingPart.id = ?1 and createdDate >= ?2 and createdDate <= ?3 and deleted = ?4 and batchWorkDetail.deleted = 0")
    Long countByWorkSheetAndWearingPartIdAndCreatedDateAndDeleted(Long wearingPartId, Instant startDate, Instant endDate, Long deleted);

    /**
     * 通过易损件主键主键id 以及创建时间获取当前时间内总数
     * @param wearingPartId 易损件主键主键id
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param deleted 逻辑删除
     * @return java.lang.Long  当前时间内总数
     */
    @EntityGraph(value = "batchWorkDetailWearingPartEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select count(id) from BatchWorkDetailWearingPart where batchWorkDetail.subWorkSheet is not null and wearingPart.id = ?1 and createdDate >= ?2 and createdDate <= ?3 and deleted = ?4 and batchWorkDetail.deleted = 0")
    Long countBySubWorkSheetAndWearingPartIdAndCreatedDateAndDeleted(Long wearingPartId, Instant startDate, Instant endDate, Long deleted);

    /**
     * 通过易损件主键id 以及创建时间获取当前时间设备信息
     * @param wearingPartId 易损件主键id
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.wearingpart.BatchWorkDetailWearingPart> 批量生产详情易损件列表
     */
    @EntityGraph(value = "batchWorkDetailWearingPartEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    @Query("select bwdw from BatchWorkDetailWearingPart bwdw where bwdw.wearingPart.id = ?1 and bwdw.createdDate >= ?2 and bwdw.createdDate <= ?3 and bwdw.deleted = ?4 and bwdw.batchWorkDetail.deleted = 0 and bwdw.batchWorkDetail.subWorkSheet is not null")
    List<BatchWorkDetailWearingPart> findBySubWorkSheetAndWearingPartIdAndCreatedDateBetween(Long wearingPartId, Instant startDate, Instant endDate, Long deleted);

    /**
     * 通过易损件主键id 以及创建时间获取当前时间设备信息
     * @param wearingPartId 易损件主键id
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.wearingpart.BatchWorkDetailWearingPart> 批量生产详情易损件列表
     */
    @EntityGraph(value = "batchWorkDetailWearingPartEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    @Query("select bwdw from BatchWorkDetailWearingPart bwdw where bwdw.wearingPart.id = ?1 and bwdw.createdDate >= ?2 and bwdw.createdDate <= ?3 and bwdw.deleted = ?4 and bwdw.batchWorkDetail.deleted = 0 and bwdw.batchWorkDetail.workSheet is not null")
    List<BatchWorkDetailWearingPart> findByWorkSheetAndWearingPartIdAndCreatedDateBetween(Long wearingPartId, Instant startDate, Instant endDate, Long deleted);

    /**
     * 通过易损件编码获取在一定范围内使用该易损件的工单
     *
     * @param wearingPartCode 易损件编码
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return java.util.List<net.airuima.rbase.domain.procedure.wearingpart.BatchWorkDetailWearingPart> 批量生产详情易损件列表
     */
    @EntityGraph(value = "batchWorkDetailWearingPartEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    @Query("select b from BatchWorkDetailWearingPart b where b.wearingPart.code = ?1 and b.batchWorkDetail.startDate >= ?2 and b.batchWorkDetail.endDate <= ?3 and b.deleted = 0L")
    List<BatchWorkDetailWearingPart> findByWearingPartCodeAndStartDateAndEndDate(String wearingPartCode, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 获取易损件使用详情
     *
     * @param wearingPartId 易损件Id
     * @param deleted 逻辑删除
     */
    @EntityGraph(value = "batchWorkDetailWearingPartEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @FetchMethod
    List<BatchWorkDetailWearingPart> findByWearingPartIdAndDeleted(Long wearingPartId, Long deleted);
}
