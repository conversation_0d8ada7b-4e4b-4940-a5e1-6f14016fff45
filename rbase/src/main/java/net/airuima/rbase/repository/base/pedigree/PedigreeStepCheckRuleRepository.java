package net.airuima.rbase.repository.base.pedigree;


import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 检测判定标准Repository
 * <AUTHOR>
 * @date 2021-03-22
 */
@Repository
public interface PedigreeStepCheckRuleRepository extends LogicDeleteableRepository<PedigreeStepCheckRule>,
        EntityGraphJpaSpecificationExecutor<PedigreeStepCheckRule>, EntityGraphJpaRepository<PedigreeStepCheckRule, Long> {

    /**
     * 根据优先级配置主键id查询是否有关联的检测规则
     * <AUTHOR>
     * @date 2022/11/4 17:18
     * @param priorityElementConfigId 优先级配置主键id
     * @param deleted 逻辑删除
     * @return net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule  产品谱系工序检测规则
     */
    @FetchMethod
    PedigreeStepCheckRule findTop1ByPriorityElementConfigIdAndDeleted(Long priorityElementConfigId, Long deleted);

    /**
     * 通过谱系主键ID、工艺路线主键ID，工序主键ID及类型获取产品谱系工序检测规则
     * <AUTHOR>
     * @param pedigreeId 产品谱系主键ID
     * @param workFlowId 工艺路线主键ID
     * @param stepId 工序主键ID
     * @param category 类型(0,首检;1,QC抽检）
     * @param deleted     逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule> 产品谱系工序检测规则
     * @date 2021-03-23
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<PedigreeStepCheckRule> findByPedigreeIdAndWorkFlowIdAndStepIdAndCategoryAndDeleted(Long pedigreeId,Long workFlowId,Long stepId,Integer category,Long deleted);

    /**
     * 返回匹配的产品谱系工序检测规则列表
     *
     * @param pedigreeIdList    产品谱系主键id集合
     * @param workSheetId       工单主键id
     * @param workSheetCategory 工单类型
     * @param stepGroupId       工序组主键id
     * @param stepId            工序主键id
     * @param workFlowId        工艺流程主键id
     * @param clientId          客户主键id
     * @param workCellId        工位主键id
     * @param category          检测类型(0：首检；1：巡检；2：末检；3：终检；4：抽检)
     * @param deleted           删除标识
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule> 产品谱系工序检测规则列表
     * <AUTHOR>
     * @date 2022/10/18
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    @EntityGraph(value = "pedigreeStepCheckRuleEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query(value = "select a from PedigreeStepCheckRule a  " +
            "where (a.pedigree is null or a.pedigree.id in (?1)) " +
            "and (a.workSheet is null or a.workSheet.id = ?2) " +
            "and (a.workSheetCategory is null or a.workSheetCategory = ?3) " +
            "and (a.stepGroup is null or a.stepGroup.id = ?4) " +
            "and (a.step is null or a.step.id = ?5) " +
            "and (a.workFlow is null or a.workFlow.id = ?6) " +
            "and (a.clientId is null or a.clientId = ?7) " +
            "and (a.workCell is null or a.workCell.id = ?8)" +
            "and (a.varietyId is null or a.varietyId = ?10)" +
            "and a.category = ?9 and a.deleted = ?11")
    List<PedigreeStepCheckRule> findAllStandardByElementOrderByPriorityAndPedigree(List<Long> pedigreeIdList, Long workSheetId, Integer workSheetCategory, Long stepGroupId, Long stepId, Long workFlowId, Long clientId, Long workCellId, Integer category, Long varietyId,Long deleted);

    /**
     * 返回匹配的产品谱系工序检测规则列表
     *
     * @param pedigreeIdList    产品谱系主键id集合
     * @param workSheetId       工单主键id
     * @param workSheetCategory 工单类型
     * @param stepGroupId       工序组主键id
     * @param stepId            工序主键id
     * @param workFlowId        工艺流程主键id
     * @param clientId          客户主键id
     * @param workCellId        工位主键id
     * @param deleted           删除标识
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule> 产品谱系工序检测规则列表
     * <AUTHOR>
     * @date 2022/10/18
     **/
    @FetchMethod
    @Query(value = "select a from PedigreeStepCheckRule a  " +
            "where (a.pedigree is null or a.pedigree.id in (?1)) " +
            "and (a.workSheet is null or a.workSheet.id = ?2) " +
            "and (a.workSheetCategory is null or a.workSheetCategory = ?3) " +
            "and (a.stepGroup is null or a.stepGroup.id = ?4) " +
            "and (a.step is null or a.step.id = ?5) " +
            "and (a.workFlow is null or a.workFlow.id = ?6) " +
            "and (a.clientId is null or a.clientId = ?7) " +
            "and (a.workCell is null or a.workCell.id = ?8)" +
            "and a.deleted = ?9")
    List<PedigreeStepCheckRule> findAllStandardByElementOrderByPriorityAndPedigreeNoCategory(List<Long> pedigreeIdList, Long workSheetId, Integer workSheetCategory, Long stepGroupId, Long stepId, Long workFlowId, Long clientId, Long workCellId, Long deleted);

    /**
     * 根据产品谱系主键ID和工艺路线主键ID和工序主键ID查询产品谱系工序检测规则列表列表
     * @param pedigreeId 产品谱系主键ID
     * @param workFlowId 工艺路线主键ID
     * @param stepId 工序主键ID
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule> 产品谱系工序检测规则列表
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    List<PedigreeStepCheckRule> findByPedigreeIdAndWorkFlowIdAndStepIdAndDeleted(Long pedigreeId,Long workFlowId,Long stepId,Long deleted);

    /**
     * 根据产品谱系工序检测规则编码查询检测规则
     * <AUTHOR>
     * @date 2022/11/2 19:12
     * @param code 检测规则编码
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule> 产品谱系工序检测规则
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    Optional<PedigreeStepCheckRule> findByCodeAndDeleted(String code, Long deleted);

    /**
     * 根据规则类型和组合条件查询产品谱系工序检测规则
     * <AUTHOR>
     * @date 2022/11/2 19:45
     * @param pedigreeId 产品谱系主键id
     * @param stepId 工序主键id
     * @param workFlowId 工艺路线主键id
     * @param stepGroupId 工序组主键id
     * @param workSheetId 工单主键id
     * @param clientId 客户主键id
     * @param workSheetCategory 工单类型主键id
     * @param category 规则类型
     * @param workCellId 工位主键id
     * @param varietyObjId 项目类型主键ID
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule> 产品谱系工序检测规则
     */
    @FetchMethod
    @Query("select p from PedigreeStepCheckRule p where ((?1 is null and  p.pedigree is null) or p.pedigree.id=?1) " +
            "and ( (?3 is null and  p.workFlow is null) or p.workFlow.id = ?3) " +
            "and ((?2 is null and  p.step is null) or p.step.id = ?2) " +
            "and ( (?4 is null and  p.stepGroup is null) or p.stepGroup.id = ?4) " +
            "and  ( (?5 is null and  p.workSheet is null) or p.workSheet.id = ?5) " +
            "and  ( (?6 is null and  p.clientId is null) or p.clientId = ?6) " +
            "and  ( (?7 is null and  p.workSheetCategory is null) or p.workSheetCategory = ?7) " +
            "and  p.category = ?8  and p.workCell.id=?9 and p.varietyId=?10 and p.deleted=?11")
    @DataFilter(isSkip = true)
    Optional<PedigreeStepCheckRule> findByPedigreeIdAndStepIdAndWorkFlowIdAndStepGroupIdAndWorkSheetIdAndClientIdAndWorkSheetCategoryAndCategoryAndWorkCellIdAndVarietyObjIdAndDeleted(
            Long pedigreeId,Long stepId,Long workFlowId,Long stepGroupId,Long workSheetId,Long clientId,Integer workSheetCategory,Integer category,Long workCellId,Long varietyObjId,Long deleted);

    /**
     * 查询来料检质检方案
     * @param target 优先级配置目标
     * @param category 类别
     * @param attributeId 属性id
     * @param materialId 物料id
     * @param supplierId  供应商id
     * @param clientId 客户id
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule> 质检方案
     */
    @FetchMethod
    @Query("""
            select p from PedigreeStepCheckRule p
            where p.priorityElementConfig.target = ?1 and p.category = ?2 and ((?3 is null and p.attributeId is null) or p.attributeId = ?3) and  ((?4 is null and p.materialId is null) or p.materialId = ?4) and ((?5 is null and p.supplierId is null) or p.supplierId = ?5) and ((?6 is null and p.clientId is null) or p.clientId = ?6) and p.deleted = ?7""")
    @DataFilter(isSkip = true)
    Optional<PedigreeStepCheckRule> findByPriorityElementConfigTargetAndCategoryAndAttributeIdAndMaterialIdAndSupplierIdAndClientIdAndDeleted(int target, int category, Long attributeId, Long materialId, Long supplierId, Long clientId, Long deleted);


    /**
     * 逻辑删除产品谱系工序检测规则
     * <AUTHOR>
     * @date 2022/11/3 13:32
     * @param id 检测规则主键id
     * @param deleted 逻辑删除
     */
    @Modifying
    @Query(value = "update PedigreeStepCheckRule pscr set pscr.deleted = pscr.id where pscr.id =?1 and pscr.deleted=?2")
    void deleteByIdAndDeleted(Long id,Long deleted);

    /**
     * 根据规则类型和组合条件查询检测规则
     *
     * @param pedigreeId 产品谱系主键id
     * @param workSheetId 工单主键id
     * @param workSheetCategory 工单类型主键id
     * @param stepGroupId 工序组主键id
     * @param stepId 工序主键id
     * @param workFlowId 工艺路线主键id
     * @param clientId 客户主键id
     * @return java.util.Optional<net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule> 产品谱系工序检测规则
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    @Query("select p from PedigreeStepCheckRule p where p.deleted = 0L" +
            " and (coalesce(?1, p.pedigree.id) is null or p.pedigree.id = ?1)" +
            " and (coalesce(?2, p.workSheet.id) is null or p.workSheet.id = ?2)" +
            " and (coalesce(?3, p.workSheetCategory) is null or p.workSheetCategory = ?3)" +
            " and (coalesce(?4, p.stepGroup.id) is null or p.stepGroup.id = ?4)" +
            " and (coalesce(?5, p.step.id) is null or p.step.id = ?5)" +
            " and (coalesce(?6, p.workFlow.id) is null or p.workFlow.id = ?6)" +
            " and (coalesce(?7, p.clientId) is null or p.clientId = ?7)")
    Optional<PedigreeStepCheckRule> findUnique(Long pedigreeId, Long workSheetId, Integer workSheetCategory, Long stepGroupId,
            Long stepId, Long workFlowId, Long clientId);

    /**
     * 根据时间、启用状态、删除标识更新启用状态
     * @param isEnable          更新后的是否启用状态
     * @param expiryDate        时间
     * @param sourceIsEnable    跟新前的是否启用状态
     * @param deleted           删除标识
     */
    @Modifying
    @Query(value = "update PedigreeStepCheckRule pscr set pscr.isEnable = ?1 where pscr.expiryDate < ?2 and pscr.isEnable = ?3 and pscr.deleted = ?4 ")
    void upDateIsEnableByExpiryDateAndIsEnableAndDeleted(Boolean isEnable, LocalDate expiryDate, Boolean sourceIsEnable, Long deleted);

    /**
     * 根据主键id 获取检测产品谱系工序检测规则
     * @param id 主键id
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule> 产品谱系工序检测规则
     * <AUTHOR>
     * @Date  2023/4/27
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    Optional<PedigreeStepCheckRule> findByIdAndDeleted(Long id,Long deleted);

    /**
     * 根据抽样方式获取检测方案
     * @param judgeWay  抽样方式
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2023/5/16
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule> 产品谱系工序检测规则列表
     */
    @FetchMethod
    List<PedigreeStepCheckRule> findByJudgeWayAndDeleted(Integer judgeWay,Long deleted);


    /**
     * 查询来料检质检方案
     * @param category 类别
     * @param attributeIds   属性id列表
     * @param materialId   物料id
     * @param supplierId  供应商id
     * @param clientId 客户id
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule> 质检方案
     */
    @Query("""
            select p from PedigreeStepCheckRule p
            where p.category = ?1
            and (p.attributeId is null or p.attributeId in ?2)
            and (p.materialId is null or p.materialId = ?3)
            and (p.supplierId is null or p.supplierId = ?4)
            and (p.clientId is null or p.clientId = ?5)
            and p.isEnable = true
            and p.deleted = ?6
            """)
    @DataFilter(isSkip = true)
    @FetchMethod
    List<PedigreeStepCheckRule> findByCategoryAndAttributeIdAndMaterialIdAndSupplierIdAndClientIdAndDeleted(Integer category, List<Long> attributeIds, Long materialId, Long supplierId, Long clientId, Long deleted);


    /**
     * 通过 产品谱系+工序+供应商+质检类型+检验类型 获取质检方案
     * @param category 质检类型
     * @param pedigreeIds 产品谱系ids
     * @param stepId 工序id
     * @param supplierId 供应商id
     * @param varietyId 检验类型id
     * @param deleted 逻辑删除
     * @return 质检方案历史
     */
    @Query("""
        
         select p from PedigreeStepCheckRule p
            where p.category = ?1
            and (p.pedigree.id is null or p.pedigree.id in ?2)
            and (p.step.id is null or p.step.id = ?3)
            and (p.supplierId is null or p.supplierId = ?4)
            and (p.varietyId is null or p.varietyId = ?5)
            and p.isEnable = true
            and p.deleted = ?6 
    
""")
    @FetchMethod
    List<PedigreeStepCheckRule> findByCategoryAndPedigreeIdInAndStepIdAndSupplierIdAndVarietyIdAndDeleted(Integer category,List<Long> pedigreeIds, Long stepId, Long supplierId,Long varietyId, Long deleted);
}
