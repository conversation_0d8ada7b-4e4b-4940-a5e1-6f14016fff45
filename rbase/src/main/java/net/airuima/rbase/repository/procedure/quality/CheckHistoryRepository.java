package net.airuima.rbase.repository.procedure.quality;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.quality.CheckHistory;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 检测历史Repository
 * <AUTHOR>
 * @date 2021-03-22
 */
@Repository
public interface CheckHistoryRepository extends LogicDeleteableRepository<CheckHistory>,
        EntityGraphJpaSpecificationExecutor<CheckHistory>, EntityGraphJpaRepository<CheckHistory, Long> {
    /**
     * 根据检测类型、项目类型主键id、工位主键id、删除标识倒序查询最新一条检测历史
     *
     * @param category   检测类型
     * @param varietyId  项目类型主键id
     * @param workCellId 工位主键id
     * @param deleted    删除标识
     * @return net.airuima.rbase.domain.procedure.quality.CheckHistory 检测历史
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("""
            select c from CheckHistory c
            where c.category = ?1 and ((?2 is null and c.varietyId is null) or (c.varietyId = ?2)) and c.workCell.id = ?3 and c.deleted = ?4  ORDER BY c.id desc limit 1""")
    CheckHistory findTopOneByCategoryAndVarietyIdAndWorkCellIdAndDeletedOrderByCreatedDateDesc(Integer category, Long varietyId, Long workCellId, Long deleted);

    /**
     * 根据检测类型、项目类型主键id、工位主键id、删除标识倒序查询最新一条检测历史
     *
     * @param category   检测类型
     * @param varietyId  项目类型主键id
     * @param workCellId 工位主键id
     * @param deleted    删除标识
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.CheckHistory> 检测历史
     */
    @DataFilter(isSkip = true)
    Optional<CheckHistory> findTop1ByWorkCellIdAndCategoryAndVarietyIdAndDeletedOrderByIdDesc(Long workCellId, Integer category, Long varietyId, Long deleted);

    /**
     * 通过主键id 获取检测历史记录
     * @param id 检测历史主键id
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.CheckHistory> 检测历史
     */
    @DataFilter(isSkip = true)
    Optional<CheckHistory> findByIdAndDeleted(Long id,Long deleted);

    /**
     * 通过子工单主键ID和类型和状态查找 检测历史
     * @param subWorkSheetId  子工单主键ID
     * @param category 类型
     * @param status 状态
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.CheckHistory> 检测历史
     */
    @DataFilter(isSkip = true)
    Optional<CheckHistory> findTop1BySubWorkSheetIdAndWorkSheetIsNullAndContainerCodeIsNullAndCategoryGreaterThanAndStatusAndDeleted(Long subWorkSheetId,Integer category,Boolean status,Long deleted);


    /**
     * 通过工单主键ID和类型和状态查找 检测历史
     * @param workSheetId 工单主键ID
     * @param category 类型
     * @param status 状态
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.CheckHistory> 检测历史
     */
    @DataFilter(isSkip = true)
    Optional<CheckHistory> findTop1ByWorkSheetIdAndSubWorkSheetIsNullAndContainerCodeIsNullAndCategoryGreaterThanAndStatusAndDeleted(Long workSheetId,Integer category,Boolean status,Long deleted);

    /**
     * 通过子工单主键ID和容器编码和类型和状态查找 检测历史
     * @param subWorkSheetId 子工单主键id
     * @param containerCode  容器编码
     * @param category 类型
     * @param status 状态
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.CheckHistory> 检测历史
     */
    @DataFilter(isSkip = true)
    Optional<CheckHistory> findTop1BySubWorkSheetIdAndWorkSheetIsNullAndContainerCodeAndCategoryGreaterThanAndStatusAndDeleted(Long subWorkSheetId,String containerCode,Integer category,Boolean status,Long deleted);

    /**
     *通过工单主键ID和容器编码和类型和状态查找 检测历史
     * @param workSheetId 工单主键id
     * @param containerCode 容器编码
     * @param category 类型
     * @param status 状态
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.quality.CheckHistory> 检测历史
     */
    @DataFilter(isSkip = true)
    Optional<CheckHistory> findTop1ByWorkSheetIdAndSubWorkSheetIsNullAndContainerCodeAndCategoryGreaterThanAndStatusAndDeleted(Long workSheetId,String containerCode,Integer category,Boolean status,Long deleted);

    /**
     * 根据流水号和逻辑删除获取唯一记录
     * @param serialNumber 流水号
     * @param deleted 逻辑删除
     * @return CheckHistory
     */
    @DataFilter(isSkip = true)
    Optional<CheckHistory> findBySerialNumberAndDeleted(String serialNumber,Long deleted);

    /**
     * 通过子工单ID、质检类型、状态和逻辑删除统计数量
     * @param subWorkSheetId 子工单ID
     * @param category 质检类型
     * @param status 处理状态
     * @param deleted 逻辑删除
     * @return 数量
     */
    long countBySubWorkSheetIdAndCategoryInAndStatusAndDeleted(Long subWorkSheetId, List<Integer> category, Boolean status, Long deleted);


    /**
     * 通过工单ID、质检类型、状态和逻辑删除统计数量
     * @param workSheetId 工单ID
     * @param category 质检类型
     * @param status 处理状态
     * @param deleted 逻辑删除
     * @return 数量
     */
    long countByWorkSheetIdAndCategoryInAndStatusAndDeleted(Long workSheetId,List<Integer> category,Boolean status,Long deleted);

    /**
     * 通过子工单ID、质检类型、状态和逻辑删除统计数量
     * @param subWorkSheetId 子工单ID
     * @param category 质检类型
     * @param status 处理状态
     * @param deleted 逻辑删除
     * @return 数量
     */
    long countBySubWorkSheetIdAndStepIdAndCategoryInAndStatusAndDeleted(Long subWorkSheetId, Long stepId,List<Integer> category, Boolean status, Long deleted);


    /**
     * 通过工单ID、质检类型、状态和逻辑删除统计数量
     * @param workSheetId 工单ID
     * @param category 质检类型
     * @param status 处理状态
     * @param deleted 逻辑删除
     * @return 数量
     */
    long countByWorkSheetIdAndStepIdAndCategoryInAndStatusAndDeleted(Long workSheetId,Long stepId,List<Integer> category,Boolean status,Long deleted);

    /**
     * 通过子工单id 工序id 状态 检查历史类型 处理方式 获取 检查历史记录列表
     * @param subWsId 子工单id
     * @param stepId 工序id
     * @param status 状态
     * @param category 检查历史类型
     * @param dealWay 处理方式
     * @param deleted 逻辑删除
     * @return 检查历史记录列表
     */
    List<CheckHistory> findBySubWorkSheetIdAndStepIdAndStatusAndCategoryInAndDealWayAndDeleted(Long subWsId,Long stepId,Boolean status,List<Integer> category,Integer dealWay,Long deleted);

    /**
     * 通过工单id 工序id 状态 检查历史类型 处理方式 获取 检查历史记录列表
     * @param wsId 工单id
     * @param stepId 工序id
     * @param status 状态
     * @param category 检查历史类型
     * @param dealWay 处理方式
     * @param deleted 逻辑删除
     * @return 检查历史记录列表
     */
    List<CheckHistory> findByWorkSheetIdAndStepIdAndStatusAndCategoryInAndDealWayAndDeleted(Long wsId,Long stepId,Boolean status,List<Integer> category,Integer dealWay,Long deleted);


    /**
     * 通过子工单id 工序id 检查历史类型 处理方式 获取 检查历史记录数量
     * @param subWorkSheetId 子工单id
     * @param stepId 工序id
     * @param categorys 检查历史类型
     * @param dealWay 处理方式
     * @param deleted 逻辑删除
     * @return 检查历史记录数量
     * <AUTHOR>
     * @since 1.8.1
     * @version 1.8.1
     */
    long countBySubWorkSheetIdAndStepIdAndCategoryInAndDealWayAndDeleted(Long subWorkSheetId, Long stepId, List<Integer> categorys, int dealWay,Long deleted);


    /**
     * 通过工单id 工序id 检查历史类型 处理方式 获取 检查历史记录数量
     * @param workSheetId 工单id
     * @param stepId 工序id
     * @param categorys 检查历史类型
     * @param dealWay 处理方式
     * @param deleted 逻辑删除
     * @return 检查历史记录数量
     * <AUTHOR>
     * @since 1.8.1
     * @version 1.8.1
     */
    long countByWorkSheetIdAndStepIdAndCategoryInAndDealWayAndDeleted(Long workSheetId, Long stepId, List<Integer> categorys, int dealWay,Long deleted);

}
