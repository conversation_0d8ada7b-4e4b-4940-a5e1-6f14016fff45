package net.airuima.rbase.repository.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetailMaterialBatch;
import net.airuima.rbase.domain.procedure.batch.ContainerDetailMaterialBatch;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.web.rest.rmps.dto.PackRelationDataDTO;
import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.rbase.web.rest.report.dto.ProcessStepMaterialBatchDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器工作详情物料批次表Repository
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Repository
public interface ContainerDetailMaterialBatchRepository extends LogicDeleteableRepository<ContainerDetailMaterialBatch>,
        EntityGraphJpaSpecificationExecutor<ContainerDetailMaterialBatch>, EntityGraphJpaRepository<ContainerDetailMaterialBatch, Long> {

    /**
     * 通过容器详情主键ID、物料主键ID、物料批次获取唯一记录
     *
     * @param ContainerDetailId 容器详情ID
     * @param materialId        物料ID
     * @param batch             批次号
     * @param deleted           逻辑删除
     * @return java.util.Optionall<net.airuima.domain.procedure.batch.ContainerDetailMaterialBatch> 容器工作详情物料批次表
     * <AUTHOR>
     * @date 2021-01-19
     **/
    @DataFilter(isSkip = true)
    Optional<ContainerDetailMaterialBatch> findByContainerDetailIdAndMaterialIdAndBatchAndDeleted(Long ContainerDetailId, Long materialId, String batch, Long deleted);

    /**
     * 特殊物料没有物料批次时通过容器详情ID、物料ID获取唯一记录
     *
     * @param ContainerDetailId 容器详情ID
     * @param materialId        物料ID
     * @param deleted           逻辑删除
     * @return java.util.Optionall<net.airuima.domain.procedure.batch.ContainerDetailMaterialBatch> 容器工作详情物料批次表
     * <AUTHOR>
     * @date 2021-01-19
     **/
    @DataFilter(isSkip = true)
    Optional<ContainerDetailMaterialBatch> findByContainerDetailIdAndMaterialIdAndDeleted(Long ContainerDetailId, Long materialId, Long deleted);

    /**
     * 特殊物料没有物料主键ID时通过容器详情主键ID、物料批次获取唯一记录
     *
     * @param ContainerDetailId 容器详情主键ID
     * @param batch             批次号
     * @param deleted           逻辑删除
     * @return java.util.Optionall<net.airuima.domain.procedure.batch.ContainerDetailMaterialBatch> 容器工作详情物料批次表
     * <AUTHOR>
     * @date 2021-01-19
     **/
    @DataFilter(isSkip = true)
    Optional<ContainerDetailMaterialBatch> findByContainerDetailIdAndBatchAndDeleted(Long ContainerDetailId, String batch, Long deleted);

    /**
     * 获得容器物料批次生产详情
     *
     * @param batch   物料批次
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetailMaterialBatch> 容器工作详情物料批次表列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<ContainerDetailMaterialBatch> findByBatchAndDeleted(String batch, Long deleted);


    /**
     * 根据日期、批次获取数据 分页
     *
     * @param batch     物料批次
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param deleted   逻辑删除
     * @param pageable  分页
     * @return org.springframework.data.domain.Page<net.airuima.domain.procedure.batch.ContainerDetailMaterialBatch> 容器工作详情物料批次表分页
     * <AUTHOR>
     * @date 2021-03-31
     **/
    @FetchMethod
    @DataFilter(isSkip = true)
    Page<ContainerDetailMaterialBatch> findByBatchAndCreatedDateGreaterThanEqualAndCreatedDateLessThanEqualAndDeleted(String batch, Instant startDate, Instant endDate, Long deleted, Pageable pageable);

    /**
     * 根据容器详情ID删除物料批次信息
     *
     * @param containerDetailId 容器详情ID
     * @return void
     * <AUTHOR>
     * @date 2021-06-13
     **/
    @Modifying
    @Query("update ContainerDetailMaterialBatch cmb set cmb.deleted=cmb.id where cmb.containerDetail.id=?1")
    void batchDeleteByContainerDetailId(Long containerDetailId);

    /**
     * 通过容器详情 获取对应的 容器物料批次信息
     *
     * @param containerDetailId 容器详情主键id
     * @param deleted           逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetailMaterialBatch> 容器工作详情物料批次表列表
     * <AUTHOR>
     * @date 2022/4/13
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<ContainerDetailMaterialBatch> findByContainerDetailIdAndDeleted(Long containerDetailId, Long deleted);

    /**
     * 通过条件获取不等于某种扣料方式的记录
     *
     * @param containerDetailId 容器详情id
     * @param type              扣料方式 0：不扣 1：工单扣料，2：工位扣料
     * @param deleted           逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetailMaterialBatch> 容器工作详情物料批次表列表
     */
    @DataFilter(isSkip = true)
    List<ContainerDetailMaterialBatch> findByContainerDetailIdAndTypeNotAndDeleted(Long containerDetailId, int type, Long deleted);

    /**
     * 通过容器详情列表 获取对应的 容器物料批次信息
     *
     * @param containerDetailIds 容器详情主键id列表
     * @param deleted            逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetailMaterialBatch> 容器工作详情物料批次表列表
     * <AUTHOR>
     * @date 2022/5/5
     */
    @DataFilter(isSkip = true)
    List<ContainerDetailMaterialBatch> findByContainerDetailIdInAndDeleted(List<Long> containerDetailIds, Long deleted);


    /**
     * 根据批次详情主键id 与物料主键id 批次 获取 物料批次详情使用信息列表
     *
     * @param batchDetailId 批次详情主键id
     * @param materialId    物料主键id
     * @param batch         批次
     * @param deleted 删除标记
     * @return java.util.List<net.airuima.domain.procedure.batch.ContainerDetailMaterialBatch> 容器工作详情物料批次表列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select new net.airuima.rbase.web.rest.report.dto.ProcessStepMaterialBatchDTO(cdmb.containerDetail.id,cdmb.containerDetail.containerCode,cdmb.containerDetail.staffId,cdmb.containerDetail.workCell.code,cdmb.containerDetail.workCell.name," +
            "cdmb.materialId,cdmb.batch,cdmb.containerDetail.inputNumber,cdmb.number,cdmb.containerDetail.qualifiedNumber,cdmb.supplierId) " +
            "from ContainerDetailMaterialBatch cdmb where cdmb.containerDetail.batchWorkDetail.id = ?1 and " +
            "(?3 is null or cdmb.batch = ?3) and " +
            "cdmb.materialId = ?2 and cdmb.deleted = ?4")
    List<ProcessStepMaterialBatchDTO> findByContainerDetailBatchWorkDetailIdAndMaterialIdAndBatchAndDeleted(Long batchDetailId, Long materialId, String batch, Long deleted);

    /**
     * 获取工单 指定容器的物料或者批次信息
     * @param serialNumber 工单编码
     * @param materialId 物料id
     * @param materialBatch 物料批次
     * @return List<PackRelationDataDTO>
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    @Query("select new net.airuima.rbase.web.rest.rmps.dto.PackRelationDataDTO(cdmb.containerDetail.batchWorkDetail.workSheet.serialNumber,cdmb.materialId,cdmb.batch,cdmb.containerDetail.containerCode) from ContainerDetailMaterialBatch cdmb where cdmb.containerDetail.batchWorkDetail.workSheet.serialNumber = ?1 and (?2 is null or cdmb.materialId = ?2) and (?3 is null  or cdmb.batch = ?3) and cdmb.containerDetail.containerCode = ?4 and cdmb.deleted = 0 group by cdmb.containerDetail.batchWorkDetail.workSheet.id,cdmb.materialId,cdmb.batch,cdmb.containerDetail.containerCode")
    List<PackRelationDataDTO> findPackRelationDataByWsAndMaterialIdAndMaterialBatch(String serialNumber, Long materialId, String materialBatch,String containerCode);

    /**
     * 获取子工单列表 指定容器的物料或者批次信息
     * @param serialNumber 子工单列表
     * @param materialId 物料id
     * @param materialBatch 物料批次
     * @return List<PackRelationDataDTO>
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    @Query("select new net.airuima.rbase.web.rest.rmps.dto.PackRelationDataDTO(ws.serialNumber,sub.serialNumber,cdmb.materialId,cdmb.batch,cdmb.containerDetail.containerCode) from ContainerDetailMaterialBatch cdmb " +
            "left join SubWorkSheet sub on sub.id = cdmb.containerDetail.batchWorkDetail.subWorkSheet.id " +
            "left join WorkSheet ws on ws.id = sub.workSheet.id" +
            " where cdmb.containerDetail.batchWorkDetail.subWorkSheet.serialNumber in ?1 and (?2 is null or cdmb.materialId = ?2) and (?3 is null  or cdmb.batch = ?3) and cdmb.containerDetail.containerCode = ?4 and cdmb.deleted = 0 group by cdmb.containerDetail.batchWorkDetail.subWorkSheet.id,cdmb.materialId,cdmb.batch,cdmb.containerDetail.containerCode")
    List<PackRelationDataDTO> findPackRelationDataBySubWsAndMaterialIdAndMaterialBatch(List<String> serialNumber, Long materialId, String materialBatch,String containerCode);

    /**
     * 通过物料id获取在一定范围内使用该物料的工单
     *
     * @param materialId 物料id
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkDetail> 容器工作详情物料批次表列表
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    @Query("select c from ContainerDetailMaterialBatch c where c.materialId = ?1 and c.containerDetail.recordDate >= ?2 and c.containerDetail.recordDate <= ?3 and c.deleted = 0L")
    List<ContainerDetailMaterialBatch> findByMaterialIdAndStartDateAndEndDate(Long materialId, LocalDateTime startDate, LocalDateTime endDate);
}
