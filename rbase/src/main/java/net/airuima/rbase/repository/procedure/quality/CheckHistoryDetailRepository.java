package net.airuima.rbase.repository.procedure.quality;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.quality.CheckHistoryDetail;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 检测历史明细Repository
 * <AUTHOR>
 * @date 2021-03-22
 */
@Repository
public interface CheckHistoryDetailRepository extends LogicDeleteableRepository<CheckHistoryDetail>,
        EntityGraphJpaSpecificationExecutor<CheckHistoryDetail>, EntityGraphJpaRepository<CheckHistoryDetail, Long> {

    /**
     * 通过检测历史主键ID获取明细
     * <AUTHOR>
     * @param historyId 检测历史主键ID
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.quality.CheckHistoryDetail> 检测历史明细列表
     * @date 2021-03-23
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<CheckHistoryDetail> findByCheckHistoryIdAndDeletedOrderBySn(Long historyId,Long deleted);

    /**
     * 通过子工单主键ID，处理状态、sn以及是否为虚拟SN获取质检历史明细
     * @param subWorkSheetId  子工单主键ID
     * @param status 历史处理状态
     * @param sn sn
     * @param virtual 是否为虚拟SN
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.quality.CheckHistoryDetail> 检测历史明细列表
     * <AUTHOR>
     * @date 2023/10/9
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<CheckHistoryDetail> findByCheckHistorySubWorkSheetIdAndCheckHistoryStatusAndCheckHistoryWorkSheetIsNullAndSnAndVirtualAndDeleted(Long subWorkSheetId,Boolean status,String sn,Boolean virtual,Long deleted);

    /**
     * 通过工单主键ID，处理状态、sn以及是否为虚拟SN获取质检历史明细
     * @param workSheetId 工单主键ID
     * @param status 历史处理状态
     * @param sn SN
     * @param virtual 是否为虚拟SN
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.quality.CheckHistoryDetail> 检测历史明细列表
     * <AUTHOR>
     * @date 2023/10/9
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<CheckHistoryDetail> findByCheckHistoryWorkSheetIdAndCheckHistoryStatusAndCheckHistorySubWorkSheetIsNullAndSnAndVirtualAndDeleted(Long workSheetId,Boolean status,String sn,Boolean virtual,Long deleted);
}
