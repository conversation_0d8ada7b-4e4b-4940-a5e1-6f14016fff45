package net.airuima.rbase.repository.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.batch.WsStepUnqualifiedItem;
import net.airuima.rbase.web.rest.report.dto.WorkSheetQualifiedRateReportChartResultDTO;
import net.airuima.rbase.web.rest.report.dto.WorkSheetQualifiedRateReportTableItemDTO;
import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.rbase.web.rest.report.dto.ForwardTraceResultDTO;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单工序不良项目统计Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface WsStepUnqualifiedItemRepository extends LogicDeleteableRepository<WsStepUnqualifiedItem>,
        EntityGraphJpaSpecificationExecutor<WsStepUnqualifiedItem>, EntityGraphJpaRepository<WsStepUnqualifiedItem, Long> {

    /**
     * 通过子工单主键ID、工序主键ID列表,不良项目主键ID列表获取满足条件的不良总数
     *
     * @param subWsId               子工单主键ID
     * @param stepIdList            工序主键ID列表
     * @param unqualifiedItemIdList 不良项目主键ID列表
     * @param deleted               逻辑删除
     * @return java.lang.Long  不良总数
     * <AUTHOR>
     * @date 2021-01-18
     **/
    @DataFilter(isSkip = true)
    @Query("select sum(wsStepUnqualifiedItem.number) from WsStepUnqualifiedItem wsStepUnqualifiedItem " +
            "where wsStepUnqualifiedItem.subWorkSheet.id=?1 " +
            "and wsStepUnqualifiedItem.step.id in (?2) " +
            "and wsStepUnqualifiedItem.unqualifiedItem.id in (?3) " +
            "and wsStepUnqualifiedItem.deleted=?4")
    Long sumNumberBySubWorkSheetIdAndStepIdInAndUnqualifiedItemIdInAndDeleted(Long subWsId, List<Long> stepIdList, List<Long> unqualifiedItemIdList, Long deleted);


    /**
     * 通过子工单主键ID、不良项目主键ID列表获取满足条件的不良总数
     *
     * @param subWsId               子工单主键ID
     * @param unqualifiedItemIdList 不良项目ID列表
     * @param deleted               逻辑删除
     * @return java.lang.Long  不良总数
     * <AUTHOR>
     * @date 2021-04-20
     **/
    @DataFilter(isSkip = true)
    @Query("select sum(wsStepUnqualifiedItem.number) from WsStepUnqualifiedItem wsStepUnqualifiedItem " +
            "where wsStepUnqualifiedItem.subWorkSheet.id=?1 " +
            "and wsStepUnqualifiedItem.unqualifiedItem.id in (?2) " +
            "and wsStepUnqualifiedItem.deleted=?3")
    Long sumNumberBySubWorkSheetIdAndUnqualifiedItemIdInAndDeleted(Long subWsId, List<Long> unqualifiedItemIdList, Long deleted);


    /**
     * 通过工单主键ID、不良项目主键ID列表获取满足条件的不良总数
     *
     * @param workSheetId           工单主键ID
     * @param unqualifiedItemIdList 不良项目主键ID列表
     * @param deleted               逻辑删除
     * @return java.lang.Long  不良总数
     */
    @DataFilter(isSkip = true)
    @Query("select sum(wsStepUnqualifiedItem.number) from WsStepUnqualifiedItem wsStepUnqualifiedItem " +
            "where wsStepUnqualifiedItem.workSheet.id=?1 " +
            "and wsStepUnqualifiedItem.unqualifiedItem.id in (?2) " +
            "and wsStepUnqualifiedItem.deleted=?3")
    Long sumNumberByWorkSheetIdAndUnqualifiedItemIdInAndDeleted(Long workSheetId, List<Long> unqualifiedItemIdList, Long deleted);

    /**
     * 通过子工单主键ID、工序主键ID,不良项目主键ID获取满足条件的不良总数
     *
     * @param subWsId           子工单主键ID
     * @param stepId            工序主键ID
     * @param unqualifiedItemId 不良项目主键ID
     * @param deleted           逻辑删除
     * @return java.lang.Long  不良总数
     * <AUTHOR>
     * @date 2021-01-20
     **/
    @DataFilter(isSkip = true)
    @Query("select sum(wsStepUnqualifiedItem.number) from WsStepUnqualifiedItem wsStepUnqualifiedItem " +
            "where wsStepUnqualifiedItem.subWorkSheet.id=?1 " +
            "and wsStepUnqualifiedItem.step.id=?2 " +
            "and wsStepUnqualifiedItem.unqualifiedItem.id=?3 " +
            "and wsStepUnqualifiedItem.deleted=?4")
    Long sumNumberBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(Long subWsId, Long stepId, Long unqualifiedItemId, Long deleted);


    /**
     * 通过工单主键ID、工序主键ID,不良项目主键ID获取满足条件的不良总数
     *
     * @param workSheetId       工单主键ID
     * @param stepId            工序主键ID
     * @param unqualifiedItemId 不良项目主键ID
     * @param deleted           逻辑删除
     * @return java.lang.Long  不良总数
     **/
    @DataFilter(isSkip = true)
    @Query("select sum(wsStepUnqualifiedItem.number) from WsStepUnqualifiedItem wsStepUnqualifiedItem " +
            "where wsStepUnqualifiedItem.workSheet.id=?1 " +
            "and wsStepUnqualifiedItem.step.id=?2 " +
            "and wsStepUnqualifiedItem.unqualifiedItem.id=?3 " +
            "and wsStepUnqualifiedItem.deleted=?4")
    Long sumNumberByWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(Long workSheetId, Long stepId, Long unqualifiedItemId, Long deleted);

    /**
     * 根据子工单主键ID、工序主键ID获取所有不良
     *
     * @param subWorkSheetId 子工单主键ID
     * @param stepId         工序主键ID
     * @param deleted        逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.WsStepUnqualifiedItem>  工单工序不良项目统计数据列表
     * <AUTHOR>
     * @date 2021-01-19
     **/
    @DataFilter(isSkip = true)
    List<WsStepUnqualifiedItem> findBySubWorkSheetIdAndStepIdAndDeleted(Long subWorkSheetId, Long stepId, Long deleted);

    /**
     * 根据子工单主键ID、工序主键ID获取所有不良
     *
     * @param subWorkSheetId 子工单主键ID
     * @param stepIds         工序主键ID
     * @param deleted        逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.WsStepUnqualifiedItem>  工单工序不良项目统计数据列表
     * <AUTHOR>
     * @date 2021-01-19
     **/
    @DataFilter(isSkip = true)
    List<WsStepUnqualifiedItem> findBySubWorkSheetIdAndStepIdInAndDeleted(Long subWorkSheetId, List<Long> stepIds, Long deleted);


    /**
     * 根据子工单主键ID、工序主键ID获取所有不良
     *
     * @param workSheetId 工单主键ID
     * @param stepIds         工序主键ID
     * @param deleted        逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.WsStepUnqualifiedItem>  工单工序不良项目统计数据列表
     * <AUTHOR>
     * @date 2021-01-19
     **/
    @DataFilter(isSkip = true)
    List<WsStepUnqualifiedItem> findByWorkSheetIdAndStepIdInAndDeleted(Long workSheetId, List<Long> stepIds, Long deleted);

    /**
     * 根据工单主键ID、工序主键ID获取所有不良
     *
     * @param workSheetId 工单主键ID
     * @param stepId      工序主键ID
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.WsStepUnqualifiedItem>  工单工序不良项目统计数据列表
     * <AUTHOR>
     * @date 2021-01-19
     **/
    @DataFilter(isSkip = true)
    List<WsStepUnqualifiedItem> findByWorkSheetIdAndStepIdAndDeleted(Long workSheetId, Long stepId, Long deleted);


    /**
     * 通过总工单主键id数组查询子工单工序生产不良记录
     *
     * @param idList  总工单主键id数组
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.WsStepUnqualifiedItem>  工单工序不良项目统计数据列表
     */
    @DataFilter(isSkip = true)
    List<WsStepUnqualifiedItem> findBySubWorkSheetWorkSheetIdInAndDeleted(List<Long> idList, Long deleted);

    /**
     * 获取子工单 不良信息集合
     *
     * @param subWorkSheetId 子工单id
     * @return List<ForwardTraceResultDTO.UnqualifiedItemDetail>
     */
    @Query("select new net.airuima.rbase.web.rest.report.dto.ForwardTraceResultDTO$UnqualifiedItemDetail(wsui.unqualifiedItem.name,wsui.unqualifiedItem.code,sum(wsui.number)) from WsStepUnqualifiedItem wsui where wsui.subWorkSheet.id in ?1 and wsui.deleted = 0 group by wsui.unqualifiedItem.id")
    List<ForwardTraceResultDTO.UnqualifiedItemDetail> findBySubWorkSheetUnqualifiedItem(List<Long> subWorkSheetId);

    /**
     * 获取工单 不良信息集合
     *
     * @param workSheetId 工单id
     * @return List<ForwardTraceResultDTO.UnqualifiedItemDetail>
     */
    @Query("select new net.airuima.rbase.web.rest.report.dto.ForwardTraceResultDTO$UnqualifiedItemDetail(wsui.unqualifiedItem.name,wsui.unqualifiedItem.code,sum(wsui.number)) from WsStepUnqualifiedItem wsui where wsui.workSheet.id in ?1 and wsui.deleted = 0 group by wsui.unqualifiedItem.id")
    List<ForwardTraceResultDTO.UnqualifiedItemDetail> findByWorkSheetUnqualifiedItem(List<Long> workSheetId);

    /**
     * 根据子工单主键ID及工序主键ID列表获取待返修的不良项目信息
     *
     * @param subWorkSheetId 子工单主键ID
     * @param stepIdList     工序主键ID列表
     * @param dealWay        不良项目处理方式
     * @param deleted        逻辑山粗
     * @return java.util.List<net.airuima.domain.procedure.batch.WsStepUnqualifiedItem>  工单工序不良项目统计数据列表
     * <AUTHOR>
     * @date 2021-04-29
     **/
    @DataFilter(isSkip = true)
    List<WsStepUnqualifiedItem> findBySubWorkSheetIdAndStepIdInAndUnqualifiedItemDealWayAndDeleted(Long subWorkSheetId, List<Long> stepIdList, Integer dealWay, Long deleted);


    /**
     * 通过子工单列表及是否已生成在线返修单标志获取记录
     *
     * @param subWorkSheetIdList 子工单主键ID列表
     * @param flag               是否已生成
     * @param deleted            逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.WsStepUnqualifiedItem>  工单工序不良项目统计数据列表
     * <AUTHOR>
     * @date 2021-05-11
     **/
    @DataFilter(isSkip = true)
    List<WsStepUnqualifiedItem> findBySubWorkSheetIdInAndFlagAndDeleted(List<Long> subWorkSheetIdList, boolean flag, Long deleted);

    /**
     * 通过子工单列表及工序是否已生成在线返修单标志获取记录
     *
     * @param subWorkSheetId 子工单主键ID列表
     * @param stepIdList     工序主键ID列表
     * @param flag           是否已生成
     * @param deleted        逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.WsStepUnqualifiedItem>  工单工序不良项目统计数据列表
     */
    @DataFilter(isSkip = true)
    List<WsStepUnqualifiedItem> findBySubWorkSheetIdAndStepIdInAndFlagAndDeleted(Long subWorkSheetId, List<Long> stepIdList, boolean flag, Long deleted);

    /**
     * 通过子工单主键ID、工序主键ID及是否已生成在线返修单标志获取记录
     *
     * @param subWorkSheetId 子工单主键ID
     * @param stepId         工序主键ID
     * @param flag           是否已生成在线返修单
     * @param deleted        逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.WsStepUnqualifiedItem>  工单工序不良项目统计数据列表
     * <AUTHOR>
     * @date 2021-08-27
     **/
    @DataFilter(isSkip = true)
    List<WsStepUnqualifiedItem> findBySubWorkSheetIdAndStepIdAndFlagAndDeleted(Long subWorkSheetId, Long stepId, boolean flag, Long deleted);

    /**
     * 通过工单主键ID、工序主键ID及是否已生成在线返修单标志获取记录
     *
     * @param workSheetId 工单主键ID
     * @param stepId      工序主键ID
     * @param flag        是否已生成在线返修单
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.WsStepUnqualifiedItem>  工单工序不良项目统计数据列表
     */
    @DataFilter(isSkip = true)
    List<WsStepUnqualifiedItem> findByWorkSheetIdAndStepIdAndFlagAndDeleted(Long workSheetId, Long stepId, boolean flag, Long deleted);

    /**
     * 通过子工单主键ID及是否已生成在线返修单标志获取记录
     *
     * @param subWorkSheetId 子工单主键ID
     * @param flag           是否已生成
     * @param deleted        逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.WsStepUnqualifiedItem>  工单工序不良项目统计数据列表
     * <AUTHOR>
     * @date 2021-06-13
     **/
    @DataFilter(isSkip = true)
    List<WsStepUnqualifiedItem> findBySubWorkSheetIdAndFlagAndDeleted(Long subWorkSheetId, boolean flag, Long deleted);

    /**
     * 通过子工单主键ID和工序主键id删除不良汇总
     *
     * @param subWorkSheetId 子工单主键ID
     * @param stepId         工序主键ID
     * @return void
     * <AUTHOR>
     * @date 2021-06-13
     **/
    @Modifying
    @Query("update WsStepUnqualifiedItem wsi set wsi.deleted=wsi.id where wsi.subWorkSheet.id=?1 and wsi.step.id=?2")
    void batchDeleteBySubWorkSheetIdAndsAndStepId(Long subWorkSheetId, Long stepId);

    /**
     * 通过子工单主键ID和工序主键id删除不良汇总
     *
     * @param workSheetId 工单主键ID
     * @param stepId      工序主键ID
     * <AUTHOR>
     */
    @Modifying
    @Query("update WsStepUnqualifiedItem wsi set wsi.deleted=wsi.id where wsi.workSheet.id=?1 and wsi.step.id=?2")
    void batchDeleteByWorkSheetIdAndsAndStepId(Long workSheetId, Long stepId);

    /**
     * 通过子工单主键ID、工序主键ID、不良项目主键ID获取唯一记录
     *
     * @param subWorkSheetId    子工单主键ID
     * @param stepId            工序主键ID
     * @param unqualifiedItemId 不良项目主键ID
     * @param deleted           逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.batch.WsStepUnqualifiedItem>  工单工序不良项目统计数据
     * <AUTHOR>
     * @date 2021-08-27
     **/
    @DataFilter(isSkip = true)
    Optional<WsStepUnqualifiedItem> findBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(Long subWorkSheetId, Long stepId, Long unqualifiedItemId, Long deleted);

    /**
     * 通过工单主键ID、工序主键ID、不良项目主键ID获取唯一记录
     *
     * @param workSheetId       工单主键ID
     * @param stepId            工序主键ID
     * @param unqualifiedItemId 不良项目主键ID
     * @param deleted           逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.batch.WsStepUnqualifiedItem>  工单工序不良项目统计数据
     * <AUTHOR>
     * @date 2021-08-27
     **/
    @DataFilter(isSkip = true)
    Optional<WsStepUnqualifiedItem> findByWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(Long workSheetId, Long stepId, Long unqualifiedItemId, Long deleted);

    /**
     * 通过子工单主键id，工序主键id，不良项目主键id 修改工单工序不良项目统计
     *
     * @param subWorkSheetId    工单主键id
     * @param stepId            工序主键id
     * @param unqualifiedItemId 不良项目主键id
     * @param flag              标识是否已生成在线返修单0:否;1:部分；2全部
     */
    @Modifying
    @Query("update WsStepUnqualifiedItem wsui set wsui.flag = ?4 , wsui.repairCount = wsui.number where   wsui.subWorkSheet.id = ?1 and wsui.step.id = ?2 and wsui.unqualifiedItem.id = ?3 and wsui.deleted = 0")
    void updateByFlag(Long subWorkSheetId, Long stepId, Long unqualifiedItemId, boolean flag);

    /**
     * 通过子工单主键id，工序主键id，不良项目主键id列表 获取多条唯一记录
     *
     * @param subWorkSheetId    子工单主键id
     * @param stepId            工序主键id
     * @param unqualifiedItemId 不良项目主键id
     * @param deleted           逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.WsStepUnqualifiedItem>  工单工序不良项目统计数据列表
     * <AUTHOR>
     * @date 2022/10/11
     */
    @DataFilter(isSkip = true)
    List<WsStepUnqualifiedItem> findBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdInAndDeleted(Long subWorkSheetId, Long stepId, List<Long> unqualifiedItemId, Long deleted);


    /**
     * 通过工单主键id，工序主键id，不良项目主键id列表 获取多条唯一记录
     *
     * @param workSheetId    子工单主键id
     * @param stepId            工序主键id
     * @param unqualifiedItemId 不良项目主键id
     * @param deleted           逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.WsStepUnqualifiedItem>  工单工序不良项目统计数据列表
     * <AUTHOR>
     */
    @DataFilter(isSkip = true)
    List<WsStepUnqualifiedItem> findByWorkSheetIdAndStepIdAndUnqualifiedItemIdInAndDeleted(Long workSheetId, Long stepId, List<Long> unqualifiedItemId, Long deleted);

    /**
     * 通过总工单主键id查询子工单工序生产不良数量
     *
     * @param subWorkSheetId 总工单主键id
     * @param deleted        逻辑删除
     * @return java.lang.Long 不良数量
     */
    @DataFilter(isSkip = true)
    @Query("select sum(w.number) from WsStepUnqualifiedItem w where w.subWorkSheet.id = ?1 and w.deleted = ?2")
    Long findBySubWorkSheetIdAndDeleted(Long subWorkSheetId, Long deleted);

    /**
     * 子工单投产时按照工单、不良项目分组获取工单的不良信息集合
     *
     * @param subWorkSheetId 子工单id
     * @return List<ForwardTraceResultDTO.UnqualifiedItemDetail>
     */
    @Query("select new net.airuima.rbase.web.rest.report.dto.WorkSheetQualifiedRateReportTableItemDTO$UnqualifiedItemInfo(wsui.subWorkSheet.workSheet.serialNumber,wsui.unqualifiedItem.name,wsui.unqualifiedItem.code,sum(wsui.number)) from WsStepUnqualifiedItem wsui where wsui.subWorkSheet.workSheet.id in ?1 and wsui.deleted = 0 group by wsui.subWorkSheet.workSheet.id, wsui.unqualifiedItem.id")
    List<WorkSheetQualifiedRateReportTableItemDTO.UnqualifiedItemInfo> findWorkSheetUnqualifiedItemWhenSubWorkSheet(List<Long> workSheetIds);

    /**
     * 工单投产时按照工单、不良项目分组获取工单的不良信息集合
     *
     * @param workSheetId 工单id
     * @return List<ForwardTraceResultDTO.UnqualifiedItemDetail>
     */
    @Query("select new net.airuima.rbase.web.rest.report.dto.WorkSheetQualifiedRateReportTableItemDTO$UnqualifiedItemInfo(wsui.workSheet.serialNumber,wsui.unqualifiedItem.name,wsui.unqualifiedItem.code,sum(wsui.number)) from WsStepUnqualifiedItem wsui where wsui.workSheet.id in ?1 and wsui.deleted = 0 group by wsui.workSheet.id,wsui.unqualifiedItem.id")
    List<WorkSheetQualifiedRateReportTableItemDTO.UnqualifiedItemInfo> findWorkSheetUnqualifiedItemWhenWorkSheet(List<Long> workSheetIds);
}
