package net.airuima.rbase.repository.procedure.report;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.batch.ContainerDetailFacility;
import net.airuima.rbase.domain.procedure.report.StaffPerform;
import net.airuima.rbase.dto.aps.InProcessScheduleReportDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.rbase.web.rest.report.dto.StepProductCapacityReportChartDTO;
import net.airuima.rbase.web.rest.report.dto.digitalworkshop.WorkCellStepStatisticsDTO;
import net.airuima.rbase.web.rest.report.dto.perform.DistributionChartDataDTO;
import net.airuima.rbase.web.rest.report.dto.perform.StaffRankQueryDataDTO;
import net.airuima.rbase.web.rest.report.dto.perform.StepRankDataDTO;
import net.airuima.rbase.web.rest.report.dto.perform.WorkCellRankDataDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/12/2
 */
@Repository
public interface StaffPerformRepository extends LogicDeleteableRepository<StaffPerform>,
        EntityGraphJpaSpecificationExecutor<StaffPerform>, EntityGraphJpaRepository<StaffPerform, Long> {

    /**
     * 根据批次主键id获取员工产量数据
     *
     * @param batchWorkDetailId 批量工序生产详情主键ID
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.report.StaffPerform> 员工产量列表
     * <AUTHOR>
     * @date 2022/12/5
     */
    @DataFilter(isSkip = true)
    List<StaffPerform> findByBatchWorkDetailIdAndDeleted(Long batchWorkDetailId, Long deleted);

    /**
     * 根据批次主键id获取员工产量数据
     *
     * @param batchWorkDetailId 批量工序生产详情主键ID
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.report.StaffPerform> 员工产量列表
     * <AUTHOR>
     * @date 2022/12/5
     */
    @DataFilter(isSkip = true)
    Optional<StaffPerform> findByBatchWorkDetailIdAndDeletedAndContainerDetailIdIsNullAndSnWorkDetailIdNull(Long batchWorkDetailId, Long deleted);

    /**
     * 根据容器详情主键id获取员工产量数据
     *
     * @param containDetailId 容器详情主键id
     * @param deleted         逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.report.StaffPerform> 员工产量列表
     * <AUTHOR>
     * @date 2022/12/5
     */
    @DataFilter(isSkip = true)
    List<StaffPerform> findByContainerDetailIdAndDeleted(Long containDetailId, Long deleted);

    /**
     * 根据容器详情主键id获取员工产量数据
     *
     * @param containDetailId 容器详情主键id
     * @param deleted         逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.report.StaffPerform> 员工产量列表
     * <AUTHOR>
     * @date 2022/12/5
     */
    @DataFilter(isSkip = true)
    Optional<StaffPerform> findByContainerDetailIdAndDeletedAndSnWorkDetailIdIsNull(Long containDetailId, Long deleted);

    /**
     * 根据sn详情id 获取员工产量数据
     *
     * @param snWorkDetailId sn详情id
     * @param deleted        逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.report.StaffPerform> 员工产量列表
     * <AUTHOR>
     * @date 2022/12/5
     */
    @DataFilter(isSkip = true)
    Optional<StaffPerform> findBySnWorkDetailIdAndDeleted(Long snWorkDetailId, Long deleted);

    /**
     * 通过工位 工序 以及时间段获取员工产量数据
     *
     * @param stepId 工序主键id
     * @param workCellId 工位主键id
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.report.StaffPerform> 员工产量列表
     * <AUTHOR>
     * @date 2022/12/6
     */
    @DataFilter(isSkip = true)
    List<StaffPerform> findByStepIdAndWorkCellIdAndRecordTimeBetweenAndDeleted(Long stepId, Long workCellId, LocalDateTime startTime, LocalDateTime endTime, Long deleted);

    /**
     * 查询报工分布条形图数据 工单投产
     *
     * @param pedigreeId 产品谱系主键id
     * @param workLineId 产线主键id
     * @param staffId    员工主键id
     * @param startTime  查询开始时间
     * @param endTime    查询结束时间
     * @param deleted    删除标记
     * @return java.util.List<net.airuima.web.rest.report.dto.DistributionChartDataDTO> 报工分布条形图数据
     */
    @EntityGraph(value = "staffPerformEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.perform.DistributionChartDataDTO(DATE_FORMAT(s.recordTime,'%Y-%m-%d'), sum(s.qualifiedNumber + s.unqualifiedNumber) ) from StaffPerform s where (?1 is null or s.workSheet.pedigree.id = ?1) and (?2 is null or s.workSheet.workLine.id = ?2) and (?3 is null or s.staffId = ?3) and  s.subWorkSheet is null and ( ?4 is null or s.recordTime >= ?4  ) and ( ?5 is null or s.recordTime < ?5 ) and s.deleted = ?6 group by DATE_FORMAT(s.recordTime,'%Y-%m-%d') order by DATE_FORMAT(s.recordTime,'%Y-%m-%d') asc")
    List<DistributionChartDataDTO> findWorkSheetStaffPerformStatisticDistributionChart(Long pedigreeId, Long workLineId, Long staffId, LocalDateTime startTime, LocalDateTime endTime, Long deleted);

    /**
     * 查询报工分布条形图数据 子工单投产
     *
     * @param pedigreeId 产品谱系主键id
     * @param workLineId 产线主键id
     * @param staffId    员工主键id
     * @param startTime  查询开始时间
     * @param endTime    查询结束时间
     * @param deleted    删除标记
     * @return java.util.List<net.airuima.web.rest.report.dto.DistributionChartDataDTO> 报工分布条形图数据
     */
    @EntityGraph(value = "staffPerformEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.perform.DistributionChartDataDTO(DATE_FORMAT(s.recordTime,'%Y-%m-%d'), sum(s.qualifiedNumber + s.unqualifiedNumber) ) from StaffPerform s where (?1 is null or s.subWorkSheet.workSheet.pedigree.id = ?1) and (?2 is null or s.subWorkSheet.workSheet.workLine.id = ?2) and (?3 is null or s.staffId = ?3) and s.subWorkSheet is not null and ( ?4 is null or s.recordTime >= ?4  ) and ( ?5 is null or s.recordTime < ?5 ) and s.deleted = ?6 group by DATE_FORMAT(s.recordTime,'%Y-%m-%d') order by DATE_FORMAT(s.recordTime,'%Y-%m-%d') asc")
    List<DistributionChartDataDTO> findSubWorkSheetStaffPerformStatisticDistributionChart(Long pedigreeId, Long workLineId, Long staffId, LocalDateTime startTime, LocalDateTime endTime, Long deleted);


    /**
     * 工序产出推移图数据 子工单投产
     *
     * @param pedigreeId 产品谱系主键id
     * @param workFlowId  工艺路线id
     * @param workLineId 产线主键id
     * @param organizationId  员工主键id
     * @param startTime  查询开始时间
     * @param endTime    查询结束时间
     * @param deleted    删除标记
     * @return java.util.List<net.airuima.web.rest.report.dto.StepProductCapacityReportChartDTO> 工序产出推移图数据
     */
    @EntityGraph(value = "staffPerformEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.StepProductCapacityReportChartDTO('actual',s.step.id, s.step.name, sum(s.qualifiedNumber) ) from StaffPerform s where (?1 is null or s.subWorkSheet.workSheet.pedigree.id = ?1) and (?2 is null or s.subWorkSheet.workSheet.workFlow.id = ?2)  and (?3 is null or s.subWorkSheet.workSheet.workLine.id = ?3) and (?4 is null or s.subWorkSheet.workSheet.organizationId = ?4) and s.subWorkSheet is not null and ( ?5 is null or s.recordTime >= ?5  ) and ( ?6 is null or s.recordTime < ?6 ) and s.deleted = ?7 group by s.step.name order by sum(s.qualifiedNumber) desc")
    List<StepProductCapacityReportChartDTO> findSubWorkSheetStepProductionCapacityReportChart(Long pedigreeId, Long workFlowId, Long workLineId, Long organizationId, LocalDateTime startTime, LocalDateTime endTime, Long deleted);


    /**
     * 工序组产出推移图数据 子工单投产
     *
     * @param pedigreeId 产品谱系主键id
     * @param workLineId 产线主键id
     * @param organizationId  员工主键id
     * @param startTime  查询开始时间
     * @param endTime    查询结束时间
     * @param deleted    删除标记
     * @return java.util.List<net.airuima.web.rest.report.dto.StepProductCapacityReportChartDTO> 工序组产出推移图数据
     */
    @EntityGraph(value = "staffPerformEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.StepProductCapacityReportChartDTO('actual', s.step.stepGroup.id, s.step.stepGroup.name, sum(s.qualifiedNumber) ) from StaffPerform s where (?1 is null or s.subWorkSheet.workSheet.pedigree.id = ?1) and (?2 is null or s.subWorkSheet.workSheet.workLine.id = ?2) and (?3 is null or s.subWorkSheet.workSheet.organizationId = ?3) and s.subWorkSheet is not null and ( ?4 is null or s.recordTime >= ?4  ) and ( ?5 is null or s.recordTime < ?5 ) and s.deleted = ?6 group by s.step.stepGroup.name order by sum(s.qualifiedNumber) desc")
    List<StepProductCapacityReportChartDTO> findSubWorkSheetStepGroupProductionCapacityReportChart(Long pedigreeId, Long workLineId, Long organizationId, LocalDateTime startTime, LocalDateTime endTime, Long deleted);


    /**
     * 工序产出推移图数据 工单投产
     *
     * @param pedigreeId 产品谱系主键id
     * @param workFlowId  工艺路线id
     * @param workLineId 产线主键id
     * @param organizationId  员工主键id
     * @param startTime  查询开始时间
     * @param endTime    查询结束时间
     * @param deleted    删除标记
     * @return java.util.List<net.airuima.web.rest.report.dto.StepProductCapacityReportChartDTO> 工序产出推移图数据
     */
    @EntityGraph(value = "staffPerformEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.StepProductCapacityReportChartDTO('actual',s.step.id, s.step.name, sum(s.qualifiedNumber) ) from StaffPerform s where (?1 is null or s.workSheet.pedigree.id = ?1) and  (?2 is null or s.workSheet.workFlow.id = ?2) and (?3 is null or s.workSheet.workLine.id = ?3) and (?4 is null or s.workSheet.organizationId = ?4) and s.subWorkSheet is  null and ( ?5 is null or s.recordTime >= ?5  ) and ( ?6 is null or s.recordTime < ?6 ) and s.deleted = ?7 group by s.step.name order by sum(s.qualifiedNumber) desc")
    List<StepProductCapacityReportChartDTO> findWorkSheetStepProductionCapacityReportChart(Long pedigreeId, Long workFlowId, Long workLineId, Long organizationId, LocalDateTime startTime, LocalDateTime endTime, Long deleted);

    /**
     * 工序组产出推移图数据 工单投产
     *
     * @param pedigreeId 产品谱系主键id
     * @param workLineId 产线主键id
     * @param organizationId  员工主键id
     * @param startTime  查询开始时间
     * @param endTime    查询结束时间
     * @param deleted    删除标记
     * @return java.util.List<net.airuima.web.rest.report.dto.StepProductCapacityReportChartDTO> 工序组产出推移图数据
     */
    @EntityGraph(value = "staffPerformEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.StepProductCapacityReportChartDTO('actual',s.step.stepGroup.id, s.step.stepGroup.name, sum(s.qualifiedNumber) ) from StaffPerform s where (?1 is null or s.workSheet.pedigree.id = ?1) and (?2 is null or s.workSheet.workLine.id = ?2) and (?3 is null or s.workSheet.organizationId = ?3) and s.subWorkSheet is  null and ( ?4 is null or s.recordTime >= ?4  ) and ( ?5 is null or s.recordTime < ?5 ) and s.deleted = ?6 group by s.step.stepGroup.name order by sum(s.qualifiedNumber) desc")
    List<StepProductCapacityReportChartDTO> findWorkSheetStepGroupProductionCapacityReportChart(Long pedigreeId, Long workLineId, Long organizationId, LocalDateTime startTime, LocalDateTime endTime, Long deleted);

    /**
     * 查询员工报工排行榜查询数据 (工单投产)
     *
     * @param pedigreeId 产品谱系主键id
     * @param workLineId 产线主键id
     * @param staffId    员工主键id
     * @param startTime  查询开始时间
     * @param endTime    查询结束时间
     * @param deleted    删除标记
     * @return org.springframework.data.domain.Page<net.airuima.web.rest.report.dto.DistributionChartDataDTO> 员工报工排行榜查询数据
     */
    @EntityGraph(value = "staffPerformEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.perform.StaffRankQueryDataDTO(s.staffId, sum(s.qualifiedNumber + s.unqualifiedNumber),sum(s.workHour) ) from StaffPerform s where (?1 is null or s.workSheet.pedigree.id = ?1) and (?2 is null or s.workSheet.workLine.id = ?2) and (?3 is null or s.staffId = ?3)  and s.subWorkSheet is null and ( ?4 is null or s.recordTime >= ?4  ) and ( ?5 is null or s.recordTime < ?5 ) and s.deleted = ?6 group by s.staffId order by sum(s.qualifiedNumber + s.unqualifiedNumber) desc")
    @FetchMethod
    Page<StaffRankQueryDataDTO> findWorkSheetStaffPerformStatisticStaffRankChart(Long pedigreeId, Long workLineId, Long staffId, LocalDateTime startTime, LocalDateTime endTime, Long deleted, Pageable pageable);


    /**
     * 查询工位报工排行榜数据 (工单投产)
     *
     * @param pedigreeId 产品谱系主键id
     * @param workLineId 产线主键id
     * @param staffId    员工主键id
     * @param startTime  查询开始时间
     * @param endTime    查询结束时间
     * @param deleted    删除标记
     * @return org.springframework.data.domain.Page<net.airuima.web.rest.report.dto.DistributionChartDataDTO> 工位报工排行榜数据
     */
    @EntityGraph(value = "staffPerformEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.perform.WorkCellRankDataDTO(s.workCell.name,s.workCell.code, sum(s.qualifiedNumber + s.unqualifiedNumber),sum(s.workHour) ) from StaffPerform s where (?1 is null or s.workSheet.pedigree.id = ?1) and (?2 is null or s.workSheet.workLine.id = ?2) and (?3 is null or s.staffId = ?3)  and s.subWorkSheet is null and ( ?4 is null or s.recordTime >= ?4  ) and ( ?5 is null or s.recordTime < ?5 ) and s.deleted = ?6 group by s.workCell.id order by sum(s.qualifiedNumber + s.unqualifiedNumber) desc")
    Page<WorkCellRankDataDTO> findWorkSheetStaffPerformStatisticWorkCellRankChart(Long pedigreeId, Long workLineId, Long staffId, LocalDateTime startTime, LocalDateTime endTime, Long deleted,Pageable pageable);


    /**
     * 查询工序报工排行榜数据 (工单投产)
     *
     * @param pedigreeId 产品谱系id
     * @param workLineId 产线主键id
     * @param staffId    员工主键id
     * @param startTime  查询开始时间
     * @param endTime    查询结束时间
     * @param deleted    删除标记
     * @return org.springframework.data.domain.Page<net.airuima.web.rest.report.dto.StepRankDataDTO> 工序报工排行榜数据
     */
    @EntityGraph(value = "staffPerformEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.perform.StepRankDataDTO(s.step.name,s.step.code, sum(s.qualifiedNumber + s.unqualifiedNumber),sum(s.workHour) ) from StaffPerform s where (?1 is null or s.workSheet.pedigree.id = ?1) and (?2 is null or s.workSheet.workLine.id = ?2) and (?3 is null or s.staffId = ?3)  and  s.subWorkSheet is null and ( ?4 is null or s.recordTime >= ?4  ) and ( ?5 is null or s.recordTime < ?5 ) and s.deleted = ?6 group by s.step.id order by sum(s.qualifiedNumber + s.unqualifiedNumber) desc")
    Page<StepRankDataDTO> findWorkSheetStaffPerformStatisticStepRankChart(Long pedigreeId, Long workLineId, Long staffId, LocalDateTime startTime, LocalDateTime endTime, Long deleted,Pageable pageable);


    /**
     * 查询员工报工排行榜查询数据 (子工单投产)
     *
     * @param pedigreeId 产品谱系主键id
     * @param workLineId 产线主键id
     * @param staffId    员工主键id
     * @param startTime  查询开始时间
     * @param endTime    查询结束时间
     * @param deleted    删除标记
     * @return org.springframework.data.domain.Page<net.airuima.web.rest.report.dto.DistributionChartDataDTO> 员工报工排行榜查询数据
     */
    @EntityGraph(value = "staffPerformEntityGraph",type = EntityGraph.EntityGraphType.LOAD)
    @Query("select new net.airuima.rbase.web.rest.report.dto.perform.StaffRankQueryDataDTO(s,s.staffId, sum(s.qualifiedNumber + s.unqualifiedNumber),sum(s.workHour) ) from StaffPerform s where (?1 is null or s.subWorkSheet.workSheet.pedigree.id = ?1) and (?2 is null or s.subWorkSheet.workSheet.workLine.id = ?2) and (?3 is null or s.staffId = ?3)  and s.subWorkSheet is not null and ( ?4 is null or s.recordTime >= ?4  ) and ( ?5 is null or s.recordTime < ?5 ) and s.deleted = ?6 group by s.staffId order by sum(s.qualifiedNumber + s.unqualifiedNumber) desc")
    @FetchMethod
    Page<StaffRankQueryDataDTO> findSubWorkSheetStaffPerformStatisticStaffRankChart(Long pedigreeId, Long workLineId, Long staffId, LocalDateTime startTime, LocalDateTime endTime, Long deleted,Pageable pageable);


    /**
     * 查询工位报工排行榜数据 (子工单投产)
     *
     * @param pedigreeId 产品谱系主键id
     * @param workLineId 产线主键id
     * @param staffId    员工主键id
     * @param startTime  查询开始时间
     * @param endTime    查询结束时间
     * @param deleted    删除标记
     * @return org.springframework.data.domain.Page<net.airuima.web.rest.report.dto.DistributionChartDataDTO> 工位报工排行榜数据
     */
    @EntityGraph(value = "staffPerformEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.perform.WorkCellRankDataDTO(s,s.workCell.name,s.workCell.code, sum(s.qualifiedNumber + s.unqualifiedNumber),sum(s.workHour) ) from StaffPerform s where (?1 is null or s.subWorkSheet.workSheet.pedigree.id = ?1) and (?2 is null or s.subWorkSheet.workSheet.workLine.id = ?2) and (?3 is null or s.staffId = ?3)  and s.subWorkSheet is not null and ( ?4 is null or s.recordTime >= ?4  ) and ( ?5 is null or s.recordTime < ?5 ) and s.deleted = ?6 group by s.workCell.id order by sum(s.qualifiedNumber + s.unqualifiedNumber) desc")
    Page<WorkCellRankDataDTO> findSubWorkSheetStaffPerformStatisticWorkCellRankChart(Long pedigreeId, Long workLineId, Long staffId, LocalDateTime startTime, LocalDateTime endTime, Long deleted,Pageable pageable);


    /**
     * 查询工序报工排行榜数据 (子工单投产)
     *
     * @param pedigreeId 产品谱系主键id
     * @param workLineId 产线主键id
     * @param staffId    员工主键id
     * @param startTime  查询开始时间
     * @param endTime    查询结束时间
     * @param deleted    删除标记
     * @return org.springframework.data.domain.Page<net.airuima.web.rest.report.dto.StepRankDataDTO> 工序报工排行榜数据
     */
    @EntityGraph(value = "staffPerformEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.perform.StepRankDataDTO(s,s.step.name,s.step.code, sum(s.qualifiedNumber + s.unqualifiedNumber),sum(s.workHour) ) from StaffPerform s where (?1 is null or s.subWorkSheet.workSheet.pedigree.id = ?1) and (?2 is null or s.subWorkSheet.workSheet.workLine.id = ?2) and (?3 is null or s.staffId = ?3)  and  s.subWorkSheet is not null and ( ?4 is null or s.recordTime >= ?4  ) and ( ?5 is null or s.recordTime < ?5 ) and s.deleted = ?6 group by s.step.id order by sum(s.qualifiedNumber + s.unqualifiedNumber) desc")
    Page<StepRankDataDTO> findSubWorkSheetStaffPerformStatisticStepRankChart(Long pedigreeId, Long workLineId, Long staffId, LocalDateTime startTime, LocalDateTime endTime, Long deleted,Pageable pageable);



    /**
     * 在制工单进度报表结果-子工单
     * @param subWorkSheetIds 子工单主键id列表
     * @return java.util.List<net.airuima.dto.aps.InProcessScheduleReportDTO> 在制工单进度报表结果
     */
    @DataFilter(isSkip = true)
    @EntityGraph(value = "staffPerformEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.dto.aps.InProcessScheduleReportDTO(" +
            "sp.subWorkSheet.serialNumber,sp.subWorkSheet.workSheet.pedigree.code,sp.subWorkSheet.workSheet.pedigree.name," +
            "sp.subWorkSheet.createdDate,sp.subWorkSheet.planStartDate,sp.subWorkSheet.planEndDate," +
            "sp.subWorkSheet.number,sp.workCell.code,sp.workCell.name," +
            "sp.step.code,sp.step.name,sum(sp.qualifiedNumber),bwd.finish,sp.subWorkSheet.workSheet.pedigree.specification) " +
            "from StaffPerform sp " +
            "inner join BatchWorkDetail bwd on sp.batchWorkDetailId = bwd.id" +
            " where sp.deleted = 0  and sp.subWorkSheet.id in ?1  and sp.subWorkSheet.workSheet.category > -1" +
            "group by sp.step.id,sp.workCell.id ,sp.subWorkSheet.id")
    List<InProcessScheduleReportDTO> findInProcessScheduleBySubWorkSheet(List<Long> subWorkSheetIds);


    /**
     *  在制工单进度报表结果 -工单
     * @param worksheetIds 工单主键id列表
     * @return java.util.List<net.airuima.dto.aps.InProcessScheduleReportDTO> 在制工单进度报表结果
     */
    @DataFilter(isSkip = true)
    @EntityGraph(value = "staffPerformEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.dto.aps.InProcessScheduleReportDTO(" +
            "sp.workSheet.serialNumber,sp.workSheet.pedigree.code,sp.workSheet.pedigree.name," +
            "sp.workSheet.createdDate,sp.workSheet.planStartDate,sp.workSheet.planEndDate," +
            "sp.workSheet.number,sp.workCell.code,sp.workCell.name," +
            "sp.step.code,sp.step.name,sum(sp.qualifiedNumber),bwd.finish,sp.subWorkSheet.workSheet.pedigree.specification) " +
            "from StaffPerform sp " +
            "inner join BatchWorkDetail bwd on sp.batchWorkDetailId = bwd.id" +
            " where sp.deleted = 0  and sp.workSheet.id in ?1 and sp.workSheet.category > -1" +
            "group by sp.step.id,sp.workCell.id ,sp.workSheet.id")
    List<InProcessScheduleReportDTO> findInProcessScheduleByWorkSheet(List<Long> worksheetIds);

    /**
     * 通过产线主键id 获取开始结束时间内，子工单工位工序 投产详情数据
     * @param workLineId 产线主键id
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return java.util.List<WorkCellStepStatisticsDTO.WorkCellStepProductionInfo> 投产详情数据
     */
    @EntityGraph(value = "staffPerformEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.digitalworkshop.WorkCellStepStatisticsDTO$WorkCellStepProductionInfo(sp.workCell,sp.step,count(DISTINCT sp.subWorkSheet.id),sum(sp.inputNumber),sum(sp.qualifiedNumber),sum(sp.unqualifiedNumber)) from StaffPerform sp where sp.subWorkSheet.workLine.id = ?1 and sp.subWorkSheet.workSheet.category > -1 and (sp.recordTime >= ?2) and (sp.recordTime < ?3) and sp.deleted = 0 group by sp.step.id,sp.workCell.id")
    List<WorkCellStepStatisticsDTO.WorkCellStepProductionInfo> findBySubWorkSheetWorkCellStepProductionInfo(Long workLineId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 通过产线主键主键id 获取开始结束时间内，工单工位工序 投产详情数据
     * @param workLineId 产线主键主键id
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return java.util.List<WorkCellStepStatisticsDTO.WorkCellStepProductionInfo> 投产详情数据
     */
    @EntityGraph(value = "staffPerformEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select new net.airuima.rbase.web.rest.report.dto.digitalworkshop.WorkCellStepStatisticsDTO$WorkCellStepProductionInfo(sp.workCell,sp.step,count(DISTINCT sp.workSheet.id),sum(sp.inputNumber),sum(sp.qualifiedNumber),sum(sp.unqualifiedNumber)) from StaffPerform sp where sp.workSheet.workLine.id = ?1 and sp.workSheet.category > -1 and (sp.recordTime >= ?2) and (sp.recordTime < ?3) and sp.deleted = 0 group by sp.step.id,sp.workCell.id")
    List<WorkCellStepStatisticsDTO.WorkCellStepProductionInfo> findByWorkSheetWorkCellStepProductionInfo(Long workLineId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     *
     * 通过工位主键ID及逻辑删除获取最新记录
     * @param workCellId 工位主键ID
     * @param deleted 逻辑删除
     * @return net.airuima.domain.procedure.report.StaffPerform 员工生产明细
     */
    @DataFilter(isSkip = true)
    StaffPerform findTop1ByWorkCellIdAndDeletedOrderByIdDesc(Long workCellId,Long deleted);


    /**
     * 通过员工id获取在一定范围内使用该员工的工单
     *
     * @param staffId 员工id
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return java.util.List< net.airuima.domain.procedure.report.StaffPerform> 员工生产明细列表
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    @Query("select s from StaffPerform s where s.staffId = ?1 and s.recordTime >= ?2 and s.recordTime <= ?3 and s.deleted = 0L")
    List<StaffPerform> findByStaffIdAndStartDateAndEndDate(Long staffId, LocalDateTime startDate, LocalDateTime endDate);
}
