package net.airuima.rbase.repository.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.rbase.web.rest.report.dto.digitalworkshop.ProductionCycleStatisticsDTO;
import net.airuima.rbase.web.rest.report.dto.forwardtracereport.BatchForwardTraceStepDetailExportDTO;
import net.airuima.rbase.web.rest.report.dto.forwardtracereport.ContainerForwardTraceStepDetailExportDTO;
import net.airuima.rbase.web.rest.report.dto.forwardtracereport.SnForwardTraceStepDetailExportDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 批量工序生产详情Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface BatchWorkDetailRepository extends LogicDeleteableRepository<BatchWorkDetail>,
        EntityGraphJpaSpecificationExecutor<BatchWorkDetail>, EntityGraphJpaRepository<BatchWorkDetail, Long> {

    /**
     * 通过主键ID获取记录
     *
     * @param id      主键id
     * @param deleted 逻辑删除
     * @return net.airuima.domain.procedure.batch.BatchWorkDetail 批量工序生产详情
     */
    @DataFilter(isSkip = true)
    BatchWorkDetail findByIdAndDeleted(Long id, Long deleted);

    /**
     * 根据子工单获取工序详情总数
     *
     * @param subWorkSheetId 子工单主键ID
     * @param deleted        是否删除
     * @return java.lang.Long  工序详情总数
     */
    @DataFilter(isSkip = true)
    Long countBySubWorkSheetIdAndDeleted(Long subWorkSheetId, Long deleted);

    /**
     * 根据总工单主键ID获取工序详情数据条数
     *
     * @param workSheetId 总工单主键ID
     * @param deleted     总工单主键ID
     * @return java.lang.Long  工序详情数据条数
     */
    @DataFilter(isSkip = true)
    @Query("select count(id) from BatchWorkDetail where subWorkSheet.workSheet.id=?1 and deleted=?2")
    Long countBySubWorkSheetWorkSheetIdAndDeleted(Long workSheetId, Long deleted);

    /**
     * 通过子工单id查找工单生产详情
     *
     * @param subWorkSheetId 子工单id
     * @param deleted        逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.BatchWorkDetail> 工单生产详情列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @EntityGraph(value = "batchWorkDetailEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    List<BatchWorkDetail> findBySubWorkSheetIdAndDeleted(Long subWorkSheetId, Long deleted);

    /**
     * 通过子工单ID、工序ID列表及完成状态获取详情
     * @param subWorkSheetId 子工单ID
     * @param stepIdList 工序ID列表
     * @param finish 是否完成
     * @param deleted 逻辑删除
     * @return  List<BatchWorkDetail>
     */
    @DataFilter(isSkip = true)
    List<BatchWorkDetail> findBySubWorkSheetIdAndStepIdInAndFinishAndDeleted(Long subWorkSheetId,List<Long> stepIdList,int finish, Long deleted);

    /**
     * 通过工单ID、工序ID列表及完成状态获取详情
     * @param workSheetId 子工单ID
     * @param stepIdList 工序ID列表
     * @param finish 是否完成
     * @param deleted 逻辑删除
     * @return  List<BatchWorkDetail>
     */
    @DataFilter(isSkip = true)
    List<BatchWorkDetail> findByWorkSheetIdAndStepIdInAndFinishAndDeleted(Long workSheetId,List<Long> stepIdList,int finish, Long deleted);


    @FetchMethod
    @EntityGraph(value = "batchWorkDetailEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select bwd from BatchWorkDetail bwd where bwd.subWorkSheet.id=?1 and bwd.deleted=?2")
    List<BatchWorkDetail> findBySubWorkSheetIdAndDeletedWhenDataFilter(Long subWorkSheetId, Long deleted);

    /**
     * 通过工单主键ID及逻辑删除获取生产详情
     *
     * @param workSheetId 工单主键ID
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.BatchWorkDetail> 工单生产详情列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @EntityGraph(value = "batchWorkDetailEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    List<BatchWorkDetail> findByWorkSheetIdAndDeleted(Long workSheetId, Long deleted);

    @FetchMethod
    @EntityGraph(value = "batchWorkDetailEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select bwd from BatchWorkDetail bwd where bwd.workSheet.id=?1 and bwd.deleted=?2")
    List<BatchWorkDetail> findByWorkSheetIdAndDeletedWhenDataFilter(Long workSheetId, Long deleted);

    /**
     * 通过子工单主键id和工序查找唯一批量生产详情
     *
     * @param subWsId 子工单主键id
     * @param stepId  工序主键id
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.batch.BatchWorkDetail> 工单生产详情
     */
    @DataFilter(isSkip = true)
    Optional<BatchWorkDetail> findBySubWorkSheetIdAndStepIdAndDeleted(Long subWsId, Long stepId, Long deleted);


    /**
     * 通过子工单主键id和工序查找唯一批量生产详情
     *
     * @param subWsId 子工单主键id
     * @param stepId  工序主键id
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.batch.BatchWorkDetail> 工单生产详情
     */
    @DataFilter(isSkip = true)
    Optional<BatchWorkDetail> findByStepIdAndSubWorkSheetIdAndDeleted(Long stepId, Long subWsId, Long deleted);

    /**
     * 通过工单主键id和工序查找唯一批量生产详情
     *
     * @param workSheetId 工单主键id
     * @param stepId      工序主键id
     * @param deleted     逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.batch.BatchWorkDetail> 工单生产详情
     */
    @DataFilter(isSkip = true)
    Optional<BatchWorkDetail> findByWorkSheetIdAndStepIdAndDeleted(Long workSheetId, Long stepId, Long deleted);

    /**
     * 通过工单主键id和工序查找唯一批量生产详情
     *
     * @param workSheetId 工单主键id
     * @param stepId      工序主键id
     * @param deleted     逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.batch.BatchWorkDetail> 工单生产详情
     */
    @DataFilter(isSkip = true)
    Optional<BatchWorkDetail> findByStepIdAndWorkSheetIdAndDeleted(Long stepId, Long workSheetId, Long deleted);

    /**
     * 通过工单主键ID、工序主键ID列表及逻辑删除获取详情列表
     *
     * @param workSheetId 工单主键ID
     * @param stepIdList  工序主键ID列表
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.BatchWorkDetail> 工单生产详情列表
     */
    @DataFilter(isSkip = true)
    List<BatchWorkDetail> findByWorkSheetIdAndStepIdInAndDeleted(Long workSheetId, List<Long> stepIdList, Long deleted);

    /**
     * 通过子工单主键ID、工序主键ID列表获取生产详情信息
     *
     * @param subWsId    子工单主键ID
     * @param stepIdList 工序主键ID列表
     * @param deleted    逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.BatchWorkDetail> 工单生产详情列表
     * <AUTHOR>
     * @date 2021-01-18
     **/
    @DataFilter(isSkip = true)
    List<BatchWorkDetail> findBySubWorkSheetIdAndStepIdInAndDeleted(Long subWsId, List<Long> stepIdList, Long deleted);

    /**
     * 通过子工单主键id数组查找生产工作详情 分页
     *
     * @param subWsIdList 子工单主键id数组
     * @param deleted     逻辑删除
     * @param pageable    分页
     * @return org.springframework.data.domain.Page<net.airuima.domain.procedure.batch.BatchWorkDetail> 工单生产详情分页
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    Page<BatchWorkDetail> findBySubWorkSheetIdInAndDeleted(List<Long> subWsIdList, Long deleted, Pageable pageable);

    /**
     * 通过子工单id数组查找生产工作详情
     *
     * @param subWsIdList 子工单id数组
     * @param deleted     逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.BatchWorkDetail> 工单生产详情列表
     */
    @DataFilter(isSkip = true)
    BatchWorkDetail findTop1BySubWorkSheetIdInAndDeleted(List<Long> subWsIdList, Long deleted);

    /**
     * 通过子工单主键ID获取最新生成的批量详情记录
     *
     * @param subWorkSheetId 子工单主键ID
     * @param deleted        逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.batch.BatchWorkDetail> 工单生产详情
     * <AUTHOR>
     * @date 2021-06-03
     **/
    @DataFilter(isSkip = true)
    Optional<BatchWorkDetail> findTop1BySubWorkSheetIdAndDeletedOrderByIdDesc(Long subWorkSheetId, Long deleted);

    /**
     * 通过工单主键ID获取最新生成的批量详情记录
     *
     * @param workSheetId 工单主键ID
     * @param deleted     逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.batch.BatchWorkDetail> 工单生产详情
     * <AUTHOR>
     * @date 2022/12/30
     **/
    @DataFilter(isSkip = true)
    Optional<BatchWorkDetail> findTop1ByWorkSheetIdAndDeletedOrderByIdDesc(Long workSheetId, Long deleted);

    /**
     * 通过子工单ID获取最新生成以完成的批量详情记录
     *
     * @param subWorkSheetId 子工单ID
     * @param deleted        逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.batch.BatchWorkDetail> 工单生产详情
     * <AUTHOR>
     * @date 2021-06-03
     **/
    @DataFilter(isSkip = true)
    Optional<BatchWorkDetail> findTop1BySubWorkSheetIdAndFinishAndDeletedOrderByIdDesc(Long subWorkSheetId, int finish, Long deleted);


    /**
     * 通过工位主键ID+删除标识，根据完成时间倒序排列，取最新一条工位生产记录
     *
     * @param workCellId 工位主键ID
     * @param deleted    删除标识
     * @return net.airuima.domain.procedure.batch.BatchWorkDetail 工单生产详情
     * <AUTHOR>
     * @date 2022/9/15
     **/
    @DataFilter(isSkip = true)
    BatchWorkDetail findTop1ByWorkCellIdAndDeletedOrderByEndDateDesc(Long workCellId, Long deleted);

    /**
     * 通过工单主键id查找工单生产详情
     *
     * @param workSheetId 工单主键ID
     * @param deleted     删除标识
     * @return java.util.List<net.airuima.domain.procedure.batch.BatchWorkDetail> 工单生产详情列表
     * <AUTHOR>
     * @date 2022/11/9
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    List<BatchWorkDetail> findByWorkSheetIdAndSubWorkSheetIdIsNullAndDeleted(Long workSheetId, Long deleted);

    /**
     * 获取子工单列表，以及工序 查找生产详情
     *
     * @param subWorkSheetIds 子工单主键id列表
     * @param stepId          工序主键id
     * @param deleted         逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.BatchWorkDetail> 工单生产详情列表
     * <AUTHOR>
     * @date 2023/3/18
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<BatchWorkDetail> findBySubWorkSheetIdInAndStepIdAndDeleted(List<Long> subWorkSheetIds, Long stepId, Long deleted);

    /**
     * 根据子工单和工序类型查询最新的工单生产详情
     *
     * @param subWorkSheetId 工单主键ID
     * @param Category       工序类型
     * @param deleted        逻辑删除
     * @return net.airuima.domain.procedure.batch.BatchWorkDetail 工单生产详情
     */
    @DataFilter(isSkip = true)
    BatchWorkDetail findTop1BySubWorkSheetIdAndStepCategoryAndDeletedOrderByIdDesc(Long subWorkSheetId, int Category, Long deleted);

    /**
     * 根据子工单和工序类型查询最新的工单生产详情
     *
     * @param workSheetId 单主键id
     * @param category 类型
     * @param deleted 删除标记
     * @return net.airuima.domain.procedure.batch.BatchWorkDetail 工单生产详情
     * <AUTHOR>
     * @date 2023/3/30
     */
    @DataFilter(isSkip = true)
    BatchWorkDetail findTop1ByWorkSheetIdAndStepCategoryAndDeletedOrderByIdDesc(Long workSheetId, int category, Long deleted);

    /**
     * 通过工单主键id 和 完成状态 获取 对应完成状态的 批次详情列表
     *
     * @param wsId    工单主键id
     * @param finish  1:完成;0:未完成
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.BatchWorkDetail> 工单生产详情列表
     */
    @DataFilter(isSkip = true)
    @EntityGraph(value = "batchWorkDetailEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    List<BatchWorkDetail> findByWorkSheetIdAndFinishAndDeleted(Long wsId, int finish, Long deleted);

    /**
     * 通过子工单主键id 和 完成状态 获取 对应完成状态的 批次详情列表
     *
     * @param subWorkSheetId 子工单主键id
     * @param finish         1:完成;0:未完成
     * @param deleted        逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.BatchWorkDetail> 工单生产详情列表
     */
    @DataFilter(isSkip = true)
    @EntityGraph(value = "batchWorkDetailEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    List<BatchWorkDetail> findBySubWorkSheetIdAndFinishAndDeleted(Long subWorkSheetId, int finish, Long deleted);


    /**
     * 通过子工单 获取未被删除的 不良数量
     *
     * @param subWorkSheetId 子工单主键ID
     * @return java.lang.Integer 不良数量
     * <AUTHOR>
     * @date 2023/4/27
     */
    @DataFilter(isSkip = true)
    @Query("SELECT SUM(d.unqualifiedNumber) FROM BatchWorkDetail d WHERE d.subWorkSheet.id = ?1 and d.deleted = 0")
    Integer sumUnqualifiedNumberBySubWorkSheetId(Long subWorkSheetId);

    /**
     * 通过工单 获取未被删除的 不良数量
     *
     * @param workSheetId 工单主键id
     * @return java.lang.Integer 不良数量
     * <AUTHOR>
     * @date 2023/4/27
     */
    @DataFilter(isSkip = true)
    @Query("SELECT SUM(d.unqualifiedNumber) FROM BatchWorkDetail d WHERE d.workSheet.id = ?1 and d.deleted = 0")
    Integer sumUnqualifiedNumberByWorkSheetId(Long workSheetId);

    /**
     * 正向追溯工单详情-批量(子工单)
     * @param wsId 子工单id
     * @return List<BatchForwardTraceStepDetailExportDTO>
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select new net.airuima.rbase.web.rest.report.dto.forwardtracereport.BatchForwardTraceStepDetailExportDTO(" +
            "bwd.subWorkSheet.serialNumber,bwd.step.code, bwd.step.name, bwd.startDate ,bwd.endDate, bwd.inputNumber," +
            "bwd.qualifiedNumber ,bwd.unqualifiedNumber,bwd.operatorId , bwd.workCell.code , bwd.workCell.name ," +
            "dmb.materialId , dmb.materialBatch , dmb.number ,wp.code , wp.name ," +
            "bwd.inputNumber , bwd.qualifiedNumber,bwf.facilityId,wsstepun.unqualifiedItem.code,wsstepun.unqualifiedItem.name,wsstepun.number,bwd.workHour,bwd.custom1,bwd.custom2) " +
            "from BatchWorkDetail bwd " +
            "left join BatchWorkDetailMaterialBatch dmb on bwd.id = dmb.batchWorkDetail.id " +
            "left join BatchWorkDetailWearingPart dwp on bwd.id = dwp.batchWorkDetail.id " +
            "left join BatchWorkDetailFacility bwf on bwd.id = bwf.batchWorkDetail.id " +
            "left join WearingPart wp on dwp.wearingPart.id = wp.id " +
            "left join WsStepUnqualifiedItem wsstepun on (wsstepun.subWorkSheet.id = bwd.subWorkSheet.id and wsstepun.step.id = bwd.step.id) " +
            "left join UnqualifiedItem  unq on unq.id = wsstepun.unqualifiedItem.id "+
            "where bwd.subWorkSheet.id = ?1 and bwd.deleted = 0l")
    List<BatchForwardTraceStepDetailExportDTO> findByForwardTraceReportExportBatchAndSubWorkSheet(Long wsId);

    /**
     * 正向追溯工单详情-批量(工单)
     * @param wsId 工单id
     * @return List<BatchForwardTraceStepDetailExportDTO>
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select new net.airuima.rbase.web.rest.report.dto.forwardtracereport.BatchForwardTraceStepDetailExportDTO(" +
            "bwd.subWorkSheet.serialNumber,bwd.step.code, bwd.step.name, bwd.startDate ,bwd.endDate, bwd.inputNumber," +
            "bwd.qualifiedNumber ,bwd.unqualifiedNumber,bwd.operatorId , bwd.workCell.code , bwd.workCell.name ," +
            "dmb.materialId , dmb.materialBatch , dmb.number ,wp.code , wp.name ," +
            "bwd.inputNumber , bwd.qualifiedNumber,bwf.facilityId,wsstepun.unqualifiedItem.code,wsstepun.unqualifiedItem.name,wsstepun.number,bwd.workHour,bwd.custom1,bwd.custom2) " +
            "from BatchWorkDetail bwd " +
            "left join BatchWorkDetailMaterialBatch dmb on bwd.id = dmb.batchWorkDetail.id " +
            "left join BatchWorkDetailWearingPart dwp on bwd.id = dwp.batchWorkDetail.id " +
            "left join BatchWorkDetailFacility bwf on bwd.id = bwf.batchWorkDetail.id " +
            "left join WearingPart wp on dwp.wearingPart.id = wp.id " +
            "left join WsStepUnqualifiedItem wsstepun on (wsstepun.subWorkSheet.id = bwd.subWorkSheet.id and wsstepun.step.id = bwd.step.id) " +
            "left join UnqualifiedItem  unq on unq.id = wsstepun.unqualifiedItem.id "+
            "where bwd.workSheet.id = ?1 and bwd.deleted = 0l")
    List<BatchForwardTraceStepDetailExportDTO> findByForwardTraceReportExportBatchAndWorkSheet(Long wsId);

    /**
     *  正向追溯工单详情-容器批量(子工单)
     * @param wsId 子工单id
     * @param containerCode 容器编码
     * @return List<ContainerForwardTraceStepDetailExportDTO>
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select new net.airuima.rbase.web.rest.report.dto.forwardtracereport.ContainerForwardTraceStepDetailExportDTO(" +
            "bwd.batchWorkDetail.subWorkSheet.serialNumber,bwd.batchWorkDetail.step.code,bwd.batchWorkDetail.step.name,bwd.batchWorkDetail.startDate,bwd.batchWorkDetail.endDate," +
            "bwd.batchWorkDetail.inputNumber,bwd.batchWorkDetail.qualifiedNumber,bwd.batchWorkDetail.unqualifiedNumber,bwd.containerCode,bwd.staffId,bwd.workCell.code,bwd.workCell.name," +
            "dmb.materialId,dmb.batch,dmb.number,wp.code,wp.name,bwd.inputNumber,bwd.qualifiedNumber,conf.facilityId,conun.unqualifiedItem.code,conun.unqualifiedItem.name,conun.number,bwd.batchWorkDetail.workHour,bwd.custom1,bwd.custom2)" +
            "from ContainerDetail bwd " +
            "left join ContainerDetailMaterialBatch dmb on bwd.id = dmb.containerDetail.id " +
            "left join ContainerDetailWearingPart dwp on bwd.id = dwp.containerDetail.id " +
            "left join WearingPart wp on dwp.wearingPart.id = wp.id " +
            "left join ContainerDetailFacility conf on bwd.id = conf.containerDetail.id " +
            "left join ContainerDetailUnqualifiedItem conun on bwd.id = conun.containerDetail.id " +
            "left join UnqualifiedItem  unq on unq.id = conun.unqualifiedItem.id "+
            "where bwd.batchWorkDetail.subWorkSheet.id = ?1 and (?2 is null or bwd.containerCode = ?2) and bwd.deleted = 0l")
    List<ContainerForwardTraceStepDetailExportDTO> findByForwardTraceReportExportContainerAndSubWorkSheet(Long wsId,String containerCode);

    /**
     *  正向追溯工单详情-容器批量(工单)
     * @param wsId 工单id
     * @param containerCode 容器编码
     * @return List<ContainerForwardTraceStepDetailExportDTO>
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select new net.airuima.rbase.web.rest.report.dto.forwardtracereport.ContainerForwardTraceStepDetailExportDTO(" +
            "bwd.batchWorkDetail.subWorkSheet.serialNumber,bwd.batchWorkDetail.step.code,bwd.batchWorkDetail.step.name,bwd.batchWorkDetail.startDate,bwd.batchWorkDetail.endDate," +
            "bwd.batchWorkDetail.inputNumber,bwd.batchWorkDetail.qualifiedNumber,bwd.batchWorkDetail.unqualifiedNumber,bwd.containerCode,bwd.staffId,bwd.workCell.code,bwd.workCell.name," +
            "dmb.materialId,dmb.batch,dmb.number,wp.code,wp.name,bwd.inputNumber,bwd.qualifiedNumber,conf.facilityId,conun.unqualifiedItem.code,conun.unqualifiedItem.name,conun.number,bwd.batchWorkDetail.workHour,bwd.custom1,bwd.custom2)" +
            "from ContainerDetail bwd " +
            "left join ContainerDetailMaterialBatch dmb on bwd.id = dmb.containerDetail.id " +
            "left join ContainerDetailWearingPart dwp on bwd.id = dwp.containerDetail.id " +
            "left join WearingPart wp on dwp.wearingPart.id = wp.id " +
            "left join ContainerDetailFacility conf on bwd.id = conf.containerDetail.id " +
            "left join ContainerDetailUnqualifiedItem conun on bwd.id = conun.containerDetail.id " +
            "left join UnqualifiedItem  unq on unq.id = conun.unqualifiedItem.id "+
            "where bwd.batchWorkDetail.workSheet.id = ?1 and (?2 is null or bwd.containerCode = ?2) and bwd.deleted = 0l")
    List<ContainerForwardTraceStepDetailExportDTO> findByForwardTraceReportExportContainerAndWorkSheet(Long wsId,String containerCode);


    /**
     * 正向追溯工单详情-容器sn(子工单)
     * @param wsId 子工单id
     * @param containerCode 容器号
     * @param sn sn
     * @return List<SnForwardTraceStepDetailExportDTO>
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select new net.airuima.rbase.web.rest.report.dto.forwardtracereport.SnForwardTraceStepDetailExportDTO(" +
            "bwd.containerDetail.batchWorkDetail.subWorkSheet.serialNumber,bwd.step.code, bwd.step.name,bwd.containerDetail.batchWorkDetail.startDate," +
            "bwd.containerDetail.batchWorkDetail.endDate,bwd.containerDetail.batchWorkDetail.inputNumber,bwd.containerDetail.batchWorkDetail.qualifiedNumber," +
            "bwd.containerDetail.batchWorkDetail.unqualifiedNumber,bwd.containerDetail.containerCode," +
            "bwd.sn,bwd.operatorId,bwd.workCell.code,bwd.workCell.name,dmb.materialId,dmb.materialBatch,dmb.number," +
            "wp.code,wp.name,bwd.containerDetail.inputNumber,bwd.containerDetail.qualifiedNumber,snf.facilityId,snun.unqualifiedItem.code,snun.unqualifiedItem.name,bwd.workHour,bwd.custom1,bwd.custom2) " +
            "from SnWorkDetail bwd " +
            "left join SnWorkDetailMaterialBatch dmb on bwd.id = dmb.snWorkDetail.id " +
            "left join SnWorkDetailWearingPart dwp on bwd.id = dwp.snWorkDetail.id " +
            "left join WearingPart wp on dwp.wearingPart.id = wp.id " +
            "left join SnWorkDetailFacility snf on bwd.id = snf.snWorkDetail.id " +
            "left join SnUnqualifiedItem snun on bwd.id = snun.snWorkDetail.id " +
            "left join UnqualifiedItem  unq on unq.id = snun.unqualifiedItem.id "+
            "where bwd.containerDetail.batchWorkDetail.subWorkSheet.id = ?1 and (?2 is null  or bwd.containerDetail.containerCode = ?2) and (?3 is null or bwd.sn = ?3)and bwd.deleted = 0l")
    List<SnForwardTraceStepDetailExportDTO> findByForwardTraceReportExportContainerSnAndSubWorkSheet(Long wsId,String containerCode,String sn);

    /**
     * 正向追溯工单详情-容器sn(工单)
     * @param wsId 工单id
     * @param containerCode 容器号
     * @param sn sn
     * @return List<SnForwardTraceStepDetailExportDTO>
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select new net.airuima.rbase.web.rest.report.dto.forwardtracereport.SnForwardTraceStepDetailExportDTO(" +
            "bwd.containerDetail.batchWorkDetail.subWorkSheet.serialNumber,bwd.step.code, bwd.step.name,bwd.containerDetail.batchWorkDetail.startDate,bwd.containerDetail.batchWorkDetail.endDate,bwd.containerDetail.batchWorkDetail.inputNumber,bwd.containerDetail.batchWorkDetail.qualifiedNumber,bwd.containerDetail.batchWorkDetail.unqualifiedNumber,bwd.containerDetail.containerCode," +
            "bwd.sn,bwd.operatorId,bwd.workCell.code,bwd.workCell.name,dmb.materialId,dmb.materialBatch,dmb.number," +
            "wp.code,wp.name,bwd.containerDetail.inputNumber,bwd.containerDetail.qualifiedNumber,snf.facilityId,snun.unqualifiedItem.code,snun.unqualifiedItem.name,bwd.workHour,bwd.custom1,bwd.custom2) " +
            "from SnWorkDetail bwd " +
            "left join SnWorkDetailMaterialBatch dmb on bwd.id = dmb.snWorkDetail.id " +
            "left join SnWorkDetailWearingPart dwp on bwd.id = dwp.snWorkDetail.id " +
            "left join WearingPart wp on dwp.wearingPart.id = wp.id " +
            "left join SnWorkDetailFacility snf on bwd.id = snf.snWorkDetail.id " +
            "left join SnUnqualifiedItem snun on bwd.id = snun.snWorkDetail.id " +
            "left join UnqualifiedItem  unq on unq.id = snun.unqualifiedItem.id "+
            "where bwd.containerDetail.batchWorkDetail.workSheet.id = ?1 and (?2 is null  or bwd.containerDetail.containerCode = ?2) and (?3 is null or bwd.sn = ?3)and bwd.deleted = 0l")
    List<SnForwardTraceStepDetailExportDTO> findByForwardTraceReportExportContainerSnAndWorkSheet(Long wsId,String containerCode,String sn);


    /**
     * 正向追溯工单详情-SN(子工单)
     *
     * @param wsId           子工单主键ID
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select new net.airuima.rbase.web.rest.report.dto.forwardtracereport.SnForwardTraceStepDetailExportDTO(" +
            "bb.subWorkSheet.serialNumber,bwd.step.code, bwd.step.name,bb.startDate,bb.endDate,bb.inputNumber,bb.qualifiedNumber,bb.unqualifiedNumber,''," +
            "bwd.sn,bwd.operatorId,bwd.workCell.code,bwd.workCell.name,dmb.materialId,dmb.materialBatch,dmb.number," +
            "wp.code,wp.name,1,bwd.result,snf.facilityId,snun.unqualifiedItem.code,snun.unqualifiedItem.name,bwd.workHour,bwd.custom1,bwd.custom2) " +
            "from SnWorkDetail bwd " +
            "left join SnWorkDetailMaterialBatch dmb on bwd.id = dmb.snWorkDetail.id " +
            "left join SnWorkDetailWearingPart dwp on bwd.id = dwp.snWorkDetail.id " +
            "left join WearingPart wp on dwp.wearingPart.id = wp.id " +
            "left join SnWorkDetailFacility snf on bwd.id = snf.snWorkDetail.id " +
            "left join SnUnqualifiedItem snun on bwd.id = snun.snWorkDetail.id " +
            "left join BatchWorkDetail bb on (bb.subWorkSheet.id = bwd.subWorkSheet.id and bb.step.id = bwd.step.id)" +
            "left join UnqualifiedItem  unq on unq.id = snun.unqualifiedItem.id "+
            "where bwd.subWorkSheet.id = ?1 and (?2 is null or bwd.sn = ?2) and bwd.deleted = 0l")
    List<SnForwardTraceStepDetailExportDTO> findByForwardTraceReportExportSnAndSubWorkSheet(Long wsId,String sn);

    /**
     * 正向追溯工单详情-SN(工单)
     *
     * @param wsId           工单主键ID
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select new net.airuima.rbase.web.rest.report.dto.forwardtracereport.SnForwardTraceStepDetailExportDTO(" +
            "bb.subWorkSheet.serialNumber,bwd.step.code, bwd.step.name,bb.startDate,bb.endDate,bb.inputNumber,bb.qualifiedNumber,bb.unqualifiedNumber,''," +
            "bwd.sn,bwd.operatorId,bwd.workCell.code,bwd.workCell.name,dmb.materialId,dmb.materialBatch,dmb.number," +
            "wp.code,wp.name,1,bwd.result,snf.facilityId,snun.unqualifiedItem.code,snun.unqualifiedItem.name,bwd.workHour,bwd.custom1,bwd.custom2) " +
            "from SnWorkDetail bwd " +
            "left join SnWorkDetailMaterialBatch dmb on bwd.id = dmb.snWorkDetail.id " +
            "left join SnWorkDetailWearingPart dwp on bwd.id = dwp.snWorkDetail.id " +
            "left join WearingPart wp on dwp.wearingPart.id = wp.id " +
            "left join SnWorkDetailFacility snf on bwd.id = snf.snWorkDetail.id " +
            "left join SnUnqualifiedItem snun on bwd.id = snun.snWorkDetail.id " +
            "left join BatchWorkDetail bb on (bb.subWorkSheet.id = bwd.subWorkSheet.id and bb.step.id = bwd.step.id)" +
            "left join UnqualifiedItem  unq on unq.id = snun.unqualifiedItem.id "+
            "where bwd.workSheet.id = ?1 and (?2 is null or bwd.sn = ?2) and bwd.deleted = 0l")
    List<SnForwardTraceStepDetailExportDTO> findByForwardTraceReportExportSnAndWorkSheet(Long wsId,String sn);

    /**
     * 通过部门主键id 开始结束时间，获取子工单产品谱系工序使用时长
     *
     * @param organizationId 部门主键id
     * @param startDateTime  开始时间
     * @param endDateTime    结束时间
     * @return java.util.List<ProductionCycleStatisticsDTO.StepAverageTimeConsumptionInfo> 工序使用时长
     */
    @Query(value = "SELECT" +
            "    pe.id AS pedigreeId," +
            "    pe.`code` AS pedigreeCode," +
            "    pe.`name` AS pedigreeName," +
            "    step.id AS stepId," +
            "    step.`code` AS stepCode," +
            "    step.`name` AS stepName," +
            "    CASE" +
            "        WHEN COUNT(bwd.id) > 0 THEN ROUND(ROUND(SUM(TIMESTAMPDIFF(SECOND, bwd.start_date, bwd.end_date))/60.0,4) / sum(bwd.finish_number), 1)" +
            "        ELSE 0 " +
            "    END AS averageUsageDuration" +
            " FROM" +
            "    procedure_batch_work_detail bwd" +
            " INNER JOIN procedure_sub_work_sheet sub ON sub.id = bwd.sub_work_sheet_id" +
            " INNER JOIN procedure_work_sheet ws ON ws.id = sub.work_sheet_id" +
            " INNER JOIN base_pedigree pe ON pe.id = ws.pedigree_id" +
            " INNER JOIN base_step step ON step.id = bwd.step_id" +
            " WHERE" +
            "    bwd.deleted = 0" +
            "    AND sub.`status` = 3" +
            "    AND ws.organization_id = ?1 " +
            "    AND sub.actual_end_date IS NOT NULL" +
            "    AND sub.actual_end_date >= ?2 " +
            "    AND sub.actual_end_date < ?3 " +
            "GROUP BY" +
            "    step.id, pe.id;", nativeQuery = true)
    List<ProductionCycleStatisticsDTO.StepAverageTimeConsumptionInfo> findBySubWorkSheetStepAverageTimeConsumptionInfo(Long organizationId, LocalDateTime startDateTime, LocalDateTime endDateTime);


    /**
     * 通过部门主键id 开始结束时间，获取工单产品谱系工序使用时长
     *
     * @param organizationId 部门主键id
     * @param startDateTime  开始时间
     * @param endDateTime    结束时间
     * @return java.util.List<ProductionCycleStatisticsDTO.StepAverageTimeConsumptionInfo> 工序使用时长
     */
    @Query(value = "SELECT" +
            "    pe.id AS pedigreeId," +
            "    pe.`code` AS pedigreeCode," +
            "    pe.`name` AS pedigreeName," +
            "    step.id AS stepId," +
            "    step.`code` AS stepCode," +
            "    step.`name` AS stepName," +
            "    CASE" +
            "        WHEN COUNT(bwd.id) > 0 THEN ROUND(ROUND(SUM(TIMESTAMPDIFF(SECOND, bwd.start_date, bwd.end_date))/60.0,4) / sum(bwd.finish_number), 1)" +
            "        ELSE 0 " +
            "    END AS averageUsageDuration" +
            " FROM" +
            "    procedure_batch_work_detail bwd" +
            " INNER JOIN procedure_work_sheet ws ON ws.id = bwd.work_sheet_id" +
            " INNER JOIN base_pedigree pe ON pe.id = ws.pedigree_id" +
            " INNER JOIN base_step step ON step.id = bwd.step_id" +
            " WHERE" +
            "    bwd.deleted = 0" +
            "    AND ws.`status` = 3" +
            "    AND ws.organization_id = ?1 " +
            "    AND ws.actual_end_date IS NOT NULL" +
            "    AND ws.actual_end_date >= ?2 " +
            "    AND ws.actual_end_date < ?3 " +
            "GROUP BY" +
            "    step.id, pe.id;", nativeQuery = true)
    List<ProductionCycleStatisticsDTO.StepAverageTimeConsumptionInfo> findByWorkSheetStepAverageTimeConsumptionInfo(Long organizationId, LocalDateTime startDateTime, LocalDateTime endDateTime);

    /**
     *
     * 通过子工单主键ID、完成状态及逻辑删除获取记录个数
     * @param subWorkSheetId 子工单主键ID
     * @param finish 完成状态
     * @param deleted 逻辑删除
     * @return java.util.Optional<java.lang.Integer> 记录个数
     */
    @DataFilter(isSkip = true)
    Long countBySubWorkSheetIdAndFinishAndDeleted(Long subWorkSheetId,Integer finish,Long deleted);

    /**
     *
     * 通过工单主键ID、完成状态及逻辑删除获取记录个数
     * @param workSheetId 工单主键ID
     * @param finish 完成状态
     * @param deleted 逻辑删除
     * @return java.util.Optional<java.lang.Integer> 记录个数
     */
    @DataFilter(isSkip = true)
    Long countByWorkSheetIdAndFinishAndDeleted(Long workSheetId,Integer finish,Long deleted);

    /**
     * 获取当前子工单批次下 大于当前id 且完成的批次列表
     *
     * @param subWorkSheetId 子工单
     * @param finish         完成
     * @param deleted        逻辑删除
     * @param id             批次id
     * @return List<BatchWorkDetail>
     */
    List<BatchWorkDetail> findBySubWorkSheetIdAndFinishAndDeletedAndIdGreaterThan(Long subWorkSheetId, Integer finish, Long deleted, Long id);

    /**
     * 获取当前工单批次下 大于当前id 且完成的批次列表
     *
     * @param workSheetId 工单
     * @param finish      完成
     * @param deleted     逻辑删除
     * @param id          批次id
     * @return List<BatchWorkDetail>
     */
    List<BatchWorkDetail> findByWorkSheetIdAndFinishAndDeletedAndIdGreaterThan(Long workSheetId, Integer finish, Long deleted, Long id);
}
