package net.airuima.rbase.repository.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetailMaterialBatch;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.web.rest.rmps.dto.PackRelationDataDTO;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 批量生产详情物料批次Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface BatchWorkDetailMaterialBatchRepository extends LogicDeleteableRepository<BatchWorkDetailMaterialBatch>,
        EntityGraphJpaSpecificationExecutor<BatchWorkDetailMaterialBatch>, EntityGraphJpaRepository<BatchWorkDetailMaterialBatch, Long> {

    /**
     * 通过详情主键IID，物料主键IID及批次号获取唯一记录
     *
     * @param batchWorkDetailId 详情主键IID
     * @param materialId        物料主键IID
     * @param materialBatch     物料批次
     * @param deleted           逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.batch.BatchWorkDetailMaterialBatch> 批量生产详情物料批次
     * <AUTHOR>
     * @date 2021-01-19
     **/
    @DataFilter(isSkip = true)
    Optional<BatchWorkDetailMaterialBatch> findByBatchWorkDetailIdAndMaterialIdAndMaterialBatchAndDeleted(Long batchWorkDetailId, Long materialId, String materialBatch, Long deleted);

    /**
     * 特殊的物料没有批次号时以详情主键IID，物料主键IID获取唯一记录
     *
     * @param batchWorkDetailId 详情主键IID
     * @param materialId        物料主键IID
     * @param deleted           逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.batch.BatchWorkDetailMaterialBatch> 批量生产详情物料批次
     * <AUTHOR>
     * @date 2021-01-19
     **/
    @DataFilter(isSkip = true)
    Optional<BatchWorkDetailMaterialBatch> findByBatchWorkDetailIdAndMaterialIdAndDeleted(Long batchWorkDetailId, Long materialId, Long deleted);

    /**
     * 特殊的物料没有物料主键IID时以详情主键IID，批次号获取唯一记录
     *
     * @param batchWorkDetailId 详情主键IID
     * @param materialBatch     物料批次
     * @param deleted           逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.batch.BatchWorkDetailMaterialBatch> 批量生产详情物料批次
     * <AUTHOR>
     * @date 2021-01-19
     **/
    @DataFilter(isSkip = true)
    Optional<BatchWorkDetailMaterialBatch> findByBatchWorkDetailIdAndMaterialBatchAndDeleted(Long batchWorkDetailId, String materialBatch, Long deleted);

    /**
     * 通过生产详情主键Iid数组查询所有批次详情
     *
     * @param idList  生产详情主键Iid数组
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.BatchWorkDetailMaterialBatch> 批量生产详情物料批次列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<BatchWorkDetailMaterialBatch> findByBatchWorkDetailIdInAndDeleted(List<Long> idList, Long deleted);

    /**
     * 通过物料批次查询批量生产详情
     *
     * @param materialBatch 物料批次
     * @param deleted       逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.BatchWorkDetailMaterialBatch> 批量生产详情物料批次列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<BatchWorkDetailMaterialBatch> findByMaterialBatchAndDeleted(String materialBatch, Long deleted);


    /**
     * 通过批次及日期获取数据 分页
     * <AUTHOR>
     * @param materialBatch 物料批次
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param deleted 逻辑删除
     * @param pageable 分页
     * @return java.util.List<net.airuima.domain.procedure.batch.BatchWorkDetailMaterialBatch> 批量生产详情物料批次列表
     * @date 2021-03-31
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    Page<BatchWorkDetailMaterialBatch> findByMaterialBatchAndCreatedDateGreaterThanEqualAndCreatedDateLessThanEqualAndDeleted(String materialBatch, Instant startDate, Instant endDate, Long deleted, Pageable pageable);


    /**
     * 通过批量工作详情主键ID删除物料批次信息
     * <AUTHOR>
     * @param batchWorkDetailId     批量工作详情ID
     * @return void
     * @date 2021-06-13
     **/
    @Modifying
    @Query("update BatchWorkDetailMaterialBatch bamb set bamb.deleted=bamb.id where bamb.batchWorkDetail.id=?1 and bamb.deleted=0L")
    void batchDeleteByBatchWorkDetailId(Long batchWorkDetailId);

    /**
     *  获取当前批量工作详情主键id对应的批次物料用量信息
     * @param batchWorkDetailId  批量工作详情id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/4/13
     * @return java.util.List<net.airuima.domain.procedure.batch.BatchWorkDetailMaterialBatch> 批量生产详情物料批次列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<BatchWorkDetailMaterialBatch> findByBatchWorkDetailIdAndDeleted(Long batchWorkDetailId,Long deleted);


    /**
     * 通过条件获取不等于某种扣料方式的记录
     * @param batchWorkDetailId 批量工作详情主键id
     * @param type 扣料方式 0：不扣 1：工单扣料，2：工位扣料
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.procedure.batch.BatchWorkDetailMaterialBatch> 批量生产详情物料批次列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<BatchWorkDetailMaterialBatch> findByBatchWorkDetailIdAndTypeNotAndDeleted(Long batchWorkDetailId,int type,Long deleted);

    /**
     * 通过子工单主键id 获取当前子工单上料信息
     * @param subWorkSheetId 子工单号
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/10/8
     * @return java.util.List<net.airuima.domain.procedure.batch.BatchWorkDetailMaterialBatch> 批量生产详情物料批次列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<BatchWorkDetailMaterialBatch> findByBatchWorkDetailSubWorkSheetIdAndDeleted(Long subWorkSheetId,Long deleted);

    /**
     * 通过子工单主键id列表 获取当前子工单上料信息
     * @param subWorkSheetIds 子工单主键id列表
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/10/8
     * @return java.util.List<net.airuima.domain.procedure.batch.BatchWorkDetailMaterialBatch> 批量生产详情物料批次列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<BatchWorkDetailMaterialBatch> findByBatchWorkDetailSubWorkSheetIdInAndDeleted(List<Long> subWorkSheetIds,Long deleted);

    /**
     *
     * @param materialId
     * @param batch
     * @param deleted
     * @param pageable
     * @return java.util.List<net.airuima.domain.procedure.batch.BatchWorkDetailMaterialBatch> 批量生产详情物料批次列表
     */
    @DataFilter(isSkip = true)
    List<BatchWorkDetailMaterialBatch> findByMaterialIdAndMaterialBatchAndDeletedOrderByCreatedDateDesc(Long materialId,String batch,Long deleted,Pageable pageable);

    /**
     * 根据查询条件查找批量生产详情物料批次列表
     * @param spec 查询条件
     * @return java.util.List<net.airuima.domain.procedure.batch.BatchWorkDetailMaterialBatch> 批量生产详情物料批次列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<BatchWorkDetailMaterialBatch> findAll(Specification<BatchWorkDetailMaterialBatch> spec);

    /**
     * 根据查询条件查找批量生产详情物料批次列表
     * @param spec 查询条件
     * @param pageable 分页
     * @return org.springframework.data.domain.Page<net.airuima.domain.procedure.batch.BatchWorkDetailMaterialBatch> 批量生产详情物料批次分页
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    Page<BatchWorkDetailMaterialBatch> findAll(Specification<BatchWorkDetailMaterialBatch> spec, Pageable pageable);

    /**
     * 获取工单 指定的物料或者批次信息
     * @param serialNumber 工单编码
     * @param materialId 物料id
     * @param materialBatch 物料批次
     * @return List<PackRelationDataDTO>
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    @Query("select new net.airuima.rbase.web.rest.rmps.dto.PackRelationDataDTO(bdmb.batchWorkDetail.workSheet.serialNumber,bdmb.materialId,bdmb.materialBatch) from BatchWorkDetailMaterialBatch bdmb where bdmb.batchWorkDetail.workSheet.serialNumber = ?1 and (?2 is null or bdmb.materialId = ?2) and (?3 is null  or bdmb.materialBatch = ?3) and bdmb.deleted = 0 group by bdmb.batchWorkDetail.workSheet.id,bdmb.materialId,bdmb.materialBatch")
    List<PackRelationDataDTO> findPackRelationDataByWsAndMaterialIdAndMaterialBatch(String serialNumber,Long materialId,String materialBatch);

    /**
     * 获取子工单列表 指定的物料或者批次信息
     * @param serialNumber 子工单列表
     * @param materialId 物料id
     * @param materialBatch 物料批次
     * @return List<PackRelationDataDTO>
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    @Query("select new net.airuima.rbase.web.rest.rmps.dto.PackRelationDataDTO(ws.serialNumber,sub.serialNumber,bdmb.materialId,bdmb.materialBatch) from BatchWorkDetailMaterialBatch bdmb " +
            "left join SubWorkSheet sub on sub.id = bdmb.batchWorkDetail.subWorkSheet.id" +
            " left join WorkSheet  ws on ws.id = sub.workSheet.id " +
            "where bdmb.batchWorkDetail.subWorkSheet.serialNumber in ?1 and (?2 is null or bdmb.materialId = ?2) and (?3 is null  or bdmb.materialBatch = ?3) and bdmb.deleted = 0 group by bdmb.batchWorkDetail.subWorkSheet.id,bdmb.materialId,bdmb.materialBatch")
    List<PackRelationDataDTO> findPackRelationDataBySubWsAndMaterialIdAndMaterialBatch(List<String> serialNumber,Long materialId,String materialBatch);

    /**
     * 通过物料批次号获取第一个记录
     * @param serialNumber 批次号
     * @param deleted 逻辑删除
     * @return List<BatchWorkDetailMaterialBatch>
     */
    BatchWorkDetailMaterialBatch findTop1ByMaterialBatchInAndDeleted(List<String> serialNumber,Long deleted);

    /**
     * 通过物料id获取在一定范围内使用该物料的工单
     *
     * @param materialId 物料id
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return java.util.List<net.airuima.domain.procedure.single.SnWorkDetail> sn生产详情集合
     */
    @FetchMethod
    @DataFilter(isSkip = true)
    @Query("select b from BatchWorkDetailMaterialBatch b where b.materialId = ?1 and b.batchWorkDetail.startDate >= ?2 and b.batchWorkDetail.endDate <= ?3 and b.deleted = 0L")
    List<BatchWorkDetailMaterialBatch> findByMaterialIdAndStartDateAndEndDate(Long materialId, LocalDateTime startDate, LocalDateTime endDate);
}
