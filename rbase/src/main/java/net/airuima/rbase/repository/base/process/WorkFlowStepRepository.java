package net.airuima.rbase.repository.base.process;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlowStep;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 流程框图工序Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface WorkFlowStepRepository extends LogicDeleteableRepository<WorkFlowStep>,
        EntityGraphJpaSpecificationExecutor<WorkFlowStep>, EntityGraphJpaRepository<WorkFlowStep, Long> {

    /**
     * 根据工艺路线主键ID获取工序信息
     *
     * @param workFlowId 工艺路线主键ID
     * @param deleted    删除标志
     * @return java.util.List<net.airuima.rbase.domain.base.process.WorkFlowStep> 工艺路线工序列表
     */
    @Query("select workFlowStep from WorkFlowStep workFlowStep where  workFlowStep.workFlow.id=?1 and workFlowStep.deleted=?2")
    List<WorkFlowStep> findStepByWorkFlowIdAndDeleted(Long workFlowId, Long deleted);

    /**
     * 根据工艺路线主键ID物理删除绑定的工序
     *
     * @param workFlowId 工艺路线主键ID
     */
    @Modifying
    @Query("update WorkFlowStep wfs  set wfs.deleted = wfs.id where wfs.workFlow.id=?1 and wfs.deleted = 0")
    void physicallyDeletedByWorkFlowId(Long workFlowId);

    /**
     * 通过工艺路线主键ID、工序主键ID获取工序信息
     * <AUTHOR>
     * @param workFlowId 工艺路线主键ID
     * @param stepId 工序主键ID
     * @param deleted 逻辑删除
     * @return java.util.Optionallnet.airuima.domain.base.process.WorkFlowStep > 工艺路线工序
     * @date 2021-06-11
     **/
    Optional<WorkFlowStep> findByWorkFlowIdAndStepIdAndDeleted(Long workFlowId, Long stepId, Long deleted);

    /**
     * 通过工艺路线主键ID信息
     *
     * <AUTHOR>
     * @param workFlowId 工艺路线主键ID
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.process.WorkFlowStep> 工艺路线工序列表
     */
    @EntityGraph(value = "workFlowStepEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    List<WorkFlowStep> findByWorkFlowIdAndDeleted(Long workFlowId, Long deleted);

    /**
     * 通过工艺路线列表获取工艺路线工序列表
     * @param workFlowIds 工艺路线主键ID列表
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2021/12/15
     * @return java.util.List<net.airuima.rbase.domain.base.process.WorkFlowStep> 工艺路线工序列表
     */
    @Query("from WorkFlowStep wfs where wfs.workFlow.id in ?1 and wfs.deleted = ?2")
    List<WorkFlowStep> findByWorkFlowIdInAndDeleted(List<Long> workFlowIds, Long deleted);

    /**
     * 根据工艺路线主键id 和删除标记查询对应的工序
      * @param workFlowId 工艺路线主键id
     * @param deleted 删除标记
     * @return java.util.List<net.airuima.rbase.domain.base.process.Step> 工序集合
     */
    @Query("select w.step from WorkFlowStep w where w.workFlow.id = ?1 and w.deleted = ?2")
    List<Step> findStepListByWorkFlowIdAndDeleted(Long workFlowId, Long deleted);


}
