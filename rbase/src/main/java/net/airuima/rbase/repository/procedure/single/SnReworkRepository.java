package net.airuima.rbase.repository.procedure.single;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.rbase.domain.procedure.single.SnRework;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 容器在线返修单SN关联Repository
 *
 * <AUTHOR>
 * @date 2021-01-11
 */
@Repository
public interface SnReworkRepository extends LogicDeleteableRepository<SnRework>,
        EntityGraphJpaSpecificationExecutor<SnRework>, EntityGraphJpaRepository<SnRework, Long> {

    /**
     * 通过容器主键ID、状态获取返修SN关联记录
     *
     * @param containerId 容器主键ID
     * @param status      状态
     * @param deleted     删除标识
     * @return java.util.List<net.airuima.rbase.domain.procedure.single.SnRework> 容器在线返修单SN关联信息列表
     * <AUTHOR>
     * @date 2021-01-12
     **/
    @DataFilter(isSkip = true)
    List<SnRework> findByContainerIdAndStatusAndDeleted(Long containerId, Integer status, Long deleted);

    /**
     * 批量修改容器内待返修SN的绑定状态
     *
     * @param status     状态
     * @param wsReworkId 在线返修单关联关系实体主键ID
     * @param deleted    逻辑删除
     * <AUTHOR>
     * @date 2021-01-18
     **/
    @Modifying
    @Query("update SnRework set status=?1 where wsRework.id=?2 and deleted=?3")
    void updateStatusByWsReworkId(Integer status, Long wsReworkId, Long deleted);

    /**
     * 通过sn状态主键id 以及返修工单主键id 获取返修对应关系
     * @param snWorkStatus sn状态主键id
     * @param reWorkSheet 返修工单主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/10/25
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.single.SnRework> 容器在线返修单SN关联信息
     */
    @DataFilter(isSkip = true)
    Optional<SnRework> findBySnWorkStatusIdAndWsReworkReworkWorkSheetIdAndDeleted(Long snWorkStatus,Long reWorkSheet,Long deleted);

    /**
     * 根据sn生产状态主键id 与 绑定状态获取对应的返修对应关系 最近一次记录
     * @param snWorkStatusId sn生产状态主键id
     * @param status 绑定状态 1
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/11/2
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.single.SnRework> 容器在线返修单SN关联信息
     */
    @DataFilter(isSkip = true)
    Optional<SnRework> findTop1BySnWorkStatusIdAndStatusAndDeletedOrderByIdDesc(Long snWorkStatusId,Integer status,Long deleted);

    /**
     * 获取当前sn 返修的第一道记录
     * @param snWorkStatusId sn状态主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/11/7
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.single.SnRework> 容器在线返修单SN关联信息
     */
    @DataFilter(isSkip = true)
    Optional<SnRework> findTop1BySnWorkStatusIdAndDeletedOrderByIdDesc(Long snWorkStatusId,Long deleted);

    /**
     * 通过返修单获取以及容器id不为null 获取返修对应关系
     * @param reworkSheetId 返修单主键i的
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/11/15
     * @return java.util.List<net.airuima.rbase.domain.procedure.single.SnRework> 容器在线返修单SN关联信息
     */
    @DataFilter(isSkip = true)
    List<SnRework> findByWsReworkReworkWorkSheetIdAndDeletedAndContainerIsNotNull(Long reworkSheetId,Long deleted);



    /**
     * 根据容器主键id 与 绑定状态获取对应的返修对应关系 最近一次记录
     * @param containerId 容器生产状态主键id
     * @param status 绑定状态 1
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/11/2
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.single.SnRework> 容器在线返修单SN关联信息
     */
    @DataFilter(isSkip = true)
    Optional<SnRework> findTop1ByContainerIdAndStatusAndDeletedOrderByIdDesc(Long containerId,Integer status,Long deleted);

    /**
     * 通过sn状态id 获取返修对应关系
     * @param snWorkStatusId sn状态id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/11/15
     * @return java.util.List<net.airuima.rbase.domain.procedure.single.SnRework> 容器在线返修单SN关联信息
     */
    @DataFilter(isSkip = true)
    List<SnRework> findBySnWorkStatusIdAndDeleted(Long snWorkStatusId,Long deleted);
}

