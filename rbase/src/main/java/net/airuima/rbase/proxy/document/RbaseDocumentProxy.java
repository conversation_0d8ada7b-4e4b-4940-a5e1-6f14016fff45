package net.airuima.rbase.proxy.document;

import net.airuima.config.bean.BeanDefine;
import net.airuima.config.bean.ObjectField;
import net.airuima.rbase.dto.document.DocumentDTO;
import net.airuima.rbase.dto.document.DocumentRelationDTO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseDocumentProxy {

    @BeanDefine(value = "documentRelationService",funcKey = "Document")
    public void relation(@ObjectField("net.airuima.document.web.rest.dto.DocumentRelationDTO") DocumentRelationDTO documentRelationDTO){};


    @BeanDefine(value = "documentRelationService",funcKey = "Document")
    public List<DocumentDTO> getByRecordId(Long recordId){
        return new ArrayList<>();
    }
}
