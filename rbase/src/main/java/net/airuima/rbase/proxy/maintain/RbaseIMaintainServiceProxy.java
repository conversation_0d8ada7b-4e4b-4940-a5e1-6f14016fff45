package net.airuima.rbase.proxy.maintain;

import net.airuima.config.bean.BeanDefine;
import net.airuima.config.bean.ObjectField;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.ContainerDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.dto.client.MaintainAnalyseInfoDTO;
import net.airuima.rbase.dto.client.SaveMaterialAnalyseDTO;
import net.airuima.rbase.dto.rworker.maintain.dto.MaintainAnalyseDTO;
import net.airuima.rbase.dto.rworker.maintain.dto.MaintainHistoryInfoDTO;
import net.airuima.rbase.dto.rworker.maintain.dto.MaintainHistorySaveDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseIMaintainServiceProxy {

    /**
     * 单支回退删除维修分析记录
     * @param snWorkDetail sn生产详情
     * <AUTHOR>
     */
    @BeanDefine(value = "net.airuima.maintain.service.procedure.MaintainService", funcKey = "RepaireAnalysis",interfaceBean = true)
    public void rollBackSn(SnWorkDetail snWorkDetail){

    }

    /**
     * 工单批次回退删除维修分析记录
     * @param batchWorkDetail 批量工序生产详情
     * <AUTHOR>
     */
    @BeanDefine(value = "net.airuima.maintain.service.procedure.MaintainService", funcKey = "RepaireAnalysis",interfaceBean = true)
    public void rollBackBatch(BatchWorkDetail batchWorkDetail){
    }

    /**
     * 容器回退删除维修分析记录
     * @param containerDetail 容器生产详情
     * <AUTHOR>
     */
    @BeanDefine(value = "net.airuima.maintain.service.procedure.MaintainService", funcKey = "RepaireAnalysis",interfaceBean = true)
    public void rollBackContainer(ContainerDetail containerDetail){

    }

    /**
     * 开返工单验证是否存在维修分析，存在则提示先进行维修分析
     * @param subWorkSheetId 子工单主键ID
     * @param stepIds 工序主键ID列表
     */
    @BeanDefine(value = "net.airuima.maintain.service.procedure.MaintainService", funcKey = "RepaireAnalysis",interfaceBean = true)
    public void validReWorkMaintain(Long subWorkSheetId, List<Long> stepIds){

    }

    /**
     * 获取待维修信息阶段信息
     * @param maintainAnalyseInfoDto 维修分析信息
     * <AUTHOR>
     * @date  2022/10/14
     * @return net.airuima.rbase.web.rest.procedure.maintaincase.dto.MaintainAnalyseInfoDTO 维修分析信息
     */
    @BeanDefine(value = "net.airuima.maintain.service.procedure.MaintainService", funcKey = "RepaireAnalysis",interfaceBean = true)
    public  MaintainAnalyseInfoDTO getReworkInfo(@ObjectField("net.airuima.maintain.web.rest.procedure.dto.MaintainAnalyseInfoDTO") MaintainAnalyseInfoDTO maintainAnalyseInfoDto){
        return maintainAnalyseInfoDto;
    }


    /**
     * 保存维修阶段信息
     * @param saveMaterialAnalyseDtoList 维修分析记录信息列表
     * <AUTHOR>
     * @date  2022/10/14
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO  结果信息
     */
    @BeanDefine(value = "net.airuima.maintain.service.procedure.MaintainService", funcKey = "RepaireAnalysis",interfaceBean = true)
    public net.airuima.rbase.dto.client.base.BaseClientDTO saveReworkInfo(@ObjectField(value = "net.airuima.maintain.web.rest.procedure.dto.SaveMaterialAnalyseDTO",isArray = true) List<SaveMaterialAnalyseDTO> saveMaterialAnalyseDtoList){
        return null;
    }

    /**
     * 保存分析阶段信息
     * @param saveMaterialAnalyseDtoList 维修分析记录信息列表
     * <AUTHOR>
     * @date  2022/10/14
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO  结果信息
     */
    @BeanDefine(value = "net.airuima.maintain.service.procedure.MaintainService", funcKey = "RepaireAnalysis",interfaceBean = true)
    public net.airuima.rbase.dto.client.base.BaseClientDTO saveAnalysisInfo(@ObjectField(value = "net.airuima.maintain.web.rest.procedure.dto.SaveMaterialAnalyseDTO",isArray = true) List<SaveMaterialAnalyseDTO> saveMaterialAnalyseDtoList){
        return null;
    }

    /**
     * 获取维修分析信息
     *
     * @param maintainAnalyseDTO RWorker请求维修分析参数DTO
     * @return net.airuima.rbase.web.rest.procedure.maintaincase.dto.MaintainHistoryInfoDTO 维修分析信息
     */
    @BeanDefine(value = "net.airuima.maintain.service.procedure.RworkerMaintainService", funcKey = "RepaireAnalysis",interfaceBean = true)
    public MaintainHistoryInfoDTO getMaintainAnalyseInfo(@ObjectField("net.airuima.maintain.web.rest.procedure.dto.MaintainAnalyseDTO") MaintainAnalyseDTO maintainAnalyseDTO) {
        return null;
    }

    /**
     * 保存维修历史记录
     *
     * @param maintainHistorySaveDto Rworker保存维修分析记录信息
     */
    @BeanDefine(value = "net.airuima.maintain.service.procedure.RworkerMaintainService", funcKey = "RepaireAnalysis",interfaceBean = true)
    public void saveMaintainAnalyseInfo(@ObjectField("net.airuima.maintain.web.rest.procedure.dto.MaintainHistorySaveDTO") MaintainHistorySaveDTO maintainHistorySaveDto) {

    }

}
