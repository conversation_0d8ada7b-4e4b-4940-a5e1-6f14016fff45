package net.airuima.rbase.proxy.rfms;

import net.airuima.config.bean.BeanDefine;
import net.airuima.config.bean.ObjectField;
import net.airuima.rbase.dto.rfms.FacilityDTO;
import net.airuima.rbase.dto.rfms.FacilityLatestStatusDTO;
import net.airuima.rbase.dto.rfms.FacilityStatusChangeDTO;
import net.airuima.rbase.dto.rfms.FacilityStatusChangeRequestDTO;
import net.airuima.util.BeanUtil;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseFacilityProxy {

    @BeanDefine(value = "facilityRepository",funcKey = "FBase")
    public FacilityDTO findByCodeAndDeleted(String code, Long deleted){
        return null;
    }

    @BeanDefine(value = "facilityRepository",funcKey = "FBase")
    public List<FacilityDTO> findByCodeInAndIsEnableAndDeleted(List<String> codes, Boolean isEnable, Long deleted){
        return null;
    }

    @BeanDefine(value = "facilityLatestStatusRepository",funcKey = "FBase")
    public List<FacilityLatestStatusDTO> findByFacilityIdInAndDeleted(List<Long> facilityIdList, Long deleted){return new ArrayList<>();}

    @BeanDefine(value = "facilityLatestStatusRepository",funcKey = "FBase")
    public FacilityLatestStatusDTO findByFacilityIdAndDeleted(Long facilityId,Long deleted){return null;}

    @BeanDefine(value = "facilityStatusChangeHistoryService",funcKey = "FBase")
    public List<FacilityStatusChangeDTO> findByConditions(@ObjectField(value = "net.airuima.facility.base.web.rest.procedure.facility.dto.FacilityStatusChangeRequestDTO") FacilityStatusChangeRequestDTO facilityStatusChangeRequestDTO){
        return null;
    }

    @BeanDefine(value = "facilityStatusChangeHistoryService",funcKey = "FBase")
    public boolean manualUpdate(@ObjectField(value = "net.airuima.facility.base.web.rest.procedure.facility.dto.FacilityStatusChangeDTO") FacilityStatusChangeDTO facilityStatusChangeDTO){
        return Boolean.FALSE;
    }

    /**
     * 验证设备基础状态
     * @param facilityIds 设备ID列表
     */
    @BeanDefine(value = "facilityLatestStatusService",funcKey = "FBase")
    public void validateFacilityBaseStatus(List<Long> facilityIds){

    }

    /**
     * 验证设备巡检
     * @param facilityIdList 设备ID列表
     */
    @BeanDefine(value = "patrolInspectHistoryService",funcKey = "FBase && FacilityPointInspection")
    public void validateFacilityPatrolInspect(List<Long> facilityIdList){

    }


    /**
     * 验证设备点检
     * @param facilityIdList 设备ID列表
     */
    @BeanDefine(value = "pointInspectHistoryService",funcKey = "FBase && FacilityPointInspection")
    public void validateFacilityPointInspect(List<Long> facilityIdList){

    }

    /**
     * 验证设备维保
     * @param facilityIds 设备ID列表
     */
    @BeanDefine(value = "maintainTaskService",funcKey = "FBase && FMaintenance")
    public String validateMaintainTaskByFacilityIds(List<Long> facilityIds){
        return null;
    }
}
