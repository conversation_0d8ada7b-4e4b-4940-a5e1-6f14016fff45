package net.airuima.rbase.proxy.maintain;

import net.airuima.config.annotation.DataFilter;
import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.dto.maintain.MaintainHistoryDTO;
import net.airuima.rbase.dto.maintain.MaintainHistoryDetailDTO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Component
public class RbaseMaintainHistoryDetailProxy {

    /**
     * 获取容器详情id获取已生成的返工单记录
     *
     * @param containDetailId 容器详情id
     * @param deleted         逻辑删除
     * @return List<MaintainHistoryDetail>
     */
    @BeanDefine(value = "maintainHistoryDetailRepository", funcKey = "RepaireAnalysis")
    public List<MaintainHistoryDetailDTO> findByMaintainHistoryContainerDetailIdAndDeletedAndWsReworkIsNotNull(Long containDetailId, Long deleted) {
        return new ArrayList<>();
    }

    /**
     * 获取 子工单对应 工序 已生成返工单的记录
     *
     * @param subWorkSheetId 子工单id
     * @param stepId         工序id
     * @param deleted        逻辑删除
     * @return List<MaintainHistoryDetail>
     */
    @BeanDefine(value = "maintainHistoryDetailRepository", funcKey = "RepaireAnalysis")
    public List<MaintainHistoryDetailDTO> findByMaintainHistorySubWorkSheetIdAndMaintainHistoryStepIdAndDeletedAndWsReworkIsNotNull(Long subWorkSheetId, Long stepId, Long deleted) {
        return new ArrayList<>();
    }

    /**
     * 获取 工单对应 工序 已生成返工单的记录
     *
     * @param workSheetId 工单id
     * @param stepId      工序id
     * @param deleted     逻辑删除
     * @return List<MaintainHistoryDetail>
     */
    @BeanDefine(value = "maintainHistoryDetailRepository", funcKey = "RepaireAnalysis")
    public List<MaintainHistoryDetailDTO> findByMaintainHistoryWorkSheetIdAndMaintainHistoryStepIdAndDeletedAndWsReworkIsNotNull(Long workSheetId, Long stepId, Long deleted){
        return new ArrayList<>();
    }

    /**
     * 获取 子工单对应 工序 sn 已生成返工单的记录
     *
     * @param subWorkSheetId 子工单id
     * @param stepId         工序id
     * @param sn             sn
     * @param deleted        逻辑删除
     * @return List<MaintainHistoryDetail>
     */
    @BeanDefine(value = "maintainHistoryDetailRepository", funcKey = "RepaireAnalysis")
    public List<MaintainHistoryDetailDTO> findBySubWorkSheetIdAndStepIdAndSnAndDeletedAndWsReworkIsNotNull(Long subWorkSheetId, Long stepId, String sn, Long deleted){
        return new ArrayList<>();
    }

    /**
     * 获取 工单对应 工序 sn 已生成返工单的记录
     *
     * @param workSheetId 工单id
     * @param stepId      工序id
     * @param sn          sn
     * @param deleted     逻辑删除
     * @return List<MaintainHistoryDetail>
     */
    @BeanDefine(value = "maintainHistoryDetailRepository", funcKey = "RepaireAnalysis")
    public List<MaintainHistoryDetailDTO> findByWorkSheetIdAndStepIdAndSnAndDeletedAndWsReworkIsNotNull(Long workSheetId, Long stepId, String sn, Long deleted){
        return new ArrayList<>();
    }

    /**
     * 通过子工单ID、SN生产状态ID、返工单ID、工序ID获取维修分析记录
     *
     * @param subWorkSheetId 子工单ID
     * @param snWorkStatusId SN生产状态ID
     * @param reworkSheetId  返工单ID
     * @param stepId         工序ID
     * @param deleted        逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     */
    @BeanDefine(value = "maintainHistoryDetailRepository", funcKey = "RepaireAnalysis")
    public Optional<MaintainHistoryDetailDTO> findTop1ByMaintainHistorySubWorkSheetIdAndMaintainHistorySnWorkStatusIdAndWsReworkReworkWorkSheetIdAndMaintainHistoryStepIdAndDeleted(Long subWorkSheetId, Long snWorkStatusId, Long reworkSheetId, Long stepId, Long deleted){
        return Optional.empty();
    }

    /**
     * 通过子工单ID、SN生产状态ID、返工单ID、工序ID获取维修分析记录
     *
     * @param workSheetId    工单ID
     * @param snWorkStatusId SN生产状态ID
     * @param reworkSheetId  返工单ID
     * @param stepId         工序ID
     * @param deleted        逻辑删除
     * @return java.util.Optional<net.airuima.domain.procedure.maintaincase.MaintainHistory> 维修分析记录
     */
    @BeanDefine(value = "maintainHistoryDetailRepository", funcKey = "RepaireAnalysis")
    public Optional<MaintainHistoryDetailDTO> findTop1ByMaintainHistoryWorkSheetIdAndMaintainHistorySnWorkStatusIdAndWsReworkReworkWorkSheetIdAndMaintainHistoryStepIdAndDeleted(Long workSheetId, Long snWorkStatusId, Long reworkSheetId, Long stepId, Long deleted){
        return Optional.empty();
    }


    @BeanDefine(value = "maintainHistoryDetailRepository", funcKey = "RepaireAnalysis")
    public  List<MaintainHistoryDetailDTO> findByMaintainHistoryIdAndResultAndDeleted(Long maintainHistoryId, Integer result, Long deleted){
        return new ArrayList<>();
    }

    /**
     * 通过返工单主键ID、容器主键ID获取SN生产状态不为空的维修历史记录列表
     *
     * @param reWorkSheetId 返工单主键ID
     * @param containerId   容器主键ID
     * @param deleted       逻辑删除
     * @r* @return java.util.List<net.airuima.rbase.domain.procedure.maintaincase.MaintainHistory> 维修分析记录列表
     */
    @BeanDefine(value = "maintainHistoryDetailRepository", funcKey = "RepaireAnalysis")
    public List<MaintainHistoryDTO> findByWsReworkReworkWorkSheetIdAndContainerDetailContainerIdAndSnWorkStatusIsNotNullAndDeleted(Long reWorkSheetId, Long containerId, Integer result, Long deleted){
        return new ArrayList<>();
    }


    @BeanDefine(value = "maintainHistoryDetailService", funcKey = "RepaireAnalysis")
    public MaintainHistoryDetailDTO saveInstance(MaintainHistoryDetailDTO maintainHistoryDetail){
        return null;
    }

    @BeanDefine(value = "maintainHistoryDetailService", funcKey = "RepaireAnalysis")
    public List<MaintainHistoryDetailDTO> batchSaveInstance(List<MaintainHistoryDetailDTO> maintainHistoryDetailList){
        return null;
    }

    /**
     * 获取容器id获取维修分析详情
     *
     * @param containerId 容器id
     * @param deleted         逻辑删除
     * @return List<MaintainHistoryDetail>
     */
    @BeanDefine(value = "maintainHistoryDetailRepository", funcKey = "RepaireAnalysis")
    public List<MaintainHistoryDetailDTO> findByMaintainHistoryContainerDetailContainerIdAndDeleted(Long containerId, Long deleted){
        return new ArrayList<>();
    }

    /**
     * 获取sn状态id获取维修分析详情
     *
     * @param snWorkStatusId sn状态id
     * @param deleted         逻辑删除
     * @return List<MaintainHistoryDetail>
     */
    @BeanDefine(value = "maintainHistoryDetailRepository", funcKey = "RepaireAnalysis")
    public List<MaintainHistoryDetailDTO> findByMaintainHistorySnWorkStatusIdAndDeleted(Long snWorkStatusId, Long deleted){
        return new ArrayList<>();
    }
}
