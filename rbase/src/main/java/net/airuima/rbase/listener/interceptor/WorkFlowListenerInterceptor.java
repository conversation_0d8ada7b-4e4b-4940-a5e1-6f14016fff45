package net.airuima.rbase.listener.interceptor;

import net.airuima.rbase.domain.base.process.WorkFlow;

/**
 * 工艺路线新增修改监听接口
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
public interface WorkFlowListenerInterceptor {

    /**
     * 工艺路线新增监听实现
     */
    default void prePersist(WorkFlow workFlow) {

    }

    /**
     * 工艺路线修改监听实现
     */
    default void preUpdate(WorkFlow workFlow) {

    }
}
