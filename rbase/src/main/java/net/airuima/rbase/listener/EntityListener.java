package net.airuima.rbase.listener;

import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.listener.interceptor.StepListenerInterceptor;
import net.airuima.rbase.listener.interceptor.WorkFlowListenerInterceptor;
import net.airuima.rbase.listener.interceptor.WorkLineListenerInterceptor;
import net.airuima.util.BeanUtil;

/**
 * 工序新增修改监听
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
public class EntityListener {

    /**
     * 新增监听
     */
    @PrePersist
    public void prePersist(Object object) {
        if (object instanceof Step step) {
            BeanUtil.getHighestPrecedenceBean(StepListenerInterceptor.class).prePersist(step);
        }
        if (object instanceof WorkFlow workFlow) {
            BeanUtil.getHighestPrecedenceBean(WorkFlowListenerInterceptor.class).prePersist(workFlow);
        }
        if (object instanceof WorkLine workLine) {
            BeanUtil.getHighestPrecedenceBean(WorkLineListenerInterceptor.class).prePersist(workLine);
        }
    }

    /**
     * 修改监听
     */
    @PreUpdate
    public void preUpdate(Object object) {
        if (object instanceof Step step) {
            BeanUtil.getHighestPrecedenceBean(StepListenerInterceptor.class).preUpdate(step);
        }
        if (object instanceof WorkFlow workFlow) {
            BeanUtil.getHighestPrecedenceBean(WorkFlowListenerInterceptor.class).preUpdate(workFlow);
        }
        if (object instanceof WorkLine workLine) {
            BeanUtil.getHighestPrecedenceBean(WorkLineListenerInterceptor.class).preUpdate(workLine);
        }
    }
}
