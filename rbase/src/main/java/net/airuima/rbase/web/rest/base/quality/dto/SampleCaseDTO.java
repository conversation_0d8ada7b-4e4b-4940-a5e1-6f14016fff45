package net.airuima.rbase.web.rest.base.quality.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 抽样方案DTO
 * <AUTHOR>
 * @date 2023-04-26
 */
@AuditEntity(value = "抽样方案DTO")
public class SampleCaseDTO implements Serializable {

    /**
     * id,新增时无需输入，修改时比输入
     */
    @Schema(description = "id,新增时无需输入，修改时比输入")
    private Long id;

    /**
     * 抽样方案编码
     */
    @NotNull
    @Schema(description = "抽样方案编码", required = true)
    private String code;

    /**
     * 抽样方案名称
     */
    @NotNull
    @Schema(description = "抽样方案名称", required = true)
    private String name;

    /**
     * 抽样类型:全检0/固定数量1/按百分比抽样2/按国标抽样3
     */
    @NotNull
    @Schema(description = "抽样类型:全检0/固定数量1/按百分比抽样2/按国标抽样3", required = true)
    private Integer category;

    /**
     * 有效期
     */
    @NotNull
    @Schema(description = "有效期", required = true)
    private LocalDate expiryDate;

    /**
     * 是否启用(0:禁用;1:启用)
     */
    @NotNull
    @Schema(description = "是否启用(false:禁用;true:启用)", required = true)
    private Boolean isEnable;

    /**
     * 抽样数量,类型为固定数量时必输入
     */
    @Schema(description = "抽样数量,类型为固定数量时必输入")
    private Integer number;

    /**
     * 允收数ac,类型为固定数量时必输入
     */
    @Schema(description = "允收数ac,类型为固定数量时必输入")
    private Integer ac;

    /**
     * 抽样百分比,类型为百分比抽样时必输入*
     */
    @Schema(description = "抽样百分比,类型为百分比抽样时必输入")
    private Double rate;

    /**
     * 合格百分比,类型为百分比抽样时必输入*
     */
    @Schema(description = "合格百分比,类型为百分比抽样时必输入", required = true)
    private Double qualifiedRate;

    /**
     * 国标类型id,类型为国标抽样时比输入
     */
    @Schema(description = "国标类型id,类型为国标抽样时比输入")
    private Long gbtId;

    /**
     * 检验水平,类型为国标抽样时比输入
     */
    @Schema(description = "检验水平,类型为国标抽样时比输入")
    private String insolationLevel;

    /**
     * 接收质量限AQL,类型为国标抽样时比输入*
     */
    @Schema(description = "接收质量限AQL,类型为国标抽样时比输入")
    private Double aql;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public LocalDate getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(LocalDate expiryDate) {
        this.expiryDate = expiryDate;
    }

    public Boolean getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(Boolean isEnable) {
        this.isEnable = isEnable;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Integer getAc() {
        return ac;
    }

    public void setAc(Integer ac) {
        this.ac = ac;
    }

    public Double getRate() {
        return rate;
    }

    public void setRate(Double rate) {
        this.rate = rate;
    }

    public Double getQualifiedRate() {
        return qualifiedRate;
    }

    public void setQualifiedRate(Double qualifiedRate) {
        this.qualifiedRate = qualifiedRate;
    }

    public Long getGbtId() {
        return gbtId;
    }

    public void setGbtId(Long gbtId) {
        this.gbtId = gbtId;
    }

    public String getInsolationLevel() {
        return insolationLevel;
    }

    public void setInsolationLevel(String insolationLevel) {
        this.insolationLevel = insolationLevel;
    }

    public Double getAql() {
        return aql;
    }

    public void setAql(Double aql) {
        this.aql = aql;
    }
}
