package net.airuima.rbase.web.rest.base.pedigree.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.StepGroup;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.bom.MaterialAttributeDTO;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.dto.organization.SupplierDTO;
import net.airuima.rbase.dto.qms.SampleCaseDTO;
import net.airuima.rbase.dto.qms.VarietyDTO;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * 产品谱系工序检测判定标准数据导入DTO
 *
 * <AUTHOR>
 * @date 2023/03/10
 */
@Schema(description = "产品谱系工序检测判定标准数据导入DTO")
public class PedigreeStepCheckRuleImportDTO {

    /**
     * 编码
     */
    @Schema(description = "编码")
    @NotNull
    @Excel(name = "编码")
    private String code;

    /**
     * 名称
     */
    @Schema(description = "名称")
    @NotNull
    @Excel(name = "名称")
    private String name;

    /**
     * 检测类型(0：首检；1：巡检；2：末检；3：终检；4：抽检)
     */
    @Schema(description = "检测类型(0：首检；1：巡检；2：末检；3：终检；4：抽检 5:来料检)")
    @Excel(name = "检测类型")
    @NotNull
    private String category;

    /**
     * 产品谱系编码
     */
    @Schema(description = "产品谱系编码")
    @Excel(name = "产品谱系编码")
    private String pedigreeCode;

    /**
     * 工序编码
     */
    @Schema(description = "工序编码")
    @Excel(name = "工序编码")
    private String stepCode;

    /**
     * 工单号
     */
    @Schema(description = "工单号")
    @Excel(name = "工单号")
    private String serialNumber;

    /**
     * 工位编码
     */
    @Schema(description = "工位编码")
    @Excel(name = "工位编码")
    private String workCellCode;

    /**
     * 工单类型(0:离线返修单;1:正常单;)
     */
    @Schema(description = "工单类型(0:离线返修单;1:正常单;)")
    @Excel(name = "工单类型")
    private String workSheetCategory;

    /**
     * 工序组编码
     */
    @Schema(description = "工序组编码")
    @Excel(name = "工序组编码")
    private String stepGroupCode;

    /**
     * 客户编码
     */
    @Schema(description = "客户编码")
    @Excel(name = "客户编码")
    private String clientCode;

    /**
     * 物料属性编码
     */
    @Schema(description = "物料属性编码")
    @Excel(name = "物料属性编码")
    private String attributeCode;

    /**
     * 物料编码
     */
    @Schema(description = "物料编码")
    private String materialCode;

    /**
     * 供应商编码
     */
    @Excel(name = "供应商编码")
    @Schema(description = "供应商编码")
    private String  supplierCode;

    /**
     * 工艺路线编码
     */
    @Schema(description = "工艺路线编码")
    @Excel(name = "工艺路线编码")
    private String workFlowCode;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    @Excel(name = "备注信息")
    private String note;

    /**
     * 项目类型编码
     */
    @NotNull
    @Schema(description = "项目类型编码")
    @Excel(name = "项目类型编码")
    private String varietyCode;

    /**
     * 抽样方案编码
     */
    @NotNull
    @Schema(description = "抽样方案编码")
    @Excel(name = "抽样方案编码")
    private String sampleCaseCode;

    /**
     * 有效期
     */
    @NotNull
    @Schema(description = "有效期", required = true)
    @Excel(name = "有效期",importFormat = "yyyy-MM-dd", exportFormat = "yyyy-MM-dd")
    private LocalDate expiryDate;

    /**
     * 产品谱系工序检测项目
     */
    @Schema(description = "产品谱系工序检测项目")
    @ExcelCollection(name = "产品谱系工序检测项目")
    private List<PedigreeStepCheckItemDTO> pedigreeStepCheckItemCodeList;

    /**
     * 产品谱系工序检测项目
     */
    public static class PedigreeStepCheckItemDTO {

        /**
         * 检测项目编码
         */
        @Schema(description = "检测项目编码")
        @Excel(name = "检测项目编码")
        private String checkItemCode;

        /**
         * 是否管控结果
         */
        @Schema(description = "是否管控结果")
        @Excel(name = "是否管控结果", replace = {"否_false", "是_true"})
        private Boolean control;

        /**
         * 检测数据录入数
         */
        @Schema(description = "检测数据录入数")
        @Excel(name = "检测数据录入数", replace = {"按抽样方案数_0", "只录一次_1"})
        private Integer inspectNumberCase;

        public Integer getInspectNumberCase() {
            return inspectNumberCase;
        }

        public PedigreeStepCheckItemDTO setInspectNumberCase(Integer inspectNumberCase) {
            this.inspectNumberCase = inspectNumberCase;
            return this;
        }

        public Boolean getControl() {
            return control;
        }

        public PedigreeStepCheckItemDTO setControl(Boolean control) {
            this.control = control;
            return this;
        }

        public String getCheckItemCode() {
            return checkItemCode;
        }

        public PedigreeStepCheckItemDTO setCheckItemCode(String checkItemCode) {
            this.checkItemCode = checkItemCode;
            return this;
        }
    }

    public String getCode() {
        return code;
    }

    public PedigreeStepCheckRuleImportDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getCategory() {
        return category;
    }

    public PedigreeStepCheckRuleImportDTO setCategory(String category) {
        this.category = category;
        return this;
    }

    public String getPedigreeCode() {
        return pedigreeCode;
    }

    public PedigreeStepCheckRuleImportDTO setPedigreeCode(String pedigreeCode) {
        this.pedigreeCode = pedigreeCode;
        return this;
    }

    public String getStepCode() {
        return stepCode;
    }

    public PedigreeStepCheckRuleImportDTO setStepCode(String stepCode) {
        this.stepCode = stepCode;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public PedigreeStepCheckRuleImportDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public String getWorkCellCode() {
        return workCellCode;
    }

    public PedigreeStepCheckRuleImportDTO setWorkCellCode(String workCellCode) {
        this.workCellCode = workCellCode;
        return this;
    }

    public String getWorkSheetCategory() {
        return workSheetCategory;
    }

    public PedigreeStepCheckRuleImportDTO setWorkSheetCategory(String workSheetCategory) {
        this.workSheetCategory = workSheetCategory;
        return this;
    }

    public String getStepGroupCode() {
        return stepGroupCode;
    }

    public PedigreeStepCheckRuleImportDTO setStepGroupCode(String stepGroupCode) {
        this.stepGroupCode = stepGroupCode;
        return this;
    }

    public String getClientCode() {
        return clientCode;
    }

    public PedigreeStepCheckRuleImportDTO setClientCode(String clientCode) {
        this.clientCode = clientCode;
        return this;
    }

    public String getWorkFlowCode() {
        return workFlowCode;
    }

    public PedigreeStepCheckRuleImportDTO setWorkFlowCode(String workFlowCode) {
        this.workFlowCode = workFlowCode;
        return this;
    }

    public String getNote() {
        return note;
    }

    public PedigreeStepCheckRuleImportDTO setNote(String note) {
        this.note = note;
        return this;
    }

    public String getName() {
        return name;
    }

    public PedigreeStepCheckRuleImportDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getVarietyCode() {
        return varietyCode;
    }

    public PedigreeStepCheckRuleImportDTO setVarietyCode(String varietyCode) {
        this.varietyCode = varietyCode;
        return this;
    }

    public String getSampleCaseCode() {
        return sampleCaseCode;
    }

    public PedigreeStepCheckRuleImportDTO setSampleCaseCode(String sampleCaseCode) {
        this.sampleCaseCode = sampleCaseCode;
        return this;
    }

    public LocalDate getExpiryDate() {
        return expiryDate;
    }

    public PedigreeStepCheckRuleImportDTO setExpiryDate(LocalDate expiryDate) {
        this.expiryDate = expiryDate;
        return this;
    }

    public String getAttributeCode() {
        return attributeCode;
    }

    public PedigreeStepCheckRuleImportDTO setAttributeCode(String attributeCode) {
        this.attributeCode = attributeCode;
        return this;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public PedigreeStepCheckRuleImportDTO setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
        return this;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public PedigreeStepCheckRuleImportDTO setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
        return this;
    }

    public List<PedigreeStepCheckItemDTO> getPedigreeStepCheckItemCodeList() {
        return pedigreeStepCheckItemCodeList;
    }

    public PedigreeStepCheckRuleImportDTO setPedigreeStepCheckItemCodeList(List<PedigreeStepCheckItemDTO> pedigreeStepCheckItemCodeList) {
        this.pedigreeStepCheckItemCodeList = pedigreeStepCheckItemCodeList;
        return this;
    }

    public PedigreeStepCheckRuleImportDTO() {
    }

    public PedigreeStepCheckRuleImportDTO(PedigreeStepCheckRule pedigreeStepCheckRule) {
        this.code = pedigreeStepCheckRule.getCode();
        this.name = pedigreeStepCheckRule.getName();
        this.pedigreeCode = Optional.ofNullable(pedigreeStepCheckRule.getPedigree()).orElse(new Pedigree()).getCode();
        this.stepCode = Optional.ofNullable(pedigreeStepCheckRule.getStep()).orElse(new Step()).getCode();
        this.serialNumber = Optional.ofNullable(pedigreeStepCheckRule.getWorkSheet()).orElse(new WorkSheet()).getSerialNumber();
        this.workCellCode = Optional.ofNullable(pedigreeStepCheckRule.getWorkCell()).orElse(new WorkCell()).getCode();
        this.stepGroupCode = Optional.ofNullable(pedigreeStepCheckRule.getStepGroup()).orElse(new StepGroup()).getCode();
        this.clientCode = Optional.ofNullable(pedigreeStepCheckRule.getClientDto()).map(ClientDTO::getCode).orElse(null);
        this.workFlowCode = Optional.ofNullable(pedigreeStepCheckRule.getWorkFlow()).orElse(new WorkFlow()).getCode();
        this.note = pedigreeStepCheckRule.getNote();
        this.varietyCode = Optional.ofNullable(pedigreeStepCheckRule.getVarietyObj()).orElse(new VarietyDTO()).getCode();
        this.sampleCaseCode =
                Optional.ofNullable(pedigreeStepCheckRule.getSampleCase()).orElse(new SampleCaseDTO()).getCode();
        this.expiryDate = pedigreeStepCheckRule.getExpiryDate();
        this.attributeCode = Optional.ofNullable(pedigreeStepCheckRule.getMaterialAttributeDto()).map(MaterialAttributeDTO::getCode).orElse(null);
        this.supplierCode = Optional.ofNullable(pedigreeStepCheckRule.getSupplierDto()).map(SupplierDTO::getCode).orElse(null);
        this.materialCode = Optional.ofNullable(pedigreeStepCheckRule.getMaterialDto()).map(MaterialDTO::getCode).orElse(null);
    }
}
