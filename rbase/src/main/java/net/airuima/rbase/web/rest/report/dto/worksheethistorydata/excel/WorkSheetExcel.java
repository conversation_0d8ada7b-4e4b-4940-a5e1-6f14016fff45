package net.airuima.rbase.web.rest.report.dto.worksheethistorydata.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 生产履历导出Excel
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
public class WorkSheetExcel {

    /**
     * 工单号
     */
    @Excel(name = "工单号")
    private String serialNumber;

    /**
     * 工单状态(0:已下单;1:投产中;2:已暂停;3:已完成;4:正常结单;5:异常结单，6：收货中)
     */
    @Excel(name = "工单状态", replace = {"已取消_-2", "审批中_-1", "已下单_0", "投产中_1", "已暂停_2", "已完成_3", "正常结单_4", "异常结单_5"})
    private int status;

    /**
     * 工单进度
     */
    @Excel(name = "工单进度")
    private BigDecimal progress;

    /**
     * 产品谱系
     */
    @Excel(name = "产品谱系")
    private String pedigreeNameCode;

    /**
     * 规格型号
     */
    @Excel(name = "规格型号")
    private String specification;

    /**
     * 产线
     */
    @Excel(name = "产线")
    private String workLineNameCode;

    /**
     * 工艺路线
     */
    @Excel(name = "工艺路线")
    private String workFlowNameCode;


    /**
     * 物料清单
     */
    @Excel(name = "物料清单")
    private String bomInfoNameCode;

    /**
     * 计划开工日期
     */
    @Excel(name = "计划开工日期", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planStartDate;

    /**
     * 计划结单日期
     */
    @Excel(name = "计划结单日期", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planEndDate;

    /**
     * 实际开工日期
     */
    @Excel(name = "实际开工日期", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime actualStartDate;

    /**
     * 实际完成日期
     */
    @Excel(name = "实际完成日期", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime actualEndDate;

    /**
     * 投产数
     */
    @Excel(name = "投产数")
    private Integer number;

    /**
     * 合格数
     */
    @Excel(name = "合格数")
    private Integer qualifiedNumber;

    /**
     * 履历信息
     */
    @ExcelCollection(name = "履历信息")
    private List<HistoryExcel> historyExcelList;

    public static class HistoryExcel {

        /**
         * 0,创建
         * 1,分单
         * 2,暂停
         * 3,恢复
         * 4,完成
         */
        @Excel(name = "工单状态", replace = {"创建_0", "分单_1", "暂停_2", "恢复_3", "完成_4"})
        private Integer type;

        /**
         * 记录时间
         */
        @Excel(name = "时间", format = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime localDateTime;

        /**
         * 人员信息
         */
        @Excel(name = "人员信息")
        private String staffNameCode;

        /**
         * 备注
         */
        @Excel(name = "备注")
        private String note;

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public LocalDateTime getLocalDateTime() {
            return localDateTime;
        }

        public void setLocalDateTime(LocalDateTime localDateTime) {
            this.localDateTime = localDateTime;
        }

        public String getStaffNameCode() {
            return staffNameCode;
        }

        public void setStaffNameCode(String staffNameCode) {
            this.staffNameCode = staffNameCode;
        }

        public String getNote() {
            return note;
        }

        public void setNote(String note) {
            this.note = note;
        }
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public BigDecimal getProgress() {
        return progress;
    }

    public void setProgress(BigDecimal progress) {
        this.progress = progress;
    }

    public String getPedigreeNameCode() {
        return pedigreeNameCode;
    }

    public void setPedigreeNameCode(String pedigreeNameCode) {
        this.pedigreeNameCode = pedigreeNameCode;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public String getWorkLineNameCode() {
        return workLineNameCode;
    }

    public void setWorkLineNameCode(String workLineNameCode) {
        this.workLineNameCode = workLineNameCode;
    }

    public String getWorkFlowNameCode() {
        return workFlowNameCode;
    }

    public void setWorkFlowNameCode(String workFlowNameCode) {
        this.workFlowNameCode = workFlowNameCode;
    }

    public String getBomInfoNameCode() {
        return bomInfoNameCode;
    }

    public void setBomInfoNameCode(String bomInfoNameCode) {
        this.bomInfoNameCode = bomInfoNameCode;
    }

    public LocalDateTime getPlanStartDate() {
        return planStartDate;
    }

    public void setPlanStartDate(LocalDateTime planStartDate) {
        this.planStartDate = planStartDate;
    }

    public LocalDateTime getPlanEndDate() {
        return planEndDate;
    }

    public void setPlanEndDate(LocalDateTime planEndDate) {
        this.planEndDate = planEndDate;
    }

    public LocalDateTime getActualStartDate() {
        return actualStartDate;
    }

    public void setActualStartDate(LocalDateTime actualStartDate) {
        this.actualStartDate = actualStartDate;
    }

    public LocalDateTime getActualEndDate() {
        return actualEndDate;
    }

    public void setActualEndDate(LocalDateTime actualEndDate) {
        this.actualEndDate = actualEndDate;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Integer getQualifiedNumber() {
        return qualifiedNumber;
    }

    public void setQualifiedNumber(Integer qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
    }

    public List<HistoryExcel> getHistoryExcelList() {
        return historyExcelList;
    }

    public void setHistoryExcelList(List<HistoryExcel> historyExcelList) {
        this.historyExcelList = historyExcelList;
    }
}
