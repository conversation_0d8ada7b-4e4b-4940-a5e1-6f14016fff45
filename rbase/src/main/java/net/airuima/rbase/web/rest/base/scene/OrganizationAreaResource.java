package net.airuima.rbase.web.rest.base.scene;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.scene.OrganizationArea;
import net.airuima.rbase.service.base.scene.OrganizationAreaService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 部门区域表Resource
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Tag(name = "部门区域表Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/organization-areas")
@AuthorityRegion("环境管控")
@FuncInterceptor("EnvCleanliness || EnvHumiture")
public class OrganizationAreaResource extends ProtectBaseResource<OrganizationArea> {

    private final OrganizationAreaService organizationAreaService;

    public OrganizationAreaResource(OrganizationAreaService organizationAreaService) {
        this.organizationAreaService = organizationAreaService;
        this.mapUri = "/api/organization-areas";
    }

    /**
     * 根据部门ID查询部门区域列表接口
     *
     * @param organizationId 部门ID
     * @return ResponseEntity<List < OrganizationArea>>
     * <AUTHOR>
     * @date 2022-06-29
     **/
    @Operation(summary = "根据部门ID查询部门区域列表接口")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')or @sc.checkSecurity()")
    @GetMapping("/organizationId/{organizationId}")
    public ResponseEntity<ResponseData<List<OrganizationArea>>> findByOrganizationId(@PathVariable("organizationId") Long organizationId) {
        try {
            List<OrganizationArea> organizationAreaList = organizationAreaService.findByOrganizationId(organizationId);
            return ResponseData.ok(organizationAreaList);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 根据区域编码或者名称获取部门区域
     *
     * @param text         区域编码或者名称
     * @param size         最大返回数据条数
     * <AUTHOR>
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "根据区域编码或者名称获取部门区域")
    @GetMapping("/byNameOrCode")
    public ResponseEntity<ResponseData<List<OrganizationArea>>> findByNameOrCode(@RequestParam(value = "text") String text, 
                                           @RequestParam(value = "size") Integer size) {
        return ResponseData.ok(organizationAreaService.findByCodeOrName(text, size));
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(entityName, authority, "部门区域");
    }
}
