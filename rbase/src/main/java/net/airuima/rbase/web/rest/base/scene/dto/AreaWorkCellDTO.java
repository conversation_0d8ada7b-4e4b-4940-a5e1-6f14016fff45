package net.airuima.rbase.web.rest.base.scene.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/4/19 10:10
 **/
@Schema(description = "区域工位DTO")
public class AreaWorkCellDTO {

    @NotNull
    @Schema(description = "区域Id")
    private Long areaId;

    @NotNull
    @Schema(description = "工位ID列表")
    private List<Long> workCellIds;

    public Long getAreaId() {
        return areaId;
    }

    public AreaWorkCellDTO setAreaId(Long areaId) {
        this.areaId = areaId;
        return this;
    }

    public List<Long> getWorkCellIds() {
        return workCellIds;
    }

    public AreaWorkCellDTO setWorkCellIds(List<Long> workCellIds) {
        this.workCellIds = workCellIds;
        return this;
    }
}
