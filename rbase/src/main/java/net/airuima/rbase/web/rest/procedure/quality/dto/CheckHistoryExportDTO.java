package net.airuima.rbase.web.rest.procedure.quality.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.procedure.quality.CheckHistory;
import net.airuima.rbase.domain.procedure.quality.CheckHistoryDetail;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
public class CheckHistoryExportDTO {

    /**
     * 单据号
     */
    @Schema(description = "单据号")
    @Excel(name = "单据号",orderNum = "1",needMerge = true , width = 15)
    private String serialNumber;

    @Excel(name = "工单号",orderNum = "2",needMerge = true , width = 15)
    @Schema(description = "工单号")
    private String workSheetSerialNumber;

    /**
     * 检测类型
     */
    @Schema(description = "检验类型(首检0/巡检1/终检3/抽检4)")
    @Excel(name = "检验类型",orderNum = "3",replace = {"首检_0","巡检_1","末检_2","终检_3","抽检_4"},needMerge = true , width = 15)
    private Integer category;

    @Excel(name = "项目类型",orderNum = "4",needMerge = true , width = 15)
    @Schema(description = "项目类型")
    private String varietyName;

    @Schema(description = "质检方案")
    @Excel(name = "质检方案",orderNum = "5",needMerge = true , width = 15)
    private String checkRuleName;

    @Excel(name = "工位名称",orderNum = "6",needMerge = true , width = 15)
    @Schema(description = "工位名称")
    private String workCellName;

    @Excel(name = "工位编码",orderNum = "7",needMerge = true , width = 15)
    @Schema(description = "工位编码")
    private String workCellCode;

    @Excel(name = "工序名称",orderNum = "8",needMerge = true , width = 15)
    @Schema(description = "工序名称")
    private String stepName;

    @Excel(name = "工序编码",orderNum = "9",needMerge = true , width = 15)
    @Schema(description = "工序编码")
    private String stepCode;

    /**
     * 报检数量
     */
    @Schema(description = "报检数量")
    @Excel(name = "报检数量",orderNum = "10",needMerge = true , width = 15)
    private Integer inspectNumber;

    /**
     * 检测数量
     */
    @Schema(description = "检测数量")
    @Excel(name = "抽样数量",orderNum = "11",needMerge = true , width = 15)
    private Integer number;

    /**
     * 合格数量
     */
    @Schema(description = "合格数量")
    @Excel(name = "合格数量",orderNum = "12",needMerge = true , width = 15)
    private Integer qualifiedNumber;

    /**
     * 不合格数量
     */
    @Schema(description = "不合格数量")
    @Excel(name = "不合格数量",orderNum = "13",needMerge = true , width = 15)
    private Integer unqualifiedNumber;


    @Schema(description = "检测结果")
    @Excel(name = "测试结果",orderNum = "14",replace = {"合格_true","不合格_false"},needMerge = true , width = 15)
    private Boolean result;


    /**
     * 待处理-0/通过-1/重检-2/放行-3/批退-4/MRB-5
     */
    @Schema(description = "处理方式")
    @Excel(name = "处理方式",orderNum = "15",replace = {"待处理_0","通过_1","重检_2","放行_3","批退_4","MRB_5"},needMerge = true , width = 15)
    private Integer dealWay;


    @ExcelCollection(name = "检测数据明细",orderNum = "16")
    @Schema(description = "检测明细信息列表")
    private List<CheckDetailInfo> checkDetailInfoList;

    public CheckHistoryExportDTO() {

    }

    public CheckHistoryExportDTO(CheckHistory checkHistory) {
        this.serialNumber = checkHistory.getSerialNumber();
        this.workSheetSerialNumber = Objects.nonNull(checkHistory.getSubWorkSheet())?checkHistory.getSubWorkSheet().getSerialNumber():checkHistory.getWorkSheet().getSerialNumber();
        this.category = checkHistory.getCategory();
        this.varietyName = Objects.nonNull(checkHistory.getVarietyObj())?checkHistory.getVarietyObj().getName():null;
        this.checkRuleName = Objects.nonNull(checkHistory.getCheckRule())?checkHistory.getCheckRule().getName():null;
        this.workCellName = Objects.nonNull(checkHistory.getWorkCell())?checkHistory.getWorkCell().getName():null;
        this.workCellCode = Objects.nonNull(checkHistory.getWorkCell())?checkHistory.getWorkCell().getCode():null;
        this.stepName = Objects.nonNull(checkHistory.getStep())?checkHistory.getStep().getName():null;
        this.stepCode = Objects.nonNull(checkHistory.getStep())?checkHistory.getStep().getCode():null;
        this.inspectNumber = checkHistory.getInspectNumber();
        this.number = checkHistory.getNumber();
        this.qualifiedNumber = checkHistory.getQualifiedNumber();
        this.unqualifiedNumber = checkHistory.getUnqualifiedNumber();
        this.result = checkHistory.getResult();
        this.dealWay = checkHistory.getDealWay();


    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public CheckHistoryExportDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public String getWorkSheetSerialNumber() {
        return workSheetSerialNumber;
    }

    public CheckHistoryExportDTO setWorkSheetSerialNumber(String workSheetSerialNumber) {
        this.workSheetSerialNumber = workSheetSerialNumber;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public CheckHistoryExportDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public String getVarietyName() {
        return varietyName;
    }

    public CheckHistoryExportDTO setVarietyName(String varietyName) {
        this.varietyName = varietyName;
        return this;
    }

    public String getCheckRuleName() {
        return checkRuleName;
    }

    public CheckHistoryExportDTO setCheckRuleName(String checkRuleName) {
        this.checkRuleName = checkRuleName;
        return this;
    }

    public String getWorkCellName() {
        return workCellName;
    }

    public CheckHistoryExportDTO setWorkCellName(String workCellName) {
        this.workCellName = workCellName;
        return this;
    }

    public String getWorkCellCode() {
        return workCellCode;
    }

    public CheckHistoryExportDTO setWorkCellCode(String workCellCode) {
        this.workCellCode = workCellCode;
        return this;
    }

    public String getStepName() {
        return stepName;
    }

    public CheckHistoryExportDTO setStepName(String stepName) {
        this.stepName = stepName;
        return this;
    }

    public String getStepCode() {
        return stepCode;
    }

    public CheckHistoryExportDTO setStepCode(String stepCode) {
        this.stepCode = stepCode;
        return this;
    }

    public Integer getInspectNumber() {
        return inspectNumber;
    }

    public CheckHistoryExportDTO setInspectNumber(Integer inspectNumber) {
        this.inspectNumber = inspectNumber;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public CheckHistoryExportDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public Integer getQualifiedNumber() {
        return qualifiedNumber;
    }

    public CheckHistoryExportDTO setQualifiedNumber(Integer qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public Integer getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public CheckHistoryExportDTO setUnqualifiedNumber(Integer unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public Boolean getResult() {
        return result;
    }

    public CheckHistoryExportDTO setResult(Boolean result) {
        this.result = result;
        return this;
    }

    public Integer getDealWay() {
        return dealWay;
    }

    public CheckHistoryExportDTO setDealWay(Integer dealWay) {
        this.dealWay = dealWay;
        return this;
    }

    public List<CheckDetailInfo> getCheckDetailInfoList() {
        return checkDetailInfoList;
    }

    public CheckHistoryExportDTO setCheckDetailInfoList(List<CheckDetailInfo> checkDetailInfoList) {
        this.checkDetailInfoList = checkDetailInfoList;
        return this;
    }

    @Schema(description = "检测明细信息")
    public static class CheckDetailInfo{

        @Schema(description = "SN")
        @Excel(name = "SN",needMerge = true , width = 15)
        private String sn;

        @ExcelCollection(name = "检测项目明细")
        private List<CheckItemInfo> checkItemInfos;

        public String getSn() {
            return sn;
        }

        public CheckDetailInfo setSn(String sn) {
            this.sn = sn;
            return this;
        }

        public List<CheckItemInfo> getCheckItemInfos() {
            return checkItemInfos;
        }

        public CheckDetailInfo setCheckItemInfos(List<CheckItemInfo> checkItemInfos) {
            this.checkItemInfos = checkItemInfos;
            return this;
        }

        @Schema(description = "检测项目信息")
        public static class CheckItemInfo{
            @Schema(description = "检测项目名称" )
            @Excel(name = "检测项目名称",width = 15,needMerge = true)
            private String checkItemName;

            @Schema(description = "检测项目数据" )
            @Excel(name = "检测项目数据" , width = 15,needMerge = true)
            private String checkData;

            @Schema(description = "检测项目结果")
            @Excel(name = "检测项目结果",replace = {"合格_true","不合格_false"} , width = 15,needMerge = true)
            private Boolean result;

            @Schema(description = "缺陷原因" )
            @Excel(name = "缺陷原因" , width = 15,needMerge = true)
            private String defectName;

            @ExcelCollection(name = "图片列表")
            private List<DefectImagesInfo> defectImages;

            public String getCheckItemName() {
                return checkItemName;
            }

            public CheckItemInfo setCheckItemName(String checkItemName) {
                this.checkItemName = checkItemName;
                return this;
            }

            public String getCheckData() {
                return checkData;
            }

            public CheckItemInfo setCheckData(String checkData) {
                this.checkData = checkData;
                return this;
            }

            public Boolean getResult() {
                return result;
            }

            public CheckItemInfo setResult(Boolean result) {
                this.result = result;
                return this;
            }

            public String getDefectName() {
                return defectName;
            }

            public CheckItemInfo setDefectName(String defectName) {
                this.defectName = defectName;
                return this;
            }

            public List<DefectImagesInfo> getDefectImages() {
                return defectImages;
            }

            public CheckItemInfo setDefectImages(List<DefectImagesInfo> defectImages) {
                this.defectImages = defectImages;
                return this;
            }

            public static class DefectImagesInfo{
                @Excel(name = "图片", type = 2 ,width = 40 , height = 20,imageType = 2)
                private byte[] defectImage;

                public byte[] getDefectImage() {
                    return defectImage;
                }

                public DefectImagesInfo setDefectImage(byte[] defectImage) {
                    this.defectImage = defectImage;
                    return this;
                }
            }
        }
    }

}
