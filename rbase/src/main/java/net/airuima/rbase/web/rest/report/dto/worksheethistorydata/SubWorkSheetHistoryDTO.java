package net.airuima.rbase.web.rest.report.dto.worksheethistorydata;

import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.base.wearingpart.WearingPart;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetail;
import net.airuima.rbase.domain.procedure.batch.BatchWorkDetailMaterialBatch;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.dynamic.StepDynamicDataColumnGetDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.rbase.dto.rfms.FacilityDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 子工单履历DTO
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
public class SubWorkSheetHistoryDTO {

    /**
     * 子工单基础信息
     */
    private SubWorkSheet subWorkSheet;

    /**
     * 履历列表
     */
    private List<HistoryDTO> historyDTOList;

    public static class HistoryDTO {

        /**
         * 0,投产
         * 1,回退
         */
        private Integer type;

        /**
         * 记录时间
         */
        private LocalDateTime localDateTime;

        /**
         * 生产详情
         */
        private BatchWorkDetail batchWorkDetail;

        /**
         * 员工信息
         */
        private List<StaffDTO> staffDTOList;

        /**
         * 设备信息
         */
        private List<FacilityDTO> facilityDTOList;

        /**
         * 物料信息
         */
        private List<BatchWorkDetailMaterialBatch> batchWorkDetailMaterialBatchList;

        /**
         * 动态数据信息
         */
        private List<StepDynamicDataColumnGetDTO> stepDynamicDataColumnGetDTOList;

        /**
         * 不良项目信息
         */
        private List<UnqualifiedItem> unqualifiedItemList;

        /**
         * 易损件信息
         */
        private List<WearingPart> wearingPartList;

        public Integer getType() {
            return type;
        }

        public HistoryDTO setType(Integer type) {
            this.type = type;
            return this;
        }

        public LocalDateTime getLocalDateTime() {
            return localDateTime;
        }

        public HistoryDTO setLocalDateTime(LocalDateTime localDateTime) {
            this.localDateTime = localDateTime;
            return this;
        }

        public BatchWorkDetail getBatchWorkDetail() {
            return batchWorkDetail;
        }

        public HistoryDTO setBatchWorkDetail(BatchWorkDetail batchWorkDetail) {
            this.batchWorkDetail = batchWorkDetail;
            return this;
        }

        public List<StaffDTO> getStaffDTOList() {
            return staffDTOList;
        }

        public HistoryDTO setStaffDTOList(List<StaffDTO> staffDTOList) {
            this.staffDTOList = staffDTOList;
            return this;
        }

        public List<FacilityDTO> getFacilityDTOList() {
            return facilityDTOList;
        }

        public HistoryDTO setFacilityDTOList(List<FacilityDTO> facilityDTOList) {
            this.facilityDTOList = facilityDTOList;
            return this;
        }

        public List<BatchWorkDetailMaterialBatch> getBatchWorkDetailMaterialBatchList() {
            return batchWorkDetailMaterialBatchList;
        }

        public HistoryDTO setBatchWorkDetailMaterialBatchList(List<BatchWorkDetailMaterialBatch> batchWorkDetailMaterialBatchList) {
            this.batchWorkDetailMaterialBatchList = batchWorkDetailMaterialBatchList;
            return this;
        }

        public List<StepDynamicDataColumnGetDTO> getStepDynamicDataColumnGetDTOList() {
            return stepDynamicDataColumnGetDTOList;
        }

        public HistoryDTO setStepDynamicDataColumnGetDTOList(List<StepDynamicDataColumnGetDTO> stepDynamicDataColumnGetDTOList) {
            this.stepDynamicDataColumnGetDTOList = stepDynamicDataColumnGetDTOList;
            return this;
        }

        public List<UnqualifiedItem> getUnqualifiedItemList() {
            return unqualifiedItemList;
        }

        public HistoryDTO setUnqualifiedItemList(List<UnqualifiedItem> unqualifiedItemList) {
            this.unqualifiedItemList = unqualifiedItemList;
            return this;
        }

        public List<WearingPart> getWearingPartList() {
            return wearingPartList;
        }

        public HistoryDTO setWearingPartList(List<WearingPart> wearingPartList) {
            this.wearingPartList = wearingPartList;
            return this;
        }
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public SubWorkSheetHistoryDTO setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public List<HistoryDTO> getHistoryDTOList() {
        return historyDTOList;
    }

    public SubWorkSheetHistoryDTO setHistoryDTOList(List<HistoryDTO> historyDTOList) {
        this.historyDTOList = historyDTOList;
        return this;
    }
}
