package net.airuima.rbase.web.rest.rmps.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

public class SpecifyContentRelationDTO implements Serializable {

    /**
     * 投产模式
     */
    @Schema(description = "投产模式")
    private Boolean subWsProductionMode;

    /**
     * （子）工单
     */
    @Schema(description = "（子）工单")
    private String serialNumber;

    /**
     * 容器编码
     */
    @Schema(description = "容器编码")
    private String containerCode;

    /**
     * sn
     */
    @Schema(description = "sn")
    private String sn;

    /**
     * 物料编码
     */
    @Schema(description = "物料编码")
    private String materialCode;

    /**
     * 物料批次
     */
    @Schema(description = "物料批次")
    private String materialBatch;

    public Boolean getSubWsProductionMode() {
        return subWsProductionMode;
    }

    public SpecifyContentRelationDTO setSubWsProductionMode(Boolean subWsProductionMode) {
        this.subWsProductionMode = subWsProductionMode;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public SpecifyContentRelationDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public SpecifyContentRelationDTO setContainerCode(String containerCode) {
        this.containerCode = containerCode;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public SpecifyContentRelationDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public SpecifyContentRelationDTO setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
        return this;
    }

    public String getMaterialBatch() {
        return materialBatch;
    }

    public SpecifyContentRelationDTO setMaterialBatch(String materialBatch) {
        this.materialBatch = materialBatch;
        return this;
    }
}
