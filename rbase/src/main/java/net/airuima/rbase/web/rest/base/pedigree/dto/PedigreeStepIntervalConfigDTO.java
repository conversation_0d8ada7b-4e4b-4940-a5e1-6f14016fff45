package net.airuima.rbase.web.rest.base.pedigree.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * 新增产品谱系工序间隔配置Dto
 *
 * <AUTHOR>
 * @date 2022/4/24 9:49
 */
public class PedigreeStepIntervalConfigDTO {

    /**
     * id
     */
    @Schema(description = "id")
    private Long id;

    /**
     * 产品谱系
     */
    @Schema(description = "产品谱系")
    private Long pedigreeId;

    /**
     * 工序
     */
    @Schema(description = "工序")
    private Long stepId;

    /**
     * 工艺路线
     */
    @Schema(description = "工艺路线")
    private Long workFlowId;

    /**
     * 前置工序与时间间隔配置
     */
    private List<PreStepDurationDTO> preStepDurationDtoList;

    public Long getPedigreeId() {
        return pedigreeId;
    }

    public PedigreeStepIntervalConfigDTO setPedigreeId(Long pedigreeId) {
        this.pedigreeId = pedigreeId;
        return this;
    }

    public Long getStepId() {
        return stepId;
    }

    public PedigreeStepIntervalConfigDTO setStepId(Long stepId) {
        this.stepId = stepId;
        return this;
    }

    public Long getWorkFlowId() {
        return workFlowId;
    }

    public PedigreeStepIntervalConfigDTO setWorkFlowId(Long workFlowId) {
        this.workFlowId = workFlowId;
        return this;
    }

    public List<PreStepDurationDTO> getPreStepDurationDtoList() {
        return preStepDurationDtoList;
    }

    public PedigreeStepIntervalConfigDTO setPreStepDurationDtoList(List<PreStepDurationDTO> preStepDurationDtoList) {
        this.preStepDurationDtoList = preStepDurationDtoList;
        return this;
    }

    public Long getId() {
        return id;
    }

    public PedigreeStepIntervalConfigDTO setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * 存放前置工序与时间间隔的关系
     */
    public static class PreStepDurationDTO {
        /**
         * 前置工序
         */
        @Schema(description = "前置工序")
        private Long preStepId;

        /**
         * 时长间隔
         */
        @Schema(description = "时长间隔")
        private String duration;

        /**
         * 单位(0:秒，1:分钟,2:小时,3:天)
         */
        @Schema(description = "单位(0:秒，1:分钟,2:小时,3:天)")
        private int durationUnit;

        public Long getPreStepId() {
            return preStepId;
        }

        public PreStepDurationDTO setPreStepId(Long preStepId) {
            this.preStepId = preStepId;
            return this;
        }

        public String getDuration() {
            return duration;
        }

        public PreStepDurationDTO setDuration(String duration) {
            this.duration = duration;
            return this;
        }

        public int getDurationUnit() {
            return durationUnit;
        }

        public PreStepDurationDTO setDurationUnit(int durationUnit) {
            this.durationUnit = durationUnit;
            return this;
        }
    }
}
