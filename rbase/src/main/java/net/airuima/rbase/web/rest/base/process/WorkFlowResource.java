package net.airuima.rbase.web.rest.base.process;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.dto.process.WorkFlowDTO;
import net.airuima.rbase.dto.sync.SyncResultDTO;
import net.airuima.rbase.dto.sync.SyncWorkFlowDTO;
import net.airuima.rbase.service.base.process.WorkFlowService;
import net.airuima.rbase.web.rest.base.process.dto.WorkFlowCloneDTO;
import net.airuima.rbase.web.rest.base.process.dto.WorkFlowCreateDTO;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.net.URISyntaxException;
import java.util.List;
import java.util.Set;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工艺流程框图Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "工艺路线Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/work-flows")
@AuthorityRegion("工艺模型")
@AuthSkip("D")
public class WorkFlowResource extends ProtectBaseResource<WorkFlow> {

    private static final String MODULE = "工艺路线";
    private final Logger log = LoggerFactory.getLogger(WorkFlowResource.class);
    private final WorkFlowService workFlowService;

    public WorkFlowResource(WorkFlowService workFlowService) {
        this.workFlowService = workFlowService;
        this.mapUri = "/api/work-flows";
    }

    /**
     * 根据框图编码或者名称获取启用的框图
     *
     * @param isEnable     启用
     * @param categoryList 0:正常生产流程;1:返修方案流程
     * @param text         框图编码或者名称
     * @param size         最大返回数据条数
     * <AUTHOR>
     * @updateTime 2020/12/22 18:53
     * @return: java.util.List<net.airuima.rbase.domain.base.process.WorkFlow>
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "框图编码或者名称获取框图信息")
    @GetMapping("/byNameOrCode")
    public ResponseEntity<ResponseData<List<WorkFlow>>> findByNameOrCode(@RequestParam(value = "text", required = false) String text,
                                           @RequestParam(value = "categoryList", required = false) List<Integer> categoryList,
                                           @RequestParam(value = "size") Integer size,
                                           @RequestParam(value = "isEnable", required = false) Boolean isEnable) {

        return ResponseData.ok(workFlowService.findByCodeOrName(text, size, categoryList, isEnable));
    }

    /**
     * 新增流程框图，同时需要绑定工序
     *
     * @param workFlowDto 流程框图DTO
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "新增流程框图")
    @PostMapping("/createCustom")
    public ResponseEntity<ResponseData<WorkFlow>> create(@RequestBody WorkFlowDTO workFlowDto) throws URISyntaxException {
        return ResponseData.ok(workFlowService.saveInstance(workFlowDto).getBody());
    }

    /**
     * 修改流程框图，同时需要修改绑定工序
     *
     * @param workFlowDto 流程框图DTO
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "修改流程框图")
    @PutMapping("/updateCustom")
    public ResponseEntity<ResponseData<WorkFlow>> update(@RequestBody WorkFlowDTO workFlowDto) throws URISyntaxException {
        return ResponseData.ok(workFlowService.updateInstance(workFlowDto).getBody());
    }


    /**
     * 通过产品谱系id和客户id获取工艺路线列表
     *
     * @param pedigreeId 产品谱系id
     * @param clientId 客户od
     * @return org.springframework.http.ResponseEntity<java.util.List< net.airuima.rbase.domain.base.process.WorkFlow> 工艺路线集合
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过产品谱系id获取工艺路线列表")
    @GetMapping("/byPedigreeIdAndClientId")
    public ResponseEntity<ResponseData<Set<WorkFlow>>> byPedigreeIdAndClientId(@RequestParam(value = "pedigreeId") Long pedigreeId, @RequestParam(value = "clientId", required = false) Long clientId) {
        Set<WorkFlow> workFlowList = workFlowService.findByPedigreeIdAndClientId(pedigreeId, clientId);
        return ResponseData.ok(workFlowList);
    }

    /**
     * 新增工艺路线
     *
     * @param entity 新增工艺路线DTO
     * @return : org.springframework.http.ResponseEntity<net.airuima.rbase.domain.base.process.WorkFlow>
     * <AUTHOR>
     * @date 2022/12/13
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PreventRepeatSubmit
    @PostMapping("/custom")
    public ResponseEntity<ResponseData<WorkFlow>> custom(@RequestBody WorkFlowCreateDTO entity) {
        try {
            WorkFlow workFlow = workFlowService.create(entity);
            return ResponseData.ok(workFlow);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 启用/禁用指定工艺路线
     *
     * @param workFlowId
     * @return : org.springframework.http.ResponseEntity<java.lang.Void>
     * <AUTHOR>
     * @date 2022/12/13
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "启用/禁用指定工艺路线")
    @Parameters({
            @Parameter(name = "workFlowId", description = "工艺路线ID", required = true)
    })
    @PutMapping("/workFlowId/{workFlowId}")
    public ResponseEntity<ResponseData<Void>> enableByWorkFlowId(@PathVariable("workFlowId") Long workFlowId) {
        try {
            workFlowService.enableByWorkFlowId(workFlowId);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            log.info(e.getMessage(), e);
            return ResponseData.error(e);
        }
    }

    /**
     * 工艺路线同步
     * @param syncWorkFlowList 同步工艺路线数据
     * <AUTHOR>
     * @date  2023/3/15
     * @return java.util.List<net.airuima.rbase.dto.sync.SyncResultDTO>
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "同步工艺路线信息")
    @PostMapping("/syncWorkFlow")
    public ResponseEntity<ResponseData<List<SyncResultDTO>>> syncWorkFlow(@RequestBody List<SyncWorkFlowDTO> syncWorkFlowList){
        try {
           return ResponseData.ok(workFlowService.syncWorkFlow(syncWorkFlowList));
        }catch (Exception e){
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 克隆工艺路线
     *
     * @param workFlowCloneDto 工艺路线克隆参数
     * @return org.springframework.http.ResponseEntity<net.airuima.rbase.domain.base.process.WorkFlow> 工艺路线
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "克隆工艺路线")
    @PostMapping("/clone")
    public ResponseEntity<ResponseData<WorkFlow>> cloneWorkFlow(@RequestBody WorkFlowCloneDTO workFlowCloneDto) {
        try {
            WorkFlow workFlow = workFlowService.cloneWorkFlow(workFlowCloneDto);
            return ResponseData.ok(workFlow);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 通过编码和否启用查询对应工艺路线
     *
     * @param code 编码
     * @param isEnable 是否启用
     * @return org.springframework.http.ResponseEntity<net.airuima.rbase.domain.base.process.WorkFlow> 工艺路线
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过编码和否启用查询对应工艺路线")
    @GetMapping("/byCode")
    public ResponseEntity<ResponseData<WorkFlow>> findByCode(@RequestParam(value = "code") String code, @RequestParam(value = "isEnable",required = false) Boolean isEnable) {
        return ResponseData.ok(workFlowService.findByCode(code, isEnable));
    }


    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

}
