package net.airuima.rbase.web.rest.base.process.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工艺流程框图Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "新增工艺路线DTO", description = "新增工艺路线DTO")
public class WorkFlowCreateDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 框图编码
     */
    @Schema(description = "框图编码")
    private String code;

    /**
     * 流程框图类型(0:正常生产流程;1:返在线修流程;2:离线返修流程)
     */
    @Schema(description = "流程框图类型(0:正常生产流程;1:在线返修流程;2:离线返修流程)")
    private Integer category;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public int getCategory() {
        return category;
    }

    public void setCategory(int category) {
        this.category = category;
    }
}
