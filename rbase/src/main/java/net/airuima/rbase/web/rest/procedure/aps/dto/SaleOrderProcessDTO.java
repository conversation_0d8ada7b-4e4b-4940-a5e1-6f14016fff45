package net.airuima.rbase.web.rest.procedure.aps.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.aps.WorkSheetSimpleGetDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Schema(description = "销售订单进度明细DTO")
public class SaleOrderProcessDTO {

    /**
     * 销售订单进度
     */
    @Schema(description = "销售订单进度")
    private BigDecimal progress;

    /**
     * 工单及关联的半成品信息
     */
    @Schema(description = "工单及关联的半成品信息")
    private List<WorkSheetSimpleGetDTO> workSheetSimpleGetDTOList;

    public BigDecimal getProgress() {
        return progress;
    }

    public SaleOrderProcessDTO setProgress(BigDecimal progress) {
        this.progress = progress;
        return this;
    }

    public List<WorkSheetSimpleGetDTO> getWorkSheetSimpleGetDTOList() {
        return workSheetSimpleGetDTOList;
    }

    public SaleOrderProcessDTO setWorkSheetSimpleGetDTOList(List<WorkSheetSimpleGetDTO> workSheetSimpleGetDTOList) {
        this.workSheetSimpleGetDTOList = workSheetSimpleGetDTOList;
        return this;
    }
}
