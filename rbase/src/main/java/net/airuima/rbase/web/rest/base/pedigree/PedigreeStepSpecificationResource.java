package net.airuima.rbase.web.rest.base.pedigree;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.dto.ExportDTO;
import net.airuima.dto.ExportParamDTO;
import net.airuima.query.FilterReformer;
import net.airuima.query.QueryCondition;
import net.airuima.query.QueryConditionParser;
import net.airuima.query.SearchFilter;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepSpecification;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.proxy.document.RbaseDocumentProxy;
import net.airuima.rbase.service.base.pedigree.IPedigreeStepSpecificationExtraQueryService;
import net.airuima.rbase.service.base.pedigree.PedigreeStepSpecificationService;
import net.airuima.util.*;
import net.airuima.web.ProtectBaseResource;
import net.airuima.web.rest.errors.BadRequestAlertException;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.ui.ModelMap;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工序指标Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "产品谱系工序指标Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/pedigree-step-specifications")
@AuthorityRegion("工艺模型")
@FuncInterceptor("ESop")
public class PedigreeStepSpecificationResource extends ProtectBaseResource<PedigreeStepSpecification> {
    private static final String EXCEPTION = "exception";
    private static final String MODULE = "工序指标";

    private final PedigreeStepSpecificationService pedigreeStepSpecificationService;

    @Autowired
    private IPedigreeStepSpecificationExtraQueryService[] pedigreeStepSpecificationExtraQueryServices;

    @Autowired
    private RbaseDocumentProxy rbaseDocumentProxy;

    public PedigreeStepSpecificationResource(PedigreeStepSpecificationService pedigreeStepSpecificationService) {
        this.pedigreeStepSpecificationService = pedigreeStepSpecificationService;
        this.mapUri = "/api/pedigree-step-specifications";
    }

    /**
     * getAll 重写 展示相关文件
     * @param pageable 分页参数
     * @param token 令牌
     * @param request 请求
     * @return org.springframework.http.ResponseEntity<java.util.List< net.airuima.rbase.domain.base.pedigree.PedigreeStepSpecification> 产品谱系工序指标数据
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @GetMapping
    @Override
    public ResponseEntity<List<PedigreeStepSpecification>> getAll(@PageableDefault(size = 1) Pageable pageable, Long token, HttpServletRequest request) {
        List<SearchFilter> lists = new ArrayList<>();
        ResponseEntity<List<PedigreeStepSpecification>> pedigreeStepSpecificationResponseEntity;
        lists = pedigreeStepSpecificationExtraQueryServices[0].getExtraQueryConditionWhenGetAll(lists);
        if(!CollectionUtils.isEmpty(lists)){
            List<SearchFilter> finalLists = lists;
            pedigreeStepSpecificationResponseEntity = this.dataTables(searchFilters -> finalLists.toArray(new SearchFilter[0]), pageable, token, null, request);
        }else {
            pedigreeStepSpecificationResponseEntity = this.dataTables(null, pageable, token, null, request);
        }
        //产品谱系工序指标集合
        List<PedigreeStepSpecification> body = pedigreeStepSpecificationResponseEntity.getBody();
        // 设置相关指标文件
        if(!CollectionUtils.isEmpty(body)){
            body.forEach(i -> i.setDocumentDTOList(Optional.ofNullable(rbaseDocumentProxy.getByRecordId(i.getId())).orElse(Lists.newArrayList())));
        }
        return pedigreeStepSpecificationResponseEntity;
    }


    /**
     * 查询重写 展示相关文件
     * @param pageable 分页参数
     * @param qcs 查询条件
     * @param request 请求
     * @return org.springframework.http.ResponseEntity<java.util.List< net.airuima.rbase.domain.base.pedigree.PedigreeStepSpecification> 产品谱系工序指标数据
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping(
            value = {"/search"},
            produces = {"application/json"}
    )
    @Override
    public ResponseEntity<List<PedigreeStepSpecification>> searchQuery(Pageable pageable, @RequestBody List<QueryCondition> qcs, HttpServletRequest request) {
        if (ValidateUtils.isValid(qcs)) {
            qcs = pedigreeStepSpecificationExtraQueryServices[0].getExtraQueryConditionWhenSearchQuery(qcs);
        }
        // 构建查询条件
        Specification<PedigreeStepSpecification> spec = QueryConditionParser.buildSpecificationWithClassName(GenericsUtils.getSuperClassGenericType(this.getClass()).getName(), qcs, this.filters, this.filterReformer);
        // 查询分页数据
        Page<PedigreeStepSpecification> page = this.getService().find(spec, pageable);
        // 响应头
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, request.getRequestURI().endsWith("search") ? request.getRequestURI() : request.getRequestURI() + "/search");
        List<PedigreeStepSpecification> body = page.getContent();
        // 设置相关指标文件
        if(!CollectionUtils.isEmpty(body)){
            body.forEach(i -> i.setDocumentDTOList(Optional.ofNullable(rbaseDocumentProxy.getByRecordId(i.getId())).orElse(Lists.newArrayList())));
        }
        return new ResponseEntity(page.getContent(), headers, HttpStatus.OK);
    }


    /**
     * 新增重写
     *
     * @param param 产品谱系工序指标参数
     * @return : ResponseEntity<ResponseData<PedigreeStepSpecification>>
     * <AUTHOR>
     * @date 2022/8/19
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping
    @Override
    public ResponseEntity<PedigreeStepSpecification> create(@Valid @RequestBody PedigreeStepSpecification param) throws URISyntaxException {
        BaseResultDTO<PedigreeStepSpecification> baseResultDto = pedigreeStepSpecificationService.saveInstance(param);
        if (Constants.KO.equals(baseResultDto.getStatus())) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(baseResultDto.getKey(), baseResultDto.getMessage())).build();
        } else {
            return (ResponseEntity.created(new URI(this.mapUri + "/" + baseResultDto.getData().getId())).headers(HeaderUtil.createdAlert(this.entityName, baseResultDto.getData().getId().toString()))).body(baseResultDto.getData());
        }
    }

    /**
     * 更新重写
     *
     * @param pedigreeStepSpecification 产品谱系工序指标参数
     * @return : ResponseEntity<ResponseData<PedigreeStepSpecification>>
     * <AUTHOR>
     * @date 2022/8/18
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PutMapping
    @Override
    public ResponseEntity<PedigreeStepSpecification> update(@Valid @RequestBody PedigreeStepSpecification pedigreeStepSpecification) throws URISyntaxException {
        BaseResultDTO<PedigreeStepSpecification> baseResultDto =  pedigreeStepSpecificationService.saveInstance(pedigreeStepSpecification);
        if (Constants.KO.equals(baseResultDto.getStatus())) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(baseResultDto.getKey(), baseResultDto.getMessage())).build();
        } else {
            return ResponseEntity.ok().headers(HeaderUtil.updatedAlert(this.entityName, pedigreeStepSpecification.getId().toString())).body(baseResultDto.getData());
        }
    }

    /**
     * 详情重写
     *
     * @param id 产品谱系工序指标ID
     * @return : ResponseEntity<ResponseData<PedigreeStepSpecification>>
     * <AUTHOR>
     * @date 2022/8/18
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @GetMapping({"/{id}"})
    @Override
    public ResponseEntity<PedigreeStepSpecification> get(@PathVariable Long id) {
        try {
            PedigreeStepSpecification pedigreeStepSpecification = pedigreeStepSpecificationService.get(id);
            return ResponseEntity.ok().body(pedigreeStepSpecification);
        } catch (ResponseException responseException){
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        }catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, EXCEPTION, e.getMessage())).build();
        }
    }


    /**
     * 根据产品谱系ID,工艺路线Id、客户ID、工序ID获取工序指标信息
     * @param pedigreeId 产品谱系主键id
     * @param clientId 客户ID
     * @param workFlowId 工艺路线主键id
     * @param stepId 工序ID
     * @return List<PedigreeStepMaterialRule>
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "根据产品谱系ID,工艺路线Id、客户ID、工序ID获取工序指标信息")
    @GetMapping("/byConditions")
    public ResponseEntity<ResponseData<PedigreeStepSpecification>> byCondition(@RequestParam(value = "pedigreeId",required = false) Long pedigreeId,
                                                                               @RequestParam(value = "workFlowId",required = false) Long workFlowId,
                                                                               @RequestParam(value = "clientId",required = false) Long clientId,
                                                                               @RequestParam(value = "stepId",required = false) Long stepId){
        PedigreeStepSpecification pedigreeStepSpecification = pedigreeStepSpecificationService.findAllByPedigreeIdAndClientIdAndWorkFlowIdAndStepId(pedigreeId, clientId,workFlowId, stepId);
        if(!Objects.isNull(pedigreeStepSpecification)){
            pedigreeStepSpecification.setDocumentDTOList(Optional.ofNullable(rbaseDocumentProxy.getByRecordId(pedigreeStepSpecification.getId())).orElse(Lists.newArrayList()));
        }
        return ResponseData.ok(pedigreeStepSpecification);
    }


    /**
     * 产品谱系工序指标导入 重写支持优先级配置
     * @param file 导入文件
     * @param data 数据
     * @param suffix 后缀
     * @param metaColumn 原数据
     * @param response 响应
     * @return org.springframework.http.ResponseEntity 通用响应
     */

    /**
     * 产品谱系工序指标导入 重写支持优先级配置
     * @param file 导入文件
     * @param data 数据
     * @param suffix 后缀
     * @param metaColumn 原数据
     * @param response 响应
     * @return org.springframework.http.ResponseEntity 通用响应
     */
    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_IMPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping({"/importTableExcel"})
    public ResponseEntity<Void> importTableExcel(@RequestParam("file") MultipartFile file, @RequestParam("data") String data, @RequestParam(value = "suffix",required = false) String suffix, @RequestParam(value = "metaColumn",required = false) String metaColumn, HttpServletResponse response) throws Exception {
        try{
            pedigreeStepSpecificationService.importPedigreeStepSpecification(file,response);
            return ResponseEntity.ok().headers(HeaderUtil.succeedAlert("import")).build();
        }catch (ResponseException responseException){
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        }catch (Exception e){
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, EXCEPTION, e.getMessage())).build();
        }
    }

    /**
     * 导出重写
     */
    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_EXPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    public void exportExcel(ModelMap modelMap, @Parameter(schema = @Schema(implementation = ExportDTO.class),required = true,description = "导出数据参数DTO") @RequestBody ExportDTO exportDTO, HttpServletRequest request, HttpServletResponse response) throws Exception {

        if (exportDTO.getExportTemplate() != null && exportDTO.getExportTemplate()) {
            pedigreeStepSpecificationService.exportExcel(null,response, exportDTO.getExcelTitle());
        }else {
            Specification<PedigreeStepSpecification> spec = QueryConditionParser.buildSpecificationWithClassName(this.typeClazz.getName(), exportDTO.getQcs(), this.filters, this.filterReformer);
            List<PedigreeStepSpecification> pedigreeStepSpecifications = pedigreeStepSpecificationService.find(spec);
            pedigreeStepSpecificationService.exportExcel(pedigreeStepSpecifications,response, exportDTO.getExcelTitle());
        }
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

}
