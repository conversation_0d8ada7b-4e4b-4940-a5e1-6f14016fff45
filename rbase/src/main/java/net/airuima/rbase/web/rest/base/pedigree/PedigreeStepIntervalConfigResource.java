package net.airuima.rbase.web.rest.base.pedigree;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.dto.ExportParamDTO;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepIntervalConfig;
import net.airuima.rbase.service.base.pedigree.PedigreeStepIntervalConfigService;
import net.airuima.rbase.web.rest.base.pedigree.dto.PedigreeStepIntervalConfigDTO;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 产品谱系工序间隔配置Resource
 *
 * <AUTHOR>
 * @date 2022/4/11 15:18
 */
@Tag(name = "产品谱系工序间隔配置Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/pedigree-step-interval-config")
@AuthorityRegion("工艺模型")
@FuncInterceptor("StepInterval")
public class PedigreeStepIntervalConfigResource extends ProtectBaseResource<PedigreeStepIntervalConfig> {

    private static final String MODULE = "工序间隔配置";

    private final PedigreeStepIntervalConfigService pedigreeStepIntervalConfigService;

    public PedigreeStepIntervalConfigResource(PedigreeStepIntervalConfigService pedigreeStepIntervalConfigService) {
        this.pedigreeStepIntervalConfigService = pedigreeStepIntervalConfigService;
        this.mapUri = "/api/pedigree-step-interval-config";
    }

    /**
     * 新增产品谱系工序间隔配置
     *
     * @param pedigreeStepIntervalConfigDto 新增产品谱系工序间隔配置Dto
     * @return ResponseEntity
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "新增产品谱系工序间隔配置")
    @PostMapping("/create")
    public ResponseEntity<ResponseData<Void>> create(@RequestBody PedigreeStepIntervalConfigDTO pedigreeStepIntervalConfigDto) {
        try {
            pedigreeStepIntervalConfigService.create(pedigreeStepIntervalConfigDto);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
        return ResponseData.save();
    }

    /**
     * 通过产品谱系主键ID、工序主键ID、工艺路线主键ID获取产品谱系工序间隔配置
     * @param pedigreeId 产品谱系ID
     * @param workFlowId 工艺路线ID
     * @param stepId 工序ID
     * @return  List<PedigreeStepIntervalConfig>
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "根据产品谱系ID,工艺路线Id、工序ID获取工序间隔信息")
    @GetMapping("/byConditions")
    public ResponseEntity<ResponseData<List<PedigreeStepIntervalConfig>>> byCondition(@RequestParam(value = "pedigreeId",required = false) Long pedigreeId,
                                                                                      @RequestParam(value = "workFlowId",required = false) Long workFlowId,
                                                                                      @RequestParam(value = "stepId",required = false) Long stepId){
        return ResponseData.ok(pedigreeStepIntervalConfigService.findAllByPedigreeIdAndWorkFlowIdAndStepId(pedigreeId,workFlowId, stepId));
    }

    /**
     * 批量更新产品谱系工序上料规则
     * @param pedigreeStepMaterialRules 参数列表
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "批量更新产品谱系工序时间间隔")
    @PreventRepeatSubmit
    @PostMapping("/batch/custom")
    public ResponseEntity<ResponseData<Void>> batch(@RequestBody List<PedigreeStepIntervalConfigDTO> pedigreeStepIntervalConfigDTOList){
        try {
            pedigreeStepIntervalConfigService.batchUpdate(pedigreeStepIntervalConfigDTOList);
            return ResponseData.save();
        }  catch (ResponseException e) {
            return ResponseData.error(e);
        }
    }



    /**
     * 重写导入
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_IMPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Override
    public ResponseEntity<Void> importTableExcel(@RequestParam("file") MultipartFile file, @RequestParam("data") String data, @RequestParam(value = "suffix", required = false) String suffix,
                                                 @RequestParam(value = "metaColumn", required = false) String metaColumn, HttpServletResponse response) throws Exception {

        this.prepareImportParams();
        List<ExportParamDTO> exportParamDTOList = JSON.parseArray(data, ExportParamDTO.class);
        List<Map<String, Object>> illegalDataList = pedigreeStepIntervalConfigService.importPedigreeStepIntervalConfigExcel(file);
        //获取excel文件信息
        List<Map<String, Object>> rowList = ExcelImportUtil.importExcel(file.getInputStream(), Map.class, this.importParams);
        // 返回不合法的数据
        if (!illegalDataList.isEmpty()) {
            int failedSize = illegalDataList.size();
            List<ExcelExportEntity> excelExportEntityList = exportParamDTOList.stream().map(s -> StringUtils.substringBefore(s.getLabel(), "[[")).map(label -> new ExcelExportEntity(label, label)).collect(Collectors.toList());
            excelExportEntityList.add(new ExcelExportEntity("错误信息", "错误信息"));
            String originalFilename = file.getOriginalFilename();
            if (null == originalFilename || originalFilename.isEmpty()) {
                return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(String.class.getSimpleName()), "fileNameEmpty", "文件名为空")).build();
            }
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "", originalFilename.contains("xlsx") ? ExcelType.XSSF : ExcelType.HSSF), excelExportEntityList, illegalDataList);
            response.setContentType(originalFilename.contains("xlsx") ? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" : "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(originalFilename, "utf-8"));
            response.setStatus(HttpStatus.BAD_REQUEST.value());
            response.setHeader("X-app-alert", "app.import.failure");
            String errorMessage = "上传数据" + rowList.size() + "条,导入成功" + (rowList.size() - failedSize) + "条,导入失败" + failedSize + "条,请检查下载的文件,检查失败的详细原因";
            response.setHeader(HeaderUtil.APP_PARAMS, URLEncoder.encode(errorMessage, "UTF-8"));
            response.setHeader(HeaderUtil.APP_ERROR_MESSAGE, URLEncoder.encode(errorMessage, "UTF-8"));
            workbook.write(response.getOutputStream());
            return ResponseEntity.badRequest().headers(HeaderUtil.failureAlert("import")).build();
        } else {
            return ResponseEntity.ok().headers(HeaderUtil.succeedAlert("import")).build();
        }
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }
}
