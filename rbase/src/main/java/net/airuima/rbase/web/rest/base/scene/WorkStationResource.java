package net.airuima.rbase.web.rest.base.scene;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.base.scene.WorkStation;
import net.airuima.rbase.service.base.scene.WorkStationService;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseContent;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工站Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "工站Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/work-stations")
@AuthorityRegion("生产现场")
@FuncInterceptor("WorkStation")
@AuthSkip("D")
public class WorkStationResource extends ProtectBaseResource<WorkStation> {

    private final WorkStationService workStationService;

    public WorkStationResource(WorkStationService workStationService) {
        this.workStationService = workStationService;
        this.mapUri = "/api/work-stations";
    }

    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "批量更新多个工站相关信息")
    @PutMapping("/updateWorkStations")
    public ResponseEntity<ResponseContent<Void>> updateWorkCells(@RequestBody List<WorkStation> workStationList){
        try{
            workStationService.save(workStationList);
            return ResponseContent.ok().isOkBuild(HeaderUtil.succeedAlert(WorkCell.class.getSimpleName()));
        }catch (Exception e){
            e.printStackTrace();
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
        }
    }

    /**
     * 通过名称和编码模糊查询工站
     *
     * @param text 名称或编码
     * @param isEnable 是否启用
     * @param size 查询数量
     * @return 工站列表
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过名称和编码模糊查询工站")
    @GetMapping("/byNameOrCode")
    public ResponseEntity<ResponseData<List<WorkStation>>> findByNameOrCode(@RequestParam(value = "text") String text,
                                                                            @RequestParam(value = "isEnable",required = false) Boolean isEnable,
                                                                            @RequestParam(value = "size") Integer size) {
        List<WorkStation> workStations = workStationService.findByNameOrCode(text);
        if(ValidateUtils.isValid(workStations)){
            workStations = null != isEnable ? workStations.stream().filter(workStation -> isEnable == workStation.getIsEnable()).collect(Collectors.toList()) : workStations;
        }
        return ResponseData.ok(workStations.stream().limit(size).collect(Collectors.toList()));
    }

    /**
     * 通过生产线ID查询工站列表
     * @param workLineId 生产线ID
     * @return List<WorkStation>
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过生产线ID查询工站列表")
    @GetMapping("/workLine/{workLineId}")
    public ResponseEntity<ResponseData<List<WorkStation>>> findByWorkLineId(@PathVariable("workLineId") Long workLineId){
        return ResponseData.ok(workStationService.findByWorkLineId(workLineId));
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, "工站");
    }

}
