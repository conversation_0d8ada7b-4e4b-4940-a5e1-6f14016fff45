package net.airuima.rbase.web.rest.base.scene;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.service.base.scene.WorkCellService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseContent;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工位Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "工位Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/work-cells")
@AuthorityRegion("生产现场")
@AuthSkip("D")
public class WorkCellResource extends ProtectBaseResource<WorkCell> {

    private final WorkCellService workCellService;

    public WorkCellResource(WorkCellService workCellService) {
        this.workCellService = workCellService;
        this.mapUri = "/api/work-cells";
    }

    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "批量更新多个工位相关信息")
    @PutMapping("/updateWorkCells")
    public ResponseEntity<ResponseContent<Void>> updateWorkCells(@RequestBody List<WorkCell> workCellList){
        try{
            workCellService.save(workCellList);
            return ResponseContent.ok().isOkBuild(HeaderUtil.succeedAlert(WorkCell.class.getSimpleName()));
        }catch (Exception e){
            e.printStackTrace();
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
        }
    }

    /**
     * 通过名称和编码模糊查询工位
     *
     * @param text 名称或编码
     * @param isEnable 是否启用
     * @param size 查询数量
     * @return 工位列表
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过名称和编码模糊查询工位")
    @GetMapping("/byNameOrCode")
    public ResponseEntity<ResponseData<List<WorkCell>>> findByNameOrCode(@RequestParam(value = "text",required = false) String text,
                                                                         @RequestParam(value = "isEnable",required = false) Boolean isEnable,
                                                                         @RequestParam(value = "size") Integer size) {
        return ResponseData.ok(workCellService.findByNameOrCode(text, isEnable,size));
    }

    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "通过工站ID获取工位列表")
    @GetMapping("/workStationId/{workStationId}")
    public ResponseEntity<ResponseData<List<WorkCell>>> byWorkStationId(@PathVariable("workStationId") Long workStationId){
        return ResponseData.ok(workCellService.findByWorkStationId(workStationId));
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(entityName, authority, "工位");
    }

}
