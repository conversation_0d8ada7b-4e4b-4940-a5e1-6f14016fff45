package net.airuima.rbase.web.rest.base.scene;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.dto.scene.WorkLineDTO;
import net.airuima.rbase.service.base.scene.WorkLineService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产线Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "生产线Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/work-lines")
@AuthorityRegion("生产现场")
@AuthSkip("D")
public class WorkLineResource extends ProtectBaseResource<WorkLine> {

    private final WorkLineService workLineService;

    public WorkLineResource(WorkLineService workLineService) {
        this.workLineService = workLineService;
        this.mapUri = "/api/work-lines";
    }

    /**
     * 通过名称和编码模糊查询生产线
     *
     * @param text 名称或编码
     * @param isEnable 是否启用
     * @param size 查询数量
     * @return 生产线列表
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过名称和编码模糊查询生产线")
    @GetMapping("/byNameOrCode")
    public ResponseEntity<ResponseData<List<WorkLine>>> findByNameOrCode(@RequestParam(value = "text") String text,
                                                                         @RequestParam(value = "isEnable",required = false) Boolean isEnable,
                                                                         @RequestParam(value = "size") Integer size) {
        return ResponseData.ok(workLineService.findByNameOrCode(text,isEnable, size));
    }

    /**
     * 通过组织架构ID查询生产线
     *
     * @param organizationId 组织架构ID
     * @return List<WorkLine>
     * @throws
     * <AUTHOR>
     * @date 2020-12-28
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过组织架构ID查询生产线")
    @GetMapping("/byOrganizationId/{organizationId}")
    public ResponseEntity<ResponseData<List<WorkLine>>> findByOrganizationId(@PathVariable("organizationId") Long organizationId) {
        return ResponseData.ok(workLineService.findByOrganizationId(organizationId));
    }

    /**
     * 通过生产线ID获取工站及工位信息
     * <AUTHOR>
     * @param id
     * @return WorkLineDTO
     * @date 2021-04-12
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过生产线ID获取工站及工位信息")
    @GetMapping("/treeData/{id}")
    public WorkLineDTO getTreeData(@PathVariable("id") Long id){

        return workLineService.findTreeDataById(id);
    }


    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, "生产线");
    }
}
