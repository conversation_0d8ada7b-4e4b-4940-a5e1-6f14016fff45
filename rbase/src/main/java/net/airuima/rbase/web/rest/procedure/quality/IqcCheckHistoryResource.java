package net.airuima.rbase.web.rest.procedure.quality;

import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.query.FilterReformer;
import net.airuima.query.SearchFilter;
import net.airuima.rbase.domain.procedure.quality.IqcCheckHistory;
import net.airuima.rbase.service.procedure.quality.IqcCheckHistoryService;
import net.airuima.rbase.web.rest.procedure.quality.dto.IqcCheckDTO;
import net.airuima.rbase.web.rest.procedure.quality.dto.IqcCheckDealDTO;
import net.airuima.rbase.web.rest.procedure.quality.dto.IqcCheckPreviewDTO;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.BaseResource;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 来料检验Resource
 *
 * <AUTHOR>
 */
@Tag(name = "来料检验Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/iqc-check-histories")
@AuthorityRegion("生产质量数据")
@FuncInterceptor("IQC")
@AuthSkip("I")
public class IqcCheckHistoryResource extends BaseResource<IqcCheckHistory> {

    private static final String MODULE = "来料检验";

    private final IqcCheckHistoryService iqcCheckHistoryService;

    public IqcCheckHistoryResource(IqcCheckHistoryService iqcCheckHistoryService) {
        this.iqcCheckHistoryService = iqcCheckHistoryService;
        this.mapUri = "/api/iqc-check-histories";
    }


    /**
     * 分别来料检分页数据
     *
     * @param pageable 分页参数
     * @param token    token
     * @param request  请求request
     * @return org.springframework.http.ResponseEntity<java.util.List < net.airuima.rbase.domain.procedure.quality.IqcCheckHistory>> 分页数据
     */
    @Override
    @Operation(description = "获取来料检数据")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @GetMapping
    public ResponseEntity<List<IqcCheckHistory>> getAll(@PageableDefault(size = 1) Pageable pageable, Long token, HttpServletRequest request) {
        if (Objects.isNull(request.getParameter("status"))) {
            return this.dataTables(null, pageable, token, null, request);
        } else {
            tableName = this.getEntityName() + request.getParameter("status");
            return this.dataTables(searchFilters -> {
                List<SearchFilter> lists = Lists.newArrayList();
                lists.add(new SearchFilter("IEQI_status", request.getParameter("status")));
                return lists.toArray(new SearchFilter[0]);
            }, pageable, token, null, request);
        }
    }


    /**
     * 新增来料检验
     *
     * @param entity 待保存规则
     */
    @Operation(summary = "新增来料检验")
    @PostMapping("/save")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN') or  @sc.checkSecurity()")
    public ResponseEntity<ResponseData<IqcCheckHistory>> createIqcCheckHistory(@Valid @RequestBody IqcCheckHistory entity) {
        try {
            entity = iqcCheckHistoryService.saveInstance(entity);
            return ResponseData.ok(entity);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 修改来料检验
     *
     * @param entity 待保存规则
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData < Void>> 通用返回参数
     */
    @Operation(summary = "修改来料检验")
    @PostMapping("/config")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    public ResponseEntity<ResponseData<IqcCheckHistory>> updateIqcCheckHistory(@Valid @RequestBody IqcCheckHistory entity) {
        try {
            entity = iqcCheckHistoryService.updateInstance(entity);
            return ResponseData.ok(entity);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 执行来料检验
     *
     * @param iqcCheckDto 执行来料检验参数
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData < Void>> 通用返回参数
     */
    @Operation(summary = "执行来料检验")
    @PostMapping("/check")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN') or  @sc.checkSecurity()")
    public ResponseEntity<ResponseData<Void>> check(@Valid @RequestBody IqcCheckDTO iqcCheckDto) throws URISyntaxException {
        try {
            iqcCheckHistoryService.check(iqcCheckDto);
            return ResponseData.ok();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }


    /**
     * 来料检验处理
     *
     * @param iqcCheckDealDTO 来料检验处理参数
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData < Void>> 通用返回参数
     */
    @Operation(summary = "来料检验处理")
    @PostMapping("/deal")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    public ResponseEntity<ResponseData<Void>> deal(@Valid @RequestBody IqcCheckDealDTO iqcCheckDealDTO) {
        try {
            iqcCheckHistoryService.deal(iqcCheckDealDTO);
            return ResponseData.ok();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }


    /**
     * 来料检验预览
     *
     * @param serialNumber 来料检验单单号
     * @param checkRuleId  质检方案ID
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData < net.airuima.rbase.web.rest.procedure.quality.dto.IqcCheckPreviewDTO>> 来料检验预览
     */
    @Operation(summary = "来料检验预览")
    @GetMapping("/preview/{serialNumber}")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or  @sc.checkSecurity()")
    public ResponseEntity<ResponseData<IqcCheckPreviewDTO>> preview(@PathVariable(name = "serialNumber") String serialNumber, @RequestParam(name = "checkRuleId", required = false) Long checkRuleId) throws URISyntaxException {
        try {
            IqcCheckPreviewDTO iqcCheckPreviewDTO = iqcCheckHistoryService.preview(serialNumber, checkRuleId);
            return ResponseData.ok(iqcCheckPreviewDTO);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }



    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

}
