package net.airuima.rbase.web.rest.base.process;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.view.PoiBaseView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.dto.ExportDTO;
import net.airuima.dto.ExportParamDTO;
import net.airuima.query.QueryCondition;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlowStep;
import net.airuima.rbase.dto.process.WorkFlowDTO;
import net.airuima.rbase.service.base.process.WorkFlowStepService;
import net.airuima.util.*;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.SpreadsheetVersion;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 流程框图工序Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "流程框图工序Resource")
@RestController
@RequestMapping("/api/work-flow-steps")
@AuthorityRegion("工艺模型")
public class WorkFlowStepResource extends ProtectBaseResource<WorkFlowStep> {
    private static final String EXCEPTION = "exception";
    private static final String MODULE = "工艺路线定义";

    private final WorkFlowStepService workFlowStepService;

    public WorkFlowStepResource(WorkFlowStepService workFlowStepService) {
        this.workFlowStepService = workFlowStepService;
        this.mapUri = "/api/work-flow-steps";
    }

    /**
     * 根据流程框图ID获取对应工序信息
     *
     * @param workFlowId 框图ID
     * @return WorkFlowDTO
     * @throws
     * <AUTHOR>
     * @date 2020-12-28
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过工艺路线ID获取对应工艺路线及工序信息")
    @GetMapping("/byWorkFlowId/{workFlowId}")
    public ResponseEntity<ResponseData<WorkFlowDTO>> findByWorkFlowId(@PathVariable("workFlowId") long workFlowId) {
        return ResponseData.ok(workFlowStepService.findByWorkFlowId(workFlowId));
    }

    /**
     * 通过工艺路线ID和工序ID获取前置工序数据
     *
     * @param workFlowId 工艺路线ID
     * @param stepId     工序ID
     * @return List
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过工艺路线ID和工序编码获取前置工序数据")
    @GetMapping("/by-pre-step")
    public ResponseEntity<ResponseData<List<Step>>> findPreStep(Long workFlowId, Long stepId) {
        return ResponseData.ok(workFlowStepService.findPreStep(workFlowId, stepId));
    }

    /**
     * 获取当前工序的后置工序
     *
     * @param workFlowId 工艺路线
     * @param stepId     工序id
     *                   org.springframework.http.ResponseEntity<java.util.List<net.airuima.domain.base.process.Step>> 工序集合
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "获取当前工序的后置工序")
    @GetMapping("/by-after-step")
    public ResponseEntity<ResponseData<List<Step>>> findByAfterStep(@RequestParam(value = "workFlowId") Long workFlowId, @RequestParam(value = "stepId") Long stepId) {
        return ResponseData.ok(workFlowStepService.findByAfterStep(workFlowId, stepId));
    }

    /**
     * 工艺路线工序配置有序搜索显示
     *
     * @param pageable 分页
     * @param qcs      搜索条件
     * @param request
     * @return ResponseEntity<List < WorkFlowStep>>
     * <AUTHOR>
     * @date 2022/10/26
     */
    @Override
    @Deprecated
    @PostMapping(value = {"/search"}, produces = {"application/json"})
    public ResponseEntity<List<WorkFlowStep>> searchQuery(Pageable pageable, @RequestBody List<QueryCondition> qcs, HttpServletRequest request) {
        Page<WorkFlowStep> workFlowStepPage = workFlowStepService.searchQuery(pageable, qcs, this.filters, request);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(workFlowStepPage, request.getRequestURI().endsWith("search") ? request.getRequestURI() : request.getRequestURI() + "/search");
        return ResponseEntity.ok().headers(headers).body(workFlowStepPage.getContent());
    }

    /**
     * 导入excel表数据与工艺路线自动进行组合
     *
     * @param workFlowId 工艺路线ID
     * @param excelFile  excel文件
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_IMPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "在工艺路线定义模块导入工序信息")
    @PostMapping("/import")
    public ResponseEntity<ResponseData<Void>> saveWorkFlowStep(Long workFlowId, MultipartFile excelFile) {
        try {
            workFlowStepService.saveWorkFlowStep(workFlowId, excelFile);
            return ResponseData.save();
        }  catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 批量 导入excel表数据与工艺路线自动进行组合
     *
     * @param excelFile excel文件
     * @return ResponseEntity<ResponseData<Void>>
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_IMPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "在工艺路线工序模块导入关系清单")
    @PostMapping("/multiple-import")
    public ResponseEntity<ResponseData<Void>> importWorkFlowSteps(@RequestParam("file") MultipartFile excelFile) {
        try {
            workFlowStepService.saveWorkFlowSteps(excelFile);
            return ResponseData.importExcel();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 工艺路线工序 存在工艺路线搜索可有序导出
     *
     * @param modelMap  模板
     * @param exportDTO 导出模板与过滤参数
     * @param request
     * @param response
     * @return void
     * <AUTHOR>
     * @date 2022/10/26
     */
    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_EXPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping({"/exportTableExcel"})
    public void exportTableExcel(ModelMap modelMap, @RequestBody ExportDTO exportDTO, HttpServletRequest request, HttpServletResponse response) throws Exception {

        List<WorkFlowStep> workFlowSteps = workFlowStepService.exportTableExcel(exportDTO, filters);
        this.exportParams = new ExportParams((String) null, exportDTO.getExcelTitle(), ExcelType.XSSF);
        this.exportParams.setFreezeCol(2);
        if(StringUtils.isNotBlank(exportDTO.getExcelType()) && exportDTO.getExcelType().equals("xls")){
            exportParams = new ExportParams(null, exportDTO.getExcelTitle(), ExcelType.HSSF);
        }
        List<ExportParamDTO> exportParamDTOList = exportDTO.getExportParams();
        List<Map<String, String>> result = new ArrayList();
        if (!exportParamDTOList.isEmpty()) {
            ExcelUtil.dealExcelFilterData(workFlowSteps, exportParamDTOList, result);
        }
        List<ExcelExportEntity> excelExportEntityList = (List) exportParamDTOList.stream().map((s) -> StringUtils.substringBefore(s.getLabel(), "[[")).map((label) -> {
            return new ExcelExportEntity(label, label);
        }).collect(Collectors.toList());
        modelMap.put("mapList", result);
        modelMap.put("EntityList", excelExportEntityList);
        modelMap.put("params", this.exportParams);
        modelMap.put("fileName", URLEncoder.encode(exportDTO.getExcelTitle(), StandardCharsets.UTF_8));
        PoiBaseView.render(modelMap, request, response, "easypoiMapExcelView");
    }

    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_EXPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping({"/v2/exportExcel"})
    public void exportExcel(ModelMap modelMap, @RequestBody ExportDTO exportDTO, HttpServletRequest request, HttpServletResponse response) throws Exception {
        QueryCondition workFlowQueryCondition = exportDTO.getQcs().stream().filter(queryCondition -> "workFlow.code".equals(queryCondition.getFieldName()) && queryCondition.getFieldValue() != null).findFirst()
                .orElse(exportDTO.getQcs().stream().filter(queryCondition -> "workFlow.id".equals(queryCondition.getFieldName()) && queryCondition.getFieldValue() != null).findFirst().orElse(null));
        if (Objects.nonNull(workFlowQueryCondition)) {
            List<WorkFlowStep> workFlowSteps = workFlowStepService.exportTableExcel(exportDTO, filters);
            this.exportParams = new ExportParams((String) null, exportDTO.getExcelTitle(), ExcelType.XSSF);
            this.exportParams.setFreezeCol(2);
            exportParams.setMaxNum(SpreadsheetVersion.EXCEL2007.getLastRowIndex());
            if(StringUtils.isNotBlank(exportDTO.getExcelType()) && exportDTO.getExcelType().equals("xls")){
                exportParams = new ExportParams(null, exportDTO.getExcelTitle(), ExcelType.HSSF);
                exportParams.setMaxNum(SpreadsheetVersion.EXCEL97.getLastRowIndex());
            }
            List<ExportParamDTO> exportParamDTOList = exportDTO.getExportParams();
            List<Map<String, String>> result = new ArrayList();
            if (!exportParamDTOList.isEmpty()) {
                ExcelUtil.dealExcelFilterData(workFlowSteps, exportParamDTOList, result);
            }
            List<ExcelExportEntity> excelExportEntityList = (List) exportParamDTOList.stream().map((s) -> {
                return StringUtils.substringBefore(s.getLabel(), "[[");
            }).map((label) -> {
                return new ExcelExportEntity(label, label);
            }).collect(Collectors.toList());
            modelMap.put("mapList", result);
            modelMap.put("EntityList", excelExportEntityList);
            modelMap.put("params", this.exportParams);
            response.setHeader("message", "export!");
            modelMap.put("fileName", URLEncoder.encode(exportDTO.getExcelTitle(), "utf-8"));
            PoiBaseView.render(modelMap, request, response, "easypoiMapExcelView");
        } else {
            super.exportExcel(modelMap, exportDTO, request, response);
        }
    }

    /**
     * 根据工艺路线查询对应的工序
     *
     * @param workFlowId 工艺路线id
     * @return org.springframework.http.ResponseEntity<java.util.List < net.airuima.domain.base.process.Step>> 工序集合
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "根据工艺路线查询对应的工序")
    @GetMapping("/step/{workFlowId}")
    public ResponseEntity<ResponseData<List<Step>>> findStepByWorkFlow(@PathVariable("workFlowId") long workFlowId) {
        return ResponseData.ok(workFlowStepService.findStepByWorkFlow(workFlowId));
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

}
