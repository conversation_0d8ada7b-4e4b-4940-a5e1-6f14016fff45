package net.airuima.rbase.web.rest.base.pedigree;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.dto.ExportParamDTO;
import net.airuima.rbase.domain.base.pedigree.PedigreeStep;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.service.base.pedigree.PedigreeStepService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.web.rest.errors.BadRequestAlertException;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工序配置Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "产品谱系工艺路线工序配置Resource")
@RestController
@RequestMapping("/api/pedigree-steps")
@AuthorityRegion("工艺模型")
public class PedigreeStepResource extends ProtectBaseResource<PedigreeStep> {

    private static final String MODULE = "工序配置";

    private static final String ERROR = "error";

    private static final String EXCEPTION = "exception";

    private final PedigreeStepService pedigreeStepService;

    public PedigreeStepResource(PedigreeStepService pedigreeStepService) {
        this.pedigreeStepService = pedigreeStepService;
        this.mapUri = "/api/pedigree-steps";
    }

    /**
     * 查询产品谱系配置所有工序
     *
     * @param pedigreeId 产品谱系id
     * @param keyword 产品谱系id
     * @param pedigreeId 产品谱系id
     * @return 产品谱系工序
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @GetMapping("/byPedigreeId")
    @Operation(summary = "查询产品谱系配置所有工序")
    public ResponseEntity<ResponseData<List<Step>>> findByPedigreeId(@RequestParam("pedigreeId") Long pedigreeId,
                                                                     @RequestParam(value = "keyword", required = false) String keyword,
                                                                     @RequestParam(value = "clientId", required = false) Long clientId) {
        List<Step> steps = pedigreeStepService.findByPedigreeIdAndWorkFlowLikeAndClientId(pedigreeId, keyword,clientId);
        return ResponseData.ok(steps);
    }

    /**
     * 查询产品谱系配置所有工序(包括父级)
     *
     * @param pedigreeId 产品谱系id
     * @param keyword    检索关键字
     * @param clientId    客户id
     * @return 产品谱系工序
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @GetMapping("/LastByPedigreeId")
    @Operation(summary = "查询产品谱系所有层级的工序")
    public ResponseEntity<ResponseData<Set<Step>>> findLastStepByPedigreeIdAndWorkFlowLikeAndClientId(@RequestParam("pedigreeId") Long pedigreeId,
                                                                                                      @RequestParam(value = "keyword", required = false) String keyword,
                                                                                                      @RequestParam(value = "clientId", required = false) Long clientId) {
        Set<Step> steps = pedigreeStepService.findLastStepByPedigreeIdAndWorkFlowLikeAndClientId(pedigreeId, keyword,clientId);
        return ResponseData.ok(steps);
    }


    /**
     * 重写新增函数，需要验证请求模式下不可批量生产及绑定容器
     *
     * @param entity 工序配置
     * @return org.springframework.http.ResponseEntity<net.airuima.domain.base.pedigree.PedigreeStep> 工序配置
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Override
    public ResponseEntity<PedigreeStep> create(@Valid @RequestBody PedigreeStep entity) throws URISyntaxException {
        try {
            pedigreeStepService.saveInstance(entity);
            return ResponseEntity.ok().headers(HeaderUtil.createdAlert(this.entityName, "")).build();
        } catch (BadRequestAlertException badRequestAlertException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(badRequestAlertException.getErrorKey(), badRequestAlertException.getMessage())).build();
        }  catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(ERROR, e.getMessage())).build();
        }
    }

    /**
     * 重写更新函数，需要验证请求模式下不可批量生产及绑定容器
     *
     * @param entity 工序配置
     * @return org.springframework.http.ResponseEntity<net.airuima.domain.base.pedigree.PedigreeStep> 工序配置
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Override
    public ResponseEntity<PedigreeStep> update(@Valid @RequestBody PedigreeStep entity) throws URISyntaxException {
        try {
            pedigreeStepService.saveInstance(entity);
            return ResponseEntity.ok().headers(HeaderUtil.updatedAlert(this.entityName, "")).build();
        } catch (BadRequestAlertException badRequestAlertException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(badRequestAlertException.getErrorKey(), badRequestAlertException.getMessage())).build();
        }  catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(ERROR, e.getMessage())).build();
        }
    }


    /**
     * 根据工艺路线Id工序ID获取工序配置信息
     *
     * @param stepId     工序ID
     * @param workFlowId 工艺路线Id
     * @return org.springframework.http.ResponseEntity<net.airuima.domain.base.pedigree.PedigreeStep> 工序配置信息
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "根据工艺路线Id工序ID获取工序配置信息")
    @GetMapping("/byWorkFlowIdAndStepId")
    public ResponseEntity<ResponseData<PedigreeStep>> findByStepId(@RequestParam(value = "stepId", required = true) Long stepId,
                                                                   @RequestParam(value = "workFlowId", required = true) Long workFlowId) {
        PedigreeStep validPedigreeStep = pedigreeStepService.getValidPedigreeStep(workFlowId, stepId);
        return ResponseData.ok(validPedigreeStep);
    }

    /**
     * 通过产品谱系ID、工艺路线ID、工序ID、客户ID查询工序配置
     * @param pedigreeId 产品谱系id
     * @param workFlowId 工艺路线id
     * @param stepId 工序ID
     * @param clientId 客户ID
     * @return PedigreeStep
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "根据产品谱系ID、工艺路线Id、客户ID、工序ID获取工序配置信息")
    @GetMapping("/byConditions")
    public ResponseEntity<ResponseData<PedigreeStep>> byCondition(@RequestParam(value = "pedigreeId",required = false) Long pedigreeId,
                                                                  @RequestParam(value = "workFlowId",required = false) Long workFlowId,
                                                                  @RequestParam(value = "clientId",required = false) Long clientId,
                                                                  @RequestParam(value = "stepId",required = false) Long stepId) {
        return ResponseData.ok(pedigreeStepService.findByPedigreeIdAndWorkFlowIdAndStepIdAndClientId(pedigreeId, workFlowId, stepId, clientId));
    }

    /**
     * 工序配置导入
     *
     * @param file 工序配置导入
     * @return org.springframework.http.ResponseEntity<java.lang.Void>通用返回对象
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_IMPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "工序配置导入")
    @Override
    public ResponseEntity<Void> importTableExcel(@RequestParam("file") MultipartFile file, @RequestParam("data") String data, @RequestParam(value = "suffix", required = false) String suffix, @RequestParam(value = "metaColumn", required = false) String metaColumn, HttpServletResponse response) throws Exception {
        this.prepareImportParams();
        List<ExportParamDTO> exportParamDTOList = JSON.parseArray(data, ExportParamDTO.class);
        List<Map<String, Object>> illegalDataList = pedigreeStepService.importPedigreeStepExcel(file);
        //获取excel文件信息
        List<Map<String, Object>> rowList = ExcelImportUtil.importExcel(file.getInputStream(), Map.class, this.importParams);
        // 返回不合法的数据
        if (!illegalDataList.isEmpty()) {
            int failedSize = illegalDataList.size();
            List<ExcelExportEntity> excelExportEntityList = exportParamDTOList.stream().map(s -> StringUtils.substringBefore(s.getLabel(), "[[")).map(label -> new ExcelExportEntity(label, label)).collect(Collectors.toList());
            excelExportEntityList.add(new ExcelExportEntity("错误信息", "错误信息"));
            String originalFilename = file.getOriginalFilename();
            if (null == originalFilename || originalFilename.isEmpty()) {
                return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(String.class.getSimpleName()), "fileNameEmpty", "文件名为空")).build();
            }
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "", originalFilename.contains("xlsx") ? ExcelType.XSSF : ExcelType.HSSF), excelExportEntityList, illegalDataList);
            response.setContentType(originalFilename.contains("xlsx") ? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" : "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(originalFilename, "utf-8"));
            response.setStatus(HttpStatus.BAD_REQUEST.value());
            response.setHeader("X-app-alert", "app.import.failure");
            String errorMessage = "上传数据" + rowList.size() + "条,导入成功" + (rowList.size() - failedSize) + "条,导入失败" + failedSize + "条,请检查下载的文件,检查失败的详细原因";
            response.setHeader(HeaderUtil.APP_PARAMS, URLEncoder.encode(errorMessage, "UTF-8"));
            response.setHeader(HeaderUtil.APP_ERROR_MESSAGE, URLEncoder.encode(errorMessage, "UTF-8"));
            workbook.write(response.getOutputStream());
            return ResponseEntity.badRequest().headers(HeaderUtil.failureAlert("import")).build();
        } else {
            return ResponseEntity.ok().headers(HeaderUtil.succeedAlert("import")).build();
        }
    }


    /**
     * 删除工序配置
     *
     * @param id 工序配置id
     * @return org.springframework.http.ResponseEntity 通用返回对象
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @DeleteMapping({"/{id}"})
    @Override
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        try {
            pedigreeStepService.deleteEntity(id);
            return ResponseEntity.ok().headers(HeaderUtil.deletedAlert(StringUtils.uncapitalize(PedigreeStep.class.getSimpleName()), "")).build();
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, EXCEPTION, e.getMessage())).build();
        }
    }


    /**
     * 详情重写
     *
     * @param id 工序配置ID
     * @return : org.springframework.http.ResponseEntitynet.airuima.domain.base.pedigree.PedigreeStep> 工序配置
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @GetMapping({"/{id}"})
    @Override
    public ResponseEntity<PedigreeStep> get(@PathVariable Long id) {
        try {
            PedigreeStep pedigreeStep = pedigreeStepService.get(id);
            return ResponseEntity.ok().body(pedigreeStep);
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, EXCEPTION, e.getMessage())).build();
        }
    }


    /**
     * 重写批量删除
     *
     * @param deleteIds 删除的id集合
     * @return org.springframework.http.ResponseEntity 通用返回对象
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Override
    public ResponseEntity<Void> delMultiSel(@RequestBody List<Long> deleteIds) {
        Iterator var2 = deleteIds.iterator();
        while (var2.hasNext()) {
            long currId = (Long) var2.next();
            pedigreeStepService.deleteEntity(currId);
        }
        return ((ResponseEntity.BodyBuilder) ResponseEntity.ok().headers(HeaderUtil.deletedAlert(this.entityName, JSON.toJSONString(deleteIds)))).build();
    }


    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

}
