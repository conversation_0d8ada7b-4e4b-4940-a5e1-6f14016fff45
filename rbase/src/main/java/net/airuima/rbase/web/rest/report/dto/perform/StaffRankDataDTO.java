package net.airuima.rbase.web.rest.report.dto.perform;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 员工报工排行数据DTO
 *
 * <AUTHOR>
 * @date 2023/06/28
 */
@Schema(description = "员工报工排行数据DTO")
public class StaffRankDataDTO {

    /**
     * 员工名字
     */
    @Schema(description = "员工名字")
    private String name;

    /**
     * 员工编码
     */
    @Schema(description = "员工编码")
    private String code;

    /**
     * 报工数量
     */
    @Schema(description = "报工数量")
    private Long number;

    /**
     * 耗时(分钟)
     */
    @Schema(description = "耗时(分钟)")
    private Double workHour;

    public String getName() {
        return name;
    }

    public StaffRankDataDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public StaffRankDataDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public Double getWorkHour() {
        return workHour;
    }

    public StaffRankDataDTO setWorkHour(Double workHour) {
        this.workHour = workHour;
        return this;
    }

    public Long getNumber() {
        return number;
    }

    public StaffRankDataDTO setNumber(Long number) {
        this.number = number;
        return this;
    }

    public StaffRankDataDTO() {
    }

    public StaffRankDataDTO(String name, String code, Long number) {
        this.name = name;
        this.code = code;
        this.number = number;
    }
}
