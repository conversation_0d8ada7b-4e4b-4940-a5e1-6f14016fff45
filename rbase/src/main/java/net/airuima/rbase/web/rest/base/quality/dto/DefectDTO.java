package net.airuima.rbase.web.rest.base.quality.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 缺陷管理DTO
 * <AUTHOR>
 * @date 2023-04-28
 */
@AuditEntity(value = "抽样方案DTO")
public class DefectDTO implements Serializable {

    /**
     * id,新增时无需输入，修改时比输入
     */
    @Schema(description = "id,新增时无需输入，修改时比输入")
    private Long id;

    /**
     * 缺陷编码
     */
    @NotNull
    @Schema(description = "缺陷编码", required = true)
    private String code;

    /**
     * 缺陷名称
     */
    @NotNull
    @Schema(description = "缺陷名称", required = true)
    private String name;

    /**
     * 缺陷类型id
     */
    @NotNull
    @Schema(description = "缺陷类型id", required = true)
    private Long groupId;

    /**
     * 缺陷等级:轻微0/普通1/严重2
     */
    @NotNull
    @Schema(description = "缺陷等级:轻微0/普通1/严重2", required = true)
    private Integer level;

    /**
     * 是否启用(0:禁用;1:启用)
     */
    @NotNull
    @Schema(description = "是否启用(0:禁用;1:启用)", required = true)
    private Boolean isEnable;

    /**
     * 检测项目id列表
     */
    @Schema(description = "检测项目id列表", required = true)
    private List<Long> checkItemIdList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Boolean getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(Boolean idEnable) {
        this.isEnable = idEnable;
    }

    public List<Long> getCheckItemIdList() {
        return checkItemIdList;
    }

    public void setCheckItemIdList(List<Long> checkItemIdList) {
        this.checkItemIdList = checkItemIdList;
    }
}
