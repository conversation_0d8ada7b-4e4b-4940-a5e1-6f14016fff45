package net.airuima.rbase.web.rest.report.dto.perform;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Transient;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.procedure.report.StaffPerform;
import net.airuima.rbase.dto.organization.StaffDTO;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 员工报工排行查询数据DTO
 *
 * <AUTHOR>
 * @date 2023/06/28
 */
@Schema(description = "员工报工排行查询数据DTO")
@FetchEntity
public class StaffRankQueryDataDTO {

    /**
     * 员工ID
     */
    @Schema(description = "员工ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long staffId;

    /**
     * 员工DTO
     */
    @Schema(description = "员工DTO")
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "staffId")
    @Transient
    private StaffDTO staffDto = new StaffDTO();

    /**
     * 报工数量
     */
    @Schema(description = "报工数量")
    private Long number;

    /**
     * 耗时(分钟)
     */
    @Schema(description = "耗时(分钟)")
    private Double workHour;

    public StaffRankQueryDataDTO() {
    }

    public StaffRankQueryDataDTO(StaffPerform staffPerform,Long staffId, Long number,Double workHour) {
        this.staffId = staffId;
        this.number = number;
        this.workHour = workHour;
    }

    public Long getStaffId() {
        return staffId;
    }

    public StaffRankQueryDataDTO setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public StaffDTO getStaffDto() {
        return staffDto;
    }

    public StaffRankQueryDataDTO setStaffDto(StaffDTO staffDto) {
        this.staffDto = staffDto;
        return this;
    }

    public Long getNumber() {
        return number;
    }

    public StaffRankQueryDataDTO setNumber(Long number) {
        this.number = number;
        return this;
    }


    public Double getWorkHour() {
        return workHour;
    }

    public StaffRankQueryDataDTO setWorkHour(Double workHour) {
        this.workHour = workHour;
        return this;
    }
}
