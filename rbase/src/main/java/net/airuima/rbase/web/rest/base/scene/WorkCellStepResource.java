package net.airuima.rbase.web.rest.base.scene;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.domain.MetaTable;
import net.airuima.dto.ExcelMetaColumnDTO;
import net.airuima.dto.ExportDTO;
import net.airuima.dto.ExportParamDTO;
import net.airuima.dto.UserHabitDTO;
import net.airuima.query.QueryCondition;
import net.airuima.query.QueryConditionParser;
import net.airuima.query.SearchFilter;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.base.scene.WorkCellStep;
import net.airuima.rbase.dto.scene.WorkCellStepDTO;
import net.airuima.rbase.service.base.scene.WorkCellService;
import net.airuima.rbase.service.base.scene.WorkCellStepService;
import net.airuima.service.MetaColumnService;
import net.airuima.util.*;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工位工序Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "工位工序管理Resource")
@RestController
@RequestMapping("/api/work-cell-steps")
@AuthorityRegion("生产现场")
public class WorkCellStepResource extends ProtectBaseResource<WorkCellStep> {

    private final Logger log = LoggerFactory.getLogger(WorkCellStepResource.class);

    private final WorkCellStepService workCellStepService;
    @Autowired
    private WorkCellService workCellService;
    @Autowired
    MetaColumnService metaColumnService;

    public WorkCellStepResource(WorkCellStepService workCellStepService) {
        this.workCellStepService = workCellStepService;
        this.mapUri = "/api/work-cell-steps";
    }

    /**
     * 查询该工位绑定的工序信息
     *
     * @param isEnable   是否启用
     * @param keyword    查询内容
     * @param workCellId 工位id
     * @return java.util.List<net.airuima.domain.base.process.Step> 工序列表
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "查询工位绑定的工序信息")
    @GetMapping("/byWorkCellId")
    public ResponseEntity<ResponseData<List<Step>>> findByWorkCellId(@RequestParam("workCellId") Long workCellId, @RequestParam(value = "isEnable", required = false) Boolean isEnable, @RequestParam(value = "keyword", required = false) String keyword) {
        return ResponseData.ok(workCellStepService.findByWorkCellId(workCellId, isEnable, keyword));
    }

    /**
     * 获取全部工位工序列表
     *
     * @param pageable 分页信息
     * @param token
     * @param request
     * @return 工位工序对应列表(一对多)
     */
    @Operation(summary = "获取全部工位工序列表")
    @Override
    public ResponseEntity<List<WorkCellStep>> getAll(Pageable pageable, Long token, HttpServletRequest request) {
        try{
            List<SearchFilter> lists = new ArrayList<>();
            lists.add(new SearchFilter("IEQB_isEnable", "true"));
            this.filterReformer = filters -> lists.toArray(new SearchFilter[0]);
            Specification<WorkCell> spec = QueryConditionParser.buildSpecificationWithClassName(WorkCell.class.getName(), (List<QueryCondition>) null, this.filters, this.filterReformer);
            Page<WorkCell> workCellPage = workCellService.find(spec, pageable);
            List<WorkCellStep> workCellStepList = new ArrayList<>();
            if (Objects.nonNull(workCellPage) && CollectionUtils.isNotEmpty(workCellPage.getContent())) {
                workCellPage.getContent().forEach(workCell -> {
                    WorkCellStep workCellStep = new WorkCellStep();
                    workCellStep.setWorkCell(workCell).setSteps(workCell.getSteps()).setIsEnable(Boolean.TRUE).setId(workCell.getId());
                    workCellStepList.add(workCellStep);
                });
            }
            MetaTable metaTableWithColumn = metaColumnService.findMetaTableWithColumn(this.tableName, token);
            Map<String, Object> searchParams = QueryConditionParser.getParametersStartingWith(request, "search_");
            List<QueryCondition> qcs = this.prepareQueryCondition(request, searchParams, this.isQuerySuperInclude);
            Method prepareHabit = getClass().getSuperclass().getDeclaredMethod("prepareHabit", Integer.class);
            prepareHabit.setAccessible(Boolean.TRUE);
            Optional<UserHabitDTO> userViewHabit = (Optional<UserHabitDTO>)prepareHabit.invoke(this, VIEW_HABIT);
            Optional<UserHabitDTO> userQueryHabit = (Optional<UserHabitDTO>)prepareHabit.invoke(this, QUERY_HABIT);
            Page<WorkCellStep> page = PageableExecutionUtils.getPage(workCellStepList, pageable, () -> workCellPage.getTotalPages());
            HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, this.mapUri);
            HeaderUtil.add(headers, "qcs", JSON.toJSONString(qcs));
            HeaderUtil.add(headers, "fieldState", JSON.toJSONString(metaTableWithColumn));
            HeaderUtil.add(headers, "viewHabit", userViewHabit.map(JSON::toJSONString).orElse(""));
            HeaderUtil.add(headers, "queryHabit",userQueryHabit.map(JSON::toJSONString).orElse(""));
            return ResponseEntity.ok().headers(headers).body(page.getContent());
        }catch (Exception e){
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "getAll", e.getMessage())).build();
        }

    }

    /**
     * 根据条件检索全部工位工序列表
     *
     * @param pageable 分页信息
     * @param qcs      查询条件
     * @param request
     * @return
     */
    @Override
    public ResponseEntity<List<WorkCellStep>> searchQuery(Pageable pageable, @RequestBody List<QueryCondition> qcs, HttpServletRequest request) {
        qcs.stream().filter(queryCondition -> queryCondition.getFieldName().equals("isEnable")).findFirst().ifPresent(qcs::remove);
        qcs.forEach(queryCondition -> {
            if (queryCondition.getFieldName().startsWith("workCell")) {
                queryCondition.setFieldName(queryCondition.getFieldName().split("\\.")[1]);
            }
            if (queryCondition.getFieldName().startsWith("step")) {
                queryCondition.setFieldName(queryCondition.getFieldName().replace("step", "steps"));
            }
        });
        List<SearchFilter> lists = new ArrayList<>();
        lists.add(new SearchFilter("IEQB_isEnable", "true"));
        this.filterReformer = filters -> lists.toArray(new SearchFilter[0]);
        Specification<WorkCell> spec = QueryConditionParser.buildSpecificationWithClassName(WorkCell.class.getName(), qcs, this.filters, filterReformer);
        Page<WorkCell> workCellPage = workCellService.find(spec, pageable);
        List<WorkCellStep> workCellStepList = new ArrayList<>();
        if (Objects.nonNull(workCellPage) && CollectionUtils.isNotEmpty(workCellPage.getContent())) {
            workCellPage.getContent().forEach(workCell -> {
                WorkCellStep workCellStep = new WorkCellStep();
                workCellStep.setWorkCell(workCell).setSteps(workCell.getSteps()).setIsEnable(Boolean.TRUE).setId(workCell.getId());
                workCellStepList.add(workCellStep);
            });
        }
        Page<WorkCellStep> page = PageableExecutionUtils.getPage(workCellStepList, pageable, () -> workCellPage.getTotalPages());
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, request.getRequestURI().endsWith("search") ? request.getRequestURI() : request.getRequestURI() + "/search");
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_IMPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    public ResponseEntity<Void> importTableExcel(@RequestParam("file") MultipartFile file, @RequestParam("data") String data, @RequestParam(value = "suffix", required = false) String suffix, @RequestParam(value = "metaColumn", required = false) String metaColumn, HttpServletResponse response) throws Exception {
        if (file.isEmpty()) {
            return (ResponseEntity.ok().headers(HeaderUtil.createFailureAlert(this.entityName, "FileEmpty", "File invalid."))).build();
        }
        this.prepareImportParams();

        //获取excel文件信息
        List<Map<String, Object>> rowList = ExcelImportUtil.importExcel(file.getInputStream(), Map.class, this.importParams);
        //重写web端传来的data
        data = "[{\"fieldName\":\"workCell.name\",\"label\":\"工位名称\"},{\"fieldName\":\"workCell.code\",\"label\":\"工位编码\"},{\"fieldName\":\"step.name\",\"label\":\"工序名称\"},{\"fieldName\":\"step.code\",\"label\":\"工序编码\"},{\"fieldName\":\"isEnable\",\"label\":\"是否启用[[true,是;false,否;]]\"}]";
        //前端代码的元数据
        List<ExcelMetaColumnDTO> excelMetaColumnDTOList = StringUtils.isNotBlank(metaColumn) ? JSON.parseArray(metaColumn, ExcelMetaColumnDTO.class) : Lists.newArrayList();
        //将json转为对应的对象
        List<ExportParamDTO> exportParamDTOList = JSON.parseArray(data, ExportParamDTO.class);
        List<Map<String, Object>> illegalDataList = importTableExcelInfo(rowList, exportParamDTOList, excelMetaColumnDTOList, suffix);

        // 返回不合法的数据
        if (!illegalDataList.isEmpty()) {
            int failedSize = illegalDataList.size();
            List<ExcelExportEntity> excelExportEntityList = exportParamDTOList.stream().map(s -> StringUtils.substringBefore(s.getLabel(), "[[")).map(label -> new ExcelExportEntity(label, label)).collect(Collectors.toList());
            excelExportEntityList.add(new ExcelExportEntity("错误信息", "错误信息"));
            String originalFilename = file.getOriginalFilename();
            if (null == originalFilename || originalFilename.isEmpty()) {
                return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(String.class.getSimpleName()), "fileNameEmpty", "文件名为空")).build();
            }
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "", originalFilename.contains("xlsx") ? ExcelType.XSSF : ExcelType.HSSF), excelExportEntityList, illegalDataList);
            response.setContentType(originalFilename.contains("xlsx") ? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" : "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(originalFilename, "utf-8"));
            response.setStatus(HttpStatus.BAD_REQUEST.value());
            response.setHeader("X-app-alert", "app.import.failure");
            String errorMessage = "上传数据" + rowList.size() + "条,导入成功" + (rowList.size() - failedSize) + "条,导入失败" + failedSize + "条,请检查下载的文件,检查失败的详细原因";
            response.setHeader(HeaderUtil.APP_PARAMS, URLEncoder.encode(errorMessage, "UTF-8"));
            response.setHeader(HeaderUtil.APP_ERROR_MESSAGE, URLEncoder.encode(errorMessage, "UTF-8"));
            workbook.write(response.getOutputStream());
            return ResponseEntity.badRequest().headers(HeaderUtil.failureAlert("import")).build();
        } else {
            return ResponseEntity.ok().headers(HeaderUtil.succeedAlert("import")).build();
        }
    }

    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_EXPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    public void exportTableExcel(ModelMap modelMap, @RequestBody ExportDTO exportDTO, HttpServletRequest request, HttpServletResponse response) throws Exception {
        //重写exportParams列表
        List<ExportParamDTO> exportParamDtoList = exportDTO.getExportParams().stream().filter(exportDto -> !"steps".equals(exportDto.getFieldName())).collect(Collectors.toList());
        exportParamDtoList.add(new ExportParamDTO("step.name", "工序名称"));
        exportParamDtoList.add(new ExportParamDTO("step.code", "工序编码"));
        exportParamDtoList.stream().filter(exportParamDto -> "isEnable".equals(exportParamDto.getFieldName()))
                .forEach(exportParamDto -> exportParamDto.setLabel("是否启用[[false,否;true,是;]]"));
        exportDTO.setExportParams(exportParamDtoList);
        super.exportTableExcel(modelMap, exportDTO, request, response);

    }

    /**
     * 工位新增工序，需要批量添加
     *
     * @param workCellStepDto 工位工序DTO
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "新增工位工序")
    @PostMapping("/create")
    public ResponseEntity<ResponseData<Void>> create(@RequestBody WorkCellStepDTO workCellStepDto) {
        try{
            workCellStepService.bindSteps(workCellStepDto);
            return ResponseData.save();
        }catch (ResponseException e){
            return ResponseData.error(e);
        }catch (Exception e){
            return ResponseData.error(e);
        }

    }

    /**
     * 工位修改工序，需要批量修改
     *
     * @param workCellStepDto 工位工序DTO
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "工位修改工序")
    @PutMapping("/update")
    public ResponseEntity<ResponseData<Void>> update(@RequestBody WorkCellStepDTO workCellStepDto) {
        try{
            workCellStepService.bindSteps(workCellStepDto);
            return ResponseData.save();
        }catch (ResponseException e){
            return ResponseData.error(e);
        }catch (Exception e){
            return ResponseData.error(e);
        }


    }

    /**
     * 启用/禁用指定工位工序
     *
     * @param workCellId
     * @return : org.springframework.http.ResponseEntity<java.lang.Void>
     * <AUTHOR>
     * @date 2022/12/13
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "启用/禁用指定工位工序")
    @Parameters({
            @Parameter(name = "workCellId", description = "工位ID", required = true)
    })
    @PutMapping("/workCellId/{workCellId}")
    public ResponseEntity<ResponseData<Void>> enableByStepId(@PathVariable("workCellId") Long workCellId) {
        try {
            workCellStepService.enableByWorkCellId(workCellId);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            log.info(e.getMessage(), e);
            return ResponseData.error(e);
        }
    }

    /***
     * 重写导出
     * @param modelMap 模型Map
     * @param exportDTO 导出参数
     * @param request 请求
     * @param response 相应
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_EXPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping({"/v2/exportExcel"})
    @Override
    public void exportExcel(ModelMap modelMap, @RequestBody ExportDTO exportDTO, HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<ExportParamDTO> exportParamDtoList = exportDTO.getExportParams().stream().filter(exportDto -> !"steps".equals(exportDto.getFieldName())).collect(Collectors.toList());
        exportParamDtoList.add(new ExportParamDTO("step.name", "工序名称"));
        exportParamDtoList.add(new ExportParamDTO("step.code", "工序编码"));
        exportParamDtoList.add(new ExportParamDTO("isEnable", "是否启用[[false,否;true,是;]]"));
        this.filterReformer = null;
        exportDTO.setExportParams(exportParamDtoList);
        super.exportExcel(modelMap, exportDTO, request, response);
    }


    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, "工位工序");
    }

}
