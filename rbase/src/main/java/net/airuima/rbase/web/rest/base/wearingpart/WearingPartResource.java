package net.airuima.rbase.web.rest.base.wearingpart;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.base.wearingpart.WearingPart;
import net.airuima.rbase.service.base.wearingpart.IWearingPartBaseService;
import net.airuima.rbase.service.base.wearingpart.WearingPartService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.web.rest.errors.BadRequestAlertException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司 易损件基础信息Resource
 *
 * <AUTHOR>
 * @date 2021/6/23
 */
@Tag(name = "易损件基础信息Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/wearing-parts")
@AuthorityRegion("易损件管理")
@FuncInterceptor("WearingPart")
@AuthSkip("D")
public class WearingPartResource extends ProtectBaseResource<WearingPart> {
    private static final String MODULE = "易损件";
    private final WearingPartService wearingPartService;
    @Autowired
    private IWearingPartBaseService[] iWearingPartBaseServices;

    public WearingPartResource(WearingPartService wearingPartService) {
        this.wearingPartService = wearingPartService;
        this.mapUri = "/api/wearing-parts";
    }

    /**
     * 新增易损件
     * @param entity
     * @return
     * @throws URISyntaxException
     */
    @Operation(summary = "新增易损件")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Override
    public ResponseEntity<WearingPart> create(@Valid @RequestBody WearingPart entity) throws URISyntaxException {
        try{
            WearingPart result = wearingPartService.saveInstance(entity);
            return ResponseEntity.created(new URI(mapUri + "/" + result.getId())).headers(HeaderUtil.createdAlert(entityName, result.getId().toString())).body(result);
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        }  catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "exception", e.getMessage())).build();
        }
    }

    /**
     * 修改易损件
     * @param entity
     * @return
     * @throws URISyntaxException
     */
    @Operation(summary = "修改易损件")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Override
    public ResponseEntity<WearingPart> update(@Valid @RequestBody WearingPart entity) throws URISyntaxException {
        try{
            WearingPart result = wearingPartService.saveInstance(entity);
            return ResponseEntity.ok()
                    .headers(HeaderUtil.updatedAlert(entityName, entity.getId().toString()))
                    .body(result);
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        }  catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "exception", e.getMessage())).build();
        }
    }

    /**
     * 易损件名称或编码获取易损件信息列表
     *
     * @param text 易损件编码或者名称
     * @param size 返回数据个数
     * <AUTHOR>
     * @date 2021/6/25
     * @return java.util.List<net.airuima.rbase.domain.base.wearingpart.WearingPart>
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "易损件名称或编码获取易损件信息列表")
    @GetMapping("/byNameOrCode")
    public ResponseEntity<ResponseData<List<WearingPart>>> findByNameOrCode(@RequestParam(value = "text") String text,
                                                                            @RequestParam(value = "size") int size) {
        return ResponseData.ok(wearingPartService.findByCodeOrName(text, size));
    }

    /**
     * 修改易损件状态
     *
     * @param id     易损件ID
     * @param status 易损件状态
     * @return : java.util.List<net.airuima.rbase.domain.base.wearingpart.WearingPart>
     * <AUTHOR>
     * @date 2023/3/16
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "修改易损件状态")
    @Parameters({
            @Parameter(name = "id", description = "易损件ID", required = true),
            @Parameter(name = "status", description = "易损件状态", required = true)
    })
    @PutMapping("/id/{id}/status/{status}")
    public ResponseEntity<ResponseData<Void>> updateStatus(@PathVariable("id") Long id, @PathVariable("status") Integer status) {
        try {
            iWearingPartBaseServices[0].updateStatus(id, status);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 通过易损件ID进行重置易损件使用寿命
     * @param id 易损件ID
     */
    @Operation(summary = "通过易损件ID进行重置易损件使用寿命")
    @Parameters({
            @Parameter(name = "id", description = "易损件ID", required = true,in = ParameterIn.PATH)
    })
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @PutMapping("/id/{id}/reset")
    public ResponseEntity<ResponseData<Void>> reset(@PathVariable("id") Long id){
        try {
            wearingPartService.reset(id);
            return ResponseData.ok("success.reset","易损件重置成功!");
        }catch (ResponseException e){
            return ResponseData.error(e);
        }catch (Exception e){
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }



    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }
}
