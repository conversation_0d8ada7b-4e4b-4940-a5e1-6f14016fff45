package net.airuima.rbase.web.rest.base.quality;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.quality.UnqualifiedItemCause;
import net.airuima.rbase.dto.quality.UnqualifiedDTO;
import net.airuima.rbase.dto.quality.UnqualifiedItemCauseDTO;
import net.airuima.rbase.service.base.quality.UnqualifiedItemCauseService;
import net.airuima.util.ResponseContent;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良现象原因关系Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "不良现象原因关系Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/unqualified-item-causes")
@AuthorityRegion("质量不良")
@FuncInterceptor("QUnqualifiedCause")
@AuthSkip("D")
public class UnqualifiedItemCauseResource extends ProtectBaseResource<UnqualifiedItemCause> {

    private final UnqualifiedItemCauseService unqualifiedItemCauseService;

    public UnqualifiedItemCauseResource(UnqualifiedItemCauseService unqualifiedItemCauseService) {
        this.unqualifiedItemCauseService = unqualifiedItemCauseService;
        this.mapUri = "/api/unqualified-item-causes";
    }


    /**
     * 不良现象批量或单个新增不良原因
     *
     * @param unqualifiedItemCauseDto 不良现象原因DTO(一对多)
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary= "不良现象批量或单个新增不良原因")
    @PostMapping("/createCustom")
    public ResponseEntity<ResponseData<Void>> createCustom(@Valid @RequestBody UnqualifiedItemCauseDTO unqualifiedItemCauseDto) {
        unqualifiedItemCauseService.bindCauses(unqualifiedItemCauseDto);
        return ResponseData.save();
    }

    /**
     * 不良现象批量或单个修改不良原因
     *
     * @param unqualifiedItemCauseDto 不良现象原因DTO(一对多)
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary= "不良现象批量或单个新增不良原因")
    @PutMapping("/updateCustom")
    public ResponseEntity<ResponseData<Void>> updateCustom(@Valid @RequestBody UnqualifiedItemCauseDTO unqualifiedItemCauseDto) {
        unqualifiedItemCauseService.bindCauses(unqualifiedItemCauseDto);
        return ResponseData.save();
    }

    /**
     * 获得树形结构的不良数据
     *
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary= "获得树形结构的不良数据")
    @GetMapping("/unqualified-tree-data")
    public ResponseEntity<ResponseContent<List<UnqualifiedDTO.UnqualifiedGroupDTO>>> findUnqualifiedTreeData() {
        return unqualifiedItemCauseService.unqualifiedTreeData();
    }

    /**
     * 新增不良原因同时绑定不良项目
     *
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary= "新增不良原因同时绑定不良项目")
    @PostMapping("/unqualified-item-bind-cause")
    public ResponseEntity<ResponseContent<Void>> saveUnqualifiedItemBindCause(@RequestBody UnqualifiedItemCause unqualifiedItemCause) {
        return unqualifiedItemCauseService.unqualifiedItemBindCause(unqualifiedItemCause);
    }

    /**
     * 删除不良原因绑定的不良项目关系
     *
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary= "删除不良原因绑定的不良项目关系")
    @DeleteMapping("/unqualified-item-bind-cause")
    public ResponseEntity<ResponseContent<Void>> deleteUnqualifiedItemBindCause(@RequestBody UnqualifiedItemCause unqualifiedItemCause) {
        return unqualifiedItemCauseService.deleteUnqualifiedItemBindCause(unqualifiedItemCause);
    }

    /**
     * 删除不良原因同时删除绑定不良项目关系
     * <AUTHOR>
     * @param unqualifiedItemCause
     * @return ResponseEntity<ResponseContent<Void>>
     * @date 2021-04-13
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary= "删除不良原因同时删除绑定不良项目关系")
    @DeleteMapping("/unqualified-item-cause")
    public ResponseEntity<ResponseContent<Void>> deleteUnqualifiedItemCause(@RequestBody UnqualifiedItemCause unqualifiedItemCause){
        return unqualifiedItemCauseService.deleteUnqualifiedItemCause(unqualifiedItemCause);
    }

    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览不良现象原因";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建不良现象原因";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改不良现象原因";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除不良现象原因";
        }
        return "";
    }

}
