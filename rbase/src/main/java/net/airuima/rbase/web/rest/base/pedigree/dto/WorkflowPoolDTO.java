package net.airuima.rbase.web.rest.base.pedigree.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系绑定流程框图 池
 *
 * <AUTHOR>
 * @date 2021/2/7
 */
@Schema(description = "工艺SPA流程定义DTO")
public class WorkflowPoolDTO {

    @Schema(description = "工艺SPA流程定义-谱系ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pedigreeId;

    @Schema(description = "工艺SPA流程定义-谱系名称")
    private String name;
    @Schema(description = "工艺SPA流程定义-谱系编码")
    private String code;
    @Schema(description = "工艺SPA流程定义-谱系层级")
    private Integer type;
    @Schema(description = "工艺SPA流程定义-流程框图列表")
    private List<WorkFlowInfo> workFlowInfoList;

    public Long getPedigreeId() {
        return pedigreeId;
    }

    public WorkflowPoolDTO setPedigreeId(Long pedigreeId) {
        this.pedigreeId = pedigreeId;
        return this;
    }

    public String getName() {
        return name;
    }

    public WorkflowPoolDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public WorkflowPoolDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public Integer getType() {
        return type;
    }

    public WorkflowPoolDTO setType(Integer type) {
        this.type = type;
        return this;
    }

    public List<WorkFlowInfo> getWorkFlowInfoList() {
        return workFlowInfoList;
    }

    public WorkflowPoolDTO setWorkFlowInfoList(List<WorkFlowInfo> workFlowInfoList) {
        this.workFlowInfoList = workFlowInfoList;
        return this;
    }

    @Schema(description = "工艺SPA流程定义-框图DTO")
    public static class WorkFlowInfo{
        @JsonSerialize(using = ToStringSerializer.class)
        @Schema(description = "框图ID", required = true)
        private Long id;
        /**
         * 名称
         */
        @Schema(description = "框图名称", required = true)
        private String name;

        /**
         * 框图编码
         */
        @Schema(description = "框图编码", required = true)
        private String code;

        /**
         * 是否启用(0:禁用;1:启用)
         */
        @Schema(description = "是否启用(0:禁用;1:启用)")
        private Boolean isEnable;

        /**
         * 流程框图类型(0:正常生产流程;1:返修方案流程)
         */
        @Schema(description = "流程框图类型(0:正常生产流程;1:返修生产流程)", required = true)
        private int category;

        @Schema(description = "产品谱系不良种类返修流程对应的不良种类)")
        private UnqualifiedGroupInfo unqualifiedGroupInfo;

        public Long getId() {
            return id;
        }

        public WorkFlowInfo setId(Long id) {
            this.id = id;
            return this;
        }

        public String getName() {
            return name;
        }

        public WorkFlowInfo setName(String name) {
            this.name = name;
            return this;
        }

        public String getCode() {
            return code;
        }

        public WorkFlowInfo setCode(String code) {
            this.code = code;
            return this;
        }

        public Boolean getIsEnable() {
            return isEnable;
        }

        public WorkFlowInfo setIsEnable(Boolean isEnable) {
            this.isEnable = isEnable;
            return this;
        }

        public int getCategory() {
            return category;
        }

        public WorkFlowInfo setCategory(int category) {
            this.category = category;
            return this;
        }

        public UnqualifiedGroupInfo getUnqualifiedGroupInfo() {
            return unqualifiedGroupInfo;
        }

        public WorkFlowInfo setUnqualifiedGroupInfo(UnqualifiedGroupInfo unqualifiedGroupInfo) {
            this.unqualifiedGroupInfo = unqualifiedGroupInfo;
            return this;
        }

        @Schema(description = "工艺SPA流程定义-不良种类DTO")
        public static class UnqualifiedGroupInfo{
            /**
             * 不良种类ID
             */
            @Schema(description = "不良种类ID")
            @JsonSerialize(using = ToStringSerializer.class)
            private Long id;
            /**
             * 不良种类名称
             */
            @Schema(description = "不良种类ID名称")
            private String name;

            /**
             * 不良种类编码
             */
            @Schema(description = "不良种类ID编码")
            private String code;

            public Long getId() {
                return id;
            }

            public UnqualifiedGroupInfo setId(Long id) {
                this.id = id;
                return this;
            }

            public String getName() {
                return name;
            }

            public UnqualifiedGroupInfo setName(String name) {
                this.name = name;
                return this;
            }

            public String getCode() {
                return code;
            }

            public UnqualifiedGroupInfo setCode(String code) {
                this.code = code;
                return this;
            }
        }
    }
}
