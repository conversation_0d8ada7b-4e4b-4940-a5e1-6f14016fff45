package net.airuima.rbase.web.rest.base.process;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.rbase.domain.base.process.StepGroup;
import net.airuima.rbase.service.base.process.StepGroupService;
import net.airuima.rbase.web.rest.base.process.dto.StepGroupCreateDTO;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工序组别Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "工序组别Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/step-groups")
@AuthorityRegion("工艺模型")
@AuthSkip("D")
public class StepGroupResource extends ProtectBaseResource<StepGroup> {

    private static final String MODULE = "工序组别";
    private final Logger log = LoggerFactory.getLogger(StepGroupResource.class);
    private final StepGroupService stepGroupService;

    public StepGroupResource(StepGroupService stepGroupService) {
        this.stepGroupService = stepGroupService;
        this.mapUri = "/api/step-groups";
    }

    /**
     * 获取所有工序组别
     * @param isEnable
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @GetMapping("/all")
    @Operation(summary = "获取所有工序组别")
    public ResponseEntity<ResponseData<List<StepGroup>>> findAll(@RequestParam(value = "isEnable") Boolean isEnable) {
        return ResponseData.ok(stepGroupService.findAllNotDeleted(isEnable));
    }

    /**
     * 通过工序组名称或编码模糊查询工序组
     *
     * @param text 名称或编码
     * @param size 行数
     * @return 工序组
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过工序组名称或编码模糊查询工序组")
    @GetMapping("/byNameOrCode")
    @Parameters({
           @Parameter(name = "text", description = "工序组名称或编码", required = true),
           @Parameter(name = "size", description = "行数", required = true)
    })
    public ResponseEntity<ResponseData<List<StepGroup>>> findByCodeOrName(@RequestParam(value = "text") String text,
                                            @RequestParam(value = "size") Integer size) {
        return ResponseData.ok(stepGroupService.findByCodeOrName(text, size));
    }

    /**
     * 新增工序组
     *
     * @param entity 新增工序组DTO
     * @return : org.springframework.http.ResponseEntity<net.airuima.rbase.domain.base.process.StepGroup>
     * <AUTHOR>
     * @date 2022/12/13
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PreventRepeatSubmit
    @PostMapping("/custom")
    public ResponseEntity<ResponseData<StepGroup>> custom(@RequestBody StepGroupCreateDTO entity) {
        try {
            StepGroup stepGroup = stepGroupService.create(entity);
            return ResponseData.ok(stepGroup);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 启用/禁用指定工序组
     *
     * @param stepGroupId
     * @return : org.springframework.http.ResponseEntity<java.lang.Void>
     * <AUTHOR>
     * @date 2022/12/13
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "启用/禁用指定工序组")
    @Parameters({
            @Parameter(name = "stepGroupId", description = "工序组ID", required = true)
    })
    @PutMapping("/stepGroupId/{stepGroupId}")
    public ResponseEntity<ResponseData<Void>> enableByStepGroupId(@PathVariable("stepGroupId") Long stepGroupId) {
        try {
            stepGroupService.enableByStepGroupId(stepGroupId);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            log.info(e.getMessage(), e);
            return ResponseData.error(e);
        }
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

}
