package net.airuima.rbase.web.rest.base.scene.dto;

import cn.afterturn.easypoi.excel.annotation.ExcelEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工位工序导出DTO
 *
 * <AUTHOR>
 * @date 2023/08/25
 */
@Schema(description = "工位工序导出DTO")
public class WorkCellStepExportDTO implements Serializable {

    /**
     * 工位
     */
    @Schema(description = "工位")
    @ExcelEntity(name = "工位")
    private WorkCell workCell;

    /**
     * 工序id
     */
    @Schema(description = "工序")
    @ExcelEntity(name = "工序")
    private Step step;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    private String isEnable;

    /**
     * 工序列表
     */
    @Schema(description = "工序列表")
    private String steps;


    public WorkCell getWorkCell() {
        return workCell;
    }

    public WorkCellStepExportDTO setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public WorkCellStepExportDTO setStep(Step step) {
        this.step = step;
        return this;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public WorkCellStepExportDTO setIsEnable(String isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    public String getSteps() {
        return steps;
    }

    public WorkCellStepExportDTO setSteps(String steps) {
        this.steps = steps;
        return this;
    }
}
