package net.airuima.rbase.web.rest.base.quality;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.base.quality.UnqualifiedCause;
import net.airuima.rbase.service.base.quality.UnqualifiedCauseService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 不良原因Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "不良原因Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/unqualified-causes")
@AuthorityRegion("质量不良")
@FuncInterceptor("QUnqualifiedCause")
public class UnqualifiedCauseResource extends ProtectBaseResource<UnqualifiedCause> {

    private static final String MODULE = "不良原因";

    private final UnqualifiedCauseService unqualifiedCauseService;

    public UnqualifiedCauseResource(UnqualifiedCauseService unqualifiedCauseService) {
        this.unqualifiedCauseService = unqualifiedCauseService;
        this.mapUri = "/api/unqualified-causes";
    }

    /**
     * 通过名称和编码模糊查询不良原因
     *
     * @param text 名称或编码
     * @param isEnable 是否启用
     * @param size 查询数量
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过名称和编码模糊查询不良原因")
    @GetMapping("/byNameOrCode")
    public ResponseEntity<ResponseData<List<UnqualifiedCause>>> findByNameOrCode(@RequestParam(value = "text") String text,
                                                                                 @RequestParam(value = "isEnable",required = false) Boolean isEnable,
                                                                                 @RequestParam(value = "size") Integer size) {
        return ResponseData.ok(unqualifiedCauseService.findByCodeOrName(text,isEnable, size));
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(entityName, authority, "不良现象原因");
    }

}
