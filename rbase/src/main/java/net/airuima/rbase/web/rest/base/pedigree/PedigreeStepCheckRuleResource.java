package net.airuima.rbase.web.rest.base.pedigree;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.dto.ExportDTO;
import net.airuima.query.QueryConditionParser;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule;
import net.airuima.rbase.service.base.pedigree.PedigreeStepCheckRuleService;
import net.airuima.rbase.web.rest.base.pedigree.dto.PedigreeStepCheckRuleSaveDTO;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.List;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 产品谱系工序检测判定标准Resource
 * <AUTHOR>
 * @date 2021-03-22
 */
@Tag(name = "产品谱系工序检测规则Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/pedigree-step-check-rules")
@AuthorityRegion("质量检测")
@FuncInterceptor("FAI || IPQC || PQC || FQC || IQC")
@AuthSkip("E")
public class PedigreeStepCheckRuleResource extends ProtectBaseResource<PedigreeStepCheckRule> {

    private static final String MODULE = "质检方案";
    private final PedigreeStepCheckRuleService pedigreeStepCheckRuleService;

    public PedigreeStepCheckRuleResource(PedigreeStepCheckRuleService pedigreeStepCheckRuleService) {
        this.pedigreeStepCheckRuleService = pedigreeStepCheckRuleService;
        this.mapUri = "/api/pedigree-step-check-rules";
    }

    /**
     * 新增产品谱系工序检测规则
     * <AUTHOR>
     * @date 2022/11/2
     * @param pedigreeStepCheckRuleSaveDTO 检测规则DTO
     * @return ResponseEntity<ResponseData<PedigreeStepCheckRule>>
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping("/createRule")
    @PreventRepeatSubmit
    @Operation(summary = "新增产品谱系工序检测规则")
    public ResponseEntity<ResponseData<PedigreeStepCheckRuleSaveDTO>> createRule(@Parameter(schema = @Schema(implementation = PedigreeStepCheckRuleSaveDTO.class),required = true,description = "产品谱系工序检测规则信息") @Valid @RequestBody PedigreeStepCheckRuleSaveDTO pedigreeStepCheckRuleSaveDTO) throws URISyntaxException {
        try {
            pedigreeStepCheckRuleService.saveInstance(pedigreeStepCheckRuleSaveDTO);
            return ResponseData.ok(pedigreeStepCheckRuleSaveDTO);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 修改检测规则
     * <AUTHOR>
     * @date 2022/11/2
     * @param pedigreeStepCheckRuleSaveDTO 检测规则DTO
     * @return ResponseEntity<ResponseData<PedigreeStepCheckRule>>
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PutMapping("/updateRule")
    @PreventRepeatSubmit
    @Operation(summary = "修改产品谱系工序检测规则")
    public ResponseEntity<ResponseData<PedigreeStepCheckRuleSaveDTO>> updateRule(@Parameter(schema = @Schema(implementation = PedigreeStepCheckRuleSaveDTO.class),required = true,description = "产品谱系工序检测规则信息") @Valid @RequestBody PedigreeStepCheckRuleSaveDTO pedigreeStepCheckRuleSaveDTO) throws URISyntaxException {
        try {
            pedigreeStepCheckRuleService.updateInstance(pedigreeStepCheckRuleSaveDTO);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 逻辑删除检测规则及关联的检测项目
     * <AUTHOR>
     * @date 2022/11/3 13:31
     * @param id 检测规则id
     * @return ResponseEntity<ResponseData<Void>>
     */
    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @DeleteMapping({"/{id}"})
    @Operation(summary = "逻辑删除检测规则及关联的检测项目")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        pedigreeStepCheckRuleService.deletedById(id);
        return ResponseEntity.ok().headers(HeaderUtil.deletedAlert(this.entityName, id.toString())).build();
    }

    /**
     * 产品谱系工序检测判定标准数据导入
     * 
     * <AUTHOR>
     * @date 2023/03/10 18:00
     * @param file 产品谱系工序检测判定标准数据
     * @param target 条件对象
     * @return ResponseEntity<ResponseData<Void>>
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_IMPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping({"/multiple-import"})
    public ResponseEntity<ResponseData<Void>> multipleImport(@RequestParam("file") MultipartFile file, 
                                               @RequestParam("target") Integer target) {
        try {
            if (file.isEmpty()) {
                return ResponseData.error("FileEmpty", "File invalid.");
            }
            pedigreeStepCheckRuleService.multipleImport(file, target);
            return ResponseData.importExcel();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        }catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 产品谱系工序检测判定标准数据导出
     *
     * @param modelMap      模型映射
     * @param exportDTO     导出DTO对象
     * @param request       请求对象
     * @param response      响应对象
     * @throws IOException io异常
     */
    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "产品谱系工序检测判定标准数据导出")
    public void exportTableExcel(ModelMap modelMap, @RequestBody ExportDTO exportDTO, HttpServletRequest request, HttpServletResponse response) throws IOException {
        // 获得过滤后的数据
        Specification<PedigreeStepCheckRule> spec = QueryConditionParser.buildSpecificationWithClassName(PedigreeStepCheckRule.class.getName(), exportDTO.getQcs(), this.filters, filterReformer);
        List<PedigreeStepCheckRule> pedigreeStepCheckRuleList = pedigreeStepCheckRuleService.find(spec);
        pedigreeStepCheckRuleService.exportTableExcel(pedigreeStepCheckRuleList, exportDTO, response);
    }

    /**
     * 初始化检测方案
     */
    @Operation(summary = "初始化检测方案")
    @GetMapping("/init")
    public ResponseEntity<ResponseData<Void>> init(){
        pedigreeStepCheckRuleService.init();
        return ResponseData.save();
    }


    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }
}
