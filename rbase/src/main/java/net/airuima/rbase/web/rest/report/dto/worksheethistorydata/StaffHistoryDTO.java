package net.airuima.rbase.web.rest.report.dto.worksheethistorydata;

import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.base.scene.WorkCellStaff;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.rbase.dto.skill.SkillDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 员工履历DTO
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
public class StaffHistoryDTO {

    /**
     * 员工信息
     */
    private StaffDTO staff;

    /**
     * 履历列表
     */
    private List<HistoryDTO> historyDTOList;

    public static class HistoryDTO {

        /**
         * 0,入职
         * 1,新增技能
         * 2,绑定工位
         * 3,离职
         */
        private Integer type;

        /**
         * 记录时间
         */
        private LocalDateTime localDateTime;

        /**
         * 员工技能信息
         */
        private SkillDTO skill;

        /**
         * 工位信息
         */
        private WorkCell workCell;

        public Integer getType() {
            return type;
        }

        public HistoryDTO setType(Integer type) {
            this.type = type;
            return this;
        }

        public LocalDateTime getLocalDateTime() {
            return localDateTime;
        }

        public HistoryDTO setLocalDateTime(LocalDateTime localDateTime) {
            this.localDateTime = localDateTime;
            return this;
        }

        public SkillDTO getSkill() {
            return skill;
        }

        public HistoryDTO setSkill(SkillDTO skill) {
            this.skill = skill;
            return this;
        }

        public WorkCell getWorkCell() {
            return workCell;
        }

        public HistoryDTO setWorkCell(WorkCell workCell) {
            this.workCell = workCell;
            return this;
        }
    }

    public StaffDTO getStaff() {
        return staff;
    }

    public StaffHistoryDTO setStaff(StaffDTO staff) {
        this.staff = staff;
        return this;
    }

    public List<HistoryDTO> getHistoryDTOList() {
        return historyDTOList;
    }

    public StaffHistoryDTO setHistoryDTOList(List<HistoryDTO> historyDTOList) {
        this.historyDTOList = historyDTOList;
        return this;
    }
}
