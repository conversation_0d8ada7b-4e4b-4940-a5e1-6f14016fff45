package net.airuima.rbase.web.rest.base.quality.dto;


import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.base.quality.WorkCellCheckStartRule;
import net.airuima.rbase.dto.bom.MaterialAttributeDTO;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.dto.organization.SupplierDTO;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.util.Optional;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 检测发起规则导入DTO
 * <AUTHOR>
 * @date 2023-04-28
 */
@Schema(description = "检测发起规则导入DTO")
public class WorkCellCheckStartRuleImportDTO implements Serializable {

    /**
     * 质检类型 首检0/巡检1/终检3/抽检4
     */
    @Schema(description = "检测类型(首检0/巡检1/终检3/抽检4)", required = true)
    @Excel(name = "质检类型", replace = {"首检_0", "巡检_1", "终检_3", "抽检_4"})
    private Integer category;

    /**
     * 项目类型编码
     */
    @Schema(description = "项目类型编码")
    @Excel(name = "项目类型编码")
    private String varietyCode;

    /**
     * 工位编码
     */
    @Schema(description = "工位编码，首检/巡检时输入")
    @Excel(name = "工位编码")
    private String workCellCode;

    /**
     * 检测时机
     */
    @Schema(description = "检测时机(0:切换型号;1:切换总工单;2:切换子工单;3:固定周期;4:指定时间)")
    @Excel(name = "检测时机", replace = {"切换型号_0", "切换总工单_1", "切换子工单_2", "固定周期_3", "指定时间_4"})
    private Integer flag;

    /**
     * 工艺路线编码
     */
    @Schema(description = "工艺路线编码,抽检/终检时输入")
    @Excel(name = "工艺路线编码")
    private String workFlowCode;

    /**
     * 工序编码
     */
    @Schema(description = "工序编码，抽检/终检时输入")
    @Excel(name = "工序编码")
    private String stepCode;

    /**
     * 检测周期
     */
    @Schema(description = "检测周期(H)")
    @Excel(name = "检测周期")
    private Double duration;

    /**
     * 固定时间
     */
    @Schema(description = "固定时间(例如 11:45;12:00)")
    @Excel(name = "固定时间")
    private String specifyTime;

    /**
     * 放宽时长
     */
    @Schema(description = "宽放时长(H)")
    @Excel(name = "放宽时长")
    private Double extendTime;

    /**
     * 是否启用(0:禁用;1:启用)
     */
    @Schema(description = "是否启用(0:禁用;1:启用)", required = true)
    @Excel(name = "是否启用", replace = {"禁用_false", "启用_true"})
    private Boolean isEnable;


    /**
     * 物料属性编码
     */
    @Schema(description = "物料属性编码")
    @Excel(name = "物料属性编码")
    private String attributeCode;

    /**
     * 物料编码
     */
    @Schema(description = "物料编码")
    private String materialCode;

    /**
     * 供应商编码
     */
    @Excel(name = "供应商编码")
    @Schema(description = "供应商编码")
    private String  supplierCode;


    /**
     * 客户编码
     */
    @Schema(description = "客户编码")
    @Excel(name = "客户编码")
    private String clientCode;


    public WorkCellCheckStartRuleImportDTO() {
    }

    public WorkCellCheckStartRuleImportDTO(WorkCellCheckStartRule workCellCheckStartRule) {
        this.category = workCellCheckStartRule.getCategory();
        this.varietyCode = !ObjectUtils.isEmpty(workCellCheckStartRule.getVarietyObj())?workCellCheckStartRule.getVarietyObj().getCode():null;
        this.workCellCode = !ObjectUtils.isEmpty(workCellCheckStartRule.getWorkCell())?workCellCheckStartRule.getWorkCell().getCode():null;
        this.flag = workCellCheckStartRule.getFlag();
        this.workFlowCode = !ObjectUtils.isEmpty(workCellCheckStartRule.getWorkFlow())?workCellCheckStartRule.getWorkFlow().getCode():null;
        this.stepCode = !ObjectUtils.isEmpty(workCellCheckStartRule.getStep())?workCellCheckStartRule.getStep().getCode():null;
        this.duration = workCellCheckStartRule.getDuration();
        this.specifyTime = workCellCheckStartRule.getSpecifyTime();
        this.extendTime = workCellCheckStartRule.getExtendTime();
        this.isEnable = workCellCheckStartRule.getIsEnable();
        this.attributeCode = Optional.ofNullable(workCellCheckStartRule.getMaterialAttributeDto()).map(MaterialAttributeDTO::getCode).orElse(null);
        this.supplierCode = Optional.ofNullable(workCellCheckStartRule.getSupplierDto()).map(SupplierDTO::getCode).orElse(null);
        this.materialCode = Optional.ofNullable(workCellCheckStartRule.getMaterialDto()).map(MaterialDTO::getCode).orElse(null);
        this.clientCode = Optional.ofNullable(workCellCheckStartRule.getClientDto()).map(ClientDTO::getCode).orElse(null);
    }


    public String getAttributeCode() {
        return attributeCode;
    }

    public WorkCellCheckStartRuleImportDTO setAttributeCode(String attributeCode) {
        this.attributeCode = attributeCode;
        return this;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public WorkCellCheckStartRuleImportDTO setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
        return this;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public WorkCellCheckStartRuleImportDTO setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
        return this;
    }

    public String getClientCode() {
        return clientCode;
    }

    public WorkCellCheckStartRuleImportDTO setClientCode(String clientCode) {
        this.clientCode = clientCode;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public String getVarietyCode() {
        return varietyCode;
    }

    public void setVarietyCode(String varietyCode) {
        this.varietyCode = varietyCode;
    }

    public String getWorkCellCode() {
        return workCellCode;
    }

    public void setWorkCellCode(String workCellCode) {
        this.workCellCode = workCellCode;
    }

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    public String getWorkFlowCode() {
        return workFlowCode;
    }

    public void setWorkFlowCode(String workFlowCode) {
        this.workFlowCode = workFlowCode;
    }

    public String getStepCode() {
        return stepCode;
    }

    public void setStepCode(String stepCode) {
        this.stepCode = stepCode;
    }

    public Double getDuration() {
        return duration;
    }

    public void setDuration(Double duration) {
        this.duration = duration;
    }

    public String getSpecifyTime() {
        return specifyTime;
    }

    public void setSpecifyTime(String specifyTime) {
        this.specifyTime = specifyTime;
    }

    public Double getExtendTime() {
        return extendTime;
    }

    public void setExtendTime(Double extendTime) {
        this.extendTime = extendTime;
    }

    public Boolean getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(Boolean isEnable) {
        this.isEnable = isEnable;
    }
}
