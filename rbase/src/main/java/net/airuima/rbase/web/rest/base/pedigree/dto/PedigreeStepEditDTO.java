package net.airuima.rbase.web.rest.base.pedigree.dto;

import jakarta.validation.constraints.NotNull;
import net.airuima.rbase.domain.base.process.Step;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/2/8
 */
public class PedigreeStepEditDTO {

    @NotNull
    private Long pedigreeId;

    @NotNull
    private Step step;

    public Long getPedigreeId() {
        return pedigreeId;
    }

    public PedigreeStepEditDTO setPedigreeId(Long pedigreeId) {
        this.pedigreeId = pedigreeId;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public PedigreeStepEditDTO setStep(Step step) {
        this.step = step;
        return this;
    }
}
