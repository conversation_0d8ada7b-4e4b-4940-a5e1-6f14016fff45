package net.airuima.rbase.web.rest.base.process;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.dto.ExportParamDTO;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.process.WorkFlowConvertConfig;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.service.base.process.WorkFlowConvertConfigService;
import net.airuima.rbase.web.rest.base.process.dto.WorkFlowConvertConfigBatchDTO;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseUtil;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 转工艺路线配置
 *
 * <AUTHOR>
 * @date 2023/09/20
 */
@Tag(name = "转工艺路线配置Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/workflow-convert-configs")
@FuncInterceptor("ConversionProcess")
@AuthorityRegion("工艺模型")
public class WorkFlowConvertConfigResource extends ProtectBaseResource<WorkFlowConvertConfig> {

    private final Logger log = LoggerFactory.getLogger(WorkFlowConvertConfigResource.class);
    private static final String MODULE = "转工艺路线配置";

    private final WorkFlowConvertConfigService workFlowConvertConfigService;

    public WorkFlowConvertConfigResource(WorkFlowConvertConfigService workFlowConvertConfigService) {
        this.workFlowConvertConfigService = workFlowConvertConfigService;
        this.mapUri = "/api/workflow-convert-configs";
    }

    /**
     * 重写根据id获取工艺路线配置
     *
     * @param id 主键
     * @return org.springframework.http.ResponseEntity<net.airuima.rbase.domain.base.process.WorkFlowConvertConfig> 工艺路线配置
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')  or @sc.checkSecurity()")
    @GetMapping({"/{id}"})
    @Override
    public ResponseEntity<WorkFlowConvertConfig> get(@PathVariable Long id) {
        Optional<WorkFlowConvertConfig> entity = workFlowConvertConfigService.findById(id);
        return ResponseUtil.wrapOrNotFound(entity);
    }

    /**
     * 删除转工艺配置
     *
     * @param id 转工艺配置id
     * @return org.springframework.http.ResponseEntity 通用返回对象
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @DeleteMapping({"/{id}"})
    @Override
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        workFlowConvertConfigService.deleteEntity(id);
        return ResponseEntity.ok().headers(HeaderUtil.deletedAlert(StringUtils.uncapitalize(WorkFlowConvertConfig.class.getSimpleName()), "")).build();
    }

    /**
     * 重写批量删除
     *
     * @param deleteIds 删除的id集合
     * @return org.springframework.http.ResponseEntity 通用返回对象
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Override
    public ResponseEntity<Void> delMultiSel(@RequestBody List<Long> deleteIds) {
        Iterator var2 = deleteIds.iterator();
        while (var2.hasNext()) {
            long currId = (Long) var2.next();
            workFlowConvertConfigService.deleteEntity(currId);
        }
        return ((ResponseEntity.BodyBuilder) ResponseEntity.ok().headers(HeaderUtil.deletedAlert(this.entityName, JSON.toJSONString(deleteIds)))).build();
    }

    /**
     * 重写新增函数
     *
     * @param entity 转工艺路线配置
     * @return org.springframework.http.ResponseEntity<net.airuima.rbase.domain.base.process.WorkFlowConvertConfig> 转工艺路线配置
     */
    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    public ResponseEntity<WorkFlowConvertConfig> create(@Valid @RequestBody WorkFlowConvertConfig entity) throws URISyntaxException {
        BaseResultDTO<WorkFlowConvertConfig> baseResultDto = workFlowConvertConfigService.saveInstance(entity);
        if (Constants.KO.equals(baseResultDto.getStatus())) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(baseResultDto.getKey(), baseResultDto.getMessage())).build();
        } else {
            return (ResponseEntity.created(new URI(this.mapUri + "/" + baseResultDto.getData().getId())).headers(HeaderUtil.createdAlert(this.entityName, baseResultDto.getData().getId().toString()))).body(baseResultDto.getData());
        }
    }

    /**
     * 批量新增转工艺路线配置
     *
     * @param entity 批量转工艺路线配置参数
     * @return org.springframework.http.ResponseEntity<java.util.List < net.airuima.rbase.domain.base.process.WorkFlowConvertConfig>> 转工艺路线配置集合
     */
    @PostMapping("/batch/create")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    public ResponseEntity<ResponseData<List<WorkFlowConvertConfig>>> batchCreate(@RequestBody WorkFlowConvertConfigBatchDTO entity) {
        BaseResultDTO<List<WorkFlowConvertConfig>> baseResultDto = workFlowConvertConfigService.batchCreate(entity);
        if (Constants.KO.equals(baseResultDto.getStatus())) {
            return ResponseData.error(baseResultDto.getKey(), baseResultDto.getMessage());
        } else {
            return ResponseData.ok(baseResultDto.getData());
        }
    }

    /**
     * 重写更新函数，
     *
     * @param entity 转工艺路线配置
     * @return org.springframework.http.ResponseEntity<net.airuima.rbase.domain.base.process.WorkFlowConvertConfig> 转工艺路线配置
     */
    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    public ResponseEntity<WorkFlowConvertConfig> update(@Valid @RequestBody WorkFlowConvertConfig entity) throws URISyntaxException {
        BaseResultDTO<WorkFlowConvertConfig> baseResultDto = workFlowConvertConfigService.saveInstance(entity);
        if (Constants.KO.equals(baseResultDto.getStatus())) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(baseResultDto.getKey(), baseResultDto.getMessage())).build();
        } else {
            return (ResponseEntity.ok().headers(HeaderUtil.updatedAlert(this.entityName, entity.getId().toString()))).body(baseResultDto.getData());
        }
    }

    /**
     * 转工艺路线配置导入
     *
     * @param file 转工艺路线配置导入
     * @return org.springframework.http.ResponseEntity<java.lang.Void> 通用返回对象
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_IMPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "转工艺路线配置导入")
    @Override
    public ResponseEntity<Void> importTableExcel(@RequestParam("file") MultipartFile file, @RequestParam("data") String data, @RequestParam(value = "suffix", required = false) String suffix, @RequestParam(value = "metaColumn", required = false) String metaColumn, HttpServletResponse response) throws Exception {
        this.prepareImportParams();
        List<ExportParamDTO> exportParamDTOList = JSON.parseArray(data, ExportParamDTO.class);
        List<Map<String, Object>> illegalDataList = workFlowConvertConfigService.importWorkFlowConvertConfigExcel(file);
        // 获取excel文件信息
        List<Map<String, Object>> rowList = ExcelImportUtil.importExcel(file.getInputStream(), Map.class, this.importParams);
        // 返回不合法的数据
        if (!illegalDataList.isEmpty()) {
            int failedSize = illegalDataList.size();
            List<ExcelExportEntity> excelExportEntityList = exportParamDTOList.stream().map(s -> StringUtils.substringBefore(s.getLabel(), "[[")).map(label -> new ExcelExportEntity(label, label)).collect(Collectors.toList());
            excelExportEntityList.add(new ExcelExportEntity("错误信息", "错误信息"));
            String originalFilename = file.getOriginalFilename();
            if (null == originalFilename || originalFilename.isEmpty()) {
                return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(String.class.getSimpleName()), "fileNameEmpty", "文件名为空")).build();
            }
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "", originalFilename.contains("xlsx") ? ExcelType.XSSF : ExcelType.HSSF), excelExportEntityList, illegalDataList);
            response.setContentType(originalFilename.contains("xlsx") ? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" : "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(originalFilename, "utf-8"));
            response.setStatus(HttpStatus.BAD_REQUEST.value());
            response.setHeader("X-app-alert", "app.import.failure");
            String errorMessage = "上传数据" + rowList.size() + "条,导入成功" + (rowList.size() - failedSize) + "条,导入失败" + failedSize + "条,请检查下载的文件,检查失败的详细原因";
            response.setHeader(HeaderUtil.APP_PARAMS, URLEncoder.encode(errorMessage, "UTF-8"));
            response.setHeader(HeaderUtil.APP_ERROR_MESSAGE, URLEncoder.encode(errorMessage, "UTF-8"));
            workbook.write(response.getOutputStream());
            return ResponseEntity.badRequest().headers(HeaderUtil.failureAlert("import")).build();
        } else {
            return ResponseEntity.ok().headers(HeaderUtil.succeedAlert("import")).build();
        }
    }

    /**
     * 查询已配置的转工艺路线
     *
     * @param pedigreeId 产品谱系id
     * @param workFlowId 工艺路线id
     * @param clientId   客户id
     * @param stepId     工序id
     * @param keyword    模糊查询字符串
     * @return org.springframework.http.ResponseEntity<java.util.List < net.airuima.rbase.domain.base.process.WorkFlow> 转工艺路线集合
     */
    @GetMapping("/workFlow")
    public ResponseEntity<ResponseData<List<WorkFlow>>> findWorkFlow(@RequestParam(value = "pedigreeId") Long pedigreeId,
                                                      @RequestParam(value = "workFlowId") Long workFlowId, @RequestParam(value = "clientId", required = false) Long clientId,
                                                      @RequestParam(value = "stepId") Long stepId, @RequestParam(value = "keyword", required = false) String keyword) {
        return ResponseData.ok( workFlowConvertConfigService.findWorkFlow(pedigreeId, workFlowId, clientId, stepId, keyword));
    }


    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

}



