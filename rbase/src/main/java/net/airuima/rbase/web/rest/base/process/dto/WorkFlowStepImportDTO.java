package net.airuima.rbase.web.rest.base.process.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import net.airuima.rbase.util.ValidateUtils;

/**
 * 存放工艺路线excel对象
 * <p>
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/08/09
 */
public class WorkFlowStepImportDTO {

    /**
     * 工艺路线编码
     */
    @Excel(name = "工艺路线编码")
    private String workFlowCode;

    /**
     * 工艺路线名称
     */
    @Excel(name = "工艺路线名称")
    private String workFlowName;

    /**
     * 当前工序名称
     */
    @Excel(name = "工序名称")
    private String stepName;

    /**
     * 当前工序编码
     */
    @Excel(name = "工序编码")
    private String stepCode;

    /**
     * 后置工序编码
     */
    private String afterStepCode;

    /**
     * 前置工序编码
     */
    private String preStepCode;

    public String getStepName() {
        return stepName;
    }

    public WorkFlowStepImportDTO setStepName(String stepName) {
        this.stepName = stepName;
        return this;
    }

    public String getStepCode() {
        return stepCode;
    }

    public WorkFlowStepImportDTO setStepCode(String stepCode) {
        this.stepCode = stepCode;
        return this;
    }

    public String getAfterStepCode() {
        return afterStepCode;
    }

    public WorkFlowStepImportDTO setAfterStepCode(String afterStepCode) {
        this.afterStepCode = afterStepCode;
        return this;
    }

    public String getPreStepCode() {
        return preStepCode;
    }

    public WorkFlowStepImportDTO setPreStepCode(String preStepCode) {
        this.preStepCode = preStepCode;
        return this;
    }

    public String getWorkFlowCode() {
        return workFlowCode;
    }

    public WorkFlowStepImportDTO setWorkFlowCode(String workFlowCode) {
        this.workFlowCode = workFlowCode;
        return this;
    }

    public String getWorkFlowName() {
        return workFlowName;
    }

    public WorkFlowStepImportDTO setWorkFlowName(String workFlowName) {
        this.workFlowName = workFlowName;
        return this;
    }

    public static Boolean isExist(WorkFlowStepImportDTO workFlowStepImportDto){
        if (!ValidateUtils.isValid(workFlowStepImportDto.getWorkFlowCode()) || !ValidateUtils.isValid(workFlowStepImportDto.getWorkFlowName())
        ||!ValidateUtils.isValid(workFlowStepImportDto.getStepCode()) ){
            return false;
        }else {
            return true;
        }
    }

}
