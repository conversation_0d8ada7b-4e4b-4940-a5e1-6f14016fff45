package net.airuima.rbase.web.rest.procedure.aps;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.constant.Constants;
import net.airuima.rbase.domain.procedure.aps.SaleOrder;
import net.airuima.rbase.dto.aps.SaleOrderIssuedDTO;
import net.airuima.rbase.dto.aps.WorkSheetSimpleGetDTO;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.exception.BadRequestException;
import net.airuima.rbase.dto.sync.SyncResultDTO;
import net.airuima.rbase.dto.sync.SyncSaleOrderDTO;
import net.airuima.rbase.service.procedure.aps.SaleOrderService;
import net.airuima.rbase.service.procedure.aps.WorkSheetService;
import net.airuima.rbase.web.rest.procedure.aps.dto.SaleOrderProcessDTO;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.web.rest.errors.BadRequestAlertException;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 销售订单Resource
 *
 * <AUTHOR>
 * @date 2022-12-19
 */
@Tag(name = "销售订单Resource")
@RestController
@RequestMapping("/api/sale-orders")
@AuthorityRegion("生产工单")
@FuncInterceptor("SaleOrder")
public class SaleOrderResource extends ProtectBaseResource<SaleOrder> {
    private static final String EXCEPTION = "exception";
    private final SaleOrderService saleOrderService;
    private final WorkSheetService workSheetService;

    public SaleOrderResource(SaleOrderService saleOrderService, WorkSheetService workSheetService) {
        this.saleOrderService = saleOrderService;
        this.workSheetService = workSheetService;
        this.mapUri = "/api/sale-orders";
    }

    /**
     * 订单下发
     *
     * @date 2022-12-21
     * <AUTHOR>
     * @param saleOrderIssuedDTO 订单下发
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "订单下发")
    @PreventRepeatSubmit
    @PostMapping("/orderIssued")
    public ResponseEntity<ResponseData<Void>> orderIssued(@RequestBody SaleOrderIssuedDTO saleOrderIssuedDTO){
        try {
            BaseDTO responseBaseDto = saleOrderService.orderIssued(saleOrderIssuedDTO);
            if (Constants.KO.equals(responseBaseDto.getStatus())){
                return ResponseData.error("exception", responseBaseDto.getMessage());
            }
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            return ResponseData.error(e);
        }
        return ResponseData.save();
    }

    /**
     * 根据订单号、谱系ID模糊查询
     *
     * @param text 根据订单号、谱系ID模糊查询
     * @param size 返回数据个数
     * @param pedigreeId 谱系ID
     * <AUTHOR>
     * @date 2022/12/29
     * @return List<SaleOrder>
     */
    @Parameters({
            @Parameter(name = "text", description = "订单号", required = true),
            @Parameter(name = "pedigreeId", description = "谱系ID", required = true),
            @Parameter(name = "size", description = "数量", required = true)
    })
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "根据订单号、谱系ID模糊查询")
    @GetMapping("/bySerialNumberAndPedigreeId")
    public ResponseEntity<ResponseData<List<SaleOrder>>> bySerialNumberAndSpecification(@RequestParam(value = "text") String text,@RequestParam(value = "pedigreeId") Long pedigreeId,@RequestParam(value = "size") int size) {
        return ResponseData.ok(saleOrderService.bySerialNumberAndPedigreeId(text, pedigreeId, size));
    }

    /**
     * 新增销售订单
     *
     * @param entity 销售订单
     * @return ResponseEntity<ResponseData<SaleOrder>>
     * <AUTHOR>
     * @date 2023-01-09
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping()
    @PreventRepeatSubmit
    @Operation(summary = "新增销售订单")
    @Override
    public ResponseEntity<SaleOrder> create(@Valid @RequestBody SaleOrder entity) throws URISyntaxException {
        try {
            SaleOrder result = saleOrderService.saveInstance(entity);
            return ResponseEntity.created(new URI(this.mapUri + "/" + result.getId())).headers(HeaderUtil.createdAlert(this.entityName, result.getId().toString())).body(result);
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        } catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, EXCEPTION, e.getMessage())).build();
        }
    }

    /**
     * 更新销售订单
     *
     * @param entity 销售订单
     * @return ResponseEntity<ResponseData<SaleOrder>>
     * <AUTHOR>
     * @date 2023-01-09
     **/
    @Operation(summary = "更新销售订单")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PutMapping
    @Override
    public ResponseEntity<SaleOrder> update(@Valid @RequestBody SaleOrder entity) throws URISyntaxException {
        try {
            return ResponseEntity.ok().headers(HeaderUtil.updatedAlert(this.entityName, entity.getId().toString())).body(saleOrderService.updateInstance(entity));
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        } catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, EXCEPTION, e.getMessage())).build();
        }
    }

    /**
     * 同步订单
     * @param syncSaleOrderList
     * <AUTHOR>
     * @date  2023/3/17
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "同步订单信息")
    @PostMapping("/syncSaleOrder")
    public ResponseEntity<ResponseData<List<SyncResultDTO>>> syncSaleOrder(@RequestBody List<SyncSaleOrderDTO> syncSaleOrderList){
        try {
            return ResponseData.ok(saleOrderService.syncSaleOrder(syncSaleOrderList));
        }catch (Exception e){
            e.printStackTrace();
            throw new BadRequestException(e.getMessage());
        }
    }

    /**
     * 通过销售订单主键ID获取已下发的工单列表
     * @param id        过销售订单主键ID
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData<java.util.List<net.airuima.dto.aps.WorkSheetSimpleGetDTO>>> 工单信息列表
     * <AUTHOR>
     * @date 2024/1/29
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过销售订单主键ID获取已下发的工单列表",parameters = {
            @Parameter(name = "id",description = "销售订单主键ID",schema = @Schema(type = "integer",format = "int64"),required = true,in = ParameterIn.PATH)
    })
    @GetMapping("/work-sheets/{id}")
    public ResponseEntity<ResponseData<SaleOrderProcessDTO>> worksheet(@PathVariable("id") Long id){
        return ResponseData.ok(workSheetService.findBySaleOrderId(id));
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority,"销售订单");
    }

}
