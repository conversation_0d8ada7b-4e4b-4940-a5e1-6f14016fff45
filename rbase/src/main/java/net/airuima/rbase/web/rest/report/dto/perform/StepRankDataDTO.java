package net.airuima.rbase.web.rest.report.dto.perform;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.procedure.report.StaffPerform;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工序报工排行数据DTO
 *
 * <AUTHOR>
 * @date 2023/06/28
 */
@Schema(description = "工序报工排行数据DTO")
public class StepRankDataDTO {

    /**
     * 工序名字
     */
    @Schema(description = "工序名字")
    private String name;

    /**
     * 工序编码
     */
    @Schema(description = "工序编码")
    private String code;

    /**
     * 报工数量
     */
    @Schema(description = "报工数量")
    private Long number;

    /**
     * 耗时(分钟)
     */
    @Schema(description = "耗时(分钟)")
    private Double workHour;


    public StepRankDataDTO() {
    }

    public StepRankDataDTO(StaffPerform staffPerform,String name, String code, Long number,Double workHour) {
        this.name = name;
        this.code = code;
        this.number = number;
        this.workHour = workHour;
    }

    public String getName() {
        return name;
    }

    public StepRankDataDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public StepRankDataDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public Long getNumber() {
        return number;
    }

    public StepRankDataDTO setNumber(Long number) {
        this.number = number;
        return this;
    }

    public Double getWorkHour() {
        return workHour;
    }

    public StepRankDataDTO setWorkHour(Double workHour) {
        this.workHour = workHour;
        return this;
    }
}
