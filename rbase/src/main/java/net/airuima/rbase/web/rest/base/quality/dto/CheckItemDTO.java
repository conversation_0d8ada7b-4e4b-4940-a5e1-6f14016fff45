package net.airuima.rbase.web.rest.base.quality.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.domain.base.AuditEntity;
import net.airuima.rbase.dto.document.DocumentRelationDTO;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 检测项目库DTO
 * <AUTHOR>
 * @date 2023-04-24
 */
@AuditEntity(value = "检测项目库DTO")
public class CheckItemDTO implements Serializable {

    /**
     * id
     */
    @Schema(description = "id，新增时不传入，修改时传入")
    private Long id;

    /**
     * 检测项目类型id
     */
    @Schema(description = "检测项目类型id")
    private Long varietyId;

    /**
     * 检测项编码
     */
    @Schema(description = "检测项编码")
    private String code;

    /**
     * 检测项名称
     */
    @Schema(description = "检测项名称")
    private String name;

    /**
     * 分析方法:定性0/定量1
     */
    @Schema(description = "分析方法:定性0/定量1")
    private int analyseWay;

    /**
     * 检验方法:目测0/检测仪器1
     */
    @Schema(description = "检验方法:目测0/检测仪器1")
    private int inspectWay;

    /**
     * 检测仪器
     */
    @Schema(description = "检测仪器")
    private String facility;

    /**
     * 计量单位id
     */
    @Schema(description = "计量单位id")
    private Long unitId;

    /**
     * 合格范围(开闭区间或者OK)
     */
    @Schema(description = "合格范围(开闭区间或者OK)")
    private String qualifiedRange;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 禁用启用(0:禁用;1:启用)
     */
    @Schema(description = "禁用启用(0:禁用;1:启用)")
    private Boolean isEnable;

    /**
     * 是否管控其检查结果(0:不管控；1：管控)
     */
    @Schema(description = "是否管控其检查结果(0:不管控；1：管控)")
    private Boolean control;

    /**
     * 操作指导书文档列表
     */
    @Schema(description = "操作指导书文档列表")
    private List<DocumentRelationDTO.Document> documentList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getVarietyId() {
        return varietyId;
    }

    public void setVarietyId(Long varietyId) {
        this.varietyId = varietyId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getAnalyseWay() {
        return analyseWay;
    }

    public void setAnalyseWay(int analyseWay) {
        this.analyseWay = analyseWay;
    }

    public int getInspectWay() {
        return inspectWay;
    }

    public void setInspectWay(int inspectWay) {
        this.inspectWay = inspectWay;
    }


    public String getFacility() {
        return facility;
    }

    public CheckItemDTO setFacility(String facility) {
        this.facility = facility;
        return this;
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public String getQualifiedRange() {
        return qualifiedRange;
    }

    public void setQualifiedRange(String qualifiedRange) {
        this.qualifiedRange = qualifiedRange;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
    }

    public Boolean getControl() {
        return control;
    }

    public CheckItemDTO setControl(Boolean control) {
        this.control = control;
        return this;
    }

    public List<DocumentRelationDTO.Document> getDocumentList() {
        return documentList;
    }

    public void setDocumentList(List<DocumentRelationDTO.Document> documentList) {
        this.documentList = documentList;
    }
}
