package net.airuima.rbase.web.rest.base.pedigree;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.constant.Constants;
import net.airuima.dto.ExportParamDTO;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepUnqualifiedItem;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.service.base.pedigree.IPedigreeStepUnqualifiedItemService;
import net.airuima.rbase.service.base.pedigree.PedigreeStepUnqualifiedItemService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工序不良现象Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "产品谱系工序不良项目Resource")
@RestController
@RequestMapping("/api/pedigree-step-unqualified-items")
@AuthorityRegion("质量不良")
public class PedigreeStepUnqualifiedItemResource extends ProtectBaseResource<PedigreeStepUnqualifiedItem> {

    private static final String MODULE = "工序不良项目";

    private static final String EXCEPTION = "exception";

    private final Logger log = LoggerFactory.getLogger(PedigreeStepUnqualifiedItemResource.class);
    private final PedigreeStepUnqualifiedItemService pedigreeStepUnqualifiedItemService;

    @Autowired
    private IPedigreeStepUnqualifiedItemService[] pedigreeStepUnqualifiedItemServices;

    private static final String ERROR = "error";

    public PedigreeStepUnqualifiedItemResource(PedigreeStepUnqualifiedItemService pedigreeStepUnqualifiedItemService) {
        this.pedigreeStepUnqualifiedItemService = pedigreeStepUnqualifiedItemService;
        this.mapUri = "/api/pedigree-step-unqualified-items";
    }

    /**
     * 通过产品谱系id工艺路线id工序id获得对应不良现象
     *
     * @param pedigreeId 产品谱系ID
     * @param workFlowId 工艺路线ID
     * @param stepId     工序ID
     * @param clientId   客户id
     * @return java.util.List<net.airuima.domain.base.quality.UnqualifiedItem> 工序不良项目
     * <AUTHOR>
     * @date 2020-12-30
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过产品谱系id与工序ID和客户id获得对应不良现象")
    @GetMapping("/unqualifiedItemByPedigreeIdAndStepIdAndClientId")
    public ResponseEntity<ResponseData<List<UnqualifiedItem>>> findUnqualifiedItemByPedigreeStep(@RequestParam("pedigreeId") Long pedigreeId, @RequestParam("workFlowId") Long workFlowId, @RequestParam("stepId") Long stepId,
                                                                                                 @RequestParam(value = "clientId", required = false) Long clientId) {
        return ResponseData.ok(pedigreeStepUnqualifiedItemService.findUnqualifiedItemByPedigreeIdAndStepId(pedigreeId, workFlowId, stepId, clientId));
    }


    /**
     * 新增工序不良项目配置
     *
     * @param pedigreeStepUnqualifiedItem 工序不良项目配置
     * @return org.springframework.http.ResponseEntity<net.airuima.domain.base.pedigree.PedigreeStepUnqualifiedItem>  工序不良项目配置
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "新增产品谱系工序不良现象")
    @PostMapping("/createCustom")
    public ResponseEntity<ResponseData<PedigreeStepUnqualifiedItem>> createCustom(@RequestBody PedigreeStepUnqualifiedItem pedigreeStepUnqualifiedItem) throws URISyntaxException {
        BaseResultDTO<PedigreeStepUnqualifiedItem> baseResultDto = pedigreeStepUnqualifiedItemService.saveInstance(pedigreeStepUnqualifiedItem);
        if (Constants.KO.equals(baseResultDto.getStatus())) {
            return ResponseData.error(baseResultDto.getKey(), baseResultDto.getMessage());
        } else {
            return ResponseData.ok(baseResultDto.getData());
        }
    }

    /**
     * 修改工序不良项目配置
     *
     * @param pedigreeStepUnqualifiedItem 工序不良项目配置
     * @return org.springframework.http.ResponseEntity<net.airuima.domain.base.pedigree.PedigreeStepUnqualifiedItem>  工序不良项目配置
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "修改产品谱系工序不良现象")
    @PutMapping("/updateCustom")
    public ResponseEntity<ResponseData<PedigreeStepUnqualifiedItem>> updateCustom(@RequestBody PedigreeStepUnqualifiedItem pedigreeStepUnqualifiedItem) {
        BaseResultDTO<PedigreeStepUnqualifiedItem> baseResultDto = pedigreeStepUnqualifiedItemService.saveInstance(pedigreeStepUnqualifiedItem);
        if (Constants.KO.equals(baseResultDto.getStatus())) {
            return ResponseData.error(baseResultDto.getKey(), baseResultDto.getMessage());
        } else {
            return ResponseData.ok(baseResultDto.getData());
        }
    }


    /**
     * 批量更新工序不良项目
     * @param pedigreeStepUnqualifiedItems 参数列表
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "批量更新工序不良项目")
    @PreventRepeatSubmit
    @PostMapping("/batch/custom")
    public ResponseEntity<ResponseData<Void>> batch(@RequestBody List<PedigreeStepUnqualifiedItem> pedigreeStepUnqualifiedItems){
        try {
            pedigreeStepUnqualifiedItemService.batchUpdate(pedigreeStepUnqualifiedItems);
            return ResponseData.save();
        }  catch (ResponseException e) {
            return ResponseData.error(e);
        }
    }

    /**
     * 启用/禁用指定产品谱系工序不良现象
     *
     * @param pedigreeStepUnqualifiedItemId
     * @return org.springframework.http.ResponseEntity<java.lang.Void> 通用返回
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "启用/禁用指定产品谱系工序不良现象")
    @Parameters({
            @Parameter(name = "pedigreeStepUnqualifiedItemId", description = "产品谱系工序不良现象ID", required = true)
    })
    @PutMapping("/pedigreeStepUnqualifiedItemId/{pedigreeStepUnqualifiedItemId}")
    public ResponseEntity<ResponseData<Void>> enableByStepId(@PathVariable("pedigreeStepUnqualifiedItemId") Long pedigreeStepUnqualifiedItemId) {
        BaseResultDTO<PedigreeStepUnqualifiedItem> baseResultDto = pedigreeStepUnqualifiedItemService.enableByPedigreeStepUnqualifiedItemId(pedigreeStepUnqualifiedItemId);
        if (Constants.KO.equals(baseResultDto.getStatus())) {
            return ResponseData.error(baseResultDto.getKey(), baseResultDto.getMessage());
        } else {
            return ResponseData.save();
        }
    }


    /**
     * 通过工序id获得对应不良项目(适用于所有的产品谱系和工艺路线的工序不良项目)
     *
     * @param stepId   工序id
     * @return org.springframework.http.ResponseEntity<java.util.List < net.airuima.domain.base.quality.UnqualifiedItem> 不良项目集合
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "通过工序id获得对应不良项目(适用于所有的产品谱系和工艺路线的工序不良项目)")
    @GetMapping("/unqualifiedItemByStep")
    public ResponseEntity<ResponseData<List<UnqualifiedItem>>> findPedigreeStepUnqualifiedItem(@RequestParam(value = "stepId") Long stepId) {
        List<UnqualifiedItem> unqualifiedItemList = pedigreeStepUnqualifiedItemService.findByStepId(stepId);
        return ResponseData.ok(unqualifiedItemList);
    }



    /**
     * 根据产品谱系ID,工艺路线Id、客户ID、工序ID获取工序不良信息
     * @param pedigreeId 产品谱系主键id
     * @param clientId 客户ID
     * @param workFlowId 工艺路线主键id
     * @param stepId 工序ID
     * @return List<PedigreeStepUnqualifiedItem>
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "根据产品谱系ID,工艺路线Id、客户ID、工序ID获取工序不良信息")
    @GetMapping("/byConditions")
    public ResponseEntity<ResponseData<List<PedigreeStepUnqualifiedItem>>> byCondition(@RequestParam(value = "pedigreeId",required = false) Long pedigreeId,
                                                                                       @RequestParam(value = "workFlowId",required = false) Long workFlowId,
                                                                                       @RequestParam(value = "clientId",required = false) Long clientId,
                                                                                       @RequestParam(value = "stepId",required = false) Long stepId){
        return ResponseData.ok(pedigreeStepUnqualifiedItemService.findAllByPedigreeIdAndClientIdAndWorkFlowIdAndStepId(pedigreeId, clientId,workFlowId, stepId));
    }

    /**
     * 工序不良配置导入
     *
     * @param file 工序不良配置导入
     * @return org.springframework.http.ResponseEntity<java.lang.Void>通用返回对象
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_IMPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "工序不良配置导入")
    @Override
    public ResponseEntity<Void> importTableExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "data", required = false) String data, @RequestParam(value = "suffix", required = false) String suffix, @RequestParam(value = "metaColumn", required = false) String metaColumn, HttpServletResponse response) throws Exception {
        this.prepareImportParams();
        List<ExportParamDTO> exportParamDTOList = JSON.parseArray(data, ExportParamDTO.class);
        List<Map<String, Object>> illegalDataList = pedigreeStepUnqualifiedItemServices[0].importPedigreeStepUnqualifiedItemExcel(file);
        //获取excel文件信息
        List<Map<String, Object>> rowList = ExcelImportUtil.importExcel(file.getInputStream(), Map.class, this.importParams);
        // 返回不合法的数据
        if (!illegalDataList.isEmpty()) {
            int failedSize = illegalDataList.size();
            List<ExcelExportEntity> excelExportEntityList = exportParamDTOList.stream().map(s -> org.apache.commons.lang3.StringUtils.substringBefore(s.getLabel(), "[[")).map(label -> new ExcelExportEntity(label, label)).collect(Collectors.toList());
            excelExportEntityList.add(new ExcelExportEntity("错误信息", "错误信息"));
            String originalFilename = file.getOriginalFilename();
            if (null == originalFilename || originalFilename.isEmpty()) {
                return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(org.apache.commons.lang3.StringUtils.uncapitalize(String.class.getSimpleName()), "fileNameEmpty", "文件名为空")).build();
            }
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "", originalFilename.contains("xlsx") ? ExcelType.XSSF : ExcelType.HSSF), excelExportEntityList, illegalDataList);
            response.setContentType(originalFilename.contains("xlsx") ? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" : "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(originalFilename, "utf-8"));
            response.setStatus(HttpStatus.BAD_REQUEST.value());
            response.setHeader("X-app-alert", "app.import.failure");
            String errorMessage = "上传数据" + rowList.size() + "条,导入成功" + (rowList.size() - failedSize) + "条,导入失败" + failedSize + "条,请检查下载的文件,检查失败的详细原因";
            response.setHeader(HeaderUtil.APP_PARAMS, URLEncoder.encode(errorMessage, "UTF-8"));
            response.setHeader(HeaderUtil.APP_ERROR_MESSAGE, URLEncoder.encode(errorMessage, "UTF-8"));
            workbook.write(response.getOutputStream());
            return ResponseEntity.badRequest().headers(HeaderUtil.failureAlert("import")).build();
        } else {
            return ResponseEntity.ok().headers(HeaderUtil.succeedAlert("import")).build();
        }
    }


    /**
     * 删除工序不良配置
     *
     * @param id 工序不良配置id
     * @return org.springframework.http.ResponseEntity 通用返回对象
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @DeleteMapping({"/{id}"})
    @Override
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        pedigreeStepUnqualifiedItemService.deleteEntity(id);
        return ResponseEntity.ok().headers(HeaderUtil.deletedAlert(StringUtils.uncapitalize(PedigreeStepUnqualifiedItem.class.getSimpleName()), "")).build();
    }


    /**
     * 详情重写
     *
     * @param id 工序不良配置ID
     * @return : org.springframework.http.ResponseEntity<net.airuima.domain.base.pedigree.PedigreeStepUnqualifiedItem> 工序不良配置
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @GetMapping({"/{id}"})
    @Override
    public ResponseEntity<PedigreeStepUnqualifiedItem> get(@PathVariable Long id) {
        PedigreeStepUnqualifiedItem pedigreeStepUnqualifiedItem = pedigreeStepUnqualifiedItemService.get(id);
        return ResponseEntity.ok().body(pedigreeStepUnqualifiedItem);
    }

    /**
     * 重写批量删除
     *
     * @param deleteIds 删除的id集合
     * @return org.springframework.http.ResponseEntity 通用返回对象
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Override
    public ResponseEntity<Void> delMultiSel(@RequestBody List<Long> deleteIds) {
        Iterator var2 = deleteIds.iterator();
        while (var2.hasNext()) {
            long currId = (Long) var2.next();
            pedigreeStepUnqualifiedItemService.deleteEntity(currId);
        }
        return ((ResponseEntity.BodyBuilder) ResponseEntity.ok().headers(HeaderUtil.deletedAlert(this.entityName, JSON.toJSONString(deleteIds)))).build();
    }


    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

}
