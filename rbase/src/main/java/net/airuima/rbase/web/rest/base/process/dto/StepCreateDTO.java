package net.airuima.rbase.web.rest.base.process.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-05-07
 */
@Schema(description = "新增工序DTO")
public class StepCreateDTO implements Serializable {
    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 编码
     */
    @Schema(description = "编码")
    private String code;

    /**
     * 工序类型
     */
    @Schema(description = "工序类型")
    private Integer category;

    /**
     * 工序组别
     */
    @Schema(description = "工序组别DTO")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long stepGroupId;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public Long getStepGroupId() {
        return stepGroupId;
    }

    public void setStepGroupId(Long stepGroupId) {
        this.stepGroupId = stepGroupId;
    }
}
