package net.airuima.rbase.web.rest.base.pedigree.dto;

import jakarta.validation.constraints.NotNull;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021/2/8
 */
public class PedigreeStepCopyDTO {

    @NotNull
    private Long sourcePedigreeId;

    @NotNull
    private Long targetPedigreeId;

    @NotNull
    private Long workFlowId;

    @NotNull
    private Long stepId;

    public Long getSourcePedigreeId() {
        return sourcePedigreeId;
    }

    public PedigreeStepCopyDTO setSourcePedigreeId(Long sourcePedigreeId) {
        this.sourcePedigreeId = sourcePedigreeId;
        return this;
    }

    public Long getTargetPedigreeId() {
        return targetPedigreeId;
    }

    public PedigreeStepCopyDTO setTargetPedigreeId(Long targetPedigreeId) {
        this.targetPedigreeId = targetPedigreeId;
        return this;
    }

    public Long getWorkFlowId() {
        return workFlowId;
    }

    public PedigreeStepCopyDTO setWorkFlowId(Long workFlowId) {
        this.workFlowId = workFlowId;
        return this;
    }

    public Long getStepId() {
        return stepId;
    }

    public PedigreeStepCopyDTO setStepId(Long stepId) {
        this.stepId = stepId;
        return this;
    }
}
