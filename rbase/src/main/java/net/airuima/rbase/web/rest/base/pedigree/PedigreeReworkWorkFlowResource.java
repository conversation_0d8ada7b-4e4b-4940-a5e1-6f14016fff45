package net.airuima.rbase.web.rest.base.pedigree;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.dto.ExportParamDTO;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.PedigreeReworkWorkFlow;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.dto.base.BaseResultDTO;
import net.airuima.rbase.service.base.pedigree.PedigreeReworkWorkFlowService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品型号不良种类流程框图Resource
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Tag(name = "产品谱系返工工艺路线Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/pedigree-rework-work-flows")
@AuthorityRegion("工艺模型")
public class PedigreeReworkWorkFlowResource extends ProtectBaseResource<PedigreeReworkWorkFlow> {

    private static final String MODULE = "产品谱系返工工艺路线";
    private final Logger log = LoggerFactory.getLogger(PedigreeReworkWorkFlowResource.class);
    private final PedigreeReworkWorkFlowService pedigreeReworkWorkFlowService;

    public PedigreeReworkWorkFlowResource(PedigreeReworkWorkFlowService pedigreeReworkWorkFlowService) {
        this.pedigreeReworkWorkFlowService = pedigreeReworkWorkFlowService;
        this.mapUri = "/api/pedigree-rework-work-flows";
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

    @Operation(summary = "新增产品谱系返修工艺路线信息")
    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    public ResponseEntity<PedigreeReworkWorkFlow> create(@Parameter(schema = @Schema(implementation = PedigreeReworkWorkFlow.class),required = true,description = "产品谱系返修工艺路线信息") @Valid @RequestBody PedigreeReworkWorkFlow entity) throws URISyntaxException {
        BaseResultDTO<PedigreeReworkWorkFlow> baseResultDto = pedigreeReworkWorkFlowService.saveInstance(entity);
        if (Constants.KO.equals(baseResultDto.getStatus())) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(baseResultDto.getKey(), baseResultDto.getMessage())).build();
        } else {
            return (ResponseEntity.created(new URI(this.mapUri + "/" + baseResultDto.getData().getId())).headers(HeaderUtil.createdAlert(this.entityName, baseResultDto.getData().getId().toString()))).body(baseResultDto.getData());
        }
    }

    /**
     * 重写更新函数，
     *
     * @param entity 产品谱系不良组别返修工艺路线
     * @return org.springframework.http.ResponseEntity<net.airuima.rbase.domain.base.pedigree.PedigreeReworkWorkFlow> 产品谱系不良组别返修工艺路线
     */
    @Operation(summary = "更新产品谱系返修工艺路线信息")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Override
    public ResponseEntity<PedigreeReworkWorkFlow> update(@Parameter(schema = @Schema(implementation = PedigreeReworkWorkFlow.class),required = true,description = "产品谱系返修工艺路线信息")  @Valid @RequestBody PedigreeReworkWorkFlow entity) throws URISyntaxException {
        BaseResultDTO<PedigreeReworkWorkFlow> baseResultDto = pedigreeReworkWorkFlowService.saveInstance(entity);
        if (Constants.KO.equals(baseResultDto.getStatus())) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(baseResultDto.getKey(), baseResultDto.getMessage())).build();
        } else {
            return ResponseEntity.ok().headers(HeaderUtil.updatedAlert(this.entityName, entity.getId().toString())).body(baseResultDto.getData());
        }
    }


    /**
     * 通过产品谱系ID、工艺路线ID及不良组别ID 、客户ID删除关联关系
     *
     * @param pedigreeId         谱系ID
     * @param workFlowId         工艺路线ID
     * @param unqualifiedGroupId 不良组别ID
     * @param clientId 客户id
     * @return ResponseEntity<ResponseData<Void>>
     * @date 2021-05-10
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过产品谱系ID、工艺路线ID及不良组别ID、客户ID删除关联关系", parameters = {
            @Parameter(name = "pedigreeId",required = true, description = "产品谱系主键ID",schema = @Schema(type = "int64") ,in = ParameterIn.QUERY),
            @Parameter(name = "unqualifiedGroupId",required = true, description = "工艺路线主键ID",schema = @Schema(type = "int64") ,in = ParameterIn.QUERY),
            @Parameter(name = "workFlowId",required = true, description = "工艺路线主键ID",schema = @Schema(type = "int64") ,in = ParameterIn.QUERY),
            @Parameter(name = "clientId",description = "客户主键ID",schema = @Schema(type = "int64") ,in = ParameterIn.QUERY)
    })
    @DeleteMapping("/deleteByPedigreeIdAndWorkFlowIdAndUnqualifiedGroupId")
    public ResponseEntity<ResponseData<Void>> deleteByPedigreeIdAndWorkFlowId(@RequestParam("pedigreeId") Long pedigreeId,
                                                                @RequestParam("unqualifiedGroupId")Long unqualifiedGroupId,
                                                                @RequestParam("workFlowId") Long workFlowId,
                                                                @RequestParam(value = "clientId", required = false) Long clientId){
        try{
            pedigreeReworkWorkFlowService.deleteByPedigreeIdAndUnqualifiedGroupIdAndWorkFlowIdAndClientId(pedigreeId,unqualifiedGroupId,workFlowId,clientId);
            return ResponseData.delete();
        }catch (Exception e){
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 启用/禁用指定产品谱系不良种类工艺路线
     *
     * @param pedigreeReworkWorkFlowId
     * @return : org.springframework.http.ResponseEntity<java.lang.Void>
     * <AUTHOR>
     * @date 2022/12/13
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "启用/禁用指定产品谱系不良种类工艺路线", parameters = {
            @Parameter(name = "pedigreeReworkWorkFlowId", description = "产品谱系不良种类工艺路线主键ID", required = true, schema = @Schema(type = "int64") ,in = ParameterIn.PATH)
    })
    @PutMapping("/pedigreeReworkWorkFlowId/{pedigreeReworkWorkFlowId}")
    public ResponseEntity<ResponseData<Void>> enableByStepId(@PathVariable("pedigreeReworkWorkFlowId") Long pedigreeReworkWorkFlowId) {
        try {
            pedigreeReworkWorkFlowService.enableByPedigreeReworkWorkFlowId(pedigreeReworkWorkFlowId);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            log.info(e.getMessage(), e);
            return ResponseData.error(e);
        }
    }

    /**
     * 根据产品谱系主键ID和客户主键ID获取产品谱系返修工艺路线
     *
     * @param pedigreeId 谱系ID
     * @param clientId   客户id
     * @return java.util.List<net.airuima.rbase.domain.base.process.WorkFlow> 工艺路线集合
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "根据产品谱系主键ID和客户主键ID获取产品谱系返修工艺路线", parameters = {
            @Parameter(name = "pedigreeId",required = true, description = "产品谱系主键ID",schema = @Schema(type = "int64") ,in = ParameterIn.QUERY),
            @Parameter(name = "clientId",required = true, description = "客户主键ID",schema = @Schema(type = "int64") ,in = ParameterIn.QUERY)
    })
    @GetMapping("/byPedigreeIdAndClientId")
    public ResponseEntity<ResponseData<List<WorkFlow>>> findByPedigreeIdAndClientId(@RequestParam("pedigreeId") Long pedigreeId, @RequestParam(value = "clientId", required = false) Long clientId) {
        return ResponseData.ok(pedigreeReworkWorkFlowService.findByPedigreeIdAndClientId(pedigreeId, clientId));
    }

    /**
     * 产品谱系返修工艺路线导入
     *
     * @param file 产品谱系返修工艺路线导入
     * @return org.springframework.http.ResponseEntity<java.lang.Void>通用返回对象
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_IMPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "产品谱系返修工艺路线导入" ,parameters = {
            @Parameter(name = "file",description = "导入的产品谱系返修工艺路线Excel",schema = @Schema(type = "String",format = "binary"),in = ParameterIn.QUERY),
            @Parameter(name = "data",description = "字段数据", schema = @Schema(type = "string"),in = ParameterIn.QUERY),
            @Parameter(name = "suffix",description = "后缀", schema = @Schema(type = "string"),in = ParameterIn.QUERY),
            @Parameter(name = "metaColumn",description = "元数据列",schema = @Schema(type = "string"),in = ParameterIn.QUERY),
    })
    @Override
    public ResponseEntity<Void> importTableExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "data", required = false) String data, @RequestParam(value = "suffix", required = false) String suffix,
                                                 @RequestParam(value = "metaColumn", required = false) String metaColumn, HttpServletResponse response) throws Exception {
        this.prepareImportParams();
        List<ExportParamDTO> exportParamDTOList = JSON.parseArray(data, ExportParamDTO.class);
        List<Map<String, Object>> illegalDataList = pedigreeReworkWorkFlowService.importPedigreeReworkWorkFlowExcel(file);
        //获取excel文件信息
        List<Map<String, Object>> rowList = ExcelImportUtil.importExcel(file.getInputStream(), Map.class, this.importParams);
        // 返回不合法的数据
        if (!illegalDataList.isEmpty()) {
            int failedSize = illegalDataList.size();
            List<ExcelExportEntity> excelExportEntityList = exportParamDTOList.stream().map(s -> org.apache.commons.lang3.StringUtils.substringBefore(s.getLabel(), "[[")).map(label -> new ExcelExportEntity(label, label)).collect(Collectors.toList());
            excelExportEntityList.add(new ExcelExportEntity("错误信息", "错误信息"));
            String originalFilename = file.getOriginalFilename();
            if (null == originalFilename || originalFilename.isEmpty()) {
                return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(org.apache.commons.lang3.StringUtils.uncapitalize(String.class.getSimpleName()), "fileNameEmpty", "文件名为空")).build();
            }
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "", originalFilename.contains("xlsx") ? ExcelType.XSSF : ExcelType.HSSF), excelExportEntityList, illegalDataList);
            response.setContentType(originalFilename.contains("xlsx") ? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" : "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(originalFilename, "utf-8"));
            response.setStatus(HttpStatus.BAD_REQUEST.value());
            response.setHeader("X-app-alert", "app.import.failure");
            String errorMessage = "上传数据" + rowList.size() + "条,导入成功" + (rowList.size() - failedSize) + "条,导入失败" + failedSize + "条,请检查下载的文件,检查失败的详细原因";
            response.setHeader(HeaderUtil.APP_PARAMS, URLEncoder.encode(errorMessage, "UTF-8"));
            response.setHeader(HeaderUtil.APP_ERROR_MESSAGE, URLEncoder.encode(errorMessage, "UTF-8"));
            workbook.write(response.getOutputStream());
            return ResponseEntity.badRequest().headers(HeaderUtil.failureAlert("import")).build();
        } else {
            return ResponseEntity.ok().headers(HeaderUtil.succeedAlert("import")).build();
        }
    }

}
