package net.airuima.rbase.web.rest.procedure.material;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.rbase.domain.procedure.material.WsMaterialBatch;
import net.airuima.rbase.dto.sync.SyncResultDTO;
import net.airuima.rbase.dto.sync.SyncWorkSheetMaterialBatchDTO;
import net.airuima.rbase.service.procedure.material.WsMaterialBatchService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单领料明细表Resource
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Tag(name = "工单领料明细表Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/ws-material-batches")
@AuthorityRegion("工单物料管控")
@FuncInterceptor("WorksheetMaterial && WsMaterialBatch && WsMaterialBatchNumber")
@AuthSkip("ICD")
public class WsMaterialBatchResource extends ProtectBaseResource<WsMaterialBatch> {
    private static final String MODULE = "工单物料库存";
    private final WsMaterialBatchService wsMaterialBatchService;

    public WsMaterialBatchResource(WsMaterialBatchService wsMaterialBatchService) {
        this.wsMaterialBatchService = wsMaterialBatchService;
        this.mapUri = "/api/ws-material-batches";
    }

    /**
     * 同步工单领料批次数据 （目前仅sap使用）
     * <AUTHOR>
     * @param syncWorkSheetMaterialBatchDtoList     上传的工单物料批次信息
     * @return List<SapBaseDTO>
     * @date 2021-06-04
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "同步工单领料批次数据")
    @PostMapping("/workSheetMaterialBatchSync")
    public ResponseEntity<ResponseData<List<SyncResultDTO>>> workSheetMaterialBatchSync(@RequestBody List<SyncWorkSheetMaterialBatchDTO> syncWorkSheetMaterialBatchDtoList){
        try{
            return ResponseData.ok(wsMaterialBatchService.workSheetMaterialBatchSync(syncWorkSheetMaterialBatchDtoList));
        }catch (Exception e){
            e.printStackTrace();
        }
        return ResponseData.ok(Collections.emptyList());
    }

    /**
     * 通过工单id 获取对应的工单领料记录
     * @param wsId 工单id
     * <AUTHOR>
     * @date  2022/3/25
     * @return List<WsMaterialBatch>
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过工单id 获取对应的工单领料记录")
    @GetMapping("/wsId")
    public ResponseEntity<ResponseData<List<WsMaterialBatch>>> findByWsMaterialBatch(@RequestParam(value = "wsId",required = true) Long wsId, @RequestParam(value = "materialId",required = false) Long materialId){
        return ResponseData.ok(wsMaterialBatchService.findByWsMaterialBatch(wsId,materialId));
    }

    /**
     * 通过工单id、物料id获取对应的工单领料记录
     * @param wsId 工单id
     * @param materialId 物料id
     * @return java.util.List<net.airuima.rbase.domain.procedure.material.WsMaterialBatch>
     * <AUTHOR>
     * @date 2023/3/25
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过工单id 、物料id获取对应的工单领料批次记录")
    @GetMapping("/wsId/{wsId}/materialId/{materialId}")
    public ResponseEntity<ResponseData<List<WsMaterialBatch>>> findByWorkSheetIdAndMaterialIdAndDeleted(@PathVariable("wsId") Long wsId, @PathVariable("materialId") Long materialId){
        return ResponseData.ok(wsMaterialBatchService.findByWorkSheetIdAndMaterialIdAndDeleted(wsId, materialId));
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

}
