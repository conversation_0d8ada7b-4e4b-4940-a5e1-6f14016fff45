package net.airuima.rbase.web.rest.base.pedigree.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.quality.UnqualifiedGroup;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-05-10
 */
@Schema(description = "工艺路线查询获取的产品谱系不良组别列表")
public class PedigreeUnqualifiedGroupDTO {


    private Pedigree pedigree;

    public UnqualifiedGroup unqualifiedGroup;

    public Pedigree getPedigree() {
        return pedigree;
    }

    public PedigreeUnqualifiedGroupDTO setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public UnqualifiedGroup getUnqualifiedGroup() {
        return unqualifiedGroup;
    }

    public PedigreeUnqualifiedGroupDTO setUnqualifiedGroup(UnqualifiedGroup unqualifiedGroup) {
        this.unqualifiedGroup = unqualifiedGroup;
        return this;
    }
}
