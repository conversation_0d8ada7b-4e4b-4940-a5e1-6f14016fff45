package net.airuima.rbase.web.rest.base.priority;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.config.annotation.HideAuthority;
import net.airuima.constant.Constants;
import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.rbase.service.base.priority.PriorityElementConfigService;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.web.rest.errors.BadRequestAlertException;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.net.URISyntaxException;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 条件优先级配置Resource
 *
 * <AUTHOR>
 * @date 2022-10-21
 */
@Tag(name = "条件优先级配置Resource")
@RestController
@RequestMapping("/api/priority-element-configs")
@AuthorityRegion("业务规则引擎")
@FuncInterceptor("RuleEngine")
@HideAuthority
public class PriorityElementConfigResource extends ProtectBaseResource<PriorityElementConfig> {
    private static final String EXCEPTION = "exception";
    private final PriorityElementConfigService priorityElementConfigService;

    public PriorityElementConfigResource(PriorityElementConfigService priorityElementConfigService) {
        this.priorityElementConfigService = priorityElementConfigService;
        this.mapUri = "/api/priority-element-configs";
    }

    /**
     * 根据对象查询所有优先级配置
     * <AUTHOR>
     * @date 2022/10/25
     * @return ResponseEntity<ResponseData<List<PriorityElementConfig>>>
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "根据对象查询所有优先级配置")
    @GetMapping("/findAll/{target}")
    public ResponseEntity<ResponseData<List<PriorityElementConfig>>> findAll(@PathVariable Integer target) {
        try {
            return ResponseData.ok(priorityElementConfigService.findAllByTarget(target));
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 逻辑删除条件优先级配置
     * <AUTHOR>
     * @date 2022/10/25
     * @param priorityElementConfigId 优先级配置id
     * @return ResponseEntity<ResponseData<Void>>
     */
    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @DeleteMapping({"/{priorityElementConfigId}"})
    @Operation(summary = "逻辑删除条件优先级配置")
    public ResponseEntity<Void> delete(@PathVariable("priorityElementConfigId") Long priorityElementConfigId) {
        try {
            priorityElementConfigService.deleteInstance(priorityElementConfigId);
            return ResponseEntity.ok().headers(HeaderUtil.deletedAlert(this.entityName, "")).build();
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, EXCEPTION, e.getMessage())).build();
        }
    }

    /**
     * 新增条件优先级配置
     * <AUTHOR>
     * @date 2022/10/21
     * @param priorityElementConfig 条件优先级
     * @return ResponseEntity<ResponseData<PriorityElementConfig>>
     */
    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PreventRepeatSubmit
    @Operation(summary = "新增条件优先级配置")
    public ResponseEntity<PriorityElementConfig> create(@Valid @RequestBody PriorityElementConfig priorityElementConfig) throws URISyntaxException {
        try {
            priorityElementConfigService.saveInstance(priorityElementConfig);
            return ResponseEntity.ok().headers(HeaderUtil.createdAlert(this.entityName, "")).build();
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        } catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, EXCEPTION, e.getMessage())).build();
        }
    }

    /**
     * 修改条件优先级配置
     * <AUTHOR>
     * @date 2022/10/24
     * @param priorityElementConfig 优先级配置
     * @return ResponseEntity<ResponseData<PriorityElementConfig>>
     */
    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PreventRepeatSubmit
    @Operation(summary = "修改条件优先级配置")
    public ResponseEntity<PriorityElementConfig> update(@Valid @RequestBody PriorityElementConfig priorityElementConfig) throws URISyntaxException {
        try {
            priorityElementConfigService.updateInstance(priorityElementConfig);
            return ResponseEntity.ok().headers(HeaderUtil.createdAlert(this.entityName, "")).build();
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        } catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, EXCEPTION, e.getMessage())).build();
        }
    }


    /**
     * 通过条件组合和条件对象查找优先级配置
     *
     * @param combination 条件组合
     * @param target 条件对象
     * @return org.springframework.http.ResponseEntity<net.airuima.domain.base.priority.PriorityElementConfig> 优先级配置
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "通过条件组合和条件对象查找优先级配置")
    @GetMapping("/byCombinationAndTarget")
    public ResponseEntity<ResponseData<PriorityElementConfig>> byCombinationAndTarget(@RequestParam(value = "combination") String combination,
                                                                                      @RequestParam(value = "target") Integer target) {
        return ResponseData.ok(priorityElementConfigService.byCombinationAndTarget(combination, target));
    }



    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览条件优先级配置";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建条件优先级配置";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改条件优先级配置";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除条件优先级配置";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_IMPORT)) {
            return "导入条件优先级配置";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出条件优先级配置";
        }
        return "";
    }

}
