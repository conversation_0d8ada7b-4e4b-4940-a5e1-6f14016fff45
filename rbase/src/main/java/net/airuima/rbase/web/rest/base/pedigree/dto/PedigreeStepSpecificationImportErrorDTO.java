package net.airuima.rbase.web.rest.base.pedigree.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Schema(description = "产品谱系工序指标导入DTO")
public class PedigreeStepSpecificationImportErrorDTO extends PedigreeStepSpecificationImportDTO{

    @Schema(description = "错误信息")
    @Excel(name = "错误信息", orderNum = "10")
    private String errorMessage;

    public PedigreeStepSpecificationImportErrorDTO() {

    }

    public PedigreeStepSpecificationImportErrorDTO(PedigreeStepSpecificationImportDTO importDto,String errorMessage) {
        super(importDto);
        this.errorMessage = errorMessage;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public PedigreeStepSpecificationImportErrorDTO setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
        return this;
    }
}
