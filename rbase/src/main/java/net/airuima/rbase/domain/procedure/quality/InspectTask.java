package net.airuima.rbase.domain.procedure.quality;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Table;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.qms.VarietyDTO;
import net.airuima.rbase.dto.rworker.quality.dto.RworkerQualityInspectionPlanDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.*;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 待检任务表Domain
 *
 * <AUTHOR>
 * @date 2023-04-20
 */
@Schema(name = "待检任务表(InspectTask)", description = "待检任务表")
@Entity
@Table(name = "procedure_inspect_task")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@FetchEntity
@NamedEntityGraph(name = "inspectTaskEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph"),
        @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")}),
                @NamedSubgraph(name = "workSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode("pedigree")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class InspectTask extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工单
     */
    @Schema(description = "工单")
    @ManyToOne
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;

    /**
     * 子工单
     */
    @Schema(description = "子工单")
    @ManyToOne
    @JoinColumn(name = "sub_work_sheet_id")
    private SubWorkSheet subWorkSheet;

    /**
     * 工序
     */
    @Schema(description = "工序")
    @ManyToOne
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 工位
     */
    @Schema(description = "工位")
    @ManyToOne
    @JoinColumn(name = "work_cell_id")
    private WorkCell workCell;

    /**
     * 项目类型
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "项目类型")
    @Column(name = "variety_id")
    private Long varietyId;

    @FetchField(mapUri = "/api/varieties", serviceId = "mom", paramKey = "varietyId")
    @Transient
    private VarietyDTO variety = new VarietyDTO();

    /**
     * 检测类型(首检0/巡检1/末检2/终检3/抽检4)
     */
    @Schema(description = "检测类型(首检0/巡检1/末检2/终检3/抽检4)")
    @Column(name = "category")
    private int category;

    /**
     * 容器号
     */
    @Schema(description = "容器号")
    @Column(name = "container_code")
    private String containerCode;

    /**
     * 处理结果:0未处理，1已处理
     */
    @Schema(description = "处理结果:0未处理，1已处理", required = true)
    @Column(name = "status", nullable = false)
    private boolean status;

    /**
     * 待检测参数明细信息(MRB指定全检方案时)
     */
    @Schema(description = "待检测参数明细信息(MRB指定全检方案时)")
    @Type(JsonType.class)
    @Column(name = "inspect_parameter_info")
    public RworkerQualityInspectionPlanDTO inspectParameterInfo;

    /**
     * 缓存具体内容
     */
    @Schema(description = "缓存具体内容")
    @Column(name = "cache")
    private String cache;

    public InspectTask(){

    }
    public InspectTask(CheckHistory checkHistory){
        this.category = checkHistory.getCategory();
        this.step = checkHistory.getStep();
        this.containerCode = checkHistory.getContainerCode();
        this.subWorkSheet = checkHistory.getSubWorkSheet();
        this.workSheet = checkHistory.getWorkSheet();
        this.workCell = checkHistory.getWorkCell();
        this.variety = checkHistory.getVarietyObj();
    }
    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public InspectTask setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public InspectTask setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public InspectTask setStep(Step step) {
        this.step = step;
        return this;
    }

    public Long getVarietyId() {
        return varietyId;
    }

    public InspectTask setVarietyId(Long varietyId) {
        this.varietyId = varietyId;
        return this;
    }

    public VarietyDTO getVariety() {
        return variety;
    }

    public InspectTask setVariety(VarietyDTO variety) {
        this.variety = variety;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public InspectTask setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public InspectTask setCategory(int category) {
        this.category = category;
        return this;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public InspectTask setContainerCode(String containerCode) {
        this.containerCode = containerCode;
        return this;
    }

    public boolean getStatus() {
        return status;
    }

    public InspectTask setStatus(boolean status) {
        this.status = status;
        return this;
    }

    public RworkerQualityInspectionPlanDTO getInspectParameterInfo() {
        return inspectParameterInfo;
    }

    public InspectTask setInspectParameterInfo(RworkerQualityInspectionPlanDTO inspectParameterInfo) {
        this.inspectParameterInfo = inspectParameterInfo;
        return this;
    }

    public String getCache() {
        return cache;
    }

    public InspectTask setCache(String cache) {
        this.cache = cache;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        InspectTask inspectTask = (InspectTask) o;
        if (inspectTask.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), inspectTask.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
