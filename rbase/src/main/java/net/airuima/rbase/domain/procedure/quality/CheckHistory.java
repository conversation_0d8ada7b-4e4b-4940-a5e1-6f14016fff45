package net.airuima.rbase.domain.procedure.quality;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.dto.qms.VarietyDTO;
import net.airuima.rbase.dto.quality.MrbProcessResultDTO;
import org.hibernate.annotations.*;
import org.hibernate.annotations.Cache;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-03-22
 */
@Schema(name = "检测历史(CheckHistory)", description = "检测历史")
@Entity
@Table(name = "procedure_check_history")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "checkHistoryEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph"),
        @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")}),
                @NamedSubgraph(name = "workSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode("pedigree")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class CheckHistory extends CustomBaseEntity implements Serializable {

    /**
     * 单据号
     */
    @Schema(description = "单据号")
    @Column(name = "serial_number")
    private String serialNumber;

    /**
     * 检测类型
     */
    @Schema(description = "检测类型(首检0/巡检1/终检3/抽检4)")
    @Column(name = "category", nullable = false)
    private int category;

    /**
     * 检测日期
     */
    @NotNull
    @Schema(description = "检测日期")
    @Column(name = "record_date")
    private LocalDateTime recordDate;

    /**
     * 检测结果
     */
    @Schema(description = "检测结果(0:不合格;1:合格)")
    @Column(name = "result", nullable = false)
    private boolean result;

    /**
     * 检测数量
     */
    @Schema(description = "检测数量")
    @Column(name = "number", nullable = false)
    private int number;

    /**
     * 合格数量
     */
    @Schema(description = "合格数量")
    @Column(name = "qualified_number", nullable = false)
    private int qualifiedNumber;

    /**
     * 不合格数量
     */
    @Schema(description = "不合格数量")
    @Column(name = "unqualified_number", nullable = false)
    private int unqualifiedNumber;

    /**
     * 子工单
     */
    @ManyToOne
    @Schema(description = "子工单")
    @JoinColumn(name = "sub_work_sheet_id")
    private SubWorkSheet subWorkSheet;

    /**
     * 工单
     */
    @ManyToOne
    @Schema(description = "工单")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;

    /**
     * 工位
     */
    @ManyToOne
    @Schema(description = "工位")
    @JoinColumn(name = "work_cell_id")
    private WorkCell workCell;

    /**
     * 工序
     */
    @ManyToOne
    @Schema(description = "工序")
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 操作人ID
     */
    @NotNull
    @Schema(description = "操作人ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "operator_id", nullable = false)
    private Long operatorId;

    /**
     * 操作人DTO
     */
    @Schema(description = "操作人DTO")
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "operatorId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "operator_id")
    @Transient
    private StaffDTO operatorDto = new StaffDTO();

    /**
     * 项目类型
     */
    @Schema(description = "项目类型")
    @Column(name = "variety")
    private Integer variety;

    /**
     * 项目类型
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "项目类型")
    @Column(name = "variety_id")
    private Long varietyId;

    @FetchField(mapUri = "/api/varieties", serviceId = "mom", paramKey = "varietyId")
    @Transient
    private VarietyDTO varietyObj = new VarietyDTO();

    /**
     * 质检方案
     */
    @Schema(description = "质检方案")
    @ManyToOne
    @JoinColumn(name = "check_rule_id")
    private PedigreeStepCheckRule checkRule;

    /**
     * 报检数量
     */
    @Schema(description = "报检数量")
    @Column(name = "inspect_number")
    private int inspectNumber;

    /**
     * 检测方式(待处理-0/通过-1/重检-2/放行-3/批退-4/MRB-5)
     */
    @Schema(description = "检测方式(待处理-0/通过-1/重检-2/放行-3/批退-4/MRB-5)", required = true)
    @Column(name = "deal_way", nullable = false)
    private int dealWay;

    /**
     * 容器号
     */
    @Schema(description = "容器号")
    @Column(name = "container_code")
    private String containerCode;

    /**
     * 是否已处理结果(0:否;1:是)
     */
    @Schema(description = "是否已处理结果(0:否;1:是)")
    @Column(name = "status", nullable = false)
    private boolean status;

    /**
     * 是否质检SN为虚拟SN
     */
    @Schema(description = "是否质检SN为虚拟SN")
    @Column(name = "is_virtual", nullable = false)
    private boolean virtual = Boolean.TRUE;

    /**
     * 是否需要根据MRD处理不良项(0:否;1:是)
     */
    @Schema(description = "是否需要根据MRD处理不良项(0:否;1:是)")
    @Column(name = "process_mrb_unqualified_item", nullable = false)
    private boolean processMrbUnqualifiedItem = Boolean.FALSE;

    /**
     * 质检处理结果明细列表
     */
    @Schema(description = "质检处理结果明细列表")
    @Type(JsonType.class)
    @Column(name = "process_result_info")
    private List<MrbProcessResultDTO> mrbProcessResultInfoList;

    public String getSerialNumber() {
        return serialNumber;
    }

    public CheckHistory setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public boolean getProcessMrbUnqualifiedItem() {
        return processMrbUnqualifiedItem;
    }

    public CheckHistory setProcessMrbUnqualifiedItem(boolean processMrbUnqualifiedItem) {
        this.processMrbUnqualifiedItem = processMrbUnqualifiedItem;
        return this;
    }

    public boolean getStatus() {
        return status;
    }

    public CheckHistory setStatus(boolean status) {
        this.status = status;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public CheckHistory setCategory(int category) {
        this.category = category;
        return this;
    }

    public Integer getVariety() {
        return variety;
    }

    public CheckHistory setVariety(Integer variety) {
        this.variety = variety;
        return this;
    }

    public LocalDateTime getRecordDate() {
        return recordDate;
    }

    public CheckHistory setRecordDate(LocalDateTime recordDate) {
        this.recordDate = recordDate;
        return this;
    }

    public boolean getResult() {
        return result;
    }

    public CheckHistory setResult(boolean result) {
        this.result = result;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public CheckHistory setNumber(int number) {
        this.number = number;
        return this;
    }

    public int getQualifiedNumber() {
        return qualifiedNumber;
    }

    public CheckHistory setQualifiedNumber(int qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public int getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public CheckHistory setUnqualifiedNumber(int unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public CheckHistory setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public CheckHistory setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public CheckHistory setStep(Step step) {
        this.step = step;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public CheckHistory setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public StaffDTO getOperatorDto() {
        return operatorDto;
    }

    public CheckHistory setOperatorDto(StaffDTO operatorDto) {
        this.operatorDto = operatorDto;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public CheckHistory setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public Long getVarietyId() {
        return varietyId;
    }

    public CheckHistory setVarietyId(Long varietyId) {
        this.varietyId = varietyId;
        return this;
    }

    public VarietyDTO getVarietyObj() {
        return varietyObj;
    }

    public CheckHistory setVarietyObj(VarietyDTO varietyObj) {
        this.varietyObj = varietyObj;
        return this;
    }

    public PedigreeStepCheckRule getCheckRule() {
        return checkRule;
    }

    public CheckHistory setCheckRule(PedigreeStepCheckRule checkRule) {
        this.checkRule = checkRule;
        return this;
    }

    public int getInspectNumber() {
        return inspectNumber;
    }

    public CheckHistory setInspectNumber(int inspectNumber) {
        this.inspectNumber = inspectNumber;
        return this;
    }

    public int getDealWay() {
        return dealWay;
    }

    public CheckHistory setDealWay(int dealWay) {
        this.dealWay = dealWay;
        return this;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public CheckHistory setContainerCode(String containerCode) {
        this.containerCode = containerCode;
        return this;
    }

    public List<MrbProcessResultDTO> getMrbProcessResultInfoList() {
        return mrbProcessResultInfoList;
    }

    public CheckHistory setMrbProcessResultInfoList(List<MrbProcessResultDTO> mrbProcessResultInfoList) {
        this.mrbProcessResultInfoList = mrbProcessResultInfoList;
        return this;
    }

    public boolean getVirtual() {
        return virtual;
    }

    public CheckHistory setVirtual(boolean virtual) {
        this.virtual = virtual;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CheckHistory checkHistory = (CheckHistory) o;
        if (checkHistory.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), checkHistory.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }
}
