package net.airuima.rbase.domain.procedure.aps;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.util.NumberUtils;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 销售订单Domain
 *
 * <AUTHOR>
 * @date 2022-12-19
 */
@Schema(name = "销售订单(SaleOrder)", description = "销售订单")
@Entity
@Table(name = "procedure_sale_order", uniqueConstraints = @UniqueConstraint(name = "procedure_sale_order_unique", columnNames = {"serial_number", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "saleOrderEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree")})
public class SaleOrder extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    @Schema(description = "订单号", required = true)
    @Column(name = "serial_number", nullable = false)
    private String serialNumber;

    /**
     * 优先级
     */
    @Schema(description = "优先级", required = true)
    @Column(name = "priority", nullable = false)
    private int priority;

    /**
     * 订单类型
     */
    @Schema(description = "订单类型", required = true)
    @Column(name = "category", nullable = false)
    private int category;

    /**
     * 交付日期
     */
    @Schema(description = "交付日期")
    @Column(name = "delivery_date")
    private LocalDate deliveryDate;

    /**
     * 产品谱系ID
     */
    @NotNull
    @Schema(description = "产品谱系ID", required = true)
    @ManyToOne
    @JoinColumn(name = "pedigree_id")
    private Pedigree pedigree;

    /**
     * 订单数量
     */
    @NotNull
    @Schema(description = "订单数量", required = true)
    @Column(name = "number", nullable = false)
    private int number;

    /**
     * 投产数
     */
    @NotNull
    @Schema(description = "投产数", required = true)
    @Column(name = "production_quantity", nullable = false)
    private int productionQuantity;

    /**
     * 合同编号
     */
    @Schema(description = "合同编号")
    @Column(name = "contract_no")
    private String contractNo;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "client_id")
    private Long clientId;

    /**
     * 客户DTO
     */
    @FetchField(mapUri = "/api/clients", serviceId = "mom", paramKey = "clientId",tableName = "client")
    @Schema(description = "客户DTO")
    @Transient
    private ClientDTO clientDTO = new ClientDTO();

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Column(name = "note")
    private String note;

    /**
     * 计划开工日期
     */
    @Schema(description = "计划开工日期")
    @Column(name = "plan_start_date")
    private LocalDate planStartDate;

    /**
     * 计划结单日期
     */
    @Schema(description = "计划完工日期")
    @Column(name = "plan_end_date")
    private LocalDate planEndDate;

    /**
     * 完成数量
     */
    @Schema(description = "完成数量")
    @Column(name = "finish_number")
    private int finishNumber;

    /**
     * 订单进度
     */
    @Schema(description = "订单进度")
    @Transient
    private BigDecimal progress;

    public String getSerialNumber() {
        return serialNumber;
    }

    public SaleOrder setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public int getPriority() {
        return priority;
    }

    public SaleOrder setPriority(int priority) {
        this.priority = priority;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public SaleOrder setCategory(int category) {
        this.category = category;
        return this;
    }

    public LocalDate getDeliveryDate() {
        return deliveryDate;
    }

    public SaleOrder setDeliveryDate(LocalDate deliveryDate) {
        this.deliveryDate = deliveryDate;
        return this;
    }

    public Pedigree getPedigree() {
        return pedigree;
    }

    public SaleOrder setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public SaleOrder setNumber(int number) {
        this.number = number;
        return this;
    }

    public int getProductionQuantity() {
        return productionQuantity;
    }

    public SaleOrder setProductionQuantity(int productionQuantity) {
        this.productionQuantity = productionQuantity;
        return this;
    }

    public String getContractNo() {
        return contractNo;
    }

    public SaleOrder setContractNo(String contractNo) {
        this.contractNo = contractNo;
        return this;
    }

    public Long getClientId() {
        return clientId;
    }

    public SaleOrder setClientId(Long clientId) {
        this.clientId = clientId;
        return this;
    }

    public ClientDTO getClientDTO() {
        return clientDTO;
    }

    public SaleOrder setClientDTO(ClientDTO clientDTO) {
        this.clientDTO = clientDTO;
        return this;
    }

    public String getNote() {
        return note;
    }

    public SaleOrder setNote(String note) {
        this.note = note;
        return this;
    }

    public LocalDate getPlanStartDate() {
        return planStartDate;
    }

    public SaleOrder setPlanStartDate(LocalDate planStartDate) {
        this.planStartDate = planStartDate;
        return this;
    }

    public LocalDate getPlanEndDate() {
        return planEndDate;
    }

    public SaleOrder setPlanEndDate(LocalDate planEndDate) {
        this.planEndDate = planEndDate;
        return this;
    }

    public int getFinishNumber() {
        return finishNumber;
    }

    public SaleOrder setFinishNumber(int finishNumber) {
        this.finishNumber = finishNumber;
        return this;
    }

    public BigDecimal getProgress() {
        if (this.getNumber() == Constants.INT_ZERO){
            return BigDecimal.ZERO;
        }
        return NumberUtils.divide(this.getFinishNumber(), this.getNumber(), Constants.INT_FOUR);
    }

    public SaleOrder setProgress(BigDecimal progress) {
        this.progress = progress;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SaleOrder wsRework = (SaleOrder) o;
        if (wsRework.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), wsRework.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
