package net.airuima.rbase.domain.base.quality;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.dto.bom.MaterialAttributeDTO;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.dto.organization.SupplierDTO;
import net.airuima.rbase.dto.qms.VarietyDTO;
import net.airuima.rbase.web.rest.base.quality.dto.WorkCellCheckStartRuleImportDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 主要针对工位上的首中末检测及抽检的发起时机（适用于首检、过程检）
 * <AUTHOR>
 * @date 2021-03-22
 */
@Schema(name = "工位检测配置(WorkCellCheckConfig)", description = "工位检测配置")
@Entity
@Table(name = "base_work_cell_check_start_rule", uniqueConstraints = @UniqueConstraint(columnNames = {"work_cell_id","work_flow_id","step_id","category","flag","variety_id","deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@AuditEntity(value = "工位检测配置")
@NamedEntityGraph(name = "workCellCheckStartRuleEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class WorkCellCheckStartRule extends CustomBaseEntity implements Serializable {

    @ManyToOne
    @Schema(description = "工位id")
    @JoinColumn(name = "work_cell_id")
    private WorkCell workCell;

    @Schema(description = "检测类型(首检0/巡检1/末检2/抽检3/终检4/来料检5)")
    @Column(name = "category", nullable = false)
    private int category;

    @Schema(description = "检测时机(0:切换型号;1:切换总工单;2:切换子工单;3:固定周期;4:指定时间)")
    @Column(name = "flag")
    private int flag;

    @Schema(description = "检测周期(H)")
    @Column(name = "duration", nullable = false)
    private double duration;

    @Schema(description = "宽放时长(H)")
    @Column(name = "extend_time", nullable = false)
    private double extendTime;

    /**
     * 宽放类型 0:按计划宽放(自动生成下一个待检时间+宽放时长);1:按实际宽放（自动生成下一个待检时间，等实际触发的第一次请求加上宽放时长）
     */
    @Schema(description = "宽放类型(0:按计划宽放(自动生成下一个待检时间+宽放时长);1:按实际执行宽放（自动生成下一个待检时间，等实际触发的第一次请求加上宽放时长）)")
    @Column(name = "extend_type")
    private int extendType;

    @Schema(description = "指定时间(例如 11:45,12:00)")
    @Column(name = "specify_time")
    private String specifyTime;

    /**
     * 项目类型
     */
    @Schema(description = "项目类型")
    @Column(name = "variety")
    private Integer variety;

    /**
     * 项目类型
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "项目类型")
    @Column(name = "variety_id")
    private Long varietyId;

    @FetchField(mapUri = "/api/varieties", serviceId = "mom", paramKey = "varietyId")
    @Transient
    private VarietyDTO varietyObj = new VarietyDTO();

    /**
     * 发起类型(工位0/工序后1)
     */
    @Schema(description = "发起类型(工位0/工序后1/物料2))", required = true)
    @Column(name = "target", nullable = false)
    private int target;

    /**
     * 工艺路线ID
     */
    @Schema(description = "工艺路线ID")
    @ManyToOne
    @JoinColumn(name = "work_flow_id")
    private WorkFlow workFlow;

    /**
     * 工序ID
     */
    @Schema(description = "工序ID")
    @ManyToOne
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 物料属性ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "物料属性ID")
    @Column(name = "attribute_id")
    private Long attributeId;

    @FetchField(mapUri = "/api/material-attributes", serviceId = "mom", paramKey = "attributeId")
    @Transient
    private MaterialAttributeDTO materialAttributeDto = new MaterialAttributeDTO();

    /**
     * 物料id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "物料id")
    @Column(name = "material_id")
    private Long materialId;

    /**
     * 物料id列表
     */
    @Schema(description = "物料id列表")
    @Transient
    private List<Long> materialIdList;


    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "materialId")
    @Transient
    private MaterialDTO materialDto = new MaterialDTO();


    /**
     * 供应商ID
     */
    @Schema(description = "供应商ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "supplier_id")
    private Long supplierId;

    /**
     * 供应商DTO
     */
    @Transient
    @Schema(description = "供应商DTO")
    @FetchField(mapUri = "/api/suppliers", serviceId = "mom", paramKey = "supplierId",tableName = "supplier")
    private SupplierDTO supplierDto = new SupplierDTO();


    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "client_id")
    private Long clientId;

    /**
     * 客户DTO
     */
    @FetchField(mapUri = "/api/clients", serviceId = "mom", paramKey = "clientId", tableName = "client")
    @Schema(description = "客户DTO")
    @Transient
    private ClientDTO clientDto = new ClientDTO();

    /**
     * 是否启用(0:禁用;1:启用)
     */
    @Schema(description = "是否启用(0:禁用;1:启用)", required = true)
    @Column(name = "is_enable", nullable = false)
    private boolean isEnable;

    public WorkCellCheckStartRule(WorkCellCheckStartRuleImportDTO workCellCheckStartRuleImportDto) {
        this.category = workCellCheckStartRuleImportDto.getCategory();
        this.flag = !ObjectUtils.isEmpty(workCellCheckStartRuleImportDto.getFlag())?workCellCheckStartRuleImportDto.getFlag():Constants.INT_ZERO;
        this.duration = !ObjectUtils.isEmpty(workCellCheckStartRuleImportDto.getDuration()) ? workCellCheckStartRuleImportDto.getDuration() : Constants.DOUBLE_ZERRO;
        this.extendTime = !ObjectUtils.isEmpty(workCellCheckStartRuleImportDto.getExtendTime()) ? workCellCheckStartRuleImportDto.getExtendTime() : Constants.DOUBLE_ZERRO;
        this.specifyTime = workCellCheckStartRuleImportDto.getSpecifyTime();
        this.isEnable = workCellCheckStartRuleImportDto.getIsEnable();
    }
    public WorkCellCheckStartRule() {
    }

    public List<Long> getMaterialIdList() {
        return materialIdList;
    }

    public WorkCellCheckStartRule setMaterialIdList(List<Long> materialIdList) {
        this.materialIdList = materialIdList;
        return this;
    }

    public Long getAttributeId() {
        return attributeId;
    }

    public WorkCellCheckStartRule setAttributeId(Long attributeId) {
        this.attributeId = attributeId;
        return this;
    }

    public MaterialAttributeDTO getMaterialAttributeDto() {
        return materialAttributeDto;
    }

    public WorkCellCheckStartRule setMaterialAttributeDto(MaterialAttributeDTO materialAttributeDto) {
        this.materialAttributeDto = materialAttributeDto;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public WorkCellCheckStartRule setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public WorkCellCheckStartRule setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public WorkCellCheckStartRule setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
        return this;
    }

    public SupplierDTO getSupplierDto() {
        return supplierDto;
    }

    public WorkCellCheckStartRule setSupplierDto(SupplierDTO supplierDto) {
        this.supplierDto = supplierDto;
        return this;
    }

    public Long getClientId() {
        return clientId;
    }

    public WorkCellCheckStartRule setClientId(Long clientId) {
        this.clientId = clientId;
        return this;
    }

    public ClientDTO getClientDto() {
        return clientDto;
    }

    public WorkCellCheckStartRule setClientDto(ClientDTO clientDto) {
        this.clientDto = clientDto;
        return this;
    }


    public WorkCell getWorkCell() {
        return workCell;
    }

    public Integer getVariety() {
        return variety;
    }

    public WorkCellCheckStartRule setVariety(Integer variety) {
        this.variety = variety;
        return this;
    }

    public WorkCellCheckStartRule setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public WorkCellCheckStartRule setCategory(int category) {
        this.category = category;
        return this;
    }

    public int getFlag() {
        return flag;
    }

    public WorkCellCheckStartRule setFlag(int flag) {
        this.flag = flag;
        return this;
    }

    public double getDuration() {
        return duration;
    }

    public WorkCellCheckStartRule setDuration(double duration) {
        this.duration = duration;
        return this;
    }

    public double getExtendTime() {
        return extendTime;
    }

    public WorkCellCheckStartRule setExtendTime(double extendTime) {
        this.extendTime = extendTime;
        return this;
    }

    public String getSpecifyTime() {
        return specifyTime;
    }

    public WorkCellCheckStartRule setSpecifyTime(String specifyTime) {
        this.specifyTime = specifyTime;
        return this;
    }

    public Long getVarietyId() {
        return varietyId;
    }

    public WorkCellCheckStartRule setVarietyId(Long varietyId) {
        this.varietyId = varietyId;
        return this;
    }

    public VarietyDTO getVarietyObj() {
        return varietyObj;
    }

    public WorkCellCheckStartRule setVarietyObj(VarietyDTO varietyObj) {
        this.varietyObj = varietyObj;
        return this;
    }

    public int getTarget() {
        return target;
    }

    public WorkCellCheckStartRule setTarget(int target) {
        this.target = target;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public WorkCellCheckStartRule setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public WorkCellCheckStartRule setStep(Step step) {
        this.step = step;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public WorkCellCheckStartRule setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }


    public int getExtendType() {
        return extendType;
    }

    public WorkCellCheckStartRule setExtendType(int extendType) {
        this.extendType = extendType;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        WorkCellCheckStartRule that = (WorkCellCheckStartRule) o;
        return category == that.category &&
                flag == that.flag &&
                Double.compare(that.duration, duration) == 0 &&
                Double.compare(that.extendTime, extendTime) == 0 &&
                Objects.equals(workCell, that.workCell);
    }

    @Override
    public int hashCode() {
        return Objects.hash(workCell, category, flag, duration, extendTime);
    }
}
