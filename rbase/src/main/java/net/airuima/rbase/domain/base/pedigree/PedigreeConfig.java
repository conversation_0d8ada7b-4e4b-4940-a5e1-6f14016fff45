package net.airuima.rbase.domain.base.pedigree;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系属性Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "产品谱系属性(PedigreeConfig)", description = "产品谱系属性")
@Entity
@Table(name = "base_pedigree_config", uniqueConstraints = @UniqueConstraint(columnNames = {"pedigree_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "产品谱系属性数据")
@NamedEntityGraph(name = "pedigreeConfigEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree")})
public class PedigreeConfig extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品谱系id
     */
    @NotNull
    @ManyToOne
    @Schema(description = "产品谱系id")
    @JoinColumn(name = "pedigree_id", nullable = false)
    private Pedigree pedigree;

    /**
     * 分单数量
     */
    @Schema(description = "分单数量")
    @Column(name = "split_number")
    private int splitNumber;

    /**
     * 计划完成天数
     */
    @Schema(description = "计划完成天数")
    @Column(name = "plan_finish_day")
    private int planFinishDay;

    /**
     * 目标成品率
     */
    @Schema(description = "目标成品率")
    @Column(name = "qualified_rate")
    private double qualifiedRate;

    /**
     * 是否需要重新定制流程框图(0:否;1:是)
     */
    @Schema(description = "是否需要重新定制流程框图(0:否;1:是)")
    @Column(name = "is_custom_work_flow")
    private boolean isCustomWorkFlow;

    /**
     * 是否验证工单领料(0:否;1:是)
     */
    @Schema(description = "是否验证工单领料(0:否;1:是)")
    @Column(name = "is_check_receive_material")
    private boolean isCheckReceiveMaterial;

    @Schema(description = "是否复用SN(0:否;1:是)")
    @Column(name = "is_reuse_sn")
    private boolean isReuseSN;

    /**
     * 下单流程类型(0:使用流程框图;1:使用型号最新下单流程;2:待定制下单流程)
     */
    @Schema(description = "下单流程类型(0:使用流程框图;1:使用型号最新下单流程;2:待定制下单流程)")
    @Column(name = "workflow_type")
    private int workFlowType;

    /**
     * 是否启用(0:禁用;1:启用)
     */
    @Schema(description = "是否启用(0:禁用;1:启用)")
    @Column(name = "is_enable")
    private boolean isEnable;

    public Pedigree getPedigree() {
        return pedigree;
    }

    public PedigreeConfig setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public int getSplitNumber() {
        return splitNumber;
    }

    public PedigreeConfig setSplitNumber(int splitNumber) {
        this.splitNumber = splitNumber;
        return this;
    }

    public int getPlanFinishDay() {
        return planFinishDay;
    }

    public PedigreeConfig setPlanFinishDay(int planFinishDay) {
        this.planFinishDay = planFinishDay;
        return this;
    }

    public double getQualifiedRate() {
        return qualifiedRate;
    }

    public PedigreeConfig setQualifiedRate(double qualifiedRate) {
        this.qualifiedRate = qualifiedRate;
        return this;
    }

    public boolean getIsCustomWorkFlow() {
        return isCustomWorkFlow;
    }

    public PedigreeConfig setIsCustomWorkFlow(boolean isCustomWorkFlow) {
        this.isCustomWorkFlow = isCustomWorkFlow;
        return this;
    }

    public boolean getIsCheckReceiveMaterial() {
        return isCheckReceiveMaterial;
    }

    public PedigreeConfig setIsCheckReceiveMaterial(boolean isCheckReceiveMaterial) {
        this.isCheckReceiveMaterial = isCheckReceiveMaterial;
        return this;
    }

    public int getWorkFlowType() {
        return workFlowType;
    }

    public PedigreeConfig setWorkFlowType(int workFlowType) {
        this.workFlowType = workFlowType;
        return this;
    }

    public boolean getIsReuseSN() {
        return isReuseSN;
    }

    public PedigreeConfig setIsReuseSN(boolean isReuseSN) {
        this.isReuseSN = isReuseSN;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public PedigreeConfig setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PedigreeConfig pedigreeConfig = (PedigreeConfig) o;
        if (pedigreeConfig.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), pedigreeConfig.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
