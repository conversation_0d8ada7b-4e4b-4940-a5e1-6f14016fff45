package net.airuima.rbase.domain.procedure.single;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.query.annotation.FetchEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 单支生产过程产生不良记录Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "单支生产过程产生不良记录(SnUnqualifiedItem)", description = "单支生产过程产生不良记录实体")
@Entity
@Table(name = "procedure_sn_unqualified_item", uniqueConstraints = {@UniqueConstraint(name = "procedure_sn_unqualified_item_unique_index",
        columnNames = {"sn_work_detail_id", "deleted"})})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@FetchEntity
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "snUnqualifiedItemEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "workSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode("pedigree"),@NamedAttributeNode("workLine")})})
public class SnUnqualifiedItem extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 投产SN
     */
    @NotNull
    @Schema(description = "投产SN", required = true)
    @Column(name = "sn", nullable = false)
    private String sn;

    /**
     * 不良项目
     */
    @NotNull
    @ManyToOne
    @Schema(description = "不良项目", required = true)
    @JoinColumn(name = "unqualified_item_id", nullable = false)
    private UnqualifiedItem unqualifiedItem;

    /**
     * 工单
     */
    @ManyToOne
    @Schema(description = "工单")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;

    /**
     * 子工单
     */
    @ManyToOne
    @Schema(description = "子工单")
    @JoinColumn(name = "sub_work_sheet_id")
    private SubWorkSheet subWorkSheet;

    /**
     * 工序
     */
    @NotNull
    @ManyToOne
    @Schema(description = "工序", required = true)
    @JoinColumn(name = "step_id", nullable = false)
    private Step step;

    /**
     * 标识是否已生成在线返修单0:否;1:是
     */
    @Schema(description = "标识是否已生成在线返修单0:否;1:是")
    @Column(name = "flag")
    private boolean flag;

    public boolean getFlag() {
        return flag;
    }

    public SnUnqualifiedItem setFlag(boolean flag) {
        this.flag = flag;
        return this;
    }
    /**
     * sn详情
     */
    @ManyToOne
    @Schema(description = "sn详情")
    @JoinColumn(name = "sn_work_detail_id")
    private SnWorkDetail snWorkDetail;

    public SnWorkDetail getSnWorkDetail() {
        return snWorkDetail;
    }

    public SnUnqualifiedItem setSnWorkDetail(SnWorkDetail snWorkDetail) {
        this.snWorkDetail = snWorkDetail;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public SnUnqualifiedItem setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public UnqualifiedItem getUnqualifiedItem() {
        return unqualifiedItem;
    }

    public SnUnqualifiedItem setUnqualifiedItem(UnqualifiedItem unqualifiedItem) {
        this.unqualifiedItem = unqualifiedItem;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public SnUnqualifiedItem setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public SnUnqualifiedItem setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public SnUnqualifiedItem setStep(Step step) {
        this.step = step;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SnUnqualifiedItem snUnqualifiedItem = (SnUnqualifiedItem) o;
        if (snUnqualifiedItem.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), snUnqualifiedItem.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
