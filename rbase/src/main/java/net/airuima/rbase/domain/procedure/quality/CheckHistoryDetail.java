package net.airuima.rbase.domain.procedure.quality;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.dto.qms.CheckItemDTO;
import net.airuima.rbase.dto.qms.DefectDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 * 现场无SN时，Rworker根据检测数量自动生成对应SN填入，适用于首检、过程检、末检
 * <AUTHOR>
 * @date 2021-03-22
 */
@Schema(name = "检测历史明细(CheckHistoryDetail)", description = "检测历史明细")
@Entity
@Table(name = "procedure_check_history_detail")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "checkHistoryDetailEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "checkHistory",subgraph = "checkHistoryEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "checkHistoryEntityGraph",
                        attributeNodes = {
                        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
                        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph"),
                        @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")}),
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")}),
                @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
@FetchEntity
public class CheckHistoryDetail extends CustomBaseEntity implements Serializable {

    private String sn;

    /**
     * 检测结果(0:不合格;1:合格)
     */
    @Schema(description = "检测结果(0:不合格;1:合格)")
    @Column(name = "result", nullable = false)
    private boolean result;

    /**
     * 检测历史
     */
    @NotNull
    @ManyToOne
    @Schema(description = "检测历史")
    @JoinColumn(name = "history_id", nullable = false)
    private CheckHistory checkHistory;

    /**
     * 检测项目
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "检测项目")
    @Column(name = "check_item_id")
    private Long checkItemId;

    @FetchField(mapUri = "/api/check-items", serviceId = "mom", paramKey = "checkItemId")
    @Transient
    private CheckItemDTO checkItem = new CheckItemDTO();

    /**
     * 检测数据值
     */
    @Schema(description = "检测数据值")
    @Column(name = "check_data", nullable = false)
    private String checkData;

    /**
     * 合格范围(开闭区间或者OK)
     */
    @Schema(description = "合格范围(开闭区间或者OK)")
    @Column(name = "qualified_range", nullable = false)
    private String qualifiedRange;

    /**
     * 缺陷原因
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "缺陷原因")
    @Column(name = "defect_id")
    private Long defectId;

    @FetchField(mapUri = "/api/defects", serviceId = "mom", paramKey = "defectId")
    @Transient
    private DefectDTO defect = new DefectDTO();

    /**
     * 不良项目
     */
    @Schema(description = "不良项目")
    @ManyToOne
    @JoinColumn(name = "unqualified_item_id")
    private UnqualifiedItem unqualifiedItem;

    /**
     * 是否为虚拟SN(0:否;1:是)
     */
    @Schema(description = "是否为虚拟SN(0:否;1:是)")
    @Column(name = "is_virtual", nullable = false)
    private boolean virtual;

    public UnqualifiedItem getUnqualifiedItem() {
        return unqualifiedItem;
    }

    public CheckHistoryDetail setUnqualifiedItem(UnqualifiedItem unqualifiedItem) {
        this.unqualifiedItem = unqualifiedItem;
        return this;
    }

    public boolean getVirtual() {
        return virtual;
    }

    public CheckHistoryDetail setVirtual(boolean virtual) {
        this.virtual = virtual;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public CheckHistoryDetail setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public boolean getResult() {
        return result;
    }

    public CheckHistoryDetail setResult(boolean result) {
        this.result = result;
        return this;
    }

    public CheckHistory getCheckHistory() {
        return checkHistory;
    }

    public CheckHistoryDetail setCheckHistory(CheckHistory checkHistory) {
        this.checkHistory = checkHistory;
        return this;
    }

    public Long getCheckItemId() {
        return checkItemId;
    }

    public CheckHistoryDetail setCheckItemId(Long checkItemId) {
        this.checkItemId = checkItemId;
        return this;
    }

    public CheckItemDTO getCheckItem() {
        return checkItem;
    }

    public CheckHistoryDetail setCheckItem(CheckItemDTO checkItem) {
        this.checkItem = checkItem;
        return this;
    }

    public String getCheckData() {
        return checkData;
    }

    public CheckHistoryDetail setCheckData(String checkData) {
        this.checkData = checkData;
        return this;
    }

    public String getQualifiedRange() {
        return qualifiedRange;
    }

    public CheckHistoryDetail setQualifiedRange(String qualifiedRange) {
        this.qualifiedRange = qualifiedRange;
        return this;
    }

    public Long getDefectId() {
        return defectId;
    }

    public CheckHistoryDetail setDefectId(Long defectId) {
        this.defectId = defectId;
        return this;
    }

    public DefectDTO getDefect() {
        return defect;
    }

    public CheckHistoryDetail setDefect(DefectDTO defect) {
        this.defect = defect;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CheckHistoryDetail that = (CheckHistoryDetail) o;
        return result == that.result &&
                Objects.equals(sn, that.sn) &&
                Objects.equals(checkHistory, that.checkHistory) &&
                Objects.equals(checkItem, that.checkItem) &&
                Objects.equals(checkData, that.checkData) &&
                Objects.equals(qualifiedRange, that.qualifiedRange);
    }

    @Override
    public int hashCode() {
        return Objects.hash(sn, result, checkHistory, checkItem, checkData, qualifiedRange);
    }
}
