package net.airuima.rbase.domain.procedure.batch;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.query.annotation.FetchEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器工作详情不良明细表Domain
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Schema(name = "容器工作详情不良明细表(ContainerDetailUnqualifiedItem)", description = "容器工作详情不良明细表")
@Entity
@Table(name = "procedure_container_detail_unqualified_item", uniqueConstraints = @UniqueConstraint(name = "container_detail_unqualified_item_unique", columnNames = {"container_detail_id", "unqualified_item_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@FetchEntity
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "containerDetailUnqualifiedItemEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "containerDetail",subgraph = "containerDetailEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "containerDetailEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "batchWorkDetail",subgraph = "batchWorkDetailEntityGraph"),
                                @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")}),
                @NamedSubgraph(name = "batchWorkDetailEntityGraph", attributeNodes = {
                        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),
                        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph")}),
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph"),@NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree")}),
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class ContainerDetailUnqualifiedItem extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 容器详情ID
     */
    @ManyToOne
    @Schema(description = "容器详情ID")
    @JoinColumn(name = "container_detail_id")
    private ContainerDetail containerDetail;

    /**
     * 不良项目 ID
     */
    @ManyToOne
    @Schema(description = "不良项目 ID")
    @JoinColumn(name = "unqualified_item_id")
    private UnqualifiedItem unqualifiedItem;

    /**
     * 数量
     */
    @Schema(description = "数量")
    @Column(name = "number")
    private int number;

    /**
     * 标识生成返工单标识0:未生成;1：全部生成
     */
    @Schema(description = "标识是否已生成在线返修单0:否;1:是")
    @Column(name = "flag")
    private boolean flag;

    /**
     * 已返修数
     */
    @Schema(description = "已返修数")
    @Column(name = "repair_count")
    private int repairCount;

    public int getRepairCount() {
        return repairCount;
    }

    public ContainerDetailUnqualifiedItem setRepairCount(int repairCount) {
        this.repairCount = repairCount;
        return this;
    }

    public boolean getFlag() {
        return flag;
    }

    public ContainerDetailUnqualifiedItem setFlag(boolean flag) {
        this.flag = flag;
        return this;
    }

    public ContainerDetail getContainerDetail() {
        return containerDetail;
    }

    public ContainerDetailUnqualifiedItem setContainerDetail(ContainerDetail containerDetail) {
        this.containerDetail = containerDetail;
        return this;
    }

    public UnqualifiedItem getUnqualifiedItem() {
        return unqualifiedItem;
    }

    public ContainerDetailUnqualifiedItem setUnqualifiedItem(UnqualifiedItem unqualifiedItem) {
        this.unqualifiedItem = unqualifiedItem;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public ContainerDetailUnqualifiedItem setNumber(int number) {
        this.number = number;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ContainerDetailUnqualifiedItem ContainerDetailUnqualifiedItem = (ContainerDetailUnqualifiedItem) o;
        if (ContainerDetailUnqualifiedItem.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), ContainerDetailUnqualifiedItem.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
