package net.airuima.rbase.domain.base.pedigree;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.priority.PriorityElementConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.dto.batch.PreContainerDetailInfo;
import net.airuima.rbase.dto.document.DocumentDTO;
import net.airuima.rbase.dto.organization.ClientDTO;
import org.hibernate.annotations.*;
import org.hibernate.annotations.Cache;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工序指标Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "产品谱系工序指标(PedigreeStepSpecification)", description = "产品谱系工序指标")
@Entity
@Table(name = "base_pedigree_step_specification", uniqueConstraints = @UniqueConstraint(columnNames = {"pedigree_id", "work_flow_id", "step_id","client_id" ,"deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "pedigreeStepSpecificationEntityGraph", attributeNodes = {@NamedAttributeNode("pedigree"),
        @NamedAttributeNode("workFlow"),
        @NamedAttributeNode(value = "step", subgraph = "stepEntityGraph")}, subgraphs = {
        @NamedSubgraph(name = "stepEntityGraph", attributeNodes = {
                @NamedAttributeNode("stepGroup")})})
@FetchEntity
public class PedigreeStepSpecification extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 技术指标
     */
    @Schema(description = "技术指标")
    @Column(name = "qualification")
    private String qualification;

    /**
     * 谱系id
     */
    @ManyToOne
    @Schema(description = "谱系id")
    @JoinColumn(name = "pedigree_id")
    private Pedigree pedigree;

    @ManyToOne
    @Schema(description = "工艺路线id")
    @JoinColumn(name = "work_flow_id")
    private WorkFlow workFlow;

    /**
     * 工序Id
     */
    @NotNull
    @ManyToOne
    @Schema(description = "工序Id")
    @JoinColumn(name = "step_id", nullable = false)
    private Step step;


    /**
     * 条件优先级配置id
     */
    @ManyToOne
    @Schema(description = "条件优先级配置id")
    @JoinColumn(name = "priority_element_config_id")
    private PriorityElementConfig priorityElementConfig;

    /**
     * SOP图片DTO集合
     */
    @Schema(description = "SOP图片DTO集合")
    @Transient
    private List<DocumentDTO> documentDTOList;


    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "client_id")
    private Long clientId;

    /**
     * 客户DTO
     */
    @FetchField(mapUri = "/api/clients", serviceId = "mom", paramKey = "clientId", tableName = "client")
    @Schema(description = "客户DTO")
    @Transient
    private ClientDTO clientDto = new ClientDTO();

    /**
     * 外部系统文件id编码Json
     */
    @Schema(description = "外部系统文件Json")
    @Type(JsonType.class)
    @Column(name = "foreign_document_code")
    private List<String> foreignDocumentCodeList;

    public Long getClientId() {
        return clientId;
    }

    public PedigreeStepSpecification setClientId(Long clientId) {
        this.clientId = clientId;
        return this;
    }

    public ClientDTO getClientDto() {
        return clientDto;
    }

    public PedigreeStepSpecification setClientDto(ClientDTO clientDto) {
        this.clientDto = clientDto;
        return this;
    }

    public PriorityElementConfig getPriorityElementConfig() {
        return priorityElementConfig;
    }

    public PedigreeStepSpecification setPriorityElementConfig(PriorityElementConfig priorityElementConfig) {
        this.priorityElementConfig = priorityElementConfig;
        return this;
    }

    public List<DocumentDTO> getDocumentDTOList() {
        return documentDTOList;
    }

    public void setDocumentDTOList(List<DocumentDTO> documentDTOList) {
        this.documentDTOList = documentDTOList;
    }

    public String getQualification() {
        return qualification;
    }

    public PedigreeStepSpecification setQualification(String qualification) {
        this.qualification = qualification;
        return this;
    }

    public Pedigree getPedigree() {
        return pedigree;
    }

    public PedigreeStepSpecification setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public PedigreeStepSpecification setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public PedigreeStepSpecification setStep(Step step) {
        this.step = step;
        return this;
    }

    public List<String> getForeignDocumentCodeList() {
        return foreignDocumentCodeList;
    }

    public PedigreeStepSpecification setForeignDocumentCodeList(List<String> foreignDocumentCodeList) {
        this.foreignDocumentCodeList = foreignDocumentCodeList;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PedigreeStepSpecification pedigreeStepSpecification = (PedigreeStepSpecification) o;
        if (pedigreeStepSpecification.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), pedigreeStepSpecification.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
