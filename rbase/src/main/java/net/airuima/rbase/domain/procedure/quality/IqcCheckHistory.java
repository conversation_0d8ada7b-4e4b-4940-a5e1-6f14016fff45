package net.airuima.rbase.domain.procedure.quality;

import io.hypersistence.utils.hibernate.type.json.JsonType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.persistence.Table;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.pedigree.PedigreeStepCheckRule;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.ClientDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.rbase.dto.quality.MrbProcessResultDTO;
import org.hibernate.annotations.*;
import net.airuima.rbase.dto.organization.SupplierDTO;
import org.hibernate.annotations.Cache;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 来料检验
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Schema(name = "来料检验", description = "来料检验")
@Entity
@Table(name = "procedure_iqc_check_history", uniqueConstraints = @UniqueConstraint(columnNames = {"serial_number", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@AuditEntity(value = "来料检验")
public class IqcCheckHistory extends CustomBaseEntity implements Serializable {

    /**
     * 检验单号，必须唯一
     */
    @Column(name = "serial_number", nullable = false)
    @Schema(description = "检验单号，必须唯一")
    private String serialNumber;

    /**
     * 状态(0:待检验;1:已质检)
     */
    @Column(name = "status", nullable = false)
    @Schema(description = "状态(0:待检验;1:已质检)")
    private Integer status;

    /**
     * 质检方案
     */
    @Schema(description = "质检方案")
    @ManyToOne
    @JoinColumn(name = "check_rule_id")
    private PedigreeStepCheckRule pedigreeStepCheckRule;


    /**
     * 物料id
     */
    @Column(name = "material_id", nullable = false)
    @Schema(description = "物料id")
    private Long materialId;

    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "materialId")
    @Transient
    private MaterialDTO materialDto = new MaterialDTO();

    /**
     * 物料批次
     */
    @Column(name = "lot")
    @Schema(description = "物料批次")
    private String lot;

    /**
     * 来料数量
     */
    @Column(name = "number")
    @Schema(description = "来料数量")
    private BigDecimal number;

    /**
     * 客户id
     */
    @Column(name = "client_id")
    @Schema(description = "客户id")
    private Long clientId;

    /**
     * 客户DTO
     */
    @FetchField(mapUri = "/api/clients", serviceId = "mom", paramKey = "clientId", tableName = "client")
    @Schema(description = "客户DTO")
    @Transient
    private ClientDTO clientDto = new ClientDTO();

    /**
     * 供应商id
     */
    @Column(name = "supplier_id")
    @Schema(description = "供应商id")
    private Long supplierId;

    /**
     * 供应商DTO
     */
    @Transient
    @Schema(description = "供应商DTO")
    @FetchField(mapUri = "/api/suppliers", serviceId = "mom", paramKey = "supplierId", tableName = "supplier")
    private SupplierDTO supplierDto = new SupplierDTO();


    /**
     * 来料日期
     */
    @Column(name = "arrival_time")
    @Schema(description = "来料日期")
    private LocalDateTime arrivalTime;

    /**
     * 生产日期
     */
    @Column(name = "production_time")
    @Schema(description = "生产日期")
    private LocalDateTime productionTime;

    /**
     * 有效期
     */
    @Column(name = "valid_time")
    @Schema(description = "有效期")
    private LocalDateTime validTime;

    /**
     * 检验员id
     */
    @Column(name = "operator_id")
    @Schema(description = "检验员id")
    private Long operatorId;

    /**
     * 操作人DTO
     */
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "operatorId")
    @FetchDataFilter(schema = "mom", tableName = "staff", foreignKey = "operator_id")
    @Transient
    private StaffDTO operatorDto = new StaffDTO();


    /**
     * 处理人id
     */
    @Column(name = "dealer_id")
    @Schema(description = "处理人id")
    private Long dealerId;

    /**
     * 操作人DTO
     */
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "dealerId")
    @FetchDataFilter(schema = "mom", tableName = "staff", foreignKey = "dealer_id")
    @Transient
    private StaffDTO dealerDto = new StaffDTO();


    /**
     * 检验日期
     */
    @Column(name = "check_time")
    @Schema(description = "检验日期")
    private LocalDateTime checkTime;


    /**
     * 检验结果(0:不合格;1:合格)
     */
    @Column(name = "result")
    @Schema(description = "检验结果(0:不合格;1:合格)")
    private Boolean result;


    /**
     * 处理方式(0-待处理;1-合格接收;2-让步接收;3-判退;4-挑选;5-MRB)
     */
    @Column(name = "deal_way")
    @Schema(description = "处理方式(0-待处理;1-合格接收;2-让步接收;3-判退;4-挑选;5-MRB)")
    private Integer dealWay;


    /**
     * 描述
     */
    @Column(name = "note")
    @Schema(description = "描述")
    private String note;

    /**
     * 合格数量
     */
    @Column(name = "qualified_number")
    @Schema(description = "合格数量")
    private BigDecimal qualifiedNumber;

    /**
     * 不良数量
     */
    @Column(name = "unqualified_number")
    @Schema(description = "不良数量")
    private BigDecimal unqualifiedNumber;


    /**
     * 质检处理结果明细列表
     */
    @Schema(description = "质检处理结果明细列表")
    @Type(JsonType.class)
    @Column(name = "process_result_info")
    private List<MrbProcessResultDTO> mrbProcessResultInfoList;

    /**
     * 缓存具体内容
     */
    @Schema(description = "缓存具体内容")
    @Column(name = "cache")
    private String cache;

    public ClientDTO getClientDto() {
        return clientDto;
    }

    public IqcCheckHistory setClientDto(ClientDTO clientDto) {
        this.clientDto = clientDto;
        return this;
    }

    public PedigreeStepCheckRule getPedigreeStepCheckRule() {
        return pedigreeStepCheckRule;
    }

    public IqcCheckHistory setPedigreeStepCheckRule(PedigreeStepCheckRule pedigreeStepCheckRule) {
        this.pedigreeStepCheckRule = pedigreeStepCheckRule;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public IqcCheckHistory setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public IqcCheckHistory setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public IqcCheckHistory setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public IqcCheckHistory setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    public String getLot() {
        return lot;
    }

    public IqcCheckHistory setLot(String lot) {
        this.lot = lot;
        return this;
    }

    public BigDecimal getNumber() {
        return number;
    }

    public IqcCheckHistory setNumber(BigDecimal number) {
        this.number = number;
        return this;
    }

    public Long getClientId() {
        return clientId;
    }

    public IqcCheckHistory setClientId(Long clientId) {
        this.clientId = clientId;
        return this;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public IqcCheckHistory setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
        return this;
    }

    public SupplierDTO getSupplierDto() {
        return supplierDto;
    }

    public IqcCheckHistory setSupplierDto(SupplierDTO supplierDto) {
        this.supplierDto = supplierDto;
        return this;
    }

    public LocalDateTime getArrivalTime() {
        return arrivalTime;
    }

    public IqcCheckHistory setArrivalTime(LocalDateTime arrivalTime) {
        this.arrivalTime = arrivalTime;
        return this;
    }

    public LocalDateTime getProductionTime() {
        return productionTime;
    }

    public IqcCheckHistory setProductionTime(LocalDateTime productionTime) {
        this.productionTime = productionTime;
        return this;
    }

    public LocalDateTime getValidTime() {
        return validTime;
    }

    public IqcCheckHistory setValidTime(LocalDateTime validTime) {
        this.validTime = validTime;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public IqcCheckHistory setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public StaffDTO getOperatorDto() {
        return operatorDto;
    }

    public IqcCheckHistory setOperatorDto(StaffDTO operatorDto) {
        this.operatorDto = operatorDto;
        return this;
    }

    public Long getDealerId() {
        return dealerId;
    }

    public IqcCheckHistory setDealerId(Long dealerId) {
        this.dealerId = dealerId;
        return this;
    }

    public StaffDTO getDealerDto() {
        return dealerDto;
    }

    public IqcCheckHistory setDealerDto(StaffDTO dealerDto) {
        this.dealerDto = dealerDto;
        return this;
    }

    public LocalDateTime getCheckTime() {
        return checkTime;
    }

    public IqcCheckHistory setCheckTime(LocalDateTime checkTime) {
        this.checkTime = checkTime;
        return this;
    }

    public Boolean getResult() {
        return result;
    }

    public IqcCheckHistory setResult(Boolean result) {
        this.result = result;
        return this;
    }

    public Integer getDealWay() {
        return dealWay;
    }

    public IqcCheckHistory setDealWay(Integer dealWay) {
        this.dealWay = dealWay;
        return this;
    }

    public String getNote() {
        return note;
    }

    public IqcCheckHistory setNote(String note) {
        this.note = note;
        return this;
    }

    public BigDecimal getQualifiedNumber() {
        return qualifiedNumber;
    }

    public IqcCheckHistory setQualifiedNumber(BigDecimal qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
        return this;
    }

    public BigDecimal getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public IqcCheckHistory setUnqualifiedNumber(BigDecimal unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
        return this;
    }


    public List<MrbProcessResultDTO> getMrbProcessResultInfoList() {
        return mrbProcessResultInfoList;
    }

    public IqcCheckHistory setMrbProcessResultInfoList(List<MrbProcessResultDTO> mrbProcessResultInfoList) {
        this.mrbProcessResultInfoList = mrbProcessResultInfoList;
        return this;
    }

    public String getCache() {
        return cache;
    }

    public IqcCheckHistory setCache(String cache) {
        this.cache = cache;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        IqcCheckHistory iqcCheckHistory = (IqcCheckHistory) o;
        if (iqcCheckHistory.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), iqcCheckHistory.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
