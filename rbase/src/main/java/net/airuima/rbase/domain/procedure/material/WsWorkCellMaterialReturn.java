package net.airuima.rbase.domain.procedure.material;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.bom.MaterialDTO;
import net.airuima.rbase.dto.organization.StaffDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/3/28
 */
@Schema(name = "工单工位退料表(WsWorkCellMaterialReturn)", description = "工单工位退料表")
@Entity
@Table(name = "procedure_ws_work_cell_material_return")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "WsWorkCellMaterialReturnEntityGraph", attributeNodes = {
        @NamedAttributeNode(value = "workSheet", subgraph = "workSheetEntityGraph"),
        @NamedAttributeNode(value = "workCell", subgraph = "workCellEntityGraph")}, subgraphs = {
        @NamedSubgraph(name = "workSheetEntityGraph", attributeNodes = {
                @NamedAttributeNode("pedigree")}),
        @NamedSubgraph(name = "workCellEntityGraph",
                attributeNodes = {@NamedAttributeNode("workLine"),
                        @NamedAttributeNode(value = "workStation",
                                subgraph = "workStationEntityGraph")}),
        @NamedSubgraph(name = "workStationEntityGraph",
                attributeNodes = {@NamedAttributeNode("workLine")})})
public class WsWorkCellMaterialReturn extends CustomBaseEntity implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 总工单id
     */
    @ManyToOne
    @Schema(description = "总工单id")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;

    /**
     * 工位id
     */
    @ManyToOne
    @Schema(description = "工位id")
    @JoinColumn(name = "work_cell_id")
    private WorkCell workCell;


    /**
     * 物料Id
     */
    @Schema(description = "物料Id", required = true)
    @Column(name = "material_id", nullable = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long materialId;

    @Transient
    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "materialId")
    private MaterialDTO materialDto = new MaterialDTO();

    /**
     * 操作员工ID
     */
    @Schema(description = " 操作员工ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "operator_id", nullable = false)
    private Long operatorId;


    /**
     * 操作员工DTO
     */
    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "operatorId")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "operator_id")
    @Transient
    private StaffDTO operatorDto = new StaffDTO();

    /**
     * 批次号
     */
    @Schema(description = "批次号")
    @Column(name = "batch")
    private String batch;

    /**
     * 退料数量
     */
    @Schema(description = "退料数量")
    @Column(name = "number")
    private double number;

    /**
     * 记录日期时间
     */
    @Schema(description = "记录日期")
    @Column(name = "record_time")
    private LocalDateTime recordTime;

    /**
     * 记录日期(方便统计）
     */
    @Schema(description = "记录日期(方便统计）")
    @Column(name = "record_date")
    private LocalDateTime recordDate;

    public WsWorkCellMaterialReturn() {
    }

    public WsWorkCellMaterialReturn(WorkSheet workSheet, WorkCell workCell, Long materialId, Long operatorId, String batch, double number) {
        this.workSheet = workSheet;
        this.workCell = workCell;
        this.materialId = materialId;
        this.operatorId = operatorId;
        this.batch = batch;
        this.number = number;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public WsWorkCellMaterialReturn setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public WsWorkCellMaterialReturn setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public WsWorkCellMaterialReturn setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public WsWorkCellMaterialReturn setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    public String getBatch() {
        return batch;
    }

    public WsWorkCellMaterialReturn setBatch(String batch) {
        this.batch = batch;
        return this;
    }

    public double getNumber() {
        return number;
    }

    public WsWorkCellMaterialReturn setNumber(double number) {
        this.number = number;
        return this;
    }

    public LocalDateTime getRecordTime() {
        return recordTime;
    }

    public WsWorkCellMaterialReturn setRecordTime(LocalDateTime recordTime) {
        this.recordTime = recordTime;
        return this;
    }

    public LocalDateTime getRecordDate() {
        return recordDate;
    }

    public WsWorkCellMaterialReturn setRecordDate(LocalDateTime recordDate) {
        this.recordDate = recordDate;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public WsWorkCellMaterialReturn setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public StaffDTO getOperatorDto() {
        return operatorDto;
    }

    public WsWorkCellMaterialReturn setOperatorDto(StaffDTO operatorDto) {
        this.operatorDto = operatorDto;
        return this;
    }
}
