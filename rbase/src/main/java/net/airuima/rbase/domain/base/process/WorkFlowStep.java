package net.airuima.rbase.domain.base.process;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 流程框图工序Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "流程框图工序(WorkFlowStep)", description = "流程框图工序实体")
@Entity
@Table(name = "base_work_flow_step", uniqueConstraints = @UniqueConstraint(name = "base_work_flow_step_unique", columnNames = {"work_flow_id", "step_id", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "流程框图工序数据")
@NamedEntityGraph(name = "workFlowStepEntityGraph", attributeNodes = {
        @NamedAttributeNode("workFlow"),
        @NamedAttributeNode(value = "step", subgraph = "stepEntityGraph")}, subgraphs = {
        @NamedSubgraph(name = "stepEntityGraph", attributeNodes = {
                @NamedAttributeNode("stepGroup")})})
public class WorkFlowStep extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 流程框图
     */
    @NotNull
    @ManyToOne
    @Schema(description = "流程框图", required = true)
    @JoinColumn(name = "work_flow_id", nullable = false)
    private WorkFlow workFlow;

    /**
     * 工序
     */
    @ManyToOne
    @Schema(description = "工序", required = true)
    @JoinColumn(name = "step_id", nullable = false)
    private Step step;

    /**
     * 前置工序列表，分号隔开
     */
    @Schema(description = "前置工序列表，分号隔开")
    @Column(name = "pre_step_id")
    private String preStepId;

    /**
     * 后置工序列表，分号隔开
     */
    @Schema(description = "后置工序列表，分号隔开")
    @Column(name = "after_step_id")
    private String afterStepId;


    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public WorkFlowStep setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public WorkFlowStep setStep(Step step) {
        this.step = step;
        return this;
    }

    public String getPreStepId() {
        return preStepId;
    }

    public WorkFlowStep setPreStepId(String preStepId) {
        this.preStepId = preStepId;
        return this;
    }

    public String getAfterStepId() {
        return afterStepId;
    }

    public WorkFlowStep setAfterStepId(String afterStepId) {
        this.afterStepId = afterStepId;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WorkFlowStep workFlowStep = (WorkFlowStep) o;
        if (workFlowStep.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), workFlowStep.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
