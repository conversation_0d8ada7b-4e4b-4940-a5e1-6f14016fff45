package net.airuima.rbase.domain.base.scene;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.Forbidden;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.dto.organization.OrganizationDTO;
import net.airuima.rbase.listener.EntityListener;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产线Domain
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Schema(name = "生产线(WorkLine)", description = "生产线")
@Entity
@Table(name = "base_work_line", uniqueConstraints = @UniqueConstraint(columnNames = {"code", "deleted"}))
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@AuditEntity(value = "生产线数据")
@EntityListeners(EntityListener.class)
public class WorkLine extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 线体名称
     */
    @NotNull
    @Schema(description = "线体名称")
    @Column(name = "name", nullable = false)
    private String name;

    /**
     * 组织架构id
     */
    @Schema(description = "组织架构id")
    @Column(name = "organization_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long organizationId;

    @FetchField(mapUri = "/api/organizations", serviceId = "mom", paramKey = "organizationId",tableName = "organization")
    @Transient
    private OrganizationDTO organizationDto = new OrganizationDTO();

    /**
     * 线体编码
     */
    @NotNull
    @Schema(description = "线体编码")
    @Column(name = "code", nullable = false)
    private String code;

    /**
     * 禁用启用(0:禁用;1:启用)
     */
    @Schema(description = "禁用启用(0:禁用;1:启用)")
    @Column(name = "is_enable", nullable = false)
    @Forbidden
    private boolean isEnable;

    public String getName() {
        return name;
    }

    public WorkLine setName(String name) {
        this.name = name;
        return this;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public WorkLine setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
        return this;
    }

    public OrganizationDTO getOrganizationDto() {
        return organizationDto;
    }

    public WorkLine setOrganizationDto(OrganizationDTO organizationDto) {
        this.organizationDto = organizationDto;
        return this;
    }

    public String getCode() {
        return code;
    }

    public WorkLine setCode(String code) {
        this.code = code;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public WorkLine setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WorkLine workLine = (WorkLine) o;
        if (workLine.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), workLine.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
