package net.airuima.rbase.integrate.rwms;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.proxy.rwms.RbaseShipmentOrderProxy;
import net.airuima.rbase.service.procedure.batch.WsMaterialService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Order(0)
@Service
public class ShipmentOrderSyncWmsService implements IShipmentOrderSyncWmsService{

    @Autowired
    private RbaseShipmentOrderProxy rbaseShipmentOrderProxy;

    @Autowired
    private WsMaterialService wsMaterialService;

    private final String KEY_SYNC_SHIPMENT_ORDER = "key_sync_shipment_order";

    /**
     * 保存同步出库单及明细数据至RWMS
     * @param workSheet 工单
     * @return net.airuima.rbase.dto.base.BaseDTO 同步结果
     */
    @Override
    public BaseDTO syncShipmentOrder(WorkSheet workSheet) {
//        List<WsMaterial> wsMaterialList = wsMaterialService.findByWorkSheetId(workSheet.getId());
//        if (CollectionUtils.isEmpty(wsMaterialList)) {
//            return new BaseDTO(Constants.OK);
//        }
//        SyncShipmentOrderDTO shipmentOrderDTO = new SyncShipmentOrderDTO();
//        String result = dictionaryFeignClient.findDictionaryByCode(KEY_SYNC_SHIPMENT_ORDER);
//        if (StringUtils.isBlank(result)) {
//            return new BaseDTO(Constants.OK);
//        }
//        Map<String, String> syncShipmentOrderConfig = JSON.parseObject(result, new TypeReference<Map<String, String>>() {
//        });
//        shipmentOrderDTO.setEstimateTime(workSheet.getPlanStartDate())
//                .setOriginSerialNumber(workSheet.getSerialNumber())
//                .setOwnerCode(syncShipmentOrderConfig.get("ownerCode"))
//                .setWarehouseCode(syncShipmentOrderConfig.get("wareHouseCode"))
//                .setReceiverCode(syncShipmentOrderConfig.get("receiverCode"));
//        List<SyncShipmentOrderDTO.SyncShipmentOrderDetailInfo> syncShipmentOrderDetailInfoList = Lists.newArrayList();
//        wsMaterialList.forEach(wsMaterial -> {
//            syncShipmentOrderDetailInfoList.add(new SyncShipmentOrderDTO.SyncShipmentOrderDetailInfo(wsMaterial));
//        });
//        shipmentOrderDTO.setSyncShipmentOrderDetailInfoList(syncShipmentOrderDetailInfoList);
//        return rbaseShipmentOrderProxy.syncShipmentOrder(shipmentOrderDTO);
        return new BaseDTO(Constants.OK);
    }

}
