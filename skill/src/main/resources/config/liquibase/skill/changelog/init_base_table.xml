<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.6.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.6.xsd">
    <changeSet author="zhuhuawu (generated)" id="1737508642733-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="base_pedigree_step_skill"/>
            </not>
        </preConditions>
        <createTable remarks="产品谱系工艺路线工序技能配置表" tableName="base_pedigree_step_skill">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="pedigree_id" remarks="产品谱系ID" type="BIGINT"/>
            <column name="work_flow_id" remarks="工艺路线ID" type="BIGINT"/>
            <column name="step_id" remarks="工序ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="skill_id" remarks="技能ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueBoolean="true" name="is_enable" remarks="是否启用(0:禁用;1:启用)" type="BIT(1)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
        </createTable>
        <createTable remarks="生产技能表" tableName="base_skill">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="code" remarks="技能编码" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="name" remarks="技能名称" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueBoolean="true" name="is_enable" remarks="是否启用(0:禁用;1:启用)" type="BIT(1)"/>
            <column name="note" remarks="备注" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
        </createTable>
        <createTable tableName="base_staff_skill">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="staff_id" remarks="员工ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="skill_id" remarks="技能ID" type="BIGINT"/>
            <column defaultValueBoolean="true" name="is_enable" remarks="是否启用(0:禁用;1:启用)" type="BIT(1)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="expire_date" remarks="失效日期" type="timestamp"/>
        </createTable>
        <addUniqueConstraint columnNames="pedigree_id, work_flow_id, step_id, skill_id, deleted" constraintName="base_pedigree_step_skill_unique" tableName="base_pedigree_step_skill"/>
        <addUniqueConstraint columnNames="code, deleted" constraintName="base_skill_code_deleted_unique" tableName="base_skill"/>
        <addUniqueConstraint columnNames="staff_id, skill_id, deleted" constraintName="base_staff_skill_unique" tableName="base_staff_skill"/>
        <createIndex indexName="base_pedigree_step_skill_multi_index" tableName="base_pedigree_step_skill">
            <column name="pedigree_id"/>
            <column name="work_flow_id"/>
            <column name="step_id"/>
            <column defaultValueBoolean="true" name="is_enable"/>
            <column defaultValueNumeric="0" name="deleted"/>
        </createIndex>
        <createIndex indexName="base_pedigree_step_skill_pedigree_index" tableName="base_pedigree_step_skill">
            <column name="pedigree_id"/>
        </createIndex>
        <createIndex indexName="base_pedigree_step_skill_skill_index" tableName="base_pedigree_step_skill">
            <column name="skill_id"/>
        </createIndex>
        <createIndex indexName="base_pedigree_step_skill_step_index" tableName="base_pedigree_step_skill">
            <column name="step_id"/>
        </createIndex>
        <createIndex indexName="base_pedigree_step_skill_work_flow_index" tableName="base_pedigree_step_skill">
            <column name="work_flow_id"/>
        </createIndex>
        <createIndex indexName="base_skill_code_index" tableName="base_skill">
            <column name="code"/>
        </createIndex>
        <createIndex indexName="base_skill_enable_index" tableName="base_skill">
            <column defaultValueBoolean="true" name="is_enable"/>
        </createIndex>
        <createIndex indexName="base_staff_skill_skill_indedx" tableName="base_staff_skill">
            <column name="skill_id"/>
        </createIndex>
        <createIndex indexName="base_staff_skill_staff_index" tableName="base_staff_skill">
            <column name="staff_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
