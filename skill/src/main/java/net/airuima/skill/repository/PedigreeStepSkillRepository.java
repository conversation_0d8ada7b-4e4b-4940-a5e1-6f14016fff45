package net.airuima.skill.repository;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.skill.domain.PedigreeStepSkill;
import net.airuima.skill.domain.Skill;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工艺路线工序技能配置Repository
 *
 * <AUTHOR>
 * @date 2022-08-08
 */
@Repository
public interface PedigreeStepSkillRepository extends LogicDeleteableRepository<PedigreeStepSkill>,
        EntityGraphJpaSpecificationExecutor<PedigreeStepSkill>, EntityGraphJpaRepository<PedigreeStepSkill, Long> {

    /**
     * 获取谱系下对应的技能列表
     * @param pedigreeId 产品谱系主键id
     * @param workflowId 工艺路线主键ID
     * @param stepId 工序主键ID
     * @param isEnable 是否启用
     * @param deleted 删除标识
     * @return java.util.List<net.airuima.domain.base.skill.Skill> 返回技能列表
     */
    @DataFilter(isSkip = true)
    @Query("select pss.skill from PedigreeStepSkill pss where pss.pedigree.id=?1 and pss.workFlow.id=?2 and pss.step.id=?3 and pss.isEnable=?4 and pss.deleted=?5")
    List<Skill> findSkillByPedigreeIdAndWorkflowIdAndStepIdAndDeleted(Long pedigreeId, Long workflowId, Long stepId, boolean isEnable, Long deleted);

    /**
     * 获取谱系下对应的技能列表
     * @param pedigreeIds 产品谱系主键id列表
     * @param workflowId 工艺路线主键ID
     * @param stepId 工序主键ID
     * @param isEnable 是否启用
     * @param deleted 删除标识
     * @return java.util.List<net.airuima.domain.base.skill.Skill> 返回技能列表
     */
    @DataFilter(isSkip = true)
    @EntityGraph(value = "pedigreeStepSkillEntityGraph",type = EntityGraph.EntityGraphType.FETCH)
    @Query("select pss from PedigreeStepSkill pss where pss.pedigree.id in(?1) and pss.workFlow.id=?2 and pss.step.id=?3 and pss.skill.isEnable=?4 and pss.deleted=?5")
    List<PedigreeStepSkill> findByPedigreeIdInAndWorkflowIdAndStepIdAndDeleted(List<Long> pedigreeIds,Long workflowId,Long stepId, boolean isEnable, Long deleted);

    /**
     * 通过主键id与删除字段查询产品谱系工艺路线工序技能配置
     *
     * @param id      主键id
     * @param deleted 逻辑删除
     * @return java.util.Optional<net.airuima.domain.base.skill.Skill> 返回技能
     */
    @DataFilter(isSkip = true)
    Optional<PedigreeStepSkill> findByIdAndDeleted(Long id, Long deleted);

    /**
     *  根据谱系id、工艺路线主键id、工序主键id删除配置
     * @param pedigreeId 产品谱系主键ID
     * @param workFlowId 工艺路线主键id
     * @param stepId 工序主键id
     */
    @Modifying
    @Query("delete from PedigreeStepSkill pss where pss.pedigree.id = ?1 and pss.workFlow.id = ?2 and pss.step.id = ?3")
    void deleteByPedigreeIdAndWorkFlowIdAndStepId(Long pedigreeId, Long workFlowId, Long stepId);
}
