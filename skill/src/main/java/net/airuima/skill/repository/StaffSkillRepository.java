package net.airuima.skill.repository;

import net.airuima.config.annotation.DataFilter;
import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.skill.domain.Skill;
import net.airuima.skill.domain.StaffSkill;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 员工技能配置Repository
 *
 * <AUTHOR>
 * @date 2022-08-08
 */
@Repository
public interface StaffSkillRepository extends LogicDeleteableRepository<StaffSkill>,
        JpaSpecificationExecutor<StaffSkill>, JpaRepository<StaffSkill, Long> {

    /**
     * 根据逻辑删除标识及员工主键id查询技能配置
     * @param deleted 逻辑删除标识
     * @param staffId 员工主键id
     * @return java.util.List<net.airuima.domain.base.skill.StaffSkill>  员工技能配置列表
     */
    @DataFilter(isSkip = true)
    @Query("select ss from StaffSkill ss where ss.staffId=?1 and ss.deleted=?2")
    List<StaffSkill> findAllSkillByStaffIdAndDeleted(long staffId,long deleted);

    /**
     * 根据员工主键ID等条件获取合规的技能列表
     * @param staffId 员工主键ID
     * @param compareDate 比较日期
     * @param deleted        逻辑删除
     * @return java.util.List<net.airuima.domain.base.skill.Skill> 技能列表
     * <AUTHOR>
     * @date 2023/12/11
     */
    @DataFilter(isSkip = true)
    @Query("select ss.skill from StaffSkill ss where  ss.staffId=?1  and (ss.expireDate is null or ss.expireDate>=?2) and ss.skill.isEnable=true and ss.deleted=?3")
    List<Skill> findLegalSkillByStaffIdAndDeleted(long staffId, LocalDate compareDate, long deleted);

    /**
     * 删除指定员工主键id的配置
     * @param staffId 员工主键id
     */
    @Modifying
    @Query("delete from StaffSkill ss  where ss.staffId = ?1")
    void deleteByStaffId(Long staffId);
}
