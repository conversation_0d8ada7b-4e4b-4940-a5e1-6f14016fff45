package net.airuima.skill.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工艺路线工序技能配置Domain
 *
 * <AUTHOR>
 * @date 2022-08-08
 */
@Schema(name = "产品谱系工艺路线工序技能配置(PedigreeStepSkill)", description = "产品谱系工艺路线工序技能配置")
@Entity
@Table(name = "base_pedigree_step_skill", uniqueConstraints = {
        @UniqueConstraint(name = "base_pedigree_step_skill_unique", columnNames = {"pedigree_id", "work_flow_id", "step_id", "skill_id", "deleted"})
})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "pedigreeStepSkillEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree"),@NamedAttributeNode("workFlow"),
        @NamedAttributeNode(value = "step",subgraph = "stepEntityGraph")}, subgraphs = {
        @NamedSubgraph(name = "stepEntityGraph", attributeNodes = {
                @NamedAttributeNode("stepGroup")})})
public class PedigreeStepSkill extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品谱系ID
     */
    @NotNull
    @Schema(description = "产品谱系ID")
    @ManyToOne
    @JoinColumn(name = "pedigree_id")
    private Pedigree pedigree;

    /**
     * 工艺路线ID
     */
    @Schema(description = "工艺路线ID")
    @ManyToOne
    @JoinColumn(name = "work_flow_id")
    private WorkFlow workFlow;

    /**
     * 工序ID
     */
    @NotNull
    @Schema(description = "工序ID", required = true)
    @ManyToOne
    @JoinColumn(name = "step_id", nullable = false)
    private Step step;

    /**
     * 技能ID
     */
    @NotNull
    @Schema(description = "技能ID", required = true)
    @ManyToOne
    @JoinColumn(name = "skill_id", nullable = false)
    private Skill skill;

    /**
     * 是否启用(0:禁用;1:启用)
     */
    @Schema(description = "是否启用(0:禁用;1:启用)")
    @Column(name = "is_enable")
    private boolean isEnable;


    public Pedigree getPedigree() {
        return pedigree;
    }

    public PedigreeStepSkill setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public PedigreeStepSkill setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public PedigreeStepSkill setStep(Step step) {
        this.step = step;
        return this;
    }

    public Skill getSkill() {
        return skill;
    }

    public PedigreeStepSkill setSkill(Skill skill) {
        this.skill = skill;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public PedigreeStepSkill setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PedigreeStepSkill pedigreeStepSkill = (PedigreeStepSkill) o;
        if (pedigreeStepSkill.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), pedigreeStepSkill.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    public PedigreeStepSkill() {
    }

    public PedigreeStepSkill(Pedigree pedigree,WorkFlow workFlow,Step step,Skill skill,boolean isEnable) {
        this.pedigree = pedigree;
        this.workFlow = workFlow;
        this.step = step;
        this.skill = skill;
        this.isEnable = isEnable;
    }
}
