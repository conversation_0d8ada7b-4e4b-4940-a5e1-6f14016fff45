package net.airuima.skill.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.dto.organization.StaffDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 员工技能配置Domain
 *
 * <AUTHOR>
 * @date 2022-08-08
 */
@Schema(name = "(员工技能配置BaseStaffSkill)", description = "")
@Entity
@Table(name = "base_staff_skill", uniqueConstraints = {
        @UniqueConstraint(name = "base_staff_skill_unique", columnNames = {"staff_id", "skill_id", "deleted"})
})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@FetchEntity
@DiscriminatorValue(value = "base")
public class StaffSkill extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 员工ID
     */
    @Schema(description = "员工ID", required = true)
    @Column(name = "staff_id", nullable = false)
    private Long staffId;

    @FetchField(mapUri = "/api/staff", serviceId = "mom", paramKey = "staffId",tableName = "staff")
    @FetchDataFilter(schema = "mom",tableName = "staff",foreignKey = "staff_id")
    @Transient
    private StaffDTO staffDto = new StaffDTO();

    /**
     * 技能ID
     */
    @Schema(description = "技能ID")
    @ManyToOne
    @JoinColumn(name = "skill_id")
    private Skill skill;

    /**
     * 失效日期(不填,永久有效)
     */
    @Schema(description = "失效日期(不填,永久有效)")
    @Column(name = "expire_date", nullable = true)
    private LocalDate expireDate;

    /**
     * 是否启用(0:禁用;1:启用)
     */
    @Schema(description = "是否启用(0:禁用;1:启用)")
    @Column(name = "is_enable")
    private boolean isEnable;

    public Long getStaffId() {
        return staffId;
    }

    public StaffSkill setStaffId(Long staffId) {
        this.staffId = staffId;
        return this;
    }

    public StaffDTO getStaffDto() {
        return staffDto;
    }

    public StaffSkill setStaffDto(StaffDTO staffDto) {
        this.staffDto = staffDto;
        return this;
    }

    public Skill getSkill() {
        return skill;
    }

    public StaffSkill setSkill(Skill skill) {
        this.skill = skill;
        return this;
    }

    public LocalDate getExpireDate() {
        return expireDate;
    }

    public StaffSkill setExpireDate(LocalDate expireDate) {
        this.expireDate = expireDate;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public StaffSkill setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        StaffSkill staffSkill = (StaffSkill) o;
        if (staffSkill.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), staffSkill.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    public StaffSkill() {
    }

    public StaffSkill(long staffId,Skill skill,boolean isEnable,LocalDate expireDate) {
        this.staffId = staffId;
        this.skill = skill;
        this.isEnable = isEnable;
        this.expireDate = expireDate;
    }
}
