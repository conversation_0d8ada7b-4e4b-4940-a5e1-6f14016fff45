package net.airuima.skill.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.Forbidden;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产技能Domain
 *
 * <AUTHOR>
 * @date 2022-08-08
 */
@Schema(name = "生产技能(Skill)", description = "生产技能")
@Entity
@Table(name = "base_skill", uniqueConstraints = {
        @UniqueConstraint(name = "base_skill_code_deleted_unique", columnNames = {"code", "deleted"})
})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorValue(value = "base")
public class Skill extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 技能编码(获取的key为key_skill_code)
     */
    @NotNull
    @Schema(description = "技能编码(获取的key为key_skill_code)", required = true)
    @Column(name = "code", nullable = false)
    private String code;

    /**
     * 技能名称
     */
    @NotNull
    @Schema(description = "技能名称", required = true)
    @Column(name = "name", nullable = false)
    private String name;

    /**
     * 是否启用(0:禁用;1:启用)
     */
    @Schema(description = "是否启用(0:禁用;1:启用)")
    @Column(name = "is_enable")
    @Forbidden
    private boolean isEnable;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Column(name = "note")
    private String note;


    public String getCode() {
        return code;
    }

    public Skill setCode(String code) {
        this.code = code;
        return this;
    }

    public String getName() {
        return name;
    }

    public Skill setName(String name) {
        this.name = name;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public Skill setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    public String getNote() {
        return note;
    }

    public Skill setNote(String note) {
        this.note = note;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Skill skill = (Skill) o;
        if (skill.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), skill.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
