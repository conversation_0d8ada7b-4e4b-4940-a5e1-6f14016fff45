package net.airuima.skill.web.rest.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工艺路线工序技能配置新增DTO
 *
 * <AUTHOR>
 * @date 2022-08-10
 */
@Tag(name = "产品谱系工艺路线工序技能配置新增DTO")
public class PedigreeStepSkillDTO implements Serializable {

    /**
     * 产品谱系ID
     */
    @Schema(description = "产品谱系ID", required = true)
    private Long pedigreeId;

    /**
     * 工艺路线ID
     */
    @Schema(description = "工艺路线ID", required = true)
    private Long workFlowId;

    /**
     * 工序ID
     */
    @Schema(description = "工序ID", required = true)
    private Long stepId;

    /**
     * 技能信息列表
     */
    @Schema(description = "技能信息列表", required = true)
    private List<SkillInfo> skillInfoList;

    public Long getPedigreeId() {
        return pedigreeId;
    }

    public void setPedigreeId(Long pedigreeId) {
        this.pedigreeId = pedigreeId;
    }

    public Long getWorkFlowId() {
        return workFlowId;
    }

    public void setWorkFlowId(Long workFlowId) {
        this.workFlowId = workFlowId;
    }

    public Long getStepId() {
        return stepId;
    }

    public void setStepId(Long stepId) {
        this.stepId = stepId;
    }

    public List<SkillInfo> getSkillInfoList() {
        return skillInfoList;
    }

    public void setSkillInfoList(List<SkillInfo> skillInfoList) {
        this.skillInfoList = skillInfoList;
    }

    public PedigreeStepSkillDTO() {
    }

    public PedigreeStepSkillDTO(Long pedigreeId, Long workFlowId, Long stepId, List<SkillInfo> skillInfoList) {
        this.pedigreeId = pedigreeId;
        this.workFlowId = workFlowId;
        this.stepId = stepId;
        this.skillInfoList = skillInfoList;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PedigreeStepSkillDTO that = (PedigreeStepSkillDTO) o;
        return Objects.equals(pedigreeId, that.pedigreeId) &&
                Objects.equals(workFlowId, that.workFlowId) &&
                Objects.equals(stepId, that.stepId) &&
                Objects.equals(skillInfoList, that.skillInfoList);
    }

    @Override
    public int hashCode() {
        return Objects.hash(pedigreeId, workFlowId, stepId, skillInfoList);
    }

    public static class SkillInfo implements Serializable {
        /**
         * 技能ID
         */
        @Schema(description = "技能ID", required = true)
        private Long skillId;

        /**
         * 是否启用(0:禁用;1:启用)
         */
        @Schema(description = "是否启用(0:禁用;1:启用)", required = true)
        private Boolean isEnable;

        public Long getSkillId() {
            return skillId;
        }

        public void setSkillId(Long skillId) {
            this.skillId = skillId;
        }

        public boolean getIsEnable() {
            return isEnable;
        }

        public void setIsEnable(boolean isEnable) {
            this.isEnable = isEnable;
        }

        public SkillInfo() {
        }

        public SkillInfo(Long skillId, Boolean isEnable) {
            this.skillId = skillId;
            this.isEnable = isEnable;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            SkillInfo skillInfo = (SkillInfo) o;
            return Objects.equals(skillId, skillInfo.skillId) &&
                    Objects.equals(isEnable, skillInfo.isEnable);
        }

        @Override
        public int hashCode() {
            return Objects.hash(skillId, isEnable);
        }
    }
}
