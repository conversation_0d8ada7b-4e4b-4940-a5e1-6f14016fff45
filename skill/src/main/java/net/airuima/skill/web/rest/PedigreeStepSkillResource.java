package net.airuima.skill.web.rest;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.constant.Constants;
import net.airuima.dto.ExcelMetaColumnDTO;
import net.airuima.skill.domain.PedigreeStepSkill;
import net.airuima.skill.domain.Skill;
import net.airuima.skill.service.PedigreeStepSkillService;
import net.airuima.skill.web.rest.dto.PedigreeStepSkillDTO;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 产品谱系工艺路线工序技能配置Resource
 *
 * <AUTHOR>
 * @date 2022-08-08
 */
@Tag(name = "产品谱系工艺路线工序技能配置Resource")
@RestController
@RequestMapping("/api/pedigree-step-skills")
@AuthorityRegion("员工技能")
@FuncInterceptor("StaffSkillControl")
public class PedigreeStepSkillResource extends ProtectBaseResource<PedigreeStepSkill> {

    private final PedigreeStepSkillService pedigreeStepSkillService;

    public PedigreeStepSkillResource(PedigreeStepSkillService pedigreeStepSkillService) {
        this.pedigreeStepSkillService = pedigreeStepSkillService;
        this.mapUri = "/api/pedigree-step-skills";
    }

    /**
     * 通过产品谱系Id+工艺路线ID+工序ID获取技能列表
     * @param pedigreeId 产品谱系id
     * @param workflowId 工艺路线ID
     * @param stepId 工序ID
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(summary = "获取技能列表")
    @Parameters({
            @Parameter(name = "pedigreeId", description = "产品谱系ID", required = true),
            @Parameter(name = "workFlowId", description = "工艺路线ID", required = true),
            @Parameter(name = "stepId", description = "工序ID", required = true)
    })
    @GetMapping("/skills")
    public ResponseEntity<ResponseData<List<Skill>>> findSkill(@RequestParam(value = "pedigreeId") Long pedigreeId,
                                                               @RequestParam(value = "workFlowId") Long workflowId,
                                                               @RequestParam(value = "stepId") Long stepId){
        List<Skill> skillList = pedigreeStepSkillService.findSkill(pedigreeId, workflowId, stepId);
        return ResponseData.ok(skillList);
    }

    /**
     * 添加产品谱系工艺路线工序技能配置
     *
     * @param pedigreeStepSkillDTO 新增配置对象
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "添加产品谱系工艺路线工序技能配置")
    @PostMapping("/custom")
    public ResponseEntity<ResponseData<PedigreeStepSkillDTO>> saveConfig(@RequestBody PedigreeStepSkillDTO pedigreeStepSkillDTO){
        try {
            pedigreeStepSkillService.saveConfig(pedigreeStepSkillDTO);
            return ResponseData.ok(pedigreeStepSkillDTO);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 重写工序技能配置
     * @param file
     * @param data
     * @param suffix
     * @param metaColumn
     * @param response
     * @return org.springframework.http.ResponseEntity<java.lang.Void>
     * <AUTHOR>
     * @date 2024/3/13
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_IMPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Override
    public ResponseEntity<Void> importTableExcel(@RequestParam("file") MultipartFile file, @RequestParam("data") String data,
                                                 @RequestParam(value = "suffix", required = false) String suffix, @RequestParam(value = "metaColumn", required = false) String metaColumn,
                                                 HttpServletResponse response) throws Exception {

        //前端代码的元数据
        List<ExcelMetaColumnDTO> excelMetaColumnDTOList = StringUtils.isNotBlank(metaColumn) ? JSON.parseArray(metaColumn, ExcelMetaColumnDTO.class) : Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(excelMetaColumnDTOList)){
            excelMetaColumnDTOList.forEach(excelMetaColumnDTO -> {
                if(excelMetaColumnDTO.getName().equals("pedigree")){
                    excelMetaColumnDTO.setColumnValidate("{\"required\":true,\"mulCheckUnique\":[\"pedigree.id\",\"workFlow.id\",\"step.id\",\"skill.id\"]}");
                }
            });
        }
        return super.importTableExcel(file, data, suffix, JSON.toJSONString(excelMetaColumnDTOList), response);
    }


    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, "工序技能配置");
    }

}
