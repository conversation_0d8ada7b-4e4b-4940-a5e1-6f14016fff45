package net.airuima.calibrate.service.base;

import net.airuima.calibrate.domain.base.CalibrateItem;
import net.airuima.calibrate.repository.base.CalibrateItemRepository;
import net.airuima.constant.Constants;
import net.airuima.rbase.util.ToolUtils;
import net.airuima.service.CommonJpaService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 设备校准项目Service
 *
 * <AUTHOR>
 * @date 2022-07-04
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class CalibrateItemService extends CommonJpaService<CalibrateItem> {
    private final String qualifiedRangeRegex = "^(\\(|\\[)(-?\\d*\\.?\\d*?),\\s*(-?\\d*\\.?\\d*?)(\\)|\\])$|^(\\(|\\[)\\s*\\d*\\.?\\d*?\\s*,\\s*-?\\d*\\.?\\d*?\\s*(\\)|\\])$|^(\\(|\\[)(-?\\d*\\.?\\d*?\\s*,\\s*-?\\d*\\.?\\d*?|\\s*-?\\d*\\.?\\d*?,\\s*-?\\d*\\.?\\d*?)\\s*(\\)|\\])$|^OK$";

    private final CalibrateItemRepository calibrateItemRepository;

    public CalibrateItemService(CalibrateItemRepository calibrateItemRepository) {
        this.calibrateItemRepository = calibrateItemRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CalibrateItem> find(Specification<CalibrateItem> spec, Pageable pageable) {
        return calibrateItemRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<CalibrateItem> find(Specification<CalibrateItem> spec) {
        return calibrateItemRepository.findAll(spec);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CalibrateItem> findAll(Pageable pageable) {
        return calibrateItemRepository.findAll(pageable);
    }

    /**
     * 根据项目名称查询校准项目
     *
     * @param name 项目名称
     * @param isEnable 是否启用
     * @param size 分页大小
     * @return  java.util.List<net.airuima.domain.base.calibrate.CalibrateItem> 校准项目集合
     * <AUTHOR>
     * @date 2022/7/6
     **/
    @Transactional(readOnly = true)
    public List<CalibrateItem> findByName(String name,Boolean isEnable, Integer size) {
        Page<CalibrateItem> page = calibrateItemRepository.findByNameLike(name, PageRequest.of(Constants.INT_ZERO, size));
        return Optional.ofNullable(page).map(Slice::getContent).orElse(null);
    }

    @Override
    public String validateBeforeSave(CalibrateItem entity) {
        if( StringUtils.isBlank(entity.getQualifiedRange())){
            return  "合格范围为空";
        }
        if(!entity.getQualifiedRange().matches(qualifiedRangeRegex)){
            return "合格范围不合法";
        }
        if(!entity.getQualifiedRange().equals(Constants.OK) && (entity.getQualifiedRange().contains("(")||entity.getQualifiedRange().contains("[")) && !ToolUtils.validateQualifiedRange(entity.getQualifiedRange())){
            return "合格范围不合法";
        }
        return super.validateBeforeSave(entity);
    }

    /**
     * 通过项目Id 修改启用禁用 校准项目
     *
     * @param id     项目Id
     * @param enable 启用禁用
     * <AUTHOR>
     * @date 2022/7/6
     **/
    public void updateEnable(Long id, Boolean enable) {
        calibrateItemRepository.updateEnableById(enable, id);
    }

    /**
     * 通过规则ID删除校准规则
     *
     * @param id 规则ID
     * <AUTHOR>
     * @date 2022/7/22
     **/
    public void delete(Long id) {
        calibrateItemRepository.deleteById(id);
    }
}
