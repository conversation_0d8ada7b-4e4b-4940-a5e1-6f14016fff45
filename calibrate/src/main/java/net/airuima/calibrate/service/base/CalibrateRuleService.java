package net.airuima.calibrate.service.base;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import com.google.common.collect.Lists;
import jakarta.persistence.EntityManager;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.calibrate.domain.base.CalibrateItem;
import net.airuima.calibrate.domain.base.CalibrateRule;
import net.airuima.calibrate.domain.base.CalibrateRuleDetail;
import net.airuima.calibrate.repository.base.CalibrateItemRepository;
import net.airuima.calibrate.repository.base.CalibrateRuleDetailRepository;
import net.airuima.calibrate.repository.base.CalibrateRuleRepository;
import net.airuima.calibrate.web.rest.base.dto.CalibrateRuleGetDTO;
import net.airuima.calibrate.web.rest.base.dto.CalibrateRuleImportDTO;
import net.airuima.calibrate.web.rest.base.dto.CalibrateRuleSaveDTO;
import net.airuima.dto.AbstractDto;
import net.airuima.dto.ExportDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.CalibrateRuleEnum;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.DateUnitEnum;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.dto.rfms.FacilityDTO;
import net.airuima.rbase.proxy.rfms.RbaseFacilityProxy;
import net.airuima.rbase.repository.base.scene.WorkCellRepository;
import net.airuima.rbase.util.ToolUtils;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 校准规则表Service
 *
 * <AUTHOR>
 * @date 2022-07-04
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class CalibrateRuleService extends CommonJpaService<CalibrateRule> {

    private static final String CALIBRATE_ITEM_REPEAT = "CalibrateItemRepeat";
    private final String CALIBRATE_RULE_ENTITY_GRAPH = "calibrateRuleEntityGraph";
    @Autowired
    private CalibrateRuleRepository calibrateRuleRepository;
    @Autowired
    private CalibrateRuleDetailRepository calibrateRuleDetailRepository;
    @Autowired
    private EntityManager em;
    @Autowired
    private WorkCellRepository workCellRepository;
    @Autowired
    private CalibrateItemRepository calibrateItemRepository;
    @Autowired
    private RbaseFacilityProxy rbaseFacilityProxy;

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<CalibrateRule> find(Specification<CalibrateRule> spec, Pageable pageable) {
        return calibrateRuleRepository.findAll(spec, pageable,new NamedEntityGraph(CALIBRATE_RULE_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<CalibrateRule> find(Specification<CalibrateRule> spec) {
        return calibrateRuleRepository.findAll(spec,new NamedEntityGraph(CALIBRATE_RULE_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<CalibrateRule> findAll(Pageable pageable) {
        return calibrateRuleRepository.findAll(pageable,new NamedEntityGraph(CALIBRATE_RULE_ENTITY_GRAPH));
    }

    /**
     * 新增检测规则
     *
     * @param saveDto 检测规则DTO
     * <AUTHOR>
     * @date 2022/7/5
     **/
    public void create(CalibrateRuleSaveDTO saveDto) throws Exception {
        List<Long> facilityIdList = saveDto.getFacilityIdList();
        List<CalibrateRule> calibrateRuleList = new ArrayList<>();
        List<CalibrateRuleDetail> calibrateRuleDetailList = new ArrayList<>();
        //1. 组装数据
        if (!CollectionUtils.isEmpty(facilityIdList)) {
            //有关联设备
            for (Long facilityId : facilityIdList) {
                //主表
                CalibrateRule calibrateRule = baseCalibrateRule(saveDto);
                calibrateRule.setFacilityId(facilityId);
                calibrateRuleList.add(calibrateRule);
                //详情表
                List<CalibrateRuleDetail> calibrateRuleDetailTemp = baseCalibrateRuleDetail(saveDto);
                calibrateRuleDetailTemp.forEach(i -> i.setCalibrateRule(calibrateRule));
                calibrateRuleDetailList.addAll(calibrateRuleDetailTemp);
            }
        } else {
            //无关联设备
            //主表
            CalibrateRule calibrateRule = baseCalibrateRule(saveDto);
            calibrateRuleList.add(calibrateRule);
            //详情表
            List<CalibrateRuleDetail> calibrateRuleDetailTemp = baseCalibrateRuleDetail(saveDto);
            calibrateRuleDetailTemp.forEach(i -> i.setCalibrateRule(calibrateRule));
            calibrateRuleDetailList.addAll(calibrateRuleDetailTemp);
        }
        //2. 参数校验
        validation(saveDto, calibrateRuleList);
        //3. 保存数据
        calibrateRuleRepository.saveAll(calibrateRuleList);
        saveBatch(calibrateRuleDetailList);
    }

    /**
     * 新增规则时，进行重复性校验
     *
     * @param saveDto           用户请求的DTO
     * @param calibrateRuleList 新增规则集合
     * <AUTHOR>
     * @date 2022/7/11
     **/
    public void validation(CalibrateRuleSaveDTO saveDto, List<CalibrateRule> calibrateRuleList) throws Exception {
        //提前提醒周期不可大于校准周期
        if ((saveDto.getPeriodUnit() < saveDto.getRemindPeriodUnit()) || (saveDto.getPeriodUnit().equals(saveDto.getRemindPeriodUnit()) && saveDto.getPeriodNumber() < saveDto.getRemindPeriodNumber())) {
            throw new ResponseException("error.PeriodError", "提前提醒周期不可大于校准周期");
        }
        List<Long> facilityIdList = calibrateRuleList.stream().map(CalibrateRule::getFacilityId).collect(Collectors.toList());
        // 校准项目不可重复
        List<CalibrateRuleSaveDTO.CalibrateItemDTO> calibrateItemList = saveDto.getCalibrateItemList();
        long itemIdCount = calibrateItemList.stream().map(CalibrateRuleSaveDTO.CalibrateItemDTO::getCalibrateItemId).distinct().count();
        if (itemIdCount < calibrateItemList.size()) {
            throw new ResponseException(CALIBRATE_ITEM_REPEAT, "项目不能重复添加");
        }
        // 查询主表(DB)
        List<CalibrateRule> calibrateRuleListDB = null;
        if (!CollectionUtils.isEmpty(saveDto.getFacilityIdList())) {
            calibrateRuleListDB = calibrateRuleRepository.findByWorkCellIdAndFacilityIdInAndProcessAndDeleted(saveDto.getWorkCellId(), facilityIdList, saveDto.getProcess(), Constants.LONG_ZERO);
        } else {
            calibrateRuleListDB = calibrateRuleRepository.findByWorkCellIdAndFacilityIdIsNullAndProcessAndDeleted(saveDto.getWorkCellId(), saveDto.getProcess(), Constants.LONG_ZERO);
        }
        //如果主表存在，则直接报错
        if (!CollectionUtils.isEmpty(calibrateRuleListDB)) {
            String msg = "工位名称：" + calibrateRuleListDB.get(0).getWorkCell().getName() + ", 设备名称：" + calibrateRuleListDB.get(0).getFacilityDto().getName();
            throw new ResponseException("error.CalibrateRuleIsExist", "存在重复记录，" + msg);
        }
    }

    /**
     * 返回无设备状态下校验规则实体
     *
     * @param saveDto 校准规则表DTO
     * @return : CalibrateRule
     * <AUTHOR>
     * @date 2022/7/5
     **/
    public CalibrateRule baseCalibrateRule(CalibrateRuleSaveDTO saveDto) {
        CalibrateRule calibrateRuleTemp = new CalibrateRule();
        BeanUtils.copyProperties(saveDto, calibrateRuleTemp);

        WorkCell workCellTemp = new WorkCell();
        workCellTemp.setId(saveDto.getWorkCellId());
        calibrateRuleTemp.setWorkCell(workCellTemp);

        return calibrateRuleTemp;
    }

    /**
     * 返回基础规则详情
     *
     * @param saveDto 校准规则表DTO
     * @return : List<CalibrateRuleDetail>
     * <AUTHOR>
     * @date 2022/7/5
     **/
    public List<CalibrateRuleDetail> baseCalibrateRuleDetail(CalibrateRuleSaveDTO saveDto) {
        List<CalibrateRuleDetail> calibrateRuleDetailList = new ArrayList<CalibrateRuleDetail>();

        for (CalibrateRuleSaveDTO.CalibrateItemDTO calibrateItemDTO : saveDto.getCalibrateItemList()) {
            CalibrateRuleDetail calibrateRuleDetail = new CalibrateRuleDetail();
            CalibrateItem calibrateItem = new CalibrateItem();
            calibrateItem.setId(calibrateItemDTO.getCalibrateItemId());
            calibrateRuleDetail.setCalibrateItem(calibrateItem);
            calibrateRuleDetail.setTestTimes(calibrateItemDTO.getTestTimes());

            calibrateRuleDetailList.add(calibrateRuleDetail);
        }
        return calibrateRuleDetailList;
    }

    /**
     * 分批插入
     *
     * @param calibrateRuleDetailList
     * <AUTHOR>
     * @date 2022/7/5
     **/
    public void saveBatch(List<CalibrateRuleDetail> calibrateRuleDetailList) {
        Iterator<CalibrateRuleDetail> iterator = calibrateRuleDetailList.iterator();
        int index = 0;
        while (iterator.hasNext()) {
            em.merge(iterator.next());
            index++;
            if (index % 20 == 0) {
                em.flush();
                em.clear();
            }
        }
        if (index % 20 != 0) {
            em.flush();
            em.clear();
        }
    }

    /**
     * 通过工位ID+内校/外校，查询关联设备列表及规则列表
     *
     * @param workCellId 工位ID
     * @param process    校准程序，0：内校，1：外校
     * @return : ResponseEntity<CalibrateRule>
     * <AUTHOR>
     * @date 2022/7/6
     **/
    @Transactional(readOnly = true)
    public List<CalibrateRuleGetDTO> findByCellIdAndProcess(Long workCellId, Integer process) {
        List<CalibrateRule> calibrateRuleList = calibrateRuleRepository.findByWorkCellIdAndProcessAndDeleted(workCellId, process, Constants.LONG_ZERO);
        //1. 获取规则列表
        List<CalibrateRuleGetDTO.CalibrateRuleDTO> calibrateRuleDtoList = new ArrayList<CalibrateRuleGetDTO.CalibrateRuleDTO>();
        for (CalibrateRule calibrateRule : calibrateRuleList) {
            List<CalibrateRuleDetail> calibrateRuleDetailList = calibrateRuleDetailRepository.findByCalibrateRuleIdInAndCalibrateItemEnableAndDeleted(Collections.singletonList(calibrateRule.getId()), Constants.LONG_ZERO);
            List<CalibrateRuleGetDTO.CalibrateRuleDTO> calibrateRuleDTOList = calibrateRuleDetailList.stream().map(j -> {
                CalibrateRuleGetDTO.CalibrateRuleDTO calibrateRuleDTO = new CalibrateRuleGetDTO.CalibrateRuleDTO();
                BeanUtils.copyProperties(j.getCalibrateItem(), calibrateRuleDTO);
                calibrateRuleDTO.setId(j.getId()).setWorkCellId(calibrateRule.getWorkCell().getId()).setFacilityId(calibrateRule.getFacilityDto().getId())
                        .setFacilityName(calibrateRule.getFacilityDto().getName()).setTestTimes(j.getTestTimes()).setFacilityCode(calibrateRule.getFacilityDto().getCode());
                return calibrateRuleDTO;
            }).toList();
            calibrateRuleDtoList.addAll(calibrateRuleDTOList);
        }
        //2. 获取校准设备集合
        List<FacilityDTO> facilityDtoList = calibrateRuleList.stream().map(CalibrateRule::getFacilityDto).filter(i -> !ObjectUtils.isEmpty(i.getId()))
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(FacilityDTO::getId))), ArrayList::new));
        Map<Long, FacilityDTO> facilityDTOMap = facilityDtoList.stream().collect(Collectors.toMap(AbstractDto::getId, item -> item));
        //3. 组装返回数据
        List<CalibrateRuleGetDTO> calibrateRuleGetDTOList = new ArrayList<CalibrateRuleGetDTO>();
        //有设备校验
        Map<Long, List<CalibrateRuleGetDTO.CalibrateRuleDTO>> orderMap = calibrateRuleDtoList.stream().filter(i -> !ObjectUtils.isEmpty(i.getFacilityId())).collect(Collectors.groupingBy(CalibrateRuleGetDTO.CalibrateRuleDTO::getFacilityId));
        Set<Map.Entry<Long, List<CalibrateRuleGetDTO.CalibrateRuleDTO>>> entries = orderMap.entrySet();
        for (Map.Entry<Long, List<CalibrateRuleGetDTO.CalibrateRuleDTO>> entry : entries) {
            List<CalibrateRuleGetDTO.CalibrateRuleDTO> value = entry.getValue();
            FacilityDTO facilityDTO = facilityDTOMap.get(entry.getKey());

            calibrateRuleGetDTOList.add(new CalibrateRuleGetDTO(facilityDTO, value));
        }
        //仅工位校验
        List<CalibrateRuleGetDTO.CalibrateRuleDTO> orderListNull = calibrateRuleDtoList.stream().filter(i -> ObjectUtils.isEmpty(i.getFacilityId())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(orderListNull)) {
            calibrateRuleGetDTOList.add(new CalibrateRuleGetDTO(null, orderListNull));
        }

        return calibrateRuleGetDTOList;
    }


    /**
     * 更新校准规则
     *
     * @param entity 校准规则实体
     * <AUTHOR>
     * @date 2022-07-11
     **/
    public void updateEntity(CalibrateRule entity) {
        CalibrateRule calibrateRuleDB = calibrateRuleRepository.findById(entity.getId()).orElseThrow(()-> new ResponseException("error.calibrateRuleNotExist", "校准规则不存在"));

        CalibrateRule calibrateRule = null;
        if (!ObjectUtils.isEmpty(calibrateRuleDB.getFacilityId())) {
            calibrateRule = calibrateRuleRepository.findByWorkCellIdAndFacilityIdAndProcessAndDeletedAndIdNot(calibrateRuleDB.getWorkCell().getId(), calibrateRuleDB.getFacilityId(), entity.getProcess(), Constants.LONG_ZERO, entity.getId());
        } else {
            calibrateRule = calibrateRuleRepository.findByWorkCellIdAndFacilityIdIsNullAndProcessAndDeletedAndIdNot(calibrateRuleDB.getWorkCell().getId(), entity.getProcess(), Constants.LONG_ZERO, entity.getId());
        }
        if (!ObjectUtils.isEmpty(calibrateRule)) {
            throw new ResponseException("error.CalibrateRuleIsExist", "校准规则已存在");
        }
        BeanUtils.copyProperties(entity, calibrateRuleDB, ToolUtils.getNullPropertyNames(entity));
        this.update(calibrateRuleDB);
    }

    /**
     * 更新校准规则详情
     *
     * @param entity 校准规则实体
     * <AUTHOR>
     * @date 2022-07-11
     **/
    public void updateDetail(CalibrateRuleSaveDTO entity) {
        //参数校验
        validationUpdateDetail(entity);
        //1. 规则详情集合(参数)
        List<CalibrateRuleSaveDTO.CalibrateItemDTO> calibrateItemDTOList = entity.getCalibrateItemList();
        List<Long> itemDTOIdList = calibrateItemDTOList.stream().map(CalibrateRuleSaveDTO.CalibrateItemDTO::getCalibrateItemId).toList();
        //2. 规则详情集合(DB)
        List<CalibrateRuleDetail> calibrateRuleDetailList = calibrateRuleDetailRepository.findByCalibrateRuleIdInAndCalibrateItemEnableAndDeleted(Collections.singletonList(entity.getId()), Constants.LONG_ZERO);
        Map<Long, CalibrateRuleDetail> calibrateRuleDetailMap = calibrateRuleDetailList.stream().collect(Collectors.toMap(i -> i.getCalibrateItem().getId(), i -> i));
        List<Long> itemIdList = calibrateRuleDetailList.stream().map(i -> i.getCalibrateItem().getId()).toList();
        //3. 需删除
        List<Long> itemIdListDel = itemIdList.stream().filter(i -> !itemDTOIdList.contains(i)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(itemIdListDel)) {
            calibrateRuleDetailRepository.deleteByCalibrateRuleIdAndCalibrateItemIdInAndDeleted(entity.getId(), itemIdListDel);
        }
        //更新规则自身基础信息
        CalibrateRule calibrateRule = calibrateRuleRepository.getReferenceById(entity.getId());
        if(Objects.nonNull(entity.getProcess())){
            calibrateRule.setProcess(entity.getProcess());
        }
        if(Objects.nonNull(entity.getPeriodUnit())){
            calibrateRule.setPeriodUnit(entity.getPeriodUnit());
        }
        if(Objects.nonNull(entity.getPeriodNumber())){
            calibrateRule.setPeriodNumber(entity.getPeriodNumber());
        }
        if(Objects.nonNull(entity.getRemindPeriodUnit())){
            calibrateRule.setRemindPeriodUnit(entity.getRemindPeriodUnit());
        }
        if(Objects.nonNull(entity.getRemindPeriodNumber())){
            calibrateRule.setRemindPeriodNumber(entity.getRemindPeriodNumber());
        }
        calibrateRuleRepository.save(calibrateRule);
        //4. 需新增/更新
        List<CalibrateRuleDetail> calibrateRuleDetailListAll = new ArrayList<CalibrateRuleDetail>();
        //排除已删除集合
        List<CalibrateRuleSaveDTO.CalibrateItemDTO> itemDTOListAddOrUpdate = calibrateItemDTOList.stream().filter(i -> !itemIdListDel.contains(i.getCalibrateItemId())).toList();
        for (CalibrateRuleSaveDTO.CalibrateItemDTO calibrateItemDTO : itemDTOListAddOrUpdate) {
            CalibrateRuleDetail calibrateRuleDetail = calibrateRuleDetailMap.get(calibrateItemDTO.getCalibrateItemId());
            if (!ObjectUtils.isEmpty(calibrateRuleDetail)) {
                // 如果在规则详情集合(DB)中存在，则为更新
                calibrateRuleDetail.setTestTimes(calibrateItemDTO.getTestTimes());
            } else {
                // 如果在规则详情集合(DB)中不存在，则为新增
                CalibrateItem calibrateItem = new CalibrateItem();
                calibrateItem.setId(calibrateItemDTO.getCalibrateItemId());
                calibrateRuleDetail = new CalibrateRuleDetail().setCalibrateRule(calibrateRule).setCalibrateItem(calibrateItem).setTestTimes(calibrateItemDTO.getTestTimes());
            }
            calibrateRuleDetailListAll.add(calibrateRuleDetail);
        }
        calibrateRuleDetailRepository.saveAll(calibrateRuleDetailListAll);
    }

    /**
     * 参数校验（规则-项目更新）
     *
     * @param entity 校准规则项目集合DTO
     * <AUTHOR>
     * @date 2022/7/25
     **/
    private void validationUpdateDetail(CalibrateRuleSaveDTO entity) {
        List<CalibrateRuleSaveDTO.CalibrateItemDTO> calibrateItemList = entity.getCalibrateItemList();
        long itemIdCount = calibrateItemList.stream().map(CalibrateRuleSaveDTO.CalibrateItemDTO::getCalibrateItemId).distinct().count();
        if (itemIdCount < calibrateItemList.size()) {
            throw new ResponseException(CALIBRATE_ITEM_REPEAT, "项目不能重复添加");
        }
    }

    /**
     * 删除校准规则
     *
     * @param id 校准规则id
     */
    public void deleteEntity(Long id) {
        this.deleteById(id);
        calibrateRuleDetailRepository.deleteByCalibrateRuleIdAndDeleted(id);
    }

    /**
     * 校准规则导入
      * @param file 导入文件
     * <AUTHOR>
     * @date  2023/4/20
     */
    public void importTableExcel(MultipartFile file) throws Exception{
        ImportParams importParams = new ImportParams();
        importParams.setTitleRows(0);
        importParams.setHeadRows(2);

        List<CalibrateRuleImportDTO> calibrateRuleImportDtoList = ExcelImportUtil.importExcel(file.getInputStream(), CalibrateRuleImportDTO.class,importParams);
        //不存在数据直接结束
        if (!ValidateUtils.isValid(calibrateRuleImportDtoList)){
            return;
        }
        //验证基础数据的合法性
        validBaseImportData(calibrateRuleImportDtoList);
        //工位
        HashMap<String, WorkCell> workCellMap = new HashMap<>();
        //设备
        HashMap<String, FacilityDTO> facilityMap = new HashMap<>();
        //校准项目
        HashMap<String, CalibrateItem> calibrateItemMap = new HashMap<>();
        //通过查询获取对象信息是否存在
        validFindImportData(calibrateRuleImportDtoList,workCellMap,facilityMap,calibrateItemMap);
        calibrateRuleImportDtoList.forEach(calibrateRuleImportDto -> {
            WorkCell workCell = workCellMap.get(calibrateRuleImportDto.getWorkCellCode());
            FacilityDTO facilityDto = ObjectUtils.isEmpty(calibrateRuleImportDto.getFacilityCode())?null:facilityMap.get(calibrateRuleImportDto.getFacilityCode());
            //校准程序类型
            int process = CalibrateRuleEnum.getType(calibrateRuleImportDto.getProcess()).getStatus();
            //获取详情
            CalibrateRule calibrateRule = null;
            if (!ObjectUtils.isEmpty(facilityDto)) {
                calibrateRule = calibrateRuleRepository.findByWorkCellIdAndFacilityIdAndProcessAndDeleted(workCell.getId(), facilityDto.getId(), process, Constants.LONG_ZERO)
                        .orElse(new CalibrateRule(workCell,facilityDto.getId(),process,calibrateRuleImportDto));
            } else {
                List<CalibrateRule> calibrateRules = calibrateRuleRepository.findByWorkCellIdAndFacilityIdIsNullAndProcessAndDeleted(workCell.getId(), process, Constants.LONG_ZERO);
                if (ValidateUtils.isValid(calibrateRules)){
                    calibrateRule = calibrateRules.get(Constants.INT_ZERO);
                }else {
                    calibrateRule = new CalibrateRule(workCell,null,process,calibrateRuleImportDto);
                }
            }
            //变更周期与时间
            calibrateRule.setPeriodNumber(calibrateRuleImportDto.getPeriodNumber()).setPeriodUnit(DateUnitEnum.getType(calibrateRuleImportDto.getPeriodUnit()).getStatus())
                    .setRemindPeriodNumber(calibrateRuleImportDto.getRemindPeriodNumber()).setRemindPeriodUnit(DateUnitEnum.getType(calibrateRuleImportDto.getRemindPeriodUnit()).getStatus());
            if (!ObjectUtils.isEmpty(calibrateRule.getId())){
                calibrateRuleDetailRepository.deleteByCalibrateRuleIdAndDeleted(calibrateRule.getId());
            }
            //添加校准规则详情
            CalibrateRule finalCalibrateRule = calibrateRuleRepository.save(calibrateRule);
            List<CalibrateRuleDetail> calibrateRuleDetails = Lists.newArrayList();
            calibrateRuleImportDto.getCalibrateItemInfos().forEach(calibrateItemInfo -> {
                CalibrateRuleDetail calibrateRuleDetail = new CalibrateRuleDetail();
                calibrateRuleDetail.setCalibrateRule(finalCalibrateRule).setCalibrateItem(calibrateItemMap.get(calibrateItemInfo.getCalibrateItemCode()))
                        .setTestTimes(calibrateItemInfo.getCalibrateNumber());
                calibrateRuleDetails.add(calibrateRuleDetail);
            });
            calibrateRuleDetailRepository.saveAll(calibrateRuleDetails);
        });

    }

    /**
     * 验证需要查询数据并添加
     * @param calibrateRuleImportDtoList 导入数据
     * @param workCellMap 工位map
     * @param facilityMap 设备map
     * @param calibrateItemMap  校准项目map
     */
    private void validFindImportData(List<CalibrateRuleImportDTO> calibrateRuleImportDtoList, HashMap<String, WorkCell> workCellMap, HashMap<String, FacilityDTO> facilityMap, HashMap<String, CalibrateItem> calibrateItemMap) {
        //工位 验证及获取
        List<String> workCellCodes = calibrateRuleImportDtoList.stream().map(CalibrateRuleImportDTO::getWorkCellCode).distinct().collect(Collectors.toList());
        List<WorkCell> workCells = workCellRepository.findByCodeInAndDeleted(workCellCodes, Constants.LONG_ZERO);

        if (!ValidateUtils.isValid(workCells)){
            throw new ResponseException("error.workCellIsNotExist", "工位信息缺失");
        }
        if (ValidateUtils.isValid(workCells) && workCells.size() != workCellCodes.size()){
            workCellCodes = workCellCodes.stream().filter(workCellCode -> workCells.stream().noneMatch(workCell -> workCell.getCode().equals(workCellCode))).collect(Collectors.toList());
            throw new ResponseException("error.workCellIsNotExist", workCellCodes+"工位编码不存在");
        }
        workCells.forEach(workCell ->{
            if(!workCell.getIsEnable()){
                throw new ResponseException("error.workCellDisabled", "工位["+workCell.getCode()+"]已禁用");
            }
            workCellMap.put(workCell.getCode(),workCell);
        });

        //设备 验证及获取
        List<String> facilityCodes = calibrateRuleImportDtoList.stream().map(CalibrateRuleImportDTO::getFacilityCode).filter(facilityCode -> !ObjectUtils.isEmpty(facilityCode)).distinct().collect(Collectors.toList());
        if (ValidateUtils.isValid(facilityCodes)){

            List<FacilityDTO> facilityDtos = rbaseFacilityProxy.findByCodeInAndIsEnableAndDeleted(facilityCodes,Boolean.TRUE,Constants.LONG_ZERO);
            if (ValidateUtils.isValid(facilityDtos) && facilityDtos.size() != facilityCodes.size()){
                facilityCodes = facilityCodes.stream().filter(facilityCode -> facilityDtos.stream().noneMatch(facility -> facility.getCode().equals(facilityCode))).collect(Collectors.toList());
                throw new ResponseException("error.facilityIsNotExist", facilityCodes+"设备编码不存在");
            }
            facilityDtos.forEach(facilityDto -> {
                if(!facilityDto.getIsEnable()){
                    throw new ResponseException("error.facilityDisabled", "设备["+facilityDto.getCode()+"]已禁用");
                }
                facilityMap.put(facilityDto.getCode(),facilityDto);
            });

        }
        //校准项目 验证及获取
        List<List<CalibrateRuleImportDTO.CalibrateItemInfo>> collects = calibrateRuleImportDtoList.stream().map(CalibrateRuleImportDTO::getCalibrateItemInfos).toList();
        List<CalibrateRuleImportDTO.CalibrateItemInfo> calibrateItemInfos = Lists.newArrayList();
        collects.forEach(calibrateItemInfos::addAll);
        List<String> calibrateItemCodes = calibrateItemInfos.stream().map(CalibrateRuleImportDTO.CalibrateItemInfo::getCalibrateItemCode).distinct().collect(Collectors.toList());
        List<CalibrateItem> calibrateItems = calibrateItemRepository.findByCodeInAndEnableAndDeleted(calibrateItemCodes, Boolean.TRUE, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(calibrateItems)){
            throw new ResponseException("error.calibrateItemIsNotExist", "校准项目缺失");
        }
        if (ValidateUtils.isValid(calibrateItems) && calibrateItemCodes.size() != calibrateItems.size()){
            calibrateItemCodes = calibrateItemCodes.stream().filter(calibrateItemCode -> calibrateItems.stream().noneMatch(calibrateItem -> calibrateItem.getCode().equals(calibrateItemCode))).collect(Collectors.toList());
            throw new ResponseException("error.calibrateItemIsNotExist", calibrateItemCodes+"校准项目编码不存在");
        }
        calibrateItems.forEach(calibrateItem -> {
            if(!calibrateItem.getEnable()){
                throw new ResponseException("error.calibrateItemDisabled", "校准项目["+calibrateItem.getCode()+"]已禁用");
            }
            calibrateItemMap.put(calibrateItem.getCode(),calibrateItem);
        });
    }

    /**
     * 验证导入校准规则
     * @param calibrateRuleImportDtoList  导入校准规则参数
     * <AUTHOR>
     * @date  2023/4/19
     */
    public void validBaseImportData(List<CalibrateRuleImportDTO> calibrateRuleImportDtoList){

        //验证校准程序内容是否为：内校/外校
        if (!calibrateRuleImportDtoList.stream().allMatch(calibrateRuleImportDto -> null != CalibrateRuleEnum.getType(calibrateRuleImportDto.getProcess()))){
            throw new ResponseException("error.calibrateProcessIsError", "校准程序类型不符合规则");
        }
        //验证效准单位：年-月-日
        if(!calibrateRuleImportDtoList.stream().allMatch(calibrateRuleImportDto -> null != DateUnitEnum.getType(calibrateRuleImportDto.getPeriodUnit()) && null != DateUnitEnum.getType(calibrateRuleImportDto.getRemindPeriodUnit())
        && calibrateRuleImportDto.getPeriodNumber() != null && calibrateRuleImportDto.getRemindPeriodNumber() != null)){
            throw new ResponseException("error.dateUnitError", "周期单位不符合规则");
        }
        //提前提醒周期不可大于校准周期
        if (calibrateRuleImportDtoList.stream().anyMatch(calibrateRuleImportDto -> (DateUnitEnum.getType(calibrateRuleImportDto.getPeriodUnit()).getStatus() < DateUnitEnum.getType(calibrateRuleImportDto.getRemindPeriodUnit()).getStatus())
        || (DateUnitEnum.getType(calibrateRuleImportDto.getPeriodUnit()).getStatus() == DateUnitEnum.getType(calibrateRuleImportDto.getRemindPeriodUnit()).getStatus() && calibrateRuleImportDto.getPeriodNumber() < calibrateRuleImportDto.getRemindPeriodNumber()))){
            throw new ResponseException("error.PeriodError", "提前提醒周期不可大于校准周期");
        }
        //是否存在校准项目
        if(!calibrateRuleImportDtoList.stream().allMatch(calibrateRuleImportDto -> ValidateUtils.isValid(calibrateRuleImportDto.getCalibrateItemInfos()))){
            throw new ResponseException("error.calibrateItemCodeIsExist", "校准项目不存在");
        }
        //效准项目是否重复
        if (calibrateRuleImportDtoList.stream().anyMatch(calibrateRuleImportDto -> (calibrateRuleImportDto.getCalibrateItemInfos().size() != calibrateRuleImportDto.getCalibrateItemInfos().stream().distinct().collect(Collectors.toList()).size()) || calibrateRuleImportDto.getCalibrateItemInfos().stream().anyMatch(calibrateItemInfo -> calibrateItemInfo.getCalibrateNumber() == null))){
            throw new ResponseException(CALIBRATE_ITEM_REPEAT, "规则中校准项目重复或者校准次数不存在");
        }
    }

    /**
     * 校准规则导出
     * @param calibrateRules 导出规则
     * @param excelTitle 导出excel标题
     * @param response 响应
     */
    public void exportTableExcel(List<CalibrateRule> calibrateRules, ExportDTO exportDTO, HttpServletResponse response) throws Exception{
        List<CalibrateRuleImportDTO> calibrateRuleImports = Lists.newArrayList();
        if(!exportDTO.getExportTemplate()) {
            //根据校准规则id,获取详情
            List<CalibrateRuleDetail> calibrateRuleDetails = calibrateRuleDetailRepository.findByCalibrateRuleIdInAndCalibrateItemEnableAndDeleted(calibrateRules.stream().map(CalibrateRule::getId).collect(Collectors.toList()), net.airuima.constant.Constants.LONG_ZERO);
            //构建导出数据
            calibrateRules.forEach(calibrateRule -> {
                CalibrateRuleImportDTO calibrateRuleImportDto = new CalibrateRuleImportDTO(calibrateRule);
                if (net.airuima.util.ValidateUtils.isValid(calibrateRuleDetails)) {
                    List<CalibrateRuleImportDTO.CalibrateItemInfo> calibrateItemInfos = Lists.newArrayList();
                    calibrateRuleDetails.stream().filter(calibrateRuleDetail -> calibrateRuleDetail.getCalibrateRule().getId().equals(calibrateRule.getId()))
                            .forEach(calibrateRuleDetail -> {
                                CalibrateRuleImportDTO.CalibrateItemInfo calibrateItemInfo = new CalibrateRuleImportDTO.CalibrateItemInfo();
                                calibrateItemInfo.setCalibrateItemCode(calibrateRuleDetail.getCalibrateItem().getCode()).setCalibrateNumber(calibrateRuleDetail.getTestTimes());
                                calibrateItemInfos.add(calibrateItemInfo);
                            });
                    calibrateRuleImportDto.setCalibrateItemInfos(calibrateItemInfos);
                }
                calibrateRuleImports.add(calibrateRuleImportDto);
            });
        }
        //组合对应的导出参数
        ExportParams exportParams = new ExportParams(null, exportDTO.getExcelTitle(), ExcelType.XSSF);
        if(StringUtils.isNotBlank(exportDTO.getExcelType()) && exportDTO.getExcelType().equals("xls")){
            exportParams = new ExportParams(null, exportDTO.getExcelTitle(), ExcelType.HSSF);
        }
        String prefix = StringUtils.isNotBlank(exportDTO.getExcelType()) && exportDTO.getExcelType().equals("xls")?".xls":".xlsx";
        //写入excel
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), CalibrateRuleImportDTO.class, calibrateRuleImports);
        String fileName = URLEncoder.encode(exportDTO.getExcelTitle() + prefix, StandardCharsets.UTF_8);
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);
        response.setHeader("message", "export!");
        ServletOutputStream out = response.getOutputStream();
        workbook.write(out);
        workbook.close();
        out.close();
    }
}
