package net.airuima.calibrate.service.procedure;

import com.alibaba.fastjson.JSON;
import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.calibrate.domain.base.CalibrateRule;
import net.airuima.calibrate.domain.procedure.CalibrateCheckResult;
import net.airuima.calibrate.domain.procedure.CalibrateStatus;
import net.airuima.calibrate.repository.base.CalibrateRuleRepository;
import net.airuima.calibrate.repository.procedure.CalibrateStatusRepository;
import net.airuima.dto.UserDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.dto.message.TaskFeignDTO;
import net.airuima.rbase.dto.rabbitmq.DelayedMessageDTO;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.integrate.message.ITaskMessageService;
import net.airuima.rbase.proxy.organization.RbaseRbacProxy;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.rbase.rabbitmq.RmesRabbitMqSender;
import net.airuima.rbase.service.base.serialnumber.ISerialNumberGenerate;
import net.airuima.rbase.util.DateUtils;
import net.airuima.service.CommonJpaService;
import net.airuima.util.ResponseException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 设备校准状态表Service
 *
 * <AUTHOR>
 * @date 2022-07-04
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class CalibrateStatusService extends CommonJpaService<CalibrateStatus> {

    private static final String CALIBRATE_RULE_NOT_EXIST = "CalibrateRuleNotExist";
    private static final String CALIBRATE_RULE_NOT_EXIST_MSG = "校准规则不存在";
    private final String CALIBRATE_STATUS_ENTITY_GRAPH = "calibrateStatusEntityGraph";
    private final CalibrateStatusRepository calibrateStatusRepository;
    private final CalibrateRuleRepository calibrateRuleRepository;
    private final RmesRabbitMqSender rmesRabbitMqSender;
    @Autowired
    private RbaseRbacProxy rbaseRbacProxy;
    @Autowired
    private ISerialNumberGenerate[] serialNumberGenerate;
    @Autowired
    private ITaskMessageService[] taskMessageServices;
    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;

    public CalibrateStatusService(CalibrateStatusRepository calibrateStatusRepository, CalibrateRuleRepository calibrateRuleRepository, RmesRabbitMqSender rmesRabbitMqSender) {
        this.calibrateStatusRepository = calibrateStatusRepository;
        this.calibrateRuleRepository = calibrateRuleRepository;
        this.rmesRabbitMqSender = rmesRabbitMqSender;
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<CalibrateStatus> find(Specification<CalibrateStatus> spec, Pageable pageable) {
        return calibrateStatusRepository.findAll(spec, pageable,new NamedEntityGraph(CALIBRATE_STATUS_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public List<CalibrateStatus> find(Specification<CalibrateStatus> spec) {
        return calibrateStatusRepository.findAll(spec,new NamedEntityGraph(CALIBRATE_STATUS_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    @FetchMethod
    public Page<CalibrateStatus> findAll(Pageable pageable) {
        return calibrateStatusRepository.findAll(pageable,new NamedEntityGraph(CALIBRATE_STATUS_ENTITY_GRAPH));
    }

    /**
     * 保存校准设备状态
     *
     * @param calibrateCheckResultList 校准数据集合
     * <AUTHOR>
     * @date 2022/7/12
     **/
    public void saveStatus(List<CalibrateCheckResult> calibrateCheckResultList) {
        CalibrateCheckResult calibrateCheckResultByZero = calibrateCheckResultList.get(Constants.INT_ZERO);
        //1. 获取工位或设备校准状态 及 校准规则
        List<CalibrateStatus> calibrateStatusList = new ArrayList<CalibrateStatus>();
        List<CalibrateRule> calibrateRuleList = new ArrayList<CalibrateRule>();
        getCalibrateStatusAndRule(calibrateCheckResultList, calibrateStatusList, calibrateRuleList);

        Map<String, CalibrateStatus> calibrateStatusMap = calibrateStatusList.stream().collect(Collectors.toMap(item -> item.getWorkCell().getId() + Constants.UNDERLINE + (ObjectUtils.isEmpty(item.getFacilityId()) ? "" : item.getFacilityId()), item -> item));
        calibrateRuleList = calibrateRuleList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<CalibrateRule>(Comparator.comparing(item -> item.getWorkCell().getId() + Constants.UNDERLINE + (ObjectUtils.isEmpty(item.getFacilityId()) ? "" : item.getFacilityId())))), ArrayList::new));
        Map<String, CalibrateRule> calibrateRuleMap = calibrateRuleList.stream().collect(Collectors.toMap(item -> item.getWorkCell().getId() + Constants.UNDERLINE + (ObjectUtils.isEmpty(item.getFacilityId()) ? "" : item.getFacilityId()), item -> item));
        //2. 组装状态数据
        for (CalibrateCheckResult calibrateCheckResult : calibrateCheckResultList) {
            String key = calibrateCheckResult.getWorkCell().getId() + Constants.UNDERLINE + (ObjectUtils.isEmpty(calibrateCheckResult.getFacilityId()) ? "" : calibrateCheckResult.getFacilityId());

            CalibrateStatus calibrateStatus = calibrateStatusMap.get(key);
            if (ObjectUtils.isEmpty(calibrateStatus)) {
                //无数据则为新增
                calibrateStatus = new CalibrateStatus().setWorkCell(calibrateCheckResultList.get(Constants.INT_ZERO).getWorkCell()).setFacilityId(calibrateCheckResult.getFacilityId()).setProcess(calibrateCheckResultByZero.getProcess());
                calibrateStatusList.add(calibrateStatus);
            }

            //有数据则为更新
            calibrateStatus.setLatestOperatorDate(calibrateCheckResult.getTestTime()).setResult(calibrateCheckResult.isResult()).setTester(calibrateCheckResult.getTester());
            //获取下次检测日期
            CalibrateRule calibrateRule = calibrateRuleMap.get(key);
            if (ObjectUtils.isEmpty(calibrateRule)) {
                throw new ResponseException(CALIBRATE_RULE_NOT_EXIST, CALIBRATE_RULE_NOT_EXIST_MSG);
            }
            LocalDateTime nextOperatorDate = DateUtils.delayDate(calibrateCheckResult.getTestTime(), calibrateRule.getPeriodNumber(), unit(calibrateRule.getPeriodUnit()));
            calibrateStatus.setNextOperatorDate(nextOperatorDate);
            //获取下次提醒日期
            LocalDateTime nextRemindDate = DateUtils.delayDate(nextOperatorDate, calibrateRule.getRemindPeriodNumber() * Constants.NEGATIVE_ONE, unit(calibrateRule.getRemindPeriodUnit()));
            calibrateStatus.setNextRemindDate(nextRemindDate);
        }

        //存储状态数据
        calibrateStatusRepository.saveAll(calibrateStatusList);
        //如果下次提醒日期为当天，则直接发送到延迟队列
        List<CalibrateStatus> todayRemindStatuses = calibrateStatusList.stream().filter(calibrateStatus -> calibrateStatus.getNextRemindDate().isBefore(LocalDateTime.now())||calibrateStatus.getNextRemindDate().isEqual(LocalDateTime.now())).toList();
        if(!CollectionUtils.isEmpty(todayRemindStatuses)) {
            toRemindMq(todayRemindStatuses);
        }
    }

    /**
     * 获取工位或设备校准状态 及 校准规则
     *
     * @param calibrateCheckResultList 校准数据集合
     * @param calibrateStatusList      校准状态集合（返回）
     * @param calibrateRuleList        校准规则集合（返回）
     * <AUTHOR>
     * @date 2022/7/13
     **/
    public void getCalibrateStatusAndRule(List<CalibrateCheckResult> calibrateCheckResultList, List<CalibrateStatus> calibrateStatusList, List<CalibrateRule> calibrateRuleList) {
        CalibrateCheckResult calibrateCheckResultZero = calibrateCheckResultList.get(Constants.INT_ZERO);
        List<CalibrateCheckResult> calibrateCheckResultListNoFacility = calibrateCheckResultList.stream().filter(i -> ObjectUtils.isEmpty(i.getFacilityId())).toList();
        List<CalibrateCheckResult> calibrateCheckResultListHasFacility = calibrateCheckResultList.stream().filter(i -> !ObjectUtils.isEmpty(i.getFacilityId())).toList();

        if (!CollectionUtils.isEmpty(calibrateCheckResultListNoFacility)) {
            //无设备
            List<CalibrateStatus> calibrateStatusListDB = calibrateStatusRepository.findByWorkCellIdAndFacilityIdIsNullAndProcessAndDeleted(calibrateCheckResultZero.getWorkCell().getId(), calibrateCheckResultZero.getProcess(), Constants.LONG_ZERO);
            List<CalibrateRule> calibrateRuleListDB = calibrateRuleRepository.findByWorkCellIdAndFacilityIdIsNullAndProcessAndDeleted(calibrateCheckResultZero.getWorkCell().getId(), calibrateCheckResultZero.getProcess(), Constants.LONG_ZERO);
            if (CollectionUtils.isEmpty(calibrateRuleListDB)) {
                throw new ResponseException(CALIBRATE_RULE_NOT_EXIST, CALIBRATE_RULE_NOT_EXIST_MSG);
            } else {
                calibrateRuleList.addAll(calibrateRuleListDB);
            }
            if (!CollectionUtils.isEmpty(calibrateStatusListDB)) {
                calibrateStatusList.addAll(calibrateStatusListDB);
            }
        }
        if (!CollectionUtils.isEmpty(calibrateCheckResultListHasFacility)) {
            //有设备
            List<Long> facilityIdList = calibrateCheckResultListHasFacility.stream().map(CalibrateCheckResult::getFacilityId).distinct().collect(Collectors.toList());
            List<CalibrateStatus> calibrateStatusListDB = calibrateStatusRepository.findByWorkCellIdAndFacilityIdInAndProcessAndDeleted(calibrateCheckResultZero.getWorkCell().getId(), facilityIdList, calibrateCheckResultZero.getProcess(), Constants.LONG_ZERO);
            List<CalibrateRule> calibrateRuleListDB = calibrateRuleRepository.findByWorkCellIdAndFacilityIdInAndProcessAndDeleted(calibrateCheckResultZero.getWorkCell().getId(), facilityIdList, calibrateCheckResultZero.getProcess(), Constants.LONG_ZERO);
            if (CollectionUtils.isEmpty(calibrateRuleListDB)) {
                throw new ResponseException(CALIBRATE_RULE_NOT_EXIST, CALIBRATE_RULE_NOT_EXIST_MSG);
            } else {
                calibrateRuleList.addAll(calibrateRuleListDB);
            }
            if (!CollectionUtils.isEmpty(calibrateStatusListDB)) {
                calibrateStatusList.addAll(calibrateStatusListDB);
            }
        }
    }

    /**
     * 如果下次提醒日期为当天，则直接发送到延迟队列
     *
     * @param calibrateStatusList 校准状态集合
     * <AUTHOR>
     * @date 2022/7/15
     **/
    public void toRemindMq(List<CalibrateStatus> calibrateStatusList) {
        for (CalibrateStatus calibrateStatus : calibrateStatusList) {
            //2. 计算间隔时间
            Long nextRemindMilli = calibrateStatus.getNextRemindDate().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            Long nowMilli = LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            int timeMillis = Integer.parseInt(String.valueOf(nextRemindMilli - nowMilli));
            DelayedMessageDTO delayedMessageDTO = new DelayedMessageDTO();
            delayedMessageDTO.setId(String.valueOf(calibrateStatus.getId())).setData(JSON.toJSONString(calibrateStatus)).setBusinessKey(CalibrateStatus.class.getSimpleName());
            rmesRabbitMqSender.send(JSON.toJSONString(delayedMessageDTO), timeMillis);
        }
    }


    /**
     * 状态回退
     *
     * @param calibrateCheckResult 删除的校准数据
     * @param inherit              是否有继承，false无继承，true正常继承
     * <AUTHOR>
     * @date 2022/7/12
     **/
    public void fallbackStatus(CalibrateCheckResult calibrateCheckResult, Boolean inherit) {
        //1. 获取工位或设备校准状态 及 校准规则
        List<CalibrateStatus> calibrateStatusList = new ArrayList<CalibrateStatus>();
        List<CalibrateRule> calibrateRuleList = new ArrayList<CalibrateRule>();
        getCalibrateStatusAndRule(Collections.singletonList(calibrateCheckResult), calibrateStatusList, calibrateRuleList);
        CalibrateStatus calibrateStatus = calibrateStatusList.get(Constants.INT_ZERO);
        CalibrateRule calibrateRule = calibrateRuleList.get(Constants.INT_ZERO);
        //2. 如果没有继承校准数据，则删除校准结果
        if (!inherit) {
            calibrateStatus.setDeleted(calibrateStatus.getId());
            calibrateStatusRepository.save(calibrateStatus);
            return;
        }
        //3. 组装状态数据
        //获取下次检测日期（旧）
        LocalDateTime nextOperatorDate = DateUtils.delayDate(calibrateCheckResult.getTestTime(), calibrateRule.getPeriodNumber(), unit(calibrateRule.getPeriodUnit()));
        //获取下次提醒日期
        LocalDateTime nextRemindDate = DateUtils.delayDate(nextOperatorDate, calibrateRule.getRemindPeriodNumber() * Constants.NEGATIVE_ONE, unit(calibrateRule.getRemindPeriodUnit()));

        calibrateStatus.setLatestOperatorDate(calibrateCheckResult.getTestTime()).setNextOperatorDate(nextOperatorDate).setNextRemindDate(nextRemindDate)
                .setResult(calibrateCheckResult.isResult()).setTester(calibrateCheckResult.getTester());
        //存储状态数据
        calibrateStatusRepository.save(calibrateStatus);
        //如果下次提醒日期为当天，则直接发送到延迟队列
        toRemindMq(List.of(calibrateStatus));
    }

    /**
     * 进行时间标识转换
     *
     * @param oldUnit 0:天，1:月，2:年
     * @return : Integer
     * <AUTHOR>
     * @date 2022/7/18
     **/
    public Integer unit(Integer oldUnit) {
        return oldUnit == Constants.INT_ZERO ? Constants.INT_ONE : oldUnit == Constants.INT_ONE ? Constants.INT_THREE : Constants.INT_FOUR;
    }


    public void calibrateRemind(CalibrateStatus calibrateStatus){
        //通过用户登录名查询用户ID
        UserDTO userDTO = rbaseRbacProxy.getUserByLoginName(calibrateStatus.getTester());
        List<Long> userIdList = ObjectUtils.isEmpty(userDTO) || ObjectUtils.isEmpty(userDTO.getId()) ? null : Collections.singletonList(userDTO.getId());
        //任务Code生成规则
        SerialNumberDTO serialNumberDto = serialNumberGenerate[0].getSerialNumber(Constants.KEY_MESSAGE_TASK_CODE);

        //任务说明
        String taskExplain = calibrateStatus.getFacilityDto().getName() + "需要在" + calibrateStatus.getNextOperatorDate() + "进行设备校准";

        TaskFeignDTO taskFeignRequestDTO = new TaskFeignDTO().setEstimateHandleDate(calibrateStatus.getNextOperatorDate().atZone(ZoneId.systemDefault()).toInstant()).setFollowList(userIdList).setOwnerList(userIdList)
                .setSendMessage(Constants.INT_ONE).setTaskCode(rbaseSerialNumberProxy.generate(serialNumberDto)).setTaskExplain(taskExplain)
                .setTaskName("设备待校准提醒").setTaskUrl(null).setBusinessKey(calibrateStatus.getFacilityDto().getCode());

        taskMessageServices[0].taskSystem(taskFeignRequestDTO);
    }

}
