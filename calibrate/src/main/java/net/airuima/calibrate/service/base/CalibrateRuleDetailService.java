package net.airuima.calibrate.service.base;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.calibrate.domain.base.CalibrateRuleDetail;
import net.airuima.calibrate.repository.base.CalibrateRuleDetailRepository;
import net.airuima.constant.Constants;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 校准规则详情表Service
 *
 * <AUTHOR>
 * @date 2022-07-14
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class CalibrateRuleDetailService extends CommonJpaService<CalibrateRuleDetail> {

    private final String CALIBRATE_RULE_DETAIL_ENTITY_GRAPH = "calibrateRuleDetailEntityGraph";
    private final CalibrateRuleDetailRepository calibrateRuleDetailRepository;

    public CalibrateRuleDetailService(CalibrateRuleDetailRepository calibrateRuleDetailRepository) {
        this.calibrateRuleDetailRepository = calibrateRuleDetailRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CalibrateRuleDetail> find(Specification<CalibrateRuleDetail> spec, Pageable pageable) {
        return calibrateRuleDetailRepository.findAll(spec, pageable,new NamedEntityGraph(CALIBRATE_RULE_DETAIL_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public List<CalibrateRuleDetail> find(Specification<CalibrateRuleDetail> spec) {
        return calibrateRuleDetailRepository.findAll(spec,new NamedEntityGraph(CALIBRATE_RULE_DETAIL_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CalibrateRuleDetail> findAll(Pageable pageable) {
        return calibrateRuleDetailRepository.findAll(pageable,new NamedEntityGraph(CALIBRATE_RULE_DETAIL_ENTITY_GRAPH));
    }

    /**
     * 通过 规则ID 查询规则详情
     *
     * @param calibrateRuleId 规则ID
     * @return : List<CalibrateRuleDetail>
     * <AUTHOR>
     * @date 2022/7/14
     **/
    @Transactional(readOnly = true)
    public List<CalibrateRuleDetail> findByCalibrateRuleId(Long calibrateRuleId) {
        return calibrateRuleDetailRepository.findByCalibrateRuleIdInAndCalibrateItemEnableAndDeleted(Collections.singletonList(calibrateRuleId), Constants.LONG_ZERO);
    }
}
