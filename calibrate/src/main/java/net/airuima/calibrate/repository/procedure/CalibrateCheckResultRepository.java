package net.airuima.calibrate.repository.procedure;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.calibrate.domain.procedure.CalibrateCheckResult;
import net.airuima.config.annotation.DataFilter;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 台位校准历史表Repository
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Repository
public interface CalibrateCheckResultRepository extends LogicDeleteableRepository<CalibrateCheckResult>,
        EntityGraphJpaSpecificationExecutor<CalibrateCheckResult>, EntityGraphJpaRepository<CalibrateCheckResult, Long> {

    /**
     * 通过台位更新是否为最新测试记录
     *
     * @param workCellId 工位ID
     * @param facilityId 设备ID
     * @param process    校准程序，0：内校，1：外校
     * @param isLatest   最新最新记录标识
     * <AUTHOR>
     * @date 2022/7/8
     **/
    @Modifying
    @Query(value = "update procedure_calibrate_check_result set is_latest=?4 where work_cell_id=?1 and if(COALESCE(?2,'') != '', facility_id=?2, facility_id is null) and process = ?3 and is_latest!=?4 and deleted=0", nativeQuery = true)
    void batchUpdateCalibrateCheckResultIsLatestByWordCellAndFacilityAndProcess(Long workCellId, Long facilityId, Integer process, Boolean isLatest);

    /**
     * 通过台位编码按照设备主键ID和时间排序和获取最新记录
     *
     * @param workCellCode 台位编码
     * @param facilityId   设备主键ID
     * @param deleted      逻辑删除
     * @return net.airuima.rbase.domain.procedure.calibrate.CalibrateCheckResult 台位校准历史
     */
    @DataFilter(isSkip = true)
    CalibrateCheckResult findTop1ByWorkCellCodeAndFacilityIdAndProcessAndDeletedOrderByTestTimeDescIdDesc(String workCellCode, Long facilityId, Integer process, Long deleted);

    /**
     * 通过台位编码按照id和时间排序和获取最新记录
     *
     * @param workCellCode 台位编码
     * @param deleted      逻辑删除
     * @return net.airuima.rbase.domain.procedure.calibrate.CalibrateCheckResult 台位校准历史
     */
    @DataFilter(isSkip = true)
    CalibrateCheckResult findTop1ByWorkCellCodeAndFacilityIdIsNullAndProcessAndDeletedOrderByTestTimeDescIdDesc(String workCellCode, Integer process, Long deleted);

    /**
     * 通过台位编码及最新状态获取最新记录
     *
     * @param workCellCode 台位编码
     * @param isLatest     是否为最新记录
     * @param deleted      逻辑删除
     * @return net.airuima.rbase.domain.procedure.calibrate.CalibrateCheckResult 台位校准历史
     */
    @DataFilter(isSkip = true)
    CalibrateCheckResult findByWorkCellCodeAndIsLatestAndAndDeleted(String workCellCode, Boolean isLatest, Long deleted);

    /**
     * 通过台位编码 ，标准件SN,测试开始时间及测试结束时间获取列表
     *
     * @param workCellCode 台位编码
     * @param standPartId          标准件ID
     * @param startTime    测试开始时间
     * @param endTime      测试结束时间
     * @param deleted      逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.procedure.calibrate.CalibrateCheckResult> 台位校准历史列表
     */
    @DataFilter(isSkip = true)
    List<CalibrateCheckResult> findByWorkCellCodeAndStandardPartIdAndTestTimeGreaterThanEqualAndTestTimeLessThanEqualAndDeleted(String workCellCode, Long standPartId, LocalDateTime startTime, LocalDateTime endTime, Long deleted);

    /**
     * 通过台位编码 ，标准件SN,测试时间获取校准数据
     *
     * @param workCellCode 台位编码
     * @param standPartId           标准件ID
     * @param testTime     测试时间
     * @param deleted      逻辑删除
     * @return net.airuima.rbase.domain.procedure.calibrate.CalibrateCheckResult 台位校准历史
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    CalibrateCheckResult findByWorkCellCodeAndStandardPartIdAndTestTimeAndDeleted(String workCellCode, Long standPartId, LocalDateTime testTime, Long deleted);

    /**
     * 根据主键ID和删除状态查询
     *
     * @param id      台位校准历史表主键ID
     * @param deleted 删除标识
     * @return java.util.Optional<net.airuima.rbase.domain.procedure.calibrate.CalibrateCheckResult> 台位校准历史
     * <AUTHOR>
     * @date 2022/7/7
     **/
    @DataFilter(isSkip = true)
    @FetchMethod
    Optional<CalibrateCheckResult> findByIdAndDeleted(Long id, long deleted);
}
