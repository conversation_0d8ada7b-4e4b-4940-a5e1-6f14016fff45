package net.airuima.calibrate.repository.procedure;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.calibrate.domain.procedure.CalibrateStatus;
import net.airuima.config.annotation.DataFilter;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 设备校准状态表Repository
 *
 * <AUTHOR>
 * @date 2022-07-04
 */
@Repository
public interface CalibrateStatusRepository extends LogicDeleteableRepository<CalibrateStatus>,
        EntityGraphJpaSpecificationExecutor<CalibrateStatus>, EntityGraphJpaRepository<CalibrateStatus, Long> {

    /**
     * 通过 工位主键ID + 设备集合 + 删除标识 查询校验状态 集合
     *
     * @param workCellId 工位主键ID
     * @param facilityId 设备主键ID
     * @param process    校准程序，0：内校，1：外校
     * @param deleted    删除标识
     * @return java.util.List<net.airuima.rbase.domain.procedure.calibrate.CalibrateStatus> 设备校准状态列表
     * <AUTHOR>
     * @date 2022/7/12
     **/
    @DataFilter(isSkip = true)
    List<CalibrateStatus> findByWorkCellIdAndFacilityIdInAndProcessAndDeleted(Long workCellId, List<Long> facilityId, Integer process, Long deleted);

    /**
     * 通过 工位主键ID + 设备主键ID + 删除标识 查询校验状态
     * @param workCellId 工位主键ID
     * @param facilityId 设备主键ID
     * @param process 校准程序，0：内校，1：外校
     * @param deleted 删除标识
     * @return net.airuima.rbase.domain.procedure.calibrate.CalibrateStatus 设备校准状态列表
     */
    @DataFilter(isSkip = true)
    CalibrateStatus findByWorkCellIdAndFacilityIdAndProcessAndDeleted(Long workCellId, Long facilityId, Integer process, Long deleted);

    /**
     * 通过 工位主键ID + 设备为空 + 删除标识 查询校验状态 集合
     *
     * @param workCellId 工位主键ID
     * @param process    校准程序，0：内校，1：外校
     * @param deleted    删除标识
     * @return java.util.List<net.airuima.rbase.domain.procedure.calibrate.CalibrateStatus> 设备校准状态列表
     * <AUTHOR>
     * @date 2022/7/12
     **/
    @DataFilter(isSkip = true)
    List<CalibrateStatus> findByWorkCellIdAndFacilityIdIsNullAndProcessAndDeleted(Long workCellId, Integer process, Long deleted);

    /**
     * 获取 指定时间范围内 设备状态列表
     * @param localDateTimeStart 开始时间
     * @param localDateTimeEnd 结束时间
     * @return java.util.List<net.airuima.rbase.domain.procedure.calibrate.CalibrateStatus> 设备校准状态列表
     * <AUTHOR>
     * @date 2022/7/15
     **/
    @FetchMethod
    @Query("select cs from CalibrateStatus cs where cs.deleted = 0L and cs.nextRemindDate between ?1 and ?2")
    List<CalibrateStatus> findByNextRemindDateBetween(LocalDateTime localDateTimeStart, LocalDateTime localDateTimeEnd);
}
