package net.airuima.calibrate.repository.base;

import net.airuima.calibrate.domain.base.CalibrateItem;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 校准项目表Repository
 *
 * <AUTHOR>
 * @date 2022-07-04
 */
@Repository
public interface CalibrateItemRepository extends LogicDeleteableRepository<CalibrateItem>,
        JpaSpecificationExecutor<CalibrateItem>, JpaRepository<CalibrateItem, Long> {

    /**
     * 根据项目名称或者编码模糊查询启用的校准项目
     * @param name  项目名称或者编码
     * @param pageable      分页参数
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.base.calibrate.CalibrateItem> 校准项目分页结果
     * <AUTHOR>
     * @date 2024/1/18
     */
    @Query("select ci from CalibrateItem ci where ci.deleted = 0L and (ci.name like concat('%',?1,'%') or ci.code like concat('%',?1,'%')) and ci.enable = true")
    Page<CalibrateItem> findByNameLike(String name, Pageable pageable);

    /**
     * 通过校准项目主键Id修改启用/禁用状态
     * @param enable 启用/禁用
     * @param id 校准项目主键Id
     * <AUTHOR>
     * @date 2024/1/18
     */
    @Modifying
    @Query("update CalibrateItem ci set ci.enable = ?1 where ci.id = ?2")
    void updateEnableById(Boolean enable, Long id);


    /**
     * 通过校准项目主键ID删除校准项目
     * @param id 校准项目主键ID
     * @date 2024/1/18
     */
    @Modifying
    @Query("update CalibrateItem ci set ci.enable = false, ci.deleted = ci.id where ci.id = ?1")
    void deleteById(Long id);

    /**
     * 通过校准项目编码列表获取可用项目列表
     * @param codes 校准项目编码
     * @param enable 是否启用
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.rbase.domain.base.calibrate.CalibrateItem> 校准项目列表
     */
    List<CalibrateItem> findByCodeInAndEnableAndDeleted(List<String> codes,Boolean enable,Long deleted);
}
