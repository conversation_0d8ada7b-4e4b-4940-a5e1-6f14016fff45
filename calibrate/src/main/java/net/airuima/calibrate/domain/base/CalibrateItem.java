package net.airuima.calibrate.domain.base;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.Forbidden;
import net.airuima.domain.base.CustomBaseEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 校准项目表Domain
 *
 * <AUTHOR>
 * @date 2022-07-04
 */
@Schema(name = "校准项目(CalibrateItem)", description = "校准项目")
@Entity
@Table(name = "base_calibrate_item", uniqueConstraints = {
        @UniqueConstraint(name = "base_calibrate_item_unique", columnNames = {"code", "deleted"})
})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
public class CalibrateItem extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目编码
     */
    @NotNull
    @Schema(description = "校准项目编码",  requiredMode = Schema.RequiredMode.REQUIRED,maxLength = 50,type = "string")
    @Column(name = "code", nullable = false)
    private String code;

    /**
     * 校准项目名称
     */
    @NotNull
    @Schema(description = "校准项目名称", requiredMode = Schema.RequiredMode.REQUIRED,maxLength = 100,type = "string")
    @Column(name = "name", nullable = false)
    private String name;

    /**
     * 校准标准，0：参考基值，1：是否正常
     */
    @Schema(description = "校准标准，0：参考基值，1：是否正常", requiredMode = Schema.RequiredMode.REQUIRED,type = "integer",format = "int32")
    @Column(name = "category", nullable = false)
    private int category;

    /**
     * 合格范围-采用数学开闭区间表示
     */
    @Schema(description = "合格范围-采用数学开闭区间表示",example = "(50,100]表示大于50且小于等于100",type = "string",maxLength = 50)
    @Column(name = "qualified_range")
    private String qualifiedRange;

    /**
     * 校准项目单位
     */
    @Schema(description = "校准项目单位",maxLength = 10,nullable = true,type = "string")
    @Column(name = "unit")
    private String unit;

    /**
     * 是否启用，0：未启用, 1：启用
     */
    @Schema(description = "是否启用，0：未启用, 1：启用", requiredMode = Schema.RequiredMode.REQUIRED,type = "boolean")
    @Column(name = "enable", nullable = false)
    @Forbidden
    private boolean enable;


    public String getCode() {
        return code;
    }

    public CalibrateItem setCode(String code) {
        this.code = code;
        return this;
    }

    public String getName() {
        return name;
    }

    public CalibrateItem setName(String name) {
        this.name = name;
        return this;
    }

    public int getCategory() {
        return category;
    }

    public CalibrateItem setCategory(int category) {
        this.category = category;
        return this;
    }

    public String getQualifiedRange() {
        return qualifiedRange;
    }

    public void setQualifiedRange(String qualifiedRange) {
        this.qualifiedRange = qualifiedRange;
    }

    public String getUnit() {
        return unit;
    }

    public CalibrateItem setUnit(String unit) {
        this.unit = unit;
        return this;
    }

    public boolean getEnable() {
        return enable;
    }

    public CalibrateItem setEnable(boolean enable) {
        this.enable = enable;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CalibrateItem calibrateItem = (CalibrateItem) o;
        if (calibrateItem.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), calibrateItem.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
