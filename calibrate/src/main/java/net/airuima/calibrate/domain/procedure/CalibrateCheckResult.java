package net.airuima.calibrate.domain.procedure;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.scene.WorkCell;
import net.airuima.rbase.dto.rfms.FacilityDTO;
import net.airuima.rbase.dto.standardpart.StandardPartDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 台位校准历史表Domain
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Schema(name = "台位校准历史表(CalibrateCheckResult)", description = "台位校准历史表")
@Entity
@Table(name = "procedure_calibrate_check_result")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@FetchEntity
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@NamedEntityGraph(name = "calibrateCheckResultEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "workCell",subgraph = "workCellEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "workCellEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine"),
                                @NamedAttributeNode(value = "workStation",
                                        subgraph = "workStationEntityGraph")}),
                @NamedSubgraph(name = "workStationEntityGraph",
                        attributeNodes = {@NamedAttributeNode("workLine")})})
public class CalibrateCheckResult extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 台位
     */
    @NotNull
    @Schema(description = "台位", required = true)
    @Column(name = "work_cell_code", nullable = false)
    private String workCellCode;


    /**
     * 标准件ID
     */
    @Schema(description = "标准件ID")
    @Column(name = "standard_part_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long standardPartId;

    /**
     * 标准件DTO
     */
    @Schema(description = "标准件DTO", required = true)
    @FetchField(mapUri = "/api/standard-parts", serviceId = "mom", paramKey = "standardPartId")
    @Transient
    private StandardPartDTO standardPartDto = new StandardPartDTO();

    /**
     * 测试时间
     */
    @Schema(description = "测试时间")
    @Column(name = "test_time")
    private LocalDateTime testTime;

    /**
     * 测试人员
     */
    @Schema(description = "测试人员")
    @Column(name = "tester")
    private String tester;

    /**
     * 是否为最新记录
     */
    @Schema(description = "是否为最新记录")
    @Column(name = "is_latest")
    private boolean isLatest;

    /**
     * 工位ID
     */
    @Schema(description = "工位ID")
    @ManyToOne
    @JoinColumn(name = "work_cell_id")
    private WorkCell workCell;

    /**
     * 设备ID
     */
    @Schema(description = "设备ID")
    @Column(name = "facility_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long facilityId;

    @Schema(description = "设备DTO")
    @FetchField(mapUri = "/api/facilities", serviceId = "mom", paramKey = "facilityId")
    @Transient
    private FacilityDTO facilityDto = new FacilityDTO();

    /**
     * 标准件CODE
     */
    @Schema(description = "标准件CODE")
    @Column(name = "standard_part_code")
    private String standardPartCode;

    /**
     * 校准程序，0：内校，1：外校
     **/
    @NotNull
    @Schema(description = "校准程序，0：内校，1：外校", required = true)
    @Column(name = "process", nullable = false)
    private int process;

    /**
     * 校准单位
     */
    @Schema(description = "校准单位")
    @Column(name = "calibrate_company")
    private String calibrateCompany;

    /**
     * 设备外观情况
     */
    @Schema(description = "设备外观情况")
    @Column(name = "appearance")
    private String appearance;

    /**
     * 校准合格证数量
     */
    @Schema(description = "校准合格证数量, 对应File映射表category的0")
    @Column(name = "certificate_number")
    private int certificateNumber;

    /**
     * 校准合格证数量
     */
    @Schema(description = "校准证明报告书数量, 对应File映射表category的1")
    @Column(name = "report_number")
    private int reportNumber;

    /**
     * 是否合格 0:不合格;1:合格
     */
    @Schema(description = "是否合格 0:不合格;1:合格")
    @Column(name = "result")
    private boolean result;

    public CalibrateCheckResult() {
    }

    public CalibrateCheckResult(String workCellCode, Long standardPartId, LocalDateTime testTime, String tester, boolean isLatest, WorkCell workCell, Long facilityId, String standardPartCode, int process, boolean result) {
        this.workCellCode = workCellCode;
        this.standardPartId = standardPartId;
        this.testTime = testTime;
        this.tester = tester;
        this.isLatest = isLatest;
        this.workCell = workCell;
        this.facilityId = facilityId;
        this.standardPartCode = standardPartCode;
        this.process = process;
        this.result = result;
    }

    public CalibrateCheckResult(String workCellCode, LocalDateTime testTime, String tester, boolean isLatest, WorkCell workCell, int process, String calibrateCompany) {
        this.workCellCode = workCellCode;
        this.testTime = testTime;
        this.tester = tester;
        this.isLatest = isLatest;
        this.workCell = workCell;
        this.process = process;
        this.calibrateCompany = calibrateCompany;
    }

    public String getWorkCellCode() {
        return workCellCode;
    }

    public CalibrateCheckResult setWorkCellCode(String workCellCode) {
        this.workCellCode = workCellCode;
        return this;
    }


    public Long getStandardPartId() {
        return standardPartId;
    }

    public CalibrateCheckResult setStandardPartId(Long standardPartId) {
        this.standardPartId = standardPartId;
        return this;
    }

    public StandardPartDTO getStandardPartDto() {
        return standardPartDto;
    }

    public CalibrateCheckResult setStandardPartDto(StandardPartDTO standardPartDto) {
        this.standardPartDto = standardPartDto;
        return this;
    }

    public LocalDateTime getTestTime() {
        return testTime;
    }

    public CalibrateCheckResult setTestTime(LocalDateTime testTime) {
        this.testTime = testTime;
        return this;
    }

    public String getTester() {
        return tester;
    }

    public CalibrateCheckResult setTester(String tester) {
        this.tester = tester;
        return this;
    }

    public boolean getIsLatest() {
        return isLatest;
    }

    public CalibrateCheckResult setIsLatest(boolean latest) {
        isLatest = latest;
        return this;
    }

    public Long getFacilityId() {
        return facilityId;
    }

    public CalibrateCheckResult setFacilityId(Long facilityId) {
        this.facilityId = facilityId;
        return this;
    }

    public String getStandardPartCode() {
        return standardPartCode;
    }

    public CalibrateCheckResult setStandardPartCode(String standardPartCode) {
        this.standardPartCode = standardPartCode;
        return this;
    }

    public int getProcess() {
        return process;
    }

    public CalibrateCheckResult setProcess(int process) {
        this.process = process;
        return this;
    }

    public String getCalibrateCompany() {
        return calibrateCompany;
    }

    public CalibrateCheckResult setCalibrateCompany(String calibrateCompany) {
        this.calibrateCompany = calibrateCompany;
        return this;
    }

    public String getAppearance() {
        return appearance;
    }

    public CalibrateCheckResult setAppearance(String appearance) {
        this.appearance = appearance;
        return this;
    }

    public int getCertificateNumber() {
        return certificateNumber;
    }

    public CalibrateCheckResult setCertificateNumber(int certificateNumber) {
        this.certificateNumber = certificateNumber;
        return this;
    }

    public int getReportNumber() {
        return reportNumber;
    }

    public CalibrateCheckResult setReportNumber(int reportNumber) {
        this.reportNumber = reportNumber;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public CalibrateCheckResult setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public FacilityDTO getFacilityDto() {
        return facilityDto;
    }

    public CalibrateCheckResult setFacilityDto(FacilityDTO facilityDto) {
        this.facilityDto = facilityDto;
        return this;
    }

    public boolean isResult() {
        return result;
    }

    public CalibrateCheckResult setResult(boolean result) {
        this.result = result;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CalibrateCheckResult calibrateCheckResult = (CalibrateCheckResult) o;
        if (calibrateCheckResult.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), calibrateCheckResult.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }


}
