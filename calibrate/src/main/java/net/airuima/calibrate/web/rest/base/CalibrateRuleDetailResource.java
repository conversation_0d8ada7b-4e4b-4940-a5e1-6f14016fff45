package net.airuima.calibrate.web.rest.base;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.calibrate.domain.base.CalibrateRuleDetail;
import net.airuima.calibrate.service.base.CalibrateRuleDetailService;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.BaseResource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.URISyntaxException;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 校准规则详情表Resource
 *
 * <AUTHOR>
 * @date 2022-07-14
 */
@Tag(name = "校准规则详情表Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/calibrate-rule-details")
@AuthorityRegion("工位设备校准")
@FuncInterceptor("FBase && FCalibration")
public class CalibrateRuleDetailResource extends BaseResource<CalibrateRuleDetail> {

    private final CalibrateRuleDetailService calibrateRuleDetailService;

    public CalibrateRuleDetailResource(CalibrateRuleDetailService calibrateRuleDetailService) {
        this.calibrateRuleDetailService = calibrateRuleDetailService;
        this.mapUri = "/api/calibrate-rule-details";
    }

    /**
     * 通过校准规则ID查询规则详情
     *
     * @param calibrateRuleId 规则ID
     * @return : ResponseEntity<ResponseData<List<CalibrateRuleDetail>>>
     * <AUTHOR>
     * @date 2022/7/14
     **/
    @Operation(summary = "通过校准规则ID查询规则详情", parameters = {
            @Parameter(name = "calibrateRuleId",description = "校准规则主键ID", required = true,schema = @Schema(type = "integer",format = "int64"),in = ParameterIn.PATH) }
    )
    @PreAuthorize("hasAnyAuthority('CALIBRATERULE_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @GetMapping("/calibrateRuleId/{calibrateRuleId}")
    public ResponseEntity<ResponseData<List<CalibrateRuleDetail>>> findByCalibrateRuleId(@PathVariable("calibrateRuleId") Long calibrateRuleId) throws URISyntaxException {
        try {
            List<CalibrateRuleDetail> calibrateRuleDetailList = calibrateRuleDetailService.findByCalibrateRuleId(calibrateRuleId);
            return ResponseData.ok(calibrateRuleDetailList);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

}
