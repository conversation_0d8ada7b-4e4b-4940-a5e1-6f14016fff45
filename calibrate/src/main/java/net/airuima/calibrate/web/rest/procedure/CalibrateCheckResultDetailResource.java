package net.airuima.calibrate.web.rest.procedure;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.calibrate.domain.procedure.CalibrateCheckResultDetail;
import net.airuima.calibrate.service.procedure.CalibrateCheckResultDetailService;
import net.airuima.calibrate.web.rest.procedure.dto.CalibrateCheckResultDetailGetDTO;
import net.airuima.calibrate.web.rest.procedure.dto.CalibrateCheckResultDetailSaveDTO;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.util.ResponseData;
import net.airuima.web.BaseResource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 台位校准历史数据明细Resource
 *
 * <AUTHOR>
 * @date 2022-05-23
 */
@Tag(name = "台位校准历史数据明细Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/calibrate-check-result-details")
@AuthorityRegion("工位设备校准")
@FuncInterceptor("FBase && FCalibration")
public class CalibrateCheckResultDetailResource extends BaseResource<CalibrateCheckResultDetail> {

    private final CalibrateCheckResultDetailService calibrateCheckResultDetailService;

    public CalibrateCheckResultDetailResource(CalibrateCheckResultDetailService calibrateCheckResultDetailService) {
        this.calibrateCheckResultDetailService = calibrateCheckResultDetailService;
        this.mapUri = "/api/calibrate-check-result-details";
    }

    /**
     *  通过台位校准历史ID获取检测数据明细
     * @param checkResultId 台位校准历史ID
     * @return CalibrateCheckResultDetailGetDTO
     */
    @PreAuthorize("hasAnyAuthority('CALIBRATECHECKRESULT_READ') or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "通过台位校准历史ID获取检测数据明细")
    @GetMapping("/edit/{checkResultId}")
    public ResponseEntity<ResponseData<CalibrateCheckResultDetailGetDTO>> getDetailByResultId(@PathVariable("checkResultId") Long checkResultId){
        return ResponseData.ok(calibrateCheckResultDetailService.getCheckResultDetailByResultId(checkResultId));
    }


    /**
     * 保存手动修改的台位校准历史明细数据
     * @param calibrateCheckResultDetailSaveDTOList 批量待保存的台位校准历史明细数据参数DTO
     */
    @PreAuthorize("hasAnyAuthority('CALIBRATECHECKRESULT_UPDATE') or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "保存手动修改的台位校准历史明细数据")
    @PutMapping("/batch")
    public ResponseEntity<ResponseData<Void>> batchUpdateCalibrateCheckResultDetail(@RequestBody List<CalibrateCheckResultDetailSaveDTO> calibrateCheckResultDetailSaveDTOList){
        try{
            calibrateCheckResultDetailService.batchUpdateCalibrateCheckResultDetail(calibrateCheckResultDetailSaveDTOList);
            return ResponseData.save();
        }catch (Exception e){
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

}
