package net.airuima.calibrate.web.rest.base;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import net.airuima.calibrate.domain.base.CalibrateRule;
import net.airuima.calibrate.service.base.CalibrateRuleService;
import net.airuima.calibrate.web.rest.base.dto.CalibrateRuleGetDTO;
import net.airuima.calibrate.web.rest.base.dto.CalibrateRuleSaveDTO;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.constant.Constants;
import net.airuima.dto.ExportDTO;
import net.airuima.query.QueryConditionParser;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.web.rest.errors.BadRequestAlertException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 校准规则表Resource
 *
 * <AUTHOR>
 * @date 2022-07-04
 */
@Tag(name = "校准规则表Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/calibrate-rules")
@AuthorityRegion("工位设备校准")
@FuncInterceptor("FBase && FCalibration")
public class CalibrateRuleResource extends ProtectBaseResource<CalibrateRule> {
    private static final String EXCEPTION = "exception";
    private final CalibrateRuleService calibrateRuleService;

    public CalibrateRuleResource(CalibrateRuleService calibrateRuleService) {
        this.calibrateRuleService = calibrateRuleService;
        this.mapUri = "/api/calibrate-rules";
    }

    /**
     * 新增校准规则
     *
     * @param saveDto 校准规则DTO
     * @return ResponseEntity<ResponseData<CalibrateRule>>
     * <AUTHOR>
     * @date 2022-07-05
     **/
    @Operation(summary = "新增校准规则")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping("/custom")
    public ResponseEntity<ResponseData<CalibrateRule>> create(@Parameter(schema = @Schema(implementation = CalibrateRuleSaveDTO.class),required = true,description = "校准规则信息")
                                                                  @Valid @RequestBody CalibrateRuleSaveDTO saveDto) throws URISyntaxException {
        try {
            calibrateRuleService.create(saveDto);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 更新校准规则
     *
     * @param entity 校准规则实体
     * @return ResponseEntity<ResponseData<CalibrateRule>>
     * <AUTHOR>
     * @date 2022-07-11
     **/
    @Operation(summary = "更新校准规则")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PutMapping
    @Override
    public ResponseEntity<CalibrateRule> update(@Parameter(schema = @Schema(implementation = CalibrateRuleSaveDTO.class),required = true,description = "校准规则信息") @Valid @RequestBody CalibrateRule entity) throws URISyntaxException {
        try {
            calibrateRuleService.updateEntity(entity);
            return ResponseEntity.ok().headers(HeaderUtil.updatedAlert(StringUtils.uncapitalize(CalibrateRule.class.getSimpleName()), "")).build();
        } catch (ResponseException responseException){
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        }catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, EXCEPTION, e.getMessage())).build();
        }
    }
    /**
     * 更新校准规则详情
     *
     * @param entity 校准规则实体
     * @return ResponseEntity<ResponseData<Void>>
     * <AUTHOR>
     * @date 2022-07-11
     **/
    @Operation(summary = "更新校准规则详情")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PutMapping("/detail")
    public ResponseEntity<ResponseData<Void>> updateDetail(@Valid @RequestBody CalibrateRuleSaveDTO entity) throws URISyntaxException {
        try {
            calibrateRuleService.updateDetail(entity);
            return ResponseData.save();
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 删除校准规则
     *
     * @param id 校准规则id
     * @return
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @DeleteMapping({"/{id}"})
    @Operation(summary = "删除校准规则", parameters = {
            @Parameter(name = "id",required = true, description = "校准规则主键ID",schema = @Schema(type = "int64"))
    })
    @Override
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        try {
            calibrateRuleService.deleteEntity(id);
            return ResponseEntity.ok().headers(HeaderUtil.deletedAlert(StringUtils.uncapitalize(CalibrateRule.class.getSimpleName()), "")).build();
        } catch (ResponseException responseException){
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        }catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, EXCEPTION, e.getMessage())).build();
        }
    }

    /**
     * 通过工位ID+内校/外校，查询关联设备列表及规则列表
     *
     * @param workCellId 工位ID
     * @param process    校准程序，0：内校，1：外校
     * @return : ResponseEntity<ResponseData<CalibrateRule>>
     * <AUTHOR>
     * @date 2022/7/6
     **/
    @Operation(summary = "通过工位ID+内校/外校，查询关联设备列表及规则列表")
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @GetMapping("/workCellId/{workCellId}/process/{process}")
    public ResponseEntity<ResponseData<List<CalibrateRuleGetDTO>>> findByCellIdAndProcess(@PathVariable("workCellId") Long workCellId, @PathVariable("process") Integer process) throws URISyntaxException {
        try {
            List<CalibrateRuleGetDTO> calibrateRuleGetDTOList = calibrateRuleService.findByCellIdAndProcess(workCellId, process);
            return ResponseData.ok(calibrateRuleGetDTOList);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 校准规则导入
     * @param file 导入文件
     * <AUTHOR>
     * @date  2023/4/20
     * @return void
     */
    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_IMPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping({"/importTableExcel"})
    public ResponseEntity<Void> importTableExcel(@RequestParam("file") MultipartFile file, @RequestParam("data") String data, @RequestParam(value = "suffix",required = false) String suffix, @RequestParam(value = "metaColumn",required = false) String metaColumn, HttpServletResponse response) throws Exception {
        try {
            if (file.isEmpty()) {
                return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(entityName, "FileEmpty", "File invalid.")).build();
            }
            calibrateRuleService.importTableExcel(file);
            return ResponseEntity.ok().headers(HeaderUtil.createdAlert(entityName + ".importSuccess", entityName)).build();
        }catch (ResponseException responseException){
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        }catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "importFailed", e.toString())).build();
        }
    }
    /**
     * 校准规则导出
     * <AUTHOR>
     * @date  2023/4/20
     * @return void
     */
    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_EXPORT')) or hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping({"/exportTableExcel"})
    public void exportTableExcel(ModelMap modelMap, @RequestBody ExportDTO exportDTO, HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<CalibrateRule> calibrateRules = new ArrayList<>();
        if(!exportDTO.getExportTemplate()){
            Specification<CalibrateRule> spec = QueryConditionParser.buildSpecificationWithClassName(CalibrateRule.class.getName(), exportDTO.getQcs(), this.filters, this.filterReformer);
            calibrateRules = calibrateRuleService.find(spec);
        }
        try {
            calibrateRuleService.exportTableExcel(calibrateRules,exportDTO,response);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览校准规则";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建校准规则";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改校准规则";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除校准规则";
        }else if (authority.equals(this.entityName.toUpperCase() + net.airuima.rbase.constant.Constants.AUTHORITY_IMPORT)) {
            return "导入校准规则";
        }else if (authority.equals(this.entityName.toUpperCase() + net.airuima.rbase.constant.Constants.AUTHORITY_EXPORT)) {
            return "导出校准规则";
        }
        return "";
    }

}
