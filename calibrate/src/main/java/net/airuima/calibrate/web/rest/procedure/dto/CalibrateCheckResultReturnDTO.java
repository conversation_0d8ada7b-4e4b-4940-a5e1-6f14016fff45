package net.airuima.calibrate.web.rest.procedure.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.rbase.dto.document.DocumentDTO;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 校准规则表Domain
 *
 * <AUTHOR>
 * @date 2022-07-04
 */
@Schema(description = "校准数据查看记录返回DTO")
public class CalibrateCheckResultReturnDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 校准数据ID
     */
    @Schema(description = "校准数据ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 工位编码
     */
    @Schema(description = "工位编码")
    private String workCellCode;

    /**
     * 校准程序，0：内校，1：外校
     **/
    @Schema(description = "校准程序，0：内校，1：外校", required = true)
    private Integer process;

    /**
     * 校准设备Id
     */
    @Schema(description = "校准设备Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long facilityId;

    /**
     * 校准设备名称
     */
    @Schema(description = "校准设备名称")
    private String facilityName;

    /**
     * 校准人ID
     */
    @Schema(description = "测试人员")
    private String tester;

    /**
     * 标准件ID
     */
    @Schema(description = "标准件ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long standardPartId;

    /**
     * 标准件编码
     */
    @Schema(description = "标准件编码")
    private String standardPartCode;

    /**
     * 校准单位
     */
    @Schema(description = "校准单位")
    private String calibrateCompany;

    /**
     * 设备外观情况
     */
    @Schema(description = "设备外观情况")
    private String appearance;

    /**
     * 校准合格证数量
     */
    @Schema(description = "校准合格证数量")
    private Integer certificateNumber;

    /**
     * 校准合格证数量
     */
    @Schema(description = "校准证明报告书数量")
    private Integer reportNumber;

    /**
     * 校准合格证路径
     */
    @Schema(description = "校准合格证路径")
    private List<DocumentDTO> certificateFileList;

    /**
     * 校准合格证路径
     */
    @Schema(description = "校准证明报告书路径")
    private List<DocumentDTO> reportFileList;

    /**
     * 是否合格 0:不合格;1:合格
     */
    @Schema(description = "是否合格 0:不合格;1:合格")
    private Boolean result;

    /**
     * 校准项目列表
     **/
    @Schema(description = "校准数据查看记录内部类列表", required = true)
    private List<CalibrateCheckResultReturn> calibrateCheckResultReturnList;

    public Long getId() {
        return id;
    }

    public CalibrateCheckResultReturnDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getWorkCellCode() {
        return workCellCode;
    }

    public CalibrateCheckResultReturnDTO setWorkCellCode(String workCellCode) {
        this.workCellCode = workCellCode;
        return this;
    }

    public Integer getProcess() {
        return process;
    }

    public CalibrateCheckResultReturnDTO setProcess(Integer process) {
        this.process = process;
        return this;
    }

    public Long getFacilityId() {
        return facilityId;
    }

    public CalibrateCheckResultReturnDTO setFacilityId(Long facilityId) {
        this.facilityId = facilityId;
        return this;
    }

    public String getFacilityName() {
        return facilityName;
    }

    public CalibrateCheckResultReturnDTO setFacilityName(String facilityName) {
        this.facilityName = facilityName;
        return this;
    }

    public String getTester() {
        return tester;
    }

    public CalibrateCheckResultReturnDTO setTester(String tester) {
        this.tester = tester;
        return this;
    }

    public Long getStandardPartId() {
        return standardPartId;
    }

    public CalibrateCheckResultReturnDTO setStandardPartId(Long standardPartId) {
        this.standardPartId = standardPartId;
        return this;
    }

    public String getStandardPartCode() {
        return standardPartCode;
    }

    public CalibrateCheckResultReturnDTO setStandardPartCode(String standardPartCode) {
        this.standardPartCode = standardPartCode;
        return this;
    }

    public String getCalibrateCompany() {
        return calibrateCompany;
    }

    public CalibrateCheckResultReturnDTO setCalibrateCompany(String calibrateCompany) {
        this.calibrateCompany = calibrateCompany;
        return this;
    }

    public String getAppearance() {
        return appearance;
    }

    public CalibrateCheckResultReturnDTO setAppearance(String appearance) {
        this.appearance = appearance;
        return this;
    }

    public Integer getCertificateNumber() {
        return certificateNumber;
    }

    public CalibrateCheckResultReturnDTO setCertificateNumber(Integer certificateNumber) {
        this.certificateNumber = certificateNumber;
        return this;
    }

    public Integer getReportNumber() {
        return reportNumber;
    }

    public CalibrateCheckResultReturnDTO setReportNumber(Integer reportNumber) {
        this.reportNumber = reportNumber;
        return this;
    }

    public List<DocumentDTO> getCertificateFileList() {
        return certificateFileList;
    }

    public CalibrateCheckResultReturnDTO setCertificateFileList(List<DocumentDTO> certificateFileList) {
        this.certificateFileList = certificateFileList;
        return this;
    }

    public List<DocumentDTO> getReportFileList() {
        return reportFileList;
    }

    public CalibrateCheckResultReturnDTO setReportFileList(List<DocumentDTO> reportFileList) {
        this.reportFileList = reportFileList;
        return this;
    }

    public List<CalibrateCheckResultReturn> getCalibrateCheckResultReturnList() {
        return calibrateCheckResultReturnList;
    }

    public CalibrateCheckResultReturnDTO setCalibrateCheckResultReturnList(List<CalibrateCheckResultReturn> calibrateCheckResultReturnList) {
        this.calibrateCheckResultReturnList = calibrateCheckResultReturnList;
        return this;
    }

    public Boolean getResult() {
        return result;
    }

    public CalibrateCheckResultReturnDTO setResult(Boolean result) {
        this.result = result;
        return this;
    }

    /**
     * 校准数据查看记录内部类
     **/
    @Schema(name = "校准数据查看记录内部类", description = "校准数据查看记录内部类")
    public static class CalibrateCheckResultReturn implements Serializable {
        /**
         * 校准数据详情ID
         */
        @Schema(description = "校准数据详情ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long detailId;

        /**
         * 校准设备名称
         */
        @Schema(description = "校准设备名称")
        private String facilityName;

        /**
         * 项目名称
         */
        @Schema(description = "项目名称", required = true)
        private String itemName;

        /**
         * 校准次数
         */
        @Schema(description = "校准次数，第几次")
        private Integer testTimes;


        /**
         * 校准标准，0：参考基值，1：是否正常
         */
        @Schema(description = "校准标准，0：参考基值，1：是否正常", required = true)
        private Integer category;

        /**
         * 范围，(10,50]
         */
        @Schema(description = "范围，(10,50]")
        private String qualifiedRange;

        /**
         * 单位
         */
        @Schema(description = "单位")
        private String unit;

        /**
         * 测试值（1：值，2：OK/NG）
         */
        @Schema(description = "测试值，1:2.2 2:OK/NG")
        private String number;

        public Long getDetailId() {
            return detailId;
        }

        public CalibrateCheckResultReturn setDetailId(Long detailId) {
            this.detailId = detailId;
            return this;
        }

        public String getFacilityName() {
            return facilityName;
        }

        public CalibrateCheckResultReturn setFacilityName(String facilityName) {
            this.facilityName = facilityName;
            return this;
        }

        public String getItemName() {
            return itemName;
        }

        public CalibrateCheckResultReturn setItemName(String itemName) {
            this.itemName = itemName;
            return this;
        }

        public Integer getTestTimes() {
            return testTimes;
        }

        public CalibrateCheckResultReturn setTestTimes(Integer testTimes) {
            this.testTimes = testTimes;
            return this;
        }

        public Integer getCategory() {
            return category;
        }

        public CalibrateCheckResultReturn setCategory(Integer category) {
            this.category = category;
            return this;
        }

        public String getQualifiedRange() {
            return qualifiedRange;
        }

        public CalibrateCheckResultReturn setQualifiedRange(String qualifiedRange) {
            this.qualifiedRange = qualifiedRange;
            return this;
        }

        public String getUnit() {
            return unit;
        }

        public CalibrateCheckResultReturn setUnit(String unit) {
            this.unit = unit;
            return this;
        }

        public String getNumber() {
            return number;
        }

        public CalibrateCheckResultReturn setNumber(String number) {
            this.number = number;
            return this;
        }
    }
}
