package net.airuima.calibrate.web.rest.procedure.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import net.airuima.calibrate.domain.procedure.CalibrateCheckResultDetail;
import net.airuima.rbase.dto.client.ClientCalibrateCheckResultSaveDTO;

import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2025, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Schema(description = "Rworker上传或者Excel导入保存台位校准数据参数DTO")
public class CalibrateCheckResultImportSaveDTO {

    /**
     * 台位编号
     */
    @Schema(description = "台位编号")
    @NotEmpty
    @Excel(name = "台位编码", orderNum = "1")
    private String workCellCode;

    /**
     * 测试人员
     */
    @Schema(description = "测试人员")
    @NotEmpty
    @Excel(name = "测试员工编号", orderNum = "2")
    private String tester;

    /**
     * 测试日期
     */
    @Schema(description = "测试日期", example = "2022-05-23 12:32:44")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotEmpty
    @Excel(name = "测试日期", orderNum = "3", format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime testTime;

    /**
     * 检测项目编码
     */
    @Schema(description = "检测项目编码")
    @Excel(name = "检测项目编码", orderNum = "4")
    private String checkItemCode;

    /**
     * 标准件子模块/通道
     */
    @Schema(description = "标准件子模块/通道")
    @Excel(name = "标准件子模块", orderNum = "5")
    private String subModule;

    /**
     * 标准件SN
     */
    @Schema(description = "标准件SN")
    @Excel(name = "标准件SN", orderNum = "6")
    private String sn;


    /**
     * 测试值
     */
    @Schema(description = "测试值")
    @Excel(name = "测试值", orderNum = "7")
    private String number;

    /**
     * 设备编码
     */
    @Schema(description = "设备编码")
    @Excel(name = "设备编码", orderNum = "8")
    private String facilityCode;

    /**
     * 校准程序
     **/
    @Schema(description = "校准程序")
    @NotEmpty
    @Excel(name = "校准程序", orderNum = "9")
    private ClientCalibrateCheckResultSaveDTO.ProcessEnum process;

    /**
     * 校准结果
     */
    @Schema(description = "校准结果")
    @Excel(name = "校准结果", orderNum = "10")
    private String result;

    /**
     * 校准单位
     */
    @Schema(description = "校准单位")
    @Excel(name = "校准单位", orderNum = "11")
    private String calibrateCompany;

    public CalibrateCheckResultImportSaveDTO() {

    }

    public CalibrateCheckResultImportSaveDTO(CalibrateCheckResultDetail calibrateCheckResultDetail) {
        this.sn = calibrateCheckResultDetail.getCalibrateCheckResult().getStandardPartDto().getSn();
        this.checkItemCode = calibrateCheckResultDetail.getCheckItemCode();
        this.number = calibrateCheckResultDetail.getNumber();
        this.subModule = calibrateCheckResultDetail.getSubModule();
        this.testTime = calibrateCheckResultDetail.getCalibrateCheckResult().getTestTime();
        this.tester = calibrateCheckResultDetail.getCalibrateCheckResult().getTester();
        this.workCellCode = calibrateCheckResultDetail.getCalibrateCheckResult().getWorkCellCode();
    }

    public String getWorkCellCode() {
        return workCellCode;
    }

    public CalibrateCheckResultImportSaveDTO setWorkCellCode(String workCellCode) {
        this.workCellCode = workCellCode;
        return this;
    }

    public String getTester() {
        return tester;
    }

    public CalibrateCheckResultImportSaveDTO setTester(String tester) {
        this.tester = tester;
        return this;
    }

    public LocalDateTime getTestTime() {
        return testTime;
    }

    public CalibrateCheckResultImportSaveDTO setTestTime(LocalDateTime testTime) {
        this.testTime = testTime;
        return this;
    }

    public String getCheckItemCode() {
        return checkItemCode;
    }

    public CalibrateCheckResultImportSaveDTO setCheckItemCode(String checkItemCode) {
        this.checkItemCode = checkItemCode;
        return this;
    }

    public String getSubModule() {
        return subModule;
    }

    public CalibrateCheckResultImportSaveDTO setSubModule(String subModule) {
        this.subModule = subModule;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public CalibrateCheckResultImportSaveDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public String getNumber() {
        return number;
    }

    public CalibrateCheckResultImportSaveDTO setNumber(String number) {
        this.number = number;
        return this;
    }

    public String getFacilityCode() {
        return facilityCode;
    }

    public CalibrateCheckResultImportSaveDTO setFacilityCode(String facilityCode) {
        this.facilityCode = facilityCode;
        return this;
    }

    public ClientCalibrateCheckResultSaveDTO.ProcessEnum getProcess() {
        return process;
    }

    public CalibrateCheckResultImportSaveDTO setProcess(ClientCalibrateCheckResultSaveDTO.ProcessEnum process) {
        this.process = process;
        return this;
    }

    public String getResult() {
        return result;
    }

    public CalibrateCheckResultImportSaveDTO setResult(String result) {
        this.result = result;
        return this;
    }

    public String getCalibrateCompany() {
        return calibrateCompany;
    }

    public CalibrateCheckResultImportSaveDTO setCalibrateCompany(String calibrateCompany) {
        this.calibrateCompany = calibrateCompany;
        return this;
    }
}
