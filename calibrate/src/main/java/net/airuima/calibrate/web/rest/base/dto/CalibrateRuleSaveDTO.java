package net.airuima.calibrate.web.rest.base.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 校准规则表Domain
 *
 * <AUTHOR>
 * @date 2022-07-04
 */
@Schema(name = "校准规则表DTO", description = "校准规则表DTO")
public class CalibrateRuleSaveDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "ID" ,requiredMode = Schema.RequiredMode.REQUIRED,type = "integer",format = "int64")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @Schema(description = "校准程序，0：内校，1：外校", requiredMode = Schema.RequiredMode.REQUIRED,type = "integer",format = "int32",maxLength = 11,defaultValue = "0")
    private Integer process;

    @Schema(description = "工位ID" ,requiredMode = Schema.RequiredMode.REQUIRED,type = "integer",format = "int64")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long workCellId;

    @Schema(description = "校准周期数", requiredMode = Schema.RequiredMode.REQUIRED,type = "integer",format = "int32",maxLength = 11,defaultValue = "0")
    private Integer periodNumber;

    @Schema(description = "校准周期单位(0:天，1:月，2:年)",requiredMode = Schema.RequiredMode.REQUIRED,type = "integer",format = "int32",maxLength = 11,defaultValue = "0")
    private Integer periodUnit;

    @Schema(description = "提醒周期数", requiredMode = Schema.RequiredMode.REQUIRED,type = "integer",format = "int32",maxLength = 11,defaultValue = "0")
    private Integer remindPeriodNumber;

    @Schema(description = "提醒周期单位(0:天，1:月，2:年)",requiredMode = Schema.RequiredMode.REQUIRED,type = "integer",format = "int32",maxLength = 11,defaultValue = "0")
    private Integer remindPeriodUnit;

    @ArraySchema(schema = @Schema(implementation = Long.class, description = "设备ID"))
    @JsonSerialize(using = ToStringSerializer.class)
    private List<Long> facilityIdList;


    @ArraySchema(schema = @Schema(implementation = CalibrateItemDTO.class, description = "校准项目"))
    private List<CalibrateItemDTO> calibrateItemList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getProcess() {
        return process;
    }

    public void setProcess(Integer process) {
        this.process = process;
    }

    public Long getWorkCellId() {
        return workCellId;
    }

    public void setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
    }

    public Integer getPeriodNumber() {
        return periodNumber;
    }

    public void setPeriodNumber(Integer periodNumber) {
        this.periodNumber = periodNumber;
    }

    public Integer getPeriodUnit() {
        return periodUnit;
    }

    public void setPeriodUnit(Integer periodUnit) {
        this.periodUnit = periodUnit;
    }

    public Integer getRemindPeriodNumber() {
        return remindPeriodNumber;
    }

    public void setRemindPeriodNumber(Integer remindPeriodNumber) {
        this.remindPeriodNumber = remindPeriodNumber;
    }

    public Integer getRemindPeriodUnit() {
        return remindPeriodUnit;
    }

    public void setRemindPeriodUnit(Integer remindPeriodUnit) {
        this.remindPeriodUnit = remindPeriodUnit;
    }

    public List<Long> getFacilityIdList() {
        return facilityIdList;
    }

    public void setFacilityIdList(List<Long> facilityIdList) {
        this.facilityIdList = facilityIdList;
    }

    public List<CalibrateItemDTO> getCalibrateItemList() {
        return calibrateItemList;
    }

    public void setCalibrateItemList(List<CalibrateItemDTO> calibrateItemList) {
        this.calibrateItemList = calibrateItemList;
    }

    @Schema(name = "校准项目内部类", description = "校准项目内部类")
    public static class CalibrateItemDTO implements Serializable {
        @Schema(description = "校准项目ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long calibrateItemId;

        @Schema(description = "校准次数")
        private Integer testTimes;

        public Long getCalibrateItemId() {
            return calibrateItemId;
        }

        public void setCalibrateItemId(Long calibrateItemId) {
            this.calibrateItemId = calibrateItemId;
        }

        public Integer getTestTimes() {
            return testTimes;
        }

        public void setTestTimes(Integer testTimes) {
            this.testTimes = testTimes;
        }
    }

}
