<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.6.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.6.xsd">
    <changeSet author="zhuhuawu (generated)" id="1737547006422-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="base_calibrate_item"/>
            </not>
        </preConditions>
        <createTable remarks="校准项目表" tableName="base_calibrate_item">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="code" remarks="项目编码" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="name" remarks="项目名称" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="category" remarks="校准标准，0：参考基值，1：是否正常" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="qualified_range" remarks="范围，(10,50]" type="VARCHAR(50)"/>
            <column name="unit" remarks="单位" type="VARCHAR(10)"/>
            <column defaultValueBoolean="true" name="enable" remarks="是否启用，0：未启用, 1：启用" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
        <createTable remarks="校准规则表" tableName="base_calibrate_rule">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="work_cell_id" remarks="工位ID" type="BIGINT"/>
            <column name="facility_id" remarks="设备ID" type="BIGINT"/>
            <column defaultValueNumeric="0" name="process" remarks="校准程序，0：内校，1：外校" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="1" name="period_number" remarks="校准周期数" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="period_unit" remarks="校准周期单位(0:天，1:月，2:年)" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="1" name="remind_period_number" remarks="提醒周期数" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="remind_period_unit" remarks="提醒周期单位(0:天，1:月，2:年)" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
        <createTable remarks="校准规则详情表" tableName="base_calibrate_rule_detail">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="calibrate_rule_id" remarks="校准规则ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="calibrate_item_id" remarks="校准项目ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="test_times" remarks="校准次数" type="TINYINT(3)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
        <createTable remarks="台位校准历史表" tableName="procedure_calibrate_check_result">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="work_cell_code" remarks="台位" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="standard_part_id" type="BIGINT"/>
            <column name="test_time" type="timestamp"/>
            <column name="tester" remarks="测试人员" type="VARCHAR(50)"/>
            <column defaultValueBoolean="true" name="result" remarks="预留字段(0:不合格;1:合格)" type="BIT(1)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column defaultValueBoolean="true" name="is_latest" remarks="是否为最新记录" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="work_cell_id" remarks="工位ID" type="BIGINT"/>
            <column name="facility_id" remarks="设备ID" type="BIGINT"/>
            <column name="standard_part_code" remarks="标准件CODE" type="VARCHAR(50)"/>
            <column defaultValueNumeric="0" name="process" remarks="校准程序，0：内校，1：外校" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="calibrate_company" remarks="校准单位" type="VARCHAR(100)"/>
            <column name="appearance" remarks="设备外观情况" type="VARCHAR(1000)"/>
            <column name="certificate_number" remarks="校准合格证数量" type="TINYINT(3)"/>
            <column name="report_number" remarks="校准证明报告书数量" type="TINYINT(3)"/>
        </createTable>
        <createTable remarks="台位校准历史数据明细" tableName="procedure_calibrate_check_result_detail">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="check_result_id" remarks="台位校准历史ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="check_item_code" remarks="检测项目编码" type="VARCHAR(50)"/>
            <column name="sub_module" remarks="标准件子模块" type="VARCHAR(50)"/>
            <column name="number" type="VARCHAR(100)"/>
            <column defaultValueBoolean="true" name="result" remarks="预留字段(0:不合格;1:合格)" type="BIT(1)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="calibrate_item_id" remarks="检测项目ID" type="BIGINT"/>
            <column defaultValueNumeric="1" name="test_times" remarks="第几次检测" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createTable remarks="设备校准状态表" tableName="procedure_calibrate_status">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="work_cell_id" remarks="工位ID" type="BIGINT"/>
            <column name="facility_id" remarks="设备ID" type="BIGINT"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="latest_operator_date" remarks="上次校准日期" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="next_operator_date" remarks="下次校准日期" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueBoolean="true" name="result" remarks="结果，0不合格, 1合格" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="tester" remarks="测试人员" type="VARCHAR(50)"/>
            <column defaultValueNumeric="0" name="deleted" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom2" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom3" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom4" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="custom5" remarks="定制字段" type="VARCHAR(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="process" remarks="校准程序，0：内校，1：外校" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="next_remind_date" remarks="下次提醒日期" type="timestamp">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint columnNames="code, deleted" constraintName="base_calibrate_item_unique" tableName="base_calibrate_item"/>
        <addUniqueConstraint columnNames="calibrate_rule_id, calibrate_item_id, deleted" constraintName="base_calibrate_rule_detail_unique" tableName="base_calibrate_rule_detail"/>
        <addUniqueConstraint columnNames="work_cell_id, facility_id, process, deleted" constraintName="base_calibrate_rule_unique" tableName="base_calibrate_rule"/>
        <addUniqueConstraint columnNames="work_cell_id, facility_id, deleted, process" constraintName="procedure_calibrate_status_unique" tableName="procedure_calibrate_status"/>
        <createIndex indexName="base_calibrate_item_code_index" tableName="base_calibrate_item">
            <column name="code"/>
        </createIndex>
        <createIndex indexName="base_calibrate_rule_detail_calibrate_item_id" tableName="base_calibrate_rule_detail">
            <column name="calibrate_item_id"/>
        </createIndex>
        <createIndex indexName="base_calibrate_rule_detail_calibrate_rule_id" tableName="base_calibrate_rule_detail">
            <column name="calibrate_rule_id"/>
        </createIndex>
        <createIndex indexName="base_calibrate_rule_facility_id_index" tableName="base_calibrate_rule">
            <column name="facility_id"/>
        </createIndex>
        <createIndex indexName="base_calibrate_rule_work_cell_id_index" tableName="base_calibrate_rule">
            <column name="work_cell_id"/>
        </createIndex>
        <createIndex indexName="calibrate_check_result_detail_check_result_id_index" tableName="procedure_calibrate_check_result_detail">
            <column name="check_result_id"/>
        </createIndex>
        <createIndex indexName="calibrate_check_result_detail_item_code_index" tableName="procedure_calibrate_check_result_detail">
            <column name="check_item_code"/>
        </createIndex>
        <createIndex indexName="calibrate_check_result_detail_sub_module_index" tableName="procedure_calibrate_check_result_detail">
            <column name="sub_module"/>
        </createIndex>
        <createIndex indexName="procedure_calibrate_check_result_detail_calibrate_item_id_index" tableName="procedure_calibrate_check_result_detail">
            <column name="calibrate_item_id"/>
        </createIndex>
        <createIndex indexName="procedure_calibrate_check_result_facility_id_index" tableName="procedure_calibrate_check_result">
            <column name="facility_id"/>
        </createIndex>
        <createIndex indexName="procedure_calibrate_check_result_work_cell_id_index" tableName="procedure_calibrate_check_result">
            <column name="work_cell_id"/>
        </createIndex>
        <createIndex indexName="procedure_calibrate_state_facility_id_index" tableName="procedure_calibrate_status">
            <column name="facility_id"/>
        </createIndex>
        <createIndex indexName="procedure_calibrate_state_work_cell_id_index" tableName="procedure_calibrate_status">
            <column name="work_cell_id"/>
        </createIndex>
        <createIndex indexName="procedure_work_cell_calibrate_result_standard_part_index" tableName="procedure_calibrate_check_result">
            <column name="standard_part_id"/>
        </createIndex>
        <createIndex indexName="procedure_work_cell_calibrate_result_work_cell_index" tableName="procedure_calibrate_check_result">
            <column name="work_cell_code"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
