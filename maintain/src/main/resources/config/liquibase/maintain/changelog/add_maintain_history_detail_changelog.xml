<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="202502191709" author="YangS">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="procedure_maintain_history_detail" columnName="rework_step_id"/>
            </not>
        </preConditions>
        <addColumn tableName="procedure_maintain_history_detail">
            <column defaultValueNumeric="0" name="number" remarks="维修数量" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="-1" name="result" remarks="处理方式：-1:未处理,0：报废，1：返工" type="TINYINT(3)"/>
            <column name="maintain_case_id" type="BIGINT" remarks="维修分析方案"/>
            <column name="unqualified_item_id" remarks="不良项Id" type="BIGINT"/>
            <column defaultValueNumeric="-1" name="rework_category" remarks="返修流程(0:返修工艺路线, 1:原工艺路线)" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="rework_step_id" remarks="开始返工的工序ID" type="BIGINT"/>
            <column defaultValue="0" name="is_replace_material" remarks="是否替换料(0:否;1:是)" type="BIT(1)"/>
            <column name="ws_rework_id" remarks="在线返修单和正常单关联信息" type="BIGINT"/>
        </addColumn>

        <createIndex tableName="procedure_maintain_history_detail" indexName="maintain_history_detail_ws_rework_index">
            <column name="ws_rework_id"/>
        </createIndex>
        <sql>
            UPDATE procedure_maintain_history_detail d
                JOIN procedure_maintain_history h
            ON d.history_id = h.id
                SET
                    d.maintain_case_id = h.maintain_case_id,
                    d.number = h.number,
                    d.result = h.result,
                    d.ws_rework_id = h.ws_rework_id,
                    d.unqualified_item_id = h.unqualified_item_id,
                    d.rework_category = h.rework_category,
                    d.rework_step_id = h.rework_step_id,
                    d.is_replace_material = h.is_replace_material;
        </sql>
    </changeSet>


</databaseChangeLog>
