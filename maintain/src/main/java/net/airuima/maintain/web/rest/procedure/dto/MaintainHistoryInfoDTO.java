package net.airuima.maintain.web.rest.procedure.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.maintain.domain.base.MaintainCase;
import net.airuima.maintain.domain.procedure.MaintainHistory;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.process.WorkFlowDTO;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/3/23
 */
@Schema(description = "维修分析信息")
public class MaintainHistoryInfoDTO {
    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * sn
     */
    @Schema(description = "sn")
    private String sn;
    /**
     * 容器编码
     */
    @Schema(description = "容器编码")
    private String containerCode;
    /**
     * 工单号
     */
    @Schema(description = "子工单号/工单号")
    private String wsSerialNumber;
    /**
     * 工单id
     */
    @Schema(description = "子工单ID/工单ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long wsId;

    /**
     * 产品型号
     */
    @Schema(description = "产品型号")
    private String productCode;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    private String clientName;

    /**
     * 维修数量
     */
    @Schema(description = "维修数量")
    private Integer number;

    /**
     * 不良产生工序Info
     */
    @Schema(description = "不良产生工序Info")
    private StepInfo stepInfo;

    /**
     * 不良项目
     */
    @Schema(description = "不良项目")
    private List<UnqualifiedItemDetailInfo> unqualifiedItemDetailInfoList;

    /**
     * 维修方案info
     */
    @Schema(description = "维修方案info")
    private List<MaintainCaseInfo> maintainCaseInfoList;

    public Long getId() {
        return id;
    }

    public MaintainHistoryInfoDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public MaintainHistoryInfoDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public MaintainHistoryInfoDTO setContainerCode(String containerCode) {
        this.containerCode = containerCode;
        return this;
    }

    public String getWsSerialNumber() {
        return wsSerialNumber;
    }

    public MaintainHistoryInfoDTO setWsSerialNumber(String wsSerialNumber) {
        this.wsSerialNumber = wsSerialNumber;
        return this;
    }

    public Long getWsId() {
        return wsId;
    }

    public MaintainHistoryInfoDTO setWsId(Long wsId) {
        this.wsId = wsId;
        return this;
    }

    public String getProductCode() {
        return productCode;
    }

    public MaintainHistoryInfoDTO setProductCode(String productCode) {
        this.productCode = productCode;
        return this;
    }

    public String getClientName() {
        return clientName;
    }

    public MaintainHistoryInfoDTO setClientName(String clientName) {
        this.clientName = clientName;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public MaintainHistoryInfoDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public StepInfo getStepInfo() {
        return stepInfo;
    }

    public MaintainHistoryInfoDTO setStepInfo(StepInfo stepInfo) {
        this.stepInfo = stepInfo;
        return this;
    }

    public List<UnqualifiedItemDetailInfo> getUnqualifiedItemDetailInfoList() {
        return unqualifiedItemDetailInfoList;
    }

    public MaintainHistoryInfoDTO setUnqualifiedItemDetailInfoList(List<UnqualifiedItemDetailInfo> unqualifiedItemDetailInfoList) {
        this.unqualifiedItemDetailInfoList = unqualifiedItemDetailInfoList;
        return this;
    }

    public List<MaintainCaseInfo> getMaintainCaseInfoList() {
        return maintainCaseInfoList;
    }

    public MaintainHistoryInfoDTO setMaintainCaseInfoList(List<MaintainCaseInfo> maintainCaseInfoList) {
        this.maintainCaseInfoList = maintainCaseInfoList;
        return this;
    }

    public MaintainHistoryInfoDTO() {

    }


    public MaintainHistoryInfoDTO(MaintainHistory maintainHistory) {
        this.id = maintainHistory.getId();
        WorkSheet workSheet = maintainHistory.getWorkSheet() == null ? maintainHistory.getSubWorkSheet().getWorkSheet() : maintainHistory.getWorkSheet();
        this.wsId = null !=  maintainHistory.getSubWorkSheet()? maintainHistory.getSubWorkSheet().getId():workSheet.getId();
        this.clientName = workSheet.getClientDTO() == null ? null:workSheet.getClientDTO().getName();
        this.wsSerialNumber = null != maintainHistory.getSubWorkSheet()?maintainHistory.getSubWorkSheet().getSerialNumber():workSheet.getSerialNumber();
        this.productCode = workSheet.getPedigree().getCode();
        this.number = maintainHistory.getNumber();
        this.stepInfo = new StepInfo(maintainHistory.getStep());
        this.sn = maintainHistory.getSnWorkStatus() == null ? null : maintainHistory.getSnWorkStatus().getSn();
        this.containerCode = maintainHistory.getContainerDetail() == null ? null :maintainHistory.getContainerDetail().getContainerCode();
    }

    @Schema(description = "维修分析方案")
    public static class MaintainCaseInfo{
        @Schema(description = "维修分析方案id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;
        @Schema(description = "维修分析方案名称")
        private String name;
        @Schema(description = "维修分析方案编码")
        private String code;
        /**
         * 原工艺路线info
         */
        @Schema(description = "原工艺路线info")
        private WorkFlowInfo originFlowInfo;
        /**
         * 返修工艺路线info
         */
        @Schema(description = "返修工艺路线info")
        private List<WorkFlowInfo> reWorkFlowInfoList;

        public MaintainCaseInfo() {
        }

        public MaintainCaseInfo(MaintainCase maintainCase) {
            this.id = maintainCase.getId();
            this.name = maintainCase.getName();
            this.code = maintainCase.getCode();
        }

        public Long getId() {
            return id;
        }

        public MaintainCaseInfo setId(Long id) {
            this.id = id;
            return this;
        }

        public String getName() {
            return name;
        }

        public MaintainCaseInfo setName(String name) {
            this.name = name;
            return this;
        }

        public String getCode() {
            return code;
        }

        public MaintainCaseInfo setCode(String code) {
            this.code = code;
            return this;
        }

        public WorkFlowInfo getOriginFlowInfo() {
            return originFlowInfo;
        }

        public MaintainCaseInfo setOriginFlowInfo(WorkFlowInfo originFlowInfo) {
            this.originFlowInfo = originFlowInfo;
            return this;
        }

        public List<WorkFlowInfo> getReWorkFlowInfoList() {
            return reWorkFlowInfoList;
        }

        public MaintainCaseInfo setReWorkFlowInfoList(List<WorkFlowInfo> reWorkFlowInfoList) {
            this.reWorkFlowInfoList = reWorkFlowInfoList;
            return this;
        }
    }

    @Schema(description = "不良项目处理")
    public static class UnqualifiedItemDetailInfo{

        @Schema(description = "处理方式：2：报废，3:维修分析")
        private Integer type;

        @Schema(description = "不良列表")
        private List<UnqualifiedItemInfo> unqualifiedItemInfoList;

        public Integer getType() {
            return type;
        }

        public UnqualifiedItemDetailInfo setType(Integer type) {
            this.type = type;
            return this;
        }

        public List<UnqualifiedItemInfo> getUnqualifiedItemInfoList() {
            return unqualifiedItemInfoList;
        }

        public UnqualifiedItemDetailInfo setUnqualifiedItemInfoList(List<UnqualifiedItemInfo> unqualifiedItemInfoList) {
            this.unqualifiedItemInfoList = unqualifiedItemInfoList;
            return this;
        }
    }

    @Schema(description = "不良项目")
    public static class UnqualifiedItemInfo{
        @Schema(description = "不良项目id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;
        @Schema(description = "不良项目名称")
        private String name;
        @Schema(description = "不良项目编码")
        private String code;

        public UnqualifiedItemInfo() {
        }

        public UnqualifiedItemInfo(UnqualifiedItem unqualifiedItem) {
            this.id = unqualifiedItem.getId();
            this.name = unqualifiedItem.getName();
            this.code = unqualifiedItem.getCode();
        }

        public Long getId() {
            return id;
        }

        public UnqualifiedItemInfo setId(Long id) {
            this.id = id;
            return this;
        }

        public String getName() {
            return name;
        }

        public UnqualifiedItemInfo setName(String name) {
            this.name = name;
            return this;
        }

        public String getCode() {
            return code;
        }

        public UnqualifiedItemInfo setCode(String code) {
            this.code = code;
            return this;
        }
    }

    @Schema(description = "工艺路线info")
    public static class WorkFlowInfo{
        @Schema(description = "工艺路线id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;
        @Schema(description = "工艺路线编码")
        private String workFlowCode;
        @Schema(description = "工艺路线名称")
        private String workFlowName;
        @Schema(description = "工艺路线工序列表")
        private WorkFlowDTO workFlowDTO;

        public WorkFlowInfo() {
        }

        public WorkFlowInfo(WorkFlow workFlow) {
            this.id = workFlow.getId();
            this.workFlowCode = workFlow.getCode();
            this.workFlowName = workFlow.getName();
        }

        public Long getId() {
            return id;
        }

        public WorkFlowInfo setId(Long id) {
            this.id = id;
            return this;
        }

        public String getWorkFlowCode() {
            return workFlowCode;
        }

        public WorkFlowInfo setWorkFlowCode(String workFlowCode) {
            this.workFlowCode = workFlowCode;
            return this;
        }

        public String getWorkFlowName() {
            return workFlowName;
        }

        public WorkFlowInfo setWorkFlowName(String workFlowName) {
            this.workFlowName = workFlowName;
            return this;
        }

        public WorkFlowDTO getWorkFlowDTO() {
            return workFlowDTO;
        }

        public WorkFlowInfo setWorkFlowDTO(WorkFlowDTO workFlowDTO) {
            this.workFlowDTO = workFlowDTO;
            return this;
        }
    }

    @Schema(description = "不良产生工序Info")
    public static class StepInfo{
        @Schema(description = "工序id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;
        @Schema(description = "工序名称")
        private String name;
        @Schema(description = "工序编码")
        private String code;

        public StepInfo() {
        }

        public StepInfo(Step step) {
            this.id = step.getId();
            this.name = step.getName();
            this.code = step.getCode();
        }

        public Long getId() {
            return id;
        }

        public StepInfo setId(Long id) {
            this.id = id;
            return this;
        }

        public String getName() {
            return name;
        }

        public StepInfo setName(String name) {
            this.name = name;
            return this;
        }

        public String getCode() {
            return code;
        }

        public StepInfo setCode(String code) {
            this.code = code;
            return this;
        }
    }

}
