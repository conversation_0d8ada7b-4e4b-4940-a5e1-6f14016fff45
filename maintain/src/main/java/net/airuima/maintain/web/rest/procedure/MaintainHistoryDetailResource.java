package net.airuima.maintain.web.rest.procedure;

import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.maintain.domain.procedure.MaintainHistoryDetail;
import net.airuima.maintain.service.procedure.MaintainHistoryDetailService;
import net.airuima.maintain.web.rest.procedure.dto.MaintainHistoryDetailDTO;
import net.airuima.util.ResponseData;
import net.airuima.web.BaseResource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/10/12
 */
@RestController
@AppKey("RmesService")
@RequestMapping("/api/maintain-history-details")
@AuthorityRegion("维修分析")
@FuncInterceptor("RepaireAnalysis")
public class MaintainHistoryDetailResource extends BaseResource<MaintainHistoryDetail> {

    private final MaintainHistoryDetailService maintainHistoryDetailService;

    public MaintainHistoryDetailResource(MaintainHistoryDetailService maintainHistoryDetailService) {
        this.maintainHistoryDetailService = maintainHistoryDetailService;
        this.mapUri = "/api/maintain-history-details";
    }

    /**
     * 通过维修记录id 获取维修记录详情列表
     * @param maintainHistoryId 维修记录id
     * <AUTHOR>
     * @date  2022/10/18
     */
    @GetMapping("/maintainHistoryId/{maintainHistoryId}")
    @PreAuthorize("hasAnyAuthority('MAINTAINHISTORY_READ') or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    public ResponseEntity<ResponseData<List<MaintainHistoryDetailDTO>>> findMaintainHistoryDetails(@PathVariable(value = "maintainHistoryId") Long maintainHistoryId){
        return ResponseData.ok(maintainHistoryDetailService.findMaintainHistoryDetails(maintainHistoryId));
    }
}
