package net.airuima.maintain.web.rest.base;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AppKey;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.maintain.domain.base.MaintainCase;
import net.airuima.maintain.service.base.MaintainCaseService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.web.ProtectBaseResource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 * 维修分析方案Resource
 * <AUTHOR>
 * @date 2022/9/27
 */
@Tag(name = "维修分析方案Resource")
@RestController
@AppKey("RmesService")
@RequestMapping("/api/maintain-cases")
@AuthorityRegion("维修分析")
@FuncInterceptor("RepaireAnalysis")
public class MaintainCaseResource extends ProtectBaseResource<MaintainCase> {

    private MaintainCaseService maintainCaseService;

    public MaintainCaseResource(MaintainCaseService maintainCaseService) {
        this.maintainCaseService = maintainCaseService;
        this.mapUri = "/api/maintain-cases";
    }


    /**
     * @description 根据维系分析方案编码或者名称获取启用的维修分析方案
     * @param: text 编码或者名称
     * @param: size 最大返回数据条数
     * @param isEnable 启用
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_READ')) or hasAnyAuthority('ROLE_ADMIN') or @sc.checkSecurity()")
    @Operation(description = "根据维修分析方案编码或者名称获取框图信息", parameters = {
            @Parameter(name = "text",description = "维修分析方案名称或编码",required = true, schema = @Schema(type = "string"),in = ParameterIn.QUERY),
            @Parameter(name = "isEnable",description = "是否启用" , schema = @Schema(type = "boolean"),in = ParameterIn.QUERY),
            @Parameter(name = "size",description = "返回前N条匹配的数据",required = true, schema = @Schema(type = "integer",format = "int32"), in = ParameterIn.QUERY)
    })
    @GetMapping("/byNameOrCode")
    public ResponseEntity<ResponseData<List<MaintainCase>>> findByNameOrCode(@RequestParam(value = "text") String text,
                                                                            @RequestParam(value = "size") Integer size, @RequestParam(value = "isEnable",required = false) Boolean isEnable) {
        return ResponseData.ok(maintainCaseService.findByCodeOrName(text, size,isEnable));
    }

    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(entityName, authority, "维修分析方案");
    }
}
