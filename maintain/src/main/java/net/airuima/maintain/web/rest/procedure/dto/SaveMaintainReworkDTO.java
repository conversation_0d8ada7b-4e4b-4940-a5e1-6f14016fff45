package net.airuima.maintain.web.rest.procedure.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.maintain.domain.procedure.MaintainHistory;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;

import java.util.Objects;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/3/23
 */
@Schema(description = "保存返工单维修分析信息")
public class SaveMaintainReworkDTO {

    /**
     * 工单
     */
    @Schema(description = "工单")
    private WorkSheet workSheet;

    /**
     * 维修分析历史
     */
    @Schema(description = "维修分析历史")
    private MaintainHistory maintainHistory;

    /**
     * 工艺路线
     */
    @Schema(description = "工艺路线")
    private WorkFlow workFlow;

    /**
     * 起始工序
     */
    @Schema(description = "起始工序")
    private Step step;

    /**
     * 返修流程(0:返修工艺路线, 1:原工艺路线)
     */
    @Schema(description = "返修流程(0:返修工艺路线, 1:原工艺路线)")
    private Integer reworkCategory;

    /**
     * 不良项目
     */
    @Schema(description = "不良项目")
    private UnqualifiedItem unqualifiedItem;

    /**
     * 返修数量
     */
    @Schema(description = "返修数量")
    private Integer number;


    public SaveMaintainReworkDTO() {
    }

    public SaveMaintainReworkDTO(SaveMaintainReworkDTO maintainReworkDto) {
        this.workSheet = maintainReworkDto.getWorkSheet();
        this.maintainHistory = maintainReworkDto.getMaintainHistory();
        this.unqualifiedItem = maintainReworkDto.getUnqualifiedItem();
        this.number = maintainReworkDto.getNumber();
        this.workFlow = maintainReworkDto.workFlow;
        this.step = maintainReworkDto.getStep();
        this.reworkCategory = maintainReworkDto.getReworkCategory();
    }

    public SaveMaintainReworkDTO(WorkSheet workSheet, MaintainHistory maintainHistory, WorkFlow workFlow, Step step, Integer reworkCategory,UnqualifiedItem unqualifiedItem,Integer number) {
        this.workSheet = workSheet;
        this.maintainHistory = maintainHistory;
        this.workFlow = workFlow;
        this.step = step;
        this.reworkCategory = reworkCategory;
        this.unqualifiedItem = unqualifiedItem;
        this.number = number;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public SaveMaintainReworkDTO setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public MaintainHistory getMaintainHistory() {
        return maintainHistory;
    }

    public SaveMaintainReworkDTO setMaintainHistory(MaintainHistory maintainHistory) {
        this.maintainHistory = maintainHistory;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public SaveMaintainReworkDTO setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public SaveMaintainReworkDTO setStep(Step step) {
        this.step = step;
        return this;
    }

    public Integer getReworkCategory() {
        return reworkCategory;
    }

    public Integer getNumber() {
        return number;
    }

    public SaveMaintainReworkDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public SaveMaintainReworkDTO setReworkCategory(Integer reworkCategory) {
        this.reworkCategory = reworkCategory;
        return this;
    }

    public UnqualifiedItem getUnqualifiedItem() {
        return unqualifiedItem;
    }

    public SaveMaintainReworkDTO setUnqualifiedItem(UnqualifiedItem unqualifiedItem) {
        this.unqualifiedItem = unqualifiedItem;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SaveMaintainReworkDTO that = (SaveMaintainReworkDTO) o;
        return Objects.equals(workSheet, that.workSheet) && Objects.equals(workFlow, that.workFlow) && Objects.equals(step, that.step) && Objects.equals(unqualifiedItem, that.unqualifiedItem);
    }

    @Override
    public int hashCode() {
        return Objects.hash(workSheet, workFlow, step, unqualifiedItem);
    }
}
