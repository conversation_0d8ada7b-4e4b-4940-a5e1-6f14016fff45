package net.airuima.maintain.repository.procedure;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.maintain.domain.procedure.MaintainMaterialExchange;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/9/27
 */
@Repository
public interface MaintainMaterialExchangeRepository extends LogicDeleteableRepository<MaintainMaterialExchange>,
        EntityGraphJpaSpecificationExecutor<MaintainMaterialExchange>, EntityGraphJpaRepository<MaintainMaterialExchange, Long> {

    /**
     * 通过维修历史记录获取退补料历史记录
     * @param maintainHistoryId 维修历史主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/10/10
     * @return java.util.List<net.airuima.rbase.domain.procedure.maintaincase.MaintainMaterialExchange> 退补料清单列表
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    List<MaintainMaterialExchange> findByMaintainHistoryIdAndDeleted(Long maintainHistoryId,Long deleted);
}
