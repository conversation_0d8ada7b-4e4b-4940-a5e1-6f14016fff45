package net.airuima.maintain.repository.base;

import net.airuima.maintain.domain.base.MaintainCase;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/9/27
 */
@Repository
public interface MaintainCaseRepository extends LogicDeleteableRepository<MaintainCase>,
        JpaSpecificationExecutor<MaintainCase>, JpaRepository<MaintainCase, Long> {

    /**
     * 根据主键id获取 维修分析方案
     * @param id 维修分析方案主键id
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/10/8
     * @return java.util.Optional<net.airuima.rbase.domain.base.maintiancase.MaintainCase> 维修分析方案
     */
    Optional<MaintainCase> findByIdAndDeleted(Long id,Long deleted);

    /**
     * 根据维修方案编码获取 维修分析方案
     * @param code 维修方案编码
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/10/8
     * @return java.util.Optional<net.airuima.rbase.domain.base.maintiancase.MaintainCase> 维修分析方案
     */
    Optional<MaintainCase> findByCodeAndDeleted(String code,Long deleted);

    /**
     * 根据 维修方案主键id列表 获取维修方案列表
     * @param ids 维修方案主键id列表
     * @param deleted 逻辑删除
     * <AUTHOR>
     * @date  2022/10/14
     * @return java.util.List<net.airuima.rbase.domain.base.maintiancase.MaintainCase> 维修分析方案列表
     */
    List<MaintainCase> findByIdInAndDeleted(List<Long> ids,Long deleted);

    /**
     * 根据维修分析方案（名称/编码）获取维修分析方案
     * @param text 名称/编码
     * @param pageable  分页条件
     * @return org.springframework.data.domain.Page<net.airuima.rbase.domain.base.maintiancase.MaintainCase> 维修分析方案列表
     * <AUTHOR>
     * @date  2022/10/18
     */
    @Query("select mc from MaintainCase mc where (mc.code like concat('%',?1,'%') or mc.name like concat('%',?1,'%')) and mc.deleted=0L ")
    Page<MaintainCase> findByNameOrCode(String text,Pageable pageable);
}
