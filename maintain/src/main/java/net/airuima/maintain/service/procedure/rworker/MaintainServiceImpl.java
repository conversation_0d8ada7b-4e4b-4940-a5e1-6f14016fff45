package net.airuima.maintain.service.procedure.rworker;

import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.maintain.domain.base.MaintainCase;
import net.airuima.maintain.domain.base.PedigreeMaintainCase;
import net.airuima.maintain.domain.procedure.MaintainHistory;
import net.airuima.maintain.domain.procedure.MaintainHistoryDetail;
import net.airuima.maintain.domain.procedure.MaintainMaterialExchange;
import net.airuima.maintain.repository.procedure.MaintainHistoryDetailRepository;
import net.airuima.maintain.repository.procedure.MaintainHistoryRepository;
import net.airuima.maintain.repository.procedure.MaintainMaterialExchangeRepository;
import net.airuima.maintain.service.base.PedigreeMaintainCaseService;
import net.airuima.maintain.service.procedure.RworkerMaintainService;
import net.airuima.maintain.service.procedure.dto.HandleUnqualifiedItemDetailDTO;
import net.airuima.maintain.web.rest.procedure.dto.MaintainAnalyseDTO;
import net.airuima.maintain.web.rest.procedure.dto.MaintainHistoryInfoDTO;
import net.airuima.maintain.web.rest.procedure.dto.MaintainHistorySaveDTO;
import net.airuima.maintain.web.rest.procedure.dto.SaveMaintainReworkDTO;
import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.constant.MaintainEnum;
import net.airuima.rbase.constant.SnWorkStatusEnum;
import net.airuima.rbase.domain.base.pedigree.PedigreeConfig;
import net.airuima.rbase.domain.base.process.Step;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.process.WorkFlowStep;
import net.airuima.rbase.domain.base.quality.UnqualifiedItem;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.aps.WsRework;
import net.airuima.rbase.domain.procedure.batch.*;
import net.airuima.rbase.domain.procedure.report.StaffPerform;
import net.airuima.rbase.domain.procedure.report.StaffPerformUnqualifiedItem;
import net.airuima.rbase.domain.procedure.single.SnUnqualifiedItem;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.dto.process.StepDTO;
import net.airuima.rbase.dto.process.WorkFlowDTO;
import net.airuima.rbase.dto.rule.SerialNumberDTO;
import net.airuima.rbase.dto.rule.SerialNumberHistoryDTO;
import net.airuima.rbase.dto.rworker.process.dto.RworkerStepProcessBaseDTO;
import net.airuima.rbase.proxy.rule.RbaseSerialNumberProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeConfigRepository;
import net.airuima.rbase.repository.base.process.StepRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.repository.base.process.WorkFlowStepRepository;
import net.airuima.rbase.repository.base.quality.UnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WsReworkRepository;
import net.airuima.rbase.repository.procedure.batch.*;
import net.airuima.rbase.repository.procedure.report.StaffPerformRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformUnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.single.SnUnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkStatusRepository;
import net.airuima.rbase.service.base.process.WorkFlowStepService;
import net.airuima.rbase.service.common.CommonService;
import net.airuima.rbase.service.procedure.aps.SubWorkSheetService;
import net.airuima.rbase.service.procedure.aps.WorkSheetService;
import net.airuima.rbase.service.procedure.aps.WsReworkService;
import net.airuima.rbase.service.procedure.quality.CheckHistoryService;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.rworker.service.rworker.process.IBatchProcessSaveService;
import net.airuima.util.ResponseException;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/3/23
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(0)
public class MaintainServiceImpl implements RworkerMaintainService {

    private final Logger log = LoggerFactory.getLogger(MaintainServiceImpl.class);


    @Autowired
    private SnWorkStatusRepository snWorkStatusRepository;
    @Autowired
    private ContainerRepository containerRepository;
    @Autowired
    private MaintainHistoryRepository maintainHistoryRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private MaintainHistoryDetailRepository maintainHistoryDetailRepository;
    @Autowired
    private PedigreeMaintainCaseService pedigreeMaintainCaseService;
    @Autowired
    private WorkFlowRepository workFlowRepository;
    @Autowired
    private StepRepository stepRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private PedigreeConfigRepository pedigreeConfigRepository;
    @Autowired
    private WsMaterialRepository wsMaterialRepository;
    @Autowired
    private WsReworkService wsReworkService;
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private WsReworkRepository wsReworkRepository;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private WorkSheetService workSheetService;
    @Autowired
    private WorkFlowStepService workFlowStepService;
    @Autowired
    private MaintainMaterialExchangeRepository maintainMaterialExchangeRepository;
    @Autowired
    private ContainerDetailUnqualifiedItemRepository containerDetailUnqualifiedItemRepository;
    @Autowired
    private WsStepUnqualifiedItemRepository wsStepUnqualifiedItemRepository;
    @Autowired
    private StaffPerformRepository staffPerformRepository;
    @Autowired
    private StaffPerformUnqualifiedItemRepository staffPerformUnqualifiedItemRepository;
    @Autowired
    private SnWorkDetailRepository snWorkDetailRepository;
    @Autowired
    private SnUnqualifiedItemRepository snUnqualifiedItemRepository;
    @Autowired
    private SubWorkSheetService subWorkSheetService;
    @Autowired
    private WorkFlowStepRepository workFlowStepRepository;
    @Autowired
    private RbaseSerialNumberProxy rbaseSerialNumberProxy;
    @Autowired
    private CheckHistoryService checkHistoryService;
    @Autowired
    private UnqualifiedItemRepository unqualifiedItemRepository;
    @Autowired
    private IBatchProcessSaveService[] iBatchProcessSaveServices;


    /**
     * 获取维修分析信息
     *
     * @param maintainAnalyseDTO RWorker请求维修分析参数DTO
     * @return net.airuima.rbase.web.rest.procedure.maintaincase.dto.MaintainHistoryInfoDTO 维修分析信息
     */
    @Override
    public MaintainHistoryInfoDTO getMaintainAnalyseInfo(MaintainAnalyseDTO maintainAnalyseDTO) {
        //验证数据的合法性
        if (maintainAnalyseDTO.getMaintainType() == null) {
            throw new ResponseException("error.MaintainTypeNotExist", "请先确定维修类型");
        }
        if (!ValidateUtils.isValid(maintainAnalyseDTO.getMaintainTypeCode())) {
            throw new ResponseException("error.MaintainTypeCodeNotExist", "请求参数未定义");
        }
        if (MaintainEnum.MAINTAIN_TYPE_SN.getStatus() == maintainAnalyseDTO.getMaintainType()) {
            return this.getSnStepInfo(maintainAnalyseDTO.getMaintainTypeCode());
        }
        if (MaintainEnum.MAINTAIN_TYPE_CONTAINER.getStatus() == maintainAnalyseDTO.getMaintainType()) {
            return this.getContainerStepInfo(maintainAnalyseDTO.getMaintainTypeCode());
        }
        if (MaintainEnum.MAINTAIN_TYPE_WORKSHEET.getStatus() == maintainAnalyseDTO.getMaintainType()) {
            return this.getWorkSheetStepInfo(maintainAnalyseDTO.getMaintainTypeCode());
        }
        return null;
    }

    /**
     * 保存维修历史记录
     *
     * @param maintainHistorySaveDto Rworker保存维修分析记录信息
     */
    @Override
    public void saveMaintainAnalyseInfo(MaintainHistorySaveDTO maintainHistorySaveDto) {
        if (maintainHistorySaveDto == null || maintainHistorySaveDto.getMaintainType() == null || !ValidateUtils.isValid(maintainHistorySaveDto.getMaintainAnalyseInfoList())) {
            throw new ResponseException("error.MaintainHistoryParamException", "维修分析参数异常");
        }
        List<String> maintainTypeCodeList = maintainHistorySaveDto.getMaintainAnalyseInfoList().stream().map(MaintainHistorySaveDTO.MaintainAnalyseInfo::getMaintainTypeCode)
                .filter(ValidateUtils::isValid).collect(Collectors.toList());
        if (!ValidateUtils.isValid(maintainTypeCodeList)) {
            throw new ResponseException("error.MaintainCodeEmpty", "维修分析类型码为空");
        }
        BaseClientDTO baseClientDTO = null;
        Integer maintainType = maintainHistorySaveDto.getMaintainType();
        if (maintainType == MaintainEnum.MAINTAIN_TYPE_SN.getStatus()) {
            List<SnWorkStatus> snWorkStatuses = snWorkStatusRepository.findBySnInAndDeleted(maintainTypeCodeList, Constants.LONG_ZERO);
            baseClientDTO = this.validDataIsNull(snWorkStatuses, maintainTypeCodeList, "SnWorkStatusNotExist", "sn生产状态信息不存在");
        }
        if (maintainType == MaintainEnum.MAINTAIN_TYPE_CONTAINER.getStatus()) {
            List<Container> containerList = containerRepository.findByCodeInAndDeleted(maintainTypeCodeList, Constants.LONG_ZERO);
            if (!ValidateUtils.isValid(containerList) || maintainTypeCodeList.size() != containerList.size()) {
                baseClientDTO = this.validDataIsNull(containerList, maintainTypeCodeList, "ContainerNotExist", "容器信息不存在");
            }
        }
        boolean subWsProductionMode = commonService.subWsProductionMode();
        if (maintainType == MaintainEnum.MAINTAIN_TYPE_WORKSHEET.getStatus()) {
            if (subWsProductionMode) {
                List<SubWorkSheet> subWorkSheets = subWorkSheetRepository.findBySerialNumberInAndDeleted(maintainTypeCodeList, Constants.LONG_ZERO);
                baseClientDTO = this.validDataIsNull(subWorkSheets, maintainTypeCodeList, "SubWorkSheetNotExist", "子工单信息不存在");
            } else {
                List<WorkSheet> workSheetList = workSheetRepository.findBySerialNumberList(maintainTypeCodeList);
                baseClientDTO = this.validDataIsNull(workSheetList, maintainTypeCodeList, "WorkSheetNotExist", "工单信息不存在");
            }
        }
        if (null != baseClientDTO) {
            throw new ResponseException(baseClientDTO.getStatus(), baseClientDTO.getMessage());
        }
        this.saveAnalyseInfo(maintainHistorySaveDto, subWsProductionMode);
    }

    /**
     * 保存维修历史记录
     *
     * @param maintainHistorySaveDto Rworker保存维修分析记录信息
     * @param subWsProductionMode    是否子工单投产
     */
    public void saveAnalyseInfo(MaintainHistorySaveDTO maintainHistorySaveDto, boolean subWsProductionMode) {
        Integer maintainType = maintainHistorySaveDto.getMaintainType();
        List<MaintainHistorySaveDTO.MaintainAnalyseInfo> maintainAnalyseInfoList = maintainHistorySaveDto.getMaintainAnalyseInfoList();
        List<MaintainHistoryDetail> maintainHistoryDetails = Lists.newArrayList();
        List<MaintainHistory> maintainHistoryList = maintainHistoryRepository.findByIdInAndDeleted(maintainHistorySaveDto.getMaintainAnalyseInfoList().stream().map(MaintainHistorySaveDTO.MaintainAnalyseInfo::getMaintainHistoryId).collect(Collectors.toList()), Constants.LONG_ZERO);
        List<MaintainHistory> completeMaintains = maintainHistoryList.stream().filter(maintainHistory -> maintainHistory.getStatus() == MaintainEnum.MAINTAIN_FINISHED_STATUS.getStatus()).toList();
        if (!CollectionUtils.isEmpty(completeMaintains)) {
            List<MaintainHistory> completeMaintainList = completeMaintains.stream().filter(maintainHistory -> Objects.nonNull(maintainHistory.getSnWorkStatus())).toList();
            if (!CollectionUtils.isEmpty(completeMaintainList)) {
                throw new ResponseException("error.snMaintainCompleted", "SN(" + String.join(Constants.STR_COMMA, completeMaintainList.stream().map(maintainHistory -> maintainHistory.getSnWorkStatus().getSn()).toList()) + ")已完成维修");
            }
            completeMaintainList = completeMaintains.stream().filter(maintainHistory -> Objects.nonNull(maintainHistory.getContainerDetail())).toList();
            if (!CollectionUtils.isEmpty(completeMaintainList)) {
                throw new ResponseException("error.containerMaintainCompleted", "容器(" + String.join(Constants.STR_COMMA, completeMaintainList.stream().map(maintainHistory -> maintainHistory.getContainerDetail().getContainerCode()).toList()) + ")已完成维修");
            }
            completeMaintainList = completeMaintains.stream().filter(maintainHistory -> Objects.nonNull(maintainHistory.getSubWorkSheet())).toList();
            if (!CollectionUtils.isEmpty(completeMaintainList)) {
                throw new ResponseException("error.subWorkSheetMaintainCompleted", "子工单(" + String.join(Constants.STR_COMMA, completeMaintainList.stream().map(maintainHistory -> maintainHistory.getSubWorkSheet().getSerialNumber()).toList()) + ")已完成维修");
            }
            completeMaintainList = completeMaintains.stream().filter(maintainHistory -> Objects.nonNull(maintainHistory.getWorkSheet())).toList();
            if (!CollectionUtils.isEmpty(completeMaintainList)) {
                throw new ResponseException("error.workSheetMaintainCompleted", "工单(" + String.join(Constants.STR_COMMA, completeMaintainList.stream().map(maintainHistory -> maintainHistory.getWorkSheet().getSerialNumber()).toList()) + ")已完成维修");
            }
        }
        //验证维修分析处理的数量合法性
        validMaintainAnalyseInfoList(maintainHistoryList, maintainAnalyseInfoList);
        // 单支
        if (maintainType == MaintainEnum.MAINTAIN_TYPE_SN.getStatus()) {
            this.processSn(maintainAnalyseInfoList, maintainHistoryList, maintainHistorySaveDto.getStaffId());
            //添加 放行&报废&返工 维修分析详情，添加 退补料信息，
            maintainHistoryList.stream().filter(maintainHistory -> maintainHistory.getSnWorkStatus() != null).forEach(maintainHistory -> this.processMaintainHistory(maintainAnalyseInfoList.stream().filter(maintainAnalyseInfo -> maintainHistory.getId().equals(maintainAnalyseInfo.getMaintainHistoryId())).findFirst(), maintainHistory, maintainHistoryDetails, maintainHistorySaveDto.getStaffId()));
        }
        // 容器
        if (maintainType == MaintainEnum.MAINTAIN_TYPE_CONTAINER.getStatus()) {
            Map<Long, BatchWorkDetail> batchWorkDetailMap = new HashMap<>();
            Map<Long, Integer> batchWorkDetailOrgQualifiedNumberMap = new HashMap<>();
            Map<Long, Boolean> LastbatchWorkDetailMap = new HashMap<>();
            this.processContainer(maintainAnalyseInfoList, maintainHistoryList, maintainHistorySaveDto.getStaffId(), batchWorkDetailMap, LastbatchWorkDetailMap, batchWorkDetailOrgQualifiedNumberMap);
            //添加 放行&报废&返工 维修分析详情，添加 退补料信息，
            maintainHistoryList.stream().filter(maintainHistory -> maintainHistory.getContainerDetail() != null).forEach(maintainHistory -> this.processMaintainHistory(maintainAnalyseInfoList.stream().filter(maintainAnalyseInfo -> maintainHistory.getId().equals(maintainAnalyseInfo.getMaintainHistoryId())).findFirst(), maintainHistory, maintainHistoryDetails, maintainHistorySaveDto.getStaffId()));
            //修改分步容器下交修改 前后置工序详情状态，以及最后一道工序更新（子）工单数量
            processContainerBatchWorkDetail(subWsProductionMode, batchWorkDetailMap, LastbatchWorkDetailMap, batchWorkDetailOrgQualifiedNumberMap);
        }
        // 工单
        if (maintainType == MaintainEnum.MAINTAIN_TYPE_WORKSHEET.getStatus()) {
            if (subWsProductionMode) {
                this.processSubWorkSheet(maintainAnalyseInfoList, maintainHistoryList, maintainHistorySaveDto.getStaffId());
                //添加 放行&报废&返工 维修分析详情，添加 退补料信息，
                maintainHistoryList.stream().filter(maintainHistory -> maintainHistory.getSubWorkSheet() != null).forEach(maintainHistory -> this.processMaintainHistory(maintainAnalyseInfoList.stream().filter(maintainAnalyseInfo -> maintainHistory.getId().equals(maintainAnalyseInfo.getMaintainHistoryId())).findFirst(), maintainHistory, maintainHistoryDetails, maintainHistorySaveDto.getStaffId()));
            } else {
                this.processWorkSheet(maintainAnalyseInfoList, maintainHistoryList, maintainHistorySaveDto.getStaffId());
                //添加 放行&报废&返工 维修分析详情，添加 退补料信息，
                maintainHistoryList.stream().filter(maintainHistory -> maintainHistory.getWorkSheet() != null).forEach(maintainHistory -> this.processMaintainHistory(maintainAnalyseInfoList.stream().filter(maintainAnalyseInfo -> maintainHistory.getId().equals(maintainAnalyseInfo.getMaintainHistoryId())).findFirst(), maintainHistory, maintainHistoryDetails, maintainHistorySaveDto.getStaffId()));
            }
        }
        //生成在线返修单
        this.saveMaintainReWork(maintainAnalyseInfoList, maintainHistoryList, subWsProductionMode, maintainHistoryDetails);
        //判断子工单、工单真实完成状态及更新子工单、工单信息
        maintainHistoryDetailRepository.saveAll(maintainHistoryDetails);
        //更新工单完成状态
        maintainHistoryList.forEach(maintainHistory -> {
            if (subWsProductionMode) {
                checkHistoryService.updateWorkSheetStatus(subWsProductionMode, maintainHistory.getSubWorkSheet(), maintainHistory.getSubWorkSheet().getWorkSheet());
            } else {
                checkHistoryService.updateWorkSheetStatus(subWsProductionMode, null, maintainHistory.getWorkSheet());
            }
        });
    }

    /**
     * 保存维修分析相关信息
     *
     * @param analyseInfoOptional    Rworker保存维修分析记录信息
     * @param maintainHistory        维修分析历史
     * @param maintainHistoryDetails 维修分析历史明细集合
     */
    private void processMaintainHistory(Optional<MaintainHistorySaveDTO.MaintainAnalyseInfo> analyseInfoOptional, MaintainHistory maintainHistory, List<MaintainHistoryDetail> maintainHistoryDetails, Long staffId) {
        analyseInfoOptional.ifPresent(analyseInfo -> {
            int result = 1; //-1:待处理，0：报废，1：返工;2:放行；3：退库
            maintainHistory.setStatus(MaintainEnum.MAINTAIN_FINISHED_STATUS.getStatus());
            if (ValidateUtils.isValid(analyseInfo.getReworkHandleInfos())) {
                maintainHistory.setResult(Constants.INT_ONE)
                        .setReason(analyseInfo.getReworkHandleInfos().stream().filter(entity -> ValidateUtils.isValid(entity.getReason()))
                                .map(MaintainHistorySaveDTO.ReworkHandleInfo::getReason).collect(Collectors.joining(Constants.STR_SEMICOLON)));
                result++;
            }
            if (ValidateUtils.isValid(analyseInfo.getScrapHandleInfos())) {
                maintainHistory.setResult(Constants.INT_ZERO)
                        .setReason(analyseInfo.getScrapHandleInfos().stream().filter(entity -> ValidateUtils.isValid(entity.getReason()))
                                .map(MaintainHistorySaveDTO.ScrapHandleInfo::getReason).collect(Collectors.joining(Constants.STR_SEMICOLON)));
                result++;
            }
            if (Objects.nonNull(analyseInfo.getReleaseHandleInfo())) {
                maintainHistory.setResult(Constants.INT_TWO)
                        .setReason(analyseInfo.getReleaseHandleInfo().getReason());
                result++;
            }
            if (result > Constants.INT_TWO) {
                //处理方式：多状态处理
                maintainHistory.setResult(Constants.INT_FOUR);
            }
            // 返工 保存退补料清单,添加维修分析详情
            if (ValidateUtils.isValid(analyseInfo.getReworkHandleInfos())) {
                List<MaintainHistoryDetail> reworkMaintainHistoryDetails = processReworkHandle(maintainHistory, analyseInfo.getReworkHandleInfos(), staffId);
                if (ValidateUtils.isValid(reworkMaintainHistoryDetails)) {
                    maintainHistoryDetails.addAll(reworkMaintainHistoryDetails);
                }
            }
            // 放行或者报废更新状态，添加维修分析详情
            List<MaintainHistoryDetail> scrapOrReleaseMaintainHistoryDetailList = processScrapOrReleaseHandle(maintainHistory, analyseInfo, staffId);
            if (ValidateUtils.isValid(scrapOrReleaseMaintainHistoryDetailList)) {
                maintainHistory.setEndDate(LocalDateTime.now());
                maintainHistoryDetails.addAll(scrapOrReleaseMaintainHistoryDetailList);
            }
            maintainHistoryRepository.save(maintainHistory);
        });
    }

    /**
     * 返工数据，处理退补料信息，以及收集维修分析详情记录
     *
     * @param maintainHistory   维修分析记录
     * @param reworkHandleInfos 返工数据
     * @param staffId           操作人
     * @return List<MaintainHistoryDetail>
     */
    private List<MaintainHistoryDetail> processReworkHandle(MaintainHistory maintainHistory, List<MaintainHistorySaveDTO.ReworkHandleInfo> reworkHandleInfos, Long staffId) {

        List<MaintainHistoryDetail> maintainHistoryDetails = Lists.newArrayList();

        reworkHandleInfos.stream().collect(Collectors.groupingBy(MaintainHistorySaveDTO.ReworkHandleInfo::getUnqualifiedItemId))
                .forEach((unqualifiedItemId, values) -> {
                    MaintainHistorySaveDTO.ReworkHandleInfo reworkHandleInfo = values.get(Constants.INT_ZERO);
                    MaintainHistoryDetail maintainHistoryDetail = new MaintainHistoryDetail();
                    maintainHistoryDetail
                            .setMaintainHistory(maintainHistory)
                            .setUnqualifiedItem(new UnqualifiedItem(reworkHandleInfo.getUnqualifiedItemId()))
                            .setMaintainCase(new MaintainCase(reworkHandleInfo.getMaintainCaseId()))
                            .setReplaceMaterial(values.stream().anyMatch(entity -> ValidateUtils.isValid(entity.getMaintainMaterialExchangeInfos())))
                            .setNumber(values.stream().mapToInt(MaintainHistorySaveDTO.ReworkHandleInfo::getNumber).sum())
                            .setResult(Constants.INT_ONE)
                            .setReworkCategory(reworkHandleInfo.getReworkCategory())
                            .setReWorkStep(StringUtils.isBlank(reworkHandleInfo.getStepCode()) ? null : stepRepository.findByCode(reworkHandleInfo.getStepCode()).orElse(null))
                            .setNote(reworkHandleInfo.getReason())
                            .setRecordDate(LocalDateTime.now())
                            .setStage(Constants.INT_ONE)
                            .setStaffId(staffId)
                            .setDeleted(Constants.LONG_ZERO);
                    maintainHistoryDetails.add(maintainHistoryDetail);
                });

        //保存退补料
        reworkHandleInfos.stream().filter(reworkHandleInfo -> ValidateUtils.isValid(reworkHandleInfo.getMaintainMaterialExchangeInfos()))
                .forEach(entity -> {
                    this.saveMaintainMaterialExchange(maintainHistory, entity.getMaintainMaterialExchangeInfos());
                });
        return maintainHistoryDetails;
    }

    /**
     * 报废Or放行数据，收集维修分析详情记录
     *
     * @param maintainHistory     维修分析记录
     * @param maintainAnalyseInfo 报废Or放行数据
     * @param staffId             操作人
     * @return List<MaintainHistoryDetail>
     */
    private List<MaintainHistoryDetail> processScrapOrReleaseHandle(MaintainHistory maintainHistory, MaintainHistorySaveDTO.MaintainAnalyseInfo maintainAnalyseInfo, Long staffId) {

        List<MaintainHistoryDetail> maintainHistoryDetailList = Lists.newArrayList();
        //放行
        if (Objects.nonNull(maintainAnalyseInfo.getReleaseHandleInfo())) {
            MaintainHistoryDetail maintainHistoryDetail = new MaintainHistoryDetail();
            maintainHistoryDetail
                    .setMaintainHistory(maintainHistory)
                    .setReplaceMaterial(Boolean.FALSE)
                    .setNumber(maintainAnalyseInfo.getReleaseHandleInfo().getNumber())
                    .setResult(Constants.INT_TWO)
                    .setNote(maintainAnalyseInfo.getReleaseHandleInfo().getReason())
                    .setRecordDate(LocalDateTime.now())
                    .setStage(Constants.INT_ONE)
                    .setStaffId(staffId)
                    .setDeleted(Constants.LONG_ZERO);
            maintainHistoryDetailList.add(maintainHistoryDetail);
        }
        //报废
        if (ValidateUtils.isValid(maintainAnalyseInfo.getScrapHandleInfos())) {
            maintainAnalyseInfo.getScrapHandleInfos().stream()
                    .collect(Collectors.groupingBy(MaintainHistorySaveDTO.ScrapHandleInfo::getUnqualifiedItemId))
                    .forEach((key, values) -> {
                        MaintainHistoryDetail maintainHistoryDetail = new MaintainHistoryDetail();
                        maintainHistoryDetail
                                .setMaintainHistory(maintainHistory)
                                .setUnqualifiedItem(new UnqualifiedItem(key))
                                .setReplaceMaterial(Boolean.FALSE)
                                .setNumber(values.stream().mapToInt(MaintainHistorySaveDTO.ScrapHandleInfo::getNumber).sum())
                                .setResult(Constants.INT_ZERO)
                                .setNote(values.get(Constants.INT_ZERO).getReason())
                                .setRecordDate(LocalDateTime.now())
                                .setStage(Constants.INT_ONE)
                                .setStaffId(staffId)
                                .setDeleted(Constants.LONG_ZERO);
                        maintainHistoryDetailList.add(maintainHistoryDetail);
                    });
        }
        return maintainHistoryDetailList;
    }

    /**
     * 维修分析下交，工单生产数据处理
     *
     * @param maintainAnalyseInfoList 维修分析结果集合
     */
    private void processWorkSheet(List<MaintainHistorySaveDTO.MaintainAnalyseInfo> maintainAnalyseInfoList, List<MaintainHistory> maintainHistoryList, Long staffId) {
        maintainHistoryList.forEach(maintainHistory -> maintainAnalyseInfoList.stream().filter(maintainAnalyseInfo -> maintainAnalyseInfo.getMaintainHistoryId().equals(maintainHistory.getId())).findFirst().ifPresent(maintainAnalyseInfo -> {
            WorkSheet workSheet = maintainHistory.getWorkSheet();

            //报废与返工数量认定为 不合格数
            int unqualifiedNumber = 0;
            //放行数量认定为合格数
            int qualifiedNumber = Objects.nonNull(maintainAnalyseInfo.getReleaseHandleInfo()) ? maintainAnalyseInfo.getReleaseHandleInfo().getNumber() : Constants.INT_ZERO;
            if (ValidateUtils.isValid(maintainAnalyseInfo.getScrapHandleInfos())) {
                int scrapNumber = maintainAnalyseInfo.getScrapHandleInfos().stream().mapToInt(MaintainHistorySaveDTO.ScrapHandleInfo::getNumber).sum();
                unqualifiedNumber += scrapNumber;
            }
            if (ValidateUtils.isValid(maintainAnalyseInfo.getReworkHandleInfos())) {
                int reworkNumber = maintainAnalyseInfo.getReworkHandleInfos().stream().mapToInt(MaintainHistorySaveDTO.ReworkHandleInfo::getNumber).sum();
                unqualifiedNumber += reworkNumber;
            }
            BatchWorkDetail batchWorkDetail = batchWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), maintainHistory.getStep().getId(), Constants.LONG_ZERO)
                    .orElse(null);

            if (Objects.isNull(batchWorkDetail)) {
                return;
            }

            batchWorkDetail.setQualifiedNumber(qualifiedNumber)
                    .setUnqualifiedNumber(unqualifiedNumber)
                    .setTransferNumber(qualifiedNumber);
            batchWorkDetailRepository.save(batchWorkDetail);

            Integer orgQualifiedNumber = workSheet.getQualifiedNumber();
            Integer orgUnqualifiedNumber = workSheet.getUnqualifiedNumber();


            Optional<WsStep> wsStepOptional = wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(batchWorkDetail.getWorkSheet().getId(), batchWorkDetail.getStep().getId(), Constants.LONG_ZERO);
            if (wsStepOptional.isEmpty()) {
                throw new ResponseException(StringUtils.EMPTY, "工序快照不存在");
            }
            WsStep wsStep = wsStepOptional.get();
            Boolean finishAllStep = ValidateUtils.isValid(wsStep.getAfterStepId()) ? Boolean.FALSE : Boolean.TRUE;


            if (finishAllStep || (batchWorkDetail.getFinish() == Constants.INT_ONE && qualifiedNumber == Constants.INT_ZERO)) {

                workSheet.setQualifiedNumber(workSheet.getQualifiedNumber() - (finishAllStep ? orgQualifiedNumber : Constants.INT_ZERO) + qualifiedNumber)
                        .setUnqualifiedNumber(workSheet.getUnqualifiedNumber() - (finishAllStep ? orgUnqualifiedNumber : Constants.INT_ZERO) + unqualifiedNumber);
                if (workSheet.getNumber() == (workSheet.getQualifiedNumber() + workSheet.getUnqualifiedNumber())) {
                    workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName());
                    if (Objects.isNull(workSheet.getActualEndDate())) {
                        workSheet.setActualEndDate(LocalDateTime.now());
                    }
                }
                workSheetRepository.save(workSheet);
            }

            //记录不良项目返修数量以及返修状态
            this.handleUnqualifiedItemDetailBath(new HandleUnqualifiedItemDetailDTO(workSheet, maintainHistory).setOperatorId(staffId).setBatchWorkDetail(batchWorkDetail), maintainAnalyseInfo);
        }));
    }

    /**
     * 维修分析下交，子工单生产数据处理
     *
     * @param maintainAnalyseInfoList 维修分析结果集合
     */
    private void processSubWorkSheet(List<MaintainHistorySaveDTO.MaintainAnalyseInfo> maintainAnalyseInfoList, List<MaintainHistory> maintainHistoryList, Long staffId) {
        maintainHistoryList.forEach(maintainHistory -> maintainAnalyseInfoList.stream().filter(maintainAnalyseInfo -> maintainAnalyseInfo.getMaintainHistoryId().equals(maintainHistory.getId())).findFirst().ifPresent(maintainAnalyseInfo -> {
            SubWorkSheet subWorkSheet = maintainHistory.getSubWorkSheet();
            WorkSheet workSheet = subWorkSheet.getWorkSheet();
            //报废与返工数量认定为 不合格数
            int unqualifiedNumber = 0;
            int orgSubWorkSheetStatus = subWorkSheet.getStatus();
            //放行数量认定为合格数
            int qualifiedNumber = Objects.nonNull(maintainAnalyseInfo.getReleaseHandleInfo()) ? maintainAnalyseInfo.getReleaseHandleInfo().getNumber() : Constants.INT_ZERO;
            if (ValidateUtils.isValid(maintainAnalyseInfo.getScrapHandleInfos())) {
                int scrapNumber = maintainAnalyseInfo.getScrapHandleInfos().stream().mapToInt(MaintainHistorySaveDTO.ScrapHandleInfo::getNumber).sum();
                unqualifiedNumber += scrapNumber;
            }
            if (ValidateUtils.isValid(maintainAnalyseInfo.getReworkHandleInfos())) {
                int reworkNumber = maintainAnalyseInfo.getReworkHandleInfos().stream().mapToInt(MaintainHistorySaveDTO.ReworkHandleInfo::getNumber).sum();
                unqualifiedNumber += reworkNumber;
            }
            BatchWorkDetail batchWorkDetail = batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), maintainHistory.getStep().getId(), Constants.LONG_ZERO)
                    .orElse(null);

            if (Objects.isNull(batchWorkDetail)) {
                return;
            }

            Integer orgQualifiedNumber = batchWorkDetail.getQualifiedNumber();
            Integer orgUnqualifiedNumber = batchWorkDetail.getUnqualifiedNumber();


            batchWorkDetail.setQualifiedNumber(qualifiedNumber)
                    .setUnqualifiedNumber(unqualifiedNumber)
                    .setTransferNumber(qualifiedNumber);
            batchWorkDetailRepository.save(batchWorkDetail);


            Optional<WsStep> wsStepOptional = wsStepRepository.findBySubWorkSheetIdAndStepIdAndDeleted(batchWorkDetail.getSubWorkSheet().getId(), batchWorkDetail.getStep().getId(), Constants.LONG_ZERO);
            if (wsStepOptional.isEmpty() && null != batchWorkDetail.getSubWorkSheet()) {
                wsStepOptional = wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(batchWorkDetail.getSubWorkSheet().getWorkSheet().getId(), batchWorkDetail.getStep().getId(), Constants.LONG_ZERO);
            }
            if (wsStepOptional.isEmpty()) {
                throw new ResponseException(StringUtils.EMPTY, "工序快照不存在");
            }

            WsStep wsStep = wsStepOptional.get();
            Boolean finishAllStep = ValidateUtils.isValid(wsStep.getAfterStepId()) ? Boolean.FALSE : Boolean.TRUE;

            if (finishAllStep || (batchWorkDetail.getFinish() == Constants.INT_ONE && qualifiedNumber == Constants.INT_ZERO)) {

                subWorkSheetRepository.save(subWorkSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName())
                                .setQualifiedNumber(qualifiedNumber)
                                .setUnqualifiedNumber(unqualifiedNumber))
                        .setActualEndDate(LocalDateTime.now());

                workSheet.setQualifiedNumber(workSheet.getQualifiedNumber() - (finishAllStep ? orgQualifiedNumber : Constants.INT_ZERO) + qualifiedNumber)
                        .setUnqualifiedNumber(workSheet.getUnqualifiedNumber() - (finishAllStep ? orgUnqualifiedNumber : Constants.INT_ZERO) + unqualifiedNumber);

                if (workSheet.getNumber() == (workSheet.getQualifiedNumber() + workSheet.getUnqualifiedNumber())) {
                    workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName());
                    if (Objects.isNull(workSheet.getActualEndDate())) {
                        workSheet.setActualEndDate(LocalDateTime.now());
                    }
                }
                workSheetRepository.save(workSheet);
            }

            //记录不良项目返修数量以及返修状态
            this.handleUnqualifiedItemDetailBath(new HandleUnqualifiedItemDetailDTO(subWorkSheet, maintainHistory).setOperatorId(staffId).setBatchWorkDetail(batchWorkDetail), maintainAnalyseInfo);
        }));
    }

    /**
     * 维修分析下交，容器生产数据处理
     *
     * @param maintainAnalyseInfoList 维修分析结果集合
     * @param maintainHistoryList     维修分析历史
     */
    private void processContainer(List<MaintainHistorySaveDTO.MaintainAnalyseInfo> maintainAnalyseInfoList, List<MaintainHistory> maintainHistoryList, Long staffId, Map<Long, BatchWorkDetail> batchWorkDetailMap, Map<Long, Boolean> LastbatchWorkDetailMap, Map<Long, Integer> batchWorkDetailOrgQualifiedNumberMap) {
        if (!ValidateUtils.isValid(maintainHistoryList) || !ValidateUtils.isValid(maintainAnalyseInfoList)) {
            return;
        }
        maintainHistoryList.forEach(maintainHistory -> maintainAnalyseInfoList.stream().filter(maintainAnalyseInfo -> maintainAnalyseInfo.getMaintainHistoryId().equals(maintainHistory.getId())).findFirst().ifPresent(maintainAnalyseInfo -> {

            //报废与返工数量认定为 不合格数
            int unqualifiedNumber = 0;
            //放行数量认定为合格数
            int qualifiedNumber = Objects.nonNull(maintainAnalyseInfo.getReleaseHandleInfo()) ? maintainAnalyseInfo.getReleaseHandleInfo().getNumber() : Constants.INT_ZERO;
            if (ValidateUtils.isValid(maintainAnalyseInfo.getScrapHandleInfos())) {
                int scrapNumber = maintainAnalyseInfo.getScrapHandleInfos().stream().mapToInt(MaintainHistorySaveDTO.ScrapHandleInfo::getNumber).sum();
                unqualifiedNumber += scrapNumber;
            }
            if (ValidateUtils.isValid(maintainAnalyseInfo.getReworkHandleInfos())) {
                int reworkNumber = maintainAnalyseInfo.getReworkHandleInfos().stream().mapToInt(MaintainHistorySaveDTO.ReworkHandleInfo::getNumber).sum();
                unqualifiedNumber += reworkNumber;
            }

            ContainerDetail containerDetail = maintainHistory.getContainerDetail();
            BatchWorkDetail batchWorkDetail = containerDetail.getBatchWorkDetail();
            int orgUnqualifiedNumber = batchWorkDetail.getUnqualifiedNumber();
            int orgQualifiedNumber = batchWorkDetail.getQualifiedNumber();
            batchWorkDetail.setQualifiedNumber(batchWorkDetail.getQualifiedNumber() - containerDetail.getQualifiedNumber() + qualifiedNumber)
                    .setUnqualifiedNumber(batchWorkDetail.getUnqualifiedNumber() - containerDetail.getUnqualifiedNumber() + unqualifiedNumber)
                    .setTransferNumber(batchWorkDetail.getTransferNumber() - containerDetail.getTransferNumber() + qualifiedNumber);
            batchWorkDetailRepository.save(batchWorkDetail);

            batchWorkDetailMap.put(batchWorkDetail.getId(), batchWorkDetail);

            //获取当前批次第一次分析时最原始的合格数
            if (Objects.isNull(batchWorkDetailOrgQualifiedNumberMap.get(batchWorkDetail.getId()))) {
                batchWorkDetailOrgQualifiedNumberMap.put(batchWorkDetail.getId(), orgQualifiedNumber);
            }

            Optional<WsStep> wsStepOptional = null != batchWorkDetail.getSubWorkSheet() ? wsStepRepository.findBySubWorkSheetIdAndStepIdAndDeleted(batchWorkDetail.getSubWorkSheet().getId(), batchWorkDetail.getStep().getId(), Constants.LONG_ZERO) :
                    wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(batchWorkDetail.getWorkSheet().getId(), batchWorkDetail.getStep().getId(), Constants.LONG_ZERO);
            if (wsStepOptional.isEmpty() && null != batchWorkDetail.getSubWorkSheet()) {
                wsStepOptional = wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(batchWorkDetail.getSubWorkSheet().getWorkSheet().getId(), batchWorkDetail.getStep().getId(), Constants.LONG_ZERO);
            }
            if (wsStepOptional.isEmpty()) {
                throw new ResponseException(StringUtils.EMPTY, "工序快照不存在");
            }
            Boolean finishAllStep = ValidateUtils.isValid(wsStepOptional.get().getAfterStepId()) ? Boolean.FALSE : Boolean.TRUE;
            LastbatchWorkDetailMap.put(batchWorkDetail.getId(), finishAllStep);

            //容器维修开出时默认为放行数量 -》 如果 放行 qualifiedNumber = 0 直接 解绑容器
            if (qualifiedNumber == Constants.INT_ZERO) {
                containerDetail.setTransferNumber(Constants.INT_ZERO).setQualifiedNumber(Constants.INT_ZERO).setUnqualifiedNumber(containerDetail.getInputNumber()).setUnbindTime(LocalDateTime.now()).setStatus(ConstantsEnum.UNBIND.getCategoryName());
            } else {
                WsStep nextWsStep = commonService.getNextWsStep(containerDetail.getBatchWorkDetail().getWorkSheet(), containerDetail.getBatchWorkDetail().getSubWorkSheet(), containerDetail.getBatchWorkDetail().getStep());
                containerDetail.setQualifiedNumber(qualifiedNumber)
                        .setUnqualifiedNumber(unqualifiedNumber)
                        .setStatus(finishAllStep ? ConstantsEnum.UNBIND.getCategoryName() : ConstantsEnum.BINDING.getCategoryName())
                        .setTransferNumber(finishAllStep ? Constants.INT_ZERO : qualifiedNumber);
                //下一道工序存在且为容器请求时，才修改待流转数与状态与绑定时间
                if (Objects.nonNull(nextWsStep) && nextWsStep.getRequestMode() == ConstantsEnum.CONTAINER_REQUEST_MODE.getCategoryName()) {
                    containerDetail.setTransferNumber(qualifiedNumber)
                            .setStatus(ConstantsEnum.BINDING.getCategoryName()).setUnbindTime(null);
                }
            }
            containerDetailRepository.save(containerDetail);
            //记录不良项目返修数量以及返修状态
            handleUnqualifiedItemDetailContainer(new HandleUnqualifiedItemDetailDTO(batchWorkDetail, containerDetail, maintainHistory)
                    .setOperatorId(staffId), maintainAnalyseInfo);

        }));
    }

    /**
     * 维修分析下交，SN生产数据处理
     * 放行：将不合格的SN工序相关生产数据从不合格更新至合格，重新投入原工单投产
     * 报废：SN生产状态改为报废，禁止投产
     * 返工：则相应成返工单投产
     *
     * @param maintainAnalyseInfoList 维修分析结果集合
     * @param maintainHistoryList     维修分析历史
     */
    private void processSn(List<MaintainHistorySaveDTO.MaintainAnalyseInfo> maintainAnalyseInfoList, List<MaintainHistory> maintainHistoryList, Long staffId) {
        if (!ValidateUtils.isValid(maintainHistoryList) || !ValidateUtils.isValid(maintainAnalyseInfoList)) {
            return;
        }
        maintainHistoryList.forEach(maintainHistory -> maintainAnalyseInfoList.stream().filter(maintainAnalyseInfo -> maintainAnalyseInfo.getMaintainHistoryId().equals(maintainHistory.getId())).findFirst().ifPresent(maintainAnalyseInfo -> {
            SnWorkStatus snWorkStatus = maintainHistory.getSnWorkStatus();
            UnqualifiedItem latestUnqualifiedItem = snWorkStatus.getLatestUnqualifiedItem();
            //处理类型为报废时则将SN生产状态改为报废
            if (ValidateUtils.isValid(maintainAnalyseInfo.getScrapHandleInfos())) {
                snWorkStatus.setStatus(SnWorkStatusEnum.SCRAP.getStatus());
                snWorkStatus.setEndDate(LocalDateTime.now());
                UnqualifiedItem handleUnqualifiedItem = unqualifiedItemRepository.findByIdAndDeleted(maintainAnalyseInfo.getScrapHandleInfos().get(0)
                                .getUnqualifiedItemId(), Constants.LONG_ZERO)
                        .orElseThrow(() -> new ResponseException("", "未查询到返修指定的不良项目"));
                //生产数据报废不良记录 sn详情不良项目修改
                this.handleUnqualifiedItemDetailSn(new HandleUnqualifiedItemDetailDTO(snWorkStatus, maintainHistory)
                        .setHandleUnqualifiedItem(handleUnqualifiedItem)
                        .setOperatorId(staffId)
                        .setRepair(Boolean.FALSE));
                snWorkStatus.setLatestUnqualifiedItem(handleUnqualifiedItem);
            }
            //处理类型为返工时则将SN生产状态改为返修中
            if (ValidateUtils.isValid(maintainAnalyseInfo.getReworkHandleInfos())) {
                snWorkStatus.setStatus(SnWorkStatusEnum.IN_THE_REPAIR.getStatus());
                snWorkStatus.setReworkStartDate(null != snWorkStatus.getReworkStartDate() ? snWorkStatus.getReworkStartDate() : LocalDateTime.now());

                MaintainHistorySaveDTO.ReworkHandleInfo reworkHandleInfo = maintainAnalyseInfo.getReworkHandleInfos().get(Constants.INT_ZERO);
                UnqualifiedItem handleUnqualifiedItem = unqualifiedItemRepository.findByIdAndDeleted(reworkHandleInfo.getUnqualifiedItemId(), Constants.LONG_ZERO)
                        .orElseThrow(() -> new ResponseException("", "未查询到返修指定的不良项目"));

                //生产数据不良记录 sn详情不良项目修改
                this.handleUnqualifiedItemDetailSn(new HandleUnqualifiedItemDetailDTO(snWorkStatus, maintainHistory)
                        .setHandleUnqualifiedItem(handleUnqualifiedItem)
                        .setOperatorId(staffId)
                        .setRepair(Boolean.TRUE));

                //返修更换不良项目
                snWorkStatus.setLatestUnqualifiedItem(handleUnqualifiedItem);
            }
            //处理类型为放行时则需要调整生产数据相关的所有数据如SN生产详情、容器详情、批量详情等数据
            if (Objects.nonNull(maintainAnalyseInfo.getReleaseHandleInfo())) {
                handleMaintainResultRelease(maintainHistory, maintainAnalyseInfo, snWorkStatus, latestUnqualifiedItem);
            }
            snWorkStatusRepository.save(snWorkStatus);
        }));
    }


    /**
     * 修改分步容器下交修改 前后置工序详情状态，以及最后一道工序更新（子）工单数量
     *
     * @param subWsProductionMode                  投产模式
     * @param batchWorkDetailMap                   批次详情map
     * @param LastbatchWorkDetailMap               是否为最后一道工序
     * @param batchWorkDetailOrgQualifiedNumberMap 批次详情的最原始工序合格数
     */
    private void processContainerBatchWorkDetail(Boolean subWsProductionMode, Map<Long, BatchWorkDetail> batchWorkDetailMap, Map<Long, Boolean> LastbatchWorkDetailMap, Map<Long, Integer> batchWorkDetailOrgQualifiedNumberMap) {

        //分容器下交 更新后置批次详情(当前工序还存在未分析容器，或者前置工序存在未分析记录，则不更新后续工序信息)
        batchWorkDetailMap.forEach((batchWorkDetailId, batchWorkDetail) -> {
            //获取投产工单是否存在待分析的记录
            Long todoTaskCount = subWsProductionMode ?
                    maintainHistoryRepository.countBySubWorkSheetIdAndStatusLessThanAndDeleted(batchWorkDetail.getSubWorkSheet().getId(), MaintainEnum.MAINTAIN_FINISHED_STATUS.getStatus(), Constants.LONG_ZERO)
                    : maintainHistoryRepository.countByWorkSheetIdAndStatusLessThanAndDeleted(batchWorkDetail.getWorkSheet().getId(), MaintainEnum.MAINTAIN_FINISHED_STATUS.getStatus(), Constants.LONG_ZERO);

            //分容器下交更新后置批次详情
            maintainUpdateBatchWorkDetail(batchWorkDetail, batchWorkDetailOrgQualifiedNumberMap.get(batchWorkDetailId),todoTaskCount);

            //没有待分析记录更新后置批次详情,最后一道工序时
            if (todoTaskCount == Constants.LONG_ZERO) {
                //最后工序完成修改工单状态以及完成时间(批量模式 最后一道工序完成才更新 （子）工单 合格不合格)
                if (LastbatchWorkDetailMap.get(batchWorkDetailId) && batchWorkDetail.getFinish() == Constants.INT_ONE) {
                    RworkerStepProcessBaseDTO rworkerStepProcessBaseDto = new RworkerStepProcessBaseDTO();
                    rworkerStepProcessBaseDto.setSubWsProductionMode(Objects.nonNull(batchWorkDetail.getSubWorkSheet()))
                            .setWorkSheet(Objects.nonNull(batchWorkDetail.getSubWorkSheet()) ? batchWorkDetail.getSubWorkSheet().getWorkSheet() : batchWorkDetail.getWorkSheet())
                            .setSubWorkSheet(batchWorkDetail.getSubWorkSheet());
                    iBatchProcessSaveServices[0].calculateBatchAfterAllStepFinished(rworkerStepProcessBaseDto, batchWorkDetail.getQualifiedNumber());
                }
            }
        });
    }

    /**
     * 获取当前工序的前置工序快照
     *
     * @param workSheetId 工单
     * @param stepId      工序
     */
    private List<WsStep> getParentWsStepList(Long workSheetId, Long subWorkSheetId, Long stepId) {
        List<WsStep> wsStepList = wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheetId, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(wsStepList)) {
            wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheetId, Constants.LONG_ZERO);
        }
        List<WsStep> parentWsStepList = Lists.newArrayList();
        Optional<WsStep> wsStepOptional = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(stepId)).findFirst();
        wsStepOptional.orElseThrow(() -> new ResponseException("error.", "当前工序配置不存在"));
        commonService.findParentWsStep(wsStepList, parentWsStepList, wsStepOptional.get());
        return parentWsStepList;
    }

    /**
     * 获取当前批次信息
     *
     * @param workSheetId    工单id
     * @param parentStepIds  前置工序ids
     * @param subWorkSheetId 子工单ids
     */
    private List<BatchWorkDetail> getBatchWorkDetails(Long workSheetId, List<Long> parentStepIds, Long subWorkSheetId) {
        if (subWorkSheetId != null) {
            return batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdInAndDeleted(subWorkSheetId, parentStepIds, Constants.LONG_ZERO);
        } else {
            return batchWorkDetailRepository.findByWorkSheetIdAndStepIdInAndDeleted(workSheetId, parentStepIds, Constants.LONG_ZERO);
        }
    }



    /**
     * 记录不良项目返修数量以及返修状态 sn不良详情
     *
     * @param handleUnqualifiedItemDetailDto 不良信息
     * <AUTHOR>
     * @date 2023/6/12
     */
    private void handleUnqualifiedItemDetailSn(HandleUnqualifiedItemDetailDTO handleUnqualifiedItemDetailDto) {
        UnqualifiedItem unqualifiedItem = handleUnqualifiedItemDetailDto.getUnqualifiedItem();
        UnqualifiedItem handleUnqualifiedItem = handleUnqualifiedItemDetailDto.getHandleUnqualifiedItem();
        SnWorkStatus snWorkStatus = handleUnqualifiedItemDetailDto.getSnWorkStatus();
        Boolean isRepair = handleUnqualifiedItemDetailDto.getRepair();
        if (ObjectUtils.isEmpty(unqualifiedItem)) {
            return;
        }
        Boolean isReplace = !unqualifiedItem.getId().equals(handleUnqualifiedItem.getId()) ? Boolean.TRUE : Boolean.FALSE;
        //sn不良详情
        if (!ObjectUtils.isEmpty(snWorkStatus)) {
            Optional<SnUnqualifiedItem> snUnqualifiedItemOptional = snUnqualifiedItemRepository.findBySnWorkDetailIdAndDeleted(handleUnqualifiedItemDetailDto.getSnWorkStatus().getLatestSnWorkDetail().getId(), Constants.LONG_ZERO);

            snUnqualifiedItemOptional.ifPresent(snUnqualifiedItem -> {
                if (!handleUnqualifiedItemDetailDto.getUnqualifiedItem().getId().equals(snUnqualifiedItem.getUnqualifiedItem().getId())) {
                    throw new ResponseException("", "维修分析记录的sn不良与当前sn生成详情不良不一致");
                }
                SnWorkDetail latestSnWorkDetail = handleUnqualifiedItemDetailDto.getSnWorkStatus().getLatestSnWorkDetail();
                //是否返工
                if (isRepair) {
                    snUnqualifiedItem.setFlag(Constants.TRUE);
                }
                //替换sn的不良项目
                if (isReplace) {
                    snUnqualifiedItem.setUnqualifiedItem(handleUnqualifiedItem);
                    latestSnWorkDetail.setUnqualifiedItem(handleUnqualifiedItem);
                }
                snUnqualifiedItemRepository.save(snUnqualifiedItem);
                snWorkDetailRepository.save(latestSnWorkDetail);
                ContainerDetail containerDetail = latestSnWorkDetail.getContainerDetail();
                if (!ObjectUtils.isEmpty(containerDetail)) {
                    Optional<ContainerDetailUnqualifiedItem> containerDetailUnqualifiedItemOptional = containerDetailUnqualifiedItemRepository.findByContainerDetailIdAndUnqualifiedItemIdAndDeleted(containerDetail.getId(), unqualifiedItem.getId(), Constants.LONG_ZERO);
                    containerDetailUnqualifiedItemOptional.ifPresent(containerDetailUnqualifiedItem -> {
                        if (isReplace) {
                            //说明当前不良现象已不是原始不良现象了，所以这里需要基于原始进行扣减进行替换
                            containerDetailUnqualifiedItem.setNumber(containerDetailUnqualifiedItem.getNumber() - Constants.INT_ONE);
                            if (containerDetailUnqualifiedItem.getNumber() == Constants.INT_ZERO) {
                                containerDetailUnqualifiedItem.setDeleted(containerDetailUnqualifiedItem.getId());
                            }
                        }
                        if (isRepair) {
                            containerDetailUnqualifiedItem.setRepairCount(containerDetailUnqualifiedItem.getRepairCount() + Constants.INT_ONE);
                        }
                        containerDetailUnqualifiedItemRepository.save(containerDetailUnqualifiedItem);
                    });
                    if (isReplace) {
                        Optional<ContainerDetailUnqualifiedItem> replaceContainerDetailUnqualifiedItemOptional = containerDetailUnqualifiedItemRepository.findByContainerDetailIdAndUnqualifiedItemIdAndDeleted(containerDetail.getId(), handleUnqualifiedItem.getId(), Constants.LONG_ZERO);
                        if (replaceContainerDetailUnqualifiedItemOptional.isPresent()) {
                            ContainerDetailUnqualifiedItem containerDetailUnqualifiedItem = replaceContainerDetailUnqualifiedItemOptional.get();
                            containerDetailUnqualifiedItem.setNumber(containerDetailUnqualifiedItem.getNumber() + Constants.INT_ONE)
                                    .setRepairCount(isRepair ? (containerDetailUnqualifiedItem.getRepairCount() + Constants.INT_ONE) : containerDetailUnqualifiedItem.getRepairCount());
                            containerDetailUnqualifiedItemRepository.save(containerDetailUnqualifiedItem);
                        } else {
                            ContainerDetailUnqualifiedItem containerDetailUnqualifiedItem = new ContainerDetailUnqualifiedItem();
                            containerDetailUnqualifiedItem.setContainerDetail(containerDetail)
                                    .setUnqualifiedItem(handleUnqualifiedItem)
                                    .setNumber(Constants.INT_ONE)
                                    .setRepairCount(isRepair ? Constants.INT_ONE : Constants.INT_ZERO);
                            containerDetailUnqualifiedItemRepository.save(containerDetailUnqualifiedItem);
                        }
                    }
                    //单支 不需要修改不合格数，因为本来就一个
                }
                //批量
                Optional<WsStepUnqualifiedItem> wsStepUnqualifiedItemOptional = !ObjectUtils.isEmpty(handleUnqualifiedItemDetailDto.getSubWorkSheet()) ?
                        wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(handleUnqualifiedItemDetailDto.getSubWorkSheet().getId(), handleUnqualifiedItemDetailDto.getStep().getId(), unqualifiedItem.getId(), Constants.LONG_ZERO) :
                        wsStepUnqualifiedItemRepository.findByWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(handleUnqualifiedItemDetailDto.getWorkSheet().getId(), handleUnqualifiedItemDetailDto.getStep().getId(), unqualifiedItem.getId(), Constants.LONG_ZERO);
                wsStepUnqualifiedItemOptional.ifPresent(wsStepUnqualifiedItem -> {
                    if (isReplace) {
                        wsStepUnqualifiedItem.setNumber(wsStepUnqualifiedItem.getNumber() - Constants.INT_ONE);
                        if (wsStepUnqualifiedItem.getNumber() == Constants.INT_ZERO) {
                            wsStepUnqualifiedItem.setDeleted(wsStepUnqualifiedItem.getId());
                        }
                    }
                    if (isRepair) {
                        wsStepUnqualifiedItem.setRepairCount(wsStepUnqualifiedItem.getRepairCount() + Constants.INT_ONE);
                    }
                    wsStepUnqualifiedItemRepository.save(wsStepUnqualifiedItem);
                });
                if (isReplace) {
                    Optional<WsStepUnqualifiedItem> replaceWsStepUnqualifiedItemOptional = !ObjectUtils.isEmpty(handleUnqualifiedItemDetailDto.getSubWorkSheet()) ?
                            wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(handleUnqualifiedItemDetailDto.getSubWorkSheet().getId(), handleUnqualifiedItemDetailDto.getStep().getId(), handleUnqualifiedItem.getId(), Constants.LONG_ZERO) :
                            wsStepUnqualifiedItemRepository.findByWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(handleUnqualifiedItemDetailDto.getWorkSheet().getId(), handleUnqualifiedItemDetailDto.getStep().getId(), handleUnqualifiedItem.getId(), Constants.LONG_ZERO);
                    if (replaceWsStepUnqualifiedItemOptional.isPresent()) {
                        WsStepUnqualifiedItem wsStepUnqualifiedItem = replaceWsStepUnqualifiedItemOptional.get();
                        wsStepUnqualifiedItem.setNumber(wsStepUnqualifiedItem.getNumber() + Constants.INT_ONE)
                                .setRepairCount(isRepair ? (wsStepUnqualifiedItem.getRepairCount() + Constants.INT_ONE) : wsStepUnqualifiedItem.getRepairCount());
                        wsStepUnqualifiedItemRepository.save(wsStepUnqualifiedItem);
                    } else {
                        WsStepUnqualifiedItem wsStepUnqualifiedItem = new WsStepUnqualifiedItem();
                        if (!ObjectUtils.isEmpty(handleUnqualifiedItemDetailDto.getSubWorkSheet())) {
                            wsStepUnqualifiedItem.setSubWorkSheet(handleUnqualifiedItemDetailDto.getSubWorkSheet());
                        } else {
                            wsStepUnqualifiedItem.setWorkSheet(handleUnqualifiedItemDetailDto.getWorkSheet());
                        }
                        wsStepUnqualifiedItem
                                .setNumber(Constants.INT_ONE)
                                .setRecordDate(LocalDate.now())
                                .setStep(snUnqualifiedItem.getStep())
                                .setUnqualifiedItem(handleUnqualifiedItem)
                                .setRepairCount(isRepair ? Constants.INT_ONE : Constants.INT_ZERO)
                                .setOperatorId(handleUnqualifiedItemDetailDto.getOperatorId())
                                .setDeleted(Constants.LONG_ZERO);
                        wsStepUnqualifiedItemRepository.save(wsStepUnqualifiedItem);
                    }
                }
                //员工产能处理
                handleStaffPerformSn(latestSnWorkDetail, handleUnqualifiedItem, isReplace, Boolean.FALSE);
            });
        }

        //修改sn详情
        SnWorkDetail latestSnWorkDetail = snWorkStatus.getLatestSnWorkDetail();
        latestSnWorkDetail.setUnqualifiedItem(handleUnqualifiedItem);
        snWorkDetailRepository.save(latestSnWorkDetail);

        //修改容器详情
        ContainerDetail containerDetail = latestSnWorkDetail.getContainerDetail();
        if (Objects.nonNull(containerDetail)) {
            if (containerDetail.getTransferNumber() == Constants.INT_ZERO) {
                containerDetail.setUnbindTime(LocalDateTime.now()).setStatus(ConstantsEnum.UNBIND.getCategoryName());
                containerDetailRepository.save(containerDetail);
            }
        }
        //更新工单与子工单数据
        SubWorkSheet subWorkSheet = latestSnWorkDetail.getSubWorkSheet();
        WorkSheet workSheet = Objects.isNull(subWorkSheet) ? latestSnWorkDetail.getWorkSheet() : subWorkSheet.getWorkSheet();
        if (Objects.nonNull(subWorkSheet)) {
            if (subWorkSheet.getQualifiedNumber() + subWorkSheet.getUnqualifiedNumber() == subWorkSheet.getNumber()) {
                subWorkSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName())
                        .setActualEndDate(Objects.isNull(subWorkSheet.getActualEndDate()) ? LocalDateTime.now() : subWorkSheet.getActualEndDate());
                subWorkSheetRepository.save(subWorkSheet);
            }
        }
        if (workSheet.getQualifiedNumber() + workSheet.getUnqualifiedNumber() == workSheet.getNumber()) {
            workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName())
                    .setActualEndDate(Objects.isNull(workSheet.getActualEndDate()) ? LocalDateTime.now() : workSheet.getActualEndDate());
            workSheetRepository.save(workSheet);
        }
    }

    /**
     * 记录不良项目返修数量以及返修状态  容器不良详情
     *
     * @param handleUnqualifiedItemDetailDto 不良信息
     * @return void
     * <AUTHOR>
     * @date 2023/6/12
     */
    private void handleUnqualifiedItemDetailContainer(HandleUnqualifiedItemDetailDTO handleUnqualifiedItemDetailDto, MaintainHistorySaveDTO.MaintainAnalyseInfo maintainAnalyseInfo) {
        UnqualifiedItem unqualifiedItem = handleUnqualifiedItemDetailDto.getUnqualifiedItem();
        ContainerDetail containerDetail = handleUnqualifiedItemDetailDto.getContainerDetail();
        if (ObjectUtils.isEmpty(unqualifiedItem)) {
            return;
        }
        if (!ObjectUtils.isEmpty(containerDetail)) {
            //原始不良信息
            ContainerDetailUnqualifiedItem containerDetailUnqualifiedItem = containerDetailUnqualifiedItemRepository.findByContainerDetailIdAndUnqualifiedItemIdAndDeleted(containerDetail.getId(), unqualifiedItem.getId(), Constants.LONG_ZERO)
                    .orElse(null);
            if (Objects.isNull(containerDetailUnqualifiedItem)) {
                return;
            }

            List<Long> unqualifiedItemIds = Lists.newArrayList();
            Map<Long, UnqualifiedItem> unqualifiedItemMap = new HashMap<>();
            //不良项目对应的不合格总数
            Map<Long, Integer> unqualifiedItemNumberMap = new HashMap<>();
            //返修项目对应的返修总数
            Map<Long, Integer> repairUnqualifiedItemNumberMap = new HashMap<>();

            //返工
            if (ValidateUtils.isValid(maintainAnalyseInfo.getReworkHandleInfos())) {
                maintainAnalyseInfo.getReworkHandleInfos().stream().collect(Collectors.groupingBy(MaintainHistorySaveDTO.ReworkHandleInfo::getUnqualifiedItemId))
                        .forEach((key, values) -> {
                            unqualifiedItemNumberMap.put(key, values.stream().mapToInt(MaintainHistorySaveDTO.ReworkHandleInfo::getNumber).sum());
                            repairUnqualifiedItemNumberMap.put(key, values.stream().mapToInt(MaintainHistorySaveDTO.ReworkHandleInfo::getNumber).sum());
                            unqualifiedItemIds.add(key);
                        });
            }
            //报废
            if (ValidateUtils.isValid(maintainAnalyseInfo.getScrapHandleInfos())) {
                maintainAnalyseInfo.getScrapHandleInfos().stream().collect(Collectors.groupingBy(MaintainHistorySaveDTO.ScrapHandleInfo::getUnqualifiedItemId))
                        .forEach((key, values) -> {
                            Integer number = unqualifiedItemNumberMap.get(key);
                            if (Objects.nonNull(number)) {
                                unqualifiedItemNumberMap.put(key, number + values.stream().mapToInt(MaintainHistorySaveDTO.ScrapHandleInfo::getNumber).sum());
                            } else {
                                unqualifiedItemNumberMap.put(key, values.stream().mapToInt(MaintainHistorySaveDTO.ScrapHandleInfo::getNumber).sum());
                            }
                            unqualifiedItemIds.add(key);
                        });
            }

            if (ValidateUtils.isValid(unqualifiedItemIds)) {
                unqualifiedItemMap = unqualifiedItemRepository.findByIdInAndDeleted(unqualifiedItemIds, Constants.LONG_ZERO)
                        .stream().collect(Collectors.toMap(UnqualifiedItem::getId, entity -> entity));
            }
            //先删除原始的容器详情不良-以及关联的批次详情不良信息
            logicDeleteContainerUnqualifiedItem(containerDetail);
            //直接添加当前定义的 报废与返工给定的不良项目以及数量
            if (ValidateUtils.isValid(unqualifiedItemIds)) {
                //容器
                Map<Long, UnqualifiedItem> finalUnqualifiedItemMap = unqualifiedItemMap;
                unqualifiedItemIds.stream().distinct().forEach(unqualifiedItemId -> {
                    Integer number = unqualifiedItemNumberMap.get(unqualifiedItemId);
                    Integer repairNumber = Objects.isNull(repairUnqualifiedItemNumberMap.get(unqualifiedItemId)) ? Constants.INT_ZERO : repairUnqualifiedItemNumberMap.get(unqualifiedItemId);
                    UnqualifiedItem newUnqualifiedItem = finalUnqualifiedItemMap.get(unqualifiedItemId);
                    if (Objects.isNull(newUnqualifiedItem)) {
                        throw new ResponseException("", "未查询到返修指定的不良项目");
                    }
                    //新增容器不良详情
                    ContainerDetailUnqualifiedItem newContainerDetailUnqualifiedItem = new ContainerDetailUnqualifiedItem();
                    newContainerDetailUnqualifiedItem.setContainerDetail(containerDetail)
                            .setUnqualifiedItem(newUnqualifiedItem)
                            .setNumber(number).setRepairCount(repairNumber).setDeleted(Constants.LONG_ZERO);
                    containerDetailUnqualifiedItemRepository.save(newContainerDetailUnqualifiedItem);

                    Optional<WsStepUnqualifiedItem> wsStepUnqualifiedItemOptional = !ObjectUtils.isEmpty(handleUnqualifiedItemDetailDto.getSubWorkSheet()) ?
                            wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(handleUnqualifiedItemDetailDto.getSubWorkSheet().getId(), handleUnqualifiedItemDetailDto.getStep().getId(), unqualifiedItemId, Constants.LONG_ZERO) :
                            wsStepUnqualifiedItemRepository.findByWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(handleUnqualifiedItemDetailDto.getWorkSheet().getId(), handleUnqualifiedItemDetailDto.getStep().getId(), unqualifiedItemId, Constants.LONG_ZERO);
                    //累加或者新增批次详情不良
                    if (wsStepUnqualifiedItemOptional.isPresent()) {
                        WsStepUnqualifiedItem keyWsStepUnqualifiedItem = wsStepUnqualifiedItemOptional.get();
                        keyWsStepUnqualifiedItem.setNumber(keyWsStepUnqualifiedItem.getNumber() + number);
                        keyWsStepUnqualifiedItem.setRepairCount(keyWsStepUnqualifiedItem.getRepairCount() + repairNumber);
                        wsStepUnqualifiedItemRepository.save(keyWsStepUnqualifiedItem);
                    } else {
                        WsStepUnqualifiedItem newWsStepUnqualifiedItem = new WsStepUnqualifiedItem();
                        if (!ObjectUtils.isEmpty(handleUnqualifiedItemDetailDto.getSubWorkSheet())) {
                            newWsStepUnqualifiedItem.setSubWorkSheet(handleUnqualifiedItemDetailDto.getSubWorkSheet());
                        } else {
                            newWsStepUnqualifiedItem.setWorkSheet(handleUnqualifiedItemDetailDto.getWorkSheet());
                        }
                        newWsStepUnqualifiedItem
                                .setNumber(number)
                                .setRecordDate(LocalDate.now())
                                .setStep(handleUnqualifiedItemDetailDto.getStep())
                                .setUnqualifiedItem(newUnqualifiedItem)
                                .setRepairCount(repairNumber)
                                .setOperatorId(handleUnqualifiedItemDetailDto.getOperatorId())
                                .setDeleted(Constants.LONG_ZERO);
                        wsStepUnqualifiedItemRepository.save(newWsStepUnqualifiedItem);
                    }
                });
                //维修分析下交处理容器 员工产能
                handleStaffPerformContainer(containerDetail, Boolean.FALSE);
            } else {
                //维修分析下交处理容器 员工产能
                handleStaffPerformContainer(containerDetail, Boolean.TRUE);
            }
        }
    }

    /**
     * 维修分析过程删除容器不良项目，以及容器对应的批次不良项目
     *
     * @param containerDetail 容器详情
     */
    private void logicDeleteContainerUnqualifiedItem(ContainerDetail containerDetail) {
        SubWorkSheet subWorkSheet = containerDetail.getBatchWorkDetail().getSubWorkSheet();
        WorkSheet workSheet = Objects.isNull(subWorkSheet) ? containerDetail.getBatchWorkDetail().getWorkSheet() : subWorkSheet.getWorkSheet();
        Step step = containerDetail.getBatchWorkDetail().getStep();
        //获取当前容器详情的全部不良详情
        List<ContainerDetailUnqualifiedItem> containerDetailUnqualifiedItems = containerDetailUnqualifiedItemRepository.findByContainerDetailIdAndDeleted(containerDetail.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(containerDetailUnqualifiedItems)) {
            //直接删除容器不良
            containerDetailUnqualifiedItemRepository.logicDelete(containerDetailUnqualifiedItems);
            containerDetailUnqualifiedItems.forEach(entity -> {
                //直接删除工序不良
                WsStepUnqualifiedItem wsStepUnqualifiedItem = !ObjectUtils.isEmpty(subWorkSheet) ?
                        wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(subWorkSheet.getId(), step.getId(), entity.getUnqualifiedItem().getId(), Constants.LONG_ZERO).orElse(null) :
                        wsStepUnqualifiedItemRepository.findByWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(workSheet.getId(), step.getId(), entity.getUnqualifiedItem().getId(), Constants.LONG_ZERO).orElse(null);
                //没有替换说明，换了不良直接删除
                if (Objects.nonNull(wsStepUnqualifiedItem)) {
                    wsStepUnqualifiedItem.setNumber(wsStepUnqualifiedItem.getNumber() - entity.getNumber());
                    if (wsStepUnqualifiedItem.getNumber() == Constants.INT_ZERO) {
                        wsStepUnqualifiedItem.setDeleted(wsStepUnqualifiedItem.getId());
                    }
                    wsStepUnqualifiedItemRepository.save(wsStepUnqualifiedItem);
                }
            });
        }
    }

    /**
     * 记录不良项目返修数量以及返修状态 批量不良详情
     *
     * @param handleUnqualifiedItemDetailDto 不良信息
     * @return void
     * <AUTHOR>
     * @date 2023/6/12
     */
    private void handleUnqualifiedItemDetailBath(HandleUnqualifiedItemDetailDTO handleUnqualifiedItemDetailDto, MaintainHistorySaveDTO.MaintainAnalyseInfo maintainAnalyseInfo) {
        UnqualifiedItem unqualifiedItem = handleUnqualifiedItemDetailDto.getUnqualifiedItem();
        BatchWorkDetail batchWorkDetail = handleUnqualifiedItemDetailDto.getBatchWorkDetail();
        if (ObjectUtils.isEmpty(unqualifiedItem)) {
            return;
        }
        WsStepUnqualifiedItem wsStepUnqualifiedItem = !ObjectUtils.isEmpty(handleUnqualifiedItemDetailDto.getSubWorkSheet()) ?
                wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(handleUnqualifiedItemDetailDto.getSubWorkSheet().getId(), handleUnqualifiedItemDetailDto.getStep().getId(), unqualifiedItem.getId(), Constants.LONG_ZERO).orElse(null) :
                wsStepUnqualifiedItemRepository.findByWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(handleUnqualifiedItemDetailDto.getWorkSheet().getId(), handleUnqualifiedItemDetailDto.getStep().getId(), unqualifiedItem.getId(), Constants.LONG_ZERO).orElse(null);

        if (Objects.isNull(wsStepUnqualifiedItem)) {
            throw new ResponseException("", "工单不良批次详情不存在");
        }
        List<Long> unqualifiedItemIds = Lists.newArrayList();
        Map<Long, UnqualifiedItem> unqualifiedItemMap = new HashMap<>();
        //不良项目对应的不合格总数
        Map<Long, Integer> unqualifiedItemNumberMap = new HashMap<>();
        //返修项目对应的返修总数
        Map<Long, Integer> repairUnqualifiedItemNumberMap = new HashMap<>();

        //返工
        if (ValidateUtils.isValid(maintainAnalyseInfo.getReworkHandleInfos())) {
            maintainAnalyseInfo.getReworkHandleInfos().stream().collect(Collectors.groupingBy(MaintainHistorySaveDTO.ReworkHandleInfo::getUnqualifiedItemId))
                    .forEach((key, values) -> {
                        unqualifiedItemNumberMap.put(key, values.stream().mapToInt(MaintainHistorySaveDTO.ReworkHandleInfo::getNumber).sum());
                        repairUnqualifiedItemNumberMap.put(key, values.stream().mapToInt(MaintainHistorySaveDTO.ReworkHandleInfo::getNumber).sum());
                        unqualifiedItemIds.add(key);
                    });
        }
        //报废
        if (ValidateUtils.isValid(maintainAnalyseInfo.getScrapHandleInfos())) {
            maintainAnalyseInfo.getScrapHandleInfos().stream().collect(Collectors.groupingBy(MaintainHistorySaveDTO.ScrapHandleInfo::getUnqualifiedItemId))
                    .forEach((key, values) -> {
                        Integer number = unqualifiedItemNumberMap.get(key);
                        if (Objects.nonNull(number)) {
                            unqualifiedItemNumberMap.put(key, number + values.stream().mapToInt(MaintainHistorySaveDTO.ScrapHandleInfo::getNumber).sum());
                        } else {
                            unqualifiedItemNumberMap.put(key, values.stream().mapToInt(MaintainHistorySaveDTO.ScrapHandleInfo::getNumber).sum());
                        }
                        unqualifiedItemIds.add(key);
                    });
        }

        if (ValidateUtils.isValid(unqualifiedItemIds)) {
            unqualifiedItemMap = unqualifiedItemRepository.findByIdInAndDeleted(unqualifiedItemIds, Constants.LONG_ZERO)
                    .stream().collect(Collectors.toMap(UnqualifiedItem::getId, entity -> entity));
        }
        //直接删除全部的工序批次不良
        logicDeleteBatchUnqualifiedItem(batchWorkDetail);
        if (ValidateUtils.isValid(unqualifiedItemIds)) {
            Map<Long, UnqualifiedItem> finalUnqualifiedItemMap = unqualifiedItemMap;

            unqualifiedItemIds.stream().distinct().forEach(unqualifiedItemId -> {
                Integer number = unqualifiedItemNumberMap.get(unqualifiedItemId);
                Integer repairNumber = Objects.isNull(repairUnqualifiedItemNumberMap.get(unqualifiedItemId)) ? Constants.INT_ZERO : repairUnqualifiedItemNumberMap.get(unqualifiedItemId);
                UnqualifiedItem newUnqualifiedItem = finalUnqualifiedItemMap.get(unqualifiedItemId);
                if (Objects.isNull(newUnqualifiedItem)) {
                    throw new ResponseException("", "未查询到返修指定的不良项目");
                }
                WsStepUnqualifiedItem newWsStepUnqualifiedItem = new WsStepUnqualifiedItem();
                if (!ObjectUtils.isEmpty(handleUnqualifiedItemDetailDto.getSubWorkSheet())) {
                    newWsStepUnqualifiedItem.setSubWorkSheet(handleUnqualifiedItemDetailDto.getSubWorkSheet());
                } else {
                    newWsStepUnqualifiedItem.setWorkSheet(handleUnqualifiedItemDetailDto.getWorkSheet());
                }
                newWsStepUnqualifiedItem
                        .setNumber(number)
                        .setRecordDate(LocalDate.now())
                        .setStep(handleUnqualifiedItemDetailDto.getStep())
                        .setUnqualifiedItem(newUnqualifiedItem)
                        .setRepairCount(repairNumber)
                        .setOperatorId(handleUnqualifiedItemDetailDto.getOperatorId())
                        .setDeleted(Constants.LONG_ZERO);
                wsStepUnqualifiedItemRepository.save(newWsStepUnqualifiedItem);
            });
            //维修分析下交处理批量 员工产能
            handleStaffPerformBatch(batchWorkDetail, Boolean.FALSE);
        } else {
            //维修分析下交处理批量 员工产能
            handleStaffPerformBatch(batchWorkDetail, Boolean.TRUE);
        }
    }

    /**
     * 维系分析删除批次不良信息
     *
     * @param batchWorkDetail 批次详情
     */
    private void logicDeleteBatchUnqualifiedItem(BatchWorkDetail batchWorkDetail) {

        SubWorkSheet subWorkSheet = batchWorkDetail.getSubWorkSheet();
        WorkSheet workSheet = Objects.isNull(subWorkSheet) ? batchWorkDetail.getWorkSheet() : subWorkSheet.getWorkSheet();

        List<WsStepUnqualifiedItem> wsStepUnqualifiedItemList = !ObjectUtils.isEmpty(subWorkSheet) ?
                wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), batchWorkDetail.getStep().getId(), Constants.LONG_ZERO) :
                wsStepUnqualifiedItemRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), batchWorkDetail.getStep().getId(), Constants.LONG_ZERO);
        //批量放行直接删除全部不良项目
        wsStepUnqualifiedItemRepository.logicDelete(wsStepUnqualifiedItemList);
    }


    /**
     * 放行处理
     *
     * @param maintainHistory       维修分析记录
     * @param maintainAnalyseInfo   Rworker保存维修分析记录
     * @param snWorkStatus          单支生产状态
     * @param latestUnqualifiedItem 不良项目
     */
    private void handleMaintainResultRelease(MaintainHistory maintainHistory, MaintainHistorySaveDTO.MaintainAnalyseInfo maintainAnalyseInfo, SnWorkStatus snWorkStatus, UnqualifiedItem latestUnqualifiedItem) {
        if (Objects.nonNull(maintainAnalyseInfo.getReleaseHandleInfo())) {
            boolean singleSnOnlineRepair = this.updateSnWorkStatus(maintainHistory, snWorkStatus, latestUnqualifiedItem);
            Optional<WsStep> wsStepOptional = null != snWorkStatus.getSubWorkSheet() ? wsStepRepository.findBySubWorkSheetIdAndStepIdAndDeleted(snWorkStatus.getSubWorkSheet().getId(), snWorkStatus.getLatestSnWorkDetail().getStep().getId(), Constants.LONG_ZERO) :
                    wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(snWorkStatus.getWorkSheet().getId(), snWorkStatus.getLatestSnWorkDetail().getStep().getId(), Constants.LONG_ZERO);
            if (wsStepOptional.isEmpty() && null != snWorkStatus.getSubWorkSheet()) {
                wsStepOptional = wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(snWorkStatus.getSubWorkSheet().getWorkSheet().getId(), snWorkStatus.getLatestSnWorkDetail().getStep().getId(), Constants.LONG_ZERO);
            }
            Optional<WorkFlowStep> workFlowStepOptional = Optional.empty();
            if (wsStepOptional.isEmpty()) {
                workFlowStepOptional = workFlowStepRepository.findByWorkFlowIdAndStepIdAndDeleted(snWorkStatus.getWorkFlow().getId(), snWorkStatus.getLatestSnWorkDetail().getStep().getId(), Constants.LONG_ZERO);
            }
            if (wsStepOptional.isEmpty() && workFlowStepOptional.isEmpty()) {
                throw new ResponseException(StringUtils.EMPTY, "工序快照不存在");
            }
            boolean isLastStep = wsStepOptional.isPresent() ? StringUtils.isBlank(wsStepOptional.get().getAfterStepId()) : StringUtils.isBlank(workFlowStepOptional.get().getAfterStepId());
            //更新工单/子工单合格数、不合格数信息
            updateWorkSheetQualifiedNumber(snWorkStatus, isLastStep, singleSnOnlineRepair);
        }
    }

    /**
     * 单支模式处理
     *
     * @param snWorkStatus         单支生产状态
     * @param isLastStep           是否为最后的工序
     * @param singleSnOnlineRepair 是否单支在线反
     */
    private void updateWorkSheetQualifiedNumber(SnWorkStatus snWorkStatus, boolean isLastStep, boolean singleSnOnlineRepair) {
        //更新工单、子工单的不合格数
        if (null != snWorkStatus.getSubWorkSheet()) {
            SubWorkSheet subWorkSheet = snWorkStatus.getSubWorkSheet();
            WorkSheet workSheet = subWorkSheet.getWorkSheet();
            if (!singleSnOnlineRepair) {
                subWorkSheet.setUnqualifiedNumber(subWorkSheet.getUnqualifiedNumber() - Constants.INT_ONE);
                subWorkSheet.setQualifiedNumber(subWorkSheet.getQualifiedNumber() + (isLastStep ? Constants.INT_ONE : Constants.INT_ZERO));
                workSheet.setUnqualifiedNumber(workSheet.getUnqualifiedNumber() - Constants.INT_ONE);
                workSheet.setQualifiedNumber(workSheet.getQualifiedNumber() + (isLastStep ? Constants.INT_ONE : Constants.INT_ZERO));
            }
            if (singleSnOnlineRepair && isLastStep) {
                workSheet.setUnqualifiedNumber(workSheet.getUnqualifiedNumber() - Constants.INT_ONE);
                workSheet.setQualifiedNumber(workSheet.getQualifiedNumber() + Constants.INT_ONE);
                workSheet.setReworkQualifiedNumber(workSheet.getReworkQualifiedNumber() + Constants.INT_ONE);
            }
            subWorkSheet.setStatus(subWorkSheet.getUnqualifiedNumber() + subWorkSheet.getQualifiedNumber() == subWorkSheet.getNumber() ? ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName() : ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName());
            workSheet.setStatus(workSheet.getUnqualifiedNumber() + workSheet.getQualifiedNumber() == workSheet.getNumber() ? ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName() : ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName());
            if (subWorkSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName()) {
                snWorkStatus.setStatus(SnWorkStatusEnum.QUALIFIED.getStatus()).setEndDate(LocalDateTime.now());
                snWorkStatusRepository.save(snWorkStatus);
                if (Objects.isNull(subWorkSheet.getActualEndDate())) {
                    subWorkSheet.setActualEndDate(LocalDateTime.now());
                }
            }
            if (workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName() && Objects.isNull(workSheet.getActualEndDate())) {
                workSheet.setActualEndDate(LocalDateTime.now());
            }
            subWorkSheetRepository.save(subWorkSheet);
            workSheetRepository.save(workSheet);
        } else if (null != snWorkStatus.getWorkSheet()) {
            WorkSheet workSheet = snWorkStatus.getWorkSheet();
            if (!singleSnOnlineRepair) {
                workSheet.setUnqualifiedNumber(workSheet.getUnqualifiedNumber() - Constants.INT_ONE);
                workSheet.setQualifiedNumber(workSheet.getQualifiedNumber() + (isLastStep ? Constants.INT_ONE : Constants.INT_ZERO));
            }
            if (singleSnOnlineRepair && isLastStep) {
                workSheet.setUnqualifiedNumber(workSheet.getUnqualifiedNumber() - Constants.INT_ONE);
                workSheet.setQualifiedNumber(workSheet.getQualifiedNumber() + Constants.INT_ONE);
                workSheet.setReworkQualifiedNumber(workSheet.getReworkQualifiedNumber() + Constants.INT_ONE);
            }
            if (workSheet.getStatus() == ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName()) {
                snWorkStatus.setStatus(SnWorkStatusEnum.QUALIFIED.getStatus()).setEndDate(LocalDateTime.now());
                snWorkStatusRepository.save(snWorkStatus);
                if (Objects.isNull(workSheet.getActualEndDate())) {
                    workSheet.setActualEndDate(LocalDateTime.now());
                }
            }
            workSheetRepository.save(workSheet);
        }
    }

    /**
     * 更新SnWorkStatus
     *
     * @param maintainHistory       维修分析记录
     * @param snWorkStatus          单支生产状态
     * @param latestUnqualifiedItem 最新不良项目
     */
    private Boolean updateSnWorkStatus(MaintainHistory maintainHistory, SnWorkStatus snWorkStatus, UnqualifiedItem
            latestUnqualifiedItem) {
        SnWorkDetail latestSnWorkDetail = snWorkStatus.getLatestSnWorkDetail();
        SnWorkDetail latestUnqualifiedSnWorkDetail = snWorkDetailRepository.findTop1BySnAndResultAndIdLessThanAndDeletedOrderByIdDesc(snWorkStatus.getSn(), Constants.INT_ZERO, latestSnWorkDetail.getId(), Constants.LONG_ZERO).orElse(null);
        //更新SN生产状态
        snWorkStatus.setReworkTime(snWorkStatus.getReworkTime() - Constants.INT_ONE);
        snWorkStatus.setStatus(snWorkStatus.getReworkTime() == Constants.INT_ZERO ? SnWorkStatusEnum.PUT_INTO_PRODUCTION.getStatus() : SnWorkStatusEnum.IN_THE_REPAIR.getStatus());
        snWorkStatus.setLatestUnqualifiedItem(null != latestUnqualifiedSnWorkDetail ? latestUnqualifiedSnWorkDetail.getUnqualifiedItem() : null).setLatestReworkSnWorkDetail(latestUnqualifiedSnWorkDetail);
        //更新SN详情
        latestSnWorkDetail.setUnqualifiedItem(null).setResult(Constants.INT_ONE);
        snWorkDetailRepository.save(latestSnWorkDetail);
        ContainerDetail containerDetail = latestSnWorkDetail.getContainerDetail();
        BatchWorkDetail batchWorkDetail;
        //是否为单支返工
        boolean singleSnOnlineRepair = Boolean.FALSE;
        if (null != snWorkStatus.getLatestReworkSnWorkDetail() && null != snWorkStatus.getLatestReworkSnWorkDetail().getSubWorkSheet() && snWorkStatus.getLatestReworkSnWorkDetail().getSubWorkSheet().getId().equals(latestSnWorkDetail.getSubWorkSheet().getId())) {
            if (snWorkStatus.getReworkTime() > Constants.INT_ZERO && latestSnWorkDetail.getReworkTime() == snWorkStatus.getReworkTime()) {
                singleSnOnlineRepair = Boolean.TRUE;
            }
        }
        if (null != snWorkStatus.getLatestReworkSnWorkDetail() && null != snWorkStatus.getLatestReworkSnWorkDetail().getWorkSheet() && snWorkStatus.getLatestReworkSnWorkDetail().getWorkSheet().getId().equals(latestSnWorkDetail.getWorkSheet().getId())) {
            if (snWorkStatus.getReworkTime() > Constants.INT_ZERO && latestSnWorkDetail.getReworkTime() == snWorkStatus.getReworkTime()) {
                singleSnOnlineRepair = Boolean.TRUE;
            }
        }

        //存在不良信息才更新不良记录信息
        if (!ObjectUtils.isEmpty(latestUnqualifiedItem)) {
            //更新sn详情数据
            Optional<SnUnqualifiedItem> snUnqualifiedItemOptional = snUnqualifiedItemRepository.findBySnWorkDetailIdAndDeleted(snWorkStatus.getLatestSnWorkDetail().getId(), Constants.LONG_ZERO);
            snUnqualifiedItemOptional.ifPresent(snUnqualifiedItem -> {
                snUnqualifiedItem.setDeleted(snUnqualifiedItem.getId());
                snUnqualifiedItemRepository.save(snUnqualifiedItem);
            });
            //更新容器详情数据
            if (!singleSnOnlineRepair) {
                batchWorkDetail = updateBatchWorkDetail(maintainHistory, latestUnqualifiedItem, containerDetail);
                //更新批量详情数据
                batchWorkDetail.setUnqualifiedNumber(batchWorkDetail.getUnqualifiedNumber() - Constants.INT_ONE).setQualifiedNumber(batchWorkDetail.getQualifiedNumber() + Constants.INT_ONE).setTransferNumber(batchWorkDetail.getTransferNumber() + Constants.INT_ONE);
                //更新后置批次详情 防止 分步下交导致后续工序完成
                List<BatchWorkDetail> afterBatchWorkDetailList = Objects.nonNull(batchWorkDetail.getSubWorkSheet()) ?
                        batchWorkDetailRepository.findBySubWorkSheetIdAndFinishAndDeletedAndIdGreaterThan(batchWorkDetail.getSubWorkSheet().getId(), Constants.INT_ONE, Constants.LONG_ZERO, batchWorkDetail.getId()) :
                        batchWorkDetailRepository.findByWorkSheetIdAndFinishAndDeletedAndIdGreaterThan(batchWorkDetail.getWorkSheet().getId(), Constants.INT_ONE, Constants.LONG_ZERO, batchWorkDetail.getId());
                if (ValidateUtils.isValid(afterBatchWorkDetailList)) {
                    afterBatchWorkDetailList.forEach(afterBatchWorkDetail -> {
                        afterBatchWorkDetail.setFinish(Constants.INT_ZERO).setEndDate(null);
                    });
                    batchWorkDetailRepository.saveAll(afterBatchWorkDetailList);
                }
            }
            //更新批量工序不良统计数据
            WsStepUnqualifiedItem wsStepUnqualifiedItem = null != maintainHistory.getSubWorkSheet() ?
                    wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(maintainHistory.getSubWorkSheet().getId(), maintainHistory.getStep().getId(), maintainHistory.getUnqualifiedItem().getId(), Constants.LONG_ZERO).orElse(null) :
                    wsStepUnqualifiedItemRepository.findByWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(maintainHistory.getWorkSheet().getId(), maintainHistory.getStep().getId(), maintainHistory.getUnqualifiedItem().getId(), Constants.LONG_ZERO).orElse(null);
            if (null != wsStepUnqualifiedItem) {
                wsStepUnqualifiedItem.setNumber(wsStepUnqualifiedItem.getNumber() - Constants.INT_ONE)
                        .setDeleted(wsStepUnqualifiedItem.getNumber() == Constants.INT_ZERO ? wsStepUnqualifiedItem.getId() : Constants.LONG_ZERO);
                wsStepUnqualifiedItemRepository.save(wsStepUnqualifiedItem);
            }
            //更新员工产量
            handleStaffPerformSn(latestSnWorkDetail, null, Boolean.FALSE, Boolean.TRUE);
        }
        return singleSnOnlineRepair;
    }

    /**
     * @param maintainHistory       维修分析记录
     * @param latestUnqualifiedItem 不良项目
     * @param containerDetail       容器生产详情
     * @return net.airuima.rbase.domain.procedure.batch.BatchWorkDetail 批量工序生产详情
     */
    private BatchWorkDetail updateBatchWorkDetail(MaintainHistory maintainHistory, UnqualifiedItem
            latestUnqualifiedItem, ContainerDetail containerDetail) {
        BatchWorkDetail batchWorkDetail;
        if (null != containerDetail) {
            WsStep nextWsStep = commonService.getNextWsStep(maintainHistory.getWorkSheet(), maintainHistory.getSubWorkSheet(), maintainHistory.getStep());
            batchWorkDetail = containerDetail.getBatchWorkDetail();
            containerDetail.setUnqualifiedNumber(containerDetail.getUnqualifiedNumber() - Constants.INT_ONE)
                    .setQualifiedNumber(containerDetail.getQualifiedNumber() + Constants.INT_ONE);
            //下一道工序存在且为容器请求时，才修改待流转数与状态与绑定时间
            if (Objects.nonNull(nextWsStep) && nextWsStep.getRequestMode() == ConstantsEnum.CONTAINER_REQUEST_MODE.getCategoryName()) {
                containerDetail.setTransferNumber(containerDetail.getTransferNumber() + Constants.INT_ONE)
                        .setStatus(Constants.INT_ONE).setUnbindTime(null);
            }
            ContainerDetailUnqualifiedItem containerDetailUnqualifiedItem = containerDetailUnqualifiedItemRepository.findByContainerDetailIdAndUnqualifiedItemIdAndDeleted(containerDetail.getId(), latestUnqualifiedItem.getId(), Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.containerDetailUnqualifiedItemNotExist", "容器工序详情不良不存在"));
            containerDetailUnqualifiedItem.setNumber(containerDetailUnqualifiedItem.getNumber() - Constants.INT_ONE)
                    .setDeleted(containerDetailUnqualifiedItem.getNumber() == Constants.INT_ZERO ? containerDetailUnqualifiedItem.getId() : Constants.LONG_ZERO);

        } else {
            batchWorkDetail = null != maintainHistory.getSubWorkSheet() ? batchWorkDetailRepository.findByStepIdAndSubWorkSheetIdAndDeleted(maintainHistory.getStep().getId(), maintainHistory.getSubWorkSheet().getId(), net.airuima.constant.Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.subWorkSheetNotExist", "子工单不存在")) :
                    batchWorkDetailRepository.findByStepIdAndWorkSheetIdAndDeleted(maintainHistory.getStep().getId(), maintainHistory.getWorkSheet().getId(), net.airuima.constant.Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.workSheetNotExist", "工单不存在"));
        }
        return batchWorkDetail;
    }

    /**
     * 保存退补料清单
     *
     * @param maintainHistory               维修历史清单
     * @param maintainMaterialExchangeInfos 退补料信息
     */
    public void saveMaintainMaterialExchange(MaintainHistory
                                                     maintainHistory, Set<MaintainHistorySaveDTO.MaintainMaterialExchangeInfo> maintainMaterialExchangeInfos) {
        if (!ValidateUtils.isValid(maintainMaterialExchangeInfos)) {
            return;
        }
        List<MaintainMaterialExchange> maintainMaterialExchangeList = Lists.newArrayList();
        maintainMaterialExchangeInfos.forEach(maintainMaterialExchangeInfo -> {
            MaintainMaterialExchange maintainMaterialExchange = new MaintainMaterialExchange();
            maintainMaterialExchange.setMaintainHistory(maintainHistory)
                    .setReplaceBatch(maintainMaterialExchangeInfo.getReplaceMaterialInfo().getMaterialBatch())
                    .setReplaceMaterialId(maintainMaterialExchangeInfo.getReplaceMaterialInfo().getMaterialId())
                    .setNumber(maintainMaterialExchangeInfo.getReplaceMaterialInfo().getNumber())
                    .setReturnBatch(maintainMaterialExchangeInfo.getReturnMaterialInfo().getMaterialBatch())
                    .setReturnMaterialId(maintainMaterialExchangeInfo.getReturnMaterialInfo().getMaterialId());
            maintainMaterialExchangeList.add(maintainMaterialExchange);
        });
        maintainMaterialExchangeRepository.saveAll(maintainMaterialExchangeList);
    }

    /**
     * 生成在线返修单，维修分析
     *
     * @param maintainHistoryList     维修分析历史
     * @param maintainAnalyseInfoList Rworker保存维修分析记录DTO
     * @param subWsProductionMode     是否子工单投产
     */
    public void saveMaintainReWork(List<MaintainHistorySaveDTO.MaintainAnalyseInfo> maintainAnalyseInfoList, List<MaintainHistory> maintainHistoryList, boolean subWsProductionMode, List<MaintainHistoryDetail> maintainHistoryDetails) {
        if (!ValidateUtils.isValid(maintainHistoryList) || !ValidateUtils.isValid(maintainAnalyseInfoList)) {
            return;
        }

        //获取需要返工数据
        maintainAnalyseInfoList = maintainAnalyseInfoList.stream().filter(entity -> ValidateUtils.isValid(entity.getReworkHandleInfos())).collect(Collectors.toList());
        if (!ValidateUtils.isValid(maintainHistoryList)) {
            return;
        }
        List<SaveMaintainReworkDTO> saveMaintainReworkDTOList = Lists.newArrayList();

        //合并维修分析返修数据，将同一个工单得返修数据一起返修（同一工单的相同返修工艺路线）
        List<MaintainHistorySaveDTO.MaintainAnalyseInfo> finalMaintainAnalyseInfoList = maintainAnalyseInfoList;
        maintainHistoryList.forEach(maintainHistory -> {
            Optional<MaintainHistorySaveDTO.MaintainAnalyseInfo> maintainAnalyseInfoOptional = finalMaintainAnalyseInfoList.stream().filter(entity -> entity.getMaintainHistoryId().equals(maintainHistory.getId())).findFirst();
            if (maintainAnalyseInfoOptional.isPresent()) {
                MaintainHistorySaveDTO.MaintainAnalyseInfo maintainAnalyseInfo = maintainAnalyseInfoOptional.get();
                maintainAnalyseInfo.getReworkHandleInfos().forEach(reworkHandleInfo -> {
                    WorkFlow workFlow = workFlowRepository.findByCode(reworkHandleInfo.getWorkFlowCode()).orElse(null);
                    Step step = stepRepository.findByCode(reworkHandleInfo.getStepCode()).orElse(null);
                    UnqualifiedItem unqualifiedItem = unqualifiedItemRepository.findByIdAndDeleted(reworkHandleInfo.getUnqualifiedItemId(), Constants.LONG_ZERO).orElse(null);
                    saveMaintainReworkDTOList.add(new SaveMaintainReworkDTO(subWsProductionMode ? maintainHistory.getSubWorkSheet().getWorkSheet() : maintainHistory.getWorkSheet(),
                            maintainHistory, workFlow, step, reworkHandleInfo.getReworkCategory(), unqualifiedItem, reworkHandleInfo.getNumber()));
                });
            }
        });
        margeOrder(maintainHistoryList, subWsProductionMode, saveMaintainReworkDTOList, maintainHistoryDetails);
    }

    /**
     * 合并单据
     *
     * @param maintainHistoryList       维修分析记录列表
     * @param subWsProductionMode       是否子工单投产
     * @param saveMaintainReworkDTOList 保存返工单维修分析信息列表
     */
    private void margeOrder(List<MaintainHistory> maintainHistoryList, boolean subWsProductionMode, List<SaveMaintainReworkDTO> saveMaintainReworkDTOList, List<MaintainHistoryDetail> maintainHistoryDetails) {

        if (!ValidateUtils.isValid(saveMaintainReworkDTOList)) {
            return;
        }
        saveMaintainReworkDTOList.stream().collect(Collectors.collectingAndThen(
                Collectors.groupingBy(SaveMaintainReworkDTO::new), Map::entrySet)).forEach(entry -> {
            SaveMaintainReworkDTO key = entry.getKey();
            List<SaveMaintainReworkDTO> values = entry.getValue();
            if (key.getWorkFlow() == null) {
                return;
            }
            List<MaintainHistory> maintainHistories = values.stream().map(SaveMaintainReworkDTO::getMaintainHistory).toList();
            //获取原始正常工单
            WorkSheet normalWorkSheet = commonService.findNormalWorkSheet(key.getWorkSheet());
            // 生成返工单
            WsRework wsRework = this.generateReworkSheet(normalWorkSheet, key.getWorkFlow(), key.getStep(), values.stream().mapToInt(SaveMaintainReworkDTO::getNumber).sum(), key.getReworkCategory(), subWsProductionMode);
            maintainHistories.forEach(maintainHistory -> {
                maintainHistory
//                        .setWsRework(wsRework)
                        .setEndDate(LocalDateTime.now());
                maintainHistoryRepository.save(maintainHistory);
                //如果有SN需要更新SN生产状态中的工单为返工单及工艺路线
                if (null != maintainHistory.getSnWorkStatus()) {
                    SnWorkStatus snWorkStatus = maintainHistory.getSnWorkStatus();
                    if (subWsProductionMode) {
                        SubWorkSheet subWorkSheet = subWorkSheetRepository.findTop1ByWorkSheetIdAndDeleted(wsRework.getReworkWorkSheet().getId(), Constants.LONG_ZERO).orElse(null);
                        snWorkStatus.setSubWorkSheet(subWorkSheet);
                    }
                    snWorkStatus.setWorkSheet(wsRework.getReworkWorkSheet()).setWorkFlow(wsRework.getReworkWorkSheet().getWorkFlow());
                    snWorkStatusRepository.save(snWorkStatus);
                }
                //获取维修分析详情 添加返工单
                maintainHistoryDetails.forEach(maintainHistoryDetail -> {
                    if (maintainHistoryDetail.getMaintainHistory().getId().equals(maintainHistory.getId())
                            && maintainHistoryDetail.getResult() == MaintainEnum.MAINTAIN_RESULT_REWORK.getStatus()
                            && maintainHistoryDetail.getUnqualifiedItem().getId().equals(key.getUnqualifiedItem().getId())) {
                        maintainHistoryDetail.setWsRework(wsRework);
                    }
                });
            });
        });
    }

    /**
     * 生成在线返修单
     *
     * @param originalWorkSheet   原始工单
     * @param reworkWorkFlow      返修工艺路线
     * @param step                起始工序
     * @param inputNumber         维修 投产数
     * @param reworkCategory      返修流程
     * @param subWsProductionMode 是否子工单投产
     * @return net.airuima.rbase.domain.procedure.aps.WsRework
     */
    public WsRework generateReworkSheet(WorkSheet originalWorkSheet, WorkFlow reworkWorkFlow, Step step, Integer
            inputNumber, Integer reworkCategory, Boolean subWsProductionMode) {
        // 生成在线返修单号
        SerialNumberDTO serialNumberDto = new SerialNumberDTO(Constants.KEY_SERIAL_NUMBER_ONLINE_REWORK_WORK_SHEET, null, originalWorkSheet.getId());
        // 从产品配置中获取计划结单时间
        Optional<PedigreeConfig> pedigreeConfigOptional = pedigreeConfigRepository.findByPedigreeIdAndDeleted(originalWorkSheet.getPedigree().getId(), Constants.LONG_ZERO);
        Integer planFinishDay = pedigreeConfigOptional.map(PedigreeConfig::getPlanFinishDay).orElse(Constants.INT_SEVEN);
        WorkSheet reworkSheet = new WorkSheet();
        reworkSheet.setCategory(Constants.NEGATIVE_ONE)
                .setGenerateSubWsStatus(Constants.INT_TWO)
                .setStatus(Constants.INT_ONE)
                .setNumber(inputNumber)
                .setBomInfoId(originalWorkSheet.getBomInfoId())
                .setOrganizationId(originalWorkSheet.getOrganizationId())
                .setWorkFlow(reworkWorkFlow == null ? originalWorkSheet.getWorkFlow() : reworkWorkFlow)
                .setWorkLine(originalWorkSheet.getWorkLine())
                .setClientId(originalWorkSheet.getClientId())
                .setSerialNumber(rbaseSerialNumberProxy.generate(serialNumberDto))
                .setPedigree(originalWorkSheet.getPedigree())
                .setPlanStartDate(LocalDateTime.now())
                .setPlanEndDate(LocalDateTime.now().plusDays(planFinishDay))
                .setDeleted(Constants.LONG_ZERO);
        workSheetRepository.save(reworkSheet);
        //保存流水号历史明细
        rbaseSerialNumberProxy.createInstance(new SerialNumberHistoryDTO(Constants.KEY_SERIAL_NUMBER_ONLINE_REWORK_WORK_SHEET, LocalDate.now(), reworkSheet.getSerialNumber(), reworkSheet.getOrganizationId().toString()));
        //保存在线返修单的投料单
        wsReworkService.saveReworkSheetMaterial(reworkSheet, originalWorkSheet, wsMaterialRepository.findByWorkSheetIdAndDeleted(originalWorkSheet.getId(), Constants.LONG_ZERO));
        List<WsStep> wsStepList = Lists.newArrayList();
        //保存原始工单快照某个开始节点之后的工序作为返工单的工序快照
        if (reworkCategory == Constants.INT_ONE) {
            // 从开始返工工序至所有后面的后置工序作为返工单的工单工序快照
            List<WsStep> wsSteps = wsStepRepository.findByWorkSheetIdAndDeleted(originalWorkSheet.getId(), Constants.LONG_ZERO);
            WsStep wsStep = wsSteps.stream().filter(entity -> entity.getStep().getId().equals(step.getId())).findAny().orElse(null);
            List<WsStep> childWsStepList = Lists.newArrayList();
            assert wsStep != null;
            commonService.findChildWsStep(wsSteps, childWsStepList, wsStep);
            childWsStepList.add(wsStep);
            for (WsStep currWsStep : childWsStepList) {
                orderWsStep(childWsStepList, currWsStep, wsStepList, reworkSheet);
            }
            //工单投产粒度时需要更新工单的工序个数
            if (Boolean.FALSE.equals(subWsProductionMode) && !CollectionUtils.isEmpty(wsSteps)) {
                reworkSheet.setStepNumber(wsSteps.size());
                workSheetRepository.save(reworkSheet);
            }
            wsStepRepository.saveAll(wsStepList);
        }
        // 返修工艺路线，配置的返修工艺路线作为此次维修的返修工艺路线,以及生成返工单的工艺路线及工单工序快照
        if (reworkCategory == Constants.INT_ZERO) {
            WorkFlowDTO workFlowDTO = workFlowStepService.findByWorkFlowId(reworkSheet.getWorkFlow().getId());
            wsStepList = workSheetService.saveWsSteps(subWsProductionMode, reworkSheet, reworkSheet.getWorkFlow(), reworkSheet.getPedigree(), workFlowDTO.getStepDtoList());
        }
        // 保存总工单和在线返修单关系
        WsRework wsRework = new WsRework();
        wsRework.setOriginalWorkSheet(originalWorkSheet)
                .setReworkWorkSheet(reworkSheet)
                .setDeleted(Constants.LONG_ZERO);
        if (Boolean.TRUE.equals(subWsProductionMode)) {
            // 在线返修单自动分子工单, 只分一个子工单
            SubWorkSheet subWorkSheet = new SubWorkSheet();
            subWorkSheet.setWorkSheet(reworkSheet)
                    .setSerialNumber(reworkSheet.getSerialNumber() + Constants.SUB_WORK_SHEET_SERIAL_NUMBER_FIRST)
                    .setWorkLine(originalWorkSheet.getWorkLine())
                    .setWorkFlow(reworkSheet.getWorkFlow())
                    .setPlanStartDate(reworkSheet.getPlanStartDate())
                    .setPlanEndDate(reworkSheet.getPlanEndDate())
                    .setNumber(reworkSheet.getNumber()).setStepNumber(wsStepList.size());
            subWorkSheetRepository.save(subWorkSheet);
            //更新工单的子工单个数以及完成个数(包括正常、异常)
            subWorkSheetService.updateWorkSheetSubWsNumberInfo(reworkSheet);
        }
        return wsReworkRepository.save(wsRework);
    }

    /**
     * @param originWsStepList 开始返修节点工序在原始工单工艺路线后的快照集合
     * @param currWsStep       当前快照
     * @param wsStepList       新组织后的快照列表
     * @param reworkSheet      返工工单
     */
    public void orderWsStep(List<WsStep> originWsStepList, WsStep
            currWsStep, List<WsStep> wsStepList, WorkSheet reworkSheet) {
        List<WsStep> preWsStepList = originWsStepList.stream().filter(wsStep -> StringUtils.isNotBlank(wsStep.getAfterStepId()) && wsStep.getAfterStepId().contains(currWsStep.getStep().getId().toString())).collect(Collectors.toList());
        List<WsStep> afterWsStepList = originWsStepList.stream().filter(wsStep -> StringUtils.isNotBlank(wsStep.getPreStepId()) && wsStep.getPreStepId().contains(currWsStep.getStep().getId().toString())).collect(Collectors.toList());
        WsStep newWsStep = new WsStep();
        newWsStep.setStep(currWsStep.getStep()).setInputRate(currWsStep.getInputRate()).setCategory(currWsStep.getCategory())
                .setControlMode(currWsStep.getControlMode()).setIsBindContainer(currWsStep.getIsBindContainer())
                .setIsControlMaterial(currWsStep.getIsControlMaterial()).setRequestMode(currWsStep.getRequestMode()).setWorkSheet(reworkSheet).setDeleted(Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(preWsStepList)) {
            newWsStep.setPreStepId(null);
        } else {
            newWsStep.setPreStepId(StringUtils.join(preWsStepList.stream().map(wsStep -> wsStep.getStep().getId()).collect(Collectors.toList()), Constants.STR_COMMA));
        }
        if (ValidateUtils.isValid(afterWsStepList)) {
            newWsStep.setAfterStepId(StringUtils.join(afterWsStepList.stream().map(wsStep -> wsStep.getStep().getId()).collect(Collectors.toList()), Constants.STR_COMMA));
        } else {
            newWsStep.setAfterStepId(null);
        }
        wsStepList.add(newWsStep);
    }

    /**
     * sn获取当前待做维系分析工序
     *
     * @param sn 单只
     * @return net.airuima.rbase.web.rest.procedure.maintaincase.dto.MaintainHistoryInfoDTO
     */
    public MaintainHistoryInfoDTO getSnStepInfo(String sn) {
        SnWorkStatus snWorkStatus = snWorkStatusRepository.findBySnAndDeleted(sn, Constants.LONG_ZERO).orElse(null);
        if (snWorkStatus == null) {
            throw new ResponseException("error.SnWorkStatusNotExist", "SN生产状态不存在");
        }
        Optional<MaintainHistory> maintainHistoryOptional = maintainHistoryRepository.findTop1BySnWorkStatusIdAndDeletedOrderByIdDesc(snWorkStatus.getId(), Constants.LONG_ZERO);
        return this.retReWorkFlowInfoInfo(maintainHistoryOptional);
    }

    /**
     * 容器获取当前待做维系分析
     *
     * @param containerCode 容器码
     * @return net.airuima.rbase.web.rest.procedure.maintaincase.dto.MaintainHistoryInfoDTO
     */
    public MaintainHistoryInfoDTO getContainerStepInfo(String containerCode) {
        Container container = containerRepository.findByCodeAndDeleted(containerCode, Constants.LONG_ZERO).orElse(null);
        if (container == null) {
            throw new ResponseException("error.ContainerNotExist", "容器不存在");
        }
        Optional<MaintainHistory> maintainHistoryOptional = maintainHistoryRepository.findTop1ByContainerDetailContainerIdAndDeletedOrderByIdDesc(container.getId(), Constants.LONG_ZERO);
        if (maintainHistoryOptional.isPresent() && null != maintainHistoryOptional.get().getSnWorkStatus()) {
            throw new ResponseException("error.snToMaintain", "请扫描SN进行维修分析");
        }
        return this.retReWorkFlowInfoInfo(maintainHistoryOptional);
    }

    /**
     * 根据投产粒度获取当前待做维系分析
     *
     * @param serialNumber 工单号
     * @return net.airuima.rbase.web.rest.procedure.maintaincase.dto.MaintainHistoryInfoDTO
     */
    public MaintainHistoryInfoDTO getWorkSheetStepInfo(String serialNumber) {
        boolean subWsProductMode = commonService.subWsProductionMode();
        if (subWsProductMode) {
            Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO);
            if (!subWorkSheetOptional.isPresent()) {
                throw new ResponseException("error.SubWorkSheetNotExist", "子工单不存在");
            }
            Optional<MaintainHistory> maintainHistoryOptional = maintainHistoryRepository.findTop1BySubWorkSheetIdAndSnWorkStatusIsNullAndContainerDetailIsNullAndDeletedOrderByIdDesc(subWorkSheetOptional.get().getId(), Constants.LONG_ZERO);
            if (maintainHistoryOptional.isPresent() && null != maintainHistoryOptional.get().getSnWorkStatus()) {
                throw new ResponseException("error.snToMaintain", "请扫描SN进行维修分析");
            }
            if (maintainHistoryOptional.isPresent() && null != maintainHistoryOptional.get().getContainerDetail()) {
                throw new ResponseException("error.containerToMaintain", "请扫描容器进行维修分析");
            }
            return this.retReWorkFlowInfoInfo(maintainHistoryOptional);
        } else {
            Optional<WorkSheet> workSheetOptional = workSheetRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO);
            if (!workSheetOptional.isPresent()) {
                throw new ResponseException("error.WorkSheetNotExist", "工单不存在");
            }
            Optional<MaintainHistory> maintainHistoryOptional = maintainHistoryRepository.findTop1ByWorkSheetIdAndSnWorkStatusIsNullAndContainerDetailIsNullAndDeletedOrderByIdDesc(workSheetOptional.get().getId(), Constants.LONG_ZERO);
            if (maintainHistoryOptional.isPresent() && null != maintainHistoryOptional.get().getSnWorkStatus()) {
                throw new ResponseException("error.snToMaintain", "请扫描SN进行维修分析");
            }
            if (maintainHistoryOptional.isPresent() && null != maintainHistoryOptional.get().getContainerDetail()) {
                throw new ResponseException("error.containerToMaintain", "请扫描容器进行维修分析");
            }
            return this.retReWorkFlowInfoInfo(maintainHistoryOptional);
        }
    }

    /**
     * 校验集合数据是否为空
     *
     * @param list                 集合数据
     * @param maintainTypeCodeList 维修分析类型码
     * @param message              提示信息
     * @return net.airuima.rbase.dto.client.base.BaseClientDTO
     */
    public BaseClientDTO validDataIsNull(List<?> list, List<String> maintainTypeCodeList, String key, String
            message) {
        if (!ValidateUtils.isValid(list) || maintainTypeCodeList.size() != list.size()) {
            return new BaseClientDTO(key, message);
        }
        return null;
    }

    /**
     * 返回维修分析信息
     *
     * @param maintainHistoryOptional 维修历史
     * @return net.airuima.rbase.web.rest.procedure.maintaincase.dto.MaintainHistoryInfoDTO 维修分析信息
     * <AUTHOR>
     * @date 2023/3/29
     */
    @Override
    public MaintainHistoryInfoDTO retReWorkFlowInfoInfo(Optional<MaintainHistory> maintainHistoryOptional) {
        if (!maintainHistoryOptional.isPresent() || maintainHistoryOptional.get().getStatus() != MaintainEnum.WAIT_ANALYZE_STATUS.getStatus()) {
            throw new ResponseException("error.maintainHistoryNotExist", "未获取到待分析的维修数据");
        }
        MaintainHistory maintainHistory = maintainHistoryOptional.get();
        WorkSheet workSheet = maintainHistory.getWorkSheet() == null ? maintainHistory.getSubWorkSheet().getWorkSheet() : maintainHistory.getWorkSheet();
        List<WsStep> wsStepList = null;
        if (null != maintainHistory.getSubWorkSheet()) {
            wsStepList = wsStepRepository.findBySubWorkSheetIdAndDeleted(maintainHistory.getSubWorkSheet().getId(), Constants.LONG_ZERO);
        }
        if (!ValidateUtils.isValid(wsStepList)) {
            wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        }
        if (!ValidateUtils.isValid(wsStepList)) {
            throw new ResponseException("error.wsStepNotExist", "工单工序快照不存在");
        }
        MaintainHistoryInfoDTO historyInfoDTO = new MaintainHistoryInfoDTO(maintainHistoryOptional.get());
        // 获取产品谱系维修方案列表
        List<PedigreeMaintainCase> pedigreeMaintainCaseList = pedigreeMaintainCaseService.findPedigreeMaintainCaseWorkFlow(workSheet.getPedigree(), workSheet.getClientId());
        if (ValidateUtils.isValid(pedigreeMaintainCaseList)) {
            List<MaintainHistoryInfoDTO.MaintainCaseInfo> maintainCaseInfoList = new ArrayList<>();
            Map<MaintainCase, List<PedigreeMaintainCase>> collect = pedigreeMaintainCaseList.stream().collect(Collectors.groupingBy(PedigreeMaintainCase::getMaintainCase));
            if (!ValidateUtils.isValid(collect)) {
                return historyInfoDTO;
            }
            // 以维修方案分组统计返修工艺路线
            List<WsStep> finalWsStepList = wsStepList;
            collect.keySet().forEach(maintainCase -> {
                MaintainHistoryInfoDTO.MaintainCaseInfo maintainCaseInfo = new MaintainHistoryInfoDTO.MaintainCaseInfo(maintainCase);
                List<MaintainHistoryInfoDTO.WorkFlowInfo> workFlowInfoList = new ArrayList<>();
                collect.get(maintainCase).forEach(pedigreeMaintainCase ->
                        handleWorkFlowInfo(maintainHistory, workSheet, finalWsStepList, maintainCaseInfo, workFlowInfoList, pedigreeMaintainCase)
                );
                maintainCaseInfoList.add(maintainCaseInfo);
            });
            historyInfoDTO.setMaintainCaseInfoList(maintainCaseInfoList);
        }
        //不良项目详情列表
        List<MaintainHistoryInfoDTO.UnqualifiedItemDetailInfo> unqualifiedItemDetailInfos = Lists.newArrayList();
        List<UnqualifiedItem> unqualifiedItems = commonService.findPedigreeStepUnqualifiedItem(workSheet.getPedigree(), workSheet.getWorkFlow().getId(), maintainHistory.getStep().getId(), workSheet.getClientId());
        if (ValidateUtils.isValid(unqualifiedItems)) {
            List<MaintainHistoryInfoDTO.UnqualifiedItemInfo> reworkUnqualifiedItems = unqualifiedItems.stream().filter(unqualifiedItem -> (unqualifiedItem.getDealWay() == ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_MAINTAIN_ANALYSE.getCategoryName() || unqualifiedItem.getDealWay() == ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_ONLINE_REWORK.getCategoryName()))
                    .distinct().map(MaintainHistoryInfoDTO.UnqualifiedItemInfo::new).collect(Collectors.toList());
            if (ValidateUtils.isValid(reworkUnqualifiedItems)) {
                MaintainHistoryInfoDTO.UnqualifiedItemDetailInfo unqualifiedItemDetailInfo = new MaintainHistoryInfoDTO.UnqualifiedItemDetailInfo();
                unqualifiedItemDetailInfo.setType(ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_MAINTAIN_ANALYSE.getCategoryName())
                        .setUnqualifiedItemInfoList(reworkUnqualifiedItems);
                unqualifiedItemDetailInfos.add(unqualifiedItemDetailInfo);
            }
            List<MaintainHistoryInfoDTO.UnqualifiedItemInfo> scrapUnqualifiedItems = unqualifiedItems.stream().filter(unqualifiedItem -> unqualifiedItem.getDealWay() == ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_SCRAP.getCategoryName())
                    .distinct().map(MaintainHistoryInfoDTO.UnqualifiedItemInfo::new).collect(Collectors.toList());
            if (ValidateUtils.isValid(scrapUnqualifiedItems)) {
                MaintainHistoryInfoDTO.UnqualifiedItemDetailInfo unqualifiedItemDetailInfo = new MaintainHistoryInfoDTO.UnqualifiedItemDetailInfo();
                unqualifiedItemDetailInfo.setType(ConstantsEnum.UNQUALIFIEDITEM_DEALWAY_SCRAP.getCategoryName())
                        .setUnqualifiedItemInfoList(scrapUnqualifiedItems);
                unqualifiedItemDetailInfos.add(unqualifiedItemDetailInfo);
            }
        }
        historyInfoDTO.setUnqualifiedItemDetailInfoList(unqualifiedItemDetailInfos);
        return historyInfoDTO;

    }

    /**
     * 处理工艺路线信息
     *
     * @param maintainHistory      维修分析记录
     * @param workSheet            生产总工单
     * @param finalWsStepList      生产工单定制工序列表
     * @param maintainCaseInfo     维修分析方案
     * @param workFlowInfoList     工艺路线列表
     * @param pedigreeMaintainCase 产品谱系维修分析
     */
    private void handleWorkFlowInfo(MaintainHistory maintainHistory, WorkSheet
            workSheet, List<WsStep> finalWsStepList, MaintainHistoryInfoDTO.MaintainCaseInfo
                                            maintainCaseInfo, List<MaintainHistoryInfoDTO.WorkFlowInfo> workFlowInfoList, PedigreeMaintainCase
                                            pedigreeMaintainCase) {
        // 原工艺路线
        if (pedigreeMaintainCase.getReworkCategory() == MaintainEnum.MAINTAIN_REWORK_CATEGORY_ORIGIN.getStatus()) {
            //获取定制工序中工艺路线
            WorkFlow snapshotWorkFlow = workSheet.getWorkFlow();
            MaintainHistoryInfoDTO.WorkFlowInfo workFlowInfo = new MaintainHistoryInfoDTO.WorkFlowInfo(snapshotWorkFlow);
            //只获取当前发生工序及前置工序作为可返工点
            WsStep currentWsStep = finalWsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(maintainHistory.getStep().getId())).findFirst().get();
            WorkFlowDTO workFlowDTO = new WorkFlowDTO(snapshotWorkFlow);
            workFlowDTO.setStepDtoList(getPreStepList(finalWsStepList,currentWsStep));
            workFlowInfo.setWorkFlowDTO(workFlowDTO);
            maintainCaseInfo.setOriginFlowInfo(workFlowInfo);
        }
        // 返修工艺路线
        if (pedigreeMaintainCase.getReworkCategory() == MaintainEnum.MAINTAIN_REWORK_CATEGORY_REWORK.getStatus()) {
            MaintainHistoryInfoDTO.WorkFlowInfo workFlowInfo = new MaintainHistoryInfoDTO.WorkFlowInfo(pedigreeMaintainCase.getWorkFlow());
            workFlowInfoList.add(workFlowInfo);
            maintainCaseInfo.setReWorkFlowInfoList(workFlowInfoList);
        }
    }


    /**
     * 获取当前工序及前置工序
     *
     * @param wsStepList    工序快照列表
     * @param wsStep        当前工序快照
     * @return java.util.List<net.airuima.rbase.web.rest.procedure.aps.dto.StepDTO>
     */
    public List<StepDTO> getPreStepList(List<WsStep> wsStepList, WsStep wsStep) {
        List<WsStep> preWsStepList = Lists.newArrayList();
        commonService.findParentWsStep(wsStepList,preWsStepList,wsStep);
        preWsStepList.add(wsStep);
        return preWsStepList.stream().map(StepDTO::new).collect(Collectors.toList());
    }


    /**
     * 验证维修分析处理的参数合法性
     *
     * @param maintainHistoryList     维修分析历史清单
     * @param maintainAnalyseInfoList 分析处理清单
     */
    private void validMaintainAnalyseInfoList(List<MaintainHistory> maintainHistoryList, List<MaintainHistorySaveDTO.MaintainAnalyseInfo> maintainAnalyseInfoList) {

        if (!ValidateUtils.isValid(maintainHistoryList) || !ValidateUtils.isValid(maintainAnalyseInfoList)) {
            throw new ResponseException("maintainAnalyseInfoListValidDataNull", "维修分析验证数据不存在");
        }

        maintainHistoryList.forEach(maintainHistory -> {

            MaintainHistorySaveDTO.MaintainAnalyseInfo maintainAnalyseInfo = maintainAnalyseInfoList.stream().filter(entity -> entity.getMaintainHistoryId().equals(maintainHistory.getId())).findFirst()
                    .orElseThrow(() -> new ResponseException("maintainHistoryIdNull", "未查询到维修分析记录"));

            AtomicInteger handleNumber = new AtomicInteger(Constants.INT_ZERO);

            //根据不良项目分组，累加个不良项目的报废数量
            if (ValidateUtils.isValid(maintainAnalyseInfo.getScrapHandleInfos())) {
                maintainAnalyseInfo.getScrapHandleInfos().stream().collect(Collectors.groupingBy(MaintainHistorySaveDTO.ScrapHandleInfo::getUnqualifiedItemId))
                        .forEach((key, values) -> {
                            int sum = values.stream().mapToInt(MaintainHistorySaveDTO.ScrapHandleInfo::getNumber).sum();
                            handleNumber.addAndGet(sum);
                        });
            }

            //根据不良项目分组，累加个不良项目的返修数量
            if (ValidateUtils.isValid(maintainAnalyseInfo.getReworkHandleInfos())) {
                maintainAnalyseInfo.getReworkHandleInfos().stream().collect(Collectors.groupingBy(MaintainHistorySaveDTO.ReworkHandleInfo::getUnqualifiedItemId))
                        .forEach((key, values) -> {
                            int sum = values.stream().mapToInt(MaintainHistorySaveDTO.ReworkHandleInfo::getNumber).sum();
                            handleNumber.addAndGet(sum);
                        });
            }
            //累加放行数量
            if (Objects.nonNull(maintainAnalyseInfo.getReleaseHandleInfo())) {
                handleNumber.addAndGet(maintainAnalyseInfo.getReleaseHandleInfo().getNumber());
            }
            //验证是否全部分析
            if (maintainHistory.getNumber() != handleNumber.get()) {
                throw new ResponseException("handleNumberDiffMaintainHistoryNumber", "维修分析处理数量不等于历史记录数量");
            }
        });
    }

    /**
     * 维修分析下交处理SN 员工产能
     *
     * @param snWorkDetail    sn详情
     * @param unqualifiedItem 维修分析上传的不良项目
     * @param isReplace       是否替换
     * @param isRelease       是否放行
     */
    public void handleStaffPerformSn(SnWorkDetail snWorkDetail, UnqualifiedItem unqualifiedItem, Boolean isReplace, Boolean isRelease) {
        Optional<StaffPerform> staffPerformOptional = staffPerformRepository.findBySnWorkDetailIdAndDeleted(snWorkDetail.getId(), Constants.LONG_ZERO);
        if (staffPerformOptional.isEmpty()) {
            return;
        }
        StaffPerform staffPerform = staffPerformOptional.get();
        //放行直接删除不合格数据，修改员工产能数据
        if (isRelease) {
            staffPerform.setQualifiedNumber(Constants.INT_ONE).setUnqualifiedNumber(Constants.INT_ZERO);
            staffPerformRepository.save(staffPerform);
            staffPerformUnqualifiedItemRepository.deleteByStaffPerformId(staffPerform.getId(), Constants.LONG_ZERO);
            return;
        }
        //是否替换
        if (isReplace) {
            List<StaffPerformUnqualifiedItem> staffPerformUnqualifiedItems = staffPerformUnqualifiedItemRepository.findByStaffPerformIdAndDeleted(staffPerform.getId(), Constants.LONG_ZERO);
            if (ValidateUtils.isValid(staffPerformUnqualifiedItems)) {
                staffPerformUnqualifiedItems.forEach(staffPerformUnqualifiedItem -> {
                    staffPerformUnqualifiedItem.setUnqualifiedItem(unqualifiedItem);
                });
                staffPerformUnqualifiedItemRepository.saveAll(staffPerformUnqualifiedItems);
            }
        }
    }

    /**
     * 维修分析下交处理容器 员工产能
     *
     * @param containerDetail 容器详情
     * @param isRelease       是否放行
     */
    public void handleStaffPerformContainer(ContainerDetail containerDetail, Boolean isRelease) {

        Optional<StaffPerform> staffPerformOptional = staffPerformRepository.findByContainerDetailIdAndDeletedAndSnWorkDetailIdIsNull(containerDetail.getId(), Constants.LONG_ZERO);
        if (staffPerformOptional.isEmpty()) {
            return;
        }
        StaffPerform staffPerform = staffPerformOptional.get();
        staffPerform.setQualifiedNumber(containerDetail.getQualifiedNumber()).setUnqualifiedNumber(containerDetail.getUnqualifiedNumber());
        staffPerformRepository.save(staffPerform);
        //放行直接删除不合格数据，修改员工产能数据
        if (isRelease) {
            staffPerformUnqualifiedItemRepository.deleteByStaffPerformId(staffPerform.getId(), Constants.LONG_ZERO);
            return;
        }
        //直接删除全部的产能不合格项目重新添加
        List<ContainerDetailUnqualifiedItem> containerDetailUnqualifiedItems = containerDetailUnqualifiedItemRepository.findByContainerDetailIdAndDeleted(containerDetail.getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(containerDetailUnqualifiedItems)) {
            staffPerformUnqualifiedItemRepository.deleteByStaffPerformId(staffPerform.getId(), Constants.LONG_ZERO);

            List<StaffPerformUnqualifiedItem> staffPerformUnqualifiedItemList = containerDetailUnqualifiedItems.stream().map(entity -> {
                StaffPerformUnqualifiedItem staffPerformUnqualifiedItem = new StaffPerformUnqualifiedItem();
                staffPerformUnqualifiedItem.setStaffPerform(staffPerform).setUnqualifiedItem(entity.getUnqualifiedItem())
                        .setNumber(entity.getNumber()).setRecordDate(staffPerform.getRecordDate()).setRecordTime(staffPerform.getRecordTime())
                        .setDeleted(Constants.LONG_ZERO);
                return staffPerformUnqualifiedItem;
            }).collect(Collectors.toList());
            staffPerformUnqualifiedItemRepository.saveAll(staffPerformUnqualifiedItemList);
        }
    }

    /**
     * 维修分析下交处理批量 员工产能
     *
     * @param batchWorkDetail 批量详情
     * @param isRelease       是否放行
     */
    public void handleStaffPerformBatch(BatchWorkDetail batchWorkDetail, Boolean isRelease) {
        Optional<StaffPerform> staffPerformOptional = staffPerformRepository.findByBatchWorkDetailIdAndDeletedAndContainerDetailIdIsNullAndSnWorkDetailIdNull(batchWorkDetail.getId(), Constants.LONG_ZERO);
        if (staffPerformOptional.isEmpty()) {
            return;
        }
        StaffPerform staffPerform = staffPerformOptional.get();
        staffPerform.setQualifiedNumber(batchWorkDetail.getQualifiedNumber()).setUnqualifiedNumber(batchWorkDetail.getUnqualifiedNumber());
        staffPerformRepository.save(staffPerform);
        //放行直接删除不合格数据，修改员工产能数据
        if (isRelease) {
            staffPerformUnqualifiedItemRepository.deleteByStaffPerformId(staffPerform.getId(), Constants.LONG_ZERO);
            return;
        }
        //直接删除全部的产能不合格项目重新添加
        List<WsStepUnqualifiedItem> wsStepUnqualifiedItems = Objects.nonNull(batchWorkDetail.getSubWorkSheet()) ?
                wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndDeleted(batchWorkDetail.getSubWorkSheet().getId(), batchWorkDetail.getStep().getId(), Constants.LONG_ZERO) :
                wsStepUnqualifiedItemRepository.findByWorkSheetIdAndStepIdAndDeleted(batchWorkDetail.getWorkSheet().getId(), batchWorkDetail.getStep().getId(), Constants.LONG_ZERO);
        if (ValidateUtils.isValid(wsStepUnqualifiedItems)) {
            staffPerformUnqualifiedItemRepository.deleteByStaffPerformId(staffPerform.getId(), Constants.LONG_ZERO);

            List<StaffPerformUnqualifiedItem> staffPerformUnqualifiedItemList = wsStepUnqualifiedItems.stream().map(entity -> {
                StaffPerformUnqualifiedItem staffPerformUnqualifiedItem = new StaffPerformUnqualifiedItem();
                staffPerformUnqualifiedItem.setStaffPerform(staffPerform).setUnqualifiedItem(entity.getUnqualifiedItem())
                        .setNumber(entity.getNumber()).setRecordDate(staffPerform.getRecordDate()).setRecordTime(staffPerform.getRecordTime())
                        .setDeleted(Constants.LONG_ZERO);
                return staffPerformUnqualifiedItem;
            }).collect(Collectors.toList());
            staffPerformUnqualifiedItemRepository.saveAll(staffPerformUnqualifiedItemList);
        }
    }

    /**
     * 分容器下交 更新后置批次详情
     *
     * @param batchWorkDetail    批次详情
     * @param orgQualifiedNumber 原批次详情合格数
     * @param maintainHistoryNumber 维修分析待分析数量
     */
    public void maintainUpdateBatchWorkDetail(BatchWorkDetail batchWorkDetail, Integer orgQualifiedNumber,Long maintainHistoryNumber) {

        //(维修后合格数大于原始合格数)更新后置批次详情 防止 分步下交导致后续工序完成
        if (batchWorkDetail.getQualifiedNumber() > orgQualifiedNumber) {
            List<BatchWorkDetail> afterBatchWorkDetailList = Objects.nonNull(batchWorkDetail.getSubWorkSheet()) ?
                    batchWorkDetailRepository.findBySubWorkSheetIdAndFinishAndDeletedAndIdGreaterThan(batchWorkDetail.getSubWorkSheet().getId(), Constants.INT_ONE, Constants.LONG_ZERO, batchWorkDetail.getId()) :
                    batchWorkDetailRepository.findByWorkSheetIdAndFinishAndDeletedAndIdGreaterThan(batchWorkDetail.getWorkSheet().getId(), Constants.INT_ONE, Constants.LONG_ZERO, batchWorkDetail.getId());
            if (ValidateUtils.isValid(afterBatchWorkDetailList)) {
                afterBatchWorkDetailList.forEach(afterBatchWorkDetail -> {
                    afterBatchWorkDetail.setFinish(Constants.INT_ZERO).setEndDate(null);
                });
                batchWorkDetailRepository.saveAll(afterBatchWorkDetailList);
            }
            SubWorkSheet subWorkSheet = batchWorkDetail.getSubWorkSheet();
            WorkSheet workSheet = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet() : batchWorkDetail.getWorkSheet();
            //更新子工单状态
            if (Objects.nonNull(subWorkSheet)){
                subWorkSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName());
                subWorkSheetRepository.save(subWorkSheet);
            }
            workSheet.setStatus(ConstantsEnum.WORK_SHEET_STATIC_EXECUTE.getCategoryName());
            //如果最后一道容器完成且工序完成，更新的子工单工单的合格数，这个时候需要回退掉当前工单子工单的合格与不合格数量(这个在工序完成时已处理)
            workSheetRepository.save(workSheet);
        }

        //(只有容器批量才会出现：维修后合格数小于原始合格数量) 这个时候需要检查后续工序未完成，可能根据当前工序合格 变更为已完成
        if ((batchWorkDetail.getQualifiedNumber() <= orgQualifiedNumber) && maintainHistoryNumber == Constants.INT_ZERO) {
            maintainUpdateAfterBatchWorkDetail(batchWorkDetail, commonService.getNextWsStep(batchWorkDetail.getWorkSheet(), batchWorkDetail.getSubWorkSheet(), batchWorkDetail.getStep()));
        }
    }

    /**
     * 递归的依次更新已完成批次详情
     *
     * @param currentBatchWorkDetail 当前批次详情
     * @param wsStep                 下道待做工序快照
     */
    public void maintainUpdateAfterBatchWorkDetail(BatchWorkDetail currentBatchWorkDetail, WsStep wsStep) {
        //当前工序详情不存在 或者 下道工序不存在 或者 当前工序不是完成状态
        if (Objects.isNull(currentBatchWorkDetail) || Objects.isNull(wsStep)) {
            return;
        }
        if (currentBatchWorkDetail.getFinish() != Constants.INT_ONE) {
            return;
        }
        Optional<BatchWorkDetail> afterBatchWorkDetailOptional = Objects.nonNull(currentBatchWorkDetail.getSubWorkSheet()) ?
                batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(currentBatchWorkDetail.getSubWorkSheet().getId(), wsStep.getStep().getId(), Constants.LONG_ZERO) :
                batchWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(currentBatchWorkDetail.getWorkSheet().getId(), wsStep.getStep().getId(), Constants.LONG_ZERO);
        if (afterBatchWorkDetailOptional.isEmpty()) {
            return;
        }
        BatchWorkDetail afterBatchWorkDetail = afterBatchWorkDetailOptional.get();
        //后置工序详情投产数等于 前置已完成工序的合格数  说明后置工序完成
        if ((afterBatchWorkDetail.getInputNumber() == currentBatchWorkDetail.getQualifiedNumber())) {
            afterBatchWorkDetail.setFinish(Constants.INT_ONE).setEndDate(Objects.nonNull(afterBatchWorkDetail.getEndDate()) ? afterBatchWorkDetail.getEndDate() : LocalDateTime.now());
            batchWorkDetailRepository.save(afterBatchWorkDetail);
        }
        WsStep nextWsStep = commonService.getNextWsStep(afterBatchWorkDetail.getWorkSheet(), afterBatchWorkDetail.getSubWorkSheet(), afterBatchWorkDetail.getStep());

        //最后工序完成修改工单状态以及完成时间(批量模式 最后一道工序完成才更新 （子）工单 合格不合格)
        if (afterBatchWorkDetail.getFinish() == Constants.INT_ONE && Objects.isNull(nextWsStep)) {
            RworkerStepProcessBaseDTO rworkerStepProcessBaseDto = new RworkerStepProcessBaseDTO();
            rworkerStepProcessBaseDto.setSubWsProductionMode(Objects.nonNull(afterBatchWorkDetail.getSubWorkSheet()))
                    .setWorkSheet(Objects.nonNull(afterBatchWorkDetail.getSubWorkSheet()) ? afterBatchWorkDetail.getSubWorkSheet().getWorkSheet() : afterBatchWorkDetail.getWorkSheet())
                    .setSubWorkSheet(afterBatchWorkDetail.getSubWorkSheet());
            iBatchProcessSaveServices[0].calculateBatchAfterAllStepFinished(rworkerStepProcessBaseDto, afterBatchWorkDetail.getQualifiedNumber());
        }
        maintainUpdateAfterBatchWorkDetail(afterBatchWorkDetail, nextWsStep);
    }

}
