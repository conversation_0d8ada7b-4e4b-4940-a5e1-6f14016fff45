package net.airuima.maintain.service.procedure.maintain;

import net.airuima.config.bean.BeanDefine;
import net.airuima.rbase.dto.client.base.BaseClientDTO;
import net.airuima.maintain.domain.base.MaintainCase;
import net.airuima.maintain.domain.procedure.MaintainHistory;
import net.airuima.maintain.web.rest.procedure.dto.MaintainAnalyseInfoDTO;
import net.airuima.maintain.web.rest.procedure.dto.SaveMaterialAnalyseDTO;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PClientMaintainService {

    @BeanDefine("clientMaintainService")
    public List<MaintainAnalyseInfoDTO.MaintainCaseInfo> getMaintainCase(SubWorkSheet subWorkSheet) {
        return null;
    }

    @BeanDefine("clientMaintainService")
    public List<MaintainAnalyseInfoDTO.MaterialInfo> getMaterialInfo(SubWorkSheet subWorkSheet) {
        return null;
    }

    @BeanDefine("clientMaintainService")
    public MaintainAnalyseInfoDTO getPedigreeMaintainWorkFlowStep(MaintainAnalyseInfoDTO maintainAnalyseInfoDto) {
        return null;
    }

    @BeanDefine("clientMaintainService")
    public BaseClientDTO validateSaveMaterialAnalyse(List<SaveMaterialAnalyseDTO> saveMaterialAnalyseDtoList, Boolean isExistMaintainHistory) {
        return null;
    }

    @BeanDefine("clientMaintainService")
    public List<MaintainCase> validateMaintainCase(List<SaveMaterialAnalyseDTO> saveMaterialAnalyseDtoList, BaseClientDTO baseClientDto) {
        return null;
    }

    @BeanDefine("clientMaintainService")
    public List<MaintainHistory> saveMaintainHistory(List<SaveMaterialAnalyseDTO> saveMaterialAnalyseDtoList, List<MaintainCase> maintainCaseList, int status, int stage) {
        return null;
    }

    @BeanDefine("clientMaintainService")
    public BaseClientDTO saveMaintainReWork(List<SaveMaterialAnalyseDTO> saveMaterialAnalyseDtoList, List<MaintainHistory> maintainHistoryList, int maintainType) {
        return null;
    }

    @BeanDefine("clientMaintainService")
    public String returnReWorkSerialNumber(List<MaintainHistory> maintainHistoryList) {
        return null;
    }

    @BeanDefine("clientMaintainService")
    public BaseClientDTO validateMaintainWorkFlowOrStep(List<SaveMaterialAnalyseDTO> saveMaterialAnalyseDtoList) {
        return null;
    }
}
