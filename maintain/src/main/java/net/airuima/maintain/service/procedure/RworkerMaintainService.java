package net.airuima.maintain.service.procedure;

import net.airuima.config.annotation.FuncDefault;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.maintain.domain.procedure.MaintainHistory;
import net.airuima.maintain.web.rest.procedure.dto.MaintainAnalyseDTO;
import net.airuima.maintain.web.rest.procedure.dto.MaintainHistoryInfoDTO;
import net.airuima.maintain.web.rest.procedure.dto.MaintainHistorySaveDTO;

import java.util.Optional;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 * Rworker维修分析Service
 *
 * <AUTHOR>
 * @date 2023/3/23
 */
@FuncDefault
public interface RworkerMaintainService {

    /**
     * 获取维修分析信息
     *
     * @param maintainAnalyseDTO RWorker请求维修分析参数DTO
     * @return net.airuima.rbase.web.rest.procedure.maintaincase.dto.MaintainHistoryInfoDTO 维修分析信息
     */
    @FuncInterceptor(value = "RepaireAnalysis")
    default MaintainHistoryInfoDTO getMaintainAnalyseInfo(MaintainAnalyseDTO maintainAnalyseDTO) {
        return null;
    }

    /**
     * 保存维修历史记录
     *
     * @param maintainHistorySaveDto Rworker保存维修分析记录信息
     */
    @FuncInterceptor(value = "RepaireAnalysis")
    default void saveMaintainAnalyseInfo(MaintainHistorySaveDTO maintainHistorySaveDto) {

    }

    /**
     * 返回维修分析信息
     *
     * @param maintainHistoryOptional
     * @return net.airuima.rbase.web.rest.procedure.maintaincase.dto.MaintainHistoryInfoDTO 维修分析信息
     * <AUTHOR>
     * @date 2023/3/29
     */
    @FuncInterceptor(value = "RepaireAnalysis")
    default MaintainHistoryInfoDTO retReWorkFlowInfoInfo(Optional<MaintainHistory> maintainHistoryOptional){return null;}


}
