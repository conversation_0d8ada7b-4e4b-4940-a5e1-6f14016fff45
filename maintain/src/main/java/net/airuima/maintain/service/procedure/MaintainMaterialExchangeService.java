package net.airuima.maintain.service.procedure;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.maintain.domain.procedure.MaintainMaterialExchange;
import net.airuima.maintain.repository.procedure.MaintainMaterialExchangeRepository;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.service.CommonJpaService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/9/27
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class MaintainMaterialExchangeService extends CommonJpaService<MaintainMaterialExchange> {

    private final String MAINTAIN_MATERIAL_EXCHANGE_ENTITY_GRAPH = "maintainMaterialExchangeEntityGraph";
    private MaintainMaterialExchangeRepository maintainMaterialExchangeRepository;

    public MaintainMaterialExchangeService(MaintainMaterialExchangeRepository maintainMaterialExchangeRepository) {
        this.maintainMaterialExchangeRepository = maintainMaterialExchangeRepository;
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<MaintainMaterialExchange> find(Specification<MaintainMaterialExchange> spec, Pageable pageable) {
        return maintainMaterialExchangeRepository.findAll(spec,pageable,new NamedEntityGraph(MAINTAIN_MATERIAL_EXCHANGE_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public List<MaintainMaterialExchange> find(Specification<MaintainMaterialExchange> spec) {
        return maintainMaterialExchangeRepository.findAll(spec,new NamedEntityGraph(MAINTAIN_MATERIAL_EXCHANGE_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    @Transactional(readOnly = true)
    public Page<MaintainMaterialExchange> findAll(Pageable pageable) {
        return maintainMaterialExchangeRepository.findAll(pageable,new NamedEntityGraph(MAINTAIN_MATERIAL_EXCHANGE_ENTITY_GRAPH));
    }

    public List<MaintainMaterialExchange> batchSaveInstance(List<MaintainMaterialExchange> maintainMaterialExchanges){
        return this.save(maintainMaterialExchanges);
    }
}
