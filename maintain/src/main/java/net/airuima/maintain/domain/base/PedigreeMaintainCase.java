package net.airuima.maintain.domain.base;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.SchemaProperty;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.dto.organization.ClientDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/9/27
 */
@Schema(name = "产品谱系维修分析(PedigreeMaintainCase)", description = "产品谱系维修分析")
@Entity
@Table(name = "base_pedigree_maintain_case", uniqueConstraints = {
        @UniqueConstraint(name = "base_pedigree_maintain_case_unique_index", columnNames = {"pedigree_id","client_id","maintain_case_id","work_flow_id","deleted"})
})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@FetchEntity
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "产品谱系维修方案基础信息")
@NamedEntityGraph(name = "pedigreeMaintainCaseEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree")})
public class PedigreeMaintainCase extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 维修方案
     */
    @ManyToOne
    @JoinColumn(name = "maintain_case_id")
    @SchemaProperty(name = "维修方案")
    private MaintainCase maintainCase;

    /**
     * 产品谱系
     */
    @NotNull
    @ManyToOne
    @JoinColumn(name = "pedigree_id")
    @SchemaProperty(name = "产品谱系")
    private Pedigree pedigree;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "client_id")
    private Long clientId;

    /**
     * 客户DTO
     */
    @FetchField(mapUri = "/api/clients", serviceId = "mom", paramKey = "clientId",tableName = "client")
    @Schema(description = "客户DTO")
    @Transient
    private ClientDTO clientDto = new ClientDTO();

    /**
     * 返修流程(0:返修工艺路线, 1:原工艺路线)
     */
    @NotNull
    @Schema(description = "返修流程(0:返修工艺路线, 1:原工艺路线)", required = true)
    @Column(name = "rework_category", nullable = false)
    private int reworkCategory;

    /**
     * 流程框图
     */
    @ManyToOne
    @Schema(description = "流程框图id")
    @JoinColumn(name = "work_flow_id")
    private WorkFlow workFlow;

    /**
     * 是否启用(0:禁用;1:启用)
     */
    @Schema(description = "是否启用(0:禁用;1:启用)")
    @Column(name = "is_enable")
    private boolean isEnable;

    public MaintainCase getMaintainCase() {
        return maintainCase;
    }

    public PedigreeMaintainCase setMaintainCase(MaintainCase maintainCase) {
        this.maintainCase = maintainCase;
        return this;
    }

    public Pedigree getPedigree() {
        return pedigree;
    }

    public PedigreeMaintainCase setPedigree(Pedigree pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public Long getClientId() {
        return clientId;
    }

    public PedigreeMaintainCase setClientId(Long clientId) {
        this.clientId = clientId;
        return this;
    }

    public ClientDTO getClientDto() {
        return clientDto;
    }

    public PedigreeMaintainCase setClientDto(ClientDTO clientDto) {
        this.clientDto = clientDto;
        return this;
    }

    public int getReworkCategory() {
        return reworkCategory;
    }

    public PedigreeMaintainCase setReworkCategory(int reworkCategory) {
        this.reworkCategory = reworkCategory;
        return this;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public PedigreeMaintainCase setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public PedigreeMaintainCase setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }
}
