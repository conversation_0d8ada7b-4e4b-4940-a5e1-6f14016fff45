package net.airuima.maintain.domain.procedure;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import net.airuima.rbase.dto.bom.MaterialDTO;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/9/27
 */
@Schema(name = "退补料清单(MaintainMaterialExchange)", description = "退补料清单")
@Entity
@Table(name = "procedure_maintain_material_exchange")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "maintainMaterialExchangeEntityGraph",attributeNodes = {
        @NamedAttributeNode(value = "maintainHistory",subgraph = "maintainHistoryEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "maintainHistoryEntityGraph", attributeNodes = {
                        @NamedAttributeNode(value = "subWorkSheet",subgraph = "subWorkSheetEntityGraph"),
                        @NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")}),
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet",subgraph = "workSheetEntityGraph")}),
                @NamedSubgraph(name = "workSheetEntityGraph",attributeNodes = {@NamedAttributeNode("pedigree")})})
public class MaintainMaterialExchange extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 维修分析历史记录
     */
    @ManyToOne
    @JoinColumn(name = "maintain_history_id")
    @Schema(description = "维修分析历史记录")
    private MaintainHistory maintainHistory;

    /**
     * 替换物料Id
     */
    @Schema(description = "替换物料Id")
    @Column(name = "replace_material_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long replaceMaterialId;

    @Transient
    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "replaceMaterialId")
    private MaterialDTO replaceMaterialDto = new MaterialDTO();

    /**
     * 退料批次
     */
    @Schema(description = "退料批次")
    @Column(name = "return_batch")
    private String returnBatch;

    /**
     * 替换料批次
     */
    @Schema(description = "替换料批次")
    @Column(name = "replace_batch")
    private String replaceBatch;

    /**
     * 退料物料Id
     */
    @Schema(description = "退料物料Id")
    @Column(name = "return_material_id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long returnMaterialId;

    @Transient
    @FetchField(mapUri = "/api/materials", serviceId = "mom", paramKey = "returnMaterialId")
    private MaterialDTO returnMaterialDto = new MaterialDTO();

    /**
     * 替换数量
     */
    @Column(name = "number")
    @Schema(description = "替换数量")
    private double number;

    public MaintainHistory getMaintainHistory() {
        return maintainHistory;
    }

    public MaintainMaterialExchange setMaintainHistory(MaintainHistory maintainHistory) {
        this.maintainHistory = maintainHistory;
        return this;
    }

    public Long getReplaceMaterialId() {
        return replaceMaterialId;
    }

    public MaintainMaterialExchange setReplaceMaterialId(Long replaceMaterialId) {
        this.replaceMaterialId = replaceMaterialId;
        return this;
    }

    public MaterialDTO getReplaceMaterialDto() {
        return replaceMaterialDto;
    }

    public MaintainMaterialExchange setReplaceMaterialDto(MaterialDTO replaceMaterialDto) {
        this.replaceMaterialDto = replaceMaterialDto;
        return this;
    }

    public Long getReturnMaterialId() {
        return returnMaterialId;
    }

    public MaintainMaterialExchange setReturnMaterialId(Long returnMaterialId) {
        this.returnMaterialId = returnMaterialId;
        return this;
    }

    public MaterialDTO getReturnMaterialDto() {
        return returnMaterialDto;
    }

    public MaintainMaterialExchange setReturnMaterialDto(MaterialDTO returnMaterialDto) {
        this.returnMaterialDto = returnMaterialDto;
        return this;
    }

    public double getNumber() {
        return number;
    }

    public MaintainMaterialExchange setNumber(double number) {
        this.number = number;
        return this;
    }

    public String getReplaceBatch() {
        return replaceBatch;
    }

    public MaintainMaterialExchange setReplaceBatch(String replaceBatch) {
        this.replaceBatch = replaceBatch;
        return this;
    }

    public String getReturnBatch() {
        return returnBatch;
    }

    public MaintainMaterialExchange setReturnBatch(String returnBatch) {
        this.returnBatch = returnBatch;
        return this;
    }
}
