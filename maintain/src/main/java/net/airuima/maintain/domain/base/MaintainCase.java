package net.airuima.maintain.domain.base;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.AuditEntity;
import net.airuima.domain.base.CustomBaseEntity;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/9/27
 */
@Schema(name = "维修分析方案(MaintainCase)", description = "维修分析方案")
@Entity
@Table(name = "base_maintain_case", uniqueConstraints = {
        @UniqueConstraint(name = "base_maintain_case_unique_index", columnNames = {"code","deleted"})
})
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@AuditEntity(value = "维修分析方案基础信息")
public class MaintainCase extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 维系分析方案编码
     */
    @Schema(description = "维系分析方案编码")
    @Column(name = "code")
    private String code;

    /**
     * 维系分析方案名称
     */
    @Schema(description = "维系分析方案名称")
    @Column(name = "name")
    private String name;

    /**
     * 是否启用(0:禁用;1:启用)
     */
    @Schema(description = "是否启用(0:禁用;1:启用)")
    @Column(name = "is_enable")
    private boolean isEnable;

    public MaintainCase() {

    }

    public MaintainCase(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public MaintainCase setCode(String code) {
        this.code = code;
        return this;
    }

    public String getName() {
        return name;
    }

    public MaintainCase setName(String name) {
        this.name = name;
        return this;
    }

    public boolean getIsEnable() {
        return isEnable;
    }

    public MaintainCase setIsEnable(boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        MaintainCase that = (MaintainCase) o;

        return new EqualsBuilder().append(isEnable, that.isEnable).append(code, that.code).append(name, that.name).isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37).append(code).append(name).append(isEnable).toHashCode();
    }
}
